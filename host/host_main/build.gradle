import groovy.xml.Namespace
import org.apache.tools.ant.taskdefs.condition.Os
//import org.tmatesoft.svn.core.wc.*



hostMainHead()
ext.mainApp = true //如果此module为主app module，一直以application方式编译，则启用这一行
apply from: rootProject.file('cc-settings-2.gradle')
//apply plugin: 'com.android.application'
//apply plugin: 'fastdex.app'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-kapt'
apply plugin: 'com.huawei.agconnect'
apply plugin: 'com.hihonor.mcs.asplugin'

initTask()
if (hotfix == "yes") {
    makeHotfixDex();
} else {
//    File file = new File(rootProject.rootDir.absolutePath + "/host/host_main/build/");
//    if(file.exists()){
//        deleteDir
//    }(file)
}

//fastdex {
//
//    fastdexEnable = !inJenkins.equals("yes") && !gradle.g_isci
//    Properties localProperties = new Properties()
//    try {
//        def localFile = project.rootProject.file('local.properties')
//        if (localFile != null && localFile.exists()) {
//            localProperties.load(localFile.newDataInputStream())
//            println("——————————load local.properties———————————— ")
//            if (localProperties.getProperty("fastdexEnable")) {
//                fastdexEnable = false;
//            }
//        }
//    } catch (Exception ignored) {
//
//    }
//
////    default 3
//    //当变化的java文件数量大于等于这个值时触发dex merge(随着变化的java文件的增多,补丁打包会越来越慢,dex merge以后当前的状态相当于全量打包以后的状态)
//    dexMergeThreshold = 3
//
//    //是否仅hook debug
//    onlyHookDebug = false
//    //需要注入的jar  0.jar 1.jar  .. 11.jar
//    injectJarCount = gradle.libmodemap.size()+10
//}
FsBuildListener.addByProject(project)
class FsBuildListener implements TaskExecutionListener, BuildListener{

    private times = []
    private final Project project
    private long startMillis

    FsBuildListener(Project project) {
        this.project = project
    }

    @Override
    void beforeExecute(Task task) {
        startMillis = System.currentTimeMillis()
    }

    @Override
    void afterExecute(Task task, TaskState taskState) {
        times.add([System.currentTimeMillis() - startMillis, task.path])
    }

    @Override
    void buildStarted(Gradle gradle) {}

    @Override
    void settingsEvaluated(Settings settings) {}

    @Override
    void projectsLoaded(Gradle gradle) {}

    @Override
    void projectsEvaluated(Gradle gradle) {}

    @Override
    void buildFinished(BuildResult result) {
        if (result.failure == null) {
            println "Task spend time:"
            for (time in times) {
                if (time[0] >= 50) {
                    printf "%7sms  %s\n", time
                }
            }
        }

    }

    static void addByProject(Project pro) {
        FsBuildListener listener = new FsBuildListener(pro)
        pro.gradle.addListener(listener)
    }


}


/**
 * 获取git commit 数量作为versioncode
 * @return
 */
def getGitVersion() {
    def cmd = 'git rev-parse HEAD'

    def gitVersion = cmd.execute().text
    println "git version : " + gitVersion
    return gitVersion
}

def getMyVersionCode() {
    def verNums = gradle.versionName.tokenize('.');
    def versionCode = 0;
    if (verNums != null && verNums.size >= 3) {
        versionCode = (verNums[0] as Integer) * 100000 + (verNums[1] as Integer) * 10000 + (verNums[2] as Integer) * 1000 + (expandVersionCode as Integer);
    }
    return versionCode;
}

def getVersionNameByType(svnNum) {
    return gradle.versionName
}

def svnNum = getGitVersion()
gradle.gitversion=svnNum
def myVersionName = getVersionNameByType(svnNum)
def myVersionCode = getMyVersionCode()

//定义独立编译的标识符
boolean independenceBuild = loadLocalProperties()
println("-----------------independenceBuild : "+independenceBuild+"-----------------------")

//如果有用到kapt添加如下配置
kapt {
//    useBuildCache = true
//    javacOptions {
//        option("-Xmaxerrs", 500)
//    }
}



//apply plugin: 'com.alipay.apollo.baseline.config'

android {
    compileSdkVersion gradle.compileSdkVersion
    buildToolsVersion gradle.buildToolsVersion

//    gradle.taskGraph.whenReady {
//        tasks.each { task ->
//            if (task.name.contains("Test") || task.name.contains("Lint")) {
//                task.enabled = false
//            }
//        }
//    }
    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            jniLibs.srcDirs = ['libs']
            aidl.srcDirs = ['src']
            java.srcDirs = ['src']
            res.srcDirs = ['res']
            assets.srcDirs = ['assets'] //多了一个assets目录
        }
    }
    compileOptions {
        sourceCompatibility gradle.javaVersion
        targetCompatibility gradle.javaVersion
    }

    dexOptions {
        javaMaxHeapSize "4g"
//        preDexLibraries true
//        maxProcessCount 8
    }

    defaultConfig {
        applicationId "com.facishare.fs"
        minSdkVersion gradle.minSdkVersion
        targetSdkVersion gradle.targetSdkVersion
        versionCode myVersionCode
        versionName myVersionName
        flavorDimensions "channel"
        multiDexEnabled true

        multiDexKeepProguard file('multidex-config.pro')
//        println("appinit_t:"+appinit_t)
        manifestPlaceholders = [version_code_bcr: getPluginVersionCode(gradle.versionName_bcr, gradle.pluginsSeqidMap.get("bcr"))]
//                                ,appinit_trace:appinit_t]
        ndk {
//            设置支持的SO库架构
            if(!"PLAY".equals(releaseType)){
                abiFilters 'armeabi','arm64-v8a'//, 'x86', 'armeabi-v7a', 'x86_64', 'arm64-v8a'
            }

        }

        resConfigs "en", "zh-rCN"
    }
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
        exclude 'com/qq/jce/wup/wup.properties'
        doNotStrip '*/mips/*.so'
        doNotStrip '*/mips64/*.so'
    }

    signingConfigs {
        releaseConfig {
            if (gradle.keyAlias != "") {
                keyAlias gradle.keyAlias
                keyPassword gradle.keyPassword
                storeFile file(gradle.storeFile)
                storePassword gradle.storePassword
            }
            //v2渠道包生成  http://wiki.firstshare.cn/pages/viewpage.action?pageId=172150008
            // 使用旧版签名，禁用V2版签名模式，防止渠道包安装时校验失败
//            v2SigningEnabled false
        }
    }
    buildTypes {
        debug {
            String localIP = GetlocalIP.getLocalHostIP();
            println 'local ip:' + localIP;
            manifestPlaceholders = [weixin_appid: "wx27670b328e8743a8", TINKER_ID: gradle.tinkerId, localip: localIP,cleartextTraffic:true]
            if (gradle.debugUseReleaseKeyStore) {
                signingConfig signingConfigs.releaseConfig
            }

            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), "proguard-project.txt"

            if(independenceBuild){
                buildConfigField "boolean", "runAsApp", "true"
                println("-----------buildTypes：Debug  runAsApp = true------------")
            }else{
                buildConfigField "boolean", "runAsApp", "false"
                println("-----------buildTypes：Debug  runAsApp = false-----------")
            }
        }
        release {
            boolean clearTraffic = true
//            if (releaseType.equals("DEV")||releaseType.equals("BETA")) {
//                clearTraffic = true
//            }

            if (releaseType.equals("PLAY")) {
                clearTraffic = false

            }
            manifestPlaceholders = [weixin_appid: "wx869c3185ffdbf8c7", TINKER_ID: gradle.tinkerId, localip: "",cleartextTraffic:clearTraffic]

            if(gradle.isNeedProguard()){
                minifyEnabled false
                proguardFiles getDefaultProguardFile('proguard-android.txt'), "proguard-project.txt"
            }else{

            }
            debuggable false
            signingConfig signingConfigs.releaseConfig

            if(independenceBuild){
                buildConfigField "boolean", "runAsApp", "true"
                println("-----------buildTypes：Release  runAsApp = true------------")
            }else{
                buildConfigField "boolean", "runAsApp", "false"
                println("-----------buildTypes：Release  runAsApp = false-----------")
            }
        }
    }
    productFlavors {
//        Development {
//            minSdkVersion 21
//            resConfigs("en", "xxhdpi")
//            applicationId = "com.facishare.fs"
//            manifestPlaceholders = [internal_vercode: svnNum, IFLYTEK_APPKEY: "568c7271", pkg_name: applicationId, app_display_name: "纷享销客CRM"
//                                    , external_host : "splash", external_scheme: "fxiaoke"]
//        }


        if (releaseType.equals("MAJIA")) {
            online {
                applicationId = "com.facishare.fsmajia"
                manifestPlaceholders = [internal_vercode: svnNum, IFLYTEK_APPKEY: "568c7271", pkg_name: applicationId, app_display_name: "消费家居CRM体验版"
                                        , external_host : "nouse", external_scheme: "nouse","channel_id":"",replace_icon:"@drawable/iconmajia", gaode_amap_key:"ca013c1e9b1125cbcd0c02f6443c3e86",
                                        app_splash_logo:"@style/SplashTheme",permission:"android"]
            }
        }else if (releaseType.equals("PLAY")) {
            arm32 {
                ndk.abiFilter "armeabi"

                applicationId = "com.facishare.fsplay"
                manifestPlaceholders = [internal_vercode: svnNum,
                                        IFLYTEK_APPKEY: "568c7271",
                                        pkg_name: applicationId,
                                        app_display_name: "ShareCRM"
                                        , external_host : "nouse",
                                        external_scheme: "fxiaoke",
                                        "channel_id":"play",
                                        replace_icon:"@drawable/icon",
                                        app_splash_logo:"@style/SplashTheme",
                                        gaode_amap_key:"d0adcbca9e58f554ab4a037bdb293f78",
                                        baidu_amap_key:"b9c9aVtM9TlINdi02goISWSsbLrrCjyA",
                                        permission:"android"]
            }
            arm64 {
                ndk.abiFilter "arm64-v8a"

                applicationId = "com.facishare.fsplay"
                manifestPlaceholders = [internal_vercode: svnNum, IFLYTEK_APPKEY: "568c7271", pkg_name: applicationId, app_display_name: "ShareCRM"
                                        , external_host : "nouse", external_scheme: "fxiaoke","channel_id":"play",
                                        replace_icon:"@drawable/icon",
                                        app_splash_logo:"@style/SplashTheme",
                                        gaode_amap_key:"d0adcbca9e58f554ab4a037bdb293f78",
                                        baidu_amap_key:"b9c9aVtM9TlINdi02goISWSsbLrrCjyA",
                                        permission:"android"]
            }
        } else {
            if (packageType.equals("OI")) {
                println "正式包+内测包"
                internaltest {
                    applicationId = "com.facishare.fsneice"
                    versionCode = (myVersionCode + 100000000) as Integer
                    manifestPlaceholders = [internal_vercode: svnNum, IFLYTEK_APPKEY: "59806bfa", pkg_name: applicationId, app_display_name: "纷享内测CRM"
                                            , external_host : "splash", external_scheme: "fxiaoke",
                                            "mobilegw.appid":"ALIPUB09CBC45151948",
                                            appkey:"ALIPUB09CBC45151948_ANDROID",
                                            mpaasConfigLicense:"h2J8C/ihJ2FPjBxhqcyoczqoVNBnbkjAj0x5uZ+3TMpY6iywhUJGiFTcEoECsuSIN+NnmaT9UovcvsE9BAcWeXq76W0HPDo/ZMlt6Hao7lndHH9vspABQ67y5ccSnv79rlOWQga0seejzQ668HmiqU3JidRU455u1aqYZ4qhAkqKhLN+WdIE7Tja4yKHJixaf5nZwEHZX2PLetdTqdR4D+95ylYZdf1vc5o6484b1vssB59T1y1j+lkAyC5WXZXufS0514NWYIbqP4DMSdv7O1rzBxiuBCdzwKTGuJBfyicW025M2AW4gcTa0NVtpnuzOBsb4KTvOqxRsztGUhDiUw==",
                                            mpaasConfigEnv:"ONEX_CLOUD",
                                            gaode_amap_key:"6c0eb6d84f4f3234f073e7857b2814f8",
                                            baidu_amap_key:"uvr37UZ5TGlKimh6aI32Tm5dQBbVWKRb",
                                            AppSecret:"",replace_icon:"@drawable/icontest"
                                            ,"channel_id":"",
                                            permission:"android",
                                            app_splash_logo:"@style/SplashTheme"
                    ]
                }
                online {
                    applicationId = "com.facishare.fs"
                    manifestPlaceholders = [internal_vercode: svnNum, IFLYTEK_APPKEY: "568c7271", pkg_name: applicationId, app_display_name: "纷享销客CRM"
                                            , external_host : "splash", external_scheme: "fxiaoke",
                                            "mobilegw.appid":"ONEX9C79D44131003",
                                            appkey:"ONEX9C79D44131003_ANDROID",
                                            mpaasConfigLicense:"iwvLNdXegNcKMqowEOecgru3vrt9Oc//2Gec/3GctTktJwcD1rZJ3K7Bzg3vVUCSWFEakIqWgjm50u1UoVwgk00wbxVrCrF+CCq0ncjtaXHWRSGLmAq53QRpBkMqoik9b6s+xd7B+pmDYEAJNKWYRo9ArBXQevIqDAwPscD9DngQmM7JKSWPocMrgrFN0wI30AeLmtnGXheoJv23HgaTu8arCQluumdCRNPfhuSj7So6CWPVghdIcpwGx3KVQHxZ3ZtJ/slfpcTQNRU3ntRSX3m15r5BmvGjyaDUHMsDJPWOaPlV3WaCBp/B9sWCTwLgGeWGO60fPKhngL1OjtCoLg==",
                                            mpaasConfigEnv:"ONEX_CLOUD",
                                            gaode_amap_key:"ca013c1e9b1125cbcd0c02f6443c3e86",
                                            baidu_amap_key:"kEAozqTzcmudNpZB7RRYOwHOT75P1im0",
                                            AppSecret:"",replace_icon:"@drawable/icon"
                                            ,"channel_id":"",
                                            permission:"android",
                                            app_splash_logo:"@style/SplashTheme"
                    ]
                }

                if (releaseType.equals("RELEASE")) {
                    huawei {
                        applicationId = "com.facishare.fs"
                        manifestPlaceholders = [internal_vercode: svnNum, IFLYTEK_APPKEY: "568c7271", pkg_name: applicationId, app_display_name: "纷享销客CRM"
                                                , external_host : "splash", external_scheme: "fxiaoke",
                                                "mobilegw.appid":"ONEX9C79D44131003",
                                                appkey:"ONEX9C79D44131003_ANDROID",
                                                mpaasConfigLicense:"iwvLNdXegNcKMqowEOecgru3vrt9Oc//2Gec/3GctTktJwcD1rZJ3K7Bzg3vVUCSWFEakIqWgjm50u1UoVwgk00wbxVrCrF+CCq0ncjtaXHWRSGLmAq53QRpBkMqoik9b6s+xd7B+pmDYEAJNKWYRo9ArBXQevIqDAwPscD9DngQmM7JKSWPocMrgrFN0wI30AeLmtnGXheoJv23HgaTu8arCQluumdCRNPfhuSj7So6CWPVghdIcpwGx3KVQHxZ3ZtJ/slfpcTQNRU3ntRSX3m15r5BmvGjyaDUHMsDJPWOaPlV3WaCBp/B9sWCTwLgGeWGO60fPKhngL1OjtCoLg==",
                                                mpaasConfigEnv:"ONEX_CLOUD",
                                                gaode_amap_key:"9133648b73ed6a1b47c1011560676e10",
                                                baidu_amap_key:"kEAozqTzcmudNpZB7RRYOwHOT75P1im0",
                                                AppSecret:"",replace_icon:"@drawable/icon"
                                                ,"channel_id":"",
                                                permission:"android",
                                                app_splash_logo:"@style/SplashTheme"
                        ]
                    }
                }

                if (releaseType.equals("RELEASE")) {
                    online2 {
                        applicationId = "com.facishare.fs"
                        manifestPlaceholders = [internal_vercode: svnNum, IFLYTEK_APPKEY: "568c7271", pkg_name: applicationId, app_display_name: "纷享销客CRM"
                                                , external_host : "splash", external_scheme: "fxiaoke",
                                                "mobilegw.appid":"ONEX9C79D44131003",
                                                appkey:"ONEX9C79D44131003_ANDROID",
                                                mpaasConfigLicense:"iwvLNdXegNcKMqowEOecgru3vrt9Oc//2Gec/3GctTktJwcD1rZJ3K7Bzg3vVUCSWFEakIqWgjm50u1UoVwgk00wbxVrCrF+CCq0ncjtaXHWRSGLmAq53QRpBkMqoik9b6s+xd7B+pmDYEAJNKWYRo9ArBXQevIqDAwPscD9DngQmM7JKSWPocMrgrFN0wI30AeLmtnGXheoJv23HgaTu8arCQluumdCRNPfhuSj7So6CWPVghdIcpwGx3KVQHxZ3ZtJ/slfpcTQNRU3ntRSX3m15r5BmvGjyaDUHMsDJPWOaPlV3WaCBp/B9sWCTwLgGeWGO60fPKhngL1OjtCoLg==",
                                                mpaasConfigEnv:"ONEX_CLOUD",
                                                gaode_amap_key:"9133648b73ed6a1b47c1011560676e10",
                                                baidu_amap_key:"kEAozqTzcmudNpZB7RRYOwHOT75P1im0",
                                                AppSecret:"",replace_icon:"@drawable/icon"
                                                ,"channel_id":"",
                                                permission:"android",
                                                app_splash_logo:"@style/SplashTheme"
                        ]
                    }
                }
            } else if (packageType.equals("O")) {
                println "只打正式包"
                online {
                    applicationId = "com.facishare.fs"
                    manifestPlaceholders = [internal_vercode: svnNum, IFLYTEK_APPKEY: "568c7271", pkg_name: applicationId, app_display_name: "纷享销客CRM"
                                            , external_host : "splash", external_scheme: "fxiaoke",
                                            "mobilegw.appid":"ONEX9C79D44131003",
                                            appkey:"ONEX9C79D44131003_ANDROID",
                                            mpaasConfigLicense:"iwvLNdXegNcKMqowEOecgru3vrt9Oc//2Gec/3GctTktJwcD1rZJ3K7Bzg3vVUCSWFEakIqWgjm50u1UoVwgk00wbxVrCrF+CCq0ncjtaXHWRSGLmAq53QRpBkMqoik9b6s+xd7B+pmDYEAJNKWYRo9ArBXQevIqDAwPscD9DngQmM7JKSWPocMrgrFN0wI30AeLmtnGXheoJv23HgaTu8arCQluumdCRNPfhuSj7So6CWPVghdIcpwGx3KVQHxZ3ZtJ/slfpcTQNRU3ntRSX3m15r5BmvGjyaDUHMsDJPWOaPlV3WaCBp/B9sWCTwLgGeWGO60fPKhngL1OjtCoLg==",
                                            mpaasConfigEnv:"ONEX_CLOUD",
                                            gaode_amap_key:"ca013c1e9b1125cbcd0c02f6443c3e86",
                                            baidu_amap_key:"kEAozqTzcmudNpZB7RRYOwHOT75P1im0",
                                            AppSecret:"",
                                            replace_icon:"@drawable/icon"
                                            ,"channel_id":"",
                                            app_splash_logo:"@style/SplashTheme",
                                            permission:"android"

                    ]
                }
            } else if (packageType.equals("I")) {
                println "只打内测包"
                internaltest {
                    applicationId = "com.facishare.fsneice"
                    versionCode = (myVersionCode + 100000000) as Integer
                    manifestPlaceholders = [internal_vercode: svnNum, IFLYTEK_APPKEY: "59806bfa", pkg_name: applicationId, app_display_name: "纷享内测CRM"
                                            , external_host : "splash", external_scheme: "fxiaoke",
                                            "mobilegw.appid":"ALIPUB09CBC45151948",
                                            appkey:"ALIPUB09CBC45151948_ANDROID",
                                            mpaasConfigLicense:"h2J8C/ihJ2FPjBxhqcyoczqoVNBnbkjAj0x5uZ+3TMpY6iywhUJGiFTcEoECsuSIN+NnmaT9UovcvsE9BAcWeXq76W0HPDo/ZMlt6Hao7lndHH9vspABQ67y5ccSnv79rlOWQga0seejzQ668HmiqU3JidRU455u1aqYZ4qhAkqKhLN+WdIE7Tja4yKHJixaf5nZwEHZX2PLetdTqdR4D+95ylYZdf1vc5o6484b1vssB59T1y1j+lkAyC5WXZXufS0514NWYIbqP4DMSdv7O1rzBxiuBCdzwKTGuJBfyicW025M2AW4gcTa0NVtpnuzOBsb4KTvOqxRsztGUhDiUw==",
                                            mpaasConfigEnv:"ONEX_CLOUD",
                                            gaode_amap_key:"6c0eb6d84f4f3234f073e7857b2814f8",
                                            baidu_amap_key:"uvr37UZ5TGlKimh6aI32Tm5dQBbVWKRb",
                                            AppSecret:"",
                                            replace_icon:"@drawable/icontest"
                                            ,"channel_id":"",
                                            app_splash_logo:"@style/SplashTheme",
                                            permission:"android"

                    ]
                }
            } else if (packageType.equals("MENGNIU")) {
                println "蒙牛包，内测+正式"
                internaltest {
                    println("蒙牛包 内测包")
                    applicationId = "com.mengniu.msv.neice"
                    versionCode = (myVersionCode + 100000000) as Integer
                    manifestPlaceholders = [internal_vercode: svnNum, IFLYTEK_APPKEY: "59806bfa", pkg_name: applicationId, app_display_name: "共赢内测"
                                            , external_host : "splash", external_scheme: "fxiaoke",
                                            //mpaas配置开始
                                            "mobilegw.appid":"ALIPUBE723768011147",
                                            appkey:"ALIPUBE723768011147_ANDROID",
                                            mpaasConfigLicense:"iit4i4AJDYMze++1Np1xSxwYcna6n0A6ZTEh7Q6h49953s1eIOjHGWrLjmHTzrPsUYswGpRvI+G9VdqrLvl57c+KkS7chndIbURvBomn+sLIWjXi+9sQFkkNkoYqj3NgSi6NW+zPK9+xdROFUUiFTmNUavondA9AtsYKfFBWCY6UQEquYz0vxUbGaYqVjcqJVVRQKRlOkwvDwDN62as2jpGkOdz1xiduhue4I8Gc2k2J7faKMuS3W2CnEFm16OE3g94iNQpKJxGMxOxq7+SGHwbh2NpfVGSp8456hX61DqqS+jVSUhMv+yrVBBMBMN7CAEPpSMOhfNTwOQJnljbSCQ==",
                                            mpaasConfigEnv:"ONEX_CLOUD",
                                            //mpaas配置结束
                                            gaode_amap_key:"2c445ed7ffdebf23afb52435959e0eba",
                                            baidu_amap_key:"UvcFIYjMRRCoLl5TdWV2qCDtBk66AqBC",
                                            AppSecret:"",
                                            replace_icon:"@drawable/mengniu"
                                            ,"channel_id":"",
                                            app_splash_logo:"@style/SplashThemeMengniu",
                                            permission:"com"
                    ]
                }
                online {
                    println("蒙牛包 正式包")
                    applicationId = "com.mengniu.msv"
                    manifestPlaceholders = [internal_vercode: svnNum, IFLYTEK_APPKEY: "568c7271", pkg_name: applicationId, app_display_name: "共赢"
                                            , external_host : "splash", external_scheme: "fxiaoke",
                                            gaode_amap_key:"7eea9596339d43c79c3e4177297bbc8a",
                                            baidu_amap_key:"qOh7qq03r1FNUajeSNkY7EFZEstW8vMK",
                                            //mpaas配置开始
                                            "mobilegw.appid":"ALIPUB69F9B10011146",
                                            appkey:"ALIPUB69F9B10011146_ANDROID",
                                            mpaasConfigLicense:"XReS/ev5F0Lz87dcroXLY/EUwT1JswTJZcGzV50LXyeN3h0hbHIMO9lXXC1LdUT2tMqp7sUmYWRTPV2tqbQRntwuCmqnwS1oV39iXlxspfbMEyYKInQ3qohFG85KsncpD3gW/cIbYn1OrhaU5TMdsDGxntdeAWCIGvvXt7xRsnSPAhobFiE8m8A81GR0IPW+DFh+IC0OH9/MAlATsFfWODWY9wwNWqzwDqFW1+l66R3y07lg3Sz7QvtJT2B4dsLSUbIrw34dFyivGEbaK+dN/zfGTN7GdqHCoCHT2fnLXvJ3sEY2fJCcRn6k24TIY0dEiAdZNgnK9Z0UAWXWmlgHQw==",
                                            mpaasConfigEnv:"ONEX_CLOUD",
                                            //mpaas配置结束
                                            AppSecret:"",
                                            replace_icon:"@drawable/mengniu"
                                            ,"channel_id":"",
                                            app_splash_logo:"@style/SplashThemeMengniu",
                                            permission:"com"

                    ]
                }
            }
        }
    }
}



task commandLineTadk(type: Exec) {
}

def replaceRequstUrl() {
    if(releaseType=='RELEASE'){
        def compileInfoFile = "${rootDir}/host/host_main/src/com/fxiaoke/host/context/CompileInfo.java"
        def compileInfoContent = new File(compileInfoFile).getText('UTF-8')
        println("git ver before:"+gradle.gitversion.replace('\n',''))
        if(compileInfoContent.contains("#replace_gitversion#")){
            compileInfoContent=compileInfoContent.replaceFirst("#replace_gitversion#",gradle.gitversion.replace('\n',''))
        }else{
            def reg = /String s_gitVersion=\"([a-z0-9]+)\";/
            def matcher = compileInfoContent=~reg
            if(matcher.find()){
                println("group 1 "+matcher.group(1))
                compileInfoContent=compileInfoContent.replaceFirst(matcher.group(1),gradle.gitversion.replace('\n',''))
            }
        }

        new File(compileInfoFile).write(compileInfoContent, 'UTF-8')
    }else{
        println("git ver before: null")
    }
    android.applicationVariants.all { variant ->
//        variant.mergeResources.doLast {
//            String url = "https://www.fxiaoke.com"
//            if (envType.equals("ONLINE")) {
//                url = "https://www.fxiaoke.com"
//            } else if (envType.equals("CESHI112")) {
//                url = "https://www.ceshi112.com"
//            } else if (envType.equals("CESHI113")) {
//                url = "https://www."
//            }
//            File valuesFile = file("${buildDir}/intermediates/res/merged/${variant.dirName}/values/values.xml")
//            if(valuesFile.exists()){
//                String content = valuesFile.getText('UTF-8')
//                content = content.replaceAll("#requestUrl#", url)
//                content = content.replaceAll("#releaseType#", releaseType)
//                String share2App = "分享给企信好友"
//                content = content.replaceAll("#request_share_to_app#", share2App)
//                valuesFile.write(content, 'UTF-8')
//            }
//
//        }
        variant.outputs.each { output ->
            output.processManifestProvider.get().doLast {
                String url = "https://www.fxiaoke.com"
                if (envType.equals("ONLINE")) {
                    url = "https://www.fxiaoke.com"
                } else if (envType.equals("CESHI112")) {
                    url = "https://www.ceshi112.com"
                } else if (envType.equals("MENGNIU")) {
                    url = "https://msv.mengniu.cn"
                }
                //移除之前新增的能力——谷歌包默认域名为国际域名hws.fxiaoke.com，以解决国内企业的海外员工，使用纷享App登录不了的问题
                // （因为国内的企业未在海外的云服务地址上部署，会触发查找不到账号的问题，但国外的企业均会再国内备份，可以直接使用国内玉米）
                if ("MENGNIU".equals(packageType)) {//蒙牛包默认域名为msv.mengniu.cn
                    url = "https://msv.mengniu.cn"
                }
                //${buildDir}是指build文件夹
                //${variant.dirName}是flavor/buildtype，例如GooglePlay/release，运行时会自动生成
                //下面的路径是类似这样：build/intermediates/manifests/GooglePlay/release/AndroidManifest.xml
                def appName = "纷享销客CRM"
                def appIcon = "@drawable/icon"
//                def amapkey = "ca013c1e9b1125cbcd0c02f6443c3e86"
                def networkSecurityConfig = null
                if(variant.buildType.getName().equals("debug")){
                    println("debug map key")
//                    amapkey = "ca013c1e9b1125cbcd0c02f6443c3e86"
                }

                if(!releaseType.equals("PLAY")){
                    networkSecurityConfig = "android:networkSecurityConfig=\"@xml/network_security_config\""
                }
//                def baidumapkey = "kEAozqTzcmudNpZB7RRYOwHOT75P1im0"

                def buildType = lowerFirstCase(variant.name.capitalize())
                def manifestFile = "${buildDir}/intermediates/merged_manifests/${buildType}/AndroidManifest.xml"
                def huaweiPushAppId = "1179047";
                def auth = variant.productFlavors[0].applicationId + ".fssync";
                def honorAppid = "900778358"
                def honorDeveloperid = "109999870694"
                //将字符串REPLACE_KEY替换成flavor的名字
                if (variant.productFlavors[0] != null && variant.productFlavors[0].name.equals("internaltest")
                        && variant.productFlavors[0].applicationId.contains("fsneice")) {
                    appName = "纷享内测CRM"
                    appIcon = "@drawable/icontest"
//                    amapkey = "6c0eb6d84f4f3234f073e7857b2814f8"
//                    baidumapkey = "uvr37UZ5TGlKimh6aI32Tm5dQBbVWKRb"
                    huaweiPushAppId = "10678932";
                    honorAppid = "104481238"
                    honorDeveloperid = "109999870694"
                } else if (variant.productFlavors[0] != null && variant.productFlavors[0].name.equals("online")
                        && variant.productFlavors[0].applicationId.contains("fsmajia")) {
                    appName = "消费家居crm体验版"
                    appIcon = "@drawable/iconmajia"
                } else if (variant.productFlavors[0] != null && variant.productFlavors[0].name.equals("onlinecrm")) {
                    appName = "纷享销客CRM"
                }
                //读取本地local.properties文件 检测文件中是否有crm = true or false
                //本地没有该属性时移除sharedUserId属性
                def processTag ="android:sharedUserId=\"com.facishare.fs\""
                def updatedContent = new File(manifestFile).getText('UTF-8')
                updatedContent = updatedContent.replaceAll("#replace_icon#",
                        appIcon)

                updatedContent = updatedContent.replaceAll("#replace_huawei_push_appid#",
                        huaweiPushAppId)
                updatedContent = updatedContent.replaceAll("#replace_authority#",
                        auth)


                updatedContent = updatedContent.replaceAll("#requestUrl#", url)
                updatedContent = updatedContent.replaceAll("#releaseType#", releaseType)
                updatedContent = updatedContent.replaceAll("#honorAppid#", honorAppid)
                updatedContent = updatedContent.replaceAll("#honorDeveloperid#", honorDeveloperid)
                def currentApplicationId =null;
                if(variant!= null && variant.productFlavors[0]!=null&&variant.productFlavors[0]
                        .applicationId!=null){
                    currentApplicationId = variant.productFlavors[0].applicationId

                }
                if(currentApplicationId!=null){
                    updatedContent = updatedContent.replaceAll("#aliyunVideoCertPath#", "assets/cert/"
                            + currentApplicationId +".crt")
                    println("replace aliyunVideoCertPath with applicationId " +
                            currentApplicationId)
                }

                if (releaseType.equals("PLAY")) {
                    updatedContent = updatedContent.replaceAll("#replace_name#",
                            "fxiaoke")
                    updatedContent = updatedContent.replaceAll("<uses-permission android:name=\"android.permission.MANAGE_EXTERNAL_STORAGE\" />",
                            "")
                    updatedContent = updatedContent.replaceAll("#replace_getui_appid#", "b6VoTX3XVY642hfZTcI111")
//                    updatedContent = updatedContent.replaceAll("#replace_getui_appkey#", "NZy2jPk0B48XdpLpnu0Ed5")
//                    updatedContent = updatedContent.replaceAll("#replace_getui_appsecret#", "Sz6p966PX6AFNdKd8E9JG6")
//                    amapkey = "d0adcbca9e58f554ab4a037bdb293f78";
//                    baidumapkey = "b9c9aVtM9TlINdi02goISWSsbLrrCjyA";
                }else{
                    updatedContent = updatedContent.replaceAll("#replace_name#",
                            appName)
                    updatedContent = updatedContent.replaceAll("#replace_getui_appid#", "mbaTg9w2ztAxzBnXYm5Ff4")
//                    updatedContent = updatedContent.replaceAll("#replace_getui_appkey#", "b0UzfDwY7X66PiKRV3qTi3")
//                    updatedContent = updatedContent.replaceAll("#replace_getui_appsecret#", "Czns13spIt6m2weSDWA2x2")
                }

//                updatedContent = updatedContent.replaceAll("#replace_amapkey#",
//                        amapkey)
//                updatedContent = updatedContent.replaceAll("#replace_baidumapkey#",
//                        baidumapkey)
                if(networkSecurityConfig != null){
                    println("remove https config")
                    updatedContent = updatedContent.replaceAll(networkSecurityConfig,
                            "")
                }

                if (!loadLocalProperties()) {
                    updatedContent = updatedContent.replaceAll(processTag,"")
                    println("——————————sharedUserId is removed from the AndroidManifest—————————————")
                }
                new File(manifestFile).write(updatedContent, 'UTF-8') //将此次flavor的AndroidManifest.xml文件指定为我们修改过的这个文件
//                variant.outputs[0].processResources.manifestFile = file("${buildDir}/intermediates/manifests/full/${variant.dirName}/AndroidManifest.xml")
            }
        }
    }
}
replaceRequstUrl()

def ensureDir(File dir) {
    if (!dir.exists() && !dir.isDirectory()) {
        dir.mkdir()
    }
}

//读取local文件并返回independenceBuild值
def loadLocalProperties(){
    Properties localProperties = new Properties()
    try {
        def localFile = project.rootProject.file('local.properties')
        if (localFile != null && localFile.exists()) {
            localProperties.load(localFile.newDataInputStream())
            println("——————————load local.properties———————————— ")
        }
    } catch (Exception ignored) {
        println("${PLUGIN_NAME}: ——————————local.properties not found——————————")
    }
    println("isIndependenceBuild :"+isIndependenceBuild(localProperties))
    return isIndependenceBuild(localProperties)
}

//遍历local文件并调用isPluginApk进行比较 根据local文件是否与其有交集返回boolean值
def isIndependenceBuild(projectName) {
    for(Object key : projectName.keySet()) {
        if (isPluginsApk(key)) {
            return true
        }
    }
    return false
}
//将输入值与settings.gradle中的pluginsApk遍历匹配
def isPluginsApk(projectName) {
    for (name in gradle.pluginsApk) {
        if (name.equals(projectName)) {
            return true
        }
    }
    return false
}



task secondDexJiagu(type: Exec) {
    def dx = "$System.env.ANDROID_HOME/build-tools/$gradle.buildToolsVersion/dx"
    //适配mac环境找不到appinitSystem.env.ANDROID_HOME
    if (Os.isFamily(Os.FAMILY_MAC)) {
        dx = "${android.sdkDirectory}/build-tools/$gradle.buildToolsVersion/dx"
    }
    dx = dealFilePath(dx)
    workingDir rootProject.rootDir.absolutePath
    ensureDir(new File(rootProject.rootDir.absolutePath + "/output/dexoutput"))
    if (Os.isFamily(Os.FAMILY_WINDOWS)) {
        commandLine "cmd", "/c", "$dx --dex --output=output/dexoutput/jiaguDex.dex libsDexJiagu"
    } else {
        commandLine "sh", "-c", "$dx --dex --output=output/dexoutput/jiaguDex.dex libsDexJiagu"
    }
}

task secondDexMain(type: Exec) {
    def dx = "$System.env.ANDROID_HOME/build-tools/$gradle.buildToolsVersion/dx"
    //适配mac环境找不到appinitSystem.env.ANDROID_HOME
    if (Os.isFamily(Os.FAMILY_MAC)) {
        dx = "${android.sdkDirectory}/build-tools/$gradle.buildToolsVersion/dx"
    }
    dx = dealFilePath(dx)
    workingDir rootProject.rootDir.absolutePath
    ensureDir(new File(rootProject.rootDir.absolutePath + "/output/dexoutput"))
    if (Os.isFamily(Os.FAMILY_WINDOWS)) {
        commandLine "cmd", "/c", "$dx --dex --output=output/dexoutput/mainDex.dex libsDexMain"
    } else {
        commandLine "sh", "-c", "$dx --dex --output=output/dexoutput/mainDex.dex libsDexMain"
    }
}


task testVersionCode() {
    println "vercode : " + getMyVersionCode()
}



repositories {
    flatDir {
        dirs './'
    }
}

configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}

dependencies {
    addComponent 'crm'
//    addComponent 'fxiaoke'
//    compile project(":crm")

    if("PLAY".equals(releaseType)){
        println "use no pay"
    }else{
        implementation project(":pay")
        println "use pay plug"
    }
    implementation project(":commonfunc")
    implementation project(":bi")
    implementation project(":fxiaoke")
    implementation project(":fsmail")
    implementation project(":shortvideo")
    implementation project(":trainhelper")
    implementation project(":fsinit")

    if("PLAY".equals(releaseType)){
        implementation fileTree(dir: 'playX5Lib', include: ['*.jar','*.aar'])
        println "use play x5 lib"
    }else{
        api 'com.tencent.tbs:tbssdk:44136'
    }

    implementation ('com.tencent.bugly:crashreport:4.1.9.3'){
        exclude module: 'support-v4'
    }


//    implementation 'com.tencent.bugly:nativecrashreport:3.3.1'
//    implementation 'com.tencent.bugly:crashreport:latest.release'
    debugImplementation 'com.squareup.haha:haha:2.0.2'
    debugImplementation(name: 'leakcanary-watcher-release', ext: 'aar')
    debugImplementation(name: 'leakcanary-analyzer-release', ext: 'aar')
    debugImplementation(name: 'leakcanary-android-release', ext: 'aar')
    debugImplementation(name: 'leakcanaryutil-debug', ext: 'aar')
    releaseImplementation(name: 'leakcanary-android-no-op-release', ext: 'aar')
    releaseImplementation(name: 'leakcanaryutil-release', ext: 'aar')
    implementation fileTree(dir: 'libs', include: ['*.jar','*.aar'])
    implementation fileTree(dir: rootProject.getRootDir().getAbsolutePath() + "/libsDex", include: ['*.jar'])
    implementation fileTree(dir: rootProject.getRootDir().getAbsolutePath() + "/maplibs", include: ['*.jar'])
//    implementation 'com.amap.api:3dmap:10.0.600'
//    implementation 'com.amap.api:search:9.7.0'
    implementation 'com.tencent.map.geolocation:TencentLocationSdk-openplatform:7.5.4.8'
    implementation('com.google.android.gms:play-services-maps:17.0.0') {
        exclude module: 'support-v4'
    }
    implementation('com.google.android.gms:play-services-location:18.0.0') {
        exclude module: 'support-v4'
    }

    implementation files(rootProject.getRootDir().getAbsolutePath() + "/libs/jetty-util-6.1.26.jar")
    implementation files(rootProject.getRootDir().getAbsolutePath() + "/libs/httpmime-4.1.3.jar")
    implementation files(rootProject.getRootDir().getAbsolutePath() + "/libs/android-database-sqlcipher-3.5.9.aar")
    implementation files(rootProject.getRootDir().getAbsolutePath() + "/libs/tenncentmm.jar")
    debugImplementation files(rootProject.getRootDir().getAbsolutePath() + "/libs/jsr305-3.0.2.jar")
//    implementation files(rootProject.getRootDir().getAbsolutePath() + "/libs/commons-logging-1.2.jar")
    implementation files(rootProject.getRootDir().getAbsolutePath() + "/libs/httpclient-4.5.6.jar")
//    implementation files(rootProject.getRootDir().getAbsolutePath() + "/libs/httpcore-4.4.10.jar")
    implementation files(rootProject.getRootDir().getAbsolutePath() + "/libs/fastjson_${gradle.fastjsonVersion}.jar")


    implementation files(rootProject.getRootDir().getAbsolutePath() + "/libs/okhttp-3.12.3.jar")
    implementation files(rootProject.getRootDir().getAbsolutePath() + "/libs/j2v8.jar")
    implementation files(rootProject.getRootDir().getAbsolutePath() + "/libs/setho.jar")


    implementation "com.tencent.mm.opensdk:wechat-sdk-android-without-mta:$gradle.wechatVersion"

    //compileOnly files(rootProject.getRootDir().getAbsolutePath() + "/libsDexJiagu/msdkhttpdns_20170216.jar")
    //compileOnly files(rootProject.getRootDir().getAbsolutePath() + "/libsDexJiagu/beacon_android_v2.4.7.jar")

    implementation depLibProjectWithMavenOrSource("beans")
    implementation depLibProjectWithMavenOrSource("commonViews")
    implementation depLibProjectWithMavenOrSource("fsprobuf")
    implementation depLibProjectWithMavenOrSource("paylib")
    if (isInGitlabCi()) {

        implementation depLibProjectWithMavenOrSource("FxSocketLib")
        implementation depLibProjectWithMavenOrSource("fshttp")
        implementation depLibProjectWithMavenOrSource("contactssynclib")
        implementation depLibProjectWithMavenOrSource("FxLog")
        implementation depLibProjectWithMavenOrSource("tinker")
        implementation depLibProjectWithMavenOrSource("Multidex")
        implementation depLibProjectWithMavenOrSource("fsmaindex")
    } else {
        compileOnly depLibProjectWithMavenOrSource("FxSocketLib")
        compileOnly depLibProjectWithMavenOrSource("fshttp")
        compileOnly depLibProjectWithMavenOrSource("contactssynclib")
        compileOnly depLibProjectWithMavenOrSource("FxLog")
        compileOnly depLibProjectWithMavenOrSource("tinker")
        compileOnly depLibProjectWithMavenOrSource("Multidex")
        compileOnly depLibProjectWithMavenOrSource("fsmaindex")
    }

    implementation depLibProjectWithMavenOrSource("performance")
    implementation depAarProjectWithMavenOrSource("data_impl")
    implementation depLibProjectWithMavenOrSource("EventBus")
    implementation depLibProjectWithMavenOrSource("xUtils")
    implementation depLibProjectWithMavenOrSource("UniversalImgLoader")
    implementation depLibProjectWithMavenOrSource("pluginapi")
    implementation depLibProjectWithMavenOrSource("FxDbLib")
    implementation depLibProjectWithMavenOrSource("pluginapi_contacts")
    implementation depLibProjectWithMavenOrSource("pluginapi_account")
    implementation depLibProjectWithMavenOrSource("FXStatEngine")
    implementation depLibProjectWithMavenOrSource("fscommon_nores")
    implementation depAarProjectWithMavenOrSource("jsApi")
    implementation depLibProjectWithMavenOrSource("user_context")
    implementation depLibProjectWithMavenOrSource("okgo")
    implementation depLibProjectWithMavenOrSource("okserver")
    implementation depAarProjectWithMavenOrSource("locationLib")

    implementation depLibProjectWithMavenOrSource("KWQin")
    implementation depLibProjectWithMavenOrSource("qixinlib")
    implementation depLibProjectWithMavenOrSource("crmlib")
    implementation depLibProjectWithMavenOrSource("IntelliOperation")
    implementation depLibProjectWithMavenOrSource("support_v4")
    implementation depLibProjectWithMavenOrSource("modelview")
    implementation depLibProjectWithMavenOrSource("metadata-beans")
    implementation depAarProjectWithMavenOrSource("sizectrlviews")
    implementation depAarProjectWithMavenOrSource("fscommon_res")
    implementation 'com.gyf.immersionbar:immersionbar:3.0.0'

    implementation project(":android-pluginmgr")

//    implementation project(":tinker")

//    implementation project(":fxiaoke")
//    implementation project(":crm")
//    implementation project(":commonfunc")
//    implementation project(":bi")
//    implementation project(":fsmail")
//    implementation project(":pay")
//    implementation project(":shortvideo")
//    implementation project(":trainhelper")
    implementation depAarProjectWithMavenOrSource("docpreviewlib")
    implementation depAarProjectWithMavenOrSource("remote_service")
    if(releaseType=='RELEASE'){
        implementation  depLibProjectWithAarMaven("weex_release")
        println 'implementation weex_release'
    }else{
        implementation 'com.taobao.android.weex_inspection:protocol:1.1.4.1'
//    debugImplementation ('com.squareup.okhttp3:okhttp:3.4.2'){
//        exclude module: 'okio'
//    }
//    debugImplementation('com.squareup.okhttp3:okhttp-ws:3.4.2') {
//        exclude module: 'okio'
//    }
        implementation ('com.squareup.okhttp:okhttp:2.3.0'){
            exclude module: 'okio'
        }
        implementation ('com.squareup.okhttp:okhttp-ws:2.3.0'){
            exclude module: 'okio'
        }
        implementation 'com.taobao.android:weex_inspector:0.24.2.11'
        implementation 'com.taobao.android.weex_inspection:urlconnection_interceptor:1.0.0'
        implementation depLibProjectWithAarMaven("weex_debug")
        println 'implementation weex_debug'
    }




    implementation 'com.alibaba.android:bindingx-core:1.0.1'
    implementation 'com.alibaba.android:bindingx_weex_plugin:1.0.1'
    // 华为HMS推送
//    api 'com.huawei.android.hms:push:2.5.2.300'
//    api 'com.huawei.android.hms:base:2.5.2.300'


    implementation 'com.huawei.hms:push:4.0.0.300'



    //  evernote开源的定时任务工具，替代android内置的jobschedule
    implementation 'com.evernote:android-job:1.3.0-alpha06'

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$gradle.kotlinVersion"
    implementation "org.jetbrains.kotlin:kotlin-reflect:$gradle.kotlinVersion"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.2.1"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.2.1"

    implementation 'com.tencent.wcdb:wcdb-android:1.0.6'

    debugImplementation 'com.github.markzhai:blockcanary-android:1.5.0'
    releaseImplementation 'com.github.markzhai:blockcanary-no-op:1.5.0'

    implementation "io.reactivex.rxjava2:rxjava:$gradle.rxjava2Version"
    implementation "io.reactivex.rxjava2:rxandroid:$gradle.rxandroid2Version"


    implementation "io.protostuff:protostuff-core:1.4.0"
    implementation "io.protostuff:protostuff-runtime:1.4.0"
    implementation depAarProjectWithMavenOrSource("cmViews")
    implementation depAarProjectWithAar("cmlsdk")
    implementation depAarProjectWithAar("cmlweb")
    implementation depAarProjectWithAar("cmlweex")
    implementation depAarProjectWithAar("js-bundle-mgr")
    implementation depAarProjectWithAar("sdk-image")
    implementation 'me.drakeet.support:toastcompat:1.1.0'
//    compile "com.alivc.conan:AlivcConan:0.9.0" //在这里引用还是会报找不到libalivc_conan.so而崩溃的错误
    implementation ('com.didichuxing.doraemonkit:doraemonkit:1.1.8'){
        exclude module: 'okhttp'
        exclude group: 'com.google.zxing' , module:'core'
    }
    implementation ('com.didichuxing.doraemonkit:doraemonkit-weex:1.0.0'){
        exclude group: 'com.taobao.android' , module:'weex_sdk'
        exclude group: 'com.taobao.android' , module:'weex_inspector'

    }

//    releaseImplementation 'com.didichuxing.doraemonkit:doraemonkit-no-op:1.1.6'
    implementation "androidx.exifinterface:exifinterface:${gradle.andoidxVersion}"
    implementation "androidx.appcompat:appcompat:${gradle.andoidxVersion}"
    implementation "androidx.legacy:legacy-support-v4:${gradle.andoidxVersion}"

    implementation "androidx.media:media:${gradle.andoidxVersion}"
//    implementation "androidx.fragment:fragment:${gradle.andoidxVersion}"
//    implementation "androidx.transition:transition:${gradle.andoidxVersion}"
//    implementation "androidx.vectordrawable:vectordrawable:${gradle.andoidxVersion}"
//    implementation "androidx.vectordrawable:vectordrawable-animated:${gradle.andoidxVersion}"
//    implementation "androidx.legacy:legacy-support-core-ui:${gradle.andoidxVersion}"


    implementation "androidx.annotation:annotation:${gradle.andoidxVersion}"
    implementation "androidx.core:core:${gradle.andoidxVersion}"
    implementation "androidx.legacy:legacy-support-core-utils:${gradle.andoidxVersion}"
    implementation "androidx.constraintlayout:constraintlayout:${gradle.andoidxConstraintlayoutVersion}"
    implementation "androidx.recyclerview:recyclerview:${gradle.andoidxVersion}"
    implementation "androidx.cardview:cardview:${gradle.andoidxVersion}"
//    implementation "androidx.multidex:multidex:2.0.0"
//    implementation "androidx.legacy:legacy-support-v13:${gradle.andoidxVersion}"
    implementation "com.google.android.material:material:${gradle.andoidxVersion}"

    implementation 'ren.yale.android:cachewebviewlib:2.1.8'

    implementation 'com.getui:gtsdk:3.2.2.0'
    implementation 'com.getui:gtc:3.1.2.0'

    implementation 'com.tencent:mmkv:1.2.11'

    implementation 'commons-cli:commons-cli:1.2'
    implementation  depLibProjectWithAarMaven("j2v8-debugger")

    implementation  (platform("com.mpaas.android:$mpaas_artifact:$mpaas_baseline")){
        exclude group:'com.mpaas.commonlib', module: 'libcshared-build'
    }

    implementation  ("com.mpaas.android:scan"){
        exclude group:'com.mpaas.commonlib', module: 'libcshared-build'
    }
    implementation "com.android.billingclient:billing:4.1.0"

    implementation 'com.hihonor.mcs:push:8.0.12.307'
}
