package com.fxiaoke.host;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Parcelable;
import android.text.TextUtils;

import com.facishare.fs.biz_function.webview.CheckWebActivity;
import com.facishare.fs.biz_function.webview.JsApiWebFullScreenActivity;
import com.facishare.fs.js.utils.JsApiHelper;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.IJsApiWebActivity;
import com.facishare.fs.pluginapi.webview.WebViewCoreEnum;
import com.fs.beans.webview.FSWebShareData;
import com.fxiaoke.fscommon.sandbox.SandboxContextManager;
import com.fxiaoke.fscommon.sandbox.SandboxUtils;
import com.fxiaoke.fshttp.web.sandbox.ISandboxContext;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import okhttp3.Cookie;

/**
 * Created by wubb on 2017/3/20.
 */

public class JsApiWebActivityImp implements IJsApiWebActivity {
    private String ACTION_JsApiWebActivity="fs.intent.action.JsApiWebActivity";

    final String Input_key_url = "Input_key_url";
    final String INPUT_WEB_VIEW_CORE = "input_key_web_view_core";
    final String Input_key_Title = "Input_key_title";
    final String Input_key_Show_Close_Btn = "Input_key_show_close_btn";
    final String Input_key_is_h5 = "input_key_is_h5";
    final String NEED_ERROR_PAGE = "need_error_page";
    final String INPUT_KEY_SHARE_DATA = "INPUT_KEY_SHARE_DATA";
    final String Input_key_Type = "Input_key_Type";
    final String KEY_SHOW_TITLE_BAR="key_show_title_bar";


    @Override
    public void setRunInJsApiProcess(boolean b){
        if(b){
            ACTION_JsApiWebActivity="fs.intent.action.JsApiWebActivity";
        }else{
            ACTION_JsApiWebActivity="fs.intent.action.JsApiWebFsProcessActivity";
        }
    }


    private static class JsApiWebActivityImpHolder{
        private static JsApiWebActivityImp instance=new JsApiWebActivityImp();
    }

    public static JsApiWebActivityImp getInstance(){
        return JsApiWebActivityImpHolder.instance;
    }


    @Override
    public Intent createIntent(Context context,String url) {
        return createIntent(context, url,true);
    }

    @Override
    public Intent createIntent(Context context, String url, boolean showTitleBar) {
        if(TextUtils.isEmpty(url)) return null;

        Intent intent=new Intent(ACTION_JsApiWebActivity);
        intent.putExtra(Input_key_url,url);
        intent.putExtra(KEY_SHOW_TITLE_BAR,showTitleBar);
        intent.putExtra(Input_key_is_h5,true);
        setCookiesFromContext(context,intent);
        intent.setPackage(HostInterfaceManager.getHostInterface().getApp().getPackageName());
        return intent;
    }

    @Override
    public Intent createIntent(Context context, String url, HashMap params) {
        if(TextUtils.isEmpty(url)) return null;

        Intent intent=new Intent(ACTION_JsApiWebActivity);
        intent.putExtra(Input_key_url,url);
        setCookiesFromContext(context,intent);
        intent.setPackage(HostInterfaceManager.getHostInterface().getApp().getPackageName());

        if (params != null) {
            Iterator<String> iterator = params.keySet().iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                Object value = params.get(key);
                if (value instanceof String) {
                    intent.putExtra(key, (String) value);
                } else if (value instanceof Boolean) {
                    intent.putExtra(key, (boolean) value);
                } else if (value instanceof Integer) {
                    intent.putExtra(key, (int) value);
                } else if (value instanceof Long) {
                    intent.putExtra(key, (long) value);
                } else if (value instanceof Parcelable) {
                    intent.putExtra(key, (Parcelable) value);
                } else if (value instanceof Serializable) {
                    intent.putExtra(key, (Serializable) value);
                }
            }
            boolean openInBrowser = false;
            if(params.containsKey("openInBrowser")){
               Object obj = params.get("openInBrowser");
                openInBrowser = obj instanceof Boolean && (boolean) obj;
            }
            if(openInBrowser){
                intent = JsApiHelper.getBrowserIntent(url);
            }
        }

        return intent;
    }

    @Override
    public void startActivity(Context context, String url){
        startActivity(context, url,true);
    }

    @Override
    public void startActivity(Context context, String url, boolean showTitleBar) {
        if(context==null) return;
        if(TextUtils.isEmpty(url)) return;

        Intent intent=new Intent(ACTION_JsApiWebActivity);
        intent.putExtra(Input_key_url,url);
        intent.putExtra(KEY_SHOW_TITLE_BAR,showTitleBar);
        setCookiesFromContext(context,intent);
        intent.setPackage(HostInterfaceManager.getHostInterface().getApp().getPackageName());
        context.startActivity(intent);
    }

    @Override
    public void startActivity(Context context,String url,String title){
        if(context==null) return;
        if(TextUtils.isEmpty(url)) return;

        Intent intent=new Intent(ACTION_JsApiWebActivity);
        intent.putExtra(Input_key_url,url);
        intent.putExtra(Input_key_Title,title);
        setCookiesFromContext(context,intent);
        intent.setPackage(HostInterfaceManager.getHostInterface().getApp().getPackageName());
        context.startActivity(intent);
    }

    @Override
    public void startActivity(Context context,String url,String title,boolean isShowCloseBtn){
        if(context==null) return;
        if(TextUtils.isEmpty(url)) return;

        Intent intent=new Intent(ACTION_JsApiWebActivity);
        intent.putExtra(Input_key_url,url);
        intent.putExtra(Input_key_Title,title);
        intent.putExtra(Input_key_Show_Close_Btn,isShowCloseBtn);
        setCookiesFromContext(context,intent);
        intent.setPackage(HostInterfaceManager.getHostInterface().getApp().getPackageName());
        context.startActivity(intent);
    }

    @Override
    public void startActivity(Context context,String url,String title,boolean isH5ControlTitle,boolean isNeedErrorPage){
        if(context==null) return;
        if(TextUtils.isEmpty(url)) return;

        Intent intent=new Intent(ACTION_JsApiWebActivity);
        intent.putExtra(Input_key_url,url);
        intent.putExtra(Input_key_Title,title);
        intent.putExtra(Input_key_is_h5,isH5ControlTitle);
        intent.putExtra(NEED_ERROR_PAGE,isNeedErrorPage);
        setCookiesFromContext(context,intent);
        intent.setPackage(HostInterfaceManager.getHostInterface().getApp().getPackageName());
        context.startActivity(intent);
    }

    @Override
    public void startActivity(Context context,String url,String title,boolean isShowCloseBtn,boolean isH5ControlTitle,boolean isNeedErrorPage){
        if(context==null) return;
        if(TextUtils.isEmpty(url)) return;

        Intent intent=new Intent(ACTION_JsApiWebActivity);
        intent.putExtra(Input_key_url,url);
        intent.putExtra(Input_key_Title,title);
        intent.putExtra(Input_key_Show_Close_Btn,isShowCloseBtn);
        intent.putExtra(Input_key_is_h5,isH5ControlTitle);
        intent.putExtra(NEED_ERROR_PAGE,isNeedErrorPage);
        setCookiesFromContext(context,intent);
        intent.setPackage(HostInterfaceManager.getHostInterface().getApp().getPackageName());
        context.startActivity(intent);
    }

    @Override
    public void startActivity(Context context, String url, String title, String summary, String coverPicture, String sessionType){
        if(context==null) return;
        if(TextUtils.isEmpty(url)) return;
        if(sessionType==null)
            sessionType="";

        FSWebShareData fsWebShareData=new FSWebShareData(title,summary,coverPicture);

        Intent intent=new Intent(ACTION_JsApiWebActivity);
        intent.putExtra(Input_key_url,url);
        intent.putExtra(Input_key_Title,title);
        intent.putExtra(INPUT_KEY_SHARE_DATA,fsWebShareData);
        intent.putExtra(Input_key_Type,sessionType);
        setCookiesFromContext(context,intent);
        intent.setPackage(HostInterfaceManager.getHostInterface().getApp().getPackageName());
        context.startActivity(intent);
    }

    @Override
    public void startActivity(Context context,String url,String title,String summary, String coverPicture, String sessionType,
                              boolean isShowCloseBtn,boolean isH5ControlTitle,boolean isNeedErrorPage){
        if(context==null) return;
        if(TextUtils.isEmpty(url)) return;
        if(sessionType==null)
            sessionType="";

        FSWebShareData fsWebShareData=new FSWebShareData(title,summary,coverPicture);

        Intent intent=new Intent(ACTION_JsApiWebActivity);
        intent.putExtra(Input_key_url,url);
        intent.putExtra(Input_key_Title,title);
        intent.putExtra(Input_key_Show_Close_Btn,isShowCloseBtn);
        intent.putExtra(Input_key_is_h5,isH5ControlTitle);
        intent.putExtra(NEED_ERROR_PAGE,isNeedErrorPage);
        intent.putExtra(INPUT_KEY_SHARE_DATA,fsWebShareData);
        intent.putExtra(Input_key_Type,sessionType);
        setCookiesFromContext(context,intent);
        intent.setPackage(HostInterfaceManager.getHostInterface().getApp().getPackageName());
        context.startActivity(intent);
    }

    @Override
    public void startActivityForResult(Activity activity, String url, String title, boolean isShowCloseBtn, int requestCode) {
        Intent intent=new Intent(ACTION_JsApiWebActivity);
        intent.putExtra(Input_key_url,url);
        intent.putExtra(Input_key_Title,title);
        intent.putExtra(Input_key_Show_Close_Btn,isShowCloseBtn);
        setCookiesFromContext(activity,intent);
        intent.setPackage(HostInterfaceManager.getHostInterface().getApp().getPackageName());
        activity.startActivityForResult(intent,requestCode);
    }

    @Override
    public void startActivityForResult(Activity activity, WebViewCoreEnum webViewCoreEnum, String url, String title, boolean isShowCloseBtn,boolean isLandScape, int requestCode) {
        if(isLandScape){
            Intent intent = new Intent(activity, JsApiWebFullScreenActivity.class);
            intent.putExtra(Input_key_url,url);
            intent.putExtra(INPUT_WEB_VIEW_CORE,webViewCoreEnum);
            intent.putExtra(Input_key_Title,title);
            intent.putExtra(Input_key_Show_Close_Btn,isShowCloseBtn);
            intent.putExtra(CheckWebActivity.IS_NEED_LANDSCAPE,isLandScape);
            setCookiesFromContext(activity,intent);
            intent.setPackage(HostInterfaceManager.getHostInterface().getApp().getPackageName());
            activity.startActivityForResult(intent,requestCode);
        }else{
            Intent intent=new Intent(ACTION_JsApiWebActivity);
            intent.putExtra(Input_key_url,url);
            intent.putExtra(INPUT_WEB_VIEW_CORE,webViewCoreEnum);
            intent.putExtra(Input_key_Title,title);
            intent.putExtra(Input_key_Show_Close_Btn,isShowCloseBtn);
            intent.putExtra(CheckWebActivity.IS_NEED_LANDSCAPE,isLandScape);
            setCookiesFromContext(activity,intent);
            intent.setPackage(HostInterfaceManager.getHostInterface().getApp().getPackageName());
            activity.startActivityForResult(intent,requestCode);
        }

    }

    /**
     * 传递prm cookie
     * @param context
     * @param intent
     */
    private void setCookiesFromContext(Context context,Intent intent){
        HashMap<String,String> params = new HashMap<>();
        ISandboxContext sandboxContext =
                SandboxContextManager.getInstance().getContext(SandboxUtils.getActivityByContext(context));
        if(sandboxContext!=null){
            List<Cookie> list = sandboxContext.getCookies();
            if(list != null){
                for(Cookie ck: list){
                    params.put(ck.name(),ck.value());
                }
            }
            intent.putExtra("upCookies",params);
        }

    }
}