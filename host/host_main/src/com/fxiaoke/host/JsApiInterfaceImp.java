package com.fxiaoke.host;

import android.app.Activity;
import android.os.RemoteException;

import com.facishare.fs.pluginapi.jsapi.IJsApiInterface;
import com.facishare.fs.pluginapi.jsapi.IJsApiListener;
import com.fxiaoke.fscommon_res.qrcode.IQrScanProcessor;
import com.fxiaoke.fscommon_res.qrcode.QrScanProcessCallback;
import com.fxiaoke.fscommon_res.qrcode.QrScanProcessorHolder;

public class JsApiInterfaceImp implements IJsApiInterface {
    private static class Holder{
        private static JsApiInterfaceImp instance=new JsApiInterfaceImp();
    }

    public static JsApiInterfaceImp getInstance(){
        return Holder.instance;
    }

    @Override
    public void initQrScanProcessorHolder() {
        QrScanProcessorHolder.getInstance().init();
    }

    @Override
    public void addProcessor2QrScanProcessorHolder(final IJsApiListener listener) {
        QrScanProcessorHolder.getInstance().addProcessor(new IQrScanProcessor() {
            @Override
            public boolean interceptResult(String s) {
                return true;
            }

            @Override
            public void processResult(Activity activity, String qrCode, QrScanProcessCallback qrScanProcessCallback) {
                if(listener!=null && listener.asBinder().isBinderAlive())
                {
                    try {
                        listener.handler(qrCode);
                        qrScanProcessCallback.onSuccess();
                    } catch (RemoteException e){
                        e.printStackTrace();
                    }
                }
            }
        });
    }
}
