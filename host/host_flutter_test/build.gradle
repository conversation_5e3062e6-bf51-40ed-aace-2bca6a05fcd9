import org.apache.tools.ant.taskdefs.condition.Os
import org.tmatesoft.svn.core.wc.*
def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}
apply plugin: 'com.android.application'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"
//apply plugin: 'fastdex.app'
apply plugin: 'kotlin-android'
//hostMainHead()

//initTask()


//fastdex {
//    //开启
//    fastdexEnable =  !inJenkins.equals("yes")&&!gradle.g_isci
//    //default false
//    //自定义的compile任务,用来代替compileXXXXJavaWithJavac
//    useCustomCompile = false
//
//    //default 3
//    //当变化的java文件数量大于等于这个值时触发dex merge(随着变化的java文件的增多,补丁打包会越来越慢,dex merge以后当前的状态相当于全量打包以后的状态)
//    dexMergeThreshold = 3
//
//    //是否仅hook debug
//    onlyHookDebug = false
//}

/**
 * 获取git commit 数量作为versioncode
 * @return
 */
def getGitVersion() {
    def cmd = 'git rev-parse HEAD'

    def gitVersion = cmd.exec().text
    println "git version : "+gitVersion
    return gitVersion
}

def getMyVersionCode() {
    def verNums = gradle.versionName.tokenize('.');
    def versionCode = 0;
    if (verNums != null && verNums.size >= 3) {
        versionCode = (verNums[0] as Integer) * 100000 + (verNums[1] as Integer) * 10000 + (verNums[2] as Integer) * 1000 + (expandVersionCode as Integer);
    }
    return versionCode;
}

def getVersionNameByType(svnNum) {
    return gradle.versionName
}

def svnNum = getGitVersion()
def myVersionName = getVersionNameByType(svnNum)
def myVersionCode = getMyVersionCode()

android {
    compileSdkVersion gradle.compileSdkVersion
    buildToolsVersion gradle.buildToolsVersion
    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            jniLibs.srcDirs = ['libs']
            aidl.srcDirs = ['src']
            java.srcDirs = ['src']
            res.srcDirs = ['res']
            assets.srcDirs = ['assets'] //多了一个assets目录
        }
    }
    compileOptions {
        sourceCompatibility gradle.javaVersion
        targetCompatibility gradle.javaVersion
    }

    dexOptions {
        javaMaxHeapSize "4g"
    }


    defaultConfig {
        applicationId "com.facishare.fs"
        minSdkVersion 14
        targetSdkVersion gradle.targetSdkVersion
        versionCode myVersionCode
        versionName myVersionName
        flavorDimensions "channel"
		multiDexEnabled true

        multiDexKeepProguard file('multidex-config.pro')
//        println("appinit_t:"+appinit_t)
        manifestPlaceholders = [version_code_bcr:getPluginVersionCode(gradle.versionName_bcr,gradle.pluginsSeqidMap.get("bcr"))]
//                                ,appinit_trace:appinit_t]
        ndk {
            //设置支持的SO库架构
            abiFilters 'arm64-v8a'//, 'x86', 'armeabi-v7a', 'x86_64', 'arm64-v8a'
        }
    }
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
    }

    signingConfigs {
        releaseConfig {
            if (gradle.keyAlias != "") {
                keyAlias gradle.keyAlias
                keyPassword gradle.keyPassword
                storeFile file(gradle.storeFile)
                storePassword gradle.storePassword
            }

            // 使用旧版签名，禁用V2版签名模式，防止渠道包安装时校验失败
            v2SigningEnabled false
        }
    }
    buildTypes {
		debug{
            String localIP = GetlocalIP.getLocalHostIP();
            println 'local ip:'+ localIP;
			manifestPlaceholders = [weixin_appid:"wx27670b328e8743a8", TINKER_ID:gradle.tinkerId,localip:localIP]
            if(gradle.debugUseReleaseKeyStore){
                signingConfig signingConfigs.releaseConfig
            }
		}
        release {
			manifestPlaceholders = [weixin_appid:"wx869c3185ffdbf8c7", TINKER_ID:gradle.tinkerId,localip:""]

//            if(gradle.isNeedProguard()){
//                minifyEnabled true
//                proguardFiles getDefaultProguardFile('proguard-android.txt'), "proguard-project.txt"
//            }else{
//
//            }
//            debuggable true
            signingConfig signingConfigs.releaseConfig
        }
    }
    productFlavors {
        if(releaseType.equals("MAJIA")){
            online {
                applicationId = "com.facishare.fsmajia"
                manifestPlaceholders = [internal_vercode: svnNum,IFLYTEK_APPKEY:"568c7271",pkg_name:applicationId,app_display_name:"消费家居crm体验版"
                    ,external_host:"nouse",external_scheme:"nouse"]
            }
        }else{
            if(packageType.equals("OI")){
                println "正式包+内测包"
                internaltest {
                    applicationId = "com.facishare.fsneice"
                    versionCode = (myVersionCode+100000000) as Integer
                    manifestPlaceholders = [internal_vercode: svnNum,IFLYTEK_APPKEY:"59806bfa",pkg_name:applicationId,app_display_name:"纷享内测"
                                            ,external_host:"splash",external_scheme:"fxiaoke"]
                }
                online {
                    applicationId = "com.facishare.fs"
                    manifestPlaceholders = [internal_vercode: svnNum,IFLYTEK_APPKEY:"568c7271",pkg_name:applicationId,app_display_name:"纷享销客"
                                            ,external_host:"splash",external_scheme:"fxiaoke"]
                }
            }else if(packageType.equals("O")){
                println "只打正式包"
                online {
                    applicationId = "com.facishare.fs"
                    manifestPlaceholders = [internal_vercode: svnNum,IFLYTEK_APPKEY:"568c7271",pkg_name:applicationId,app_display_name:"纷享销客"
                                            ,external_host:"splash",external_scheme:"fxiaoke"]
                }
            }else if(packageType.equals("I")){
                println "只打内测包"
                internaltest {
                    applicationId = "com.facishare.fsneice"
                    versionCode = (myVersionCode+100000000) as Integer
                    manifestPlaceholders = [internal_vercode: svnNum,IFLYTEK_APPKEY:"59806bfa",pkg_name:applicationId,app_display_name:"纷享内测"
                                            ,external_host:"splash",external_scheme:"fxiaoke"]
                }
            }

            if(releaseType.equals("RELEASE")){
                onlinecrm {
                    applicationId = "com.facishare.fs"
                    manifestPlaceholders = [internal_vercode: svnNum,IFLYTEK_APPKEY:"568c7271",pkg_name:applicationId,app_display_name:"纷享销客"
                                            ,external_host:"splash",external_scheme:"fxiaoke"]
                }
            }
        }


    }
}



task commandLineTadk(type: Exec) {
}


def replaceRequstUrl() {
    android.applicationVariants.all { variant ->
//        variant.mergeResources.doLast {
//            String url = "https://www.fxiaoke.com"
//            if (envType.equals("ONLINE")) {
//                url = "https://www.fxiaoke.com"
//            }  else if (envType.equals("CESHI112")) {
//                url = "https://www.ceshi112.com"
//            }else if (envType.equals("CESHI113")) {
//                url = "https://www."
//            }
//            File valuesFile = file("${buildDir}/intermediates/res/merged/${variant.dirName}/values/values.xml")
//            String content = valuesFile.getText('UTF-8')
//            content = content.replaceAll("#requestUrl#", url)
//            content = content.replaceAll("#releaseType#", releaseType)
//            String share2App = "分享给企信好友"
//
//            content = content.replaceAll("#request_share_to_app#", share2App)
//            valuesFile.write(content, 'UTF-8')
//        }
        variant.outputs.each { output ->
            output.processManifestProvider.get().doLast {

                String url = "https://www.fxiaoke.com"
                if (envType.equals("ONLINE")) {
                    url = "https://www.fxiaoke.com"
                } else if (envType.equals("CESHI112")) {
                    url = "https://www.ceshi112.com"
                } else if (envType.equals("MENGNIU")) {
                    url = "https://msv.mengniu.cn"
                }
                //移除之前新增的能力——谷歌包默认域名为国际域名hws.fxiaoke.com，以解决国内企业的海外员工，使用纷享App登录不了的问题
                // （因为国内的企业未在海外的云服务地址上部署，会触发查找不到账号的问题，但国外的企业均会再国内备份，可以直接使用国内玉米）
                if ("MENGNIU".equals(packageType)) {//蒙牛包默认域名为msv.mengniu.cn
                    url = "https://msv.mengniu.cn"
                }
                //${buildDir}是指build文件夹
                //${variant.dirName}是flavor/buildtype，例如GooglePlay/release，运行时会自动生成
                //下面的路径是类似这样：build/intermediates/manifests/GooglePlay/release/AndroidManifest.xml
                def appName = "纷享销客"
                def appIcon = "@drawable/icon"
                def amapkey = "0aebb5cef5d9c36dbbf2da19df5fb6d3"
                def baidumapkey = "kEAozqTzcmudNpZB7RRYOwHOT75P1im0"
                def manifestFile = "${buildDir}/intermediates/manifests/full/${variant.dirName}/AndroidManifest.xml"
                def huaweiPushAppId = "1179047";
                def auth=variant.productFlavors[0].applicationId+".fssync";

                //将字符串REPLACE_KEY替换成flavor的名字
                if (variant.productFlavors[0] != null && variant.productFlavors[0].name.equals("internaltest")
                    &&variant.productFlavors[0].applicationId.contains("fsneice")) {
                    appName = "纷享内测"
                    appIcon = "@drawable/icontest"
                    amapkey = "6c0eb6d84f4f3234f073e7857b2814f8"
                    baidumapkey = "uvr37UZ5TGlKimh6aI32Tm5dQBbVWKRb"
                    huaweiPushAppId = "10678932";
                } else if (variant.productFlavors[0] != null && variant.productFlavors[0].name.equals("online")
                        &&variant.productFlavors[0].applicationId.contains("fsmajia")) {
                    appName = "消费家居crm体验版"
                    appIcon = "@drawable/iconmajia"
                } else if(variant.productFlavors[0] != null && variant.productFlavors[0].name.equals("onlinecrm")) {
                    appName = "纷享销客CRM"
                }


                def updatedContent = new File(manifestFile).getText('UTF-8').replaceAll("#replace_name#",
                        appName)
                updatedContent = updatedContent.replaceAll("#replace_icon#",
                        appIcon)
				updatedContent = updatedContent.replaceAll("#replace_amapkey#",
                        amapkey)
                updatedContent = updatedContent.replaceAll("#replace_baidumapkey#",
                        baidumapkey)
                updatedContent = updatedContent.replaceAll("#replace_huawei_push_appid#",
                        huaweiPushAppId)
                updatedContent = updatedContent.replaceAll("#replace_authority#",
                        auth)

                updatedContent = updatedContent.replaceAll("#requestUrl#", url)
                updatedContent = updatedContent.replaceAll("#releaseType#", releaseType)

                def currentApplicationId =null;
                if(variant!= null && variant.productFlavors[0]!=null&&variant.productFlavors[0]
                        .applicationId!=null){
                    currentApplicationId = variant.productFlavors[0].applicationId;
                }
                if(currentApplicationId!=null){
                    updatedContent = updatedContent.replaceAll("#aliyunVideoCertPath#", "assets/cert/"
                            + currentApplicationId +".crt")
                    println("replace aliyunVideoCertPath with applicationId " +
                            currentApplicationId)
                }

                new File(manifestFile).write(updatedContent, 'UTF-8') //将此次flavor的AndroidManifest.xml文件指定为我们修改过的这个文件
//                variant.outputs[0].processResources.manifestFile = file("${buildDir}/intermediates/manifests/full/${variant.dirName}/AndroidManifest.xml")
            }
        }

    }
}

replaceRequstUrl()

def ensureDir(File dir) {
	if(!dir.exists() && !dir.isDirectory()) {
		dir.mkdir()
	}
}


task secondDex(type: Exec) {
    def dx = "$System.env.ANDROID_HOME/build-tools/$gradle.buildToolsVersion/dx"
    //适配mac环境找不到System.env.ANDROID_HOME
    if (Os.isFamily(Os.FAMILY_MAC)) {
        dx = "${android.sdkDirectory}/build-tools/$gradle.buildToolsVersion/dx"
    }
    dx = dealFilePath(dx)
    workingDir rootProject.rootDir.absolutePath
	ensureDir(new File(rootProject.rootDir.absolutePath + "/output/dexoutput"))
    if (Os.isFamily(Os.FAMILY_WINDOWS)) {
        commandLine "cmd", "/c", "$dx --dex --output=output/dexoutput/multidex.dex libsDex"
    } else{
        commandLine "sh", "-c", "$dx --dex --output=output/dexoutput/multidex.dex libsDex"
    }
}


task secondDexJiagu(type: Exec) {
    def dx = "$System.env.ANDROID_HOME/build-tools/$gradle.buildToolsVersion/dx"
    //适配mac环境找不到System.env.ANDROID_HOME
    if (Os.isFamily(Os.FAMILY_MAC)) {
        dx = "${android.sdkDirectory}/build-tools/$gradle.buildToolsVersion/dx"
    }
    dx = dealFilePath(dx)
    workingDir rootProject.rootDir.absolutePath
    ensureDir(new File(rootProject.rootDir.absolutePath + "/output/dexoutput"))
    if (Os.isFamily(Os.FAMILY_WINDOWS)) {
        commandLine "cmd", "/c", "$dx --dex --output=output/dexoutput/jiaguDex.dex libsDexJiagu"
    } else{
        commandLine "sh", "-c", "$dx --dex --output=output/dexoutput/jiaguDex.dex libsDexJiagu"
    }
}



task testVersionCode() {
    println "vercode : " + getMyVersionCode()
}
task secondDexFs(type: Exec) {
    def dx = "$System.env.ANDROID_HOME/build-tools/$gradle.buildToolsVersion/dx"
    //适配mac环境找不到System.env.ANDROID_HOME
    if (Os.isFamily(Os.FAMILY_MAC)) {
        dx = "${android.sdkDirectory}/build-tools/$gradle.buildToolsVersion/dx"
    }
    dx = dealFilePath(dx)
    workingDir rootProject.rootDir.absolutePath
	ensureDir(new File(rootProject.rootDir.absolutePath + "/output/dexoutput"))
    if (Os.isFamily(Os.FAMILY_WINDOWS)) {
        println dx
        commandLine "cmd", "/c", "$dx --dex --output=output/dexoutput/fsdex.dex libsDexfs"
    } else {
        commandLine "sh", "-c", "$dx --dex --output=output/dexoutput/fsdex.dex libsDexfs"
    }
}


repositories {
    flatDir {
        dirs './'
    }
}

configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}
flutter {
    source '../../flutter/demo'
}
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
}
