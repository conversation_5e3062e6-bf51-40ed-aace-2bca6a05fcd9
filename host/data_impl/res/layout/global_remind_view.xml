<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="64dp">

    <ImageView
        android:id="@+id/img_remind_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_centerVertical="true"
        android:layout_marginLeft="18dp"
        android:scaleType="center"
        android:src="@drawable/ic_remind_ok"
            android:layout_marginStart="18dp" />

    <ImageView
        android:id="@+id/img_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="12dp"
        android:scaleType="center"
        android:src="@drawable/ic_arrow"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="12dp" />


    <TextView
        android:id="@+id/tv_remind_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="5dp"
        android:layout_toLeftOf="@id/img_arrow"
        android:layout_toRightOf="@id/img_remind_icon"
        android:ellipsize="end"
        android:singleLine="true"
        android:textColor="#515151"
        android:textSize="16dp"
            android:layout_toEndOf="@id/img_remind_icon"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="5dp"
            android:layout_toStartOf="@id/img_arrow" />

</RelativeLayout>