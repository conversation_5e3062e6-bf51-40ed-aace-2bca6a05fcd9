package com.fxiaoke.dataimpl.poll;

import android.content.Context;

import com.facishare.fs.pluginapi.poll.IPolling;
import com.facishare.fs.pluginapi.poll.OnPollingListener;

import java.util.Map;

/**
 * Created by lid on 2018/3/23.
 */

public class PollingProxy implements IPolling {
    PollingManager pm;
    Context mctx;

    public PollingProxy(Context ctx) {
        mctx = ctx;
    }

    void init() {
        if (pm == null) {
            PollingManager.init(mctx);
            pm = PollingManager.getInstance();
        }
    }


    public static void  clearVersions(Context ctx){
        PollingSP.clearAllVersions(ctx);
    }

    @Override
    public void pollingOnAppLaunch(OnPollingResultListener onPollingResultListener) {
        init();
        if (pm != null) {
            pm.pollingOnAppLaunch(onPollingResultListener);
        }
    }

    @Override
    public void pollingOnResume(OnPollingResultListener onPollingResultListener) {
        init();
        if (pm != null) {
            pm.pollingOnResume(onPollingResultListener);
        }
    }

    @Override
    public void pollingOnTimer(OnPollingResultListener onPollingResultListener) {
        init();
        if (pm != null) {
            pm.pollingOnTimer(onPollingResultListener);
        }
    }

    @Override
    public void registerPollingListener(String s, long l, OnPollingListener onPollingListener) {
        init();
        if (pm != null) {
            pm.registerPollingListener(s, l, onPollingListener);
        }
    }

    @Override
    public void registerPollingListener(String s, OnPollingListener onPollingListener) {

        init();
        if (pm != null) {
            pm.registerPollingListener(s, onPollingListener);
        }
    }

    @Override
    public void unregisterPollingListener(OnPollingListener onPollingListener) {
        init();
        if (pm != null) {
            pm.unregisterPollingListener(onPollingListener);
        }
    }

    @Override
    public void updatePollingTime(String s, long l) {
        init();
        if (pm != null) {
            pm.updatePollingTime(s, l);
        }
    }

    @Override
    public boolean contain(String s) {
        init();
        if (pm != null) {
            return pm.contain(s);
        }
        return false;
    }

    @Override
    public long getVersion(String s) {
        init();
        if (pm != null) {
            return pm.getVersion(s);
        }
        return 0;
    }

    @Override
    public long getVersion(String s, long l) {
        init();
        if (pm != null) {
            return pm.getVersion(s, l);
        }
        return 0;
    }

    @Override
    public Map<String, Long> getAllVersions() {
        init();
        if (pm != null) {
            return pm.getAllVersions();
        }
        return null;
    }
}
