/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.dataimpl.webview;

import com.facishare.fs.pluginapi.WebConstants;
import com.fxiaoke.fxlog.FCLog;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import androidx.pluginmgr.PluginManager;

/**
 * webview页面跳转的特性构造类
 * Created by wangkw on 2016/8/10.
 */
public class WebViewJumpBuilder {

    private final Uri.Builder uriBuilder;
    private final Intent intent = new Intent();

    public WebViewJumpBuilder(String url) {
        if (!TextUtils.isEmpty(url)) {
            uriBuilder = Uri.parse(url).buildUpon();
        } else {
            uriBuilder = new Uri.Builder();
        }
    }

    /**
     * 控制导航栏颜色，格式为：RRGGBBAA，如：FAFAFAFF
     */
    public WebViewJumpBuilder setNavBgColor(String rgb){
        uriBuilder.appendQueryParameter(WebConstants.ParamsKey.NAV_BGCOLOR, rgb);
        return this;
    }
    /**
     * 控制导航栏进度条颜色，格式为：RRGGBBAA，如：FAFAFAFF
     */
    public WebViewJumpBuilder setNavPbColor(String rgb){
        uriBuilder.appendQueryParameter(WebConstants.ParamsKey.NAV_PBCOLOR, rgb);
        return this;
    }
    /**
     * 控制是否显示纷享菜单，true为显示，false为不显示，默认显示
     */
    public WebViewJumpBuilder setNavFsMenu(boolean hasMenu){
        uriBuilder.appendQueryParameter(WebConstants.ParamsKey.NAV_FSMENU, String.valueOf(hasMenu));
        return this;
    }
    /**
     * 控制是否需二次鉴权，如果为true则需要，否则不需要，默认false
     */
    public WebViewJumpBuilder setFsAuth(boolean hasAuth){
        uriBuilder.appendQueryParameter(WebConstants.ParamsKey.FS_AUTH, String.valueOf(hasAuth));
        return this;
    }
    /**
     * 设置标题文字
     */
    public WebViewJumpBuilder setTitle(String title){
        intent.putExtra(WebConstants.ItKey.Input_key_Title, title);
        return this;
    }
    /**
     * 是否是H5控制页面，传true之后才能由网页控制标题
     */
    public WebViewJumpBuilder setIsH5(boolean isH5Ctrl){
        intent.putExtra(WebConstants.ItKey.Input_key_is_h5, isH5Ctrl);
        return this;
    }
    /**
     * 是否需要cookie，传true或false
     */
    public WebViewJumpBuilder setIsNeedCookie(boolean isNeedCookie){
        intent.putExtra(WebConstants.ItKey.Input_key_isNeedCookie, isNeedCookie);
        return this;
    }

    /**
     * 添加url参数
     */
    public WebViewJumpBuilder appendParameter(String key, Object value){
        uriBuilder.appendQueryParameter(key, String.valueOf(value));
        return this;
    }

    private void build(){
        String url = uriBuilder.build().toString();
        intent.putExtra(WebConstants.ItKey.Input_key_url, url);
        intent.setComponent(new ComponentName(FCLog.g_HostPkgName, WebConstants.ActivityName.JsWebViewName));
    }

    /**
     * 跳转到JsWebView页面
     */
    public boolean goJsWeb(Context context){
        build();
        return PluginManager.getInstance().startActivity(context, intent);
    }

    /**
     * 跳转到JsWebView页面, 带requestCode
     */
    public void goJsWeb(Activity context, int requestCode){
        build();
        PluginManager.getInstance().startActivityForResult(context, intent, requestCode);
    }


}
