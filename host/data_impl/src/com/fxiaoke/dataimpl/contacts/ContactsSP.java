package com.fxiaoke.dataimpl.contacts;

import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

import com.facishare.fs.pluginapi.AccountManager;
import com.facishare.fs.utils_fs.FsContextUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class ContactsSP {

    // TODO 重复内容的删除
    public static final String cache = "cache";
    public static final String pushState = "PushState";
    public static final String regPushID = "regPushID";
    public static final String vibrate_key = "vibrate_key";
    public static final String sound_key = "sound_key";

    private static  final String KEY_NO_REMIND="no_remind_key"; //设置消息免打扰
    private static final String KEY_NO_REMIND_START_TIME="no_remind_start_time_key"; //设置消息免打扰的开始时间
    private static final String KEY_NO_REMIND_END_TIME="no_remind_end_time_key"; //设置消息免打扰的结束时间

    public static final String FORMAT_STR = "yyyy-MM-dd HH:mm";


    public static SharedPreferences getSharedPreferences(Context context,String name){
        context= FsContextUtils.createPackageContext(context);
        return context.getSharedPreferences(name, Activity.MODE_PRIVATE);
    }

    public static String getKey(Context con, String key) {
        String service = AccountManager.getAccount().getEnterpriseAccount();
        String myID = AccountManager.getAccount().getEmployeeId();
        return service + "_" + myID + "_" + key;
    }

    public static String getCache(Context con, String key) {
        SharedPreferences sp = getSharedPreferences(con,cache);
        return sp.getString(getKey(con, key), null);
    }

    public static boolean isLogin(Context context) {
        return AccountManager.isLogin(context);
    }

    public static boolean isPushAvailable(Context ctx) {
        SharedPreferences sp = getSharedPreferences(ctx,pushState);
        return sp.getBoolean(pushState, true);
    }

    public static String getRegPushID(Context ctx) {
        SharedPreferences sp = getSharedPreferences(ctx,regPushID);
        String string = sp.getString(regPushID, "");
        return string;
    }

    public static String putRegPushID(Context ctx, String str) {
        SharedPreferences sp = getSharedPreferences(ctx,regPushID);
        sp.edit().putString(regPushID, str).commit();
        return str;
    }

    public static boolean isSound(Context ctx) {
        SharedPreferences sp = getSharedPreferences(ctx,pushState);
        return sp.getBoolean(sound_key, true);
    }

    public static boolean isVibrate(Context ctx) {
        SharedPreferences sp = getSharedPreferences(ctx,pushState);
        return sp.getBoolean(vibrate_key, true);
    }

    public static Boolean isNORemind(Context con) {
        SharedPreferences sp = getSharedPreferences(con,pushState);
        boolean employeeAccount = sp.getBoolean(KEY_NO_REMIND, false);
        return employeeAccount;
    }

    public static String getNORemindStartTime(Context con) {
        SharedPreferences sp = getSharedPreferences(con,pushState);
        String employeeAccount = sp.getString(KEY_NO_REMIND_START_TIME, null);
        return employeeAccount;
    }

    public static String getNORemindEndTime(Context con) {
        SharedPreferences sp = getSharedPreferences(con,pushState);
        String employeeAccount = sp.getString(KEY_NO_REMIND_END_TIME, null);
        return employeeAccount;
    }

    /**
     * 判断是否是免打扰
     * @return
     */
    public static boolean isInNoRemand(Context con){

        if(!isNORemind(con)){
            return false;
        }

        boolean isInNoRemand=false;

        Calendar curCalender = Calendar.getInstance();
//        curCalender.setTimeInMillis(System.currentTimeMillis());
//        curCalender.set(Calendar.YEAR, 2000);
//        curCalender.set(Calendar.MONTH,1);
//        curCalender.set(Calendar.DAY_OF_MONTH, 1);
//        long curTime=curCalender.getTimeInMillis();
        long curTime=curCalender.get(Calendar.HOUR_OF_DAY)*60+curCalender.get(Calendar.MINUTE);

        Calendar startCalender = Calendar.getInstance();
        if(TextUtils.isEmpty(ContactsSP.getNORemindStartTime(con))){
            return isInNoRemand;
        }
        startCalender.setTime(ContactsSP.parse(ContactsSP.getNORemindStartTime(con), ContactsSP.FORMAT_STR));
//        startCalender.set(Calendar.YEAR, 2000);
//        startCalender.set(Calendar.MONTH,1);
//        startCalender.set(Calendar.DAY_OF_MONTH, 1);
//        long startTime=curCalender.getTimeInMillis();
        long startTime=startCalender.get(Calendar.HOUR_OF_DAY)*60+startCalender.get(Calendar.MINUTE);

        Calendar endCalender = Calendar.getInstance();
        if(TextUtils.isEmpty(ContactsSP.getNORemindEndTime(con))){
            return isInNoRemand;
        }
        endCalender.setTime(ContactsSP.parse(ContactsSP.getNORemindEndTime(con), ContactsSP.FORMAT_STR));
//        endCalender.set(Calendar.YEAR, 2000);
//        endCalender.set(Calendar.MONTH,1);
//        endCalender.set(Calendar.DAY_OF_MONTH, 1);
//        long endTime=curCalender.getTimeInMillis();
        long endTime=endCalender.get(Calendar.HOUR_OF_DAY)*60+endCalender.get(Calendar.MINUTE);

        if(endTime>startTime&&(curTime>=startTime&&curTime<=endTime)){
            isInNoRemand=true;
        }
        if(endTime<=startTime){
            endTime+=(60*24);
            if(endTime>startTime&&(curTime>=startTime&&curTime<=endTime)){
                isInNoRemand=true;
            }
        }

        return isInNoRemand;
    }

    public static Date parse(String strDate, String pattern) {

        if (TextUtils.isEmpty(strDate)) {
            return null;
        }
        try {
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.parse(strDate);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 保存通用的缓存键值对，和账号无关
     * {@link com.facishare.fs.common_utils.CommonSPUtils#save(Context, String, String)}
     */
    @Deprecated
    public static void saveCommonCache(Context con,String cacheKey,String cacheValue) {
        SharedPreferences sp = getSharedPreferences(con,cache);
        sp.edit().putString(cacheKey, cacheValue).commit();
    }

    /**
     * 获取缓存的sp值
     * {@link com.facishare.fs.common_utils.CommonSPUtils#get(Context, String)}
     */
    @Deprecated
    public static String getCommonCache(Context con,String cacheKey) {
        SharedPreferences sp = getSharedPreferences(con,cache);
        return sp.getString(cacheKey, null);
    }
}
