/*
 * Copyright (C) 2023 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.dataimpl.feed;

import java.util.List;

import org.mortbay.util.UrlEncoded;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.pluginapi.common_beans.Attach;
import com.facishare.fs.pluginapi.crm.beans.SimgleBizObjs2SOBParam;
import com.fs.fsprobuf.ServerProtobuf;
import com.fxiaoke.fscommon.util.FsUrlUtils;
import com.fxiaoke.fshttp.web.http.WebApiUtils;

import android.app.Activity;
import android.text.TextUtils;

public class ActiveRecordObjUtils {
    /**
     * 跳转新建销售记录页面
     *
     * @param activity
     * @param crmParam
     * @param summary
     * @param mergeResult
     */
    public static void startSendActiveRecordObjPage(Activity activity, SimgleBizObjs2SOBParam crmParam, String summary, ServerProtobuf.CreateBaseMergeMessageResult mergeResult,
                                                    List<Attach> attachList) {
        //云姐：https://wiki.firstshare.cn/pages/viewpage.action?pageId=209185503
        //                                Intent intent = HostInterfaceManager.getICrmRemote().getSendSalesRecordIntent(SessionMsgActivity.this, finalParam,
        //                                        I18NHelper.getText("qx.session.msg_guide.history_from_group_session")/* 来自企信群组的聊天记录 */,
        //                                        linkUrl, mergeResult.getTitle(), SessionInfoUtils.getSessionCardDes(mSessionDefinition));
        //                                intent.putExtra(CrmFeedImpl.KEY_CONTENT, mergeResult.getDescription());
        //                                intent.putExtra(SendSalesRecordActivity.KEY_ATTACH_LIST, (Serializable) attachList);
        //                                intent.putExtra(SendBaseUtils.PAGE_COVER_PICTURE, defaultIconPath);
        //                                SessionMsgActivity.this.startActivity(intent);
        JSONObject masterData = new JSONObject();
        JSONObject content = new JSONObject();
        try {
            appendTextData(content, mergeResult);
            appendAttachDataList(content, attachList);
            appendUrlData(content, mergeResult, summary);
            content.put("from", "fs_chat");//标记数据输入源
            masterData.put("active_record_content", content);

            appendRelatedObject(masterData, crmParam);
        } catch (Exception e) {
            e.printStackTrace();
        }

        String eventLink = createAddRecordUrl(masterData);//"event://CRM/ObjModify?type=Add&apiName=ActiveRecordObj";//新建销售记录链接
//        eventLink += "&masterData=" + UrlEncoded.encodeString(JSON.toJSONString(masterData));
        FsUrlUtils.gotoAction(activity, eventLink);
    }

    private static void appendRelatedObject(JSONObject masterData, SimgleBizObjs2SOBParam crmParam) {
        if (crmParam != null && crmParam.mCoreObjType != null && crmParam.mCoreObjType.apiName != null) {
//            JSONObject object = new JSONObject();
//            JSONArray jsonArray = new JSONArray();
//            for (String id : crmParam.mIdList) {
//                jsonArray.add(id);
//            }
//            object.put(crmParam.mCoreObjType.apiName, jsonArray);
//            masterData.put("related_object", object);//related_object_data

            JSONArray related_object_data = new JSONArray();
            for (String id : crmParam.mIdList) {
                JSONObject jsonObject = new JSONObject();
                //            {
                //                "describe_api_name": "AccountObj",
                //                    "id": "60fe6c04fff6110001736419",
                //                    "name": "测试跟进记录0726"
                //            {
                //                "describe_api_name": "object_op__c",//这种自定义的apiname，没法在预制CoreObjType中查找到
                //                    "id": "5e845a05da7f1c0001ae82ac",
                //                    "name": "纷享研发运维协调组"
                //            }
                jsonObject.put("describe_api_name", "".equals(crmParam.mCoreObjType.apiName) ? crmParam.sourceApiName : crmParam.mCoreObjType.apiName);
                jsonObject.put("id",id);
                related_object_data.add(jsonObject);
            }
//            jsonObject.put("name",crmParam.);
            masterData.put("related_object_data", related_object_data);
        }
    }

    private static void appendTextData(JSONObject content, ServerProtobuf.CreateBaseMergeMessageResult mergeResult) {
        String text = mergeResult.getDescription();
        //"杨武刚:[图片]\n"
        //                + "杨武刚:[语音]\n"
        //                + "杨武刚:[文档]\n"
        //                + "杨武刚:[文档]";
        content.put("text", text);
    }

    private static void appendAttachDataList(JSONObject content, List<Attach> attachList) {
        if (attachList != null && attachList.size() > 0) {
            JSONArray jsonArray = new JSONArray();
            for (Attach attach : attachList) {
                //                    "height": 0,
                //                            "isNormal": true,
                //                            "name": "Screenshot_20221109-223655.jpg",
                //                            "path": "N_202211_24_e0bf9dafea4146e59be0701db9848144",
                //                            "previewAble": true,
                //                            "previewFormat": 1,
                //                            "size": 367174,
                //                            "source": 1,
                //                            "subType": 0,
                //                            "width": 0
                JSONObject attachJSON = new JSONObject();
                attachJSON.put("name", attach.getFileName());
                attachJSON.put("path", attach.getFilePath());
                attachJSON.put("size", attach.getSize());
                attachJSON.put("subType", attach.getFileType());
                attachJSON.put("previewFormat", 1);//TODO: ?
                attachJSON.put("previewAble", true);//TODO: ?
                attachJSON.put("isNormal", true);//TODO: ?
                jsonArray.add(attachJSON);
            }
            content.put("attachments", jsonArray);
        }
    }

    private static void appendUrlData(JSONObject content, ServerProtobuf.CreateBaseMergeMessageResult mergeResult, String summary) {
        String title = mergeResult.getTitle();
        String url = WebApiUtils.changeToAbsoluteUrl(mergeResult.getLinkUrl());
        String icon = "https://a9.fspage.com/FSR/weex/avatar/feed/images/chat_history_to_sales_record.png";
        appendUrlDataToJSONObj(content, title, url, icon, summary, null);
    }

    private static void appendUrlDataToJSONObj(JSONObject content, String title, String url, String icon, String summary, String desc) {
        if (content != null && !TextUtils.isEmpty(url)) {
            //                "url": {//这里是邮件转销售记录/企信转销售记录挂的链接
            //                    "title": "邮件办公，如此轻松！欢迎来到网易邮箱！",
            //                            "desc": "欢迎来到网易邮箱 不想被邮件占据太多时间？网易邮箱大师让您真正回归高效生活。 来看看我们能为您做点儿什么？ 随时随地 多端协同 随时随地，全平台使用 支持iOS",
            //                            "url": "https://www.ceshi112.com/fsh5/email/5.4/email-detail.html?id=23872&code=f4cfc5379d6a6496d2e69db749120319&fs_nav_fsmenu=false",
            //                            "icon": null,
            //                            "summary": "欢迎来到网易邮箱 不想被邮件占据太多时间？网易邮箱大师让您真正回归高效生活。 来看看我们能为您做点儿什么？ 随时随地 多端协同 随时随地，全平台使用 支持iOS"
            //                },
            JSONObject urlJSON = new JSONObject();
            urlJSON.put("title", title);
            urlJSON.put("url", url);
            urlJSON.put("icon", icon);
            urlJSON.put("summary", summary);
            if (desc != null) {
                urlJSON.put("desc", desc);
            }
            content.put("url", urlJSON);
        }
    }

    private static String createAddRecordUrl(JSONObject masterData) {
        //參考周振：https://wiki.firstshare.cn/pages/viewpage.action?pageId=206055923
        //event://CRM/ObjModify?type=Add&apiName=SalesOrderObj&toDetail=true&masterData=
        //        &masterData=%7B%22_id%22%3A%22xxx%22%7D
        //        参数说明（encode前）
        //        {
        //            type: "Add",//Add/Edit 新建/编辑
        //                    apiName: "SalesOrderObj",//目标对象apiName
        //                toDetail: true,//新建成功是否自动跳转详情页
        //                recordType: "default__c",//新建时指定业务类型
        //                masterData: {name: "测试回填&符号"},//主对象回填数据， 编辑场景下代表完整主对象数据， 其他场景可用于回填主对象数据，新建时数据中的业务类型当做白名单处理，内部会做可用性校验
        //            detailData: {"a": [{}]}//从对象回填数据，编辑场景外部不用传内部会自行获取
        //        }
        String eventLink = "event://CRM/ObjModify?type=Add&apiName=ActiveRecordObj";
        if (masterData != null) {
            return eventLink + "&toDetail=true&masterData=" + UrlEncoded.encodeString(JSON.toJSONString(masterData));
        }
        return eventLink;
    }

    /**
     * 通过邮件转发新建销售记录页面
     * @param activity
     * @param url
     * @param title
     * @param summary
     * @param picUrl
     */
    public static void startEmailShareUrl(Activity activity, String title, String url, String picUrl, String summary) {
        JSONObject masterData = new JSONObject();
        JSONObject content = new JSONObject();
        try { //参数说明文档：https://wiki.firstshare.cn/pages/viewpage.action?pageId=209185503
            appendUrlDataToJSONObj(content, title, url, picUrl, summary, null);
            content.put("from", "fs_chat");//标记数据输入源
            masterData.put("active_record_content", content);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String eventLink = createAddRecordUrl(masterData);//"event://CRM/ObjModify?type=Add&apiName=ActiveRecordObj";//新建销售记录链接
//        eventLink += "&masterData=" + UrlEncoded.encodeString(JSON.toJSONString(masterData));
        FsUrlUtils.gotoAction(activity, eventLink);
    }
}
