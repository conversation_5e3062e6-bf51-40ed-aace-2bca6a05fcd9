package com.fxiaoke.dataimpl.file_service;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Intent;
import androidx.pluginmgr.PluginManager;
import com.facishare.fs.pluginapi.ISelectFile;
import com.fxiaoke.dataimpl.file_service.intent_provider.SelectFileIP;
import com.fxiaoke.fxlog.FCLog;

/**
 * Created by liuyu on 2015/9/6.
 */
public class SelectFileImpl implements ISelectFile {
    @Override
    public void go2SelectFile(Activity context, int reqCode, ComponentName backActivityName) {
        Intent it = new Intent();
        it.setComponent(new ComponentName(FCLog.g_HostPkgName,
                "com.facishare.fs.common_datactrl.files.FileMainActivity"));
        if (backActivityName != null) {
            it.putExtra(SelectFileIP.KEY_back2activity, backActivityName);
        }
        PluginManager.getInstance().
                startActivityForResult(context, it, reqCode);
    }
}
