import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.transform.Field

import java.security.MessageDigest

/**
 * 创建目录
 * @param path
 * @return
 */
def createDir(path) {
    def dir = new File(path)
    if (!dir.exists()) {
        dir.mkdirs()
    }
}

/**
 * copy文件到Jar的目录下
 * @param filePath
 * @param jarName
 * @return
 */
def copyFileToJarDir(jarName) {
    copyFile(project.getProjectDir().getAbsolutePath() + "/build/intermediates/compile_library_classes/debug/classes.jar",
            rootProject.getRootDir().getAbsolutePath() + "/moduleJarLibs/" + jarName)
}


def getJarFile(buildType){
    File file = new File(project.getProjectDir().getAbsolutePath() + "/build/intermediates/aar_main_jar/"+buildType+"/classes.jar")
    if(!file.exists()){
        file = new File(project.getProjectDir().getAbsolutePath() + "/build/intermediates/compile_library_classes/"+buildType+"/classes.jar")
    }
    return file.getAbsolutePath()

}

def copyFileToDexJarDir_temp(dir,buildType,jarName) {
    copyFile(getJarFile(buildType),
            rootProject.getRootDir().getAbsolutePath() + "/moduleJarLibs_temp/" + jarName)
}




def copyFileToDexJarDirJiagu(dir,buildType,jarName) {
    copyFile(getJarFile(buildType),
            rootProject.getRootDir().getAbsolutePath() + "/libsDexJiagu/" + jarName)
}

def copyFileToDexJarDirMain(dir,buildType,jarName) {
    copyFile(getJarFile(buildType),
            rootProject.getRootDir().getAbsolutePath() + "/libsDexMain/" + jarName)
}


/**
 * copy文件
 * @param filePath 文件路径
 * @param targetPath 目标文件
 */
def copyFile(filePath, targetPath) {
    def file = new File(filePath);
    def targetFile = new File(targetPath);
    if (!file.exists()) {
        print filePath + " not exists"
        return
    }
    if (targetFile.exists()) {
        targetFile.delete()
    }
    targetFile.getParentFile().mkdirs()
    targetFile.withOutputStream { os ->
        file.withInputStream { ism ->
            os << ism
        }
    }
}

def copyDirFile(srcDirPath, targetDirPath){
    println(srcDirPath)
    println(targetDirPath)
    def srcDir = new File(srcDirPath)
    def targetDir = new File(targetDirPath)
    if(!srcDir.exists() || !targetDir.exists()){
        return
    }
    def files = srcDir.list()
    for(fileName in files){
        println(fileName)
        copyFile(srcDirPath+"/"+fileName, targetDirPath+"/"+fileName)
    }
}

def copyMappingTxt() {
    copyFile("$project.projectDir.absolutePath/build/outputs/mapping/online/release/mapping.txt",
            gradle.outDir + "/mappings/$project.name\$mapping.txt")
}

def getVersionNameAdvanced() {
    def xmlFile = project.file("AndroidManifest.xml")
    def rootManifest = new XmlSlurper().parse(xmlFile)
    return rootManifest['@android:versionName']
}


def needToModuleJar(projectName) {
    for (name in gradle.moduleJarProjects) {
        if (name.equals(projectName)) {
            return true;
        }
    }
    return false
}

def needToModuleJar_temp(projectName) {
    for (name in gradle.moduleJarProjects_temp) {
        if (name.equals(projectName)) {
            return true;
        }
    }
    return false
}




def needToDexJar_JIAGU(projectName) {
    for (name in gradle.dexJarProjects_JIAGU) {
        if (name.equals(projectName)) {
            return true;
        }
    }
    return false
}

def needToDexJar_Main(projectName) {
    for (name in gradle.dexJarProjects_Main) {
        if (name.equals(projectName)) {
            return true;
        }
    }
    return false
}



def isPluginsApk(projectName) {
    for (name in gradle.pluginsApk) {
        if (name.equals(projectName)) {
            return true;
        }
    }
    return false
}

def isDownloadPluginsApk(projectName) {
    for (name in gradle.downloadPluginsApk) {
        if (name.equals(projectName)) {
            return true;
        }
    }
    return false
}

//def initPlugConfig() {
////    def targetFilePaht = project.projectDir.absolutePath + "/assets/plugins/plugins.json";
////    def plugjsonstrs = new PlugConfig[gradle.pluginsApk.size];
////    def i = 0;
////
////    for (name in gradle.pluginsApk) {
////        PlugConfig plugConfig = new PlugConfig();
////        plugConfig.name = name + ".apk";
////        plugConfig
////
////        plugjsonstrs[i] = "plugins/" + name + ".apk";
////        i++
////    }
////    def json = JsonOutput.toJson(plugjsonstrs);
////    new File(targetFilePaht).write(json);
//    copyFile("$rootProject.projectDir.absolutePath/plugins.json",
//        project.projectDir.absolutePath + "/assets/plugins/plugins.json")
//
//}

def getPluginSdcardPath(){
    return "/sdcard/testplugin"
}

def getSDCardPlugConfigPath(){
    return getPluginSdcardPath() + "/plugins.json"
}

def getProjectApk(String buildType) {
    return project.projectDir.absolutePath + "/build/outputs/apk/"+ buildType + "/" + project.name + "-" + buildType + ".apk"
}

def getProjectApk(String flavor, String buildType) {
    return project.projectDir.absolutePath + "/build/outputs/apk/"+ flavor+"/"+buildType + "/" + project.name + "-" + flavor + "-" + buildType + ".apk"
}
def getSDCardProjectApk(String buildType) {
    return getPluginSdcardPath() + "/" + project.name + "-" + buildType + ".apk"
}

def getSDCardProjectApkSimpleName() {
    return getPluginSdcardPath() + "/" + project.name + ".apk"
}

def getProjectManifestPath(String buildType){
    return project.projectDir.absolutePath + "/build/intermediates/manifests/full/"+ buildType +"/AndroidManifest.xml"
}

def getSDCardProjectManifestPath(){
    return getPluginSdcardPath() + "/" + project.name + "_AndroidManifest.xml"
}

def copyQuickInstallFile(onlineapkname){
    copyDirFile(rootProject.rootDir.absolutePath+"/quickinstall",gradle.outDir)
    def quickinstallFile = new File(gradle.outDir+"/quick_install.html")
    def quickinstallStr = FileUtil.getFileStr(quickinstallFile)
    quickinstallStr=quickinstallStr
            .replace("##apkFileName##", onlineapkname)
            .replace("##版本号##",android.defaultConfig.versionCode+"")
            .replace("##打包时间##",new Date().getDateTimeString())
    FileUtil.writeToFile(quickinstallStr.getBytes(), quickinstallFile)
}

def hostMainHead() {
    tasks.whenTaskAdded { task ->

        if(task.name.equals("mergeInternaltestDebugNativeLibs")||task.name.equals("mergeInternaltestReleaseNativeLibs")) {
            task.doLast{
                String type = "Debug";
                if (task.name.contains("Release")) {
                    type = "Release";
                }
                copyFileToarm(type)
            }
        }

        if(task.name.equals("buildNeeded")) {
            deleteDexNormalFile()
        }
        if (task.name.equals("assembleOnlineDebug")) {
            task.doLast {
                copyFile(getProjectApk("online", "debug"), gradle.outDir + "/" + project.name + "-online-debug.apk")
            }

        } else if (task.name.equals("assembleInternaltestDebug")) {
            task.doLast {
                copyFile(getProjectApk("internaltest", "debug"), gradle.outDir + "/" + project.name + "-internaltest-debug.apk")
            }
        } else if (task.name.equals("assembleMengniuDebug")) {
            task.doLast {
                copyFile(getProjectApk("mengniu", "debug"), gradle.outDir + "/" + project.name + "-mengniu-debug.apk")
            }
        } else if (task.name.equals("assembleOnlineRelease")) {
            task.doLast {
                def versionName = android.defaultConfig.versionName
                def versionCode = android.defaultConfig.versionCode
                def onlineapkname = project.name + "-online--$envType-$releaseType-$versionName-$versionCode-.apk"
                copyFile(getProjectApk("online", "release"), gradle.outDir + "/" + onlineapkname)
                copyMappingTxt()
//                copyQuickInstallFile(onlineapkname)
            }
        } else if (task.name.equals("assembleArm32Release")) {
            task.doLast {
                def versionName = android.defaultConfig.versionName
                def versionCode = android.defaultConfig.versionCode
                def onlineapkname = project.name + "-arm32--$envType-$releaseType-$versionName-$versionCode-.apk"
                copyFile(getProjectApk("arm32", "release"), gradle.outDir + "/" + onlineapkname)

            }
        } else if (task.name.equals("assembleArm64Release")) {
            task.doLast {
                def versionName = android.defaultConfig.versionName
                def versionCode = android.defaultConfig.versionCode
                def onlineapkname = project.name + "-arm64--$envType-$releaseType-$versionName-$versionCode-.apk"
                copyFile(getProjectApk("arm64", "release"), gradle.outDir + "/" + onlineapkname)

            }
        }   else if (task.name.equals("assembleOnlinecrmRelease")) {
            task.doLast {
                def versionName = android.defaultConfig.versionName
                def versionCode = android.defaultConfig.versionCode
                copyFile(getProjectApk("onlinecrm", "release"), gradle.outDir + "/" + project.name + "-onlinecrm-" +
                        "-$envType-$releaseType-$versionName-$versionCode-.apk")
//                copyMappingTxt()
            }
        } else if (task.name.equals("assembleInternaltestRelease")) {
            task.doLast {
                def versionName = android.defaultConfig.versionName
                def versionCode = android.defaultConfig.versionCode
                copyFile(getProjectApk("internaltest", "release"), gradle.outDir + "/" + project.name + "-internaltest-" +
                        "-$envType-$releaseType-$versionName-$versionCode-.apk")
//                copyMappingTxt()
            }
        } else if (task.name.equals("assembleHuaweiRelease")) {
            task.doLast {
                def versionName = android.defaultConfig.versionName
                def versionCode = android.defaultConfig.versionCode
                copyFile(getProjectApk("huawei", "release"), gradle.outDir + "/" + project.name + "-huawei-" +
                        "-$envType-$releaseType-$versionName-$versionCode-.apk")
//                copyMappingTxt()
            }
        } else if (task.name.equals("assembleMengniuRelease")) {
            task.doLast {
                def versionName = android.defaultConfig.versionName
                def versionCode = android.defaultConfig.versionCode
                copyFile(getProjectApk("mengniu", "release"), gradle.outDir + "/" + project.name + "-mengniu-" +
                        "-$envType-$releaseType-$versionName-$versionCode-.apk")
//                copyMappingTxt()
            }
        } else if (task.name.equals("assembleRelease")||task.name.equals("assembleDebug")) {
            if (isInGitlabCi()||isInJenkins()){
                task.doLast {
                    println("delete dex $task.name")
                    deleteDir(new File(rootProject.rootDir.absolutePath + "/output/dexoutput"));

                }
            }
        }
        checkDisableTask(task)
    }
}

class GenerateFsDexTask extends DefaultTask {
    String flavor = ""
    String buildType=""
    String variantName=""

    String getFileMD5(File file) {
        if (!file.isFile()) {
            return null;
        }
        MessageDigest digest;
        byte []buffer = new byte[8192];
        int len;
        try {
            FileInputStream ins = new FileInputStream(file)
            digest =MessageDigest.getInstance("MD5");
            while ((len = ins.read(buffer)) != -1){
                digest.update(buffer, 0, len);
            }
            BigInteger bigInt = new BigInteger(1, digest.digest());
            return bigInt.toString(16);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    boolean isDirJarChange(File dir,Map<String,String> fsJarCache){
        boolean ret=false
        File [] files = dir.listFiles()
        if(files!=null) {
            for (File file : files) {
                String newMd5 = getFileMD5(file)
                String fileName = file.getName()
                if(fsJarCache.containsKey(fileName)){
                    String cacheMd5 = fsJarCache.get(fileName)

                    if(!cacheMd5.equals(newMd5)){
                        ret = true
                    }
                }else{
                    ret=true
                }
                fsJarCache.put(fileName,newMd5)
            }
        }else{
            ret = true
        }

        return ret
    }

    @TaskAction
    void make() {
        println 'begin dex ' + System.currentTimeMillis()
        //initPlugConfig()

        def slurper = new JsonSlurper()
        Map<String, String> fsJarCache = new HashMap()

        File fsJarInfoFile = new File(project.rootProject.rootDir.absolutePath, "/output/cache/" + project.name + "/fsjar_info.json")
        if (fsJarInfoFile.exists()) {
            fsJarCache = slurper.parse(fsJarInfoFile)
        }

        boolean jiaguChange = false, mainChange = false
        long start = System.currentTimeMillis();
        if (project.secondDexJiagu != null) {
            File file1 = new File(project.rootProject.rootDir.absolutePath, "/libsDexJiagu")
            File file2 = new File(project.rootProject.rootDir.absolutePath, "/output/dexoutput/jiaguDex.dex")
            if (isDirJarChange(file1, fsJarCache) || !file2.exists()) {
                println("secondDexJiagu")
                project.secondDexJiagu.exec()
                jiaguChange = true
            }

        }

        if (project.secondDexMain != null) {
            File file1 = new File(project.rootProject.rootDir.absolutePath, "/libsDexMain")
            File file2 = new File(project.rootProject.rootDir.absolutePath, "/output/dexoutput/mainDex.dex")
            if (isDirJarChange(file1, fsJarCache) || !file2.exists()) {
                println("secondDexMain")
                project.secondDexMain.exec()
                mainChange = true
            }
        }


        if (jiaguChange || mainChange) {
            def json = JsonOutput.toJson(fsJarCache);
            fsJarInfoFile.getParentFile().mkdirs()
            fsJarInfoFile.write(json);
        }

        println("GenerateFsDexTask cost times:" + (System.currentTimeMillis()-start))

        copyDexFileAfterTransform()

        encodeHostDexBeforePackage()

    }



    void encodeHostDexBeforePackage(){
        //找到dex，加密dex copy到assets下面
        println "encodeHostDexBeforePackage in ";
        File file = new File(project.rootProject.rootDir.absolutePath , "/output/cache/jiaguDex_temp.dex")
        if(file.exists()){
            FileUtil.encodeDex(file.getAbsolutePath());
            int i = 1;
            copyFile(file.getAbsolutePath(), new File(project.getProjectDir().getAbsolutePath() +
                    "/build/intermediates/merged_assets/" +
                    project.lowerFirstCase(variantName) + "/out" +  "/vigenere/" + "caesar_" + i +".so"));
            file.delete()
        }

    }

    def initPlugConfig() {
        copyFile(project.rootProject.projectDir.absolutePath+"/plugins.json",
                new File(project.projectDir.absolutePath + "/assets/plugins/plugins.json"))

    }


    /**
     * 递增指定目录中的dex
     *
     * classes.dex   => classes2.dex
     * classes2.dex  => classes3.dex
     * classesN.dex  => classes(N + 1).dex
     *
     * @param dexDir
     */
    boolean isLegalFile(File file) {
        if (file == null) {
            return false;
        }
        return file.exists() && file.isFile() && file.length() > 0;
    }
    void incrementDexDir(File dexDir,int dsize) {
        if (dsize <= 0) {
            throw new RuntimeException("dsize must be greater than 0!")
        }
        //classes.dex  => classes2.dex.tmp
        //classes2.dex => classes3.dex.tmp
        //classesN.dex => classes(N + 1).dex.tmp

        String tmpSuffix = ".tmp"
        File classesDex = new File(dexDir, "classes.dex")
        if (isLegalFile(classesDex)) {
            classesDex.renameTo(new File(dexDir,"classes${dsize + 1}.dex${tmpSuffix}"))
        }
        int point = 2
        File dexFile = new File(dexDir,"classes${point}.dex")
        while (isLegalFile(dexFile)) {
            new File(dexDir,"classes${point}.dex").renameTo(new File(dexDir,"classes${point + dsize}.dex${tmpSuffix}"))
            point++
            dexFile = new File(dexDir,"classes${point}.dex")
        }

        //classes2.dex.tmp => classes2.dex
        //classes3.dex.tmp => classes3.dex
        //classesN.dex.tmp => classesN.dex
        point = dsize + 1
        dexFile = new File(dexDir,"classes${point}.dex${tmpSuffix}")
        while (isLegalFile(dexFile)) {
            dexFile.renameTo(new File(dexDir,"classes${point}.dex"))
            point++
            dexFile = new File(dexDir,"classes${point}.dex${tmpSuffix}")
        }
    }



    void copyDexFileAfterTransform() {
        //将dex搬过来
        File dir = new File(project.buildDir.getAbsolutePath()+"/intermediates/dex/"+project.lowerFirstCase(variantName)+"/out/")
        if(!dir.exists()){
            println('dir 2')
            dir = new File(project.buildDir.getAbsolutePath()+"/intermediates/dex/"+project.lowerFirstCase(variantName)+"/shrunkDex/")
        }

        if(!dir.exists()){
            println('dir 1')
            dir = new File(project.buildDir.getAbsolutePath()+"/intermediates/dex/"+project.lowerFirstCase(variantName)+"/mergeProjectDex"+variantName+"/out/")

        }

        if(!dir.exists()){
            println('dir 3')
            dir = new File(project.buildDir.getAbsolutePath()+"/intermediates/transforms/dex/"+flavor+"/"+buildType+"/0/")
        }


        File fsDexInfoFile = new File(project.rootProject.rootDir.absolutePath , "/output/cache/"+project.name+"/fsdex_info.json")
        def slurper = new JsonSlurper()
        Map<String,Long> fsDexCache
        if(fsDexInfoFile.exists()){
            fsDexCache=slurper.parse(fsDexInfoFile)
            File [] listFile = dir.listFiles()
            if(fsDexCache!=null&&listFile!=null&&fsDexCache.size()>0){
                for(File file:listFile){
                    String fileName = file.getName()
                    if(fsDexCache.containsKey(fileName)){
                        long cacheModified = fsDexCache.get(fileName)
                        if(cacheModified==file.lastModified()){
                            file.delete()
                        }
                    }

                }
            }
        }

        if(!project.hasProperty("fastdex")||!project.fastdex.fastdexEnable){
            println("fastdex enable is false")
            incrementDexDir(dir,1) //递增指定目录中的dex

            //主dex放在第一位
            copyFile(project.rootProject.rootDir.absolutePath + "/output/dexoutput/mainDex.dex", new File(dir,"classes.dex"))
        }


        fsDexCache = new HashMap<>()

        int index = 0;
        if (dir.isDirectory()) {
            index = dir.list().size()
        }
        println("dex count :" + index)
        println("dir :" + dir.getAbsolutePath())

        File targetFile;
//        if(project.name.equals("host_main")) {
//            File extDir = new File(project.rootProject.rootDir.absolutePath + "/output/dexextra/");
//            if (extDir.isDirectory()) {
//                for (File file : extDir.listFiles()) {
//                    targetFile = getClassesDexFile(dir, ++index)
//                    copyFile(file.getAbsolutePath(), targetFile);
//                    fsDexCache.put(targetFile.getName(), targetFile.lastModified())
//                }
//            }
//        }


        if(project.hasProperty("fastdex")&&project.fastdex.fastdexEnable){
            println("fastdex enable is true")
            targetFile = getClassesDexFile(dir,++index)
            copyFile(project.rootProject.rootDir.absolutePath + "/output/dexoutput/mainDex.dex" ,targetFile);
            fsDexCache.put(targetFile.getName(),targetFile.lastModified())
        }


        def json = JsonOutput.toJson(fsDexCache);
        fsDexInfoFile.getParentFile().mkdirs()
        fsDexInfoFile.write(json);

        copyFile(project.rootProject.rootDir.absolutePath + "/output/dexoutput/jiaguDex.dex", new File(project.rootProject
                .rootDir
                .absolutePath + "/output/cache/jiaguDex_temp.dex"));
    }

    File getClassesDexFile(File dir,int index){
        return new File(dir,"classes" +index +".dex")
    }

    void copyFile(filePath, targetFile) {
        def file = new File(filePath)
//        def targetFile = new File(targetPath);
        if (!file.exists()) {
            print filePath + " not exists"
            return
        }
        if (targetFile.exists()) {
            targetFile.delete()
        }

        targetFile.getParentFile().mkdirs()
        targetFile.withOutputStream { os ->
            file.withInputStream { ism ->
                os << ism
            }
        }
    }
}
def replaceRequstUrl() {
    android.applicationVariants.all { variant ->
        variant.mergeResources.doLast {
            String url = "https://www.fxiaoke.com"
            if (envType.equals("ONLINE")) {
                url = "https://www.fxiaoke.com"
            } else if (envType.equals("CESHI112")) {
                url = "https://www.ceshi112.com"
            } else if (envType.equals("MENGNIU")) {
                url = "https://msv.mengniu.cn"
            }
            //移除之前新增的能力——谷歌包默认域名为国际域名hws.fxiaoke.com，以解决国内企业的海外员工，使用纷享App登录不了的问题
            // （因为国内的企业未在海外的云服务地址上部署，会触发查找不到账号的问题，但国外的企业均会再国内备份，可以直接使用国内域名）
            if ("MENGNIU".equals(packageType)) {//蒙牛包默认域名为msv.mengniu.cn
                url = "https://msv.mengniu.cn"
            }
            File valuesFile = file("${buildDir}/intermediates/res/merged/${variant.flavorName}/values/values.xml")
            String content = valuesFile.getText('UTF-8')
            content = content.replaceAll("#requestUrl#", url)
            content = content.replaceAll("#releaseType#", releaseType)
            content = content.replaceAll("#request_authority#", variant.productFlavors[0].applicationId+".fssync")
            content = content.replaceAll("#request_account_type#", variant.productFlavors[0].applicationId+".fsaccount")

            content = content.replaceAll("#aliyunVideoCertPath#", "assets/cert/"
                    + variant.productFlavors[0].applicationId +".crt")
            println("variant.productFlavors[0].applicationId = "+variant.productFlavors[0].applicationId)
            String share2App = "分享给企信好友"

            content = content.replaceAll("#request_share_to_app#", share2App)
            valuesFile.write(content, 'UTF-8')
        }
//        variant.outputs.each { output ->
//            output.processManifestProvider.get().doLast {
//                //${buildDir}是指build文件夹
//                //${variant.dirName}是flavor/buildtype，例如GooglePlay/release，运行时会自动生成
//                //下面的路径是类似这样：build/intermediates/manifests/GooglePlay/release/AndroidManifest.xml
//                def appName = "纷享销客"
//                def appIcon = "@drawable/icon"
//                def amapkey = "0aebb5cef5d9c36dbbf2da19df5fb6d3"
//                def baidumapkey = "kEAozqTzcmudNpZB7RRYOwHOT75P1im0"
//                def manifestFile = "${buildDir}/intermediates/manifests/full/${variant.dirName}/AndroidManifest.xml"
//                def huaweiPushAppId = "1179047";
//                def auth=variant.productFlavors[0].applicationId+".fssync";
//                //将字符串REPLACE_KEY替换成flavor的名字
//                if (variant.productFlavors[0] != null && variant.productFlavors[0].name.equals("internaltest")
//                        &&variant.productFlavors[0].applicationId.contains("fsneice")) {
//                    appName = "纷享内测"
//                    appIcon = "@drawable/icontest"
//                    amapkey = "6c0eb6d84f4f3234f073e7857b2814f8"
//                    baidumapkey = "uvr37UZ5TGlKimh6aI32Tm5dQBbVWKRb"
//                    huaweiPushAppId = "10678932";
//                } else if (variant.productFlavors[0] != null && variant.productFlavors[0].name.equals("online")
//                        &&variant.productFlavors[0].applicationId.contains("fsmajia")) {
//                    appName = "消费家居crm体验版"
//                    appIcon = "@drawable/iconmajia"
//                } else if(variant.productFlavors[0] != null && variant.productFlavors[0].name.equals("onlinecrm")) {
//                    appName = "纷享销客CRM"
//                }
//
//                def updatedContent = new File(manifestFile).getText('UTF-8').replaceAll("#replace_name#",
//                        appName)
//                updatedContent = updatedContent.replaceAll("#replace_icon#",
//                        appIcon)
//                updatedContent = updatedContent.replaceAll("#replace_amapkey#",
//                        amapkey)
//                updatedContent = updatedContent.replaceAll("#replace_baidumapkey#",
//                        baidumapkey)
//                updatedContent = updatedContent.replaceAll("#replace_huawei_push_appid#",
//                        huaweiPushAppId)
//                updatedContent = updatedContent.replaceAll("#replace_authority#",
//                        auth)
//                new File(manifestFile).write(updatedContent, 'UTF-8') //将此次flavor的AndroidManifest.xml文件指定为我们修改过的这个文件
////                variant.outputs[0].processResources.manifestFile = file("${buildDir}/intermediates/manifests/full/${variant.dirName}/AndroidManifest.xml")
//            }
//        }

    }
}

def initTask(){

    project.afterEvaluate {
        project.extensions.android.applicationVariants.each { variant ->
            def variantName = variant.name.capitalize()
            def flavor = variant.flavorName
            def buildType = variant.buildType.name
            GenerateFsDexTask generateDexTask = project.tasks.create("generateFsDexFor${variantName}",
                    GenerateFsDexTask)
            generateDexTask.flavor = flavor
            generateDexTask.buildType = buildType
            generateDexTask.variantName=variantName
            Task mergeDexTask = getMergeDexTask(variantName)
            Task mergeProjectDex = getMergeProjectDexTask(variantName)
            Task dexBuilderTask = getDexBuilderTask(variantName)
            Task minifyR8Task = getMinifyOnlineDebugWithR8Task(variantName)

            Task transformClassesWithDexTask = getTransformClassesWithDexForTask(variantName)
            Task transformDexArchiveWithDexMergerForTask = getTransformDexArchiveWithDexMergerForTask(variantName)
            Task packageTask = getPackageTask(variantName)
            Task fastdexPatchTask = getFastdexPatchTask(variantName)

            if(mergeDexTask!=null){
                generateDexTask.mustRunAfter mergeDexTask
            }

            if(mergeProjectDex!=null){
                generateDexTask.mustRunAfter mergeProjectDex
            }

            if(dexBuilderTask!=null){
                generateDexTask.mustRunAfter dexBuilderTask
            }

            if(transformClassesWithDexTask!=null){
                generateDexTask.mustRunAfter transformClassesWithDexTask
            }
            if(transformDexArchiveWithDexMergerForTask!=null){
                generateDexTask.mustRunAfter transformDexArchiveWithDexMergerForTask
            }
            if(fastdexPatchTask!=null){
                fastdexPatchTask.mustRunAfter generateDexTask
            }

            if(minifyR8Task != null){
                generateDexTask.mustRunAfter minifyR8Task
            }

            packageTask.dependsOn generateDexTask
        }
    }
}
Task getDexBuilderTask(String variantName) {
    String taskName = "dexBuilder${variantName}"
    try {
        return  project.tasks.getByName(taskName)
    } catch (Throwable e) {
        return null
    }
}


Task getMinifyOnlineDebugWithR8Task(String variantName) {
    String taskName = "minify${variantName}WithR8"
    try {
        return  project.tasks.getByName(taskName)
    } catch (Throwable e) {
        return null
    }
}


Task getMergeProjectDexTask(String variantName) {
    String taskName = "mergeProjectDex${variantName}"
    try {
        return  project.tasks.getByName(taskName)
    } catch (Throwable e) {
        return null
    }
}
Task getMergeDexTask(String variantName) {
    String taskName = "mergeDex${variantName}"
    try {
        return  project.tasks.getByName(taskName)
    } catch (Throwable e) {
        return null
    }
}
Task getTransformDexArchiveWithDexMergerForTask(String variantName) {
    String taskName = "transformDexArchiveWithDexMergerFor${variantName}"
    try {
        return  project.tasks.getByName(taskName)
    } catch (Throwable e) {
        return null
    }
}
Task getTransformClassesWithDexForTask(String variantName) {
    String taskName = "transformClassesWithDexFor${variantName}"
    try {
        return  project.tasks.getByName(taskName)
    } catch (Throwable e) {
        return null
    }
}


Task getFastdexPatchTask(String variantName) {
    String taskName = "fastdexPatchFor${variantName}"
    try {
        return  project.tasks.getByName(taskName)
    } catch (Throwable e) {
        return null
    }
}

Task getPackageTask(String variantName) {
    String taskName = "package${variantName}"
    try {
        return  project.tasks.getByName(taskName)
    } catch (Throwable e) {
        return null
    }
}

// 将internalLibs 下的so库，拷贝到jniLibs目录
def copyFileToarm(String type) {
    def formDir = project.getProjectDir().getAbsolutePath() + "/internalLibs/armeabi/";
    def toDir = project.getProjectDir().getAbsolutePath() + "/build/intermediates/merged_native_libs/internaltest" + type +"/out/lib/armeabi/";

    String[] children = new File(formDir).list()
    for (int i=0; i<children.length; i++) {
        copyFile(formDir + children[i], toDir + children[i]);
    }

    formDir = project.getProjectDir().getAbsolutePath() + "/internalLibs/arm64-v8a/";
    toDir = project.getProjectDir().getAbsolutePath() + "/build/intermediates/merged_native_libs/internaltest" + type +"/out/lib/arm64-v8a/";

    children = new File(formDir).list()
    for (int i=0; i<children.length; i++) {
        copyFile(formDir + children[i], toDir + children[i]);
    }

}



//将原host\host_main\assets\dexlibs中的产物删除，避免打包到apk中
def deleteDexNormalFile() {
    deleteDir(new File(project.projectDir.absolutePath + "/assets/dexlibs/"))
}

def deleteDir(File dir) {
    if (dir.isDirectory()) {
        String[] children = dir.list()
        for (int i=0; i<children.length; i++) {
            deleteDir(new File(dir, children[i]))
        }
    }
    dir.delete()
}

def checkDisableTask(Task task) {
    if (task.name.contains("lint") || task.name.contains("check") /*|| task.name.contains("UnitTest")*/ || task.name.contains
            ("AndroidTest")/*|| task.name.equals("mockableAndroidJar")*/
            || (task.name.contains("Aidl")&&(!task.project.name.equals("pluginapi")&&!task.project.name.equals("remote_service")))
    ) {
//        println("disable--"+task.project.name+":"+task.name)
        task.enabled = false
    }
    if(inJenkins.equals("yes")){
        if (task.name.contains("UnitTest") || task.name.contains
                ("AndroidTest")|| task.name.equals("mockableAndroidJar"))
        {
//            println("disable--"+task.project.name+":"+task.name)
            task.enabled = false
        }
    }
}
@Field pluginjsonStr="[{\"enable\":true,\"group\":\"fxiaoke\",\"md5\":\"1116a8848d683290278bfabdbcfc10a4\",\"name\":\"fxiaoke.apk\",\"path\":\"plugins/fxiaoke.apk\",\"uid\":\"com.facishare.fs\"}," +
        "{\"enable\":true,\"group\":\"pay\",\"md5\":\"68e83a2bcc516085ccf87563264d2baa\",\"name\":\"pay.apk\",\"path\":\"plugins/pay.apk\",\"uid\":\"com.fxiaoke.plugin.pay\"}," +
        "{\"enable\":true,\"group\":\"avcall\",\"md5\":\"f3d991d68d35bec56063c6e7eb320e1d\",\"name\":\"avcall.apk\",\"path\":\"plugins/avcall.apk\",\"uid\":\"com.fxiaoke.plugin.avcall\"}," +
        "{\"enable\":true,\"group\":\"crm\",\"md5\":\"6ceaddff0b72136db5d962ce3ad388b2\",\"name\":\"crm.apk\",\"path\":\"plugins/crm.apk\",\"uid\":\"com.fxiaoke.plugin.crm\"}," +
        "{\"enable\":true,\"group\":\"bi\",\"md5\":\"99d4fb3db1563c87da2cdfc0158b37c3\",\"name\":\"bi.apk\",\"path\":\"plugins/bi.apk\",\"uid\":\"com.fxiaoke.plugin.bi\"}," +
        "{\"enable\":true,\"group\":\"fsmail\",\"md5\":\"24772ee0dc32b48227b795d43586bd81\",\"name\":\"fsmail.apk\",\"path\":\"plugins/fsmail.apk\",\"uid\":\"com.fxiaoke.plugin.fsmail\"}," +
        "{\"enable\":true,\"group\":\"trainhelper\",\"md5\":\"d6361f24abe4b1556ff74aea4000efba\",\"name\":\"trainhelper.apk\",\"path\":\"plugins/trainhelper.apk\",\"uid\":\"com.fxiaoke.plugin.trainhelper\"}," +
        "{\"enable\":true,\"group\":\"hideplug\",\"md5\":\"2cbf065aca9c333c9c7f2eede9433f5c\",\"name\":\"hideplug.apk\",\"path\":\"plugins/hideplug.apk\",\"uid\":\"com.fxiaoke.plugin.hideplug\"}," +
        "{\"enable\":true,\"group\":\"commonfunc\",\"md5\":\"e84482af5d17e3641a3380694e7c2a12\",\"name\":\"commonfunc.apk\",\"path\":\"plugins/commonfunc.apk\",\"uid\":\"com.fxiaoke.plugin.commonfunc\"}," +
        "{\"enable\":true,\"group\":\"shortvideo\",\"md5\":\"3fb2a5b83840c570f9c55a79fd985ed7\",\"name\":\"shortvideo.apk\",\"path\":\"plugins/shortvideo.apk\",\"uid\":\"com.fxiaoke.plugin.shortvideo\"}]"
def calcMd5ForPlug(pluginApk,plugname){
    File temp=new File(rootProject.projectDir.absolutePath + "/plugins.json")
    if (!temp.exists()){
        println "create pluginjson file"
        temp.append(pluginjsonStr)
    }
    File file = new File(rootProject.projectDir.absolutePath + "/plugins.json")
    def slurper = new JsonSlurper()
    def plugconfig = slurper.parseText(file.text)
//	println plugconfig
    def tname=plugname+".apk"
    plugconfig.each{
        if(it.name==tname){
            def plugfile=new File(pluginApk)
            def inputS=plugfile.newInputStream()
            def md5= FileUtil.getFileMD5(inputS);
//			println it.name+" md5:"+md5
            it.md5=md5
            it.mainVersion=getMainVersionCode()
            it.sequenceId= gradle.pluginsSeqidMap.get(plugname)
            inputS.close()
        }
    }
    def json = JsonOutput.toJson(plugconfig);
//	println json
    file.write(json);
}


def copyReleasePluginManifest(String pluginName){
    copyFile(project.projectDir.absolutePath + "/build/intermediates/manifests/full/release/AndroidManifest.xml",
            rootProject.projectDir.absolutePath + "/host/host_main/assets/plugins/" + pluginName + "_AndroidManifest.xml")
}

def cleanManifest(String xmlPath){
    String orgxml = FileUtil.getFileStr(new File(xmlPath));
    String finalxml = orgxml.replaceAll("(?s)<!--.*?-->", "");//去除注释
    finalxml = finalxml.replaceAll("(?m)^\\s*\$(\\n|\\r\\n)", "");//去除空行
    try {
        FileUtil.writeToFile(finalxml.getBytes("utf-8"), new File(xmlPath));
    } catch (IOException e) {
        e.printStackTrace();
    }
}

def disableLintTask() {
    tasks.whenTaskAdded { task ->

        if (task.name.equals("assembleRelease")) {

            String buildType="release"
            String dir="bundles"
            if (task.name.contains("Debug")) {
                buildType = "debug"
            } else if (task.name.contains("Release")) {
                buildType = "release"
            }


            task.doLast {
                if(needToDexJar_JIAGU(project.name)) {
                    println "needToDexJar_JIAGU  " + project.name
                    copyFileToDexJarDirJiagu(dir,buildType,project.name + ".jar")
                }else if(needToDexJar_Main(project.name)) {
                    println "needToDexJar_Main  " + project.name
                    copyFileToDexJarDirMain(dir,buildType,project.name + ".jar")
                } else if (needToModuleJar_temp(project.name)) {
                    println "temp jar"
                    copyFileToDexJarDir_temp(dir,buildType,project.name + ".jar")
                }
            }
        }
        if (isPluginsApk(project.name)) {
        }

        if (isDownloadPluginsApk(project.name)) {
            if (task.name.equals("assembleRelease")) {
                task.doLast {
                    copyFile(getProjectApk("release"), gradle.outDir+"/"+project.name + ".apk")
                }
            }
        }
        checkDisableTask(task)
    }
}

def printStr(String str) {
    println str;
}

def dealFilePath(String filePath) {
    def oldArr = filePath.split('\\\\')
    def newStrArr = []
    for (str1 in oldArr) {
        if (str1.contains(" ")) {
            newStrArr.add("\"" + str1 + "\"")
        } else {
            newStrArr.add(str1)
        }
    }
    return newStrArr.join("/")
}
/**当时编译环境是否为Jenkins*/
def isInJenkins(){
    return inJenkins.equals("yes")
}
/**当时编译环境是否为GitlabCi*/
def isInGitlabCi() {
    return gradle.g_isci
}

def isLocalCompile(project){
    def ob=gradle.libmodemap.get(project)
    def mode=ob==null?0:ob
    def islocal=false
    if (isInJenkins()){
        islocal=true
    }else if(isInGitlabCi()){
        islocal=true
    }else if(mode==1){
        islocal=true
    }
    return islocal
}
def dependenciesProject(project){//provided jar包，local with jar in moduleJarLibs_temp

    if(isLocalCompile(project)){

        File f=new File(rootProject.getRootDir().getAbsolutePath() + "/moduleJarLibs_temp/$project"+".jar")
        if (f.exists()){
            println("$project debug compile moduleJarLibs_temp")
            return files(rootProject.getRootDir().getAbsolutePath() + "/moduleJarLibs_temp/$project"+".jar")
        }else{
            if(new File(rootProject.getRootDir().getAbsolutePath() + "/libsDexMain/$project"+".jar").exists()){
                println("$project debug compile libsDexMain")
                return files(rootProject.getRootDir().getAbsolutePath() + "/libsDexMain/$project" + ".jar")
            }else{
                println("$project debug compile libsDexjiagu")
                return files(rootProject.getRootDir().getAbsolutePath() + "/libsDexJiagu/$project"+".jar")
            }
        }

    }else {
        def myver=gradle.versionmap.get(project)
        //println("$project $myver maven compile")
        return  "fs-android:$project:$myver@jar"
    }
}


def dependenciesJar(project){//provided jar包，local with jar in moduleJarLibs_temp

    def myver=gradle.versionmap.get(project)
    //println("$project $myver maven compile")
    return  "fs-android:$project:$myver@jar"
}


def dependenciesProjectWithAarMaven(project){//compile maven aar包
    def myver=gradle.versionmap.get(project)
    //println("$project $myver maven compile")
    return  "fs-android:$project:$myver@aar"

}
def compileAarProject(projectP){//compile aar包，local with source code
    if(!isLocalCompile(projectP)){
        def myver=gradle.versionmap.get(projectP)
        //println("$projectP $myver maven compile")
        return  "fs-android:$projectP:$myver@aar"
    }else {
        println("$projectP debug compile")
        return project(":$projectP")
    }
}


def compileAarPackage(projectP){//
    def myver=gradle.versionmap.get(projectP)
    //println("$projectP $myver maven compile")
    return  "fs-android:$projectP:$myver@aar"
}
def depAarWithClassifierInner(projectP,classifier){//
    def myver=gradle.versionmap.get(projectP)
    //println("$projectP $myver maven compile")
    return  "fs-android:$projectP:$myver:$classifier@aar"
}

def depProjectWithLocalSource(projectP){//compile jar包，local with source code
    if(!isLocalCompile(projectP)){
        def myver=gradle.versionmap.get(projectP)
        //println("$projectP $myver maven compile")
        return  "fs-android:$projectP:$myver@jar"
    }else {
        println("$projectP debug compile")
        return project(":$projectP")
    }
}

def getMainVersionCode() {
    def versionName = gradle.versionName;
    def verNums = versionName.tokenize('.');
    def versionCode = 0;
    if (verNums != null && verNums.size >= 3) {
        versionCode = (verNums[0] as Integer) * 100 + (verNums[1] as Integer) * 10 + (verNums[2] as Integer) ;
    }
    return versionCode;
}

def getPluginVersionCode(versionName, versionSeqid) {
    def verNums = versionName.tokenize('.');
    def versionCode = 0;
    if (verNums != null && verNums.size >= 3) {
        versionCode = (verNums[0] as Integer) * 100000 + (verNums[1] as Integer) * 10000 + (verNums[2] as Integer) * 1000 + versionSeqid;
    }
    return versionCode;
}

def getLibVersion(projectP) {
    return gradle.versionmap.get(projectP)
}

def depCrmComponentLibAar(pkg, module) {
    if(isInJenkins() || isInGitlabCi() || !isLocalCompile(module)){
        def myver=gradle.versionmap.get(module)
        return  "$pkg:$module:$myver"
    }else {
        println("$module debug compile")
        return files(rootProject.getRootDir().getAbsolutePath() + "/moduleJarLibs_temp/$module-release"+".aar")
    }
}

def depCrmComponentLibJar(pkg, module) {
    if(isInJenkins() || isInGitlabCi() || !isLocalCompile(module)){
        def myver=gradle.versionmap.get(module)
        return  "$pkg:$module:$myver"
    }else {
        println("$module debug compile")
        return files(rootProject.getRootDir().getAbsolutePath() + "/moduleJarLibs_temp/$module-release"+".jar")
    }
}

def getAdbPath() {
    if (org.gradle.internal.os.OperatingSystem.current().isWindows()) {
        return "$System.env.ANDROID_HOME/platform-tools/adb"
    } else {
        //mac linux下获取adb的路径
        return "which adb".execute().inputStream.readLines()[0]
    }
}

def lowerFirstCase(String str){

    char[] chars = str.toCharArray();

    //首字母小写方法，大写会变成小写，如果小写首字母会消失
    chars[0] +=32;
    return String.valueOf(chars);

}


def makeHotfixDex(){
    project.afterEvaluate {
        project.getGradle().getTaskGraph().addTaskExecutionGraphListener(new TaskExecutionGraphListener() {
            @Override
            void graphPopulated(TaskExecutionGraph taskGraph) {
                for (Task task : taskGraph.getAllTasks()) {
                    if(task.name.startsWith("fastdexPatchFor")){
                        def fastdexTask = task;
                        fastdexTask.doLast {
                            String flavor
                            String inDexPath
                            String outDexPath
                            if(fastdexTask.name=="fastdexPatchForOnlineDebug"){
                                flavor = "OnlineDebug"
                            }else if(fastdexTask.name=="fastdexPatchForInternaltestDebug"){
                                flavor = "InternaltestDebug"
                            }
                            if(fastdexTask.project.name=="host_main"){
                                outDexPath="/sdcard/testplugin/host_classes.dex"
                                inDexPath=project.projectDir.absolutePath +
                                        "/build/fastdex/"+flavor+"/dex/merged-patch/classes.dex"
                            }else  if(fastdexTask.project.name=="fxiaoke"){
                                outDexPath="/sdcard/testplugin/fxiaoke_classes.dex"
                                inDexPath=project.projectDir.absolutePath +
                                        "/build/fastdex/Debug/dex/merged-patch/classes.dex"
                            }else  if(fastdexTask.project.name=="crm"){
                                outDexPath="/sdcard/testplugin/crm_classes.dex"
                                inDexPath=project.projectDir.absolutePath +
                                        "/build/fastdex/Debug/dex/merged-patch/classes.dex"
                            }

                            if(!new File(inDexPath).exists()){
                                inDexPath = inDexPath.replace("merged-patch","patch")
                            }

                            def tasks = project.getTasksByName("commandLineTadk", false)
                            if (tasks!=null&&tasks.size() > 0&&new File(inDexPath).exists()) {
                                def pushTask = tasks[0]
                                pushTask.commandLine("$getAdbPath", "push", inDexPath,outDexPath)
                                pushTask.exec()
                            }


                        }
                    }else if(task.name.startsWith("package")||task.name.startsWith("assemble")){
                        if(task.project.name=="host_main"||task.project.name=="fxiaoke"||task.project.name=="crm"){
                            task.setEnabled(false);
                        }
                    }

                }
            }
        });
    }
}


ext {
    createDir = this.&createDir
    getVersionNameAdvanced = this.&getVersionNameAdvanced
    copyFileToJarDir = this.&copyFileToJarDir
    copyFileToDexJarDirJiagu = this.&copyFileToDexJarDirJiagu
    copyFileToDexJarDirMain = this.&copyFileToDexJarDirMain
    copyFile = this.&copyFile
    disableLintTask = this.&disableLintTask
    depLibProjectWithAarMaven = this.&dependenciesProjectWithAarMaven//纯maven aar依赖
    depLibProjectWithMavenOrJar = this.&dependenciesProject//apk需要对lib进行jar包依赖时使用
    depLibProjectWithJar = this.&dependenciesJar
    depAarProjectWithMavenOrSource = this.&compileAarProject//lib工程之间依赖必须使用它aar
    depLibProjectWithMavenOrSource = this.&depProjectWithLocalSource//lib工程之间依赖必须使用它jar
    depAarProjectWithAar = this.&compileAarPackage
    depAarWithClassifier = this.&depAarWithClassifierInner
    calcMd5ForPlug = this.&calcMd5ForPlug
    getProjectApk = this.&getProjectApk
    getPluginSdcardPath = this.&getPluginSdcardPath
    getSDCardPlugConfigPath = this.&getSDCardPlugConfigPath
    getSDCardProjectApk = this.&getSDCardProjectApk
    getSDCardProjectApkSimpleName = this.&getSDCardProjectApkSimpleName
    getProjectManifestPath = this.&getProjectManifestPath
    getSDCardProjectManifestPath = this.&getSDCardProjectManifestPath
    copyReleasePluginManifest = this.&copyReleasePluginManifest
    dealHostMainApkFinished = this.&dealHostMainApkFinished
    getSDCardProjectDexPath = this.&getSDCardProjectDexPath
    getSDCardHostDexPath = this.&getSDCardHostDexPath
    printStr = this.&printStr
    checkDisableTask = this.&chekDisableTask
    hostMainHead = this.&hostMainHead
    dealFilePath = this.&dealFilePath
    copyMappingTxt = this.&copyMappingTxt
    getMainVersionCode = this.&getMainVersionCode
    getPluginVersionCode = this.&getPluginVersionCode
    getAdbPath = this.&getAdbPath
    getLibVersionName = this.&getLibVersion
    makeHotfixDex = this.&makeHotfixDex
    lowerFirstCase = this.&lowerFirstCase
    initTask = this.&initTask
    replaceRequstUrl = this.&replaceRequstUrl
    deleteDir = this.&deleteDir
    depCrmComponentLibAar = this.&depCrmComponentLibAar//crm独立组件aar依赖，方便调试用
    depCrmComponentLibJar = this.&depCrmComponentLibJar//crm独立组件jar依赖，方便调试用
    isInJenkins = this.&isInJenkins//是否jenkins
    isInGitlabCi = this.&isInGitlabCi//是否gitlab
}
