<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2023 Facishare Technology Co., Ltd. All Rights Reserved.
  -->

<!-- 矩形圆角+圆出一个圆弧 -->
<shape xmlns:android="http://schemas.android.com/apk/res/android"
        android:shape="rectangle"
        android:useLevel="true">

    <size
            android:width="32dp"
            android:height="32dp" />
    <solid android:color="#ffF2F3F5" />
    <stroke
            android:width="4px"
            android:color="#C1C5CE" />
    <corners android:radius="32dp" />

</shape>