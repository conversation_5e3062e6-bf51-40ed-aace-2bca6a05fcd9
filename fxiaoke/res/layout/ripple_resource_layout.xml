<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical" >
    
    
     <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/centertips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/radar_ray_3"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="20dp"
        android:gravity="center"
        i18n:fstext="xt.session_layout2.text.press_talk"
        android:textColor="#a0a0a0"
        android:textSize="13sp" />
    
    <ImageView
		android:id="@+id/radar_ray_1"
		android:layout_width="70.0dip"
		android:layout_height="70.0dip"
		android:background="@drawable/ripple_effect_circle"
		android:clickable="false"
		android:layout_centerInParent="true" />
		<ImageView
		android:id="@+id/radar_ray_2"
		android:layout_width="70.0dip"
		android:layout_height="70.0dip"
		android:background="@drawable/ripple_effect_circle"
		android:clickable="false"
		android:layout_centerInParent="true" />
		<ImageView
		android:id="@+id/radar_ray_3"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:background="@drawable/bc_feed_send_voice"
		android:clickable="true"
		android:layout_centerInParent="true" />

</RelativeLayout>
