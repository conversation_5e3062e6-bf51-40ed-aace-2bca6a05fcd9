<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="#ffffff"
    android:gravity="center_vertical"
    android:orientation="horizontal" >

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dip"
        android:layout_marginLeft="10dip"
        android:layout_marginRight="5dip"
        android:layout_marginTop="10dip"
            android:layout_marginEnd="5dip"
            android:layout_marginStart="10dip">

        <ImageView
            android:id="@+id/imageview_rangeinfo_head"
            android:layout_width="40dip"
            android:layout_height="40dip"
            android:background="@drawable/user_head"
            android:scaleType="centerCrop" />
        <!-- <ImageView -->
        <!-- android:id="@+id/iv_per_user_head_online" -->
        <!-- android:layout_width="12dip" -->
        <!-- android:layout_height="12dip" -->
        <!-- android:layout_marginLeft="28dip" -->
        <!-- android:layout_marginTop="28dip" -->
        <!-- android:background="@drawable/not_online" -->
        <!-- android:scaleType="centerCrop" -->
        <!-- android:visibility="gone"/> -->
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/coll_ll"
        android:layout_width="0dp"
        android:layout_height="fill_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="vertical" >

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/textView_rangeinfo_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            i18n:fstext="xt.employeeinfolist_item.text.workers"
            android:textColor="@color/l_text_color"
            android:textSize="@dimen/l_text_size" />

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/txtInfo"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="15dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/s_text_color"
            android:textSize="@dimen/s_text_size"
                android:layout_marginEnd="15dp" />
    </LinearLayout>

    <ImageView
        android:id="@+id/expandable_toggle_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
         />

    <ImageView
        android:id="@+id/expandable"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone" />

</LinearLayout>