<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/dr_bg_color"
    android:orientation="vertical" >

    <!-- 如下为审核记录对应的布局 -->

    <LinearLayout
        android:id="@+id/drCheckDataLogLayout"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical" >

        <LinearLayout
            android:id="@+id/receivedDRReqLayout"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/invitation_no_semicircle_selector"
            android:clickable="true"
            android:orientation="horizontal"
            android:paddingRight="20dip"
                android:paddingEnd="20dip">

            <LinearLayout
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:orientation="vertical" >

                <ImageView
                    android:layout_width="30dip"
                    android:layout_height="30dip"
                    android:layout_marginBottom="13dp"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="12dp"
                    android:background="@drawable/baoshu_inbox"
                        android:layout_marginStart="16dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:layout_marginTop="20dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="vertical" >

                <com.facishare.fs.sizectrlviews.SizeControlTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="2"
                    i18n:fstext="xt.dr_my_drt_bakdata_index_fragment.text.received_review"
                    android:textColor="#3d3d3d"
                    android:textSize="16dp" />
            </LinearLayout>

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/receivedDRReqRemind"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="7dip"
                android:background="@drawable/list_badge"
                android:gravity="center"
                android:paddingBottom="2px"
                android:paddingLeft="5dip"
                android:paddingRight="5dip"
                android:paddingTop="1px"
                android:text="8"
                android:textColor="@color/l_text_write_color"
                android:textSize="12dp"
                android:visibility="gone"
                    android:layout_marginEnd="7dip"
                    android:paddingStart="5dip"
                    android:paddingEnd="5dip" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:background="#ffffffff"
            android:orientation="vertical" >

            <View
                android:layout_width="fill_parent"
                android:layout_height="1px"
                android:layout_marginLeft="60dp"
                android:background="#cccccc"
                    android:layout_marginStart="60dp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/sendedDRReqLayout"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/invitation_no_semicircle_selector"
            android:clickable="true"
            android:orientation="horizontal"
            android:paddingRight="20dip"
                android:paddingEnd="20dip">

            <LinearLayout
                android:layout_width="60dp"
                android:layout_height="wrap_content" >

                <ImageView
                    android:layout_width="30dip"
                    android:layout_height="30dip"
                    android:layout_marginBottom="13dp"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="12dp"
                    android:background="@drawable/baoshu_send"
                        android:layout_marginStart="16dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:layout_marginTop="20dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="vertical" >

                <com.facishare.fs.sizectrlviews.SizeControlTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="2"
                    i18n:fstext="xt.dr_my_drt_bakdata_index_fragment.text.issued_audit"
                    android:textColor="#3d3d3d"
                    android:textSize="16dp" />
            </LinearLayout>

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/sendedDRReqRemind"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="7dip"
                android:background="@drawable/list_badge"
                android:gravity="center"
                android:paddingBottom="2px"
                android:paddingLeft="5dip"
                android:paddingRight="5dip"
                android:paddingTop="1px"
                android:text="8"
                android:textColor="@color/l_text_write_color"
                android:textSize="12dp"
                android:visibility="gone"
                    android:paddingStart="5dip"
                    android:layout_marginEnd="7dip"
                    android:paddingEnd="5dip" />
        </LinearLayout>

        <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:background="#cccccc" />
    </LinearLayout>

</LinearLayout>