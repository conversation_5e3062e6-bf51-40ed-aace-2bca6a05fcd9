<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="wrap_content">
    <LinearLayout
            android:id="@+id/searchbar_layout"
            android:layout_width="fill_parent"
            android:layout_height="50dp"
            android:padding="10dp"
            android:orientation="horizontal"
            android:background="@color/bg_default"
            >
        <LinearLayout

                android:orientation="horizontal"
                android:background="@drawable/bg_shape_searchbar_white_round"
                android:layout_width="0dp"
                android:layout_height="fill_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                >

            <com.facishare.fs.sizectrlviews.SizeControlEditText
                    android:id="@+id/searchbar_content"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:drawableLeft="@drawable/contact_search"
                    i18n:fshint="crm.layout.layout_select_product.1825"
                    android:textColorHint="@color/searchbar_hint_text_color"
                    android:singleLine="true"
                    android:textColor="@color/searchbar_text_color"
                    android:textSize="14sp"
                    android:gravity="center_vertical"
                    android:background="@null"
                    android:drawableStart="@drawable/contact_search" />
            <ImageView
                    android:id="@+id/searchbar_del"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/searchbar_btn_delete"
                    android:visibility="gone"
                    />
        </LinearLayout>
        <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/searchbar_cancel"
                android:layout_width="wrap_content"
                android:layout_height="fill_parent"
                android:textColor="#2294fc"
                android:textSize="16sp"
                i18n:fstext="commonfunc.dialog_fragment_fsmail_choose_attachment_menu.text.cancel"
                android:paddingLeft="@dimen/margin_12"
                android:paddingRight="@dimen/margin_2"
                android:gravity="center_vertical"
                android:paddingEnd="@dimen/margin_2"
                android:paddingStart="@dimen/margin_12" />

    </LinearLayout>
    <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:background="#d8d8d8"
            />
</LinearLayout>