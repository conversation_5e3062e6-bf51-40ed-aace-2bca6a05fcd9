<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/invitation_no_semicircle_selector"
    android:orientation="vertical"
    android:paddingLeft="@dimen/work_share_vote_margin_left"
        android:paddingStart="@dimen/work_share_vote_margin_left">

    <RelativeLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:minHeight="72dp" >

        <ImageView
            android:id="@+id/imageJT"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:background="@drawable/commonlist_arrow"
                android:layout_alignParentEnd="true" />

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_toLeftOf="@id/imageJT"
            android:orientation="vertical"
                android:layout_toStartOf="@id/imageJT">

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/txtItemName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                i18n:fstext="xt.vote_radio_layout.text.zbj"
                android:textColor="@color/vote_item_text_color"
                android:textSize="@dimen/x_text_size" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:layout_marginTop="4dp"
                android:layout_marginRight="10dp"
                android:gravity="center_vertical"
                    android:layout_marginEnd="10dp">

                <ProgressBar
                    android:id="@+id/progressBarVote"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="0dp"
                    android:layout_height="12dp"
                    android:layout_weight="1"
                    android:indeterminateOnly="false"
                    android:max="100"
                    android:progress="23"
                    android:progressDrawable="@drawable/vote_progressbar_bg" />

                <com.facishare.fs.sizectrlviews.SizeControlTextView
                    android:id="@+id/txtItemCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dp"
                    android:text="323(100%)"
                    android:textColor="@color/vote_item_text_color"
                    android:textSize="14sp"
                        android:layout_marginStart="8dp" />
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:layout_alignParentBottom="true"
        android:background="#ffeeeeee" />

</LinearLayout>