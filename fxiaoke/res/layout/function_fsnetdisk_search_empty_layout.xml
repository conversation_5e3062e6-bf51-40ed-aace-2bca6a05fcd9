<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:background="#EFF0F4"
    android:layout_width="match_parent"
    android:layout_height="match_parent" >

    <View
        android:id="@+id/center_view"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_centerInParent="true" >
    </View>

    <ImageView
        android:id="@+id/no_content_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/no_content_text"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="10dp"
        android:src="@drawable/session_search_colleague_img" />

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/no_content_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/center_view"
        android:layout_centerHorizontal="true"
        i18n:fstext="xt.show_feed_task_employees_act.text.search_member_and_group"
        android:textColor="#888888"
        android:textSize="18sp" />

</RelativeLayout>