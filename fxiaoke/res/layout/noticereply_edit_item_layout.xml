<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="75dp"
    android:layout_margin="5dp"
    android:background="@drawable/send_area_selector"
    android:clickable="true"
    android:gravity="center_horizontal" >

    <ImageView
        android:id="@+id/iv_pic"
        android:layout_width="75dp"
        android:layout_height="75dp"
        android:layout_alignParentLeft="true"
        android:layout_margin="1dp"
        android:src="@drawable/edit_recording_area"
            android:layout_alignParentStart="true" />

    <ImageView
        android:id="@+id/iv_record"
        android:layout_width="75dp"
        android:layout_height="75dp"
        android:layout_alignParentLeft="true"
        android:layout_margin="1dp"
        android:src="@drawable/play_voice3"
        android:visibility="gone"
            android:layout_alignParentStart="true" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toRightOf="@+id/iv_pic"
        android:orientation="vertical"
            android:layout_toEndOf="@+id/iv_pic">

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            android:layout_marginRight="25dp"
            android:includeFontPadding="false"
            android:singleLine="true"
            android:ellipsize="middle"
            i18n:fstext="xt.notice_reply_edit.des.record"
            android:textColor="@color/black"
            android:textSize="16.0sp"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="25dp" />

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            i18n:fstext="xt.noticereply_edit_item_layout.text.click_play"
            android:textSize="16.0sp"
            android:textColor="#464646"
            android:paddingRight="5dp"
                android:layout_marginStart="4dp"
                android:paddingEnd="5dp" />
    </LinearLayout>

    <ImageButton
        android:id="@+id/ib_delete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:background="@null"
        android:onClick="doClick"
        android:src="@drawable/edit_delete"
            android:layout_alignParentEnd="true" />

</RelativeLayout>