<?xml version="1.0" encoding="utf-8"?>
<TabHost xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:id="@android:id/tabhost"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent">

    <RelativeLayout
        android:id="@+id/rlt_main_tab_menu_main"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent">

        <LinearLayout
            android:id="@+id/llt_main_tab_main"
            android:layout_width="fill_parent"
            android:layout_height="fill_parent"
            android:orientation="vertical">

            <FrameLayout
                android:id="@android:id/tabcontent"
                android:layout_width="fill_parent"
                android:layout_height="0dip"
                android:layout_weight="1"></FrameLayout>

            <TabWidget
                android:id="@android:id/tabs"
                android:layout_width="fill_parent"
                android:layout_height="49dip"
                android:visibility="gone"></TabWidget>

            <!-- <com.facishare.fs.views.slidemenu.SlidingMenu -->
            <!-- xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto" -->
            <!-- android:id="@+id/slidingmenumain" -->
            <!-- android:layout_width="fill_parent" -->
            <!-- android:layout_height="50dp" /> -->

            <LinearLayout
                android:id="@+id/tabLayout"
                android:layout_width="fill_parent"
                android:layout_height="56dp"
                android:orientation="vertical">

                <View
                    android:layout_width="fill_parent"
                    android:layout_height="1px"
                    android:background="@color/tabbar_bar_split_line_color" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/btnSwitchExperienceRole"
            android:layout_width="120dp"
            android:layout_height="36dp"
            android:layout_alignParentStart="true"
            android:layout_alignParentLeft="true"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="12dp"
            android:layout_marginLeft="12dp"
            android:layout_marginBottom="73dp"
            android:background="@drawable/btn_orange_bg"
            android:clickable="true"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="21dp"
                android:layout_height="20dp"
                android:background="@drawable/switch_role_icon" />

            <com.facishare.fs.i18n.I18NTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:layout_marginLeft="2dp"
                android:background="@null"
                android:drawablePadding="2dp"
                android:gravity="center"
                android:paddingBottom="1dp"
                android:shadowColor="#c0ff8533"
                android:shadowRadius="4"
                android:textColor="#ffffff"
                android:textSize="17sp"
                i18n:fstext="xt.main_tab_dynamic_bottom.text.change_role" />
        </LinearLayout>

        <!--<com.facishare.fs.sizectrlviews.SizeControlButton-->
        <!--android:id="@+id/btnMainLeft"-->
        <!--android:layout_width="85dp"-->
        <!--android:layout_height="30dp"-->
        <!--android:layout_alignParentBottom="true"-->
        <!--android:layout_alignParentRight="true"-->
        <!--android:layout_marginBottom="65dp"-->
        <!--android:layout_marginRight="15dp"-->
        <!--android:background="@drawable/guidance_quit"-->
        <!--android:visibility="gone"/>-->

        <!--<com.facishare.fs.sizectrlviews.SizeControlButton-->
        <!--android:id="@+id/btnMainQuit"-->
        <!--android:layout_width="97dp"-->
        <!--android:layout_height="36dp"-->
        <!--android:layout_alignParentBottom="true"-->
        <!--android:layout_alignParentRight="true"-->
        <!--android:layout_marginBottom="68dp"-->
        <!--android:layout_marginRight="12dp"-->
        <!--android:shadowColor="#c0ff8533"-->
        <!--android:shadowRadius="4"-->
        <!--android:textSize="17dp"-->
        <!--android:textColor="#ffffff"-->
        <!--android:gravity="center"-->
        <!--i18n:fstext="xt.main_tab_dynamic_bottom.tetx.quit_experience"-->
        <!--android:paddingBottom="1dp"-->
        <!--android:background="@drawable/btn_red_bg"-->
        <!--android:visibility="gone"-->
        <!--/>-->

        <!--<com.facishare.fs.sizectrlviews.SizeControlButton-->
        <!--android:id="@+id/btnMainRegister"-->
        <!--android:layout_width="97dp"-->
        <!--android:layout_height="36dp"-->
        <!--android:layout_alignParentBottom="true"-->
        <!--android:layout_alignParentRight="true"-->
        <!--android:layout_marginBottom="68dp"-->
        <!--android:layout_marginRight="12dp"-->
        <!--android:shadowColor="#c0ff8533"-->
        <!--android:shadowRadius="4"-->
        <!--android:textSize="17dp"-->
        <!--android:textColor="#ffffff"-->
        <!--android:gravity="center"-->
        <!--i18n:fstext="xt.main_tab_dynamic_bottom.text.register"-->
        <!--android:paddingBottom="1dp"-->
        <!--android:background="@drawable/btn_green_bg"-->
        <!--android:visibility="gone"-->
        <!--/>-->

        <RelativeLayout
            android:id="@+id/draftLayout"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginEnd="15dp"
            android:layout_marginRight="15dp"
            android:layout_marginBottom="65dp"
            android:layout_toStartOf="@id/btnSwitchExperienceRole"
            android:layout_toLeftOf="@id/btnSwitchExperienceRole"
            android:background="@drawable/draft_pop_btn"
            android:clickable="true"
            android:visibility="gone">

            <!-- 红点顶部12dp  29dp 红点是52*52 -->

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/tv_draft_remind_count"
                android:layout_width="17dp"
                android:layout_height="17dp"
                android:layout_alignParentEnd="true"
                android:layout_alignParentRight="true"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="12dp"
                android:layout_marginRight="12dp"
                android:gravity="center"
                android:textColor="#ffffff"
                android:textSize="11sp" />

            <!-- <TextView -->
            <!--  -->
        </RelativeLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/theme_setting_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#99212b36"
        android:paddingLeft="20dp"
        android:paddingTop="51dp"
        android:paddingRight="20dp"
        android:paddingBottom="51dp"
        android:visibility="gone"
        >



        <LinearLayout
            android:id="@+id/cover_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:background="@drawable/bg_white_rounded_32dp"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_theme_header"
                android:layout_width="match_parent"
                android:layout_height="146dp"
                android:scaleType="fitXY"
                android:src="@drawable/theme_guide_header_cn">

            </ImageView>

            <TextView
                android:id="@+id/tv_theme_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="碧海金沙"
                android:textColor="#181c25"
                android:textFontWeight="600"
                android:textSize="16dp"></TextView>


            <LinearLayout
                android:id="@+id/ll_theme_demo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center">

                <ImageView
                    android:id="@+id/iv_theme_demo_1"
                    android:layout_width="142dp"
                    android:layout_height="378dp"
                    android:layout_gravity="center"
                    >

                </ImageView>

                <ImageView
                    android:id="@+id/iv_theme_demo_2"
                    android:layout_width="142dp"
                    android:layout_height="378dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="8dp"
                    >

                </ImageView>

            </LinearLayout>



            <LinearLayout
                android:id="@+id/buttons_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="18dp"
                android:orientation="vertical">

                <!-- 可水平滑动的按钮行 -->
                <HorizontalScrollView
                    android:id="@+id/hsv_theme_buttons"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fadingEdge="horizontal"
                    android:paddingLeft="20dp"
                    android:paddingRight="20dp"
                    android:scrollbars="none">

                    <LinearLayout
                        android:id="@+id/ll_theme_buttons_container"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        >
                        <!-- 按钮1 - 碧海金沙 -->
                        <Button
                            android:id="@+id/btn_theme_1"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"
                            android:layout_marginRight="12dp"
                            android:background="@drawable/button_gradient_1"
                            android:clickable="true"
                            android:textColor="#181c25"
                            android:textSize="12sp" />


                        <Button
                            android:id="@+id/btn_theme_2"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"
                            android:layout_marginRight="12dp"
                            android:background="@drawable/button_gradient_4"
                            android:clickable="true"
                            android:textColor="#181c25"
                            android:textSize="12sp" />


                        <Button
                            android:id="@+id/btn_theme_3"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"
                            android:layout_marginRight="12dp"
                            android:background="@drawable/button_gradient_4"
                            android:clickable="true"
                            android:textColor="#181c25"
                            android:textSize="12sp" />



                        <Button
                            android:id="@+id/btn_theme_4"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"
                            android:layout_marginRight="12dp"
                            android:background="@drawable/button_gradient_4"
                            android:clickable="true"
                            android:textColor="#181c25"
                            android:textSize="12sp" />



                        <Button
                            android:id="@+id/btn_theme_5"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"
                            android:layout_marginRight="12dp"
                            android:background="@drawable/button_gradient_4"
                            android:clickable="true"
                            android:textColor="#181c25"
                            android:textSize="12sp" />


                        <Button
                            android:id="@+id/btn_theme_6"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"
                            android:layout_marginRight="12dp"
                            android:background="@drawable/button_gradient_4"
                            android:clickable="true"
                            android:textColor="#181c25"
                            android:textSize="12sp" />

                        <Button
                            android:id="@+id/btn_theme_7"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"
                            android:layout_marginRight="12dp"
                            android:background="@drawable/button_gradient_4"
                            android:clickable="true"
                            android:textColor="#181c25"
                            android:textSize="12sp" />

                        <Button
                            android:id="@+id/btn_theme_8"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"
                            android:layout_marginRight="12dp"
                            android:background="@drawable/button_gradient_4"
                            android:clickable="true"
                            android:textColor="#181c25"
                            android:textSize="12sp" />

                        <Button
                             android:id="@+id/btn_theme_9"
                             android:layout_width="wrap_content"
                             android:layout_height="28dp"
                             android:layout_marginRight="12dp"
                             android:background="@drawable/button_gradient_4"
                             android:clickable="true"
                             android:textColor="#181c25"
                             android:textSize="12sp" />


                         <Button
                             android:id="@+id/btn_theme_10"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"
                            android:layout_marginRight="12dp"
                            android:background="@drawable/button_gradient_4"
                            android:clickable="true"
                            android:textColor="#181c25"
                            android:textSize="12sp" />

                        <Button
                            android:id="@+id/btn_theme_11"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"
                            android:layout_marginRight="12dp"
                            android:background="@drawable/button_gradient_4"
                            android:clickable="true"
                            android:textColor="#181c25"
                            android:textSize="12sp" />

                        <Button
                            android:id="@+id/btn_theme_12"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"
                            android:layout_marginRight="12dp"
                            android:background="@drawable/button_gradient_4"
                            android:clickable="true"
                            android:textColor="#181c25"
                            android:textSize="12sp" />


                        <!-- 更多按钮可以继续添加 -->
                    </LinearLayout>
                </HorizontalScrollView>
            </LinearLayout>

            <com.facishare.fs.sizectrlviews.SizeControlButton
                android:id="@+id/btn_save"
                android:layout_width="match_parent"
                android:layout_height="36dp"
                android:gravity="center"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                i18n:fstext="setting_set_as_theme"
                android:background="@drawable/btn_orange_bg_selector_corner_6dp"
                android:textColor="#FFFFFF"
                android:textSize="17sp" />

        </LinearLayout>

        <ImageView
            android:id="@+id/iv_theme_close"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:scaleType="fitXY"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:layout_marginRight="13dp"
            android:layout_marginTop="17dp"
            android:src="@drawable/theme_btn_close"/>

    </RelativeLayout>

</TabHost>