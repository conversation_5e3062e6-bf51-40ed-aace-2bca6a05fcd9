<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal" android:layout_width="match_parent"
    android:layout_height="40dp">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/change_pay"
            />

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:textSize="14sp"
            i18n:fstext="xt.reward_pay_item.text.wx_pay"
                android:layout_marginStart="5dp" />
    </LinearLayout>

    <ImageView
        android:id="@+id/direct_icon"
        android:layout_alignParentRight="true"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:layout_centerVertical="true"
        android:src="@drawable/item_action"
            android:layout_alignParentEnd="true" />

</RelativeLayout>