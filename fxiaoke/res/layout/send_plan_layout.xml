<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    xmlns:emojicon="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.facishare.fs.biz_feed.subbiz_send.views.PlanDateHeaderView
        android:id="@+id/date_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        emojicon:headerStyle="day"/>

    <View
        android:id="@+id/header_line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#ffeeeeee"/>
    <com.facishare.fs.i18n.I18NTextView
        android:id="@+id/txtSummaryHint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        i18n:fstext="xt.send_edit_layout.text.write_job_plan"
        android:textSize="17sp"
        android:paddingLeft="10.0dip"
        android:paddingRight="10.0dip"
        android:paddingTop="10.0dip"
        android:textColor="@color/gray_text"
            android:paddingStart="10.0dip"
            android:paddingEnd="10.0dip" />
    <com.rockerhieu.emojicon.EmojiconEditText
        android:id="@+id/et_summary"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:background="@null"
        android:capitalize="sentences"
        android:cursorVisible="true"
        android:gravity="top|start|center"
        i18n:fshint="xt.send_plan_layout.text.write_today_job_plan"
        android:imeOptions="actionDone"
        android:isScrollContainer="true"
        android:minHeight="25dp"
        android:minLines="1"
        android:paddingBottom="8.0dip"
        android:paddingLeft="10.0dip"
        android:paddingRight="10.0dip"
        android:paddingTop="10.0dip"
        android:singleLine="false"
        android:textColorHint="#cccccc"
        android:textCursorDrawable="@drawable/cursor_backgroud"
        android:textSize="17.0sp"
        android:visibility="gone"
        emojicon:isProcessCustomFace="true"
            android:paddingStart="10.0dip"
            android:paddingEnd="10.0dip" />

    <ImageView
        android:id="@+id/summaryLine"
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/gray"
        android:visibility="gone" />
    <com.facishare.fs.i18n.I18NTextView
        android:id="@+id/txtPlanHint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        i18n:fstext="xt.send_plan_layout.text.work_feel"
        android:textSize="17sp"
        android:paddingLeft="10.0dip"
        android:paddingRight="10.0dip"
        android:paddingTop="10.0dip"
        android:textColor="@color/gray_text"
            android:paddingEnd="10.0dip"
            android:paddingStart="10.0dip" />
    <com.rockerhieu.emojicon.EmojiconEditText
        android:id="@+id/et_plan"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:background="@null"
        android:capitalize="sentences"
        android:cursorVisible="true"
        android:gravity="top|start|center"
        i18n:fshint="xt.send_plan_layout.text.write_tomarrow_job_plan"
        android:imeOptions="actionDone"
        android:isScrollContainer="true"
        android:minHeight="25dp"
        android:minLines="1"
        android:paddingBottom="8.0dip"
        android:paddingLeft="10.0dip"
        android:paddingRight="10.0dip"
        android:paddingTop="10.0dip"
        android:singleLine="false"
        android:textColorHint="#cccccc"
        android:textCursorDrawable="@drawable/cursor_backgroud"
        android:textSize="17.0sp"
        android:visibility="gone"
        emojicon:isProcessCustomFace="true"
            android:paddingEnd="10.0dip"
            android:paddingStart="10.0dip" />

    <ImageView
        android:id="@+id/planLine"
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/gray"
        android:visibility="gone" />
</LinearLayout>