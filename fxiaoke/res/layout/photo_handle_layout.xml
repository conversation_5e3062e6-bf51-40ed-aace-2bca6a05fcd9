<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:id="@+id/previewLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000"
    android:orientation="vertical">

    <com.fs.commonviews.photoview.PhotoView
        android:id="@+id/iv_pic"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <com.facishare.fs.i18n.I18NTextView
            android:id="@+id/txtTip"
            android:layout_gravity="center_horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:layout_marginTop="23dp"
            i18n:fstext="wq.fscamera.l100.imageblur"
            android:textColor="#181C25"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#C1C5CE"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="61dp"
            android:orientation="horizontal">

            <com.facishare.fs.i18n.I18NTextView
                android:id="@+id/iv_commit"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:textColor="#181C25"
                android:textSize="16sp"
                android:visibility="visible"
                i18n:fstext="wq.outdoor.camera.photo.usethis"
                />

            <View
                android:layout_width="1px"
                android:layout_height="match_parent"
                android:background="#C1C5CE"/>

            <com.facishare.fs.i18n.I18NTextView
                android:id="@+id/iv_cancle"
                android:gravity="center"
                android:textColor="#FF8000"
                android:textSize="16sp"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                i18n:fstext="wq.fscamera.l100.retake"
                />

        </LinearLayout>



    </LinearLayout>


</LinearLayout>