<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/llyt_select_project"
        android:layout_width="match_parent"
        android:layout_height="@dimen/project_new_item_height"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_select_project_belong"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/margin_12"
            android:src="@drawable/icon_task_select"
                android:layout_marginStart="@dimen/margin_12" />

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/tv_project_name"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="42dp"
            android:gravity="center_vertical"
            i18n:fstext="xt.project_select_belong_item.text.project"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/text_size_16"
                android:layout_marginStart="42dp" />
    </RelativeLayout>

    <View
        android:id="@+id/bottom_line"
        style="@style/list_item_divider_line_bg"
        android:layout_marginLeft="@dimen/margin_12"
            android:layout_marginStart="@dimen/margin_12" />
</LinearLayout>