<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2021 Facishare Technology Co., Ltd. All Rights Reserved.
  -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:i18n="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="41dp"
        android:background="#FFFFFF"
        android:paddingLeft="12dp"
        android:paddingStart="12dp">

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="44.5dp"
            android:orientation="vertical">
        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/customized_group_item_height"
                android:background="#FFFFFF"
                android:orientation="horizontal">

            <TextView
                    android:id="@+id/tv"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/customized_group_item_height"
                    android:layout_centerVertical="true"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:textColor="#ff181c25"
                    android:textSize="16dp" />

            <LinearLayout
                    android:id="@+id/sortBtn"
                    android:layout_width="16dp"
                    android:layout_height="40dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="9dp"
                    android:gravity="center"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="9dp">

                <ImageView
                        android:layout_width="8dp"
                        android:layout_height="13dp"
                        android:src="@drawable/drag_icon" />
            </LinearLayout>
        </LinearLayout>
        <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/customized_group_divider_height"
                android:background="@color/primaryBackgroundColor"/>
    </LinearLayout>
</RelativeLayout>
