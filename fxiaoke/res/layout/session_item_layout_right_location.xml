<?xml version="1.0" encoding="utf-8"?>
<FrameLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:MasgImageView="http://schemas.android.com/apk/res-auto"
	    android:layout_width="wrap_content"
	    android:layout_height="wrap_content"
	    >
	    
	        <com.facishare.fs.biz_session_msg.views.NewMaskImageView
	            android:id="@+id/iv_LocationImg" 
	            android:layout_width="wrap_content"
	            android:layout_height="wrap_content"
	          	MasgImageView:oritention="right"
	          	android:scaleType="fitCenter"
				android:foreground="@drawable/chat_to_bg_shader_selector"
	          	/>
	        <!-- <TextView 
	            android:id="@+id/tv_LocationStr" 
	            android:layout_width="93dp"
	            android:layout_height="25dp"
	            android:layout_gravity="bottom"
	            android:layout_marginRight="8dp"
	            android:gravity="center_vertical"
	            android:singleLine="true"
	            android:ellipsize="end"
	            android:background="@drawable/speak_show_bg"
	            android:textColor="#ffffff"
	            /> -->
	    
</FrameLayout>