<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical" android:layout_width="match_parent"
    android:gravity="end"
    android:layout_height="wrap_content">

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        i18n:fstext="xt.app_center_edit_tip_layout.action.editor"
        android:textSize="14sp"
        android:gravity="center"
        android:layout_marginRight="5dp"
        android:textColor="@android:color/white"
        android:background="@drawable/app_center_edit_tip"
            android:layout_marginEnd="5dp" />

</LinearLayout>