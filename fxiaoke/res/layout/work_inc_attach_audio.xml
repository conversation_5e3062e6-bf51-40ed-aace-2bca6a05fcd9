<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/margin_default"
    android:orientation="horizontal">

    <LinearLayout
        android:id="@+id/record_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_selector_feed_attach_item_new"
        android:gravity="center_vertical"
        android:minHeight="@dimen/sessionmsg_minitem_hegiht"
        android:orientation="horizontal"
        android:paddingLeft="12dp"
        android:paddingRight="28dp"
            android:paddingStart="12dp"
            android:paddingEnd="28dp">

        <ImageView
            android:id="@+id/record_icon"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@drawable/feed_send_voice_three" />

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/record_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:text="4&apos;"
            android:textColor="@color/sessionmsg_duration"
            android:textSize="@dimen/s_text_size"
                android:layout_marginStart="8dp" />
    </LinearLayout>


</LinearLayout>