<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/feed_approve_item_height">

                <com.facishare.fs.sizectrlviews.SizeControlTextView
                    style="@style/feed_approve_general_item"
                    i18n:fstext="crm.layout.feed_send_general_bill_fragment.7848" />

                <com.facishare.fs.sizectrlviews.SizeControlEditText
                    android:id="@+id/et_discount_money_content"
                    style="@style/feed_approve_value_item_text_view"
                    android:inputType="numberDecimal" />

            </LinearLayout>

            <View
                style="@style/list_item_divider_line_bg"
                android:layout_marginLeft="@dimen/margin_16"
                    android:layout_marginStart="@dimen/margin_16" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/feed_approve_item_height">

                <com.facishare.fs.sizectrlviews.SizeControlTextView
                    style="@style/feed_approve_general_item"
                    i18n:fstext="crm.layout.feed_send_approve_discount_fragment.7878" />

                <com.facishare.fs.sizectrlviews.SizeControlEditText
                    android:id="@+id/et_discount_rate_content"
                    style="@style/feed_approve_value_item_text_view"
                    android:inputType="numberDecimal" />

            </LinearLayout>


            <View
                style="@style/list_item_divider_line_bg"
                android:layout_marginLeft="@dimen/margin_16"
                    android:layout_marginStart="@dimen/margin_16" />

            <LinearLayout
                android:id="@+id/llyt_discount_end_time"
                android:layout_width="match_parent"
                android:layout_height="@dimen/feed_approve_item_height"
                android:background="@drawable/feed_send_approve_background_selector">

                <com.facishare.fs.sizectrlviews.SizeControlTextView
                    style="@style/feed_approve_general_item"
                    i18n:fstext="xt.feed_send_approve_discount_fragment.text.validity_period" />

                <com.facishare.fs.sizectrlviews.SizeControlTextView
                    android:id="@+id/tv_discount_end_time_content"
                    style="@style/feed_approve_value_item_text_view"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    i18n:fshint="crm.layout.select_task_priority.7280" />

                <ImageView
                    android:id="@+id/iv_arrow_end_tme_approve"
                    android:layout_width="12dp"
                    android:layout_height="24dp"
                    android:layout_gravity="end|center_vertical"
                    android:layout_marginRight="@dimen/margin_12"
                    android:scaleType="center"
                    android:src="@drawable/feed_approve_arrow_app_icon"
                        android:layout_marginEnd="@dimen/margin_12" />
            </LinearLayout>

            <View
                style="@style/list_item_divider_line_bg"
                android:layout_marginLeft="@dimen/margin_16"
                    android:layout_marginStart="@dimen/margin_16" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="horizontal">

                <com.facishare.fs.sizectrlviews.SizeControlTextView
                    style="@style/feed_approve_general_item"
                    i18n:fstext="xt.feed_send_approve_activity_company_name" />

                <com.facishare.fs.sizectrlviews.SizeControlEditText
                    android:id="@+id/et_companey_name_content"
                    style="@style/feed_approve_value_item_text_view"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:paddingBottom="@dimen/margin_16"
                    android:singleLine="false" />

            </LinearLayout>

            <View
                style="@style/list_item_divider_line_bg"
                android:layout_height="@dimen/text_size_12" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="horizontal">

                <com.facishare.fs.sizectrlviews.SizeControlTextView
                    android:id="@+id/tv_myinfo"
                    style="@style/feed_approve_general_item"
                    i18n:fstext="crm.layout.feed_send_general_bill_fragment.7849" />
                <com.facishare.fs.sizectrlviews.SizeControlEditText
                    android:id="@+id/et_explain_approve"
                    style="@style/feed_approve_value_item_text_view"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:paddingBottom="@dimen/margin_16"
                    android:singleLine="false"
                    i18n:fshint="xt.feed_send_approve_discount_fragment.text.such_as_discount_use"/>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>