<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:emojicon="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:id="@+id/LinearLayout_v2_ok"
    android:layout_width="match_parent"
    android:layout_height="fill_parent"
    android:orientation="vertical"
    android:background="@color/white"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:layout_marginTop="@dimen/margin_10">
    <com.rockerhieu.emojicon.EmojiconEditText
        android:id="@+id/et_content"
        android:layout_width="fill_parent"
        android:layout_height="400dp"
        android:background="@null"
        android:capitalize="sentences"
        android:cursorVisible="true"
        android:gravity="top|start|center"
        android:hint=""
        android:imeOptions="actionDone"
        android:isScrollContainer="true"

        android:minLines="2"
        android:paddingBottom="@dimen/toolbar_height"
        android:paddingLeft="10.0dip"
        android:paddingRight="10.0dip"
        android:paddingTop="10.0dip"
        android:singleLine="false"
        android:textColorHint="#cccccc"
        android:textCursorDrawable="@drawable/cursor_backgroud"
        android:textSize="17.0sp"
        emojicon:isProcessCustomFace="true"
            android:paddingEnd="10.0dip"
            android:paddingStart="10.0dip" />
</LinearLayout>