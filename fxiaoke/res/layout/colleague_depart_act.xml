<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
        android:id="@+id/title"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/title_height" />

<!--    <FrameLayout-->
<!--        android:id="@+id/container_fragment"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="0dp"-->
<!--        android:layout_weight="1"-->
<!--        >-->
<!--    </FrameLayout>-->
    <com.fxiaoke.cmviews.viewpager.ViewPagerCtrl
            android:id="@+id/pager"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="#ff00ff"
            />
    <FrameLayout
        android:id="@+id/bottom_fragment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
    </FrameLayout>

</LinearLayout>