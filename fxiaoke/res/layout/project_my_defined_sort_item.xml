<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:background="@color/white"
              android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/project_new_item_height"
        android:orientation="horizontal"
        android:background="@color/white"
        >


        <LinearLayout
            android:id="@+id/llyt_drag_item"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@color/white">

            <ImageView
                android:id="@+id/iv_drag_item"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="@dimen/margin_12"
                android:layout_marginRight="@dimen/margin_16"
                android:src="@drawable/project_task_sort"
                    android:layout_marginStart="@dimen/margin_12"
                    android:layout_marginEnd="@dimen/margin_16" />
        </LinearLayout>

        <com.facishare.fs.sizectrlviews.SizeControlEditText
            android:id="@+id/et_sort_content"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/white"
            android:gravity="center_vertical"
            i18n:fshint="xt.project_my_defined_sort_item.text.input_classify_name"
            android:textColorHint="@color/color_cccccc"
            android:textSize="@dimen/text_size_14"
            android:singleLine="true"
            />

        <LinearLayout
            android:id="@+id/llyt_delete_sort"
            android:layout_width="wrap_content"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/ib_delete_sort"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:background="@color/white"
                android:src="@drawable/project_new_delete"
                />
        </LinearLayout>

    </LinearLayout>

    <View
        android:id="@+id/bottom_line"
        style="@style/list_item_divider_line_bg"
        android:layout_marginLeft="@dimen/margin_12"
            android:layout_marginStart="@dimen/margin_12" />
</LinearLayout>