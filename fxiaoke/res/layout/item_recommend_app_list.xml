<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/list_item_select"
    android:paddingLeft="12dp"
    android:paddingRight="12dp"
        android:paddingStart="12dp"
        android:paddingEnd="12dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="12dp"
            android:layout_weight="1"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:gravity="center_vertical"
            android:orientation="vertical"
                android:layout_marginStart="12dp">
            <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/tv_app_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textColor="#333333"
                android:ellipsize="end"
                android:singleLine="true"/>
            <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/tv_desc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:textSize="12sp"
                android:textColor="#666666"
                android:ellipsize="end"
                android:singleLine="true"/>
        </LinearLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#E8E8E8"/>

</LinearLayout>