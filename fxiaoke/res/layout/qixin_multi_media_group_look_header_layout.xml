<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/qixin_mulit_img_group_look_index_bg"
    android:orientation="vertical" >

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/multi_img_group_letter_index"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:paddingBottom="4dp"
        android:paddingLeft="12dp"
        android:paddingTop="4dp"
        android:textColor="#ffffff"
        android:textSize="14sp"
            android:paddingStart="12dp" />

</LinearLayout>