<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/oderlist_title"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="30dp"
    android:layout_marginRight="29dp"
    android:layout_marginTop="4dp"
    android:layout_marginBottom="4dp"
    android:paddingLeft="20dp"
    android:orientation="vertical"
    android:background="@color/white"
        android:layout_marginStart="30dp"
        android:paddingStart="20dp"
        android:layout_marginEnd="29dp">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="8dp">

        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@color/white">
                <TextView
                    android:id="@+id/product_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:layout_marginRight="2dp"
                    android:textSize="14sp"
                    android:textColor="#181C25"
                    android:visibility="gone"
                    android:text=""
                        android:layout_marginEnd="2dp" />
                <TextView
                    android:id="@+id/product_jiage"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.5"
                    android:textSize="14sp"
                    android:textColor="#181C25"
                    android:gravity="end"
                    android:visibility="gone"
                    android:text=""
                    />
                <TextView
                    android:id="@+id/product_count"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginRight="16dp"
                    android:textSize="14sp"
                    android:textColor="#181C25"
                    android:gravity="end"
                    android:visibility="gone"
                    android:text=""
                        android:layout_marginEnd="16dp" />
        </LinearLayout>

</LinearLayout>

