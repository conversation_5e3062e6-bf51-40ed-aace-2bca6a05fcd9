<?xml version="1.0" encoding="utf-8"?>
<LinearLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/scheduleLayout"
    android:layout_width="fill_parent"
    android:layout_height="30dp"
    android:gravity="center_vertical"
    android:orientation="horizontal" >

    <ImageView
        android:id="@+id/imgSchedule"
        android:layout_width="30dp"
        android:layout_height="30dp" />

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:layout_marginLeft="5dp"
        android:id="@+id/txtSchedulInfo"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/m_text_color"
        android:textSize="@dimen/m_text_size"
            android:layout_marginStart="5dp" />

</LinearLayout>