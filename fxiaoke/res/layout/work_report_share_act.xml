<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
        android:id="@+id/title"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/title_height" />

    <com.fxiaoke.cmviews.FsWebview
        android:id="@+id/work_share_webview"
        android:layout_width="fill_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/line"
        android:layout_below="@id/title"
        android:background="#e6e6e6" />
		<com.facishare.fs.sizectrlviews.SizeControlTextView
		    android:id="@+id/line"
            android:layout_width="fill_parent"
            android:layout_height="2px"
            android:background="#dddddd"
            android:layout_above="@+id/linearLayout1"
            
            />    
    <LinearLayout
        android:id="@+id/linearLayout1"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true" 
        android:orientation="horizontal" >

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/button_select"
            android:layout_width="wrap_content"
            android:layout_height="fill_parent"
            android:layout_weight="1"
            android:gravity="center"
            i18n:fstext="xt.work_report_share_act.action.add_other_count" />
		<com.facishare.fs.sizectrlviews.SizeControlTextView
            android:layout_width="2px"
            android:layout_height="fill_parent"
            android:background="#dddddd"/>
        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/button_share"
            android:layout_width="wrap_content"
            android:layout_height="fill_parent"
            android:layout_weight="1"
            android:gravity="center"
            i18n:fstext="xt.work_report_share_act.text.share_friends" />
        
    </LinearLayout>
</RelativeLayout>