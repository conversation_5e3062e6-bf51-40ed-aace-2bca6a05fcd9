<?xml version="1.0" encoding="UTF-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@color/bg_default"
    android:orientation="vertical">

    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
        android:id="@+id/title"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/title_height" />
    <!-- 搜索框 -->
    <!--     <include -->
    <!--         android:id="@+id/searchLayout" -->
    <!--         layout="@layout/shortmessage_main_search_layout" /> -->

    <FrameLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent">

        <LinearLayout
            android:id="@+id/batchChildNoDataLayout"
            android:layout_width="fill_parent"
            android:layout_height="fill_parent"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:drawablePadding="10dip"
                android:drawableTop="@drawable/empty_data"
                android:gravity="center"
                i18n:fstext="crm.layout.crm_remind_act.1998"
                android:textColor="#B4B4B5"
                android:textSize="18sp" />
        </LinearLayout>

        <ListView
            android:id="@+id/batchChildListView"
            style="@style/default_listview_style"
            android:background="@color/bg_default"
            android:dividerHeight="0dp" />
    </FrameLayout>

</LinearLayout>