<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f2f2f4"
    android:orientation="vertical">

    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
        android:id="@+id/title"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/title_height" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.fxiaoke.cmviews.xlistview.XListView
            android:id="@+id/nonePullListview"
            style="@style/default_listview_feed_style"
            android:focusable="false" />
    </RelativeLayout>
</LinearLayout>