<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="12dp"
        i18n:fstext="xt.res.quick_entry_layout.1"
        android:textColor="#181c25"
        android:textSize="@dimen/text_size_12"
            android:layout_marginStart="12dp" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/quick_entry_pager"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/session_quick_entry_viewpager_height"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:overScrollMode="never"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp" />

    <com.facishare.fs.views.StateBar xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/quick_entry_page_statebar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingTop="10dp">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/welcome_point"
            android:visibility="visible"
            tools:ignore="ContentDescription" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:src="@drawable/welcome_point"
            android:visibility="visible"
            tools:ignore="ContentDescription"
                android:layout_marginStart="9dp" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:src="@drawable/welcome_point"
            android:visibility="visible"
            tools:ignore="ContentDescription"
                android:layout_marginStart="9dp" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:src="@drawable/welcome_point"
            android:visibility="visible"
            tools:ignore="ContentDescription"
                android:layout_marginStart="9dp" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:src="@drawable/welcome_point"
            android:visibility="visible"
            tools:ignore="ContentDescription"
                android:layout_marginStart="9dp" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:src="@drawable/welcome_point"
            android:visibility="visible"
            tools:ignore="ContentDescription"
                android:layout_marginStart="9dp" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:src="@drawable/welcome_point"
            android:visibility="visible"
            tools:ignore="ContentDescription"
                android:layout_marginStart="9dp" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:src="@drawable/welcome_point"
            android:visibility="visible"
            tools:ignore="ContentDescription"
                android:layout_marginStart="9dp" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:src="@drawable/welcome_point"
            android:visibility="visible"
            tools:ignore="ContentDescription"
                android:layout_marginStart="9dp" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:src="@drawable/welcome_point"
            android:visibility="visible"
            tools:ignore="ContentDescription"
                android:layout_marginStart="9dp" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:src="@drawable/welcome_point"
            android:visibility="visible"
            tools:ignore="ContentDescription"
                android:layout_marginStart="9dp" />
    </com.facishare.fs.views.StateBar>

</LinearLayout>