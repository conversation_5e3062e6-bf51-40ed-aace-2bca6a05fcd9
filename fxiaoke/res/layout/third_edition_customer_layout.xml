<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:background="#FFFFFFFF"
    android:gravity="center_vertical" >

    <ScrollView
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:fadingEdge="none"
        android:fadingEdgeLength="0.0dip"
        android:scrollbars="none" >

        <LinearLayout
            android:id="@+id/thirdCustomerLayout"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/invitation_no_semicircle_selector"
            android:clickable="false"
            android:minHeight="50dip"
            android:orientation="vertical"
             >

            <RelativeLayout
                android:id="@+id/mainItemLayout"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content" >

                <ImageView
                    android:id="@+id/thirdCustomerRightArrow"
                    android:layout_width="34dp"
                    android:layout_height="34dp"
                    android:layout_marginTop="8dip"
                    android:layout_marginBottom="8dip"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:background="@drawable/commonlist_arrow"
                    android:visibility="gone"
                        android:layout_alignParentEnd="true" />

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toLeftOf="@id/thirdCustomerRightArrow"
                    android:orientation="horizontal"
                    android:minHeight="50dip"
                    android:gravity="center_vertical"
                        android:layout_toStartOf="@id/thirdCustomerRightArrow">

                    <LinearLayout
                        android:id="@+id/thirdCustomerViewNameLayout"
                        android:layout_width="0dip"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dip"
                        android:layout_weight="1"
                        android:orientation="vertical"
                            android:layout_marginStart="16dip">

                        <com.facishare.fs.sizectrlviews.SizeControlTextView
                            android:id="@+id/thirdCustomerViewName"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="start|center"
                            android:maxWidth="150dp"
                            android:text=""
                            android:textColor="#ff636366"
                            android:textSize="14dp" />
                    </LinearLayout>

                    <com.facishare.fs.sizectrlviews.SizeControlEditText
                        android:id="@+id/thirdCustomerEditText"
                        android:layout_width="0dip"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:minWidth="200dip"
                        android:textCursorDrawable="@null"
                        android:background="@null"
                        android:paddingTop="24dip"
                        android:paddingBottom="24dip"
                        android:paddingRight="34dip"
                        android:gravity="center_vertical|end"
                        android:textColor="#ff46414f"
                        android:textColorHint="@color/dr_detail_numeric_hint_text_color"
                        android:textSize="16dp"
                            android:paddingEnd="34dip" />
                </LinearLayout>
            </RelativeLayout>

            <com.facishare.fs.sizectrlviews.SizeControlEditText
                android:id="@+id/thirdCustomerDescripEditText"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:minHeight="128dip"
                android:background="@null"
                android:textCursorDrawable="@null"
                android:gravity="start"
                i18n:fshint="xt.third_edition_customer_layout.text.much_hundred"
                android:inputType="textMultiLine"
                android:isScrollContainer="true"
                android:paddingLeft="16dp"
                android:paddingRight="34dp"
                android:layout_marginTop="-8dip"
                android:paddingBottom="14dip"
                android:singleLine="false"
                android:textColor="#ff46414f"
                android:textColorHint="#ffaeb2be"
                android:textSize="16dp"
                    android:paddingStart="16dp"
                    android:paddingEnd="34dp" />
        </LinearLayout>
    </ScrollView>

</LinearLayout>