<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

  <com.facishare.fs.sizectrlviews.SizeControlTextView
      android:id="@+id/boardGroupIndexTV"
      style="@style/sessionAttachHeaderStyle"
      android:layout_width="match_parent"
      android:layout_height="32dp"
      android:background="@color/contacts_indexbar_bg"
      android:gravity="center_vertical"
      android:paddingLeft="9dp"
      android:paddingRight="4dip"
      android:textColor="@color/contacts_indexbar_font"
      android:textSize="14sp"
          android:paddingEnd="4dip"
          android:paddingStart="9dp" />
  <View
      android:layout_width="match_parent"
      android:layout_height="1dp"
      android:background="#e6e7e8"
      android:layout_below="@+id/msgBoardInfoLayout"/>
</LinearLayout>