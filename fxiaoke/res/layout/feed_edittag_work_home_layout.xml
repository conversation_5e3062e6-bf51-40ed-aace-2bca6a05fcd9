<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
   	android:background="#ffffff"
    android:orientation="vertical"
    >
	<RelativeLayout
		android:layout_width="match_parent"
		android:layout_height="48dp"
			android:paddingLeft="12dp"
			android:paddingRight="12dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp">
		<ImageView
			android:id="@+id/close_tv"
			android:layout_width="27dp"
			android:layout_height="27dp"
			android:layout_centerVertical="true"
			android:layout_alignParentRight="true"
			android:layout_alignParentEnd="true"
			android:src="@drawable/btn_gray_close"
			android:background="@drawable/btn_gray_circle"
			android:scaleType="centerInside"
			android:gravity="center_vertical"/>
	</RelativeLayout>
	<RelativeLayout
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
			android:paddingLeft="12dp"
			android:paddingRight="12dp"
            android:paddingEnd="12dp"
            android:paddingStart="12dp">
		<com.facishare.fs.i18n.I18NTextView
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			i18n:fstext="xt.feed_edittag_work_home_layout.text.quick_navigation"
			android:layout_centerVertical="true"
			android:layout_alignParentLeft="true"
			android:textSize="15dp"
			android:textColor="#181C25"
			android:id="@+id/eidt_kj_tv"
                android:layout_alignParentStart="true" />
		<com.facishare.fs.i18n.I18NTextView
			android:id="@+id/edit_info_tv"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			i18n:fstext="xt.feed_edittag_work_home_layout.text.click_to_enter_the_channel"
			android:layout_alignBaseline="@+id/eidt_kj_tv"
			android:layout_alignBottom="@+id/eidt_kj_tv"
			android:layout_toRightOf="@+id/eidt_kj_tv"
			android:layout_toEndOf="@+id/eidt_kj_tv"
			android:layout_marginLeft="8dp"
			android:layout_marginStart="8dp"
			android:textSize="12dp"
			android:textColor="#91959E"/>
		<com.facishare.fs.i18n.I18NTextView
			android:id="@+id/eidt_btn_tv"
			android:layout_width="wrap_content"
			android:layout_height="26dp"
			android:layout_alignParentEnd="true"
			i18n:fstext="xt.feed_edittag_work_home_layout.text.edit_sort"
			android:layout_centerVertical="true"
			android:layout_alignParentRight="true"
			android:textSize="12dp"
			android:textColor="#FF8000"
			android:gravity="center"
			android:layout_centerHorizontal="true"
			android:paddingLeft="8dp"
			android:paddingRight="8dp"
			android:background="@drawable/bg_shape_feed_tag_yellow_item"
                android:paddingEnd="8dp"
                android:paddingStart="8dp" />
	</RelativeLayout>

	<com.fs.commonviews.dynamicgridview.DynamicGridView
	    android:id="@+id/feed_edittag_selected_tags"
	    android:layout_width="fill_parent"
    	android:layout_height="wrap_content"
    	android:numColumns="3"
    	android:layout_marginTop="7dp"
    	android:layout_marginBottom="9dp"
		android:layout_marginLeft="6dp"
		android:layout_marginRight="6dp"
    	android:background="#ffffff"
    	android:listSelector="@null"
            android:layout_marginEnd="6dp"
            android:layout_marginStart="6dp" />
	<LinearLayout 
	    android:layout_width="fill_parent"
    	android:layout_height="wrap_content"
    	android:id="@+id/feed_edittag_unselect_container"
    	android:orientation="vertical"
	    >
	<RelativeLayout 
	    android:layout_width="fill_parent"
    	android:layout_height="wrap_content"
    	android:gravity="center_vertical"
			android:paddingLeft="12dp"
			android:paddingRight="12dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp">
	    <com.facishare.fs.sizectrlviews.SizeControlTextView
			android:id="@+id/add_info_tv"
	        android:layout_width="wrap_content"
    		android:layout_height="wrap_content"
    		i18n:fstext="xt.feed_edittag_work_home_layout.text.more_categories"
    		android:textSize="15dp"
    		android:textColor="#181C25"
	        />
		<com.facishare.fs.i18n.I18NTextView
			android:id="@+id/add_info_ex_tv"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			i18n:fstext="xt.feed_edittag_work_home_layout.text.click_to_enter_the_channel"
			android:layout_alignBaseline="@+id/add_info_tv"
			android:layout_alignBottom="@+id/add_info_tv"
			android:layout_toRightOf="@+id/add_info_tv"
			android:layout_toEndOf="@+id/add_info_tv"
			android:layout_marginLeft="8dp"
			android:layout_marginStart="8dp"
			android:textSize="12dp"
			android:textColor="#91959E"/>
	</RelativeLayout>
	<com.fs.commonviews.dynamicgridview.DynamicGridView 
	    android:id="@+id/feed_edittag_unselected_tags"
	    android:layout_width="fill_parent"
    	android:layout_height="wrap_content"
    	android:numColumns="3"
    	android:layout_marginTop="9dp"
    	android:layout_marginBottom="9dp"
    	android:background="#ffffff"
    	android:listSelector="@null"
		android:layout_marginLeft="6dp"
		android:layout_marginRight="6dp"
            android:layout_marginEnd="6dp"
            android:layout_marginStart="6dp" />
	</LinearLayout>
</LinearLayout>