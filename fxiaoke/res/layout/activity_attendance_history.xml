<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_height"/>

    <RelativeLayout android:layout_below="@id/title"
        android:layout_width="match_parent" android:layout_height="match_parent"
        android:layout_weight="1" android:background="#f2f2f2">
        <RelativeLayout android:id="@+id/unusual_check_layout"
            android:layout_width="match_parent" android:layout_height="match_parent"
            android:layout_marginBottom="210dp"
            android:visibility="invisible" android:background="@null">
            <ImageView android:id="@+id/unusual_check_img"
                android:layout_width="wrap_content" android:layout_height="wrap_content"
                android:layout_centerInParent="true"/>
            <com.facishare.fs.sizectrlviews.SizeControlTextView android:id="@+id/unusual_check_text"
                android:layout_width="wrap_content" android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_below="@id/unusual_check_img" android:layout_marginTop="20dp"
                android:textSize="14sp"
                android:textColor="#cccccc"/>
        </RelativeLayout>
        <ScrollView android:id="@+id/scroll_view" android:layout_width="match_parent"
            android:layout_height="match_parent" android:background="@null" android:scrollbars="none">
            <LinearLayout android:id="@+id/time_line_parent"
                android:layout_width="match_parent" android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingBottom="210dp">
                <com.facishare.fs.sizectrlviews.SizeControlTextView android:id="@+id/elastic_time_tip" android:layout_marginTop="38dp"
                    android:layout_width="match_parent" android:layout_height="wrap_content"
                    android:gravity="center" android:textSize="12sp" android:textColor="#cccccc"
                    android:visibility="gone" android:includeFontPadding="false"/>
                <!--<RelativeLayout android:id="@+id/elastic_worktime_layout" android:layout_width="match_parent"-->
                <!--android:layout_height="wrap_content" android:layout_marginTop="5dp"-->
                <!--android:visibility="gone">-->
                <!--<View android:layout_width="67dp" android:layout_height="1dp"-->
                <!--android:background="@drawable/attendance_gradientline_1" android:layout_centerVertical="true"-->
                <!--android:layout_toLeftOf="@+id/elastic_worktime" android:layout_marginLeft="45dp"/>-->
                <!--<TextView android:id="@+id/elastic_worktime" android:layout_width="wrap_content"-->
                <!--android:layout_height="wrap_content" android:layout_marginLeft="10dp"-->
                <!--android:layout_marginRight="10dp" android:layout_centerHorizontal="true"-->
                <!--android:textSize="13sp" android:textColor="#999999" android:includeFontPadding="false"/>-->
                <!--<View android:layout_width="67dp" android:layout_height="1dp" android:layout_centerVertical="true"-->
                <!--android:layout_marginRight="45.5dp"-->
                <!--android:background="@drawable/attendance_gradientline_2"-->
                <!--android:layout_toRightOf="@id/elastic_worktime"/>-->
                <!--</RelativeLayout>-->
                <com.facishare.fs.biz_function.subbiz_attendance_new.view.TimeLineLayout
                    android:id="@+id/time_line_layout"
                    android:layout_marginTop="30dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"/>
                <!--<RelativeLayout-->
                <!--android:id="@+id/approve_layout"-->
                <!--android:layout_width="match_parent"-->
                <!--android:layout_height="wrap_content"-->
                <!--android:layout_marginLeft="12dp"-->
                <!--android:layout_marginRight="12dp"-->
                <!--android:visibility="gone"-->
                <!--android:background="@drawable/attendance_note_bg"-->

                <!--&gt;-->
                <!--<View-->
                <!--android:id="@+id/approval_icon"-->
                <!--android:layout_width="14dp"-->
                <!--android:layout_height="14dp"-->
                <!--android:layout_marginBottom="15dp"-->
                <!--android:layout_marginLeft="15dp"-->
                <!--android:layout_marginTop="15dp"-->
                <!--android:background="@drawable/approval" />-->

                <!--&lt;!&ndash;<com.facishare.fs.sizectrlviews.SizeControlTextView&ndash;&gt;-->
                <!--&lt;!&ndash;android:layout_width="wrap_content"&ndash;&gt;-->
                <!--&lt;!&ndash;android:layout_height="44dp"&ndash;&gt;-->
                <!--&lt;!&ndash;android:layout_marginLeft="6dp"&ndash;&gt;-->
                <!--&lt;!&ndash;android:layout_toRightOf="@id/approval_icon"&ndash;&gt;-->
                <!--&lt;!&ndash;android:gravity="center_vertical"&ndash;&gt;-->
                <!--&lt;!&ndash;android:includeFontPadding="false"&ndash;&gt;-->
                <!--&lt;!&ndash;i18n:fstext="xt.approve_list_center_item.text.approval"&ndash;&gt;-->
                <!--&lt;!&ndash;android:textColor="#666666"&ndash;&gt;-->
                <!--&lt;!&ndash;android:textSize="13dp"&ndash;&gt;-->
                <!--&lt;!&ndash;android:id="@+id/view2" />&ndash;&gt;-->

                <!--&lt;!&ndash;<com.facishare.fs.sizectrlviews.SizeControlTextView&ndash;&gt;-->
                <!--&lt;!&ndash;android:id="@+id/write_approval"&ndash;&gt;-->
                <!--&lt;!&ndash;android:layout_width="wrap_content"&ndash;&gt;-->
                <!--&lt;!&ndash;android:layout_height="34dp"&ndash;&gt;-->
                <!--&lt;!&ndash;android:layout_alignParentRight="true"&ndash;&gt;-->
                <!--&lt;!&ndash;android:layout_marginRight="2dp"&ndash;&gt;-->
                <!--&lt;!&ndash;android:layout_marginTop="5dp"&ndash;&gt;-->
                <!--&lt;!&ndash;android:gravity="center_vertical"&ndash;&gt;-->
                <!--&lt;!&ndash;android:includeFontPadding="false"&ndash;&gt;-->
                <!--&lt;!&ndash;android:paddingLeft="10dp"&ndash;&gt;-->
                <!--&lt;!&ndash;android:paddingRight="10dp"&ndash;&gt;-->
                <!--&lt;!&ndash;i18n:fstext="xt.attendance_history.des.send_approval"&ndash;&gt;-->
                <!--&lt;!&ndash;android:textColor="@color/attendance_clickable_text"&ndash;&gt;-->
                <!--&lt;!&ndash;android:textSize="13dp"&ndash;&gt;-->
                <!--&lt;!&ndash;android:visibility="gone"/>&ndash;&gt;-->
                <!--<com.facishare.fs.sizectrlviews.SizeControlTextView-->
                <!--android:layout_width="wrap_content"-->
                <!--android:layout_height="wrap_content"-->
                <!--android:layout_marginTop="15dp"-->
                <!--android:layout_marginLeft="6dp"-->
                <!--android:layout_toRightOf="@id/approval_icon"-->
                <!--android:gravity="center_vertical"-->
                <!--android:includeFontPadding="false"-->
                <!--i18n:fstext="xt.approve_list_center_item.text.approval"-->
                <!--android:textColor="#666666"-->
                <!--android:textSize="13dp"-->
                <!--android:id="@+id/view2" />-->

                <!--<com.facishare.fs.sizectrlviews.SizeControlTextView-->
                <!--android:id="@+id/write_approval"-->
                <!--android:layout_width="wrap_content"-->
                <!--android:layout_height="wrap_content"-->
                <!--android:layout_alignParentRight="true"-->
                <!--android:layout_marginRight="2dp"-->
                <!--android:layout_marginTop="15dp"-->
                <!--android:gravity="center_vertical"-->
                <!--android:includeFontPadding="false"-->
                <!--android:paddingLeft="10dp"-->
                <!--android:paddingRight="10dp"-->
                <!--i18n:fstext="xt.attendance_history.des.send_approval"-->
                <!--android:textColor="@color/attendance_clickable_text"-->
                <!--android:textSize="13dp"-->
                <!--android:visibility="gone"/>-->
                <!--<com.facishare.fs.sizectrlviews.SizeControlTextView-->
                <!--android:id="@+id/approval_instructions"-->
                <!--android:layout_width="wrap_content"-->
                <!--android:layout_height="wrap_content"-->
                <!--android:gravity="center_vertical"-->
                <!--android:includeFontPadding="false"-->
                <!--i18n:fstext="xt.activity_attendance_history.text.send_kj"-->
                <!--android:textColor="#999999"-->
                <!--android:textSize="13dp"-->
                <!--android:layout_marginBottom="10dp"-->
                <!--android:layout_below="@+id/view2"-->
                <!--android:layout_alignLeft="@+id/view2"-->
                <!--android:layout_alignStart="@+id/view2"-->
                <!--android:visibility="gone"/>-->

                <!--<LinearLayout-->
                <!--android:id="@+id/approval_content_layout"-->
                <!--android:layout_width="match_parent"-->
                <!--android:layout_height="wrap_content"-->
                <!--android:layout_below="@+id/approval_instructions"-->
                <!--android:orientation="vertical"-->
                <!--android:paddingLeft="15dp"></LinearLayout>-->


                <!--<View-->
                <!--android:id="@+id/bottom_line"-->
                <!--android:layout_width="match_parent"-->
                <!--android:layout_height="0.5dp"-->
                <!--android:background="#ededed"-->
                <!--android:layout_marginLeft="10dp"-->
                <!--android:layout_marginRight="10dp"-->
                <!--android:layout_below="@+id/approval_content_layout"-->
                <!--/>-->
                <!--</RelativeLayout>-->
                <!--<RelativeLayout android:id="@+id/note_layout"-->
                <!--android:layout_width="match_parent" android:layout_height="wrap_content"-->
                <!--android:layout_marginLeft="12dp" android:layout_marginRight="12dp"-->
                <!--android:visibility="gone"-->
                <!--android:background="@drawable/attendance_note_bg">-->
                <!--<View android:id="@+id/note_icon"-->
                <!--android:layout_width="14dp" android:layout_height="14dp"-->
                <!--android:background="@drawable/attendance_note" android:layout_marginLeft="15dp"-->
                <!--android:layout_marginTop="15dp" android:layout_marginBottom="15dp"/>-->
                <!--<com.facishare.fs.sizectrlviews.SizeControlTextView android:layout_width="wrap_content" android:layout_height="14dp"-->
                <!--android:layout_marginTop="15dp" android:layout_toRightOf="@id/note_icon"-->
                <!--android:gravity="center_vertical"-->
                <!--android:layout_marginLeft="6dp" android:textSize="13dp" android:textColor="#666666"-->
                <!--i18n:fstext="crm.layout.feed_send_general_bill_fragment.7849" android:includeFontPadding="false"/>-->
                <!--<LinearLayout android:id="@+id/note_content_layout"-->
                <!--android:layout_width="match_parent"-->
                <!--android:layout_height="wrap_content" android:paddingLeft="15dp"-->
                <!--android:orientation="vertical" android:layout_marginTop="44dp">-->
                <!--</LinearLayout>-->
                <!--</RelativeLayout>-->

                <RelativeLayout
                    android:id="@+id/note_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="12dp"
                    android:layout_marginTop="25dp"
                    android:background="@drawable/attendance_note_bg"

                        android:layout_marginStart="12dp"
                        android:layout_marginEnd="12dp">
                    <RelativeLayout
                        android:id="@+id/approve_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        >
                        <View
                            android:id="@+id/approval_icon"
                            android:layout_width="14dp"
                            android:layout_height="14dp"
                            android:layout_marginBottom="0dp"
                            android:layout_marginLeft="15dp"
                            android:layout_marginTop="15dp"
                            android:background="@drawable/approval"
                                android:layout_marginStart="15dp" />

                        <com.facishare.fs.sizectrlviews.SizeControlTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="15dp"
                            android:layout_marginLeft="6dp"
                            android:layout_toRightOf="@id/approval_icon"
                            android:gravity="center_vertical"
                            android:includeFontPadding="false"
                            i18n:fstext="xt.approve_list_center_item.text.approval"
                            android:textColor="#666666"
                            android:textSize="13dp"
                            android:id="@+id/view2"
                                android:layout_toEndOf="@id/approval_icon"
                                android:layout_marginStart="6dp" />

                        <com.facishare.fs.sizectrlviews.SizeControlTextView
                            android:id="@+id/write_approval"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_marginRight="2dp"
                            android:layout_marginTop="15dp"
                            android:gravity="center_vertical"
                            android:includeFontPadding="false"
                            android:paddingLeft="10dp"
                            android:paddingRight="10dp"
                            i18n:fstext="xt.attendance_history.des.send_approval"
                            android:textColor="@color/attendance_clickable_text"
                            android:textSize="13dp"
                            android:visibility="gone"
                                android:layout_marginEnd="2dp"
                                android:paddingStart="10dp"
                                android:layout_alignParentEnd="true"
                                android:paddingEnd="10dp" />
                        <com.facishare.fs.sizectrlviews.SizeControlTextView
                            android:id="@+id/approval_instructions"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:includeFontPadding="false"
                            android:layout_marginTop="10dp"
                            i18n:fstext="xt.activity_attendance_history.text.send_kj"
                            android:textColor="#999999"
                            android:textSize="13dp"
                            android:layout_marginBottom="10dp"
                            android:layout_below="@+id/view2"
                            android:layout_alignLeft="@+id/view2"
                            android:layout_alignStart="@+id/view2" />

                        <LinearLayout
                            android:id="@+id/approval_content_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/approval_instructions"
                            android:orientation="vertical"
                            android:paddingLeft="15dp"
                                android:paddingStart="15dp"></LinearLayout>


                        <View
                            android:id="@+id/bottom_line"
                            android:layout_width="match_parent"
                            android:layout_height="0.5dp"
                            android:background="#ededed"
                            android:layout_marginLeft="10dp"
                            android:layout_marginRight="10dp"
                            android:layout_below="@+id/approval_content_layout"
                                android:layout_marginEnd="10dp"
                                android:layout_marginStart="10dp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/approve_layout_new"
                        android:layout_below="@+id/approve_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        >
                        <View
                            android:id="@+id/approval_icon_new"
                            android:layout_width="14dp"
                            android:layout_height="14dp"
                            android:layout_marginBottom="0dp"
                            android:layout_marginLeft="15dp"
                            android:layout_marginTop="15dp"
                            android:background="@drawable/approval"
                                android:layout_marginStart="15dp" />

                        <com.facishare.fs.sizectrlviews.SizeControlTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="15dp"
                            android:layout_marginLeft="6dp"
                            android:layout_toRightOf="@id/approval_icon_new"
                            android:gravity="center_vertical"
                            android:includeFontPadding="false"
                            i18n:fstext="xt.approve_list_center_item.text.approval"
                            android:textColor="#666666"
                            android:textSize="13dp"
                            android:id="@+id/view2_new"
                                android:layout_toEndOf="@id/approval_icon_new"
                                android:layout_marginStart="6dp" />

                        <com.facishare.fs.sizectrlviews.SizeControlTextView
                            android:id="@+id/write_approval_new"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_marginRight="2dp"
                            android:layout_marginTop="15dp"
                            android:gravity="center_vertical"
                            android:includeFontPadding="false"
                            android:paddingLeft="10dp"
                            android:paddingRight="10dp"
                            i18n:fstext="xt.attendance_history.des.send_approval"
                            android:textColor="@color/attendance_clickable_text"
                            android:textSize="13dp"
                            android:visibility="visible"
                                android:layout_alignParentEnd="true"
                                android:paddingStart="10dp"
                                android:paddingEnd="10dp"
                                android:layout_marginEnd="2dp" />
                        <com.facishare.fs.sizectrlviews.SizeControlTextView
                            android:id="@+id/approval_instructions_new"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:includeFontPadding="false"
                            android:layout_marginTop="10dp"
                            i18n:fstext="xt.activity_attendance.text.send_qj_new"
                            android:textColor="#999999"
                            android:textSize="13dp"
                            android:layout_marginBottom="10dp"
                            android:layout_below="@+id/view2_new"
                            android:layout_alignLeft="@+id/view2_new"
                            android:layout_alignStart="@+id/view2_new" />

                        <LinearLayout
                            android:id="@+id/approval_content_layout_new"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/approval_instructions_new"
                            android:orientation="vertical"
                            android:paddingLeft="15dp"
                                android:paddingStart="15dp"></LinearLayout>


                        <View
                            android:id="@+id/bottom_line_new"
                            android:layout_width="match_parent"
                            android:layout_height="0.5dp"
                            android:background="#ededed"
                            android:layout_marginLeft="10dp"
                            android:layout_marginRight="10dp"
                            android:layout_below="@+id/approval_content_layout_new"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp" />
                    </RelativeLayout>
                    <RelativeLayout
                        android:id="@+id/notes_layout"
                        android:layout_below="@+id/approve_layout_new"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        >
                        <View
                            android:id="@+id/note_icon"
                            android:layout_width="14dp"
                            android:layout_height="14dp"
                            android:layout_marginBottom="15dp"
                            android:layout_marginLeft="15dp"
                            android:layout_marginTop="15dp"
                            android:background="@drawable/attendance_note"
                                android:layout_marginStart="15dp" />

                        <com.facishare.fs.sizectrlviews.SizeControlTextView
                            android:layout_width="wrap_content"
                            android:layout_height="44dp"
                            android:layout_marginLeft="6dp"
                            android:layout_toRightOf="@id/note_icon"
                            android:gravity="center_vertical"
                            android:includeFontPadding="false"
                            i18n:fstext="crm.layout.feed_send_general_bill_fragment.7849"
                            android:textColor="#666666"
                            android:textSize="13dp"
                                android:layout_marginStart="6dp"
                                android:layout_toEndOf="@id/note_icon" />

                        <com.facishare.fs.sizectrlviews.SizeControlTextView
                            android:id="@+id/write_note"
                            android:layout_width="wrap_content"
                            android:layout_height="34dp"
                            android:layout_alignParentRight="true"
                            android:layout_marginRight="2dp"
                            android:layout_marginTop="5dp"
                            android:gravity="center_vertical"
                            android:includeFontPadding="false"
                            android:paddingLeft="10dp"
                            android:paddingRight="10dp"
                            i18n:fstext="xt.activity_attendance_history.text.write"
                            android:textColor="@color/attendance_clickable_text"
                            android:textSize="13dp"
                                android:paddingStart="10dp"
                                android:paddingEnd="10dp"
                                android:layout_alignParentEnd="true"
                                android:layout_marginEnd="2dp" />

                        <LinearLayout
                            android:id="@+id/note_content_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="44dp"
                            android:orientation="vertical"
                            android:paddingLeft="15dp"
                                android:paddingStart="15dp"></LinearLayout>
                    </RelativeLayout>
                </RelativeLayout>
            </LinearLayout>
        </ScrollView>
    </RelativeLayout>

    <com.facishare.fs.sizectrlviews.SizeControlTextView android:id="@+id/date_text" android:layout_width="match_parent" android:layout_height="26dp"
        android:layout_below="@id/title" android:background="#f2f2f2"
        android:gravity="center" android:textSize="12sp" android:textColor="#999999"/>

    <RelativeLayout android:id="@+id/bottom_layout" android:layout_alignParentBottom="true"
        android:layout_width="match_parent" android:layout_height="210dp"
        android:background="@null">
        <LinearLayout android:layout_width="match_parent" android:layout_height="match_parent"
            android:orientation="vertical">
            <!-- 该LinearLayout存在意义是充当整个父布局的背景，该背景分两部分：上面的渐变区到下面的不透明白色区-->
            <View android:layout_width="match_parent" android:layout_height="70dp"
                android:layout_alignParentBottom="true" android:background="@drawable/attendance_bottom_gradient"/>
            <View android:layout_width="match_parent" android:layout_height="0dp" android:layout_weight="1"
                android:background="#ffffff"/>
        </LinearLayout>
        <LinearLayout android:id="@+id/check_summary_layout" android:layout_width="match_parent"
            android:layout_height="168dp" android:layout_alignParentBottom="true"
            android:gravity="center_horizontal|bottom" android:background="@null"
            android:paddingBottom="40dp"
            android:orientation="vertical" android:visibility="invisible">
            <com.facishare.fs.sizectrlviews.SizeControlTextView android:id="@+id/work_time_pretext" android:layout_width="wrap_content"
                android:layout_height="16dp"
                android:textSize="12dp"
                android:textColor="#cccccc" android:text=""/>
            <com.facishare.fs.sizectrlviews.SizeControlTextView android:id="@+id/work_time" android:layout_width="wrap_content"
                android:layout_height="31dp" android:textSize="22dp" android:layout_marginTop="7dp"
                android:textColor="#666666" android:layout_marginBottom="5dp"
                android:gravity="center_vertical"/>
            <com.facishare.fs.sizectrlviews.SizeControlTextView android:id="@+id/work_time_summary_tip" android:layout_width="wrap_content"
                android:layout_height="wrap_content" android:visibility="gone"
                android:textSize="12dp" android:textColor="#cccccc" i18n:fstext="xt.attendance_history.des.count_fail_when_error"/>
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>