<?xml version="1.0" encoding="UTF-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_shape_edit_prompt_context_menu_item_selector"
    android:orientation="vertical" >

    <LinearLayout
        android:id="@+id/feedArchiveCickedItemLL"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_shape_edit_prompt_context_menu_item_selector"
        android:orientation="horizontal"
        android:paddingLeft="23dp"
            android:paddingStart="23dp">

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/feedArchiveCickedItemNameTV"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center_vertical|start"
            android:singleLine="true"
            android:textColor="#000000"
            android:textSize="17sp" />

        <ImageView
            android:id="@+id/customerViewRightArrow"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_gravity="end|center_vertical"
            android:background="@drawable/commonlist_arrow" />
    </LinearLayout>

    <View
        android:id="@+id/feedArchiveDividerLine"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_below="@id/feedArchiveCickedItemLL"
        android:layout_marginLeft="23dp"
        android:background="#c5c3c9"
        android:visibility="gone"
            android:layout_marginStart="23dp" />

    <View
        android:id="@+id/feedArchiveFullDividerLine"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_below="@id/feedArchiveDividerLine"
        android:background="#c5c3c9"
        android:visibility="gone" />

</RelativeLayout>