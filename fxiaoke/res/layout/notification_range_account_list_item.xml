<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
              android:layout_width="fill_parent"
              android:layout_height="wrap_content"
              android:orientation="vertical" >

    <LinearLayout
            android:id="@+id/itemlayout"
            android:layout_width="fill_parent"
            android:layout_height="72dp"
            android:background="@drawable/invitation_no_semicircle_selector"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="12dp"
            android:paddingStart="12dp">

        <ImageView
                android:id="@+id/imageHeader"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginRight="12dp"
                android:background="@null"
                android:scaleType="centerCrop"
                android:layout_marginEnd="12dp" />

        <RelativeLayout
                android:id="@+id/coll_ll"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" >

            <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_alignParentLeft="true"
                    android:layout_toLeftOf="@id/iv_Feedback"
                    android:layout_alignParentStart="true"
                    android:layout_toStartOf="@id/iv_Feedback">
                <com.facishare.fs.sizectrlviews.SizeControlTextView
                        android:id="@+id/txtName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:layout_marginRight="8dp"
                        android:ellipsize="end"
                        android:singleLine="true"
                        i18n:fstext="crm.layout.meeting_member_choose_list_item.7579"
                        android:layout_centerVertical="true"
                        android:textColor="@color/contacts_text_name"
                        android:textSize="17sp"
                        android:layout_marginEnd="8dp" />


                <com.facishare.fs.sizectrlviews.SizeControlTextView
                        android:id="@+id/txtInfo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="2dp"
                        android:ellipsize="end"
                        android:singleLine="true"
                        i18n:fstext="pay.common.common.description"
                        android:layout_centerVertical="true"
                        android:textColor="@color/contacts_text_desc"
                        android:layout_toRightOf="@id/txtName"
                        android:textSize="15sp"
                        android:layout_toEndOf="@id/txtName"
                        android:layout_marginEnd="2dp" />
            </RelativeLayout>


            <ImageView
                    android:id="@+id/iv_Feedback"
                    style="@style/commonlist_arrow_style"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true" />
        </RelativeLayout>
    </LinearLayout>

    <View
            android:id="@+id/bottom_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/bottom_line_height"
            android:layout_alignParentBottom="true"
            android:background="@drawable/slice_line_1px"
            android:visibility="visible" />
</LinearLayout>