<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    android:orientation="vertical" >

    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
        android:id="@+id/title"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/title_height" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent" >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical" >

            <RelativeLayout
                android:id="@+id/callDepLayout"
                android:layout_width="fill_parent"
                android:layout_height="@dimen/customer_view_item_height"
                android:background="@drawable/invitation_no_semicircle_selector"
                android:clickable="true"
                android:gravity="center_vertical"
                android:paddingBottom="10dp"
                android:paddingLeft="10dp"
                android:paddingTop="10dp"
                android:visibility="gone"
                    android:paddingStart="10dp">

                <ImageView
                    android:id="@+id/customerViewRightArrow"
                    android:layout_width="34dp"
                    android:layout_height="34dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:background="@drawable/commonlist_arrow"
                        android:layout_alignParentEnd="true" />

                <com.facishare.fs.sizectrlviews.SizeControlTextView
                    android:id="@+id/customerViewName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:gravity="start|center"
                    i18n:fstext="xt.contact_list.text.department"
                    android:textColor="@color/customer_view_item_color"
                    android:textSize="@dimen/customer_view_item_text_size" />
            </RelativeLayout>

            <View
                android:id="@+id/callDepLayoutDivider"
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:background="#cccccc"
                android:visibility="gone" />

            <LinearLayout
                android:id="@+id/container"
                android:layout_width="fill_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical" >
            </LinearLayout>
        </LinearLayout>

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/empty_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            i18n:fstext="xt.contact_list.des.no_staffs"
            android:textColor="#cccccc"
            android:textSize="18dp"
            android:visibility="gone" />
    </FrameLayout>

</LinearLayout>