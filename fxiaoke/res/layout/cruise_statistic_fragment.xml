<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical" >
    
    
    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:gravity="center"
        i18n:fstext="xt.cruise_statistic_fragment.text.this_feature_is_not_open_yet"
        />

</LinearLayout>
