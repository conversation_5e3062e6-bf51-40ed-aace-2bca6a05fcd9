<?xml version="1.0" encoding="utf-8"?>
<!-- 分类标题 -->
<!-- <TextView -->
<!-- android:id="@+id/sendTitle" -->
<!-- android:layout_width="wrap_content" -->
<!-- android:layout_height="40dip" -->
<!-- android:gravity="center_vertical" -->
<!-- android:paddingLeft="13dip" -->
<!-- i18n:fstext="second_edition_customer_layout.text.classity_title" -->
<!-- android:textColor="#adb0b3" -->
<!-- android:textSize="13sp" /> -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="@dimen/customer_view_item_height"
    android:background="@drawable/invitation_no_semicircle_selector"
    android:clickable="true"
    android:gravity="center_vertical"
    android:paddingBottom="10dp"
    android:paddingTop="10dp" >

    <ImageView
        android:id="@+id/customerViewRightArrow"
        android:layout_width="34dp"
        android:layout_height="34dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:background="@drawable/commonlist_arrow"
            android:layout_alignParentEnd="true" />

    <!-- 有备注项时就不要显示提醒text了 -->

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toLeftOf="@id/customerViewRightArrow"
        android:orientation="vertical"
            android:layout_toStartOf="@id/customerViewRightArrow">

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/customerViewRemind"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="7dip"
            android:background="@drawable/list_badge"
            android:gravity="center"
            android:paddingBottom="2px"
            android:paddingLeft="5dip"
            android:paddingRight="5dip"
            android:paddingTop="1px"
            android:text="999+"
            android:textColor="@color/l_text_write_color"
            android:textSize="12sp"
                android:paddingStart="5dip"
                android:paddingEnd="5dip"
                android:layout_marginEnd="7dip" />
    </LinearLayout>
    <!-- 有备注项时就不要显示提醒text了 -->

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/customerViewRemark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toLeftOf="@id/customerViewRightArrow"
        android:gravity="center"
        i18n:fstext="second_edition_customer_layout.text.remark_option"
        android:textColor="@color/customer_view_remark_color"
        android:textSize="14sp"
        android:visibility="gone"
            android:layout_toStartOf="@id/customerViewRightArrow" />

    <RelativeLayout
        android:id="@+id/customer_view_icon_layout"
        android:layout_width="@dimen/customer_view_interval_line_margin_left"
        android:layout_height="35dp"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"
            android:layout_alignParentStart="true">

        <ImageView
            android:id="@+id/customerViewIcon"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:scaleType="centerInside"
            android:src="@drawable/my_schedule" 
            android:layout_centerInParent="true"
            />

        <ImageView
            android:id="@+id/customerNewViewIcon"
            android:layout_width="25dp"
            android:layout_height="18dp"
            android:layout_alignParentTop="true"
            android:layout_marginLeft="27dp"
            android:scaleType="fitStart"
            android:visibility="gone"
            android:src="@drawable/icon_fieldwork_signin_new"
                android:layout_marginStart="27dp" />
        
    </RelativeLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toRightOf="@id/customer_view_icon_layout"
        android:orientation="vertical"
            android:layout_toEndOf="@id/customer_view_icon_layout">

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/customerViewName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="start|center"
            i18n:fstext="second_edition_customer_layout.text.list_option"
            android:textColor="@color/customer_view_item_color"
            android:textSize="@dimen/customer_view_item_text_size" />
    </LinearLayout>

</RelativeLayout>