<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:id="@+id/content_layout"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/transparence">

<!--    <View-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="2px"-->
<!--        android:background="@color/sessionlist_headerview_divider_color" />-->

    <RelativeLayout

        android:layout_width="fill_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/majia_layout"
            android:layout_width="fill_parent"
            android:layout_height="87dp"
            android:background="@drawable/bg_majia_guide_icon"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:visibility="gone">

            <com.facishare.fs.i18n.I18NTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:paddingBottom="4dp"
                android:textColor="#ffffff"
                android:textSize="13dp"
                i18n:fstext="xt.shortmessage_main_listview_header.text.experience_app" />

            <com.facishare.fs.i18n.I18NTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:shadowColor="#f34578"
                android:shadowDy="1"
                android:shadowRadius="2"
                android:textColor="#ffffff"
                android:textSize="15dp"
                i18n:fstext="xt.shortmessage_main_listview_header.action.vip" />
        </LinearLayout>

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/neterror_toast"
            android:layout_width="fill_parent"
            android:layout_height="36dp"
            android:layout_below="@+id/majia_layout"
            android:background="@color/sessionlist_neterror_toast_bg"
            android:drawableLeft="@drawable/net_unavailable"
            android:gravity="center|start"
            android:paddingLeft="18dp"
            android:textColor="@color/sessionlist_neterror_toast_text"
            android:textSize="14sp"
            android:visibility="gone"
            i18n:fstext="qx.session_list.conn_status.weaknet_or_nonet"
                android:drawableStart="@drawable/net_unavailable"
                android:paddingStart="18dp" />

        <RelativeLayout
            android:id="@+id/layout_sys_maintain"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_below="@+id/neterror_toast"
            android:background="@color/sessionlist_neterror_toast_bg"
            android:paddingLeft="12dp"
            android:visibility="gone"
                android:paddingStart="12dp">

            <ImageView
                android:id="@+id/iv_maintain"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_centerVertical="true"
                android:src="@drawable/sys_maintain" />

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/neterror_toast2"
                android:layout_width="fill_parent"
                android:layout_height="36dp"
                android:layout_toLeftOf="@+id/ib_maintain_close"
                android:layout_toRightOf="@+id/iv_maintain"
                android:ellipsize="end"
                android:gravity="center|start"
                android:maxLines="1"
                android:paddingLeft="6dp"
                android:paddingRight="6dp"
                android:textColor="@color/sessionlist_neterror_toast_text"
                android:textSize="14sp"
                i18n:fstext="xt.shortmessage_main_listview_header.text.service_maintain"
                    android:paddingStart="6dp"
                    android:layout_toStartOf="@+id/ib_maintain_close"
                    android:paddingEnd="6dp"
                    android:layout_toEndOf="@+id/iv_maintain" />

            <ImageView
                android:id="@+id/ib_maintain_close"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:paddingLeft="8dp"
                android:paddingRight="12dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:src="@drawable/sys_maintain_close"
                    android:paddingEnd="12dp"
                    android:layout_alignParentEnd="true"
                    android:paddingStart="8dp" />
        </RelativeLayout>
        <!--在线设备飘条-->
        <RelativeLayout
                android:id="@+id/active_client_summery_bar_rl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/layout_sys_maintain"
                android:background="@color/transparence"
                android:minHeight="40dp"
                android:paddingStart="@dimen/common_content_padding_left_or_right"
                android:paddingLeft="@dimen/common_content_padding_left_or_right"
                android:paddingEnd="@dimen/common_content_padding_left_or_right"
                android:paddingRight="@dimen/common_content_padding_left_or_right">

            <ImageView
                android:id="@+id/active_client_summery_bar_iv"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_centerVertical="true"
                android:scaleType="centerInside"
                android:src="@drawable/active_client_summery_bar_for_phone" />

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/active_client_summery_bar_tv"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toLeftOf="@+id/ib_close_active_client_summery_bar"
                android:layout_toRightOf="@+id/active_client_summery_bar_iv"
                android:ellipsize="end"
                android:gravity="center|start"
                android:paddingLeft="6dp"
                android:paddingRight="6dp"
                android:textColor="@color/neutrals15"
                android:textSize="12sp"
                i18n:fstext="xt.shortmessage_main_listview_header.text.n_phone_online"
                    android:layout_toStartOf="@+id/ib_close_active_client_summery_bar"
                    android:paddingEnd="6dp"
                    android:paddingStart="6dp"
                    android:layout_toEndOf="@+id/active_client_summery_bar_iv" />

            <ImageView
                android:id="@+id/ib_close_active_client_summery_bar"
                android:layout_width="36dp"
                android:layout_height="40dp"
                android:paddingLeft="8dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:src="@drawable/btn_active_client_hide"
                android:paddingStart="8dp"
                    android:layout_alignParentEnd="true"
                     />
        </RelativeLayout>

    </RelativeLayout>

<!--    <View-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="2px"-->
<!--        android:background="@color/sessionlist_headerview_divider_color" />-->
</LinearLayout>