<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#FFF"
    android:orientation="vertical">

   <com.fxiaoke.fscommon_res.common_view.CommonTitleView
        android:id="@+id/title"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/title_height" />

    <com.fxiaoke.fscommon_res.view.FCSearchBar
        android:id="@id/search_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:showCancelButton="false"
        app:showSearchButton="false"
        app:hint="搜索文件"
        />

    <!--<include layout="@layout/search_layout" />-->

    <ListView
        android:id="@+id/ListView_user_down"
        style="@style/default_listview_style" >
    </ListView>

</LinearLayout>