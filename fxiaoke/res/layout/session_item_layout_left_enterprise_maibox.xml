<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:id="@+id/enterprise_mailbox_content"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:foreground="@drawable/chat_from_bg_shader_selector"
    android:orientation="vertical"
    >
    <!--android:background="@drawable/chat_share_from_bg"-->
    <!--android:gravity="center_vertical"-->
    <!--android:orientation="vertical"-->
    <!--android:paddingBottom="12dp"-->
    <!--android:paddingLeft="20dp"-->
    <!--android:paddingRight="12dp"-->
    <!--android:paddingTop="12dp"-->
    <!--<TextView-->
        <!--android:id="@+id/"-->
        <!--android:layout_width="182dp"-->
        <!--android:layout_height="wrap_content"-->
        <!--android:ellipsize="end"-->
        <!--android:gravity="center_vertical"-->
        <!--android:maxLines="2"-->
        <!--android:maxWidth="182dp"-->
        <!--android:textColor="@color/sessionmsg_worknotice_title"-->
        <!--android:textSize="16sp" />-->

    <!--<LinearLayout-->
        <!--android:layout_width="wrap_content"-->
        <!--android:layout_height="wrap_content"-->
        <!--android:layout_marginTop="10dp"-->
        <!--android:orientation="horizontal" >-->


        <!--<TextView-->
            <!--android:layout_width="116dp"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:layout_marginLeft="6dp"-->
            <!--android:ellipsize="end"-->
            <!--android:maxLines="4"-->
            <!--android:maxWidth="116dp"-->
            <!--android:textColor="@color/sessionmsg_worknotice_summary"-->
            <!--android:textSize="12sp" />-->
    <!--</LinearLayout>-->
    <!--IGT样式-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_vertical"
        android:id="@+id/mailbox_content"
        android:background="@drawable/msg_left_feed_to_bg"
        >

        <RelativeLayout
            android:layout_width="fill_parent"
            android:layout_height="25dp"
            >

            <ImageView
                android:id="@+id/icon_maibox"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:src="@drawable/icon_enterprise_maibox_session"
                    android:layout_alignParentStart="true" />

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/icon_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="5dp"
                android:layout_toRightOf="@+id/icon_maibox"
                android:textColor="#61A5F0"
                i18n:fstext="crm.layout.session_item_layout_left_enterprise_maibox.7232"
                    android:layout_marginStart="5dp"
                    android:layout_toEndOf="@+id/icon_maibox" />

        </RelativeLayout>
        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/textView_line"
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:background="#c0c0c0"
            android:layout_marginTop="6dp"/>
        <LinearLayout
            android:id="@+id/LinearLayout_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="15dp"
            >
            <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/mail_title"
                android:layout_weight="1"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:textColor="#5a5a5a"
                android:textSize="16sp"
                android:maxLines="1"
                android:ellipsize="end"
                i18n:fstext="xt.session_item_layout_left_enterprise_maibox.text.no_title" />
            <ImageView
                android:id="@+id/image_attach"
                android:layout_weight="0"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:src="@drawable/mail_attach_identify" />
        </LinearLayout>
        <LinearLayout
            android:id="@+id/LinearLayout_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="15dp">
        </LinearLayout>
        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:layout_marginBottom="15dp"
            android:id="@+id/mail_summary"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:textColor="#5a5a5a"
            android:textSize="12sp"
            android:maxLines="3"
            android:ellipsize="end"
            i18n:fstext="xt.session_item_layout_left_enterprise_maibox.text.no_digest" />

    </LinearLayout>


    <RelativeLayout
        android:id="@+id/rl_mailbox_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@drawable/msg_feed_di_left_icon"
        >
        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/mailbox_time"
            android:layout_centerVertical="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="200dp"
            android:textColor="#8f8f8f"
            android:textSize="9sp"
            android:maxLines="1"
            android:ellipsize="end"
            />

        <ImageView
            android:layout_centerVertical="true"
            android:id="@+id/imageView_arrow"
            android:layout_width="6dp"
            android:layout_height="12dp"
            android:layout_alignParentRight="true"
            android:layout_marginLeft="3dp"
            android:layout_marginRight="1dp"
            android:src="@drawable/msg_feed_arrow"
                android:layout_marginStart="3dp"
                android:layout_marginEnd="1dp"
                android:layout_alignParentEnd="true" />

    </RelativeLayout>
</LinearLayout>