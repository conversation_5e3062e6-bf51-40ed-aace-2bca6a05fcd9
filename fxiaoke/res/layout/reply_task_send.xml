<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_default"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/llyt_reply"
        android:layout_width="match_parent"
        android:layout_height="@dimen/project_new_item_height"
        android:layout_marginBottom="@dimen/margin_12"
        android:layout_marginTop="@dimen/margin_12"
        android:background="@color/white"
        android:orientation="horizontal">

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/tv_comment_person"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:paddingLeft="@dimen/margin_14"
            i18n:fstext="xt.reply_task_send.text.cobject"
            android:textColor="@color/color_999999"
            android:textSize="14dp"
                android:paddingStart="@dimen/margin_14" />

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/tv_comment_person_content"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:paddingLeft="@dimen/margin_22"
            android:paddingRight="@dimen/margin_12"
            i18n:fstext="bi.layout.item_one_rpt_form.2144"
            android:singleLine="true"
            android:textColor="@color/color_999999"
            android:textSize="14dp"
                android:paddingEnd="@dimen/margin_12"
                android:paddingStart="@dimen/margin_22" />

        <ImageView
            android:id="@+id/iv_arrow_comment"
            android:layout_width="12dp"
            android:layout_height="24dp"
            android:layout_gravity="end|center_vertical"
            android:layout_marginRight="@dimen/margin_12"
            android:scaleType="center"
            android:src="@drawable/feed_approve_arrow_app_icon"
                android:layout_marginEnd="@dimen/margin_12" />

    </LinearLayout>
</LinearLayout>