<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <View
        android:id="@+id/line2"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#DEE1E8"
        android:visibility="gone"
        />

    <ViewStub
        android:id="@+id/topSearchLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout="@layout/map_near_search_keyword_layout"
        />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#DEE1E8"
        />


    <LinearLayout
        android:id="@+id/bottomLayout"
        android:paddingRight="12dp"
        android:paddingLeft="12dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
    >

        <LinearLayout
            android:id="@+id/btnSelectRadius"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:orientation="vertical"
            android:gravity="center_vertical|left"
            >
            <TextView
                android:id="@+id/txtNear"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="#181C25"
                android:text="附近"
                />
            <TextView
                android:id="@+id/btnSelectSence"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="#181C25"
                android:text="200米"
                android:textStyle="bold"
                android:drawableRight="@drawable/to_down_arraw"
                android:drawableEnd="@drawable/to_down_arraw" />


        </LinearLayout>


<!--        <View-->
<!--            android:layout_marginLeft="12dp"-->
<!--            android:layout_width="1dp"-->
<!--            android:layout_height="22dp"-->
<!--            android:background="#DEE1E8"/>-->

        <ViewStub
            android:id="@+id/bottomSearchLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout="@layout/map_near_search_keyword_layout"
            />



        <HorizontalScrollView
            android:id="@+id/nearMapFilterLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none"
            >
            <com.fxiaoke.cmviews.view.CustomNestRadioGroup
                android:id="@+id/mapNearLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingLeft="12dp">

            </com.fxiaoke.cmviews.view.CustomNestRadioGroup>
        </HorizontalScrollView>


    </LinearLayout>



    <LinearLayout
        android:id="@+id/gaodeRangeLayout"
        android:background="#F7F8FA"
        android:layout_width="match_parent"
        android:layout_height="26dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:visibility="gone"
            android:paddingStart="12dp"
            android:paddingEnd="12dp">

        <TextView
            android:id="@+id/txtTujian"
            android:text="推荐行业范围:按地上按时发斯蒂芬萨达发生的法师法师阿斯蒂芬阿斯蒂芬阿斯蒂放水"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:ellipsize="end"
            android:textSize="12sp"
            android:layout_weight="1"
            />

        <TextView
            android:id="@+id/txtSetting"
            android:text="设置"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12dp"
            android:textColor="#0C6CFF"
            />


    </LinearLayout>
</LinearLayout>
