<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/userlayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginTop="14dp">
        <!-- 头像 -->
        <ImageView
            android:id="@+id/img_user_icon"
            android:layout_width="@dimen/group_participants_item_width"
            android:layout_height="@dimen/group_participants_item_width"
            android:scaleType="fitXY" />
        <!-- 姓名 -->
        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/tv_groupmanage_username"
            android:layout_width="56dp"
            android:layout_height="wrap_content"
            android:layout_below="@id/img_user_icon"
            android:layout_marginTop="3dp"
            android:ellipsize="end"
            android:gravity="center_horizontal"
            android:singleLine="true"
            android:textColor="#999999"
            android:textSize="11dp"
            android:visibility="gone"/>

        <ImageView
            android:id="@+id/img_outeremp_mark"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignBottom="@id/img_user_icon"
            android:layout_alignRight="@id/img_user_icon"
            android:background="@drawable/group_manage_cross_mark"
            android:visibility="gone"
                android:layout_alignEnd="@id/img_user_icon" />
    </RelativeLayout>

</RelativeLayout>