<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="fill_parent"
    android:orientation="vertical" 
    >
    <RelativeLayout
        android:id="@+id/RelativeLayout_no_list"
        android:layout_width="fill_parent"
        android:layout_height="350dp" 
        android:background="#eeeeee"
        android:visibility="gone">

       <ImageView
           android:id="@+id/imageView1"
           android:layout_width="70dp"
           android:layout_height="70dp"
           android:layout_centerHorizontal="true"
           android:layout_centerVertical="true"
           android:src="@drawable/nocontent_background" />

       <com.facishare.fs.sizectrlviews.SizeControlTextView
           android:id="@+id/textView_no_data1"
           android:layout_width="wrap_content"
           android:layout_height="wrap_content"
           android:layout_below="@+id/imageView1"
           android:layout_centerHorizontal="true"
           android:layout_marginTop="5dp"
           i18n:fstext="th.base.view.content_null" />
        
    </RelativeLayout> 
    <LinearLayout
        android:id="@+id/LinearLayout_list"
        android:layout_width="wrap_content"
        android:layout_height="260dp"
        android:orientation="horizontal" 
        android:background="#ffffff">

        <ListView
            android:id="@+id/listView_left"
			style="@style/default_listview_style"
            
            android:layout_weight="1" >
        </ListView>

        <ListView
            android:id="@+id/listView_right"
			style="@style/default_listview_feed_style"
			android:background="#EEEEEE"
            android:layout_weight="1" >
        </ListView>
    </LinearLayout>
    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/textView_x"
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:text="" 
        android:background="#ffffff"/>
    <RelativeLayout
        android:id="@+id/RelativeLayout_button"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="#f0f0f0">
        

        <com.facishare.fs.sizectrlviews.SizeControlButton
            android:id="@+id/button_ok"
            android:layout_width="100dp"
            android:layout_height="30dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:background="@drawable/btn_customer_bg"
            i18n:fstext="av.common.string.confirm" 
            android:textSize="16dp" 
            android:textColor="#555555"
                android:layout_marginEnd="15dp"
                android:layout_alignParentEnd="true" />

        <com.facishare.fs.sizectrlviews.SizeControlButton
            android:id="@+id/button_remove"
            android:layout_width="100dp"
            android:layout_height="30dp"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:background="@drawable/btn_customer_bg"
            i18n:fstext="xt.handwriting_signature_main.text.empty"
            android:textSize="16dp" 
            android:textColor="#555555"
                android:layout_marginStart="15dp"
                android:layout_alignParentStart="true" />


        
    </RelativeLayout>

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/textView_di"
        android:layout_width="fill_parent"
        android:layout_height="300dp"
        android:text="" 
        android:background="#78000000"/>

</LinearLayout>