<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#f2f2f2"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:minHeight="28dp"
    android:paddingLeft="12dp"
    android:paddingRight="8dp"
        android:paddingEnd="8dp"
        android:paddingStart="12dp">

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/tv_group_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:textColor="@color/color_999999"
        android:textSize="14dp"
        android:lines="1"
        android:ellipsize="end"/>

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/tv_group_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="4dp"
        android:textColor="@color/color_999999"
        android:textSize="14dp"
            android:layout_marginStart="4dp" />
</LinearLayout>