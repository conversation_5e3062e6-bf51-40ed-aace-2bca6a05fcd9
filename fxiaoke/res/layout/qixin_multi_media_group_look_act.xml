<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@color/qixin_mulit_img_group_look_list_bg" >

    <com.fs.commonviews.stickygridview.StickyGridHeadersGridView
        android:id="@+id/qixin_multi_img_group_look_gridView"
        style="@style/default_listview_style"
        android:descendantFocusability="beforeDescendants"
        android:layout_marginTop="@dimen/title_height" 
        android:background="@android:color/transparent"
        android:horizontalSpacing="2dp"
        android:numColumns="4"
        android:scrollingCache="false"
        android:stretchMode="columnWidth"
        />
    <TextView
            android:id="@+id/qixin_multi_media_group_no_data_prompt"
            android:layout_width="fill_parent"
            android:layout_height="fill_parent"
            android:gravity="center"
            android:textColor="#87878c"
            android:textSize="18dp"
            android:background="#EFF0F4"
            android:visibility="gone"
            />
    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
        android:id="@+id/title"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/title_height" />

</RelativeLayout>