<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="18dp"
    android:layout_marginRight="18dp"
    android:orientation="vertical"
        android:layout_marginStart="18dp"
        android:layout_marginEnd="18dp">

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/holiday_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="6dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:textColor="#ff9faecc"
        i18n:fstext="xt.attendance_new_holidays_detail_item.text.new_year"
        android:textSize="14sp" />


    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/holiday_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:singleLine="false"
        android:textColor="#ff333333"
        android:textSize="12sp" />


    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="24dp" />

</LinearLayout>