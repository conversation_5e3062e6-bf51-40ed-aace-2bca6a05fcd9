<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
              android:layout_width="fill_parent"
              android:layout_height="fill_parent"
              android:background="#fff"
              android:orientation="vertical" >

    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
            android:id="@+id/title"
            android:layout_width="fill_parent"
            android:layout_height="@dimen/title_height" />

    <LinearLayout
            android:id="@+id/commonly_no_vote_item_show"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#fff"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingTop="210dp"
            android:visibility="gone" >

        <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/no_vote_item_show_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:gravity="center"
                i18n:fstext="xt.notification_vote_layout.text.vote_removed"
                android:textColor="#aaaaaa"
                android:textSize="18sp"
                android:layout_marginEnd="16dp"
                android:layout_marginStart="16dp" />
    </LinearLayout>

    <LinearLayout
            android:id="@+id/topLayout"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone" >

        <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/work_share_vote_margin_left"
                android:layout_marginRight="@dimen/work_share_vote_margin_left"
                android:orientation="vertical"
                android:layout_marginStart="@dimen/work_share_vote_margin_left"
                android:layout_marginEnd="@dimen/work_share_vote_margin_left">

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                    android:id="@+id/txtVoteTitle"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textColor="@color/color_333333"
                    android:textSize="20sp" />

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                    android:id="@+id/txtVoteSelectType"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textColor="@color/color_999999"
                    android:textSize="16sp" />

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                    android:id="@+id/txtVoteTip"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textColor="#ff9b48"
                    android:textSize="14sp" />
        </LinearLayout>

        <View
                android:layout_width="fill_parent"
                android:layout_height="1px"
                android:layout_marginTop="10dp"
                android:background="#cccccc" />
    </LinearLayout>

    <ScrollView
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fillViewport="true"
            android:scrollbars="none" >

        <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical" >

            <LinearLayout
                    android:id="@+id/voteItemslayout"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" >

                <!-- <ProgressBar -->
                <!-- android:id="@+id/toStateProgressBar" -->
                <!-- android:layout_width="fill_parent" -->
                <!-- android:layout_height="wrap_content" -->
                <!-- android:layout_gravity="center" /> -->


                <!-- <TextView -->
                <!-- android:id="@+id/txtTip" -->
                <!-- android:layout_width="wrap_content" -->
                <!-- android:layout_height="150dp" -->
                <!-- android:gravity="center" -->
                <!-- android:visibility="gone" -->
                <!-- android:textSize="@dimen/x_text_size" -->
                <!-- i18n:fstext="xt.notification_vote_layout.text.no_permission" -->
                <!-- android:layout_gravity="center" /> -->
            </LinearLayout>

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                    android:id="@+id/txtSource"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone" />

            <!-- <TextView -->
            <!-- android:layout_width="fill_parent" -->
            <!-- android:layout_height="30dp" /> -->
        </LinearLayout>
    </ScrollView>

    <LinearLayout
            android:id="@+id/voteBottomLayout"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:background="#f9f9f9"
            android:orientation="vertical"
            android:visibility="gone" >

        <View
                android:layout_width="fill_parent"
                android:layout_height="1px"
                android:background="#cccccc" />

        <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="78dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:paddingStart="16dp"
                android:paddingEnd="16dp">

            <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal" >

                <com.facishare.fs.sizectrlviews.SizeControlButton
                        android:id="@+id/btnVote"
                        android:layout_width="@dimen/work_share_btn_width"
                        android:layout_height="@dimen/work_share_vote_item_height"
                        android:layout_gravity="start"
                        android:layout_marginRight="16dp"
                        android:layout_weight="1"
                        android:background="@drawable/btn_vote_choose_selector"
                        android:gravity="center"
                        android:onClick="onClick"
                        i18n:fstext="xt.notification_vote_layout.text.immediately_vote"
                        android:textColor="@color/white"
                        android:textSize="@dimen/work_share_vote_opp_text_size"
                        android:visibility="gone"
                        android:layout_marginEnd="16dp" />

                <com.facishare.fs.sizectrlviews.SizeControlButton
                        android:id="@+id/btnLookVote"
                        android:layout_width="@dimen/work_share_btn_width"
                        android:layout_height="@dimen/work_share_vote_item_height"
                        android:layout_gravity="end"
                        android:layout_weight="1"
                        android:background="@drawable/btn_vote_show_result_selector"
                        android:gravity="center"
                        android:onClick="onClick"
                        i18n:fstext="xt.vote_layout.action.look_result.text.look_result"
                        android:textColor="@color/white"
                        android:textSize="@dimen/work_share_vote_opp_text_size"
                        android:visibility="gone" />
            </LinearLayout>

            <com.facishare.fs.sizectrlviews.SizeControlButton
                    android:id="@+id/btnBackVote"
                    android:layout_width="fill_parent"
                    android:layout_height="@dimen/work_share_vote_item_height"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:background="@drawable/btn_vote_show_result_selector"
                    android:gravity="center"
                    android:onClick="onClick"
                    i18n:fstext="xt.notification_vote_layout.text.return_vote"
                    android:textColor="@color/white"
                    android:textSize="@dimen/work_share_vote_opp_text_size"
                    android:visibility="gone" />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>