<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
        android:id="@+id/title"
        android:layout_alignParentTop="true"
        android:layout_width="match_parent"
        android:layout_height="48dp" />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:id="@+id/container"
                android:layout_below="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                >
            </LinearLayout>
            <include layout="@layout/outdoor_list_ai_layout" />
        </FrameLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/LinearLayout_v2_ok"
        android:layout_below="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:visibility="gone"
        android:orientation="vertical"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="1dp">
        <LinearLayout
            android:id="@+id/bn_ok_send"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/btn_orange_outdoor_selector"
            android:gravity="center_vertical|center_horizontal"
            android:layout_marginLeft="14dp"
            android:layout_marginRight="14dp"
            android:layout_marginTop="9dp"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal"
                android:layout_marginEnd="14dp"
                android:layout_marginStart="14dp">
            <com.facishare.fs.i18n.I18NTextView
                android:id="@+id/tv_bn_ok_send"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/login_item_height"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:textColor="#ffffff"
                android:text="快速签到"
                android:textSize="18sp" />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>