<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="50dp"
              android:background="@color/color_ffffff"
              android:gravity="center_vertical"
              android:minHeight="50dp"
              android:orientation="horizontal"
              android:paddingLeft="5dp"
              android:paddingRight="5dp"
        android:paddingStart="5dp"
        android:paddingEnd="5dp">

    <ImageView
        android:id="@+id/image"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_margin="10dp"
        android:src="@drawable/feed_approve_todo"/>

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/approver_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="xxx"
        android:textColor="@color/color_333333"/>

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="："
        android:textColor="@color/color_333333"/>

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/approve_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        i18n:fstext="xt.adapter_approval_history_not_reply_item.text.no_approval"
        android:textColor="@color/color_333333"/>

</LinearLayout>