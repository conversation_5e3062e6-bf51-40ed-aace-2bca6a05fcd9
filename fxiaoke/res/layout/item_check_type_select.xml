<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="13dp"
        android:layout_marginRight="12dp"
        android:layout_marginTop="12dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
            android:layout_marginEnd="12dp">

        <ImageView
            android:id="@+id/img_item_selected"
            android:layout_width="48dp"
            android:layout_height="wrap_content"
            android:scaleType="center"
            android:src="@drawable/common_item_selected"
            android:visibility="visible" />

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/tv_item_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.0"
            android:gravity="center_vertical"
            android:textColor="#333333"
            android:textSize="14dp" />

        <ImageView
            android:id="@+id/img_next_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="6dp"
            android:background="@drawable/contact_list_arrow"
            android:visibility="gone"
                android:layout_marginStart="6dp" />
    </LinearLayout>

    <View
        android:id="@+id/img_bottom_line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="48dp"
        android:background="#eeeeee"
            android:layout_marginStart="48dp" />

</LinearLayout>