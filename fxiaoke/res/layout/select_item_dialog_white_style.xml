<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:background="#ffffffff"
    android:gravity="center"
    android:orientation="vertical" >

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="#ffcccccc" />

    <RelativeLayout
        android:layout_width="fill_parent"
        android:layout_height="45dp"
        android:gravity="center_vertical"
        android:orientation="horizontal" >

        <com.facishare.fs.sizectrlviews.SizeControlButton
            android:id="@+id/buttoncancle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_alignParentLeft="true"
            android:background="@null"
            i18n:fstext="commonfunc.dialog_fragment_fsmail_choose_attachment_menu.text.cancel"
            android:textColor="#ff000000"
            android:textSize="15dp"
                android:layout_marginStart="16dp"
                android:layout_alignParentStart="true" />

        <com.facishare.fs.sizectrlviews.SizeControlButton
            android:id="@+id/buttonsure"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="16dp"
            android:layout_alignParentRight="true"
            android:background="@null"
            i18n:fstext="av.common.string.confirm"
            android:textColor="#ff000000"
            android:textSize="15dp"
                android:layout_marginEnd="16dp"
                android:layout_alignParentEnd="true" />
    </RelativeLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="#ffcccccc" />
    
    <RelativeLayout 
	    android:layout_width="fill_parent"
	    android:layout_height="wrap_content"
	    android:background="#ffffffff"
	    android:orientation="horizontal"
	    android:padding="0dp" >
	
	    <LinearLayout
	        android:layout_width="fill_parent"
	        android:layout_height="wrap_content"
	        android:gravity="center"
	        android:orientation="horizontal"
	        android:padding="0dp" >
	        
	        <com.fxiaoke.cmviews.wheels.WheelWhiteStyleView
                    android:id="@+id/items"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content" />

	    </LinearLayout>
	
	    <RelativeLayout
	        android:layout_width="fill_parent"
	        android:layout_height="@dimen/wheel_white_default_item_height"
	        android:layout_centerVertical="true" >
	
	        <View
	            android:layout_width="fill_parent"
	            android:layout_height="1px"
	            android:layout_alignParentTop="true"
	            android:background="#ffcccccc" />
	
	        <View
	            android:layout_width="fill_parent"
	            android:layout_height="1px"
	            android:layout_alignParentBottom="true"
	            android:background="#ffcccccc" />
	    </RelativeLayout>
	
	</RelativeLayout>

</LinearLayout>