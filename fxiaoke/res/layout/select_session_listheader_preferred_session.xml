<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
        android:id="@+id/preferred_session_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/list_item_select"
        android:orientation="vertical"
>
    <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#cdcdcd"
    />

    <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:layout_width="fill_parent"
            android:layout_height="22dp"
            i18n:fstext="xt.select_session_listheader_preferred_session.text.recommend_talk"
            android:textSize="13dp"
            android:textColor="#7b7b80"
            android:background="#f0f0f6"
            android:gravity="center_vertical"
            android:paddingLeft="10dp"
            android:paddingStart="10dp" />
    <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#cdcdcd"
    />
    <ListView
            android:id="@+id/preferred_session_list_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:divider="#cdcdcd"
            android:dividerHeight="1px"/>
    <!--<RelativeLayout-->
    <!--android:layout_width="match_parent"-->
    <!--android:layout_height="72dp"-->
    <!--android:orientation="horizontal"-->
    <!--android:descendantFocusability="blocksDescendants"-->
    <!--android:padding="8dp">-->
    <!--<RelativeLayout-->
    <!--android:id="@+id/imgHead_content"-->
    <!--android:layout_width="40dp"-->
    <!--android:layout_height="40dp"-->
    <!--android:layout_toRightOf="@+id/cboDepartmentSelect"-->
    <!--android:layout_centerVertical="true">-->

    <!--<ImageView-->
    <!--android:id="@+id/imgHead"-->
    <!--android:layout_width="40dp"-->
    <!--android:layout_height="40dp"-->
    <!--/>-->

    <!--</RelativeLayout>-->

    <!--<com.facishare.fs.sizectrlviews.SizeControlTextView-->
    <!--android:id="@+id/txtSessionName"-->
    <!--android:layout_width="wrap_content"-->
    <!--android:layout_height="wrap_content"-->
    <!--android:layout_centerVertical="true"-->
    <!--android:layout_marginLeft="10dp"-->
    <!--android:layout_toRightOf="@+id/imgHead_content"-->
    <!--android:ellipsize="end"-->
    <!--android:maxWidth="200dp"-->
    <!--android:singleLine="true"-->
    <!--android:text="sdfsd"-->
    <!--android:textColor="#3d3d3d"-->
    <!--android:textSize="18dp"/>-->

    <!--<com.facishare.fs.sizectrlviews.SizeControlTextView-->
    <!--android:id="@+id/txtSessionName_count"-->
    <!--android:layout_width="wrap_content"-->
    <!--android:layout_height="wrap_content"-->
    <!--android:layout_centerVertical="true"-->
    <!--android:layout_toRightOf="@+id/txtSessionName"-->
    <!--android:ellipsize="end"-->
    <!--android:singleLine="true"-->
    <!--android:text=""-->
    <!--android:textColor="#888888"-->
    <!--android:textSize="16dp"/>-->

    <!--<TextView-->
    <!--android:id="@+id/session_label_des"-->
    <!--style="@style/session_label_text_style"-->
    <!--android:layout_toRightOf="@id/txtSessionName_count"/>-->
    <!--</RelativeLayout>-->
</LinearLayout>
