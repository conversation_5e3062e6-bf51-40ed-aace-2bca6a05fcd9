<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent">


    <com.facishare.fs.views.ViewPager
        android:id="@+id/vp_home_banner"
        android:layout_width="match_parent"
        android:layout_height="120dp">

    </com.facishare.fs.views.ViewPager>
    <LinearLayout
        android:id="@+id/llyt_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_alignBottom="@+id/vp_home_banner"
        android:paddingBottom="6dp"
        android:paddingTop="6dp"
        android:gravity="center"
        >

        <ImageView
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:src="@drawable/new_function_home_banner_hole_nor_bg"
            />
        <ImageView
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:layout_marginLeft="4dp"
            android:src="@drawable/new_function_home_banner_hole_solid_bg"
                android:layout_marginStart="4dp" />

    </LinearLayout>
</RelativeLayout>