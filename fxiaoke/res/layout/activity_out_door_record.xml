<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="48dp"/>
    <com.fxiaoke.fscommon_res.view.calendar.FsCalendarLayout
        android:id="@+id/calendar"
        app:multiChoose="false"
        app:enableCollapse="true"
        app:collapseMode="middle"
        app:calendarMode="week"
        app:itemHeight="45dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:id="@+id/list_frag"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <RelativeLayout android:layout_width="match_parent" android:layout_height="0dp"
                android:layout_weight="1"
                android:background="#f2f2f2">
                <LinearLayout android:id="@+id/empty_view" android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true" android:orientation="vertical" android:visibility="gone">
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:scaleType="centerInside"
                        android:src="@drawable/nocontent_background"/>
                    <com.facishare.fs.sizectrlviews.SizeControlTextView
                        android:id="@+id/empty_textview"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:gravity="center"
                        android:textColor="@color/l_text_color_gray"
                        android:textSize="14dp"
                        i18n:fstext="th.base.view.content_null"/>
                </LinearLayout>
                <com.fxiaoke.cmviews.xlistview.XListView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#00000000"
                    android:divider="#00000000"
                    android:id="@+id/outdoor_list"
                    android:dividerHeight="12dp"/>
            </RelativeLayout>
        </LinearLayout>
    </com.fxiaoke.fscommon_res.view.calendar.FsCalendarLayout>
</LinearLayout>
