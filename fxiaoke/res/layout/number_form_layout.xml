<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:background="#fafafa"
        android:layout_marginRight="@dimen/margin_10"
            android:layout_marginEnd="@dimen/margin_10">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">


            <ImageView
                android:id="@+id/ImageView_productImage"
                android:layout_width="42dp"
                android:layout_height="42dp"
                android:layout_marginTop="@dimen/margin_8"
                android:layout_marginLeft="@dimen/margin_8"
                android:src="@drawable/loadingcover_new"
                    android:layout_marginStart="@dimen/margin_8" />

        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginRight="@dimen/margin_10"
            android:layout_marginLeft="@dimen/margin_4"
            android:layout_marginBottom="@dimen/margin_10"
                android:layout_marginEnd="@dimen/margin_10"
                android:layout_marginStart="@dimen/margin_4">
            <TextView
                android:id="@+id/tv_productName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textColor="#545861"
                android:layout_marginTop="@dimen/margin_4"
                android:layout_marginLeft="@dimen/margin_4"
                android:text="ssssssssss"
                    android:layout_marginStart="@dimen/margin_4" />
            <LinearLayout
                android:id="@+id/image_values_layout_root"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"></LinearLayout>
            <TextView
                android:id="@+id/tv_FieldItem"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="#545861"
                android:layout_marginLeft="@dimen/margin_4"
                android:text=""
                    android:layout_marginStart="@dimen/margin_4" />

        </LinearLayout>
    </LinearLayout>
</LinearLayout>
