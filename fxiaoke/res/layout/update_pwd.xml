<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@color/white"
    android:orientation="vertical" >

    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
        android:id="@+id/title"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/title_height" />

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginLeft="18dp"
        android:layout_marginRight="18dp"
        android:orientation="vertical"
        android:paddingTop="53dp"
            android:layout_marginEnd="18dp"
            android:layout_marginStart="18dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:background="@drawable/bottom_line_item_bg_default" >

            <com.facishare.fs.views.LimitSpaceEditText
                android:id="@+id/et_src_password"
                style="@style/edit_text_common_style"
                android:ems="10"
                i18n:fshint="xt.update_pwd.text.in_origin_pwd"
                android:inputType="textPassword"
                android:maxLength="20"
                android:password="true"
                android:singleLine="true"
                android:textSize="16sp"
                android:textColorHint="#cccccc">
            </com.facishare.fs.views.LimitSpaceEditText>

            <CheckBox
                android:id="@+id/checkbox_et_src_password"
                android:layout_alignBaseline="@+id/et_src_password"
                android:layout_alignBottom="@+id/et_src_password"
                android:layout_alignParentRight="true"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="5dp"
                style="@style/cbo_password_hide_style"
                    android:layout_marginEnd="5dp"
                    android:layout_alignParentEnd="true"
                    android:layout_marginStart="10dp" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:background="@drawable/bottom_line_item_bg_default" >

            <com.facishare.fs.views.LimitSpaceEditText
                android:id="@+id/et_new_password"
                style="@style/edit_text_common_style"
                android:ems="10"
                i18n:fshint="xt.login_new.text.enter_password"
                android:inputType="textPassword"
                android:maxLength="20"
                android:password="true"
                android:singleLine="true"
                android:textSize="16sp"
                android:textColorHint="#cccccc">
            </com.facishare.fs.views.LimitSpaceEditText>

            <CheckBox
                android:id="@+id/checkbox_et_new_password"
                android:layout_alignBaseline="@+id/et_new_password"
                android:layout_alignBottom="@+id/et_new_password"
                android:layout_alignParentRight="true"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="5dp"
                style="@style/cbo_password_hide_style"
                    android:layout_marginStart="10dp"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:background="@drawable/bottom_line_item_bg_default" >

            <com.facishare.fs.views.LimitSpaceEditText
                android:id="@+id/et_new_password_2"
                style="@style/edit_text_common_style"
                android:ems="10"
                i18n:fshint="xt.update_pwd.text.re_in_pwd"
                android:inputType="textPassword"
                android:maxLength="20"
                android:password="true"
                android:singleLine="true"
                android:textSize="16sp"
                android:textColorHint="#cccccc">
            </com.facishare.fs.views.LimitSpaceEditText>

            <CheckBox
                android:id="@+id/checkbox_et_new_password_2"
                android:layout_alignBaseline="@+id/et_new_password_2"
                android:layout_alignBottom="@+id/et_new_password_2"
                android:layout_alignParentRight="true"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="5dp"
                style="@style/cbo_password_hide_style"
                    android:layout_marginStart="10dp"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="55dp"
             >

            <com.facishare.fs.account_system.passwordverify.LevelShowView
                android:id="@+id/password_level_update"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >
            </com.facishare.fs.account_system.passwordverify.LevelShowView>

        </RelativeLayout>

        <!-- 提 交 -->
        <com.facishare.fs.sizectrlviews.SizeControlButton
            android:id="@+id/btn_update_pwd"
            style="@style/button_orange_style"
            android:layout_marginTop="25dip"
            android:gravity="center"
            i18n:fstext="xt.compulsory_password.text.submit" />

        <LinearLayout
            android:id="@+id/phone_number_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="19.5dp"
            android:layout_gravity="center_horizontal"
            >

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="#999999"
                android:layout_marginRight="3dp"
                i18n:fstext="xt.compulsory_password.text.has_a_problem_inquiry"
                    android:layout_marginEnd="3dp" />
            <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/phone_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="#fcb058"
                android:text="@string/custom_service_phone"/>

        </LinearLayout>
    </LinearLayout>

</LinearLayout>