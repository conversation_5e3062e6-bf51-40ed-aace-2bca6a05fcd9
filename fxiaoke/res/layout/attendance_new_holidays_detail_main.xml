<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#ffffffff">

    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
        android:id="@+id/title"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/title_height"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/scroller">

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_marginTop="32dp"
                android:layout_gravity="center_horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="8dp"
                    android:scaleType="fitXY"
                    android:background="@drawable/rule_holidays_title_icon"
                        android:layout_marginEnd="8dp" />

                <com.facishare.fs.sizectrlviews.SizeControlTextView
                    android:id="@+id/holiday_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:singleLine="true"
                    android:textColor="#ffafbfe0"
                    android:textSize="16sp" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="8dp"
                    android:scaleType="fitXY"
                    android:rotation="180"
                    android:background="@drawable/rule_holidays_title_icon"
                        android:layout_marginStart="8dp" />

            </LinearLayout>

            <com.facishare.fs.biz_function.subbiz_attendance_new.view.DynamicList
                android:id="@+id/list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </LinearLayout>

    </ScrollView>


</LinearLayout>
