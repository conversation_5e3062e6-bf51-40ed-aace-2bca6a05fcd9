<?xml version="1.0" encoding="utf-8"?>
<!-- 纷享网盘列表视图，从左到右为 图标、名称、文件夹数、文件数 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical" >

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="72dp"
        android:background="@drawable/invitation_no_semicircle_selector"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="15dp"
        android:paddingRight="30dp"
            android:paddingStart="15dp"
            android:paddingEnd="30dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="72dp"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:orientation="horizontal" >

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="12dip"
                    android:layout_marginEnd="12dip">

                <ImageView
                    android:id="@+id/iv_permission"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:scaleType="centerCrop"
                    android:src="@drawable/fsnetdisk_folder_company" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" >

                <com.facishare.fs.sizectrlviews.SizeControlTextView
                    android:id="@+id/textView_name"
                    style="@style/fsnetdisk_main_item"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_marginTop="16dp"
                    android:ellipsize="end"
                    android:singleLine="true"
                    i18n:fstext="xt.function_fsnetdisk_permission_list_item.text.advanced_permissions" />

                <com.facishare.fs.sizectrlviews.SizeControlTextView
                    android:id="@+id/textView_desc"
                    style="@style/fsnetdisk_secondry_item"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/textView_name"
                    android:layout_marginTop="5dp"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text="111222" />
            </RelativeLayout>
        </LinearLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/bottom_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/bottom_line_height"
        android:layout_marginLeft="52dp"
        android:background="@drawable/slice_line_1px"
        android:visibility="visible"
            android:layout_marginStart="52dp" />

</LinearLayout>