<?xml version="1.0" encoding="utf-8"?>
<com.fxiaoke.fscommon_res.views.ResizeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                             android:layout_width="match_parent"
                                             android:layout_height="match_parent"
                                             android:background="#ededef"
                                             android:orientation="vertical">

    <com.fxiaoke.fscommon_res.common_view.CommonTitleView xmlns:android="http://schemas.android.com/apk/res/android"
                                                          android:id="@+id/title"
                                                          android:layout_width="fill_parent"
                                                          android:layout_height="@dimen/title_height" />

    <com.fxiaoke.cmviews.viewpager.ViewPagerCtrl
        android:id="@+id/pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/bg_default"
        android:visibility="visible" />

</com.fxiaoke.fscommon_res.views.ResizeLayout>