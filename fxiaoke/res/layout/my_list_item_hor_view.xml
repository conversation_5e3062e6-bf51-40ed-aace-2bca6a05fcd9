<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/LinearLayout_h_view"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    >
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/fake_white"
        android:gravity="center_vertical"
        >
        <ImageView
            android:id="@+id/image_view_icon"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@drawable/my_list_tx_icon"
            android:layout_centerVertical="true"
            android:layout_marginLeft="12dp"
            android:layout_marginStart="12dp" />
        <RelativeLayout
                android:id="@+id/textView_title_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginStart="12dp"
                android:layout_toRightOf="@+id/image_view_icon"
                android:layout_toEndOf="@+id/image_view_icon"
                android:layout_centerVertical="true"
                android:gravity="center">

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                    android:id="@+id/textView_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text=""
                    android:textSize="16dp"
                    android:textColor="#181C25"/>
            <ImageView
                    android:id="@+id/my_RemindIcon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@id/textView_title"
                    android:layout_toEndOf="@+id/textView_title"
                    android:scaleType="centerInside"
                    android:visibility="gone"
                    android:src="@drawable/dr_tab_newnotify"/>
        </RelativeLayout>

        <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/textView_count_tx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:layout_toLeftOf="@+id/imageView_arrow_m"
                android:layout_toRightOf="@id/textView_title_layout"
                android:ellipsize="end"
                android:singleLine="true"                
                android:textColor="#999999"
                android:textSize="16dp"
                android:gravity="end"
                android:paddingLeft="8dp"
                android:visibility="gone"
                android:paddingStart="8dp"
                android:layout_marginEnd="10dp"
                android:layout_toStartOf="@+id/imageView_arrow_m"
                android:layout_toEndOf="@id/textView_title_layout" />
        <TextView
                android:id="@+id/textView_notify"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="6dp"
                android:paddingRight="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginRight="6dp"
                android:textSize="10sp"
                android:gravity="center"
                android:textColor="#ffffff"
                android:background="@drawable/bg_app_new_version_red_remind"
                android:layout_toLeftOf="@id/imageView_arrow_m"
                android:layout_centerVertical="true"
                android:visibility="gone"
                android:paddingStart="6dp"
                android:layout_toStartOf="@id/imageView_arrow_m"
                android:layout_marginEnd="6dp"
                android:paddingEnd="6dp" />
        <TextView
            android:id="@+id/textView_notify_white_bg_red_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="6dp"
            android:paddingRight="6dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:layout_marginRight="6dp"
            android:textSize="10sp"
            android:gravity="center"
            android:textColor="#F45A22"
            android:background="@drawable/bg_app_white_bg_red_text_remind"
            android:layout_toLeftOf="@id/imageView_arrow_m"
            android:layout_centerVertical="true"
            android:visibility="gone"
            android:paddingStart="6dp"
            android:layout_toStartOf="@id/imageView_arrow_m"
            android:paddingEnd="6dp"
            android:layout_marginEnd="6dp" />
        <ImageView
            android:id="@+id/imageView_arrow_m"
            android:layout_width="7dp"
            android:layout_height="12dp"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:src="@drawable/my_arrow_icon_bitmap"
            android:layout_marginRight="12dp"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="12dp" />
    </RelativeLayout>

    <View
            android:id="@+id/TextView_line_my"
            style="@style/list_item_divider_line_bg"
            android:layout_marginLeft="42dp"
            android:background="#DEE1E8"
            android:layout_marginStart="42dp" />
</LinearLayout>