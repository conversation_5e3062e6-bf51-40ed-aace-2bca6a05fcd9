package com.facishare.fs.common_view.pinnedslideexpandable;

/** 
 *  
 * <AUTHOR> clw 
 */
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.Scroller;

/**
 * @blog http://blog.csdn.net/xiaanming
 * 
 * <AUTHOR>
 * 
 */
public class SlideCutListView extends PinnedSectionListView {

	private int slidePosition;

	private int downY;

	private int downX;
	
	private int screenWidth;

	private ViewGroup itemView;
	
	private View itemContextChildView;
	private View itemActionChildView;
	
	
	private Scroller scroller;
	private static final int SNAP_VELOCITY = 600;

	private VelocityTracker velocityTracker;

	private boolean isSlide = false;

	private int mTouchSlop;

	private RemoveListener mRemoveListener;

	private RemoveDirection removeDirection;
	
	public enum RemoveDirection {
		RIGHT, LEFT;
	}

	public SlideCutListView(Context context) {
		this(context, null);
	}

	public SlideCutListView(Context context, AttributeSet attrs) {
		this(context, attrs, 0);
	}

	public SlideCutListView(Context context, AttributeSet attrs, int defStyle) {
		super(context, attrs, defStyle);
		screenWidth = ((WindowManager) context
				.getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay()
				.getWidth();
		scroller = new Scroller(context);
		mTouchSlop = ViewConfiguration.get(getContext()).getScaledTouchSlop();
	}

	public void setRemoveListener(RemoveListener removeListener) {
		this.mRemoveListener = removeListener;
	}

	@Override
	public boolean dispatchTouchEvent(MotionEvent event) {
		switch (event.getAction()) {
		case MotionEvent.ACTION_DOWN: {
			addVelocityTracker(event);
		
			if (!scroller.isFinished()) {
				return super.dispatchTouchEvent(event);
			}
			downX = (int) event.getX();
			downY = (int) event.getY();

			slidePosition = pointToPosition(downX, downY);
		
			if (slidePosition == AdapterView.INVALID_POSITION) {
				return super.dispatchTouchEvent(event);
			}
		
			itemView = (ViewGroup)getChildAt(slidePosition - getFirstVisiblePosition());
			
/*			itemContextChildView = ((ViewGroup)itemView.getChildAt(0)).getChildAt(0);
			itemActionChildView = ((ViewGroup)itemView.getChildAt(0)).getChildAt(1);
			if(itemActionChildView.getVisibility() == View.GONE){
				itemActionChildView.setVisibility(View.VISIBLE);
			}*/
			
			break;
		}
		case MotionEvent.ACTION_MOVE: {
			if (Math.abs(getScrollVelocity()) > SNAP_VELOCITY
					|| (Math.abs(event.getX() - downX) > mTouchSlop && Math
							.abs(event.getY() - downY) < mTouchSlop)) {
				isSlide = true;
			}
			break;
		}
		case MotionEvent.ACTION_UP:
			recycleVelocityTracker();
			break;
		}

		return super.dispatchTouchEvent(event);
	}

	private void scrollRight() {
		removeDirection = RemoveDirection.RIGHT;
		final int delta = (screenWidth + itemView.getScrollX());
		
		scroller.startScroll(itemView.getScrollX(), 0, -delta, 0,
				Math.abs(delta));

		postInvalidate(); 
	}

	private void scrollLeft() {
		removeDirection = RemoveDirection.LEFT;
		final int delta = (screenWidth - itemView.getScrollX());	
		scroller.startScroll(itemView.getScrollX(), 0, delta, 0,
				Math.abs(delta));
	/*	itemActionChildView.offsetLeftAndRight(delta);*/
		requestLayout();
		postInvalidate(); 
	}

	private void scrollByDistanceX() {	
		if (itemView.getScrollX() >= screenWidth / 3) {
			scrollLeft();
		} else if (itemView.getScrollX() <= -screenWidth / 3) {
			scrollRight();
		} else {		
			itemView.scrollTo(0, 0);
		}

	}

	@Override
	public boolean onTouchEvent(MotionEvent ev) {
		if (isSlide && slidePosition != AdapterView.INVALID_POSITION) {
			addVelocityTracker(ev);
			final int action = ev.getAction();
			int x = (int) ev.getX();
			switch (action) {
			case MotionEvent.ACTION_MOVE:
				int deltaX = downX - x;
				downX = x;
			
				itemView.scrollBy(deltaX, 0);
				break;
			case MotionEvent.ACTION_UP:
				int velocityX = getScrollVelocity();
				if (velocityX > SNAP_VELOCITY) {
					scrollRight();
				} else if (velocityX < -SNAP_VELOCITY) {
					scrollLeft();
				} else {
					scrollByDistanceX();
				}
				recycleVelocityTracker();
			
				isSlide = false;
				break;
			}
			return true; 
		}

		return super.onTouchEvent(ev);
	}

	@Override
	public void computeScroll() {	
		if (scroller.computeScrollOffset()) {		
			itemView.scrollTo(scroller.getCurrX(), scroller.getCurrY());
			requestLayout();
			postInvalidate();
			if (scroller.isFinished()) {
		/*		if (mRemoveListener == null) {
					throw new NullPointerException(
							"RemoveListener is null, we should called setRemoveListener()");
				}
*/
				itemView.scrollTo(0, 0);
			/*	mRemoveListener.removeItem(removeDirection, slidePosition);*/
			}
		}
	}

	private void addVelocityTracker(MotionEvent event) {
		if (velocityTracker == null) {
			velocityTracker = VelocityTracker.obtain();
		}

		velocityTracker.addMovement(event);
	}

	private void recycleVelocityTracker() {
		if (velocityTracker != null) {
			velocityTracker.recycle();
			velocityTracker = null;
		}
	}

	private int getScrollVelocity() {
		velocityTracker.computeCurrentVelocity(1000);
		int velocity = (int) velocityTracker.getXVelocity();
		return velocity;
	}

	public interface RemoveListener {
		public void removeItem(RemoveDirection direction, int position);
	}

}
