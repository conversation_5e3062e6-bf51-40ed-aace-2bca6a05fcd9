package com.facishare.fs.biz_session_msg;

import com.facishare.fs.i18n.I18NHelper;
import java.io.File;

import android.os.Bundle;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.facishare.fs.BaseActivity;
import com.facishare.fslib.R;
import com.facishare.fs.dialogs.ComDialog;
import com.facishare.fs.utils_fs.ToastUtils;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.stat_engine.StatEngine;
import com.fxiaoke.stat_engine.beans.UploadParam;
import com.fxiaoke.stat_engine.callback.OnUploadListener;
import com.lidroid.xutils.util.FSNetUtils;

public class UploadLogFilesActivity extends BaseActivity{
	
	private ImageView mUploadImg = null;
	private TextView mUploadTipText1 = null;
	private TextView mUploadTipText2 = null;
	private Button mUploadBtn = null;
	
	@Override
	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);	
		initView();
		initViewListener();
	}
	
	private void initView(){
		setContentView(R.layout.activity_upload_log_files);
		initTitle();		

		mUploadBtn = (Button) findViewById(R.id.upload_btn);
		mUploadTipText1 = (TextView) findViewById(R.id.upload_tip_text1);
		mUploadTipText2 = (TextView) findViewById(R.id.upload_tip_text2);
		mUploadImg = (ImageView)findViewById(R.id.upload_img);
	}
	
	private void initTitle() {
		initTitleCommon();
		setTitleName();
	}
	
	private void setTitleName() {
		if (mCommonTitleView != null && context != null) {
		     mCommonTitleView.setMiddleText(I18NHelper.getText("xt.about_main.text.up_plan")/* 上传错误日志 */);
		}
	}
	
	private void initViewListener(){
		if (mCommonTitleView != null) {
			mCommonTitleView.addLeftAction(R.string.return_before_new_normal,
					new View.OnClickListener() {
						@Override
						public void onClick(View v) {
							close();
						}
					});
		}
		
		mUploadBtn.setOnClickListener(new OnClickListener() {
			@Override
			public void onClick(View v) {			
				switch (FSNetUtils.getAPNType()) {
				case FSNetUtils.NONET:
					ToastUtils.show(I18NHelper.getText("xt.fs.GroupMsgReceiverListActivity.1")/* 没有网络，请联网再试 */,Toast.LENGTH_SHORT);
					break;
				case FSNetUtils.MOBILE:
					ComDialog.showConfirmDialog(context,I18NHelper.getText("xt.fs.GroupMsgReceiverListActivity.1")/* 非wifi网络下，会浪费流量，请确定是否上传 */,true,new OnClickListener(){
						@Override
						public void onClick(View v) {							
							showProgressBarAndUploadLogFiles();
						}						
					});
					break;
				default:
					showProgressBarAndUploadLogFiles();	
					break;
				}														
			}
		});		
	}
	
	private void showProgressBarAndUploadLogFiles(){			
		showDialog(DIALOG_WAITING_SENDING);
        UploadParam param = new UploadParam(I18NHelper.getText("qx.upload_logs.default.title")/* 上传日志界面 */, "UploadLogUi");
        param.setFilePath(new File(FCLog.getLogPath()));
        StatEngine.uploadFile(param, new OnUploadListener() {
            @Override
            public void doSuccessed() {
				runOnUiThread(new Runnable() {
					@Override
					public void run() {
						removeDialog(DIALOG_WAITING_SENDING);
						mUploadImg.setImageResource(R.drawable.upload_success_log_files);
						mUploadTipText1.setText(I18NHelper.getText("xt.upload_logs.result.success")/* 日志上传成功 */);
						mUploadTipText2.setVisibility(View.GONE);
						mUploadBtn.setVisibility(View.GONE);
					}
				});

            }

            @Override
            public void doFailed(String error) {
                removeDialog(DIALOG_WAITING_SENDING);
                ToastUtils.show(I18NHelper.getText("qx.upload_logs.guide.upload_failed")/* 上传失败： */+error);
            }
        });
	}
}
