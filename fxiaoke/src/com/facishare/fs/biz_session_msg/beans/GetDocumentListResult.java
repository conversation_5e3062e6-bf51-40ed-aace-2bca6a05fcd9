/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.beans;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * Created by yangwg on 2016/9/7.
 */
public class GetDocumentListResult implements Serializable {
//    repeated DocumentInfo documentInfoList = 10;
//    optional int64 lastMsgId = 11;
    /**
     * 消息Id
     */
    @JSONField(name="M10")
    public List<DocumentInfo> documentInfoList;
    /**
     * 消息Id
     */
    @JSONField(name="M11")
    public long lastMsgId;
    @JSONCreator
    public GetDocumentListResult(@JSONField(name="M10") List<DocumentInfo> documentInfoList,
                        @JSONField(name="M11") long lastMsgId) {
        this.documentInfoList = documentInfoList;
        this.lastMsgId = lastMsgId;
    }
}
