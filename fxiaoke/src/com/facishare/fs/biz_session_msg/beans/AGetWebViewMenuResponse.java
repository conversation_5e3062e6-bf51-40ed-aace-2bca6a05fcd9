package com.facishare.fs.biz_session_msg.beans;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

/** 
 *  
 * <AUTHOR> clw 
 */
public class AGetWebViewMenuResponse {
	/**
	 * 菜单集合上排
	 */
	@JSONField(name="a1")
	public final List<AWebViewMenuEntity> menuCollectionUp;
	/**
	 * 菜单集合下排
	 */
	@JSONField(name="a2")
	public final List<AWebViewMenuEntity> menuCollectionDown;
 
	@JSONCreator
	public AGetWebViewMenuResponse(@JSONField(name="a1") List<AWebViewMenuEntity> menuCollectionUp,
		@JSONField(name="a2") List<AWebViewMenuEntity> menuCollectionDown) {
		this.menuCollectionUp = menuCollectionUp;
		this.menuCollectionDown = menuCollectionDown;
	}

	
	public static class AWebViewMenuEntity {
		/**
		 * 菜单
		 */
		@JSONField(name="a")
		public final int menuID;
	 
		@JSONCreator
		public AWebViewMenuEntity(@JSONField(name="a") int menuID) {
			this.menuID = menuID;
		}
	}

	
}
