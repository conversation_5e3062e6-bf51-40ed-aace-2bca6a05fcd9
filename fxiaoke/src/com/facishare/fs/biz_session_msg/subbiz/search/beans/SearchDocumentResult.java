package com.facishare.fs.biz_session_msg.subbiz.search.beans;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * Created by pangc on 2017/2/21.
 */
public class SearchDocumentResult implements Serializable {

    @JSONField(name="M1")
    public long totalCount;
    @JSONField(name="M2")
    public List<DocumentItem> docMessages;

    public SearchDocumentResult() {
    }

    public SearchDocumentResult
            (@JSONField(name="M1") long totalCount,
             @JSONField(name="M2") List<DocumentItem> docMessages) {
        this.totalCount = totalCount;
        this.docMessages = docMessages;
    }
}
