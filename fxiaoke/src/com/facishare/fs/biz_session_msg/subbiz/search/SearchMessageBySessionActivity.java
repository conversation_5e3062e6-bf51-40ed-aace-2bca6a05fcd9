package com.facishare.fs.biz_session_msg.subbiz.search;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.widget.AdapterView;
import android.widget.TextView;

import com.alibaba.fastjson.TypeReference;
import com.facishare.fs.BaseActivity;
import com.facishare.fs.biz_session_msg.SessionMsgActivity;
import com.facishare.fs.biz_session_msg.constants.SessionConstants;
import com.facishare.fs.biz_session_msg.subbiz.search.adapter.SearchMessageBySessionAdapter;
import com.facishare.fs.biz_session_msg.subbiz.search.beans.message.SearchMessageBySessionIdResult;
import com.facishare.fs.biz_session_msg.subbiz.search.beans.message.SingleMessageItem;
import com.facishare.fs.biz_session_msg.subbiz.search.utils.MessageSearchUtils;
import com.facishare.fs.biz_session_msg.utils.MsgUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.facishare.fs.utils_fs.EmptyViewUtils;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fslib.R;
import com.fxiaoke.cmviews.xlistview.XListView;
import com.fxiaoke.fscommon.util.FsUrlUtils;
import com.fxiaoke.fscommon_res.view.FCSearchBar;
import com.fxiaoke.fscommon_res.view.FCSearchListener;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.lib.qixin.biz_ctrl.SessionCommonUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * Created by pangc on 2018/11/29.
 */
public class SearchMessageBySessionActivity extends BaseActivity implements XListView.IXListViewListener {

    private TextView mChatResultHeadView;
    private FCSearchBar mSearchView;
    private XListView mXListView;
    private View mEmptyView;

    private SearchMessageBySessionAdapter mAdapter;
    private List<SingleMessageItem> mMessageItemList = new ArrayList<>();

    private boolean mShowSearchBar;
    private int mEnv;
    private SessionListRec mSessionInfo;
    private String mSessionId;
    private String mLastKeyWord = "";
    private boolean isSearching = false;
    private boolean needClearData = false;

    /**
     * 响应搜索操作
     */
    private final int HANDLER_TYPE_SEARCH = 20;

    Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            if (msg.what == HANDLER_TYPE_SEARCH) {
                if (isFinishing()) {
                    return;
                }
                String searchKey = mSearchView.getText().trim();
                String lastString = (String) msg.obj;
                if (!searchKey.equals(lastString)) {
                    return;
                }
                if (TextUtils.isEmpty(searchKey)) {
                    clearData();
                } else {
                    searchMessage(true);
                }
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_search_message_single_session);
        initData(savedInstanceState);
        if (mSessionInfo == null && !TextUtils.isEmpty(mSessionId)) {
            mSessionInfo = SessionCommonUtils.getSessionInAllSource(mSessionId);
        }
        if (mSessionInfo == null) {
            ToastUtils.show(I18NHelper.getText("qx.session.text.tip_no_session")/* 会话不能为空 */);
            finish();
            return;
        }
        initView();

        if (mShowSearchBar) {
            showDefaultEmptyView();
        } else {
            mXListView.startRefresh();
        }
    }

    @Override
    protected boolean enableHugeIntentStartAct(Intent intent) {
        return MetaDataUtils.canEnableHugeIntentStartAct(intent);
    }

    private static final String ARG_ENV = "arg_env";
    private static final String ARG_KEYWORD = "arg_keyword";
    private static final String ARG_SHOW_SEARCH_BAR = "arg_show_search_bar";

    public static Intent getIntent(Context context, SessionListRec sessionInfo, String keyword, boolean showSearchBar) {
        Intent intent = new Intent(context, SearchMessageBySessionActivity.class);
        intent.putExtra(SessionConstants.INTENT_KEY_SESSION_INFO, sessionInfo);
        intent.putExtra(ARG_ENV, sessionInfo.getEnterpriseEnvType());
        intent.putExtra(ARG_KEYWORD, keyword);
        intent.putExtra(ARG_SHOW_SEARCH_BAR, showSearchBar);
        return intent;
    }

    private static final int PAGE_SIZE = 20;

    private void initData(Bundle saveInstanceState) {
        if (saveInstanceState == null) {
            Intent intent = getIntent();
            mEnv = intent.getIntExtra(ARG_ENV, 0);
            mLastKeyWord = intent.getStringExtra(ARG_KEYWORD);
            mSessionInfo = (SessionListRec) intent.getSerializableExtra(SessionConstants.INTENT_KEY_SESSION_INFO);
            mShowSearchBar = intent.getBooleanExtra(ARG_SHOW_SEARCH_BAR, false);
        } else {
            mEnv = saveInstanceState.getInt(ARG_ENV, 0);
            mLastKeyWord = saveInstanceState.getString(ARG_KEYWORD);
            mSessionInfo = (SessionListRec) saveInstanceState.getSerializable(SessionConstants.INTENT_KEY_SESSION_INFO);
            mSessionId = saveInstanceState.getString(SessionConstants.INTENT_KEY_SESSION_ID);
            mShowSearchBar = saveInstanceState.getBoolean(ARG_SHOW_SEARCH_BAR, false);
        }
    }

    @Override
    public void onSafeSaveInstanceState(@NonNull Bundle outState) {
        super.onSafeSaveInstanceState(outState);
        outState.putInt(ARG_ENV, mEnv);
        outState.putString(ARG_KEYWORD, mLastKeyWord);
        outState.putSerializable(SessionConstants.INTENT_KEY_SESSION_INFO, mSessionInfo);
        outState.putBoolean(ARG_SHOW_SEARCH_BAR, mShowSearchBar);
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putInt(ARG_ENV, mEnv);
        outState.putString(ARG_KEYWORD, mLastKeyWord);
        outState.putString(SessionConstants.INTENT_KEY_SESSION_ID, mSessionInfo.getSessionId());
        outState.putBoolean(ARG_SHOW_SEARCH_BAR, mShowSearchBar);
    }

    private void initView() {
        initTitleCommon();
        mCommonTitleView.setMiddleText(MsgUtils.getSessionDisplayName(context, mSessionInfo));
        mCommonTitleView.addLeftAction(R.string.return_before_new_normal, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                close();
            }
        });

        mChatResultHeadView = findViewById(R.id.chat_result_tv);

        mSearchView = (FCSearchBar) findViewById(R.id.search_bar);
        mSearchView.setFCSearchListener(new FCSearchListener() {
            @Override
            public void onSearch(String key) {
                if (mSearchView.getText() == null || mSearchView.getText().length() == 0) {
                    ToastUtils.showToast(I18NHelper.getText("bi.ui.RptFormSearchAct.2113")/* 请输入搜索内容 */);
                    return;
                }
                hideInput();
                if (!isSearching) {
                    searchMessage(true);
                }
            }

            @Override
            public void onCancelClicked() {
                finish();
            }

            @Override
            public void onContentChanged(CharSequence sequence) {
                mLastKeyWord = mSearchView.getText().trim();

                mHandler.removeMessages(HANDLER_TYPE_SEARCH);
                Message msg = Message.obtain(mHandler, HANDLER_TYPE_SEARCH, mLastKeyWord);
                mHandler.sendMessageDelayed(msg, 500);
            }
        });
        if (mShowSearchBar) {
            mCommonTitleView.setVisibility(View.GONE);
            mSearchView.setVisibility(View.VISIBLE);
        } else {
            mCommonTitleView.setVisibility(View.VISIBLE);
            mSearchView.setVisibility(View.GONE);
        }
        mChatResultHeadView.setVisibility(View.GONE);

        mXListView = (XListView) findViewById(R.id.search_list);
        mXListView.setPullRefreshEnable(false);
        mXListView.setDividerHeight(0);
        mXListView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                hideInput();
                return false;
            }
        });
        mAdapter = new SearchMessageBySessionAdapter(this, mSessionInfo, mMessageItemList);
        mXListView.setAdapter(mAdapter);
        mXListView.setPullLoadEnable(false);
        mXListView.setXListViewListener(this);
        mXListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                SingleMessageItem item = (SingleMessageItem) parent.getItemAtPosition(position);
                if (item == null) {
                    ToastUtils.showToast(I18NHelper.getText("qx.single_session_search.result.cannot_located_msg")/* 无法定位消息！ */);
                    return;
                }
                Intent intent = getStartMsgActivity(mSessionInfo, item.msgId);
                startActivity(intent);
            }
        });
        initEmptyView();
    }

    private void clearData() {
        mMessageItemList.clear();
        mAdapter.updateData(mMessageItemList);
        checkShowEmptyView();
    }

    private void updateData() {
        mAdapter.updateData(mMessageItemList);
        checkShowEmptyView();
    }

    protected Intent getStartMsgActivity(SessionListRec sessionInfo, long msgId) {
//        Intent it = new Intent(this, SessionMsgActivity.class);
//        it.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        Intent it = FsUrlUtils.buildSingleTaskForChatIntent();
        it.putExtra(SessionConstants.INTENT_KEY_SESSION_INFO, sessionInfo);
        it.putExtra(SessionMsgActivity.INTENT_KEY_LOCATE_MSGID, msgId);
        return it;
    }

    private void searchMessage(boolean clearData) {
        needClearData = clearData;
        long lastMessageTime;
        if (needClearData || mMessageItemList.size() == 0) {
            lastMessageTime = 0L;
        } else {
            lastMessageTime = mMessageItemList.get(mMessageItemList.size() - 1).msgTime;
        }
        isSearching = true;
        mAdapter.setSearchKey(mLastKeyWord);
        MessageSearchUtils.searchMessageBySessionId(mEnv, mLastKeyWord, mSessionInfo.getSessionId(), lastMessageTime, PAGE_SIZE,
                new WebApiExecutionCallback<SearchMessageBySessionIdResult>() {
                    @Override
                    public TypeReference<WebApiResponse<SearchMessageBySessionIdResult>> getTypeReference() {
                        return new TypeReference<WebApiResponse<SearchMessageBySessionIdResult>>() {
                        };
                    }

                    @Override
                    public Class<SearchMessageBySessionIdResult> getTypeReferenceFHE() {
                        return SearchMessageBySessionIdResult.class;
                    }

                    @Override
                    public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                        isSearching = false;
                        ToastUtils.show(WebApiFailureType.getToastShowText(failureType, error));
                    }

                    @Override
                    public void completed(Date date, SearchMessageBySessionIdResult searchMessageBySessionIdResult) {
                        isSearching = false;
                        if (needClearData) {
                            mMessageItemList.clear();
                        }
                        if (searchMessageBySessionIdResult.messageItems == null) {
                            searchMessageBySessionIdResult.messageItems = Collections.EMPTY_LIST;
                        } else {
                            mMessageItemList.addAll(searchMessageBySessionIdResult.messageItems);
                        }
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                updateData();
                            }
                        });
                        if (searchMessageBySessionIdResult != null) {
                            if (!mShowSearchBar && needClearData) {
                                mChatResultHeadView.setText(I18NHelper.getFormatText("qx.session_search_chat.guide.total_several_related_chat_record"/* 共{0}条与“{1}”相关的聊天记录 */, searchMessageBySessionIdResult.totalCount+"", mLastKeyWord+""));
                                mChatResultHeadView.setVisibility(View.VISIBLE);
                            }
                            if (searchMessageBySessionIdResult.messageItems == null || searchMessageBySessionIdResult.messageItems.size() < PAGE_SIZE) {
                                mXListView.setOnlyPullLoadEnable(false);
                                mXListView.stopRefresh();
                                mXListView.setFootNoMore();
                            } else {
                                mXListView.onLoadSuccess(new Date());
                            }
                        }
                    }
                });
    }

    @Override
    public void onRefresh() {
        searchMessage(true);
    }

    @Override
    public void onLoadMore() {
        searchMessage(false);
    }

    public void checkShowEmptyView() {
        if (mMessageItemList.size() == 0) {
            if (TextUtils.isEmpty(mLastKeyWord)) {
                showDefaultEmptyView();
            } else {
                showNoDataEmptyView();
            }
        } else {
            hideEmptyView();
        }
    }

    private void initEmptyView() {
        mEmptyView = EmptyViewUtils.inflate(context, EmptyViewUtils.TYPE_SEARCH, I18NHelper.getText("xt.biz_session_msg.AttachFilesSearchFragment.1")/* 无匹配的搜索结果 */);
    }

    private void showDefaultEmptyView() {
        EmptyViewUtils.bindEmptyView(context, mEmptyView, EmptyViewUtils.TYPE_SEARCH, I18NHelper.getText("xt.activity_single_group_search.text.search")/* 试着搜点啥吧 */);
        EmptyViewUtils.showEmptyView(mXListView, mEmptyView);
    }

    private void showNoDataEmptyView() {
        EmptyViewUtils.bindEmptyView(context, mEmptyView, EmptyViewUtils.TYPE_SEARCH_NO_DATA, I18NHelper.getText("qx.session_search_chat.guide.no_chats")/* 没有搜索到相关聊天记录 */);
        EmptyViewUtils.showEmptyView(mXListView, mEmptyView);
    }

    private void hideEmptyView() {
        EmptyViewUtils.hideEmptyView(mXListView);
    }

}
