/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.subbiz.search.beans;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

public class SearchContactResult implements Serializable {

    public SearchContactResult() {

    }

    /**
     * 通讯录的搜索结果列表
     */
    private List<ContactItem> employees;

    @JSONField(name = "M1")
    public List<ContactItem> getEmployees() {
        return employees;
    }

    @JSONField(name = "M1")
    public void setEmployees(List<ContactItem> employees) {
        this.employees = employees;
    }

}
