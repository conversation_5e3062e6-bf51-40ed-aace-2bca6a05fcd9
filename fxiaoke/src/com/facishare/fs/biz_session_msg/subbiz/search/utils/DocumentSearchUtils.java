package com.facishare.fs.biz_session_msg.subbiz.search.utils;

import android.content.Context;
import android.content.Intent;

import com.alibaba.fastjson.TypeReference;
import com.facishare.fs.biz_session_msg.SessionMsgActivity;
import com.facishare.fs.biz_session_msg.beans.SessionChatSearchResultData;
import com.facishare.fs.biz_session_msg.constants.SessionConstants;
import com.facishare.fs.biz_session_msg.subbiz.search.beans.SearchDocumentResult;
import com.facishare.fs.biz_session_msg.utils.ErrorMsgUtils;
import com.fxiaoke.fscommon.util.FsUrlUtils;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiParameterList;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.fxiaoke.fxlog.FCLog;

import java.util.Date;

/**
 * Created by wusj on 2016/10/31.
 */
public class DocumentSearchUtils {

    private static final String QixinSearchController = "FHE/EM1HQIXINSEARCH/Searcher";
    public static void searchCrossDocument(String keyword, long lastFileMid, int count, String sessionId,
                                      final ChatSearchService.SearchCallback<SearchDocumentResult> listener) {
        WebApiExecutionCallback<SearchDocumentResult> mReqCallBack =
                new WebApiExecutionCallback<SearchDocumentResult>() {
                    @Override
                    public TypeReference<WebApiResponse<SearchDocumentResult>> getTypeReference() {
                        return new TypeReference<WebApiResponse<SearchDocumentResult>>() {
                        };
                    }

                    @Override
                    public Class<SearchDocumentResult> getTypeReferenceFHE() {
                        return SearchDocumentResult.class;
                    }

                    @Override
                    public void failed(WebApiFailureType failureType, int httpStatusCode, String error, int errorCode,
                                       int enterpriseID) {
                        if(listener != null) {
                            listener.onSearchFailed(error);
                        }
                        String errorInfo = ErrorMsgUtils.getErrorMsg("SearchDocument failed",
                                failureType, httpStatusCode, error, errorCode, enterpriseID);
                        //写入文件的log
                        FCLog.i("Search", errorInfo);
                    }

                    @Override
                    public void completed(Date date, SearchDocumentResult result) {
                        if (result != null) {
                            if (listener != null) {
                                listener.onSearchSuccess(result);
                            }
                        }
                    }
                };
        WebApiUtils.postAsync(QixinSearchController, "SearchDocument", WebApiParameterList.create()
                .with("M1", keyword)
                .with("M2", lastFileMid)
                .with("M3", count)
                .with("M4", sessionId)
                .with("M5", 1)
                , mReqCallBack);
    }
    /**
     * 联网搜索文档
     *
     * @param keyword      关键字
     * @param lastAttachId 检索开始值，默认为0
     * @param count        结果数目
     * @param listener     搜索的监听
     */
    public static void searchDocument(String keyword, long lastAttachId, int count, String sessionId,
                                      final ChatSearchService.SearchCallback<SearchDocumentResult> listener) {
        WebApiExecutionCallback<SearchDocumentResult> mReqCallBack =
                new WebApiExecutionCallback<SearchDocumentResult>() {
                    @Override
                    public TypeReference<WebApiResponse<SearchDocumentResult>> getTypeReference() {
                        return new TypeReference<WebApiResponse<SearchDocumentResult>>() {
                        };
                    }

                    @Override
                    public Class<SearchDocumentResult> getTypeReferenceFHE() {
                        return SearchDocumentResult.class;
                    }

                    @Override
                    public void failed(WebApiFailureType failureType, int httpStatusCode, String error, int errorCode,
                                       int enterpriseID) {
                        if(listener != null) {
                            listener.onSearchFailed(error);
                        }
                        String errorInfo = ErrorMsgUtils.getErrorMsg("SearchDocument failed",
                                failureType, httpStatusCode, error, errorCode, enterpriseID);
                        //写入文件的log
                        FCLog.i("Search", errorInfo);
                    }

                    @Override
                    public void completed(Date date, SearchDocumentResult result) {
                        if (result != null) {
                            if (listener != null) {
                                listener.onSearchSuccess(result);
                            }
                        }
                    }
                };
        GetDocument(sessionId, keyword, count, lastAttachId, mReqCallBack);
    }

    public static void GetDocument(String sessionId, String keyword, int count, long lastFileMid,
                                   WebApiExecutionCallback<SearchDocumentResult> callback) {
        WebApiUtils.postAsync(QixinSearchController, "SearchDocument", WebApiParameterList.create()
                .with("M1", keyword)
                .with("M2", lastFileMid)
                .with("M3", count)
                .with("M4", sessionId), callback);
    }

    public static void locateDocumentMsg(Context context, SessionChatSearchResultData item) {
        if (item.documentItem != null && item.slr != null) {
//            Intent intent = new Intent(context, SessionMsgActivity.class);
            Intent intent = FsUrlUtils.buildSingleTaskForChatIntent();
            intent.putExtra(SessionConstants.INTENT_KEY_SESSION_INFO, item.slr);
            intent.putExtra(SessionMsgActivity.INTENT_KEY_LOCATE_MSGID, item.documentItem.messageId);
            context.startActivity(intent);
        }
    }

}
