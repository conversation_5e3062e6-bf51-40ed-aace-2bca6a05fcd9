/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.interconnect.datactr;

import com.facishare.fs.contacts_fs.utils.PublicEmployeesMixedDataUtils;
import com.facishare.fs.i18n.I18NHelper;
import android.app.Activity;
import android.content.Intent;

import com.facishare.fs.BaseActivity;
import com.facishare.fs.contacts_fs.datactrl.ISelectActionListener;
import com.facishare.fs.contacts_fs.datactrl.SelectBaseOperate;
import com.facishare.fs.contacts_fs.picker.RelatedEmpPicker;
import com.facishare.fs.pluginapi.contact.beans.RelatedEmp;
import com.facishare.fs.utils_fs.ToastUtils;
import com.fxiaoke.fxdblib.beans.SessionListRec;

import java.util.Set;

/**
 * Created by yangwg on 2016/7/21.
 */
public class SelectRelatedEmpImpl extends SelectBaseOperate implements ISelectActionListener {

    private String mToastInfo;
    private boolean mSelectSingle;

    public SelectRelatedEmpImpl(String toastInfo, boolean selectSingle) {
        mToastInfo = toastInfo;
        mSelectSingle = selectSingle;
    }

    @Override
    public void onSelectEmpTabItem(final BaseActivity baseActivity, String parentSessionId) {
        Set<RelatedEmp> pickedParticipantSet = RelatedEmpPicker.getPickedRelatedEmpSet();
        Set pickedInnerEmpIdSet = RelatedEmpPicker.getPickedInnerEmpIdSet();

        int selectedCount = 0;
        if (pickedParticipantSet != null) {
            selectedCount = pickedParticipantSet.size();
        }
        if (pickedInnerEmpIdSet != null) {
            selectedCount += pickedInnerEmpIdSet.size();
        }
        if (selectedCount == 0) {
            ToastUtils.showToast(mToastInfo);
            return;
        }
        if (mSelectSingle && selectedCount > 1) {
            ToastUtils.showToast(I18NHelper.getText("qx.repost_session.guide.only_choose_one_linkman")/* 您只能选择一名互联人员 */);
            return;
        }
        PublicEmployeesMixedDataUtils.saveSelectedPublicEmployees(pickedParticipantSet);
        baseActivity.setResult(Activity.RESULT_OK, new Intent());
        baseActivity.finish();
    }

    @Override
    public void onSelectSessionTabItem(final BaseActivity baseActivity, SessionListRec slr) {

    }
}
