/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.interconnect.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import com.facishare.fs.biz_session_msg.subbiz.weixinbc.constant.OutType;
import com.facishare.fs.biz_session_msg.utils.AccountUtils;
import com.facishare.fs.biz_session_msg.utils.SessionInfoUtils;
import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.contacts_fs.beans.EmployeeKey;
import com.facishare.fs.contacts_fs.beans.IEmployeeDataContract;
import com.facishare.fs.contacts_fs.beans.MixedEmpViewData;
import com.facishare.fs.contacts_fs.picker.DepartmentPicker;
import com.facishare.fs.contacts_fs.picker.RelatedEmpPicker;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.utils_fs.EmployeeUtils;
import com.facishare.fs.utils_fs.ImageLoaderUtil;
import com.facishare.fslib.R;
import com.nostra13.universalimageloader.core.ImageLoader;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Created by pangc on 2016/11/29.
 */
public class SelectRelatedByEaAdapter extends SelectRelatedAdapter {

    public SelectRelatedByEaAdapter(Context context, List<MixedEmpViewData> personList, List<EmployeeKey> filterList, boolean showCheckbox, boolean showIndex) {
        super(context, personList, filterList, showCheckbox, showIndex);
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {
        View view;
        ViewHolder viewHolder;
        if (convertView == null) {
            if (getItemViewType(position) == DisplayItem.TYPE_GROUP) {
                view = View.inflate(mContext, R.layout.select_related_emp_group, null);
                viewHolder = new GroupViewHolder(mContext, view);
            } else {
                view = View.inflate(mContext, R.layout.select_related_emp_single, null);
                viewHolder = new SingleViewHolder(mContext, view);
            }
            view.setTag(viewHolder);
        } else {
            view = convertView;
            viewHolder = (ViewHolder) view.getTag();
        }
        viewHolder.bindData(getDisplayItem(position), position);
        return view;
    }

    static int NAME_MAX_LENGTH = (int) (FSScreen.getScreenWidth() * 0.8f);

    private class GroupViewHolder extends ViewHolder {
        TextView tv_group_name, tv_group_count;

        public GroupViewHolder(Context context, View view) {
            super(context, view);
            tv_group_name = (TextView) view.findViewById(R.id.tv_group_name);
            tv_group_count = (TextView) view.findViewById(R.id.tv_group_count);
            // 初始化设置
            tv_group_name.setMaxWidth(NAME_MAX_LENGTH);
        }

        @Override
        void bindData(DisplayItem item, int position) {
            tv_group_name.setText(item.groupName);
            tv_group_count.setText("(" + item.count + ")");
        }
    }

    private class SingleViewHolder extends ViewHolder {
        CheckBox checkBox_select;
        ImageView imageHeader;
        ImageView imgNotTenantMark;//无租户人员的标识
        TextView txtName, txtInfo;
        TextView tvSessionManagerDes, labelRelated;
        View divider;

        public SingleViewHolder(Context context, View view) {
            super(context, view);
            checkBox_select = (CheckBox) view.findViewById(R.id.checkBox_select);
            imageHeader = (ImageView) view.findViewById(R.id.imageHeader);
            imgNotTenantMark = (ImageView) view.findViewById(R.id.img_not_tenant_mark);
            txtName = (TextView) view.findViewById(R.id.txtName);
            txtInfo = (TextView) view.findViewById(R.id.txtInfo);
            tvSessionManagerDes = (TextView) view.findViewById(R.id.tv_group_manager_des);
            labelRelated = (TextView) view.findViewById(R.id.label_external_related);
            divider = view.findViewById(R.id.bottom_line);
        }

        @Override
        void bindData(final DisplayItem item, int position) {
            if (mShowCheckbox) {
                checkBox_select.setVisibility(View.VISIBLE);
            } else {
                checkBox_select.setVisibility(View.GONE);
            }

            if (isItemDisabled(item.person)) {
//                checkBox_select.setEnabled(false);
                if(mShowCheckbox){
                    checkBox_select.setVisibility(View.INVISIBLE);//参考企业内里群主和自己前面的复选框都不显示，空着就好，延时为不可选中的item就不显示勾选框。
                }
            } else {
//                boolean isSelectBefore = RelatedEmpPicker.isRelatedEmpPicked(item.person);
                boolean isSelectBefore;
                if (item.person.isAIAssistant()) {
                    isSelectBefore = DepartmentPicker.isSubscribedBotHelperPicked(item.person.getUniqueKey());
                } else {
                    isSelectBefore = RelatedEmpPicker.isRelatedEmpPicked(item.person);
                }
                checkBox_select.setEnabled(true);
                checkBox_select.setChecked(isSelectBefore);
            }
            // 姓名
            txtName.setText(item.person.getName());
            // 外部对接人标签
            labelRelated.setVisibility(item.person.getOutType() == OutType.Relate_Type ? View.VISIBLE : View.GONE);
            // 头像
            ImageLoader.getInstance().displayImage(item.person.getIconFullPath(),
                    imageHeader, ImageLoaderUtil.getUserHeadImgDisplayImageOptionsForRounded(mContext));
            // 部门-职位
            showTextInfoView(txtInfo, item);
            imgNotTenantMark.setVisibility(View.GONE);//无租户人员标识
            if (IEmployeeDataContract.isOutUserId(item.person.getIdForSessionParticipant())) {
                imgNotTenantMark.setVisibility(View.VISIBLE);
            }
            // 群主标识
            if (isGroupAdmin(item.person)) {
                tvSessionManagerDes.setVisibility(View.VISIBLE);
                SessionInfoUtils.updateSessionManagerAdminLabelView(tvSessionManagerDes.getContext(), tvSessionManagerDes);
            } else {
                tvSessionManagerDes.setVisibility(View.GONE);
            }
            // 分割线
            if (position == getCount() - 1 || getDisplayItem(position + 1).type == DisplayItem.TYPE_SINGLE) {
                divider.setVisibility(View.VISIBLE);
            } else {
                divider.setVisibility(View.INVISIBLE);
            }
        }
    }

    protected void showTextInfoView(TextView txtInfo, DisplayItem item) {
        EmployeeUtils.setPostOrDepName(txtInfo, item.person.getDepartment(), item.person.getPost());
    }

    protected String getRightName(String enterpName, String enterpriseAccount) {
        if (enterpriseAccount.equals(AccountUtils.getMyEA())) {
            return I18NHelper.getText("qx.session_members.des.our_company")/* 本公司 */;
        }
        return enterpName;
    }

    @Override
    List<DisplayItem> getDisplayItem(List<MixedEmpViewData> dataList) {
        if (mShowIndex) {
            return getDisplayItemWithEnterprise(dataList);
        } else {
            return getDisplayItemNoEnterprise(dataList);
        }
    }

    private List<DisplayItem> getDisplayItemNoEnterprise(List<MixedEmpViewData> personList) {
        List<DisplayItem> group = new ArrayList<>();
        int size = personList.size();
        if (size == 0) {
            return group;
        }
        for (int i = 0; i < size; ++i) {
            MixedEmpViewData person = personList.get(i);
            group.add(new DisplayItem(person));
        }
        return group;
    }

    // 加入Type-group
    private List<DisplayItem> getDisplayItemWithEnterprise(List<MixedEmpViewData> personList) {
        Collections.sort(personList, itemComp);
        List<DisplayItem> group = new ArrayList<>();
        int size = personList.size();
        if (size == 0) {
            return group;
        }
        if (size == 1) {
            MixedEmpViewData person = personList.get(0);
            group.add(new DisplayItem(1, getRightName(person.getEnterpriseShortName(), person.getEAForSessionParticipant())));
            group.add(new DisplayItem(person));
            return group;
        }

        String lastEnterpName = TextUtils.isEmpty(personList.get(0).getEnterpriseShortName()) ?
                personList.get(0).getEnterpriseName() : personList.get(0).getEnterpriseShortName();
        String lastEnterpAccount = personList.get(0).getEAForSessionParticipant();
        int pos = 0;
        int count = 1;
        int group_count = 0;
        group.add(new DisplayItem(personList.get(0)));
        for (int i = 1; i < size; ++i) {
            MixedEmpViewData person = personList.get(i);
            if (lastEnterpAccount.equals(person.getEAForSessionParticipant())) {
                ++count;
            } else {
                group.add(pos, new DisplayItem(count, getRightName(lastEnterpName, lastEnterpAccount)));
                ++group_count;
                lastEnterpName = TextUtils.isEmpty(person.getEnterpriseShortName()) ?
                        person.getEnterpriseName() : person.getEnterpriseShortName();
                lastEnterpAccount = person.getEAForSessionParticipant();
                count = 1;
                pos = i + group_count;
            }
            group.add(new DisplayItem(person));
            if (i == size - 1) {
                group.add(pos, new DisplayItem(count, getRightName(lastEnterpName, lastEnterpAccount)));
            }
        }

        return group;
    }

    private static final Comparator<MixedEmpViewData> itemComp = new Comparator<MixedEmpViewData>() {
        @Override
        public int compare(MixedEmpViewData person1, MixedEmpViewData person2) {
            int diff = person1.getEnterpriseType() - person2.getEnterpriseType();
            if (diff != 0) {
                return diff;   // 内部Inner=0,外部Cross=1,内部优先
            }
            return IEmployeeDataContract.compare(person1, person2);
        }
    };
}