/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.enterprisefilesystem.bean;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * Created by tianyl on 2016/7/8.
 */
public class FolderGetSubFoldersVOResult {
    //目录信息列表
    @JSONField(name="M1")
    public List<FolderIndexInfo> folderIndexInfoList;

    public FolderGetSubFoldersVOResult(){}
    @JSONCreator
    public FolderGetSubFoldersVOResult(
            @JSONField(name="M1")
            List<FolderIndexInfo> folderIndexInfoList) {
        this.folderIndexInfoList = folderIndexInfoList;
    }
}
