/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.beans;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * Created by zengjj on 2016/7/15.
 */
public class SubmitVoteResult implements Serializable {
    private static final long serialVersionUID = -336883462476448723L;

    @JSONField(name="M1")
    public int code;

    public SubmitVoteResult() {
        super();
    }
    @JSONCreator
    public SubmitVoteResult(@JSONField(name="M1") int code) {
        this.code = code;
    }
}
