/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.enterprisefilesystem.bean;

import java.io.Serializable;
import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;
/**
 * Created by tianyl on 2016/6/30.
 */
public class FilePreviewVOResult implements Serializable{
    @J<PERSON>NField(name="M1")
    public String token;

    public FilePreviewVOResult(){}
    @JSONCreator
    public FilePreviewVOResult(
            @J<PERSON>NField(name="M1")
            String token) {
        this.token = token;
    }
}
