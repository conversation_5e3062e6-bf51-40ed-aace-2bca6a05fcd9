/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification;

import com.facishare.fs.contacts_fs.beans.IEmployeeDataContract;
import com.facishare.fs.i18n.I18NHelper;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.TypeReference;
import com.facishare.fs.App;
import com.facishare.fs.BaseActivity;
import com.facishare.fslib.R;
import com.facishare.fs.biz_session_msg.beans.EmployeeInfo;
import com.facishare.fs.biz_session_msg.datactrl.IBatchChildListAction;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.api.NotificationWebApiUtils;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.beans.GetSendEmployeesResult;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.beans.ReadDetail;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.beans.RelatedEmployeeCard;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.beans.SendEmployee;
import com.facishare.fs.biz_session_msg.utils.UnknowEmployeeUtils;
import com.facishare.fs.contacts_fs.beans.EmployeeKey;
import com.facishare.fs.contacts_fs.beans.EmployeePublicData;
import com.facishare.fs.pluginapi.contact.beans.User;
import com.facishare.fs.utils_fs.ImageLoaderUtil;
import com.facishare.fs.utils_fs.ToastUtils;
import com.fxiaoke.fscommon.adapter.NormalBaseAdapter;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.nostra13.universalimageloader.core.ImageLoader;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

/**
 * Created by zengjj on 2016/7/22.
 */
public class NotificationRangeInfoActivity extends BaseActivity {

    String mNoticeId;
    ListView mListView;
    ArrayList<ReadDetail> readers;
    RangeAccountAdapter mAdapter;
    public static final String NOTICE_ID_KEY="NOTICE_ID_KEY";
    private List<SendEmployee> sendEmployees;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.notification_rangeinfo_activity);

        Intent intent=getIntent();
        if(intent!=null){
            mNoticeId=intent.getStringExtra(NOTICE_ID_KEY);
        }

        initTitle();

        initView();

        reqGetSendEmployees(mNoticeId,true);
    }

    /**
     * 初始化tittle
     */
    private void initTitle() {
        super.initTitleEx();

        TextView txtCenter = mCommonTitleView.getCenterTxtView();
        txtCenter.setText(I18NHelper.getText("xt.choose_view.des.copy_range")/* 抄送范围 */);

        mCommonTitleView.addLeftAction(I18NHelper.getText("crm.layout.activity_out_profile_person_info.8232")/* 返回 */, R.string.btn_title_back, new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });

    }

    private void initView() {
        mListView= (ListView) findViewById(R.id.listview_rangeinfo_list);
        mAdapter=new RangeAccountAdapter(this,null);
        mListView.setAdapter(mAdapter);
        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                //跳转查看详情
                SendEmployee sendEmp=(SendEmployee) mAdapter.getItem(position);
                String eaId = sendEmp.enterpriseShortName;
                if (TextUtils.isEmpty(eaId)) {
                    eaId = sendEmp.enterpriseName;
                }
                Intent intent = new Intent(NotificationRangeInfoActivity.this,NotificationRangeEmployeeActivity.class);
                intent.putExtra(NotificationRangeEmployeeActivity.EMPLOYEE_LIST_KEY,(ArrayList<RelatedEmployeeCard>)sendEmp.employees);
                intent.putExtra(NotificationRangeEmployeeActivity.ACCOUNT_NAME_KEY,eaId);
                startActivity(intent);
            }
        });
    }

    /**
     * 获取操作范围
     * @param noticeId
     * @param showProgress
     */
    public void reqGetSendEmployees(String noticeId, final boolean
            showProgress) {
        if (!App.netIsOK.get()) {
            ToastUtils.netErrShow();
            return;
        }
        if (showProgress) {
            startProgress();
        }
        WebApiExecutionCallback<GetSendEmployeesResult> callback = new WebApiExecutionCallback<GetSendEmployeesResult>() {

            public Class<GetSendEmployeesResult> getTypeReferenceFHE() {
                return GetSendEmployeesResult.class;
            }

            @Override
            public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                if (showProgress) {
                    endProgress();
                }
                ToastUtils.show(error);
            }

            @Override
            public TypeReference<WebApiResponse<GetSendEmployeesResult>> getTypeReference() {
                return new TypeReference<WebApiResponse<GetSendEmployeesResult>>() {
                };
            }

            @Override
            public void completed(Date time, GetSendEmployeesResult response) {
                if (showProgress) {
                    endProgress();
                }
                if (response == null || response.sendEmployees == null) {
                    return;
                }
                sendEmployees=response.sendEmployees;
                mAdapter.updateData(sendEmployees);
                updateUnknowDataOfSendEmployees(sendEmployees);
            }
        };
        NotificationWebApiUtils.GetSendEmployees(noticeId,callback);
    }

    private void updateUnknowDataOfSendEmployees(List<SendEmployee> sendEmployeeList) {
        List<EmployeeKey> employeeKeys = new LinkedList<>();
        for (SendEmployee sendEmployee : sendEmployeeList) {
            for (RelatedEmployeeCard employee : sendEmployee.employees) {
                employeeKeys.add(IEmployeeDataContract.createEmployeeKey(employee.ea, employee.employeeId));
            }
        }
        UnknowEmployeeUtils.updateCrossUnknownData(employeeKeys, new UnknowEmployeeUtils.GetUnknownDataListener() {
            @Override
            public void onGetUnknownData(boolean hasFake, List<EmployeeInfo> updateInfoList) {
                mAdapter.notifyDataSetChanged();
            }
        });
    }

    private void startProgress() {
        if (this instanceof BaseActivity) {
            ((BaseActivity) this).showDialog(BaseActivity.DIALOG_WAITING_BASE);
        }
    }

    private void endProgress() {
        if (this instanceof BaseActivity) {
            ((BaseActivity) this).removeDialog(BaseActivity.DIALOG_WAITING_BASE);
        }
    }

    class RangeAccountAdapter extends NormalBaseAdapter{

        public RangeAccountAdapter(Context ctx, List data) {
            super(ctx, data);
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder;
            if(convertView==null||convertView.getTag()==null){
                convertView= LayoutInflater.from(mCtx).inflate(R.layout.notification_range_account_list_item,null);
                holder=new ViewHolder();
                holder.icon= (ImageView) convertView.findViewById(R.id.imageHeader);
                holder.nameTV= (TextView) convertView.findViewById(R.id.txtName);
                holder.numTV= (TextView) convertView.findViewById(R.id.txtInfo);
                convertView.setTag(holder);
            }else{
                holder= (ViewHolder) convertView.getTag();
            }
            SendEmployee sendEmp = (SendEmployee) mData.get(position);
            String name = sendEmp.enterpriseName;
            if (!TextUtils.isEmpty(sendEmp.enterpriseShortName)) {
                name = sendEmp.enterpriseShortName;
            }
            ImageLoader.getInstance().displayImage(
                    IBatchChildListAction.URL_IMAGE_RELATED_ENTERPRISE_LOGO + sendEmp.enterpriseProfileImage,
                    holder.icon, ImageLoaderUtil.getDisplayDefaultRoundImageOptions(mCtx,
                            R.drawable.cross_enterprise_default_header_img));

//            EnterpriseUtils.bindEnterpriseIcon(holder.icon, eaid, new CrossUtils.BindImageCallback() {
//                @Override
//                public void bindImageView(ImageView imageView, String ea, String imageUrl) {
//                    if (imageView.getTag() != null && (imageView.getTag().equals(ea))) {
//                        ImageLoader.getInstance().displayImage(imageUrl,
//                                imageView, ImageLoaderUtil.getDisplayDefaultRoundImageOptions(
//                                        R.drawable.cross_enterprise_default_header_img));
//                    }
//                }
//            });

            if (!TextUtils.isEmpty(name) && name.length() > 12) {
                name = name.substring(0, 13);
                name = name + "...";
            }
            holder.nameTV.setText(name);
            int num = sendEmp.employees.size();
            holder.numTV.setText(I18NHelper.getFormatText("xt.feed_recent_discussion_menu.text.person02",num+"")/*({0}人) */);

            return convertView;
        }

         public class ViewHolder{
            ImageView icon;
             TextView nameTV;
             TextView numTV;
         }
    }
}
