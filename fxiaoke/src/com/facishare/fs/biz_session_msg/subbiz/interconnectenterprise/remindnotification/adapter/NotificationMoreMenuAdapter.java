/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.adapter;

import java.util.List;

import com.facishare.fslib.R;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.beans.MenuData;
import com.fxiaoke.fscommon.adapter.NormalBaseAdapter;
import com.facishare.fs.common_utils.FSScreen;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

/**
 * Created by zengjj on 2016/7/20.
 */
public class NotificationMoreMenuAdapter extends NormalBaseAdapter {

    float mden;
    public NotificationMoreMenuAdapter(Context ctx, List<MenuData> list){
        super(ctx,list);
        mden=ctx.getResources().getDisplayMetrics().density;
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {
        // TODO Auto-generated method stub
        ViewHolder holder=null;
        if (convertView == null) {
            holder=new ViewHolder();
            convertView = LayoutInflater.from(mCtx).inflate(R.layout.notification_more_menu_item, null);
            holder.icon=(ImageView)convertView.findViewById(R.id.menu_icon);
            holder.rootLayout = (LinearLayout) convertView.findViewById(R.id.menu_root_layout);
            holder.name=(TextView)convertView.findViewById(R.id.menu_name);
            holder.firtDivide=convertView.findViewById(R.id.firtDivide);

            LinearLayout.LayoutParams layoutParams=new LinearLayout.LayoutParams(FSScreen.dip2px(68),FSScreen
                    .dip2px(68));
            holder.icon.setLayoutParams(layoutParams);

            convertView.setTag(holder);
        }else{
            holder=(ViewHolder)convertView.getTag();
        }

        final MenuData menu = (MenuData)mData.get(position);
        holder.icon.setImageResource(menu.iconid);
        holder.name.setText(menu.name);
        if(position==0){
            holder.firtDivide.setVisibility(View.VISIBLE);
        }else{
            holder.firtDivide.setVisibility(View.GONE);
        }
        return convertView;
    }
    class ViewHolder{
        ImageView icon;
        TextView name;
        View firtDivide;
        LinearLayout rootLayout;
    }
}
