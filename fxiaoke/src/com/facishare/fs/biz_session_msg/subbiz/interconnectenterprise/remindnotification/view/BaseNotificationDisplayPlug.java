/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.view;

import com.facishare.fs.i18n.I18NHelper;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.CharacterStyle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RatingBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.facishare.fs.App;
import com.facishare.fslib.R;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.NotificationDetailActivity;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.NotificationReplyActivity;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.api.NotificationWebApiUtils;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.beans.AddPraiseResult;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.beans.MenuData;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.beans.Notice;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.beans.RemovePraiseResult;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.utils.NotificationAttatchViewContrler;
import com.facishare.fs.common_datactrl.draft.draft_fw.DraftType;
import com.facishare.fs.common_utils.DateTimeUtils;
import com.facishare.fs.common_utils.time.NetworkTime;
import com.facishare.fs.utils_fs.ToastUtils;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiResponse;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by zengjj on 2016/6/28.
 */
public class BaseNotificationDisplayPlug {

    NotificationMoreMenu.IMenuAction mIMenuAction;

    public void setIMenuAction(NotificationMoreMenu.IMenuAction mIMenuAction) {
        this.mIMenuAction = mIMenuAction;
    }

    private BaseNotificationDisplayPlug() {

    }

    private static BaseNotificationDisplayPlug plug;

    synchronized public static BaseNotificationDisplayPlug getInstance(Context mContext, Notice mNotice) {
        if (plug == null) {
            plug = new BaseNotificationDisplayPlug();
        }
        return plug;
    }

    public View displayNotice(View convertView, Context context, Notice notice,String searchKey,boolean isAdmin) {

        ViewHolder holder = null;
        //前次显示介绍图片的view时，很可能影响了converView，不能保证拿到最新的converView，及对应的holder，所以这里需重新拿下
        if (convertView == null || convertView.getTag() == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.notify_display_base_layout, null);
            holder = newViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        // 以下方法会复用
        initHolder(context, holder, notice, searchKey, isAdmin);
        return convertView;
    }

    public void initHolder(Context ctx, ViewHolder holder, Notice notice, String searchKey, boolean isAdmin) {

        //显示用户名和头像 //备注：这里的notice.sender提供了name，但没有headImage
        NotificationAttatchViewContrler.displayEmployInfo(ctx,notice.sender.ea, notice.sender.employeeId, holder.head,
                holder.name,false);
        // 创建时间
        Date date = new Date(NetworkTime.getInstance(ctx).getCurrentNetworkTime());
        String timeStr = DateTimeUtils.formatForStream(new Date(notice.createTime), date);
        holder.time.setText(timeStr);

        //信息来源
        String source = "";
        if (TextUtils.isEmpty(notice.source)) {
            source = I18NHelper.getFormatText("bi.layout.item_checkbox_multi_layout_search.2150.v1"/* 来自{0} */ , I18NHelper.getText("crm.layout.item_business.1978"))/* 纷享逍客 */;
        } else {
            source = I18NHelper.getFormatText("bi.layout.item_checkbox_multi_layout_search.2150.v1"/* 来自 */ , notice.source);
        }
        holder.source.setText(source);

        //通知类型
        String statu="";
        if(notice.hasRevoked){
            statu=I18NHelper.getText("qx.cross_notification_detail.guide.has_revoked")/*  - 已撤回 */;
        }
        holder.type.setText(notice.tag + statu);

        //通知正文
        if (notice.content != null) {
            handerText(holder.content,new SpannableStringBuilder(notice.content),searchKey);
        }
        //通知图片
        NotificationAttatchViewContrler.handlePics(ctx, holder.attachContainerLayout, notice.images, 9);
        //通知附件
        NotificationAttatchViewContrler.handleListFiles(ctx, holder.attachContainerLayout, notice);
        //通知投票
        NotificationAttatchViewContrler.handleListVote(ctx, holder.attachContainerLayout, notice);
        //通知图文
        NotificationAttatchViewContrler.handleListRichTexts(ctx, holder.attachContainerLayout, notice);

        //处理底部点击事件
        handleFooter(ctx, notice, holder, isAdmin);

    }

    /**
     * 处理正文，搜素关键字标红
     * @param textView
     * @param content
     * @param key
     */
    public void handerText(TextView textView, SpannableStringBuilder content,String key) {
        String text = content.toString();

            int index = 0;
        if(!TextUtils.isEmpty(key)){

            int length = key.length();
            while ((index = text.indexOf(key, index)) != -1) {
                content.setSpan(new CharacterStyle() {
                    @Override
                    public void updateDrawState(TextPaint tp) {
                        tp.setColor(Color.RED);
                    }
                }, index, index + length, SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
                index += length;

            }
        }

        if(content.length()>200){
            content = (SpannableStringBuilder) content.subSequence(0,200);
            content.append("...");
        }
        textView.setText(content);
    }

    private void handleFooter(final Context ctx, final Notice notice, final ViewHolder holder, final boolean isAdmin) {

        holder.replyLayout.setTag(notice);
        holder.praiseLayout.setTag(notice);
        holder.moreMenuLayout.setTag(notice);

        // -先初始化背景图-可以赞, 说明当前还未赞
        if (notice.hasPraised) {
            holder.praiseIcon.setImageResource(R.drawable.feedlist_hand_press);
        } else {
            holder.praiseIcon.setImageResource(R.drawable.feedlist_hand);
        }
        //
        if (notice.praiseCount > 0) {
            holder.praiseCount.setText("" + notice.praiseCount);
        } else {
            holder.praiseCount.setText(I18NHelper.getText("xt.x_feed_detail_activity.text.zan")/* 赞 */);// +1
        }

        if(notice.replyCount>0){
            holder.bottomReplyText.setText(notice.replyCount+"");
        }else{
            holder.bottomReplyText.setText(I18NHelper.getText("xt.work_reply_inc_footer.text.reply")/* 回复 */);
        }

        holder.replyLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //回复
                Notice not = (Notice) v.getTag();
                if(not.replyCount>0){
                    //有回复直接跳转回复列表
                    ctx.startActivity(NotificationDetailActivity.getDetailIntent(ctx,notice.id,notice,isAdmin));
                }else{
                    //没有回复跳转回复
                    Intent intent = NotificationReplyActivity.startReply(ctx, DraftType
                            .DRAFT_QIXIN_NOTIFY_REPLY, not.id, NotificationWebApiUtils.REPLY_TO_NOTICE, null, null,notice.crossEaList);
                    ((Activity) ctx).startActivityForResult(intent, NotificationDetailActivity
                            .NOTICE_REPLY_REQUESTCODE);
                }
            }
        });
        holder.praiseLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //点赞，取消赞
                final Notice not = (Notice) v.getTag();
                holder.praiseIcon.setTag(R.id.praise_reply_id, not.id);//用来校验失败回调数据
                if (!not.hasPraised) {
                    reqAddPraise(NotificationWebApiUtils.PRAISE_TO_NOTICE, not.id, null, new PraiseCallback() {
                        @Override
                        public void onFailed() {
                            Object tag = holder.praiseIcon.getTag(R.id.praise_reply_id);
                            if (TextUtils.equals((String) tag, not.id)) {
                                not.praiseCount = not.praiseCount - 1;
                                not.hasPraised = false;
                                if (not.praiseCount == 0) {
                                    holder.praiseCount.setText(I18NHelper.getText("xt.x_feed_detail_activity.text.zan")/* 赞 */);
                                } else {
                                    holder.praiseCount.setText("" + not.praiseCount);
                                }
                                NotificationAttatchViewContrler.setShowPraiseAmin(holder.praiseIcon, not.hasPraised);
                            }
                        }
                    });
                } else {
                    reqRemovePraise(NotificationWebApiUtils.PRAISE_TO_NOTICE, not.id, null, new PraiseCallback() {
                        @Override
                        public void onFailed() {
                            Object tag = holder.praiseIcon.getTag(R.id.praise_reply_id);
                            if (TextUtils.equals((String) tag, not.id)) {
                                not.praiseCount = not.praiseCount + 1;
                                not.hasPraised = true;
                                if (not.praiseCount == 0) {
                                    holder.praiseCount.setText(I18NHelper.getText("xt.x_feed_detail_activity.text.zan")/* 赞 */);
                                } else {
                                    holder.praiseCount.setText("" + not.praiseCount);
                                }
                                NotificationAttatchViewContrler.setShowPraiseAmin(holder.praiseIcon, not.hasPraised);
                            }
                        }
                    });
                }

                if (not.hasPraised) {
                    // holder.praiseIcon.setImageResource(R.drawable.work_main_praise_no);
                    not.praiseCount = not.praiseCount - 1;
                    not.hasPraised = false;
                } else {
                    // holder.praiseIcon.setImageResource(R.drawable.work_main_praise_yes);
                    not.praiseCount = not.praiseCount + 1;
                    not.hasPraised = true;
                }
                //点赞动画
                NotificationAttatchViewContrler.setShowPraiseAmin(holder.praiseIcon, not.hasPraised);

                if (not.praiseCount == 0) {
                    holder.praiseCount.setText(I18NHelper.getText("xt.x_feed_detail_activity.text.zan")/* 赞 */);
                } else {
                    holder.praiseCount.setText("" + not.praiseCount);
                }
            }
        });
        holder.moreMenuLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                List<MenuData> menuDatas = new ArrayList<MenuData>();
                /**
                 * 只有本人发送的，没有超过24小时，没有被撤回的通知才能撤回
                 */
                if (!notice.hasRevoked && (isAdmin || NotificationAttatchViewContrler
                                                            .isSelf(ctx, notice.sender.ea, notice.sender.employeeId))) {
                    menuDatas.add(new MenuData(NotificationMoreMenu.MenuActionType
                            .recall_notice, R.drawable.feed_more_revoke_task_selector, I18NHelper.getText("crm.controller.OrderMoreOpsWMController.1060")/* 撤回 */, notice));
                }
                menuDatas.add(new MenuData(NotificationMoreMenu.MenuActionType
                        .remind_notice, R
                        .drawable.feed_more_remind_selector, I18NHelper.getText("pay.common.common.reminder")/* 提醒 */, notice));
                //不管是否已经收藏，都显示收藏
                menuDatas.add(new MenuData(NotificationMoreMenu.MenuActionType
                            .add_archive_notice, R.drawable
                            .feed_more_archive_selector, I18NHelper.getText("th.base.view.store")/* 收藏 */, notice));

                NotificationMoreMenu moreMenu = new NotificationMoreMenu(ctx);
                moreMenu.setIMenuAction(mIMenuAction);
                moreMenu.updateData(notice.id, menuDatas);
                moreMenu.showMenu();
            }
        });

        /**
         * 判断是否已经撤回 或者 没有权限操作（通知对应的互联关系失效）
         */
        if(notice.hasRevoked || !notice.hasFriendRelation){
            holder.bottomBaffle.setVisibility(View.VISIBLE);
            holder.bottomMoreText.setTextColor(ctx.getResources().getColor(R.color.color_cccccc));
            holder.bottomPraiseText.setTextColor(ctx.getResources().getColor(R.color.color_cccccc));
            holder.bottomReplyText.setTextColor(ctx.getResources().getColor(R.color.color_cccccc));
            holder.bottomBaseView.setBackgroundColor(ctx.getResources().getColor(R.color.color_fafafa));

            holder.moreIcon.setImageResource(R.drawable.notification_botton_remove_more);
            holder.replyIcon.setImageResource(R.drawable.notification_bottom_remove_reply);
            holder.praiseIcon.setImageResource(R.drawable.notification_botton_remove_praise);
        }else{
            holder.bottomBaffle.setVisibility(View.GONE);
            holder.bottomMoreText.setTextColor(ctx.getResources().getColor(R.color.color_999999));
            holder.bottomPraiseText.setTextColor(ctx.getResources().getColor(R.color.color_999999));
            holder.bottomReplyText.setTextColor(ctx.getResources().getColor(R.color.color_999999));
            holder.bottomBaseView.setBackgroundColor(ctx.getResources().getColor(R.color.color_ffffff));

            holder.moreIcon.setImageResource(R.drawable.feedlist_more_icon);
            holder.replyIcon.setImageResource(R.drawable.feedlist_comment);
        }

        //挡板的点击事件不做处理
        holder.bottomBaffle.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
    }

    public interface PraiseCallback {
        void onFailed();
    }

    /**
     * 点赞
     */
    public static void reqAddPraise(int praiseType, String noticeId, String replyId,
                                    final PraiseCallback praiseCallback) {
        if (!App.netIsOK.get()) {
            ToastUtils.netErrShow();
            return;
        }
        WebApiExecutionCallback<AddPraiseResult> callback = new WebApiExecutionCallback<AddPraiseResult>() {

            public Class<AddPraiseResult> getTypeReferenceFHE() {
                return AddPraiseResult.class;
            }

            @Override
            public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                ToastUtils.show(error);
                praiseCallback.onFailed();
            }

            @Override
            public TypeReference<WebApiResponse<AddPraiseResult>> getTypeReference() {
                return new TypeReference<WebApiResponse<AddPraiseResult>>() {
                };
            }

            @Override
            public void completed(Date time, AddPraiseResult response) {
                if (response == null) {
                    return;
                }
            }
        };
        NotificationWebApiUtils.AddPraise(praiseType, noticeId, replyId, callback);
    }

    /**
     * 取消赞
     */
    public static void reqRemovePraise(int praiseType, String noticeId, String replyId,
                                       final PraiseCallback praiseCallback) {
        if (!App.netIsOK.get()) {
            ToastUtils.netErrShow();
            return;
        }
        WebApiExecutionCallback<RemovePraiseResult> callback = new WebApiExecutionCallback<RemovePraiseResult>() {

            public Class<RemovePraiseResult> getTypeReferenceFHE() {
                return RemovePraiseResult.class;
            }

            @Override
            public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                ToastUtils.show(error);
                praiseCallback.onFailed();
            }

            @Override
            public TypeReference<WebApiResponse<RemovePraiseResult>> getTypeReference() {
                return new TypeReference<WebApiResponse<RemovePraiseResult>>() {
                };
            }

            @Override
            public void completed(Date time, RemovePraiseResult response) {
                if (response == null) {
                    return;
                }
            }
        };
        NotificationWebApiUtils.RemovePraise(praiseType, noticeId, replyId, callback);
    }

    public ViewHolder newViewHolder(View layout) {
        ViewHolder holder = new ViewHolder();
        //
        holder.divideline = layout.findViewById(R.id.notice_list_item_divideline);
        holder.head = (ImageView) layout.findViewById(R.id.work_head);
        holder.name = (TextView) layout.findViewById(R.id.work_name);
        holder.unread = layout.findViewById(R.id.work_unread_state);
        // 时间,来源,工作类型-工作内容
        holder.time = (TextView) layout.findViewById(R.id.work_time);
        holder.source = (TextView) layout.findViewById(R.id.work_source);
        holder.type = (TextView) layout.findViewById(R.id.work_type);
        holder.work_type_receipt_view = (Button) layout.findViewById(R.id.work_type_receipt_view);
        holder.content = (TextView) layout.findViewById(R.id.work_content);
        holder.contentPre = (TextView) layout.findViewById(R.id.work_content_pre);
        holder.ratingBar = (RatingBar) layout.findViewById(R.id.work_rate);
        // 日志
        holder.planContentLayout = layout.findViewById(R.id.work_plan_content_layout);
        // 公告布局-公告类型
        holder.noticeLayout = layout.findViewById(R.id.work_notice_layout);
        holder.noticeTitle = (TextView) layout.findViewById(R.id.work_notice_title);
        // 附件
        holder.attachContainerLayout = (LinearLayout) layout.findViewById(R.id.work_attach_container_layout);
        holder.archiveContainerLayout = (LinearLayout) layout.findViewById(R.id.work_archive_container_layout);
        holder.leaderReplyContainer = (RelativeLayout) layout.findViewById(R.id.work_leader_reply_container);
        // 回复-提醒-投票
        holder.replyLayout = layout.findViewById(R.id.work_reply_layout);
        //赞
        holder.praiseLayout = layout.findViewById(R.id.work_praise_layout);
        holder.moreMenuLayout = layout.findViewById(R.id.work_more_layout);
        //回复和赞中间的线
        holder.work_praise_line = layout.findViewById(R.id.work_praise_line);
        holder.txtSignDateTime = (TextView) layout.findViewById(R.id.txtSignDateTime);

        holder.crmTagLayout = layout.findViewById(R.id.crmTagLayout);
        holder.txtTagList = (TextView) layout.findViewById(R.id.txtTagList);

        //底部按钮挡板
        holder.bottomBaffle = layout.findViewById(R.id.bottom_baffle_view);

        //底部文字
        holder.bottomMoreText = (TextView) layout.findViewById(R.id.work_more_menu);
        holder.bottomPraiseText = (TextView) layout.findViewById(R.id.work_praise_count);
        holder.bottomReplyText = (TextView) layout.findViewById(R.id.work_reply_info);

        //底部布局
        holder.bottomBaseView = layout.findViewById(R.id.bottom_base_view);

        if (holder.replyLayout != null) {
            holder.replyIcon = (ImageView) holder.replyLayout.findViewById(R.id.work_reply_icon);
            holder.replyInfo = (TextView) holder.replyLayout.findViewById(R.id.work_reply_info);
        }
        // 支持数
        if (holder.praiseLayout != null) {
            holder.praiseIcon = (ImageView) holder.praiseLayout.findViewById(R.id.work_praise_icon);
            holder.praiseCount = (TextView) holder.praiseLayout.findViewById(R.id.work_praise_count);
        }
        holder.moreIcon = (ImageView) holder.moreMenuLayout.findViewById(R.id.work_more_icon);
        return holder;
    }

    public static class ViewHolder {
        // 发送者信息(头像、姓名)
        View divideline;
        ImageView head;
        TextView name;
        View unread;// 消息状态
        // 工作类型、内容、时间、客户端类型
        TextView type, contentPre, content, time, source;
        Button work_type_receipt_view;
        RatingBar ratingBar;// 点评级别
        // 日志布局
        View planContentLayout;
        // 加密
        //        View encLayout;
        //        ImageView encIcon;
        //        TextView encContent, encInfo;
        // 附件
        //        LinearLayout attachLayout;
        // 附件
        LinearLayout attachContainerLayout;
        // 归档标签布局
        LinearLayout archiveContainerLayout;
        //		// 日程
        //		View scheduleLayout;
        //		ImageView scheduleIcon;
        //		TextView scheduleInfo;
        // 公告
        View noticeLayout;
        TextView noticeTitle;
        // 签到
        //		TextView location;
        // 领导审批
        //		LinearLayout leaderReplyLayout;
        //		LinearLayout leaderReplyContainer;
        RelativeLayout leaderReplyContainer;
        // 回复-赞-更多
        View replyLayout, praiseLayout, moreMenuLayout;

        //回复靠右边布局
        //        View replyLayout1;
        ImageView replyIcon, praiseIcon ,moreIcon;
        TextView replyInfo, praiseCount;// 支持数量
        View crmTagLayout;
        TextView txtTagList;

        //回复和赞中间的线
        View work_praise_line;

        /**
         * crm 相关布局
         */
        View feedCrmLayout;
        TextView txtSignDateTime;
        /**
         * 底部按钮蒙版
         */
        View bottomBaffle;
        /**
         * 底部文字
         */
        TextView bottomMoreText;
        TextView bottomPraiseText;
        TextView bottomReplyText;
        /**
         * 底部布局
         */
        View bottomBaseView;
    }

}
