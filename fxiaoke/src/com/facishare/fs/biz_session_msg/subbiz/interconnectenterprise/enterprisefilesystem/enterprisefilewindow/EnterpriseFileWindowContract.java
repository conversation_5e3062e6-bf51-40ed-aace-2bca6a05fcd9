/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.enterprisefilesystem.enterprisefilewindow;

import java.util.List;

import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.enterprisefilesystem.BaseView;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.enterprisefilesystem.bean.DynamicInfo;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.enterprisefilesystem.bean.FolderInfo;

/**
 * Created by tianyl on 2016/6/28.
 * 文件互联我方发出的和其他企业共享的页面控制接口
 */
public interface EnterpriseFileWindowContract {

    interface View extends BaseView {
        void refresh(List<FolderInfo> list);
        /**
         * 展示创建文件夹的PopupWindow
         */
        void showPopupWindow();
        /**
         * 展示文件夹详情界面
         */
        void showFolder(int position,FolderInfo folder,boolean isWeSent);
        /**
         * 展示动态详情界面
         */
        void showDynamic(String keyword,boolean isWeSent);
        /**
         * 更新动态条目
         */
        void updateDynamic(int visibility,DynamicInfo dynamicInfo,boolean isWeSent);
        /**
         * 更新标题内容
         * @param title
         */
        void refreshTitle(String title);
        /**
         * 显示创建文件夹按钮
         */
        void showCreateIcon();
    }

    interface Presenter{
        /**
         * 新建文件夹
         */
        void createFolder();
        /**
         * 点击右上角 + 号
         */
        void createEvent();
        /**
         * 点击动态视图
         */
        void goDynamic();
        /**
         *点击文件夹条目
         */
        void openFolder(int position);
        /**
         *点击左上角返回箭头
         */
        void returnEvent();
    }

}
