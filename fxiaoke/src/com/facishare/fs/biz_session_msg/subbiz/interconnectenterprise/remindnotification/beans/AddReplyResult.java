/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.remindnotification.beans;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * Created by zengjj on 2016/7/16.
 */
public class AddReplyResult implements Serializable{

    private static final long serialVersionUID = 6570152757632686494L;

    @JSONField(name="M1")
    public String replyId;

    public AddReplyResult() {
        super();
    }
    @JSONCreator
    public AddReplyResult(@JSONField(name="M1") String replyId) {
        this.replyId = replyId;
    }
}
