/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.enterprisefilesystem.folderdetail;

import com.facishare.fs.i18n.I18NHelper;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.facishare.fslib.R;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.enterprisefilesystem.FileConnectUtils;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.enterprisefilesystem.bean.FileInfo;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.enterprisefilesystem.bean.FolderInfo;
import com.facishare.fs.biz_session_msg.utils.AccountUtils;
import com.facishare.fs.common_utils.DateTimeUtils;
import com.lidroid.xutils.util.FileStorageUtils;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

/**
 * Created by tianyl on 2016/7/1.
 */
public class FolderDetailAdapter extends BaseAdapter {

    protected Context mCtx;
    private List<FolderInfo> mFolderInfo;
    private OnFolderClickLis mFolderLis;
    private List<FileInfo> mFileInfo;
    private OnFileMenuClickLis mFileLis;
    String mMyEA = AccountUtils.getMyEA();
    public FolderDetailAdapter(Context ctx, List<FolderInfo> folderInfo, List<FileInfo> fileInfo) {
        mCtx = ctx;
        if (folderInfo == null || folderInfo.size()==0){
            mFolderInfo = new ArrayList();
        }else {
            mFolderInfo = folderInfo;
        }
        if (fileInfo == null || fileInfo.size()==0){
            mFileInfo = new ArrayList();
        }else {
            mFileInfo = fileInfo;
        }
    }

    @Override
    public int getCount() {
        return mFolderInfo.size()+mFileInfo.size();
    }

    @Override
    public Object getItem(int position) {
        if (position<mFolderInfo.size()){
            return mFolderInfo.get(position);
        }else if (position>=mFolderInfo.size()&&position-mFolderInfo.size()<mFileInfo.size()){
            return mFileInfo.get(position-mFolderInfo.size());
        }
        return null;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {
        ViewHolder holder = null;
        if (convertView==null){
            holder = new ViewHolder();
            convertView = View.inflate(mCtx, R.layout.folder_detail_list_item,null);
            holder.line = convertView.findViewById(R.id.divider_line);
            holder.icon = (ImageView) convertView.findViewById(R.id.iv_icon);
            holder.title = (TextView) convertView.findViewById(R.id.tv_title);
            holder.time = (TextView) convertView.findViewById(R.id.tv_time);
            holder.size = (TextView) convertView.findViewById(R.id.tv_size);
            holder.rightIcon = (ImageView) convertView.findViewById(R.id.iv_next);
            convertView.setTag(holder);
        }else {
            holder = (ViewHolder) convertView.getTag();
        }
        if (position == getCount()-1){
            holder.line.setVisibility(View.INVISIBLE);
        }else {
            holder.line.setVisibility(View.VISIBLE);
        }
        Object obj = getItem(position);
        if (obj instanceof FolderInfo){
            //如果是文件夹
            FolderInfo item = (FolderInfo) obj;
            //设置图标
            holder.icon.setImageResource(R.drawable.enterprise_folder_icon);
            //设置标题
            holder.title.setText(item.folderName);
            //设置创建时间
            String time = I18NHelper.getText("meta.beans.InstanceState.3070")/* 未知 */;
            if (!TextUtils.isEmpty(DateTimeUtils.formatMessageDate(new Date(item.createTime),true))){
                time = DateTimeUtils.formatMessageDate(new Date(item.createTime),true);
            }
            holder.time.setText(time);
            //设置大小：文件夹不显示
            holder.size.setVisibility(View.GONE);
            //设置右方菜单
            if (holder.rightIcon.getVisibility() == View.GONE){
                holder.rightIcon.setVisibility(View.VISIBLE);
            }
            holder.rightIcon.setImageResource(R.drawable.file_list_arrow);
            //清除点击事件
            holder.rightIcon.setOnClickListener(null);
        }else if (obj instanceof FileInfo){
            //如果是文件
            final FileInfo item = (FileInfo) obj;
            //设置图标
            holder.icon.setImageResource(FileConnectUtils.getCircleImgByFileType(item.fileExtension));
            //设置标题
            holder.title.setText(item.fileName+"."+item.fileExtension);
            //设置创建时间
            String time = I18NHelper.getText("meta.beans.InstanceState.3070")/* 未知 */;
            if (!TextUtils.isEmpty(DateTimeUtils.formatMessageDate(new Date(item.createTime),true))){
                time = DateTimeUtils.formatMessageDate(new Date(item.createTime),true);
            }
            holder.time.setText(time);
            //设置大小：文件夹不显示
            holder.size.setVisibility(View.VISIBLE);
            holder.size.setText(FileStorageUtils.formatFileSize(item.fileSize));
            //设置右方菜单
            if (item.isCEReadOnly && !mMyEA.equals(item.creatorEA)){
                holder.rightIcon.setVisibility(View.GONE);
            }else {
                holder.rightIcon.setVisibility(View.VISIBLE);
            }
            holder.rightIcon.setImageResource(R.drawable.board_history_show_more);
            //点击事件
            holder.rightIcon.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mFileLis!=null){
                        mFileLis.onMenuClick(position,item);
                    }
                }
            });
        }
        return convertView;
    }

    public void updateData(List<FolderInfo> folderInfo, List<FileInfo> fileInfo) {
        mFolderInfo = folderInfo;
        mFileInfo = fileInfo;
        notifyDataSetChanged();
    }

    public void setOnFileMenuClickLis(OnFileMenuClickLis lis) {
        mFileLis = lis;
    }

    /**
     * 刷新单条视图
     */
    public void refreshItem(int position,FileInfo fileInfo) {
        if (position>=mFolderInfo.size()&&position-mFolderInfo.size()<mFileInfo.size()){
             mFileInfo.set(position-mFolderInfo.size(),fileInfo);
            notifyDataSetChanged();
        }
    }

    static class ViewHolder{
        View line;
        ImageView icon;
        TextView title;
        TextView time;
        TextView size;
        ImageView rightIcon;
    }

    public interface OnFileMenuClickLis{
        void onMenuClick(int position,FileInfo fileInfo);
    }
    public interface OnFolderClickLis{
        void onMenuClick(FolderInfo folderInfo);
    }
}
