package com.facishare.fs.biz_session_msg.subbiz.select_customer_session.view;

import java.util.Collections;
import java.util.List;

import com.facishare.fs.biz_session_msg.adapter.select.base.BaseSelectSessionAdapter;
import com.facishare.fs.biz_session_msg.adapter.select.base.ISelectSessionViewItemData;

import android.content.Context;
import androidx.annotation.NonNull;

public class SelectCustomerSessionAdapter extends BaseSelectSessionAdapter {
    public SelectCustomerSessionAdapter(@NonNull Context context, List<ISelectSessionViewItemData> list) {
        super(context, list);
    }

    @Override
    public void updateDataList(List<ISelectSessionViewItemData> infos) {
//        filterDuplicateAndSort(mDataList, infos);
        // 排序
        Collections.sort(infos, new SelectSessionComparator());
        super.updateDataList(infos);
    }

}
