package com.facishare.fs.biz_session_msg.subbiz.select_customer_session.view;

import com.facishare.fs.biz_session_msg.SessionMsgActivity;
import com.facishare.fs.biz_session_msg.adapter.select.base.ISelectSessionTalkData;
import com.facishare.fs.biz_session_msg.adapter.select.base.ISelectSessionViewItemClick;
import com.facishare.fs.biz_session_msg.adapter.select.base.SelectSessionAdapterItemTypeEnum;
import com.facishare.fs.biz_session_msg.beans.QixinStatisticsEvent;
import com.facishare.fs.biz_session_msg.sessionsettings.GroupManageIndexActivity;
import com.facishare.fs.biz_session_msg.utils.QXActivitySwitchUtils;
import com.facishare.fs.biz_session_msg.utils.SessionInfoUtils;
import com.facishare.fs.biz_session_msg.views.view_ctrl.StringText;
import com.facishare.fs.biz_session_msg.views.view_ctrl.TextItem;
import com.facishare.fs.common_utils.StringUtils;
import com.facishare.fs.contacts_fs.beans.DiscussionGroup;
import com.facishare.fs.utils_fs.EmployeeLoaderUtils;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxlog.FCLog;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;

public class SelectDataDescribeForCustomerTalk implements ISelectSessionTalkData, ISelectSessionViewItemClick {
    Activity mActivity;
    SessionListRec mSession;
    String name = null;
    String zhongWenName;
    String pingYingName;
    int participantsCount;
    int mSortIndex;

    public SelectDataDescribeForCustomerTalk(Activity activity, int sortIndex, SessionListRec sessionListRec) {
        mActivity = activity;
        mSortIndex = sortIndex;
        mSession = sessionListRec;
        if (!sessionListRec.isTempSession() && SessionInfoUtils.isGroupSession(sessionListRec)) {
            participantsCount = sessionListRec.getParticipants() != null ? sessionListRec.getParticipants().size() : 0;
        }
        name = sessionListRec.getSessionName();
        setPingYingAndZhongWen();
    }


    public void setPingYingAndZhongWen() {
        if (mSession == null) {
            return;
        }
        //普通群设置名
        zhongWenName = mSession.getSessionName();
        String sessionNameSpell = mSession.getSessionNamePinyin();
        if (TextUtils.isEmpty(sessionNameSpell)) {
            try {
                pingYingName = StringUtils.getPingYin(zhongWenName);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            pingYingName = sessionNameSpell;
        }
    }

    @Override
    public SelectSessionAdapterItemTypeEnum getViewType() {
        return SelectSessionAdapterItemTypeEnum.View_Type_For_Talk;
    }

    @Override
    public int getSortIndex() {
        return mSortIndex;
    }

    @Override
    public SessionListRec getViewContent() {
        return mSession;
    }

    @Override
    public TextItem getName() {
        if (!TextUtils.isEmpty(name)) {
            return StringText.create(name);
        }
        if (mSession != null) {
            return EmployeeLoaderUtils.getTextItemBySession(mSession);
        }
        return StringText.create(name);
    }

    @Override
    public int getParticipantsCount() {
        return participantsCount;
    }

    @Override
    public void onClick(View view) {
        QixinStatisticsEvent.tick(QixinStatisticsEvent.CUSTOM_CHAT_SELECT_ENABLE_CLICK);
        if (mSession != null) {
            SessionMsgActivity.startIntent(mActivity, mSession);
            mActivity.setResult(QXActivitySwitchUtils.Action_type_exit);//让上一页面也销毁
            mActivity.finish();
        } else {
            FCLog.w("SelectTalk", "onClick failed to response by null mSession ");
        }
    }
}
