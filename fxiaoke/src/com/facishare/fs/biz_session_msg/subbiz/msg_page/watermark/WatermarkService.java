package com.facishare.fs.biz_session_msg.subbiz.msg_page.watermark;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.fscommon.view.WatermarkConfig;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiParameterList;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fshttp.web.http.WebApiUtils;

import android.text.TextUtils;

/**
 * Created by anjx on 2018/1/23.
 */

public class WatermarkService {

    private static String CONTROLLER = new String("FHE/EM1AQIXINEXT/WatermarkApi");

    public static void getWatermark(final WaterMarkCallback callback) {
        WebApiParameterList params = WebApiParameterList.create();
        WebApiUtils.buildFHEFullJsonDataType(params);
        WebApiUtils.postAsync(CONTROLLER, "getWatermark", params, new WebApiExecutionCallback<GetWatermarkResult>() {
            @Override
            public TypeReference<WebApiResponse<GetWatermarkResult>> getTypeReference() {
                return new TypeReference<WebApiResponse<GetWatermarkResult>>() {
                };
            }

            @Override
            public Class<GetWatermarkResult> getTypeReferenceFHE() {
                return GetWatermarkResult.class;
            }

            @Override
            public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                if (callback != null) {
                    callback.onFailed(error);
                }
            }

            @Override
            public void completed(Date date, GetWatermarkResult result) {
                if (result != null && callback != null) {
                    WaterMarkArgs waterMarkArgs = new WaterMarkArgs(result.isHasWatermark(), result.getWatermarkDesc());
                    waterMarkArgs.fontSpacing = result.getFontSpacing();
                    waterMarkArgs.serviceTime = result.getServiceTime();
                    waterMarkArgs.fontSize = result.getFontSize();
                    waterMarkArgs.fontColor = result.getFontColor();
                    waterMarkArgs.fontWeight = result.getFontWeight();
                    waterMarkArgs.type = result.getType();
                    waterMarkArgs.success = result.isSuccess();
                    callback.onGetWaterMark(waterMarkArgs);
                }
            }
        });
    }

    public static void getSessionWatermark(String sessionId, int env, final WaterMarkCallback callback) {
        WebApiParameterList params = WebApiParameterList.createWith("sessionId", sessionId).with("env", env);
        WebApiUtils.buildFHEFullJsonDataType(params);
        WebApiUtils.postAsync(CONTROLLER, "getSessionWatermark", params,
                new WebApiExecutionCallback<GetSessionWatermarkResult>() {
                    @Override
                    public TypeReference<WebApiResponse<GetSessionWatermarkResult>> getTypeReference() {
                        return new TypeReference<WebApiResponse<GetSessionWatermarkResult>>() {
                        };
                    }

                    @Override
                    public Class<GetSessionWatermarkResult> getTypeReferenceFHE() {
                        return GetSessionWatermarkResult.class;
                    }

                    @Override
                    public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                        if (callback != null) {
                            callback.onFailed(error);
                        }
                    }

                    @Override
                    public void completed(Date date, GetSessionWatermarkResult result) {
                        if (result != null && callback != null) {
                            callback.onGetWaterMark(
                                    new WaterMarkArgs(result.isHasWatermark(), result.getWatermarkDesc()));
                        }
                    }
                });
    }

    public interface WaterMarkCallback {
        void onGetWaterMark(WaterMarkArgs args);
        void onFailed(String error);
    }

    public static class WaterMarkArgs implements Serializable {
        public final boolean hasWatermark;
        public final String text;
        public String fontColor;
        public int fontSize;//取值1，2，3，分别代表14、16、和20号字
        public int fontSpacing;//取值1，2，3分别代表密，常规，大（垂直|水平： 80|120，100|160，120|160）
        public String fontWeight;//"fontWeight"取值 "bold"表示加粗，其他情况都默认不加粗,
        public long serviceTime;
        public boolean success;
        public int type;
        public WaterMarkArgs(boolean hasWatermark, String text) {
            this.hasWatermark = hasWatermark;
            this.text = text;
        }

        public boolean isHasWatermark() {
            return hasWatermark;
        }

        public String getText() {
            return text;
        }

        public String getFontColor() {
            return fontColor;
        }

        public void setFontColor(String fontColor) {
            this.fontColor = fontColor;
        }

        public int getFontSize() {
            return fontSize;
        }

        public void setFontSize(int fontSize) {
            this.fontSize = fontSize;
        }

        public int getFontSpacing() {
            return fontSpacing;
        }

        public void setFontSpacing(int fontSpacing) {
            this.fontSpacing = fontSpacing;
        }

        public String getFontWeight() {
            return fontWeight;
        }

        public void setFontWeight(String fontWeight) {
            this.fontWeight = fontWeight;
        }

        public long getServiceTime() {
            return serviceTime;
        }

        public void setServiceTime(long serviceTime) {
            this.serviceTime = serviceTime;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public static WaterMarkArgs parse(String jsonString) {
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(jsonString);
            String text = object.getString("text");
            if (TextUtils.isEmpty(text)) {
                text = object.getString("watermarkDesc");
            }
            boolean hasWatermark = false;
            try {
                hasWatermark = object.getBooleanValue("hasWatermark");
            } catch (Exception e) {
            }
            WatermarkService.WaterMarkArgs result = new WatermarkService.WaterMarkArgs(hasWatermark, text != null ? text : "");
            String fontColor = object.getString("fontColor");
            result.setFontColor(fontColor);
            String fontWeight = object.getString("fontWeight");
            result.setFontWeight(fontWeight);
            int fontSpacing = object.getIntValue("fontSpacing");
            result.setFontSpacing(fontSpacing);
            int fontSize = object.getIntValue("fontSize");
            result.setFontSize(fontSize);
            int type = object.getIntValue("type");
            result.setType(type);
            long serviceTime = object.getLongValue("serviceTime");
            result.setServiceTime(serviceTime);
            boolean success = object.getBooleanValue("success");
            result.setSuccess(success);
            return result;
        }
    }

    public static WatermarkConfig transToWatermarkConfig(WaterMarkArgs args) {
        if (args == null) {
            return null;
        }
        WatermarkConfig config = new WatermarkConfig();
        config.setHasWatermark(args.isHasWatermark());
        config.setText(args.getText());
        config.setFontColor(args.getFontColor());
        config.setFontSize(args.getFontSize());//取值1，2，3，分别代表14、16、和20号字
        config.setFontSpacing(args.getFontSpacing());//取值1，2，3分别代表密，常规，大（垂直|水平： 80|120，100|160，120|160）
        config.setFontWeight(args.getFontWeight());//"fontWeight"取值 "bold"表示加粗，其他情况都默认不加粗,
        config.setServiceTime(args.getServiceTime());
        config.setSuccess(args.isSuccess());
        config.setType(args.getType());
        return config;
    }

}
