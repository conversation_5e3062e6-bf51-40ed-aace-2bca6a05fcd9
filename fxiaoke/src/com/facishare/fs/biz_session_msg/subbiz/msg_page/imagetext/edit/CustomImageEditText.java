package com.facishare.fs.biz_session_msg.subbiz.msg_page.imagetext.edit;

import com.facishare.fs.biz_session_msg.views.MsgEditText;

import android.content.ClipboardManager;
import android.content.Context;
import android.text.SpannableStringBuilder;
import android.util.AttributeSet;

public class CustomImageEditText extends MsgEditText {
    public CustomImageEditText(Context context) {
        super(context);
    }

    public CustomImageEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CustomImageEditText(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    public boolean onTextContextMenuItem(int id) {
        if (id == android.R.id.paste) {//只更改粘贴文本
            ClipboardManager clip = (ClipboardManager)getContext().getSystemService(Context.CLIPBOARD_SERVICE);
            String text=clip.getPrimaryClip().getItemAt(0).getText().toString();
            SpannableStringBuilder finalData = new SpannableStringBuilder(getText());
            finalData.append(text.replaceAll(MixMessageEditViewProvider.IMAGE_SPAN_SLOT,""));
            setText(finalData);
            setSelection(finalData.length());
            return true;
        }
        return super.onTextContextMenuItem(id);
    }
}
