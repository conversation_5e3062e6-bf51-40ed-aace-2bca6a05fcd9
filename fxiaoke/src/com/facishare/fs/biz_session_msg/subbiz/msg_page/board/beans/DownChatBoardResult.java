package com.facishare.fs.biz_session_msg.subbiz.msg_page.board.beans;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.annotation.JSONCreator;

import java.io.Serializable;

public class DownChatBoardResult implements Serializable {
    private static final long serialVersionUID = -784270198387150543L;
    //返回码 1-成功，2-看板不存在，-1-失败
    @JSONField(name="M10")
    public Integer code;

    //提示文案
    @JSONField(name="M11")
    public String message;
    public DownChatBoardResult() {
        super();
    }
    @JSONCreator
    public DownChatBoardResult(@JSONField(name="M10")Integer code, @JSONField(name="M11")String message) {
        super();
        this.code = code;
        this.message = message;
    }
}
