package com.facishare.fs.biz_session_msg.subbiz.msg_page.title.right_action;

import com.facishare.fs.biz_session_msg.utils.SessionInfoUtils;
import com.facishare.fs.pluginapi.contact.beans.coustomer.CustomerService;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.SessionTypeKey;

import android.app.Activity;

/**
 * Created by anjx on 2017/11/24.
 */

public class RightActionFactory {

    public static BaseRightAction getRightAction(Activity activity, SessionListRec sessionInfo,
                                                 CustomerService customerService) {
        if (sessionInfo == null) {
            return null;
        }
        String category = sessionInfo.getSessionCategory();
        if (SessionInfoUtils.isSingleGroup(sessionInfo)) {
            return new SingleSessionRightAction(activity, sessionInfo);
        } else if (SessionInfoUtils.isGroupSession(sessionInfo)) {
            return new GroupSessionRightAction(activity, sessionInfo);
        } else if (category.equals(SessionTypeKey.Session_Second_Level)) {
            return new SingleSessionRightAction(activity, sessionInfo);
        } else if (category.equals(SessionTypeKey.Session_Out_User) && sessionInfo.getSessionSubCategory().startsWith(SessionTypeKey.Session_Out_User)) {
            return new SingleSessionRightAction(activity, sessionInfo);
        } else if (category.equals(SessionTypeKey.Session_Anonymous_User) && sessionInfo.getSessionSubCategory().startsWith(SessionTypeKey.Session_Anonymous_User)) {
            return new SingleSessionRightAction(activity, sessionInfo);
        } else if (category.equals(SessionTypeKey.Session_Open_service_stage)) {
            return new Oss1RightAction(activity, sessionInfo, customerService);
        } else if (category.equals(SessionTypeKey.Session_FsTuanDui_key)) {
            return new FSCustomServiceRightAction(activity, sessionInfo);
        } else if (category.equals(SessionTypeKey.Session_Fshelper_key)) {
            return null;
        }else if(SessionInfoUtils.isWeChatSingleSession(sessionInfo)){
            return new MsgPageWeChatSingleSessionRightAction(activity, sessionInfo);
        } else {
            return null;
        }
    }
}
