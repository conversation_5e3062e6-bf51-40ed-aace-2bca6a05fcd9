package com.facishare.fs.biz_session_msg.subbiz.msg_page.title.title_action;

import com.facishare.fs.HelpEntryCtr;
import com.facishare.fs.biz_session_msg.SessionDefinitionUtils;
import com.facishare.fs.biz_session_msg.utils.SessionInfoUtils;
import com.facishare.fs.pluginapi.contact.beans.coustomer.CustomerService;
import com.fxiaoke.fscommon_res.common_view.CommonTitleView;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.sessiondefine.SessionDefinition;

import android.app.Activity;
import androidx.annotation.NonNull;

/**
 * Created by yangwg on 2018/12/3.
 */

public class MsgPageOSS1SessionTitleAction extends MsgPageBaseTitleAction {
    CustomerService mCustomerService;

    MsgPageOSS1SessionTitleAction(Activity context, @NonNull CommonTitleView commonTitleView,
                                  SessionListRec sessionListRec) {
        super(context, commonTitleView, sessionListRec);
    }

    MsgPageOSS1SessionTitleAction(Activity context, @NonNull CommonTitleView commonTitleView,
                                  SessionListRec sessionListRec, CustomerService customerService) {
        this(context, commonTitleView, sessionListRec);
        this.mCustomerService = customerService;
    }

    HelpEntryCtr helpCtr = null;

    @Override
    protected void updateTitleBySession(SessionListRec newSession) {
        SessionDefinition definition = SessionDefinitionUtils.getDefinitionBySession(newSession);
        String sessionName = null;
        if (definition != null && definition.getData() != null) {
            sessionName = SessionInfoUtils.getSessionName(definition);
        } else if (mCustomerService != null) {
            sessionName = mCustomerService.appName;
        }
        mCommonTitleView.setTitle(sessionName);
        if (helpCtr == null && newSession.getSessionSubCategory().equals("weixinzhushou")) {
            helpCtr = new HelpEntryCtr(mContext, mCommonTitleView, true, "",
                    HelpEntryCtr.HelpEntryType.Weichat_Help_Type, sessionName);
            helpCtr.checkShowHelpEntry();
        }
    }
}
