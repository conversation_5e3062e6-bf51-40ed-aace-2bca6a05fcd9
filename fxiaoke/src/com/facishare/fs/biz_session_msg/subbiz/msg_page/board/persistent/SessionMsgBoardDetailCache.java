package com.facishare.fs.biz_session_msg.subbiz.msg_page.board.persistent;

import com.facishare.fs.biz_session_msg.subbiz.msg_page.board.beans.ChatBoardInfo;

import android.util.LruCache;

/**
 * Created by yangwg on 2019/2/20.
 */

public class SessionMsgBoardDetailCache {
    private volatile static SessionMsgBoardDetailCache instance;
    LruCache<String, ChatBoardInfo> mChatBoardInfoCache = null;

    private SessionMsgBoardDetailCache() {
        mChatBoardInfoCache = new LruCache<>(50);
    }

    public static SessionMsgBoardDetailCache getInstance() {
        if (instance == null) {
            instance = new SessionMsgBoardDetailCache();
        }
        return instance;
    }

    public void saveChatBoard(ChatBoardInfo item) {
        if (item != null) {
            mChatBoardInfoCache.put(item.boardId, item);
        }
    }

    public ChatBoardInfo getChatBoardInfoCache(String boardId) {
        return mChatBoardInfoCache.get(boardId);
    }
}
