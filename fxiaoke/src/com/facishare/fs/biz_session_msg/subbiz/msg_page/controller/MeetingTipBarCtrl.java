/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.subbiz.msg_page.controller;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.facishare.fs.biz_session_msg.SessionMsgActivity;
import com.facishare.fs.biz_session_msg.persistent.PersistentBySP;
import com.facishare.fs.biz_session_msg.subbiz.msg_page.IControllerLifeCycleListener;
import com.facishare.fs.biz_session_msg.subbiz.msg_page.IControllerSessionChangeListener;
import com.facishare.fs.biz_session_msg.subbiz.zoom_meeting.SessionMeetingGuideModule;
import com.facishare.fs.biz_session_msg.subbiz.zoom_meeting.SessionMeetingModule;
import com.facishare.fs.biz_session_msg.subbiz.zoom_meeting.bean.SessionMeetingData;
import com.facishare.fs.biz_session_msg.utils.SessionInfoUtils;
import com.facishare.fs.dialogs.CommonDialog;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fslib.R;
import com.fxiaoke.fscommon.util.FsUrlUtils;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxlog.FCLog;

public class MeetingTipBarCtrl implements IControllerLifeCycleListener, IControllerSessionChangeListener {

    private static final String TAG = MeetingTipBarCtrl.class.getSimpleName();

    private SessionMsgActivity.SessionContextProvider mSessionContextProvider;
    private TextView mMeetingTipBar;

    public MeetingTipBarCtrl(SessionMsgActivity.SessionContextProvider sessionContextProvider) {
        mSessionContextProvider = sessionContextProvider;
        mMeetingTipBar = sessionContextProvider.getBaseActivity().findViewById(R.id.zoom_meeting_tip_text_view);
    }

    @Override
    public void onCreate() {
        updateViewBySession(mSessionContextProvider.getSession());

        // 新功能引导
        Context context = mSessionContextProvider.getContext();
        SessionListRec session = mSessionContextProvider.getSession();
        // 单聊不显示引导，因为引导图只出一次，让它出现在企信群中更有价值，已和产品同学唐春林 确认过 2020.2.10
        // 为支持国际化，避免出现中英文混显的情况，移除在线会议的引导弹框
//        if (SessionInfoUtils.isGroupSession(session)) {
//            PersistentBySP sp = new PersistentBySP(context);
//            boolean needGuideZoom = SessionMeetingModule.canUseZoomMeeting() && !sp.isGuidedZoomMeeting();
//            boolean needGuideXYLink = SessionMeetingModule.canUseXYLinkMeeting() && !sp.isGuidedXYLinkMeeting();
//
//            if (needGuideZoom) {
//                sp.setGuidedZoomMeeting();
//                SessionMeetingGuideModule.showZoomMeetingGuideDialog(context, view -> {
//                    if (needGuideXYLink) {
//                        sp.setGuidedXYLinkMeeting();
//                        SessionMeetingGuideModule.showXYlinkMeetingGuideDialog(context, null);
//                    }
//                });
//            } else if (needGuideXYLink) {
//                sp.setGuidedXYLinkMeeting();
//                SessionMeetingGuideModule.showXYlinkMeetingGuideDialog(context, null);
//            }
//        }
    }

    @Override
    public void onReCreate() {
        updateViewBySession(mSessionContextProvider.getSession());
    }

    @Override
    public void onResume() {

    }

    @Override
    public void onPause() {

    }

    @Override
    public void onDestroy() {

    }

    @Override
    public void onSessionChange(SessionListRec oldSession, SessionListRec newSession) {
        if (newSession == null) {
            FCLog.w(TAG, "onSessionChange newSession is null");
            return;
        }
        if (!TextUtils.equals(oldSession.getSessionId(), newSession.getSessionId())
                || !TextUtils.equals(oldSession.getExtraDataMap(), newSession.getExtraDataMap())) {
            mSessionContextProvider.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    updateViewBySession(newSession);
                }
            });
        }
    }

    private void updateViewBySession(SessionListRec session) {
        SessionMeetingData meetingData = SessionMeetingData.makeZoomMeetingData(session);
        if (meetingData == null || !meetingData.isInMeeting()) {
            mMeetingTipBar.setVisibility(View.GONE);
            return;
        }
        //成功查询到人员名称时，就直接显示，否则等查询成功后再显示
        if (!TextUtils.isEmpty(meetingData.getCreatorName()) && !meetingData.getCreatorName().startsWith("ID")) {
            updateView(meetingData);
        } else {
            SessionMeetingData.makeZoomMeetingData(session, new SessionMeetingData.IReplaceCreatorDataCallBack() {
                @Override
                public void onNetDataBack(SessionMeetingData zoomMeetingData) {
                    updateView(meetingData);
                }
            });
        }
    }

    private void updateView(SessionMeetingData meetingData) {
        mSessionContextProvider.getBaseActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mMeetingTipBar.setVisibility(View.VISIBLE);
                mMeetingTipBar.setText(meetingData.getMeetingTip());
                mMeetingTipBar.setOnClickListener(view -> confirmBarViewClick(meetingData));
            }
        });
    }

    private void confirmBarViewClick(SessionMeetingData meetingData) {
        String title = "";
        if (meetingData.getType() == SessionMeetingData.TYPE_ZOOM) {
            title = I18NHelper.getText("qx.session.meeting.zoom_meeting")/* Zoom会议 */;
        } else if (meetingData.getType() == SessionMeetingData.TYPE_XYLINK) {
            title = I18NHelper.getText("qx.session.meeting.xylink_meeting")/* 小鱼会议 */;
        }
        String message = meetingData.getMeetingTip();

        final CommonDialog myDialog = new CommonDialog(mSessionContextProvider.getContext(), CommonDialog.FLAG_Message_And_Tip);
        CommonDialog.myDiaLogListener myDiaLogListener = new CommonDialog.myDiaLogListener() {
            @Override
            public void onClick(View view) {
                myDialog.dismiss();

                int i = view.getId();
                if (i == R.id.button_mydialog_cancel) {
                    finishMeeting();
                } else if (i == R.id.button_mydialog_enter) {
                    FsUrlUtils.gotoAction(mSessionContextProvider.getBaseActivity(), meetingData.getMeetingUrl());
                }
            }
        };
        myDialog.setDialogListener(myDiaLogListener);
        myDialog.setTitle(title);
        myDialog.setMessage(message);
        myDialog.setCanceledOnTouchOutside(true);
        myDialog.setProceesCancelClickWhenBackBtn(true);
        myDialog.setPositiveButton(I18NHelper.getText("qx.session.meeting.enter_meeting")/* 进入会议 */);
        myDialog.setNegativeButton(I18NHelper.getText("qx.session.meeting.end_meeting")/* 结束 */);
        myDialog.setNeutralButton(I18NHelper.getText("commonfunc.dialog_fragment_fsmail_choose_attachment_menu.text.cancel")/* 取消 */);
        myDialog.setPositiveButtonBold();
        myDialog.setNegativeButtonVisible(meetingData.canEndMeeting());
        myDialog.show();
    }

    private void finishMeeting() {
        SessionListRec session = mSessionContextProvider.getSession();

        mSessionContextProvider.getBaseActivity().showBaseLoadingDialog();
        SessionMeetingModule.FinishMeeting(session.getSessionId(), session.getEnterpriseEnvType(),
                new SessionMeetingModule.ZoomMeetingCallback<SessionMeetingModule.FinishMeetingResult>() {
                    @Override
                    public void onSuccess(SessionMeetingModule.FinishMeetingResult result) {
                        mSessionContextProvider.getBaseActivity().hideBaseLoadingDialog();
                    }

                    @Override
                    public void onFailed(String error) {
                        mSessionContextProvider.getBaseActivity().hideBaseLoadingDialog();
                        ToastUtils.show(error);
                    }
                });
    }
}
