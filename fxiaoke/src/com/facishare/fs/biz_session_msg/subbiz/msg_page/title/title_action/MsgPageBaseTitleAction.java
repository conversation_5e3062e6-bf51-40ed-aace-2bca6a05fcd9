package com.facishare.fs.biz_session_msg.subbiz.msg_page.title.title_action;

import java.util.List;

import com.facishare.fs.biz_session_msg.dialog.QxSessionTestDialog;
import com.facishare.fs.biz_session_msg.utils.MsgUtils;
import com.facishare.fs.biz_session_msg.utils.SessionInfoUtils;
import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.utils_fs.FsUtils;
import com.facishare.fs.utils_fs.SpeakerUtils;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fslib.R;
import com.fxiaoke.fscommon_res.common_view.CommonTitleView;
import com.fxiaoke.fscommon_res.utils.BrandColorRenderUtils;
import com.fxiaoke.fscommon_res.views.TextViewForResize;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.SessionParticipantSLR;

import android.app.Activity;
import android.graphics.Color;
import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.UiThread;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

/**
 * Created by yangwg on 2018/12/3.
 */

public abstract class MsgPageBaseTitleAction {
    protected SessionListRec mSessionInfo;
    protected Activity mContext;
    protected CommonTitleView mCommonTitleView;
    // title bar
    TextView mtvCenterText;
    TextView mTvTitleLabel, mtvTitleMiddleCount, mTvSpeak;
    TextViewForResize mtvNopush;
    long lastUpdateTime;
    boolean isFirst = true;
    public void init() {
        if (mSessionInfo == null || TextUtils.isEmpty(mSessionInfo.getSessionCategory())) {
            return;
        }
        refreshTitle(mSessionInfo);
        mCommonTitleView.requestLayout();
    }

    @UiThread
    @CallSuper
    public void refreshTitle(SessionListRec newSession) {
        boolean isNeedUpdate = false;
        if (isFirst || mSessionInfo == null && newSession != null
                || newSession != null && newSession.getUpdateTime() > lastUpdateTime
                ) {
            isFirst = false;
            isNeedUpdate = true;
        }
        if (isNeedUpdate) {
            mSessionInfo = newSession;
            lastUpdateTime = mSessionInfo.getUpdateTime();
            resetSubTitleContent(mSessionInfo);
            if (!MsgUtils.isGroupSession(mSessionInfo)) {
                mtvTitleMiddleCount.setText("");
            }
            checkShowNoStrongNotification();
            checkShowStickyEarpiece();
            updateCountTitle(newSession);
            updateSessionLabel(newSession);
            updateTitleBySession(newSession);
            mCommonTitleView.setMiddleLayoutMargin(8,8);
            mCommonTitleView.updateCenterTopLayout();
        }
    }

    protected void resetSubTitleContent(SessionListRec newSession){
        if (!SessionInfoUtils.isSingleSession(newSession)) {
            mCommonTitleView.setSubTitleAndAdjustCenterTextSize("");
        }
    }

    public void checkShowNoStrongNotification() {
        if (mSessionInfo.isSetNoStrongNotification()) {
            mtvNopush.setIsMeasured(false);
            mtvNopush.setVisibility(View.VISIBLE);
        } else {
            mtvNopush.setIsMeasured(false);
            mtvNopush.setVisibility(View.GONE);
        }
    }

    public void checkShowStickyEarpiece() {
        if (!SpeakerUtils.isStickyEarpieceMode()) {
            mTvSpeak.setVisibility(View.GONE);
        } else {
            mTvSpeak.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 刷新标题庞的人员数，按需要复写
     *
     * @param newSession
     */
    protected void updateCountTitle(SessionListRec newSession) {
    }

    /**
     * 给子类提供的工具方法，具体显示标题旁的人员数用
     *
     * @param newSession
     */
    protected void setCountTitle(SessionListRec newSession) {
        String c = SessionInfoUtils.getSessionStatusDes(newSession);
        if (TextUtils.isEmpty(c)) {
            List<SessionParticipantSLR> sp = newSession.getParticipants();
            if (sp != null && sp.size() > 0) {
                c = String.valueOf(sp.size());
            }
        }
        if (!TextUtils.isEmpty(c)) {
            mtvTitleMiddleCount.setText("(" + c + ")");
        }
    }

    protected void updateSessionLabel(SessionListRec newSession) {
        SessionInfoUtils.checkShowSimpleSessionLabelView(mContext, mTvTitleLabel, newSession, BrandColorRenderUtils.getSkinForegroundColor(mContext));
    }

    protected abstract void updateTitleBySession(SessionListRec newSession);

    MsgPageBaseTitleAction(Activity context, @NonNull CommonTitleView commonTitleView, SessionListRec sessionListRec) {
        mContext = context;
        mCommonTitleView = commonTitleView;
        mSessionInfo = sessionListRec;

        mtvCenterText = mCommonTitleView.getCenterTxtView();
        mtvTitleMiddleCount = mCommonTitleView.addTextViewToCenterTopLayout(0);
        mtvTitleMiddleCount.setTextColor(getTitleColor());
        mtvNopush = mCommonTitleView.addTextViewToCenterTopLayout(2, 18, 18, R.drawable.session_chatnotpush, null);
        mTvSpeak = mCommonTitleView.addTextViewToCenterTopLayout(2, 18, 18, R.drawable.speaker_er_bg, null);

        // 部门群、业务群、自定义对象群等显示群类型标签
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        params.rightMargin = FSScreen.dp2px(2);
        params.gravity = Gravity.CENTER_VERTICAL;
        mTvTitleLabel = mCommonTitleView.addTextViewToCenterTopLayout(params, 0);
        mTvTitleLabel.setTextColor(getTitleColor());
        mTvTitleLabel.setTextSize(TypedValue.COMPLEX_UNIT_PX, FSScreen.dp2px(10));
        mTvTitleLabel.setPadding(FSScreen.dp2px(4), FSScreen.dp2px(2), FSScreen.dp2px(4), FSScreen.dp2px(2));
        mTvTitleLabel.setEllipsize(TextUtils.TruncateAt.END);

        if (FsUtils.isInformal()) {
            mCommonTitleView.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    ToastUtils.showToast("onLongClick");
                    showTestDialog();
                    return false;
                }
            });
        }
    }

    private int getTitleColor() {
        return Color.parseColor("#181C25");
    }

    QxSessionTestDialog testDialog = null;

    private void showTestDialog() {
        if (null == testDialog) {
            testDialog = new QxSessionTestDialog(mContext, mSessionInfo);
        }
        testDialog.show();
    }
    public static interface ITitleUpdateCallBack{
        void onTitleChanged();
    }

    public void clear(){
        mtvCenterText.setText("");
        mCommonTitleView.removeCenterTopView(mtvTitleMiddleCount);
        mCommonTitleView.removeCenterTopView(mtvNopush);
        mCommonTitleView.removeCenterTopView(mTvSpeak);
        mCommonTitleView.removeCenterTopView(mTvTitleLabel);
    }
}
