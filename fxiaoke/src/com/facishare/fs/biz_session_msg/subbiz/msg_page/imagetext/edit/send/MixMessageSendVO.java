package com.facishare.fs.biz_session_msg.subbiz.msg_page.imagetext.edit.send;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.facishare.fs.App;
import com.facishare.fs.biz_feed.subbiz_send.feedsendapi.IFeedSendTask;
import com.facishare.fs.biz_session_msg.utils.AccountUtils;
import com.facishare.fs.biz_session_msg.utils.QXAtSubscribedBotHelperUtils;
import com.facishare.fs.common_datactrl.draft.BaseVO;
import com.facishare.fs.common_utils.time.NetworkTime;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.pluginapi.common_beans.Attach;
import com.facishare.fs.utils_fs.ToastUtils;
import com.fs.beans.beans.EnumDef;
import com.fs.fsprobuf.ServerProtobuf;
import com.fxiaoke.fshttp.web.ParamValue3;
import com.fxiaoke.fxdblib.beans.MixMessageContent;
import com.fxiaoke.fxdblib.beans.MixMessageElement;
import com.fxiaoke.fxdblib.beans.MixMessageElementType;
import com.fxiaoke.fxdblib.beans.MixMessageImageContent;
import com.fxiaoke.fxdblib.beans.MsgEntity;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.SessionMessageTemp;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.fxsocketlib.businessctrl.FcpTaskBase;
import com.fxiaoke.fxsocketlib.fcp.api.FcpErrorData;
import com.fxiaoke.fxsocketlib.utils.FcpUtils;
import com.fxiaoke.lib.qixin.client.impl.sendmsgimpl.SendMIXMessageClient;

import android.content.Context;
import android.text.TextUtils;
import android.util.Pair;
import de.greenrobot.event.EventBus;

public class MixMessageSendVO extends BaseVO implements Serializable {
    Context mContext;
    SessionListRec mSessionInfo;

    public MixMessageSendVO(Context context, SessionListRec sessionListRec) {
        mContext = context;
        mSessionInfo = sessionListRec;
    }

    @Override
    public void sendDraft(IFeedSendTask task, List<ParamValue3<Integer, String, Integer, String>> response) {
        SendMixMsgLogUtils.debugLog("MixMessageSendVO", "finish upload file and call SendMixMessage ", response);
        LinkedList<Attach> attachs = task.getAttachs();
        for (Attach attach : attachs) {
            if (EnumDef.FeedAttachmentType.ImageFile.value == attach.attachType && attach.attachLocalState == Attach.AttachType.ATTACH_NETWORK_TYPE) {
                for (MixMessageElement element : elementList) {
                    if (element.isImageType()
                            && element.getSendImageContent().getOriginalFileName().equals(attach.attachName) ) {
                        element.getSendImageContent().setTempFilePath(attach.attachPath);//不结束循环是考虑到可能选择了重复的图片
                        FCLog.d(MixMessageSendTaskManager.TAG, "MixMessageSendVO sendDraft updated tempFilePath "
                                + element.getSendImageContent());
                    }
                }
            }
        }
        sendMixMsg(task, createMixMsg(elementList));
    }

    private SessionMessageTemp createMixMsg(ArrayList<MixMessageElement> mixMessageElements) {
        SessionListRec rec = mSessionInfo;
        SessionMessageTemp smt = new SessionMessageTemp();
        smt.setClientPostId(FcpUtils.makeRequestUniqueId());
        smt.setMessageType("MIX");
        smt.setSessionid(rec.getSessionId());
        try {
            MixMessageContent mixMessageContent = new MixMessageContent();
            mixMessageContent.setElements(mixMessageElements);
            smt.setContent(JSON.toJSONString(mixMessageContent));
        } catch (Exception e) {
            e.printStackTrace();
        }
        smt.setPreviousMessageId(rec.getLastMessageId());
        smt.setMessageTime(System.currentTimeMillis());
        smt.setSenderId(AccountUtils.getMyID());
        smt.setFullSenderId(AccountUtils.getMyFullId());
        smt.setEnterpriseEnvType(rec.getEnterpriseEnvType());
        String postId = FcpUtils.makeRequestUniqueId();
        smt.setClientPostId(postId);
        StringBuilder allTextSB = new StringBuilder();
        if (mixMessageElements != null && mixMessageElements.size() > 0) {
            List<MsgEntity> entities = new ArrayList<>();
            for (MixMessageElement element : mixMessageElements) {
                if (element.isImageType() && element.getSendImageContent() != null) {
                    MsgEntity entity = new MsgEntity();
                    entity.setEntitytype(MsgEntity.TYPE_FOR_MIX_IMAGE);
                    entity.setLocalPath(element.getSendImageContent().getOriginalFilePath());
                    entity.setUniqueName(element.getSendImageContent().getOriginalFileName());
                    entities.add(entity);
                }
                if (element.isTextType()) {
                    allTextSB.append("[" + element.getContent() + "]");//为了区分段，避免错误匹配到人名
                }
            }
            smt.setEntities(entities);
        }
        if (allTextSB.length() > 0) {
            Pair<Boolean, List<String>> pair = QXAtSubscribedBotHelperUtils.getCurrentAtFullUserInfo(App.getContext(), mSessionInfo, allTextSB.toString());
            Boolean isClickAll = pair.first;
            List<String> atFullUserIdList = pair.second;
            smt.setClickAll(isClickAll);
            smt.setAtFullUserIdList(atFullUserIdList);
        }
        return smt;
    }

    long reqBegTime, reqEndTime;

    public void sendMixMsg(IFeedSendTask sendTask, SessionMessageTemp smt) {
        ServerProtobuf.EnterpriseEnv env = smt.getEnterpriseEnvType() == 1 ? //设置跨企业标识
                ServerProtobuf.EnterpriseEnv.CROSS : ServerProtobuf.EnterpriseEnv.INNER;
        reqBegTime = System.currentTimeMillis();
        new SendMIXMessageClient(mContext, env, smt) {
            @Override
            public void onSuccess(FcpTaskBase task, Boolean data) {
                super.onSuccess(task, data);
                if (FCLog.isDebugMode()) {
                    reqEndTime = System.currentTimeMillis();
                    SendMixMsgLogUtils.debugLog("MixMessageSendVO", "SendMixMessage onSuccess consume time:" + (reqEndTime - reqBegTime)+"ms");
                    ToastUtils.show("发送图文消息成功");/* ignore i18n *///l-18nIgnore
                }
                sendTask.sendSuccess(new Date(NetworkTime.getInstance(App.getInstance()).getServiceDateTime()));
                EventBus.getDefault().post(new MixMessageSendCompleteEvent(true));
            }

            @Override
            public void onFailed(FcpTaskBase task, Object data) {
                super.onFailed(task, data);
                CharSequence errorMsg = getFailedMsg(task);
                reqEndTime = System.currentTimeMillis();
                SendMixMsgLogUtils.debugLog("MixMessageSendVO", "SendMixMessage onFailed： " + errorMsg + " consume time:" + (reqEndTime - reqBegTime) + "ms");
                String showError = errorMsg+"";
                if (TextUtils.isEmpty(errorMsg)) {
                    showError = I18NHelper.getText("crm.fragment.LeadsToCustomerFrag.1235")/* 发送失败 */;
                }
                ToastUtils.show(showError);
                sendTask.sendFailed(null, 200, errorMsg.toString());
                EventBus.getDefault().post(new MixMessageSendCompleteEvent(false));
            }
        }.execute();
    }

    private CharSequence getFailedMsg(FcpTaskBase task) {
        FcpErrorData errorData = FcpUtils.getFailedInfoByTask(task);
        String errorMsg = errorData.errorMsg;
        if (TextUtils.isEmpty(errorMsg)) {
            errorMsg = errorData.i18nErrorMessage;
        }
        return errorMsg;
    }

    ArrayList<MixMessageElement> elementList = new ArrayList<>();

    public void setMixMessageElementList(List<MixMessageElement> elementList) {
        if (this.elementList != null) {
            this.elementList.clear();
        }
        if (upLoadFiles != null) {
            upLoadFiles.clear();
        }
        if (elementList != null) {
            this.elementList.addAll(elementList);
        } else {
            this.elementList.clear();
        }
        for (MixMessageElement element : elementList) {
            if (element.isImageType() && element.getSendImageContent() != null) {
                MixMessageImageContent imageContent = element.getSendImageContent();
                Attach attach = new Attach(imageContent.getOriginalFileName(), imageContent.getOriginalFilePath(), EnumDef.FeedAttachmentType.ImageFile.value);
                attach.mIsSendByUnzipped = element.getSendImageContent().isHD();
                attach.fileLat = imageContent.getFileLat();
                attach.fileLon = imageContent.getFileLon();
                if(!TextUtils.isEmpty(imageContent.getTempFilePath())){
                    attach.attachLocalState = Attach.AttachType.ATTACH_NETWORK_TYPE;
                    attach.attachPath = imageContent.getTempFilePath();
                }
                if (mSessionInfo != null) {
                    attach.setEnvType(mSessionInfo.getEnterpriseEnvType());
                }
                attach.uploadedTime = imageContent.getTempFileTime();
                attach.originalPath = imageContent.getOriginalFilePath();
                attach.filelocation = imageContent.getFileLocation();
                //                copyFromDBAttach(attach);
                addUpLoadImageFile(attach);
            }
        }
    }

    @Override
    public boolean isInsertable() {
        return false;
    }
}
