/*
 * Copyright (C) 2021 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.subbiz.grouping.list;

import java.util.ArrayList;
import java.util.List;

import com.facishare.fs.BaseFragmentActivity;
import com.facishare.fs.biz_session_msg.datactrl.SessionListViewCtr;
import com.facishare.fs.biz_session_msg.datactrl.session_checker.SpecifiedGroupingSessionChecker;
import com.facishare.fs.biz_session_msg.datactrl.session_filter.GroupingSessionSelectFilter;
import com.facishare.fs.biz_session_msg.datactrl.session_filter.ISessionFilter;
import com.facishare.fs.biz_session_msg.dialog.CustomizeGroupEditDialog;
import com.facishare.fs.biz_session_msg.dialog.DialogButtonCallBak;
import com.facishare.fs.biz_session_msg.sessionlist.multi_select.MultiSelectGroupActivity;
import com.facishare.fs.biz_session_msg.sessionsettings.utils.SessionSettingsUtils;
import com.facishare.fs.biz_session_msg.subbiz.grouping.CustomizedGroupSessionDataSynchronizer;
import com.facishare.fs.contacts_fs.picker.DepartmentPicker;
import com.facishare.fs.dialogs.CustomListDialog;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fslib.R;
import com.fxiaoke.dataimpl.msg.ICustomizedGroupChangedListener;
import com.fxiaoke.fscommon_res.utils.BrandColorRenderUtils;
import com.fxiaoke.fxdblib.CustomizedGroupDataHelper;
import com.fxiaoke.fxdblib.beans.CustomizedGroup;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.lib.qixin.biz_ctrl.SessionMsgHelper;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

public class CustomizedGroupSessionListActivity extends BaseFragmentActivity {
    SessionListViewCtr mLisViewCtr;
    ListView mListView;
    String mParentSessionId;
    CustomizedGroup mCustomizedGroup = null;
    private int REQUEST_CODE_MULTI_SELECT_SESSION = 0x01;//进入选择会话添加到当前分组中的界面
    boolean isEditState = false;
    View mSettingsTitleBtn;
    View mShowMoreTitleBtn;

    public static void start(Context context, String sourceSessionsParentSessionId, CustomizedGroup groupData) {
        Intent intent = new Intent(context, CustomizedGroupSessionListActivity.class);
        intent.putExtra("sourceSessionsParentSessionId", sourceSessionsParentSessionId);
        intent.putExtra("CustomizedGroupData", groupData);
        context.startActivity(intent);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_customized_group_session_list);
        initData();
        initView();
    }

    private void initData() {
        Intent intent = getIntent();
        if (intent != null) {
            mParentSessionId = intent.getStringExtra("sourceSessionsParentSessionId");
            mCustomizedGroup = (CustomizedGroup) intent.getSerializableExtra("CustomizedGroupData");
            //            mTitle = intent.getStringExtra("title");
        }
        //        ParentSessionCache.getInstance().saveSession(mParentSessionId);
        //        FcpConnectEnvCtrl.getInstance().addFcpConnectEnvlistener(this);
        //        myID = FSContextManager.getCurUserContext().getAccount().getEmployeeIntId();
        //        ObservableCenter.getInstance().addObserver(this);
    }

    View mAddSessionQuickEntry, mAddSessionQuickEntryLayout;

    private void initView() {
        initTitle();
        initEditGroupLayout();

        mListView = findViewById(R.id.shortMessageListView);
        mLisViewCtr = new SessionListViewCtr(this, mListView, mCommonTitleView, mParentSessionId);
        mLisViewCtr.addSessionChecker(new SpecifiedGroupingSessionChecker(mCustomizedGroup.getGroupId()));//添加检查器一定要在初始显示列表前
        mLisViewCtr.setGroupSessionChangeLister(mGroupChangedListener);
        CustomizedGroupDataHelper dataHelper = new CustomizedGroupDataHelper(new SessionMsgHelper().getChatDbHelper(this));
        List<SessionListRec> slrList = null;
        if (mCustomizedGroup != null & !TextUtils.isEmpty(mCustomizedGroup.getGroupId())) {
            slrList = dataHelper.getGroupingSessionList(mParentSessionId, mCustomizedGroup.getGroupId());
        }
        mLisViewCtr.init(slrList, false, false);

        //触发网络请求前设置监听
        //        BizListenerManager.registerSecondSessionListener(App.getInstance(), this);
        mAddSessionQuickEntry = findViewById(R.id.btn_add_session);
        BrandColorRenderUtils.changeViewBackgroundDrawable(mAddSessionQuickEntry);
        mAddSessionQuickEntryLayout = findViewById(R.id.btn_add_session_layout);
        mAddSessionQuickEntry.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addSessionInToCurrentGroup();
            }
        });
        checkToShowAddSessionQuickEntry();
    }

    private void addSessionInToCurrentGroup() {
        ArrayList<ISessionFilter> sessionFilters = new ArrayList<>();
        sessionFilters.add(new GroupingSessionSelectFilter(mCustomizedGroup.getGroupId()));
        MultiSelectGroupActivity.start(CustomizedGroupSessionListActivity.this, REQUEST_CODE_MULTI_SELECT_SESSION,
                mParentSessionId, sessionFilters);
    }

    TextView mEditLayoutRightGroupNameView;
    View mAddSessionEntryInEditState;

    private void initEditGroupLayout() {
        //编辑组名称
        mEditLayoutRightGroupNameView = findViewById(R.id.group_name_for_edit);
        mEditLayoutRightGroupNameView.setText(mCustomizedGroup.getName());
        mEditLayoutRightGroupNameView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                String title = I18NHelper.getText("qx.chating.session.grouping.change_group_name", "编辑名称");
                String title = I18NHelper.getText("qx.chating.session.grouping.modify_group_name.desc", "编辑分组名称");
                CustomizeGroupEditDialog.showDialog(CustomizedGroupSessionListActivity.this, title, mCustomizedGroup.getName(),
                        mConfirmEditGroupNameListener);
            }
        });

        mAddSessionEntryInEditState = findViewById(R.id.add_session_layout);
        mAddSessionEntryInEditState.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addSessionInToCurrentGroup();
            }
        });
        ImageView addGroupImageView = findViewById(R.id.addGroupImageView);
        BrandColorRenderUtils.changeImageViewDrawable(addGroupImageView, R.drawable.create_session_group);
        TextView addGroupTextView = findViewById(R.id.addGroupTextView);
        BrandColorRenderUtils.changeTextViewColor(addGroupTextView);
    }

    CustomizeGroupEditDialog.EditDialogClickListener mConfirmEditGroupNameListener = new CustomizeGroupEditDialog.EditDialogClickListener() {
        @Override
        public void onConfirm(DialogInterface dialog, String input) {
            //            ToastUtils.show("test-更新分组成功");
            //            CustomizedGroup testData = new CustomizedGroup();
            //            testData.setUpdateTime(System.currentTimeMillis());
            //            testData.setGroupId(mCustomizedGroup.getGroupId());
            //            testData.setName("改名成功" + Math.random());
            //            checkUpdateGroupName(testData);
            CustomizedGroupSessionDataSynchronizer.changeGroupName(CustomizedGroupSessionListActivity.this, mCustomizedGroup.getGroupId(), input,
                    new CustomizedGroupSessionDataSynchronizer.CallBack<CustomizedGroup>() {
                        @Override
                        public void onSuccess(CustomizedGroup data) {
                            checkUpdateGroupName(data);
                        }

                        @Override
                        public void onFailed(String error) {
                            ToastUtils.show(error);
                        }
                    });
        }
    };

    private void checkToShowAddSessionQuickEntry() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (!mLisViewCtr.hasVisibleItem()) {
                    if (!isEditState) {
                        mAddSessionQuickEntryLayout.setVisibility(View.VISIBLE);
                    }
                    mListView.setVisibility(View.GONE);
                } else {
                    mAddSessionQuickEntryLayout.setVisibility(View.GONE);
                    mListView.setVisibility(View.VISIBLE);
                }
            }
        });
    }

    private void initTitle() {
        initTitleCommon();
        mCommonTitleView.addLeftAction(R.string.btn_title_back, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isEditState) {
                    quitEditState();
                } else {
                    close();
                }
            }
        });
        if (mCustomizedGroup != null) {
            mCommonTitleView.setMiddleText(mCustomizedGroup.getName());
        }

        mSettingsTitleBtn = mCommonTitleView.addRightAction(R.string.barbuttonicon_setting, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                enterEditState();
            }
        });

        mShowMoreTitleBtn = mCommonTitleView.addRightAction(R.string.more_icon, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clickMoreAction(v);
            }
        });
        mShowMoreTitleBtn.setVisibility(View.GONE);
    }

    /**
     * 进入编辑态
     */
    private void enterEditState() {
        //        ToastUtils.show("点击设置进入编辑态");
        isEditState = true;
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mSettingsTitleBtn.setVisibility(View.GONE);
                mShowMoreTitleBtn.setVisibility(View.VISIBLE);
                mAddSessionQuickEntryLayout.setVisibility(View.GONE);
                showEditGroupLayout();
            }
        });
    }

    private void showEditGroupLayout() {
        mCommonTitleView.setMiddleText(I18NHelper.getText("qx.chating.session.grouping.grouping_settings.desc", "分组设置"));
        findViewById(R.id.edit_group_root_layout).setVisibility(View.VISIBLE);
        mLisViewCtr.setOnlyCanChangeCustomizedGroup(isEditState);
        //        mLisViewCtr.setViewItemCur(true);
    }

    /**
     * 退出编辑态
     */
    private void quitEditState() {
        //        ToastUtils.show("点击设置进入编辑态");
        isEditState = false;
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mSettingsTitleBtn.setVisibility(View.VISIBLE);
                mShowMoreTitleBtn.setVisibility(View.GONE);
                mCommonTitleView.setMiddleText(mCustomizedGroup.getName());
                findViewById(R.id.edit_group_root_layout).setVisibility(View.GONE);
                mLisViewCtr.setOnlyCanChangeCustomizedGroup(isEditState);
                checkToShowAddSessionQuickEntry();
            }
        });
    }

    private void clickMoreAction(View v) {
        CustomListDialog menu = new CustomListDialog(this);
        menu.setTitle(I18NHelper.getText("qx.chating.session.grouping.grouping_settings.desc")/* 分组设置 */);
        String[] itemsDep = new String[] {
                I18NHelper.getText("qx.chating.session.grouping.disband_group.desc")/* 解散分组 */
        };
        menu.setMenuContent(itemsDep, new DialogInterface.OnClickListener() {

            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.cancel();
                switch (which) {
                    case 0:
                        confirmDissolveCustomizedGroup();
                        break;
                    default:
                        break;
                }
            }
        });
        menu.show();
    }

    private void confirmDissolveCustomizedGroup() {
        //TODO： 需要确认，如果当前分组内没有会话了，是否不需要弹框提示 “ 分组内的会话不会被删除，将移动到企业互联中”
        /*分组内的会话不会被删除，将移动到企业互联中*/
        String message = I18NHelper.getText("qx.chating.session.grouping.disband_group_title.desc");
        //        QixinStatisticsEvent.groupSpaceTick(QixinStatisticsEvent.MS_GROUP_SPACE_EXIT_GROUP,mSessionInfo);
        SessionSettingsUtils.showConfirmDialog(this, "", message, I18NHelper.getText("commonfunc.dialog_fragment_fsmail_choose_attachment_menu.text.cancel")/* 取消 */,
                I18NHelper.getText("av.common.string.confirm")/* 确定 */, new DialogButtonCallBak() {
                    @Override
                    public void onPositive(Object dialog) {
                        //                        QixinStatisticsEvent.groupSpaceTick(QixinStatisticsEvent.MS_GROUP_SPACE_EXIT_GROUP_CONFIRMED,mSessionInfo);
                        dissolveCustomizedGroup();
                    }

                    @Override
                    public void onNegative(Object dialog) {
                        //                        QixinStatisticsEvent.groupSpaceTick(QixinStatisticsEvent.MS_GROUP_SPACE_EXIT_GROUP_CANCELED,mSessionInfo);
                    }
                });
    }

    private void dissolveCustomizedGroup() {
        //        ToastUtils.show("触发解散分组并成功，退出编辑态");
        CustomizedGroupSessionDataSynchronizer.dissolveCustomizedGroup(this,
                mCustomizedGroup.getGroupId(), new CustomizedGroupSessionDataSynchronizer.CallBack<CustomizedGroup>() {
                    @Override
                    public void onSuccess(CustomizedGroup data) {
                        processDeleteCurrentGroupEvent(data);
                        ToastUtils.show(I18NHelper.getText("qx.chating.session.grouping.dissolve_group_ok.desc") /*解散成功*/);
                    }

                    @Override
                    public void onFailed(String error) {
                        ToastUtils.show(error);
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_MULTI_SELECT_SESSION) {
            if (data != null && resultCode == RESULT_OK) {
                //触发添加会话到分组的接口，成功后刷新界面数据显示
                addSessionListToCustomizedGroup();
            }
        }
    }

    private void addSessionListToCustomizedGroup() {
        List<String> sessionIds = new ArrayList<>(DepartmentPicker.getGroupMapPicked().keySet());
        if(sessionIds.isEmpty()){
            return;
        }
        //TODO: 目前先写死是cross类型
        CustomizedGroupSessionDataSynchronizer
                .addSessionListToCustomizedGroup(this, 1, mCustomizedGroup.getGroupId(), sessionIds,
                        new CustomizedGroupSessionDataSynchronizer.CallBack<List<SessionListRec>>() {
                            @Override
                            public void onSuccess(List<SessionListRec> data) {
                                mLisViewCtr.addListData(data, mParentSessionId);
                                checkToShowAddSessionQuickEntry();
                                ToastUtils.show(I18NHelper.getText("qx.chating.session.grouping.add_session_ok.desc") /*添加会话成功*/);
                            }

                            @Override
                            public void onFailed(String error) {
                                ToastUtils.show(error);
                            }
                        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            // saveSendInfo();
            if (isEditState) {
                quitEditState();
                return true;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    public void checkUpdateGroupName(CustomizedGroup updateGroup) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (mCustomizedGroup != null && updateGroup != null && TextUtils.equals(mCustomizedGroup.getGroupId(), updateGroup.getGroupId())) {
                    if (updateGroup.getUpdateTime() > mCustomizedGroup.getUpdateTime()) {
                        mCommonTitleView.setMiddleText(updateGroup.getName());
                        mEditLayoutRightGroupNameView.setText(updateGroup.getName());
                        CustomizedGroupSessionDataSynchronizer.assignGroupData(mCustomizedGroup, updateGroup);
                    }
                }
            }
        });
    }

    ICustomizedGroupChangedListener mGroupChangedListener = new ICustomizedGroupChangedListener() {
        @Override
        public void onAddGroup(CustomizedGroup addedGroup) {

        }

        @Override
        public void onDeleteGroup(CustomizedGroup deletedGroup) {
            //暂不自动响应其他端的分组删除事件：
            //            if (mCustomizedGroup != null && deletedGroup != null && TextUtils.equals(mCustomizedGroup.getGroupId(), deletedGroup.getGroupId())) {
            //                processDeleteCurrentGroupEvent(deletedGroup);
            //            }
        }

        @Override
        public void onUpdateGroup(CustomizedGroup updateGroup) {
            checkUpdateGroupName(updateGroup);
        }

        @Override
        public void onUpdateAllGroupList(List<CustomizedGroup> updateGroupList) {
            for (CustomizedGroup group : updateGroupList) {
                checkUpdateGroupName(group);
            }
        }

        @Override
        public void onUpdateSessionGroup(String oldGroupId, String newGroupId, List<SessionListRec> updateSessionList) {
            checkToShowAddSessionQuickEntry();
        }

        @Override
        public void onUpdateSessionGroupList(List<CustomizedGroup> addGroupList, List<CustomizedGroup> deleteGroupList, List<SessionListRec> changedGroupIdSessionList) {
            checkToShowAddSessionQuickEntry();
        }
    };

    //
    private void processDeleteCurrentGroupEvent(CustomizedGroup deletedGroup) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                close();
            }
        });
    }
}
