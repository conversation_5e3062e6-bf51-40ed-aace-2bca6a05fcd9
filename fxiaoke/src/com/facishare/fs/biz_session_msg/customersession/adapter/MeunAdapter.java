package com.facishare.fs.biz_session_msg.customersession.adapter;

// Copyright (c) 2016 ${ORGANIZATION_NAME}. All rights reserved.

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.facishare.fslib.R;
import com.facishare.fs.biz_function.appcenter.adapter.FSBaseAdapter;
import com.facishare.fs.biz_session_msg.customersession.bean.MenuItem;
import com.facishare.fs.biz_session_msg.views.CustomAppMenuView;
import com.facishare.fs.common_utils.FSScreen;
import com.fxiaoke.fscommon.adapter.NormalBaseAdapter;

import java.util.List;

/**
 * Created by 钟光燕 on 2016/11/11.
 * e-mail <EMAIL>
 */

public class MeunAdapter extends FSBaseAdapter<MenuItem> {
//    List<MenuItem> mData;

    public MeunAdapter(Context mContext, List<MenuItem> mListData) {
        super(mContext, mListData);
//        mData = mListData;
    }

//    @Override
//    public View getView(int position, View convertView, ViewGroup parent) {
//        ViewHolder holder = null;
//        if (convertView == null) {
//            holder = new ViewHolder();
//            convertView = LayoutInflater.from(mCtx).inflate(R.layout.menu_item, null);
//            TextView tv = (TextView) convertView.findViewById(R.id.id_menu_text);
//            holder.name = tv;
//            holder.line = convertView.findViewById(R.id.line);
//            convertView.setTag(holder);
//        }else{
//            holder = (ViewHolder) convertView.getTag();
//        }
//        if( mData == null || mData.size() <= 0){
//            return null;
//        }
//        MenuItem item = mData.get(position);
//        holder.name.setText(item.title);
//        RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) holder.line.getLayoutParams();
//        lp.width = LinearLayout.LayoutParams.MATCH_PARENT;
//        holder.line.setLayoutParams(lp);
//        RelativeLayout.LayoutParams rlp = (RelativeLayout.LayoutParams) holder.name.getLayoutParams();
//        if (position == 0 ){
//            rlp.topMargin = rlp.topMargin - FSScreen.dip2px(3);
//        }
//        if (position == getCount() - 1) {
//            holder.line.setVisibility(View.GONE);
//            rlp.bottomMargin = rlp.bottomMargin - FSScreen.dip2px(3);
//        }else {
//            holder.line.setVisibility(View.VISIBLE);
//        }
//        holder.name.setLayoutParams(rlp);
//        holder.name.measure(View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED), View.MeasureSpec.makeMeasureSpec
//                (0, View.MeasureSpec.UNSPECIFIED));
//        return convertView;
//    }
//
//
//    class ViewHolder {
//        TextView name;
//        View     line;
//    }


        @Override
    public ViewHolder createViewHolder(LayoutInflater inflater, int position) {
        return new ViewHolder(inflater.inflate(R.layout.menu_item,null));
    }

    @Override
    public void onBindViewHolder(BaseHolder holder, MenuItem data, int position) {
        ViewHolder viewHolder = (ViewHolder)holder;
        if(data != null && !TextUtils.isEmpty(data.title)){
            viewHolder.mMenuTitleTv.setText(data.title);
        }

    }

    public class ViewHolder extends BaseHolder {
        private TextView mMenuTitleTv;
        public ViewHolder(View item) {
            super(item);
            mMenuTitleTv = findView(R.id.id_menu_text);
        }
    }
}
