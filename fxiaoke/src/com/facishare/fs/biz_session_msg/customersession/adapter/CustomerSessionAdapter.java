/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.customersession.adapter;

import com.facishare.fs.i18n.I18NHelper;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import com.facishare.fs.App;
import com.facishare.fslib.R;
import com.facishare.fs.biz_function.appcenter.adapter.FSBaseAdapter;
import com.facishare.fs.biz_session_msg.adapter.SourceType;
import com.facishare.fs.biz_session_msg.customersession.CustomerSessionManager;
import com.facishare.fs.biz_session_msg.customersession.SessionUtils;
import com.facishare.fs.biz_session_msg.customersession.type.CustomerSessionBase;
import com.facishare.fs.biz_session_msg.views.PicMaskImageView;
import com.facishare.fs.common_utils.DateTimeUtils;
import com.facishare.fs.contacts_fs.customerservice.util.CustomerStatistics;
import com.facishare.fs.utils_fs.ImageLoaderUtil;
import com.fxiaoke.dataimpl.msg.MsgDataController;
import com.fxiaoke.fxdblib.beans.MsgTypeKey;
import com.fxiaoke.fxdblib.beans.SecondLevelSession;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.SessionMessage;
import com.fxiaoke.fxdblib.beans.SessionMessageTemp;
import com.fxiaoke.stat_engine.StatEngine;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.rockerhieu.emojicon.CustomFaceHandler;
import com.rockerhieu.emojicon.EmojiconTextView;

import android.content.Context;
import android.database.DataSetObserver;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

/**
 * Created by 钟光燕 on 2016/4/12.
 * ===================================================
 * <p>
 * code is m h l
 * <p>
 * ===================================================
 */
@Deprecated
public class CustomerSessionAdapter extends FSBaseAdapter<SecondLevelSession> {

    private SessionListRec mParentSession ;
    private HashSet<String> mOutLinkSet;
    public CustomerSessionAdapter(Context mContext, SessionListRec parentSession,List<SecondLevelSession> mListData) {
        super(mContext, mListData);
        mParentSession = parentSession ;
        Collections.sort(mListData, new SessionUtils.ComparatorCustomerSessionList());
        CustomerSessionManager.getInstance().register(mParentSession,mContext);
    }

    public void setOutLink(HashSet<String> set) {
        mOutLinkSet = set;
        notifyDataSetChanged();
    }

    @Override
    public BaseHolder createViewHolder(LayoutInflater inflater, int position) {
        return new ViewHolder(inflater.inflate(R.layout.item_customer_session,null));
    }

    @Override
    public void onBindViewHolder(BaseHolder holder, final SecondLevelSession data, int position) {
        ViewHolder viewHolder = (ViewHolder) holder;
        initViewHolder(viewHolder);
        if (data.isSetAsSticky()) {
            viewHolder.item.setBackgroundResource(R.drawable.list_item_up_select);
        }

        final CustomerSessionBase sessionBase = CustomerSessionManager.getInstance().findSession(data);
        if(sessionBase == null){
            return;
        }
        viewHolder.mName.setText(sessionBase.getSessionName(data));
        ImageLoader.getInstance().displayImage(sessionBase.getHeaderIcon(data), viewHolder.mHeader,
                ImageLoaderUtil.getUserHeadImgDisplayImageOptionsForRoundedDefault(getContext()));
//        if (data.isNotReadFlag() ){
//            viewHolder.mNotify.setVisibility(View.VISIBLE);
//        }

        if (data.getNotReadCount() > 0){
            viewHolder.mNotify.setVisibility(View.GONE);
            viewHolder.mUnReadCount.setVisibility(View.VISIBLE);
            viewHolder.mUnReadCount.setText(data.getNotReadCount()+"");
        }
        if(data != null && mOutLinkSet != null) {
            if(mOutLinkSet.contains(data.getSessionId())) {
                viewHolder.mOutLinkIcon.setVisibility(View.VISIBLE);
            } else {
                viewHolder.mOutLinkIcon.setVisibility(View.GONE);
            }
        }

        long time=data.getOrderingTime();
        if (time < 0 ){
            time = data.getUpdateTime() ;
        }
        viewHolder.mTime.setText(DateTimeUtils.formatMessageDate(new Date(time), false));
        if (position == getCount() - 1) {
            viewHolder.mBottomLine.setVisibility(View.GONE);
        }
        updateSessionContent(data,viewHolder.mContent,sessionBase) ;
        viewHolder.item.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                StatEngine.tick(CustomerStatistics.CUSTOMER_SESSION_SL,
                        MsgDataController.getInstace(App.getInstance()).getServiceId(data.getParentSessionId()));
                sessionBase.onClickSession(getContext(),data);
            }
        });
        viewHolder.item.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                sessionBase.onLongClickSession(data);
                return true;
            }
        });
    }
    public void updateUnReadFlagSession(String id){
        for (SecondLevelSession ori : getListData()) {
            if (TextUtils.equals(ori.getSessionId(),id)){
                if (ori.isNotReadFlag()||ori.getNotReadCount() > 0){
                    ori.setNotReadFlag(false);
                    ori.setNotReadCount(0);
                    MsgDataController.getInstace(App.getInstance()).updateReadFlagForCustomerSession(ori, null) ;
                }
                break;
            }

        }
        notifyDataSetChanged();
    }
    public void updateUnReadFlagSession(SecondLevelSession session){
        for (SecondLevelSession ori : getListData()) {
            if (TextUtils.equals(ori.getSessionId(),session.getSessionId())){
                ori.setNotReadFlag(session.isNotReadFlag());
                ori.setNotReadCount(session.getNotReadCount());
                break;
            }

        }
        notifyDataSetChanged();
    }
    public void updateLastMsg(String sid){
        for (SecondLevelSession ori :getListData()) {
            if (ori.getSessionId().equals(sid)) {
                notifyDataSetChanged();
                break;
            }
        }
    }
    public void updateSessionRec(SessionMessage msg){
        for (SecondLevelSession session : getListData()) {
            if (session.getSessionId().equals(msg.getSessionid())
                    && (session.getLastMessageId() < msg.getMessageId())) {
                setLastMsgSummary(session,msg);
                session.setUpdateTime(msg.getMessageTime());
                session.setLastMessageStatus(msg.getMsgSendingStatus());
                session.setLastMessageType(msg.getMessageType());
                session.setLastMessageTime(msg.getMessageTime());
                session.setLastMessageSenderId(msg.getSenderId());
                Collections.sort(getListData(), new SessionUtils.ComparatorCustomerSessionList());
                notifyDataSetChanged();
                break;
            }
        }
    }
    public void updateSessionRec(SessionMessageTemp msg){
        for (SecondLevelSession session : getListData()) {
            if (session.getSessionId().equals(msg.getSessionid())) {
                setLastMsgSummary(session,msg);
                session.setUpdateTime(msg.getMessageTime());
                session.setLastMessageStatus(msg.getMsgSendingStatus());
                session.setLastMessageType(msg.getMessageType());
                session.setLastMessageTime(msg.getMessageTime());
                session.setLastMessageSenderId(msg.getSenderId());
                session.setLastMessageFullSenderId(SourceType.human);
                Collections.sort(getListData(), new SessionUtils.ComparatorCustomerSessionList());
                notifyDataSetChanged();
                break;
            }
        }
    }
    public void addSessionList(List<SecondLevelSession> sessions){
        if (sessions.isEmpty()){
            return;
        }
        List<SecondLevelSession> removeSession = new ArrayList<>() ;
        int count = getCount() ;
        for (SecondLevelSession session : sessions){
            for (int i = 0 ; i < count ; i ++){
                if(TextUtils.equals(session.getSessionId(),getItem(i).getSessionId())){
                    removeSession.add(getItem(i));
                    break;
                }
            }
        }
        getListData().removeAll(removeSession) ;
        getListData().addAll(filter(sessions)) ;
        Collections.sort(getListData(), new SessionUtils.ComparatorCustomerSessionList());
        notifyDataSetChanged();
    }
    private List<SecondLevelSession> filter(List<SecondLevelSession> sessions){
        List<SecondLevelSession> sessions1 = new ArrayList<>() ;
        for (SecondLevelSession secondLevelSession : sessions){
            if (TextUtils.equals(mParentSession.getSessionId(),secondLevelSession.getParentSessionId())){
                sessions1.add(secondLevelSession) ;
            }
        }

        return sessions1 ;
    }

    private void setLastMsgSummary(SecondLevelSession slr, SessionMessage sm){
//        if(sm.getMessageType().equals(MsgTypeKey.MSG_EXTERNAL_NEWS_KEY)){
//            slr.setLastMessageSummary(sm.getExternalNewsMsgData().getTt());
//        }else if(sm.getMessageType().equals(MsgTypeKey.MSG_News_key)){
//            slr.setLastMessageSummary(sm.getFstdMsgData().getTt());
//        } else if (sm.getMessageType().equals(MsgTypeKey.MSG_AVMESSAGE_KEY)
//                || sm.getMessageType().equals(MsgTypeKey.MSG_AVEVENT_KEY)) {
//            slr.setLastMessageSummary(I18NHelper.getText("qx.session_list.lastsummery.voice_comm")/* [语音通话] */);
//        }else {
//            slr.setLastMessageSummary(sm.getContent());
//        }
        SessionListRec.setLastMsgSummary(slr,sm);
    }
    private void setLastMsgSummary(SecondLevelSession slr, SessionMessageTemp sm){
        if(sm.getMessageType().equals(MsgTypeKey.MSG_EXTERNAL_NEWS_KEY)){
            slr.setLastMessageSummary(sm.getExternalNewsMsgData().getTt());
        }else if(sm.getMessageType().equals(MsgTypeKey.MSG_News_key)){
            slr.setLastMessageSummary(sm.getFstdMsgData().getTt());
        } else if (sm.getMessageType().equals(MsgTypeKey.MSG_AVMESSAGE_KEY)
                || sm.getMessageType().equals(MsgTypeKey.MSG_AVEVENT_KEY)) {
            slr.setLastMessageSummary(I18NHelper.getText("qx.session_list.lastsummery.voice_comm")/* [语音通话] */);
        }else {
            slr.setLastMessageSummary(sm.getContent());
        }
    }
    void updateSessionContent(SecondLevelSession session, TextView txtSessionContent, CustomerSessionBase base){
        SpannableStringBuilder ssbBuilder = base.getSessionContent(session);
        int lms = session.getRealMessageStatus();
        if (lms==1) {
            txtSessionContent.setCompoundDrawablesWithIntrinsicBounds(R.drawable.message_sending, 0, 0, 0);
        }else if (lms==2){
            txtSessionContent.setCompoundDrawablesWithIntrinsicBounds(R.drawable.messagesendfail_messlist, 0, 0, 0);
        }else {
            txtSessionContent.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        }
        int count = ssbBuilder.length();
        ssbBuilder = (SpannableStringBuilder) (ssbBuilder.length() > 30 ? ssbBuilder.subSequence( 0, 30 ) : ssbBuilder);
        String content = ssbBuilder.toString();
        if (count > 0) {
            if ( Character.isHighSurrogate(content.charAt(content.length()-1)) ){
                ssbBuilder = (SpannableStringBuilder) ssbBuilder.subSequence(0, ssbBuilder.length() - 1);
            }
        }
        if (count >= 30) {
            ssbBuilder= CustomFaceHandler.processCustomFaceSuffix(ssbBuilder);//仅在可能显示不下时，才去剔除掉表情文字
            ssbBuilder.append("...");
        }
        txtSessionContent.setText(ssbBuilder);

    }


    private void initViewHolder(ViewHolder viewHolder){
        viewHolder.mContent.setText("");
        viewHolder.mUnReadCount.setVisibility(View.GONE);
        viewHolder.mNotify.setVisibility(View.GONE);
        viewHolder.mCustomerReplay.setVisibility(View.GONE);
        viewHolder.item.setBackgroundResource(R.drawable.list_item_select);
        viewHolder.mHeader.setImageBitmap(null);
        viewHolder.mBottomLine.setVisibility(View.VISIBLE);
        viewHolder.mOutLinkIcon.setVisibility(View.GONE);
    }

    public class ViewHolder extends FSBaseAdapter.BaseHolder{
        private PicMaskImageView mHeader ;
        private TextView mName ;
        private ImageView mCustomerReplay ;
        private ImageView mNotify ;
        private TextView mUnReadCount ;
        private TextView mTime ;
        private EmojiconTextView mContent ;
        private View mBottomLine ;
        private ImageView mOutLinkIcon;
        public ViewHolder(View item) {
            super(item);
            mHeader = (PicMaskImageView) findView(R.id.id_header_icon);
            mName = (TextView) findView(R.id.id_session_name);
            mNotify = (ImageView) findView(R.id.id_msg_notify) ;
            mTime = (TextView) findView(R.id.id_time);
            mUnReadCount = (TextView) findView(R.id.id_unread_count);
            mContent = (EmojiconTextView) findView(R.id.id_session_content);
            mBottomLine =  findView(R.id.id_divide_line);
            mCustomerReplay = (ImageView) findView(R.id.id_customer_replay);
            mOutLinkIcon = (ImageView) findView(R.id.out_link_icon);
        }
    }
}
