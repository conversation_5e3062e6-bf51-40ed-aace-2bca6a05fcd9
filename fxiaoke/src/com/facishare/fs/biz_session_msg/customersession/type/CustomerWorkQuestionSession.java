/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.customersession.type;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.facishare.fs.App;
import com.facishare.fs.biz_function.webview.CheckWebActivity;
import com.facishare.fs.biz_function.webview.JsApiWebActivity;
import com.facishare.fs.biz_session_msg.customersession.WeiXinExternalContactActivity;
import com.facishare.fs.contacts_fs.customerservice.StatService;
import com.fxiaoke.dataimpl.msg.MsgDataController;
import com.fxiaoke.fxdblib.beans.CustomerServiceVo;
import com.fxiaoke.fxdblib.beans.SecondLevelSession;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.WorkbenchEntityVo;
import com.fxiaoke.stat_engine.StatEngine;
import com.fxiaoke.stat_engine.events.session.UeEventSession;

import android.content.Context;
import android.content.Intent;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;

/**
 * Created by 钟光燕 on 2016/4/12.
 * ===================================================
 * <p>
 * code is m h l
 * <p>
 * ===================================================
 */
@Deprecated
public class CustomerWorkQuestionSession extends CustomerSessionBase {

    private List<WorkbenchEntityVo> workbenchEntityVos;

    public CustomerWorkQuestionSession(SessionListRec parentSession, Context context) {
        super(context, parentSession);
        CustomerServiceVo serviceVo = parentSession.getCusotmerSerivceVo();
        if (serviceVo != null) {
            workbenchEntityVos = serviceVo.getWorkbenchEntityVoList();
        }
    }

    @Override
    public String getSessionType() {
        return CUSTOMER_WORK_QUESTION_TYPE;
    }

    @Override
    public String getHeaderIcon(SecondLevelSession session) {
        if (workbenchEntityVos != null) {
            for (int i = 0; i < workbenchEntityVos.size(); i++) {
                if (TextUtils.equals(session.getSessionSubCategory(), workbenchEntityVos.get(i).getWorkType())) {
                    return workbenchEntityVos.get(i).getSessionIcon();
                }
            }
        }

        return "";
    }

    @Override
    public String getSessionName(SecondLevelSession session) {
        if (workbenchEntityVos != null) {
            for (int i = 0; i < workbenchEntityVos.size(); i++) {
                if (TextUtils.equals(session.getSessionSubCategory(), workbenchEntityVos.get(i).getWorkType())) {
                    return workbenchEntityVos.get(i).getSessionName();
                }
            }
        }
        return "";
    }

    @Override
    public SpannableStringBuilder getSessionContent(SecondLevelSession session) {
        if (!TextUtils.isEmpty(session.getRealLastMessageSummary())) {
            SpannableStringBuilder ssbBuilder = new SpannableStringBuilder(session.getRealLastMessageSummary());
            return ssbBuilder;
        }
        return new SpannableStringBuilder();

    }

    private UeEventSession mWork ;
    private UeEventSession mQw;
    @Override
    public void onClickSession(Context context,SecondLevelSession session) {
        String url = null;
        if (workbenchEntityVos != null) {
            for (int i = 0; i < workbenchEntityVos.size(); i++) {
                if (TextUtils.equals(session.getSessionSubCategory(), workbenchEntityVos.get(i).getWorkType())) {
                    url = workbenchEntityVos.get(i).getUri();
                }
            }
        }

        if ("WORK_ORDER".equals(session.getSessionSubCategory())){
            mWork = StatService.sendStart(StatService.ENTER_WORK_SESSION);
            StatService.sendEnd(mWork, MsgDataController.getInstace(App.getInstance()).getServiceId(session.getParentSessionId()));
        }else if ("QUESTION".equals(session.getSessionSubCategory())){
            mQw = StatService.sendStart(StatService.ENTER_QW_SESSION);
            StatService.sendEnd(mQw, MsgDataController.getInstace(App.getInstance()).getServiceId(session.getParentSessionId()));
//            StatEngine.tick(CustomerStatistics.SAAH_QUESTIONARENOTIFY_CLICK,
//                    MsgDataController.getInstace(App.getInstance()).getServiceId(session.getParentSessionId()));
        }

        if ("WEIXIN_CONTACTS".equals(session.getSessionSubCategory())){
            Intent intent = new Intent(context, WeiXinExternalContactActivity.class);
            if(parentSession != null){
                String appId = parentSession.getSessionSubCategory();
                if( appId != null && appId.startsWith("open_")){
                    appId = appId.replace("open_","");
                }
                intent.putExtra("outer_appId",appId);
//                intent.putExtra("extra",parentSession.getExtraData());
            }
            StatEngine.tick(StatService.FWH_WL_Wblxr_Second_Session_Click);
            context.startActivity(intent);
            if (session.getNotReadCount() > 0||session.isNotReadFlag()){
                session.setNotReadCount(0);
                session.setNotReadFlag(false);
                MsgDataController.getInstace(App.getInstance()).updateNotReadFlag(session,null) ;
            }
            return;
        }
        Map<String, String> param = new HashMap<>();
        param.put("unreadNumber", session.getNotReadCount() + "");
        if (!TextUtils.isEmpty(url)) {
            url = convertParameters(url, param);
            Intent intent = new Intent(context,JsApiWebActivity.class) ;
            intent.putExtra(CheckWebActivity.Input_key_is_h5, true);
            intent.putExtra(CheckWebActivity.Input_key_url, url);
            context.startActivity(intent);

            if (session.getNotReadCount() > 0||session.isNotReadFlag()){
                session.setNotReadCount(0);
                session.setNotReadFlag(false);
                MsgDataController.getInstace(App.getInstance()).updateNotReadFlag(session,null) ;
            }
        }


    }

    @Override
    public void onLongClickSession(SecondLevelSession session) {
        processLongClick(getSessionName(session), session);
    }


}
