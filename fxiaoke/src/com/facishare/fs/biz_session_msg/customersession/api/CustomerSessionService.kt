package com.facishare.fs.biz_session_msg.customersession.api

import com.alibaba.fastjson.annotation.JSONField
import com.facishare.fs.biz_session_msg.customersession.bean.SendPlusSignPluginInstructionResult
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback
import com.fxiaoke.fshttp.web.http.WebApiParameterList
import com.fxiaoke.fshttp.web.http.WebApiUtils

object CustomerSessionService {
    private const val CONTROLLER="FHE/EM1AWECHATUNION/OuterService"
    private const val CONTROLLER_MESSAGER = "FHE/EM1HQIXINBIZ/Messenger"
    /**
     * 获取当前快捷回复版本
     */
    fun quickReplyVersion(appId:String,callback:WebApiExecutionCallback<QuickReplyVersionResult>){
        WebApiUtils.postFHEAsync(CONTROLLER,"quickReplyVersion", WebApiParameterList.create().apply {
            add("M4",appId)
        },callback)
    }

    /**
     * 获取所有快捷回复内容
     */
    fun getAllQuickReplyContent(appId:String,callback:WebApiExecutionCallback<GetAllQuickReplyContentResult>){
        WebApiUtils.postFHEAsync(CONTROLLER,"allQuickReplyContent", WebApiParameterList.create().apply {
            add("M4",appId)
            add("M5",1)
        },callback)
    }

    /**
     * 快捷回复词条使用次数+1
     */
    fun increaseUseTimes(id:String,appId:String,callback:WebApiExecutionCallback<IncreaseUseTimeResult>){
        WebApiUtils.postFHEAsync(CONTROLLER,"incrUseTimes", WebApiParameterList.create().apply {
            add("M4",id)
            add("M5",appId)
        },callback)
    }

    /**
     * 重新开启会话
     */
    fun reopenSession(sessionId: String, instructionId: Int, env: Int, callback: WebApiExecutionCallback<SendPlusSignPluginInstructionResult>) {
        WebApiUtils.postAsync(CONTROLLER_MESSAGER, "SendExtraPlusSignPluginInstruction", WebApiParameterList.create().apply {
            add("M1", sessionId)
            add("M2", instructionId)
            add("M100", env)
        }, callback)
    }
}

data class QuickReplyVersionResult(@JSONField(name = "M5") val version:Int,
                                   @JSONField(name = "M1") val code:Int,
                                   @JSONField(name = "M2") val errorMessage:String)

data class GetAllQuickReplyContentResult(@JSONField(name = "M5") val replyInfo:QuickReplyInfo,
                                         @JSONField(name = "M1") val code:Int,
                                         @JSONField(name = "M2") val errorMessage:String) {
    data class QuickReplyInfo(val ea:String,val appId:String,val categories:MutableList<QuickReplyCategory>)

    data class QuickReplyCategory(val classId:String,val className:String,val replies:MutableList<QuickReplyContent>)

    data class QuickReplyContent(val id:String,val problemDesc:String,val replyContent:String,val useCount:Int,val replyOrder:Int)
}

data class IncreaseUseTimeResult(@JSONField(name = "M1") val code:Int,@JSONField(name = "M2") val errorMessage:String)