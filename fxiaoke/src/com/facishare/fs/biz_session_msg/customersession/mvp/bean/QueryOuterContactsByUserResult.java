package com.facishare.fs.biz_session_msg.customersession.mvp.bean;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;

/**
 * Created by wusj on 2016/11/18.
 * 服务器返回微信外部联系人结果
 */

public class QueryOuterContactsByUserResult  implements Serializable{
    @JSONField(name = "M2")
    public String getErrorMsg() {
        return errorMsg;
    }
    @JSONField(name = "M2")
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    /**
     * 错误码：
     *    请求成功：1
     *    参数错误  2
     */

    private String code;
    private String errorMsg;
    /**
     * 外部联系人列表
     */
    private List<OuterContactsFCPVO> outerContactsList;

    @JSONField(name = "M1")
    public String getCode() {
        return code;
    }

    @JSONField(name = "M1")
    public void setCode(String code) {
        this.code = code;
    }

    @JSONField(name = "M11")
    public List<OuterContactsFCPVO> getOuterContactsList() {
        return outerContactsList;
    }

    @JSONField(name = "M11")
    public void setOuterContactsList(List<OuterContactsFCPVO> outerContactsList) {
        this.outerContactsList = outerContactsList;
    }

    @Override
    public String toString() {
        return "QueryOuterContactsByUserResult{" +
                "code=" + code +'\'' +
                ",outerContactsList=" + outerContactsList +'\'' +
                "}";
    }
}
