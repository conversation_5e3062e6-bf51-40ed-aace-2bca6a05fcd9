package com.facishare.fs.biz_session_msg.sessionlist.lastsummary;

import com.alibaba.fastjson.JSON;
import com.facishare.fs.biz_session_msg.utils.AccountUtils;
import com.facishare.fs.biz_session_msg.utils.MsgUtils;
import com.facishare.fs.biz_session_msg.utils.SessionInfoUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fslib.R;
import com.fxiaoke.fxdblib.ChatDBHelper;
import com.fxiaoke.fxdblib.beans.MixMessageContent;
import com.fxiaoke.fxdblib.beans.MsgTypeKey;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.SessionMessage;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.lib.qixin.biz_ctrl.SessionMsgHelper;

import android.content.Context;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;

/**
 * Created by yangwg on 2019/1/14.
 * 只需进行简单的lastsummary处理，不需要反解消息内容做特殊处理的，如文本、 语音、图片、表情、定位等
 */

public class SessionLastMessageSimpleProcessor extends AbstractSessionSummaryProcessor {
    @Override
    public boolean matched(SessionListRec session) {
        String lastMsgType = session.getRealLastMessageType();
        if (TextUtils.isEmpty(lastMsgType)) {
            return false;
        }
        boolean ret = false;
        switch (lastMsgType) {
            case MsgTypeKey.MSG_Text_key:
            case MsgTypeKey.MSG_AVEVENT_KEY:
            case MsgTypeKey.MSG_AVMESSAGE_KEY:
            case MsgTypeKey.MSG_Img_key:
            case MsgTypeKey.MSG_Video_key:
            case MsgTypeKey.MSG_Emojicon_gif_key:
            case MsgTypeKey.MSG_News_key:
            case MsgTypeKey.MSG_EXTERNAL_NEWS_KEY:
            case MsgTypeKey.MSG_Audio_key:
            case MsgTypeKey.MSG_Location_key:
            case MsgTypeKey.MSG_WorkNotice_key:
            case MsgTypeKey.MSG_vote_key:
            case MsgTypeKey.MSG_SystemTextPrompt_key://"ST" 类型的系统消息
            case MsgTypeKey.MSG_CRMContact_key:
            case MsgTypeKey.MSG_MIX:
                ret = true;
                break;
            default:
                break;
        }
        return ret;
    }
    @Override
    public void process(Context context, SessionListRec slr, SpannableStringBuilder ssbBuilder) {
        super.process(context, slr, ssbBuilder);
        String lastMsgType = slr.getRealLastMessageType();
        String tempSummary = getAdjustedSummary(slr);
        if (lastMsgType == null) {
            ssbBuilder.append(tempSummary);
        } else if (lastMsgType.equals(MsgTypeKey.MSG_Text_key)) {
            ssbBuilder.append(tempSummary);
        } else if (lastMsgType.equals(MsgTypeKey.MSG_AVEVENT_KEY)) {
            // sumaryContent+="[语音会议]"; ssbBuilder.append("[语音会议]");
        } else if (MsgTypeKey.MSG_AVMESSAGE_KEY.equals(lastMsgType)) {
            ssbBuilder.append(I18NHelper.getText("qx.session_list.lastsummery.voice_comm")/* [语音通话] */);
        } else if (MsgTypeKey.MSG_Img_key.equals(lastMsgType)) {
            ssbBuilder.append(I18NHelper.getText("qx.session_list.lastsummery.image")/* [图片] */);
        } else if (MsgTypeKey.MSG_Video_key.equals(lastMsgType)) {
            ssbBuilder.append(I18NHelper.getText("qx.session_list.lastsummery.short_video")/* [小视频] */);
        } else if (MsgTypeKey.MSG_Emojicon_gif_key.equals(lastMsgType)) {
            ssbBuilder.append(I18NHelper.getText("qx.session_list.lastsummery.emotion")/* [表情] */);
        } else if (MsgTypeKey.MSG_News_key.equals(lastMsgType)) {
            ssbBuilder.append(tempSummary == null ? ""
                    : I18NHelper.getText("qx.sec_customer_list.lastsummery.link")/* [链接] */ + tempSummary);
        } else if (lastMsgType.equals(MsgTypeKey.MSG_EXTERNAL_NEWS_KEY)) {
            ssbBuilder.append(tempSummary == null ? ""
                    : I18NHelper.getText("qx.sec_customer_list.lastsummery.link")/* [链接] */ + tempSummary);
        } else if (lastMsgType.equals(MsgTypeKey.MSG_Audio_key)) {
            if ((tempSummary != null && tempSummary.equals("1")) ||
                    (AccountUtils.isMySelf(MsgUtils.getEmployeeInfoFromSessionLastMessage(slr)))) {
                ssbBuilder.append(I18NHelper.getText("qx.session_list.lastsummery.voice")/* [语音] */);
            } else {
                SpannableString audioContent =
                        new SpannableString(I18NHelper.getText("qx.session_list.lastsummery.voice")/* [语音] */);
                audioContent.setSpan(new ForegroundColorSpan(
                                context.getResources().getColor(R.color.sessionlist_sessionsumary_unread)),
                        0, audioContent.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                ssbBuilder.append(audioContent);
            }
        } else if (lastMsgType.equals(MsgTypeKey.MSG_Location_key)) {
            ssbBuilder.append(I18NHelper.getText("qx.session_list.lastsummery.location")/* [位置] */);

        } else if (lastMsgType.equals(MsgTypeKey.MSG_WorkNotice_key)) {
            //            WorkNoticeMsgData wnmd= MsgWorkNoticeViewItem.getWorkNoticeMsgData(tempSummary);
            //            if (wnmd!=null) {
            ssbBuilder.append("[" + I18NHelper.getText("xt.activity_customer_session_setting.text.group_msg")/* 群通知 */
                    + "]");
            //            }
        } else if (lastMsgType.equals(MsgTypeKey.MSG_vote_key)) {
            ssbBuilder.append("[" + I18NHelper.getText("xt.session_layout2bc.text.group_vote")/* 群投票 */ + "]");
        } else if (lastMsgType.equals(MsgTypeKey.MSG_SystemTextPrompt_key)) {
            String summary = tempSummary;
            ssbBuilder.append(summary);

        } else if (lastMsgType.equals(MsgTypeKey.MSG_CRMContact_key)) {
            ssbBuilder.append(I18NHelper.getText("qx.sec_customer_list.lastsummery.share_contacts")/* [共享联系人] */);
        } else if (lastMsgType.equals(MsgTypeKey.MSG_MIX)) {
            MixMessageContent mixContent = null;
            if (!TextUtils.isEmpty(tempSummary) && tempSummary.startsWith("{") && tempSummary.endsWith("}")) {
                try {
                    mixContent = JSON.parseObject(tempSummary, MixMessageContent.class);
                } catch (Exception e) {
                    FCLog.w(TAG, "process mix msg failed by not found MixMessageContent: " + tempSummary);
                }
            } else {
                //查数据库应该作为最后的选择；且不能暂存ChatDBHelper作为成员变量，因为其中的utils可能由于登录未初始化成功而导致构建失败，且如下的getChatDbHelper实现是的单例形式，没性能问题
                ChatDBHelper chatDBHelper = new SessionMsgHelper().getChatDbHelper(context);
                SessionMessage sm = chatDBHelper.getMessageByMsgID(slr.getSessionId(), slr.getLastMessageId());
                if (SessionInfoUtils.hasChildSessionList(slr)) {//有子session时，这个最后一条新消息的sessionId肯定不是父sessionid，具体是哪个也不好确定的，所以就只通过消息id来查找
                    sm = chatDBHelper.getMessageByMsgID(slr.getLastMessageId());
                }
                if (sm == null) {
                    FCLog.w(TAG, "process mix msg failed by not found msg with sid: " + slr.getSessionId() + " ,msgId: " + slr.getLastMessageId());
                    return;
                }
                mixContent = sm.getMixMessageContent();
            }
            if (mixContent == null) {
                return;
            }
            ssbBuilder.append(MsgUtils.getMixMessageShareContent(false, mixContent));
        }
    }
}
