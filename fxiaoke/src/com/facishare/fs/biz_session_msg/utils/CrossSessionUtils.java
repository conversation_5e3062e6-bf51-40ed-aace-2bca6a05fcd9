/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.utils;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.facishare.fs.biz_session_msg.SessionMsgActivity;
import com.facishare.fs.biz_session_msg.constants.SessionConstants;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.pluginapi.Account;
import com.facishare.fs.utils_fs.ToastUtils;
import com.fxiaoke.fscommon.sandbox.SandboxContextManager;
import com.fxiaoke.fscommon.sandbox.SandboxUtils;
import com.fxiaoke.fxdblib.beans.SessionListRec;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.Pair;

public class CrossSessionUtils {

    /**
     * 通过会话信息获取上游用户身份信息
     *
     * @param sessionInfo
     *
     * @return {first:互联群群主（上游）企业ea,  second:互联客群应用id} >
     */
    public static Pair<String, String> getUpUserInfo(SessionListRec sessionInfo) {
        if (sessionInfo != null && sessionInfo.getExtraDataMap() != null) {
            try {
                com.alibaba.fastjson.JSONObject exJsonObj = JSON.parseObject(sessionInfo.getExtraDataMap());
                String crossTrustOwnerEA = exJsonObj.getString("CrossTrustOwnerEA");//互联群群主（上游）企业ea
                String crossTrustAppId = exJsonObj.getString("CrossTrustAppId");//互联客群应用id，暂时服务端写死代理通
                return new Pair<>(crossTrustOwnerEA, crossTrustAppId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return new Pair<>("", "");
    }

    public static boolean processTrustEAAction(Context context, String url, SessionListRec sessionInfo) {
        Pair<String, String> crossInfo = getUpUserInfo(sessionInfo);
        if (UpstreamIdentityUtils.isCurrentAccountUpUserEA(crossInfo.first)) {//同一企业不需要换身份
            return false;
        }
        return processTrustEAAction(context, url, crossInfo.first, crossInfo.second, sessionInfo);
    }

    public static boolean processTrustEAAction(final Context context, final String url, String crossTrustOwnerEA, String crossTrustAppId, SessionListRec sessionInfo) {
        if (FSContextManager.getCurUserContext().getAccount().isVisitorLogin() || TextUtils.isEmpty(crossTrustOwnerEA) || TextUtils.isEmpty(crossTrustAppId)) {
            return false;//无租户账号不需要再进行互联登录更换身份了
        }
        UpstreamIdentityUtils.ILoadUpstreamCookieCallBack callBack = new UpstreamIdentityUtils.ILoadUpstreamCookieCallBack() {
            @Override
            public void onSuccess() {
                Map<String, Object> params = new HashMap<>();
                if (sessionInfo != null) {
                    params.put(SessionMsgActivity.Intent_key_Session_Env, sessionInfo.getEnterpriseEnvType());
                    params.put(SessionConstants.INTENT_KEY_SESSION_ID, sessionInfo.getSessionId());
                }
                if (context instanceof Activity) {
                    ((Activity) context).getIntent().putExtra(SandboxContextManager.Sandbox_EA_APPID, SandboxUtils.joinEaAppId(crossTrustOwnerEA, crossTrustAppId));
                }
                boolean processed = InnerUrlUtils.processUrlWithUpUserInfo(context, url, crossTrustOwnerEA, crossTrustAppId, params);
                if (!processed) {
                    if (context instanceof Activity) {
                        ((Activity) context).getIntent().removeExtra(SandboxContextManager.Sandbox_EA_APPID);
                    }
                }
            }

            @Override
            public void onFailed(String errorMsg) {
                ToastUtils.show(errorMsg);
            }
        };
        return UpstreamIdentityUtils.changeUpstreamCookie(context, crossTrustOwnerEA, crossTrustAppId, callBack);
    }
}
