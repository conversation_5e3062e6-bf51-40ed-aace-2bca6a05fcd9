/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.utils;

import com.facishare.fs.context.FSContextManager;
import com.fxiaoke.fscommon.sandbox.SandboxContextManager;

import android.content.Intent;

public class QxPageUtils {
    /**
     * 移除上游身份
     * @param intent
     */
    public static void removeUpEA(Intent intent) {
        if (intent == null) {
            return;
        }
        try {
            if(FSContextManager.getCurUserContext().getAccount().isVisitorLogin()){
                return;//无租户场景下，不让移除上游身份
            }
            intent.removeExtra(SandboxContextManager.Sandbox_EA_APPID);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
