/*
 * Copyright (C) 2024 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.utils;

import java.util.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.facishare.fs.biz_session_msg.SelectSessionActivity;
import com.facishare.fs.common_utils.Triple;
import com.facishare.fs.contacts_fs.beans.FriendEnterpriseEmployeeData;
import com.facishare.fs.contacts_fs.picker.RelatedEmpPicker;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.pluginapi.AccountManager;
import com.facishare.fs.pluginapi.IContacts;
import com.facishare.fs.pluginapi.crm.ICcCRMActions;
import com.facishare.fs.pluginapi.msg.beans.CreateDiscussConfig;
import com.facishare.fs.pluginapi.msg.beans.SelectSessionConfig;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fs.workflow.utils.Shell;
import com.fxiaoke.fscommon.sandbox.SandboxContextManager;
import com.fxiaoke.fscommon.util.MapData;
import com.fxiaoke.fxdblib.beans.ParticipantTO;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.SimpleParticipantInfo;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.fxsocketlib.businessctrl.FcpTaskBase;
import com.fxiaoke.fxsocketlib.utils.FcpUtils;
import com.fxiaoke.lib.qixin.client.impl.session.GetRelatedSessionClient;
import com.fxiaoke.lib.qixin.client.impl.session.QueryQiXinOuterListClient;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

public class CreateDiscussUtils {
    private static final String TAG = CreateDiscussUtils.class.getSimpleName();

    public static boolean delayCreateGroupTabAndStartSelectSessionIntent(Context context, CreateDiscussConfig mConfig, int requestCodeSelectSession, Runnable beginProgress,
                                                                         Runnable endProgress) {
//        String from = ICcCRMActions.Action_ApprovalDiscuss;
        if (mConfig == null) {
            return false;
        }
        if(beginProgress != null) {
            beginProgress.run();
        }
        String upEa = getUpEa(mConfig.extraDataMap, true);//接口要求，没有值时用当前企业的ea值
        new QueryQiXinOuterListClient(context, mConfig.bizType, mConfig.bizId, upEa,null) {
            @Override
            public void onSuccess(FcpTaskBase task, List<SimpleParticipantInfo> sessionList) {
                if (endProgress != null) {
                    endProgress.run();
                }
                Map<String, SimpleParticipantInfo> simpleParticipantInfoMap = new HashMap<>();
                if (sessionList != null) {
                    for (SimpleParticipantInfo simpleParticipantInfo : sessionList) {
                        if (simpleParticipantInfo.getOuterUserId() > 0) {
                            simpleParticipantInfoMap.put(simpleParticipantInfo.getOuterUserId() + "", simpleParticipantInfo);
                        }
                    }
                }

                List<SelectSessionConfig.GroupedEmployee> mergedGroupedEmployees = createGroupedEmployeeList(mConfig, simpleParticipantInfoMap);
                startSelectSessionActivity(context, mConfig, requestCodeSelectSession, mergedGroupedEmployees);
            }

            @Override
            public void onFailed(FcpTaskBase task, Object data) {
                if (endProgress != null) {
                    endProgress.run();
                }
                startSelectSessionActivity(context, mConfig, requestCodeSelectSession, mConfig.mGroupedEmployees);
                FCLog.e(TAG, FcpUtils.getFailedReason(data));

                if (FCLog.isDebugMode()) {
                    ToastUtils.show(FcpUtils.getFailedReason(data));
                }

            }
        }.execute();

        return true;

    }

    /**
     *
     * @param extraDataMap
     * @param useCurrentEaDefault 是否在没有upEa的情况下使用当前企业号
     * @return
     */
    public static String getUpEa(Map<String, Object> extraDataMap, boolean useCurrentEaDefault) {
        String upEa = null;
        if (extraDataMap != null && !extraDataMap.isEmpty()) {
            MapData mapData = new MapData(extraDataMap);
            if(extraDataMap.containsKey("extraData")){
                upEa = mapData.getJSONObject("extraData").getString("upEa");
            } else {
                upEa = mapData.getString("upEa");
            }
        }
        if (useCurrentEaDefault && TextUtils.isEmpty(upEa)) {
            upEa = AccountManager.getAccount().getEnterpriseAccount();
        }
        return upEa;
    }

    private static void startSelectSessionActivity(Context context, CreateDiscussConfig mConfig,
                                                   int requestCodeSelectSession,
                                                   List<SelectSessionConfig.GroupedEmployee> mGroupedEmployees) {

        if (mGroupedEmployees == null) {
            mGroupedEmployees = new ArrayList<>();
        }
        boolean isCreateOutSession = false;
        String upEa = null;
        if (mConfig.extraDataMap != null && !mConfig.extraDataMap.isEmpty()) {
            MapData mapData = new MapData(mConfig.extraDataMap);//GROUPED_EMPLOYEE_LIST_HAS_OUT_USER
            if (mapData.containsKey("extraData")) {
                isCreateOutSession = mapData.getJSONObject("extraData").getBooleanValue("isNeedCrossGroupSession");
            } else {
                isCreateOutSession = mapData.getBooleanValue("isNeedCrossGroupSession");
            }
            upEa = getUpEa(mConfig.extraDataMap,false);
        }
        boolean hasOutUser = checkHasOutUserInGroupedEmployeeList(mGroupedEmployees);
        SelectSessionConfig config = new SelectSessionConfig.Builder()
                .setTitle(I18NHelper.getText("qx.session.quickCreateGroupChooseAndGroup"/* 选择讨论人员并建群 */))
                .setShowGroupedEmployeeTab(mGroupedEmployees.size() > 0)
                .setGroupedEmployeeList(mGroupedEmployees)
                .setGroupedEmployeeListHasOutUser(hasOutUser)
                .setShowRecentSessionTab(false)
                .setShowGroupTab(false)
                .setRelateBizType(mConfig.bizType)
                .setRelateBizId(mConfig.bizId)
                .setCreateOutSession(isCreateOutSession)
                .setUpEaForCreateSession(upEa)
                .setGroupedEmployeeTabTitle(mConfig.groupedEmployeeTabTitle)
                .build();
        SelectSessionActivity.startIntentForResult(context, config, requestCodeSelectSession);
    }

    private static boolean checkHasOutUserInGroupedEmployeeList(List<SelectSessionConfig.GroupedEmployee> mGroupedEmployees) {
        if (mGroupedEmployees == null || mGroupedEmployees.isEmpty()) {
            return false;
        }
        boolean hasOutUser = false;
        for (SelectSessionConfig.GroupedEmployee groupedEmployee : mGroupedEmployees) {
            if (groupedEmployee != null && groupedEmployee.participantTOList != null && !groupedEmployee.participantTOList.isEmpty()) {
                for (SimpleParticipantInfo participantInfo : groupedEmployee.participantTOList) {
                    if (participantInfo.isOutUser()) {
                        hasOutUser = true;
                        break;
                    }
                }
                if (hasOutUser) {
                    break;
                }
            }
        }
        return hasOutUser;
    }

    public static List<SelectSessionConfig.GroupedEmployee> createGroupedEmployeeList(CreateDiscussConfig config,
                                                                                      Map<String, SimpleParticipantInfo> simpleParticipantInfoMap) {
        if(config == null || config.extraDataMap == null) {
            return new ArrayList<>();
        }
        MapData extraDataMap = new MapData(config.extraDataMap);

        int currentUserId = extraDataMap.getIntValue(CreateDiscussConfig.EXTRA_DATA_KEY_FOR_CURRENT_USER_ID);

//        String from = ICcCRMActions.Action_ApprovalDiscuss;
        List<SelectSessionConfig.GroupedEmployee> groups = new ArrayList<>();

        addApproveApplicantGroupData(currentUserId, simpleParticipantInfoMap, extraDataMap, groups);//添加审批申请人分组数据
        addApproveRelatedGroupDataList(currentUserId, simpleParticipantInfoMap, extraDataMap, groups);//添加审批的相关同事分组数据
        addRelevantTeamGroupDataList(currentUserId, simpleParticipantInfoMap, extraDataMap, groups);//添加相关团队成员分组数据

        return groups;
    }

    private static void removeValue(int value, List<Integer> values) {
        if (values == null || values.isEmpty()) {
            return;
        }
        Iterator<Integer> it = values.iterator();
        while (it.hasNext()) {
            Integer integer = it.next();
            if (integer == value) {
                it.remove();
            }
        }
    }
//    /**
//     * 获取当前登录的用户id, 如果是prm模式，则取outerUid
//     */
//    public static int getCurrentUserId(Activity activity) {
//        boolean prmMode = SandboxContextManager.getInstance().isUpEa(activity);
//        if (prmMode) {
//            String outerUid = SandboxContextManager.getInstance().getContext(activity).getOuterUid();
//            int intId = -1;
//            try {
//                intId = Integer.parseInt(outerUid);
//            } catch (NumberFormatException e) {
//                e.printStackTrace();
//            }
//            return intId;
//        } else {
//            return AccountManager.getAccount().getEmployeeIntId();
//        }
//    }
    //添加审批申请人分组数据
    private static void addApproveApplicantGroupData(int currentUserId,
                                                      Map<String, SimpleParticipantInfo> simpleParticipantInfoMap,
                                                      MapData extraDataMap, List<SelectSessionConfig.GroupedEmployee> groups) {
        JSONObject applicantUserObject;
        if (extraDataMap.containsKey("extraData")) {
            applicantUserObject = extraDataMap.getJSONObject("extraData").getJSONObject("applicantUser");
        } else {
            applicantUserObject = extraDataMap.getJSONObject("applicantUser");
        }
        SimpleParticipantInfo item = createParticipantTO(applicantUserObject, currentUserId, simpleParticipantInfoMap, extraDataMap,true);//ParticipantTO.create(userId, ea, companyName, isOutUser != null && isOutUser ? true : false);
        if (item != null) {
            int applicantId = extraDataMap.getIntValue(CreateDiscussConfig.EXTRA_DATA_KEY_FOR_APPLICANT_ID);
            SelectSessionConfig.GroupedEmployee originate = new SelectSessionConfig.GroupedEmployee();
            originate.setGroupName(I18NHelper.getText("BpmInstance.field.applicantId.label")/* 流程发起人 */);
            List<Integer> applicantIds = new ArrayList<>();
            applicantIds.add(applicantId);
            originate.setGroupedEmployeeIds(applicantIds);
            List<SimpleParticipantInfo> participantTOList = new ArrayList<>();
            participantTOList.add(item);
            originate.setParticipantTOList(participantTOList);
            groups.add(originate);
        }
    }

    //添加审批的相关同事分组数据
    private static void addApproveRelatedGroupDataList(int currentUserId,
                                                       Map<String, SimpleParticipantInfo> simpleParticipantInfoMap,
                                                       MapData extraDataMap, List<SelectSessionConfig.GroupedEmployee> groups) {
        if (extraDataMap != null ) {
            try {
                List<Object> discussPersonObjects = null;
                if (extraDataMap.containsKey("extraData")) {
                    if (extraDataMap.getJSONObject("extraData").get("discussPersonObjects") != null
                            && extraDataMap.getJSONObject("extraData").get("discussPersonObjects") instanceof List) {
                        discussPersonObjects = (List<Object>) extraDataMap.getJSONObject("extraData").get("discussPersonObjects");
                    }
                } else {
                    if (extraDataMap.get("discussPersonObjects") != null && extraDataMap.get("discussPersonObjects") instanceof List) {
                        discussPersonObjects = (List<Object>) extraDataMap.get("discussPersonObjects");
                    }
                }
                if (discussPersonObjects == null) {
                    return;
                }
                List<SimpleParticipantInfo> participantTOList = createParticipantDataList(discussPersonObjects, currentUserId,
                        simpleParticipantInfoMap, extraDataMap);
                if (participantTOList.size() > 0) {
                    SelectSessionConfig.GroupedEmployee approveRelatedGroup = new SelectSessionConfig.GroupedEmployee();
                    approveRelatedGroup.setGroupName(
                            I18NHelper.getText("crm.workflow.ApproveAction.approval_related_persons")/* 审批的相关同事 */);
                    approveRelatedGroup.setParticipantTOList(participantTOList);
                    groups.add(approveRelatedGroup);
                    return;
                }
            } catch (Exception e) {
                FCLog.e(TAG, "addApproveRelatedGroupDataList failed: " + Log.getStackTraceString(e));
            }
        }
    }

    //添加相关团队成员分组数据
    private static void addRelevantTeamGroupDataList(int currentUserId,
                                                     Map<String, SimpleParticipantInfo> simpleParticipantInfoMap,
                                                     MapData extraDataMap, List<SelectSessionConfig.GroupedEmployee> groups) {
        if (extraDataMap != null ) {
            try {
                List<Object> relevantTeamObjects = null;
                if (extraDataMap.containsKey("extraData")) {
                    if (extraDataMap.getJSONObject("extraData").get("relevantTeamObjects") != null
                            && extraDataMap.getJSONObject("extraData").get("relevantTeamObjects") instanceof List) {
                        relevantTeamObjects = (List<Object>) extraDataMap.getJSONObject("extraData").get("relevantTeamObjects");
                    }
                } else {
                    if (extraDataMap.get("relevantTeamObjects") != null && extraDataMap.get("relevantTeamObjects") instanceof List) {
                        relevantTeamObjects = (List<Object>) extraDataMap.get("relevantTeamObjects");
                    }
                }
                if (relevantTeamObjects == null) {
                    return;
                }

                List<SimpleParticipantInfo> participantTOList = createParticipantDataList(relevantTeamObjects, currentUserId,
                        simpleParticipantInfoMap, extraDataMap);
                if (participantTOList.size() > 0) {
                    SelectSessionConfig.GroupedEmployee relevantTeamGroup = new SelectSessionConfig.GroupedEmployee();
                    relevantTeamGroup.setGroupName(I18NHelper.getText("crm.workflow.ApproveAction.approval_team_member")/* 相关团队成员 */);
                    relevantTeamGroup.setParticipantTOList(participantTOList);
                    groups.add(relevantTeamGroup);
                    return;
                }
            } catch (Exception e) {
                FCLog.e(TAG, "addRelevantTeamGroupDataList failed: " + Log.getStackTraceString(e));
            }
        }
    }

    private static List<SimpleParticipantInfo> createParticipantDataList(List<Object> participantObjects,
                                                                         int currentUserId,
                                                                         Map<String, SimpleParticipantInfo> simpleParticipantInfoMap,
                                                                         MapData extraDataMap) {
        List<SimpleParticipantInfo> participantTOList = new ArrayList<>();
        if (participantObjects != null && participantObjects.size() > 0) {

            Map<String, Boolean> existParticipantMap = new HashMap<>();// 防止重复添加用
            for (int i = 0; i < participantObjects.size(); i++) {
                Object object = participantObjects.get(i);

                SimpleParticipantInfo item = createParticipantTO(object, currentUserId, simpleParticipantInfoMap, extraDataMap, true);//ParticipantTO.create(userId, ea, companyName, isOutUser != null && isOutUser ? true : false);
                if (item != null) {//
                    if (!TextUtils.isEmpty(item.getFullUserId()) && existParticipantMap.containsKey(item.getFullUserId())) {//去重
                        continue;
                    }
                    participantTOList.add(item);
                    existParticipantMap.put(item.getFullUserId() + "", true);
                }
            }
        }
        return participantTOList;
    }

    private static SimpleParticipantInfo createParticipantTO(Object object, int currentUserId,
                                                             Map<String, SimpleParticipantInfo> simpleParticipantInfoMap,
                                                             MapData extraDataMap,
                                                             boolean isFilterSelf) {
        if (object == null) {
            return null;
        }
        JSONObject jsonObject;
        if (object instanceof JSONObject) {
            jsonObject = (JSONObject) object;
        } else {
            jsonObject = JSONObject.parseObject(JSON.toJSONString(object));
        }
        int id = jsonObject.getInteger("id") != null ? jsonObject.getInteger("id") : -1;
        boolean isOutUser = jsonObject.getBoolean("outUser")!= null? jsonObject.getBoolean("outUser") : false;
        if (isFilterSelf) {
            if (id >= IContacts.sCrmOutUserIdThreshold) {
                if (currentUserId == id) {
                    return null;
                }
            } else {
                if (!isOutUser) {
                    if (AccountManager.getAccount().getEmployeeIntId() == id) {
                        return null;
                    }
                }
            }
        }

        if (id >= IContacts.sCrmOutUserIdThreshold) {
            if (simpleParticipantInfoMap != null && simpleParticipantInfoMap.containsKey(id + "")) {
                SimpleParticipantInfo simpleParticipantInfo = simpleParticipantInfoMap.get(id + "");
                simpleParticipantInfo.setUpstreamEa(extraDataMap.getString("upEa"));
                //                private String enterpriseAccount;//（本地用，非服务端提供数据）
                //                private int participantId;//（本地用，非服务端提供数据）
                //                private boolean isOutUser;//是否是外部用户（本地用，非服务端提供数据）
                Triple<String, String, Integer> triple = parseFullId(simpleParticipantInfo.getFullUserId());
                if (!RelatedEmpPicker.isNotTenant(simpleParticipantInfo.getOuterUserId(), simpleParticipantInfo.getFullUserId())) {
                    simpleParticipantInfo.setEnterpriseAccount(triple.second);
                    simpleParticipantInfo.setParticipantId(triple.third);
                } else {
                    simpleParticipantInfo.setUpstreamEa(triple.second);
                }
                simpleParticipantInfo.setOutUser(isOutUser);
                cacheOutsideUser(isOutUser, simpleParticipantInfo);
                return simpleParticipantInfo;
            } else {
                FCLog.e(TAG, "createParticipantTO failed by not found in serverData: " + id);
                return null;
            }
        }
        String ea;
        if(isOutUser){
            ea = getUpEa(extraDataMap.getParams(),false);
        }else{
            ea = AccountManager.getAccount().getEnterpriseAccount();
        }
        String eaName = jsonObject.getString("enterpriseShortName");
        String name = jsonObject.getString("name");
        String profileImage = jsonObject.getString("profileImage");
        SimpleParticipantInfo simpleParticipantInfo =  new SimpleParticipantInfo();
        simpleParticipantInfo.setFullUserId("E." + ea + "." + id);
        simpleParticipantInfo.setEnterpriseAccount(ea);
        simpleParticipantInfo.setParticipantId(id);
        simpleParticipantInfo.setOutUser(isOutUser);
        simpleParticipantInfo.setEaName(eaName);
        simpleParticipantInfo.setName(name);
        simpleParticipantInfo.setProfileImage(profileImage);
        simpleParticipantInfo.setUpstreamEa(extraDataMap.getString("upEa"));
        cacheOutsideUser(isOutUser, simpleParticipantInfo);
        return simpleParticipantInfo;
    }

    private static void cacheOutsideUser(boolean isOutUser, SimpleParticipantInfo simpleParticipantInfo) {
        if (isOutUser) {
            List<FriendEnterpriseEmployeeData> dataList = new ArrayList<>();
            FriendEnterpriseEmployeeData item = transformToFriendEnterpriseEmployeeData(simpleParticipantInfo);
            dataList.add(item);
            FSContextManager.getCurUserContext().getContactCache().loadRelatedEmp(dataList);
            FCLog.i(TAG, "cacheOutsideUser E. " + item.enterpriseAccount + "." + item.employeeId + ", name:" + item.employeeName);
        }
    }

    private static FriendEnterpriseEmployeeData transformToFriendEnterpriseEmployeeData(SimpleParticipantInfo simpleParticipantInfo) {
        FriendEnterpriseEmployeeData result = new FriendEnterpriseEmployeeData();
        result.enterpriseAccount = simpleParticipantInfo.getEaByFullUserId();
        result.employeeId = simpleParticipantInfo.getEmployeeIdByFullUserId();
        result.employeeName = simpleParticipantInfo.getName();
        result.profileImage = simpleParticipantInfo.getProfileImage();
        result.enterpriseName = simpleParticipantInfo.getEaName();

        return result;
    }

    public static Triple<String, String, Integer> parseFullId(String fullUserId) {
        if (!TextUtils.isEmpty(fullUserId)) {
            String[] separator;
            try {
                separator = fullUserId.split("\\.");
                if (separator != null && separator.length == 3) {
                    return new Triple(separator[0], separator[1], Integer.valueOf(separator[2]));
                }
            } catch (Exception e) {
            }
        }
        return new Triple("", "", 0);
    }
}
