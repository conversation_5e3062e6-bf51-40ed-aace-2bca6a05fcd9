/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.utils;

import com.facishare.fs.contacts_fs.beans.IEmployeeDataContract;
import com.facishare.fs.i18n.I18NHelper;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.facishare.fs.biz_session_msg.beans.EmployeeInfo;
import com.facishare.fs.biz_session_msg.views.view_ctrl.EmployeeLoader;
import com.facishare.fs.biz_session_msg.views.view_ctrl.EmployeeOptions;
import com.facishare.fs.contacts_fs.beans.EmployeeKey;
import com.facishare.fs.contacts_fs.beans.EmployeePublicData;
import com.facishare.fs.contacts_fs.datactrl.ContactDbOp;
import com.facishare.fs.contacts_fs.datactrl.ICacheThirdEmployeeData;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.pluginapi.GetUserListArgs;
import com.facishare.fs.pluginapi.GetUserListCallback;
import com.facishare.fs.pluginapi.IContactsCache;
import com.facishare.fs.pluginapi.contact.beans.RelatedEmp;
import com.facishare.fs.pluginapi.contact.beans.User;
import com.facishare.fs.utils_fs.EmployeeKeyUtils;
import com.facishare.fs.utils_fs.EmployeeUtils;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.fxiaoke.fxlog.FCLog;

import android.util.Pair;

/**
 * Created by pangc on 2016/12/7.
 */
public class UnknowEmployeeUtils {

    /*@Deprecated
    public static List<String> getInnerEmployeeName(List<EmployeeKey> innerEmployeeKeys,
                                                    final GetUnknownDataListener listener) {
        OptionWrapper wrapper = new OptionWrapper(false);
        Pair<List<EmployeeKey>, List<EmployeeKey>> separateKeys = pickUnknownKeys(innerEmployeeKeys, wrapper);
        if (listener != null) {
            updateCrossUnknownData(separateKeys.first, separateKeys.second, listener);
        }
        return wrapper.getStickInfoList();
    }

    @Deprecated
    public static List<String> getCrossEmployeeName(List<EmployeeKey> crossEmployeeKeys, boolean showEnterprise,
                                                    final GetUnknownDataListener listener) {
        OptionWrapper wrapper = new OptionWrapper(showEnterprise);
        Pair<List<EmployeeKey>, List<EmployeeKey>> separateKeys = pickUnknownKeys(crossEmployeeKeys, wrapper);
        if (listener != null) {
            updateCrossUnknownData(separateKeys.first, separateKeys.second, listener);
        }
        return wrapper.getStickInfoList();
    }

    @Deprecated
    public static List<EmployeeInfo> getCrossEmployeeInfo(List<EmployeeKey> crossEmployeeKeys, boolean showEnterprise,
                                                    final GetUnknownDataListener listener) {
        OptionWrapper wrapper = new OptionWrapper(showEnterprise);
        Pair<List<EmployeeKey>, List<EmployeeKey>> separateKeys = pickUnknownKeys(crossEmployeeKeys, wrapper);
        if (listener != null) {
            updateCrossUnknownData(separateKeys.first, separateKeys.second, listener);
        }
        return wrapper.getEmployeeInfoList();
    }

    @Deprecated
    public static EmployeeInfo getEmployeeInfo(EmployeeKey employeeKey, EmployeeOptions options,
                                               final GetUnknownDataListener listener) {
        List<EmployeeKey> employeeKeys = new ArrayList<>(1);
        employeeKeys.add(employeeKey);
        return getEmployeeInfo(employeeKeys, options, listener).get(0);
    }

    *//**
     * 通过EmployeeOption获取EmployeeInfo的通用的查询和更新接口
     *
     * @return List<EmployeeInfo>
     *//*
    @Deprecated
    public static List<EmployeeInfo> getEmployeeInfo(List<EmployeeKey> employeeKeys, EmployeeOptions options,
                                                     final GetUnknownDataListener listener) {
        OptionWrapper wrapper = new OptionWrapper(options);
        Pair<List<EmployeeKey>, List<EmployeeKey>> separateKeys = pickUnknownKeys(employeeKeys, wrapper);
        if (listener != null) {
            listener.wrapper = wrapper;
            updateCrossUnknownData(separateKeys.first, separateKeys.second, listener);
        }
        return wrapper.employeeInfos;
    }
*/
    /**
     * 更新 {@parms employeeKeys} 所包含的未知人员信息
     *
     * @param employeeKeys
     */
    public static void updateCrossUnknownData(List<EmployeeKey> employeeKeys, GetUnknownDataListener listener) {
        if (employeeKeys == null || employeeKeys.size() == 0) {
            return;
        }
        Pair<List<EmployeeKey>, List<EmployeeKey>> separateKeys = pickUnknownKeys(employeeKeys, null);
        updateCrossUnknownData(separateKeys.first, separateKeys.second, listener);
    }

    /**
     * 更新 {@parms innerKeys} 和 {@parms outerKeys}  所包含的未知人员信息
     *
     * @param innerKeys
     * @param outerKeys
     */
    public static void updateCrossUnknownData(List<EmployeeKey> innerKeys, List<EmployeeKey> outerKeys,
                                              GetUnknownDataListener listener) {
        if ((innerKeys == null || innerKeys.size() == 0) && (outerKeys == null || outerKeys.size() == 0)) {
            return;
        }
        GetDataTask task = new GetDataTask(innerKeys, outerKeys, listener);
        task.execute();
    }

    private static Pair<List<EmployeeKey>, List<EmployeeKey>> pickUnknownKeys(List<EmployeeKey> employeeKeys,
                                                                              OptionWrapper wrapper) {
        IContactsCache contactsCache = FSContextManager.getCurUserContext().getContactCache();
        ICacheThirdEmployeeData thirdEmployeeData = FSContextManager.getCurUserContext().getCacheThirdEmployeeData();
        String myEnterpAccount = AccountUtils.getMyEA();
        final List<EmployeeKey> unknownInnerList = new LinkedList<>();
        final List<EmployeeKey> unknownThirdList = new LinkedList<>();
        for (EmployeeKey employeeKey : employeeKeys) {
            if (employeeKey.enterpriseAccount.equals(myEnterpAccount) && employeeKey.employeeId < IEmployeeDataContract.OUT_USER_ID_THRESHOLD) {
                User user = contactsCache.getUser(employeeKey.employeeId);
                if (user.isFakeUser()) {
                    unknownInnerList.add(employeeKey);
                }
                if (wrapper != null) {
                    wrapper.employeeInfos.add(wrapper.processUser(user, employeeKey));
                }
            } else {
                RelatedEmp relatedEmp = ContactDbOp.findExternalEmployee(employeeKey.enterpriseAccount, employeeKey.employeeId);
                if (relatedEmp.isFakeEmp()) {
                    EmployeePublicData publicData = thirdEmployeeData.getThirdEmployeeDataAllowNull(employeeKey);
                    if (publicData == null) {
                        unknownThirdList.add(employeeKey);
                    }
                    if (wrapper != null) {
                        wrapper.employeeInfos.add(wrapper.processPublicData(publicData, employeeKey));
                    }
                } else {
                    if (wrapper != null) {
                        wrapper.employeeInfos.add(wrapper.processRelatedEmp(relatedEmp, employeeKey));
                    }
                }
            }
        }
        return new Pair<>(unknownInnerList, unknownThirdList);
    }

    public static class OptionWrapper {

        private final IContactsCache contactsCache = FSContextManager.getCurUserContext().getContactCache();

        EmployeeOptions options;
        List<EmployeeInfo> employeeInfos = new ArrayList<>();

        /**
         * 此构造函数用来兼容旧的获取姓名（＋企业名）的功能
         */
        public OptionWrapper(boolean withCompany) {
            EmployeeOptions.Builder builder;
            if (withCompany) {
                builder = EmployeeOptions.create().withNameStickCompany();
            } else {
                builder = EmployeeOptions.create().withName();
            }
            this.options = builder.build();
        }

        public OptionWrapper(EmployeeOptions employeeOptions) {
            this.options = employeeOptions;
        }

        public List<String> getStickInfoList() {
            List<String> stickInfos = new ArrayList<>(employeeInfos.size());
            for (EmployeeInfo info : employeeInfos) {
                stickInfos.add(info.name);
            }
            return stickInfos;
        }

        public List<EmployeeInfo> getEmployeeInfoList() {
            return employeeInfos;
        }

        private EmployeeInfo processUser(User user, EmployeeKey employeeKey) {
            EmployeeInfo employeeInfo = EmployeeInfo.create(employeeKey).setIsFake(user.isFakeUser()).setIsDismiss
                    (user.isDismiss());
            if (options.hasName) {
                employeeInfo.withNameNotNull(user.getNameWithUnknownID());
            }
            if (options.hasPortrait) {
                employeeInfo.withPortraitNotNull(
                        WebApiUtils.getDownloadUrlForImg(user.getImageUrl(), WebApiUtils.ImageType.IMG_100x100));
            }
            return employeeInfo;
        }

        private EmployeeInfo processRelatedEmp(RelatedEmp relatedEmp, EmployeeKey employeeKey) {
            EmployeeInfo employeeInfo = EmployeeInfo.create(employeeKey).setIsFake(relatedEmp.isFakeEmp());
            if (options.hasPortrait) {
                String portrait = WebApiUtils.getAvatarUrlForRelated(relatedEmp.getImageUrl(),
                        WebApiUtils.ImageType.IMG_100x100, relatedEmp.getEnterpriseAccount());
                employeeInfo.withPortraitNotNull(portrait);
            }
            String name = null;
            String company = null;
            if (options.hasName) {
                name = relatedEmp.getName();
            }
            if (options.hasCompany) {
                company = ContactDbOp.findExternalEnterprise(relatedEmp.getEnterpriseAccount()).getShortName();
            }
            if (options.isStickCompany) {
                employeeInfo.withName(name + "-" + company);
            } else {
                employeeInfo.withNameNotNull(name).withCompanyNotNull(company);
            }
            return employeeInfo;
        }

        private EmployeeInfo processPublicData(EmployeePublicData publicData, EmployeeKey employeeKey) {
            EmployeeInfo employeeInfo = EmployeeInfo.create(employeeKey).setIsFake(publicData == null);
            if (options.hasPortrait) {
                String portrait = publicData == null ? "" : WebApiUtils.getAvatarUrlForRelated(
                        publicData.profileImagePath, WebApiUtils.ImageType.IMG_100x100, publicData.getEAForSessionParticipant());
                employeeInfo.withPortraitNotNull(portrait);
            }
            String name = null;
            String company = null;
            if (options.hasName) {
                name = publicData == null || publicData.mIsFakeData ? "ID" + employeeKey.employeeId : publicData.getEmployeeName();
            }
            if (options.hasCompany) {
                company = publicData == null ? I18NHelper.getText("crm.beans.RelatedEnterprise.2417")/* 未知外部企业 */ : publicData.enterpriseShortName;
            }
            if (options.isStickCompany) {
                employeeInfo.withName(name + "-" + company);
            } else {
                employeeInfo.withNameNotNull(name).withCompanyNotNull(company);
            }
            return employeeInfo;
        }
    }

    private static class GetDataTask {

        List<EmployeeKey> innerKeys;
        List<EmployeeKey> outerKeys;
        GetUnknownDataListener listener;

        /*volatile*/ Map<Integer, User> userMap;
        /*volatile*/ List<EmployeePublicData> publicDataList;

        public GetDataTask(List<EmployeeKey> innerKeys, List<EmployeeKey> outerKeys,
                           GetUnknownDataListener listener) {
            this.innerKeys = innerKeys;
            this.outerKeys = outerKeys;
            this.listener = listener;
        }

        public void execute() {
            if (userMap == null && innerKeys != null && innerKeys.size() > 0) {
                getInnerUnknownData();
            } else if (publicDataList == null && outerKeys != null && outerKeys.size() > 0) {
                getOuterUnknownData();
            } else if (listener != null) {
                triggerCallback();
            }
        }

        private void getInnerUnknownData() {
            GetUserListArgs.Builder builder = new GetUserListArgs.Builder();
            for (EmployeeKey employeeKey : innerKeys) {
                builder.addId(employeeKey.employeeId);
            }
            FSContextManager.getCurUserContext().getContactCache().getUserList(builder.build(),
                    new GetUserListCallback() {
                        @Override
                        public void onUserGot(Map<Integer, User> userMap) {
                            GetDataTask.this.userMap = userMap;
                            execute();
                        }
                    });
        }

        private void getOuterUnknownData() {
            FSContextManager.getCurUserContext()
                    .getCacheThirdEmployeeData().getThirdEmployeeDataList(outerKeys,
                    new ICacheThirdEmployeeData.GetThirdEmployeeDataCallback() {
                        @Override
                        public void onDatasGot(List<EmployeePublicData> publicDataList) {
                            GetDataTask.this.publicDataList = publicDataList;
                            execute();
                        }
                    });
        }

        private void triggerCallback() {
            boolean hasFake = false;
            if (userMap != null && EmployeeUtils.checkHasFakeUser(userMap.values())) {
                hasFake = true;
            }
            if (EmployeeUtils.checkHasFakePublicData(publicDataList)) {
                hasFake = true;
            }
            List<EmployeeInfo> updateInfos = new ArrayList<>();
            if (listener.needCallbackData && listener.wrapper != null) {
                // 重新构造返回List对象，以免对使用者造成影响！
                listener.wrapper.employeeInfos = new ArrayList<>(listener.wrapper.employeeInfos);
                if (userMap != null) {
                    for (User user : userMap.values()) {
                        updateInfos.add(listener.wrapper.processUser(user, EmployeeKeyUtils.keyOf(user)));
                    }
                }
                if (publicDataList != null) {
                    for (EmployeePublicData data : publicDataList) {
                        updateInfos.add(listener.wrapper.processPublicData(data, EmployeeKeyUtils.keyOf(data)));
                    }
                }
                FCLog.d(EmployeeLoader.TAG, String.format("listener.empInfos.size:%d",
                        listener.wrapper.employeeInfos.size()));
                int i = 0;
                for (EmployeeInfo employeeInfo : listener.wrapper.employeeInfos) {
                    if (employeeInfo.isFake) {
                        for (int j = 0; j < updateInfos.size(); ++j) {
                            EmployeeInfo updateInfo = updateInfos.get(j);
                            if (employeeInfo.employeeKey.equals(updateInfo.employeeKey)) {
                                listener.wrapper.employeeInfos.set(i, updateInfo);
                                break;
                            }
                        }
                    }
                    i++;
                }
                listener.onGetUnknownData(hasFake, listener.wrapper.employeeInfos);
            } else {
                listener.onGetUnknownData(hasFake, null);
            }
        }
    }

    /**
     * 不保证返回的results中的数据非null，即如果内部查询集合无数据，则 Map<Integer, User>为null
     */
    public static abstract class GetUnknownDataListener {
        public OptionWrapper wrapper;
        private boolean needCallbackData = false;

        public GetUnknownDataListener() {
            needCallbackData = false;
        }

        public GetUnknownDataListener(boolean needCallbackData) {
            this.needCallbackData = needCallbackData;
        }

        abstract public void onGetUnknownData(boolean hasFake, List<EmployeeInfo> updateInfoList);
    }

    public static final GetUnknownDataListener defaultListener = new GetUnknownDataListener() {
        @Override
        public void onGetUnknownData(boolean hasFake, List<EmployeeInfo> updateInfoList) {
        }
    };
}
