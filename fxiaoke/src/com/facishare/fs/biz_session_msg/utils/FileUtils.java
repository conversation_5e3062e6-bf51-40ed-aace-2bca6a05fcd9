package com.facishare.fs.biz_session_msg.utils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import com.facishare.fs.i18n.I18NHelper;

/**
 * <AUTHOR>
 * at 2018/11/16 16:06
 */
public class FileUtils {

    private static final DateFormat DF_HOUR_MIN = new SimpleDateFormat("HH:mm");

    public static final String getFileExpireDesc(long expireTime, long currentTime) {
        if (currentTime > expireTime) {
            return I18NHelper.getText("qx.file_preview.big_file.expired");
        }

        int expireDay = getEpochDay(expireTime);
        int curDay = getEpochDay(currentTime);

        int intervalDay = expireDay - curDay;
        if (intervalDay == 0) {
            return I18NHelper.getFormatText("qx.file_preview.big_file.expire_today", DF_HOUR_MIN.format(new Date(expireTime)));
        } else if (intervalDay == 1) {
            return I18NHelper.getText("qx.file_preview.big_file.expire_tomorrow");
        } else {
            return I18NHelper.getFormatText("qx.file_preview.big_file.expire_desc", String.valueOf(intervalDay));
        }

    }

    private static int getEpochDay(long timestamp) {
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(timestamp);
        // 这段设置时分秒为0是为了解决跨天问题，比如23:00到第二天1:00 只有两个小时，但隔了一天。
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        //1970-01-01 08:00:00
        return (int) (c.getTimeInMillis() / 24 / 60 / 60 / 1000);
    }
}
