/*
 * Copyright (C) 2023 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.facishare.fs.Constants;
import com.facishare.fs.MainTabActivity;
import com.facishare.fs.biz_feed.newfeed.utils.FeedProgressUtils;
import com.facishare.fs.biz_session_msg.ShortMessageMainActivity;
import com.facishare.fs.biz_session_msg.constants.SessionConstants;
import com.facishare.fs.contacts_fs.AIAssistantDescriptionActivity;
import com.facishare.fs.contacts_fs.SelectEmpForQixinActivity;
import com.facishare.fs.contacts_fs.beans.MixedEmpViewData;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.pluginapi.contact.beans.EmpIndexLetter;
import com.facishare.fs.utils_fs.ImageLoaderUtil;
import com.facishare.fs.utils_fs.ToastUtils;
import com.fxiaoke.dataimpl.msg.MsgDataController;
import com.fxiaoke.fscommon.util.CommonDataContainer;
import com.fxiaoke.fscommon.util.FsUrlUtils;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.SessionTypeKey;
import com.fxiaoke.fxdblib.beans.SubscribedBot;
import com.fxiaoke.fxdblib.beans.sessiondefine.SessionBotDefinition;
import com.fxiaoke.fxdblib.utils.SessionBotDefinitionHelper;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.fxsocketlib.businessctrl.FcpTaskBase;
import com.fxiaoke.fxsocketlib.utils.FcpUtils;
import com.fxiaoke.lib.qixin.biz_ctrl.constant.TrustSessionConstant;
import com.fxiaoke.lib.qixin.client.impl.FindOrCreateBotSessionClient;
import com.nostra13.universalimageloader.core.ImageLoader;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.widget.ImageView;
import android.widget.TextView;

/**
 * 操作数字员工（AI助理）相关的工具方法
 */
public class SessionAIBotDefinitionUtils {

    public static boolean isAIBotSender(Context context, String targetFullSenderId) {
        boolean ret = false;//
        if (!TextUtils.isEmpty(targetFullSenderId) && targetFullSenderId.startsWith("BOT")) {
            String botId = SessionBotDefinitionUtils.getBotIdByFullSendId(targetFullSenderId);
            SessionBotDefinition bot = SessionBotDefinitionUtils.getSessionBotDefinition(context, botId, false, null);
            if (SessionBotDefinitionHelper.isAIBot(bot)) {
                ret = true;
            }
        }
        return ret;
    }

    public static boolean isAIBotSession(Context context, SessionListRec sessionInfo) {
        if (SessionInfoUtils.isBotGroup(sessionInfo)) {
            SessionBotDefinition botDefinition = SessionBotDefinitionUtils.getSessionBotDefinition(context, sessionInfo.getSessionSubCategory(), false, null);
            if (SessionBotDefinitionHelper.isAIBot(botDefinition)) {
                return true;
            }
        }
        return false;
    }

    private static boolean isAIMixedEmpViewData(MixedEmpViewData person) {
        return person instanceof MixedEmpViewData.AIAssistantEmpInfo && person.isAIAssistant();
    }

    /**
     * 获取当前会话的订阅机器人数据中属于数字员工（AI助理）的数据，以 ArrayList<EmpIndexLetter>形式返回
     *
     * @param sessionListRec
     *
     * @return
     */
    public static ArrayList<EmpIndexLetter> getAIBotEmpIndexList(SessionListRec sessionListRec) {
        // 目前所有的订阅机器人数据均会存储在内存中，可以通过如下方式获取到
        //        Map<String, SessionBotDefinition> cache = (Map<String, SessionBotDefinition>)
        //                CommonDataContainer.getInstance().getSavedData(SessionBotDefinitionQueueCtr.KEY_SESSION_BOT_Definition_CACHE);
        Map<String, SessionBotDefinition> cache = SessionBotDefinitionUtils.getCache();
        ArrayList<EmpIndexLetter> empList = new ArrayList<>();
        for (SubscribedBot botItem : sessionListRec.getBotIdList()) {
            SessionBotDefinition botDefinition = cache.get(botItem.botId);
            if (SessionBotDefinitionHelper.isAIBot(botDefinition) && SessionBotDefinitionHelper.isEnabledAIBot(botDefinition)) {
                EmpIndexLetter letter = new EmpIndexLetter();
                letter.extendedFirstField = botItem.botId;
                letter.extendedSecondField = botItem.subscriberId;
                letter.extendedDataBizType = "SubscribedBotType";
                letter.setIndexLetter(I18NHelper.getText("xt.select_emp_new_fragment.text.bot_letter", "助"));
                empList.add(letter);
            }
        }
        return empList;
    }

    /**
     * 追加当前可添加的数字员工（AI助理）列表数据到intent中，并过滤掉已在当前会话中的list
     *
     * @param sessionListRec
     * @param it
     */
    public static void appendAllAIBotWithoutSessionBotDataToIntent(Context context, SessionListRec sessionListRec, Intent it) {
        if (it != null ) {
            //因为获取bot列表数据需要从数据库中获取，所以需要先检查一下缓存中是否有数据，没有则从数据库中获取
            SessionBotDefinitionUtils.checkInitCacheByLocalDB(context);
            // 获取缓存中的所有bot列表数据
            Map<String, SessionBotDefinition> cache = SessionBotDefinitionUtils.getCache();

            // 获取当前会话中的botId列表
            List<SubscribedBot> sessionBotIds = new ArrayList<>();
            if (sessionListRec != null && sessionListRec.getBotIdList() != null && sessionListRec.getBotIdList().size() > 0) {
                sessionBotIds.addAll(sessionListRec.getBotIdList());
            }
            ArrayList<EmpIndexLetter> remainingBots = new ArrayList<>();
            for (Map.Entry<String, SessionBotDefinition> entry : cache.entrySet()) {
                String botId = entry.getKey();
                if (!sessionBotIds.contains(new SubscribedBot(botId, ""))) {
                    SessionBotDefinition botDefinition = entry.getValue();
                    if (SessionBotDefinitionHelper.isAIBot(botDefinition) && SessionBotDefinitionHelper.isEnabledAIBot(botDefinition)) {
                        EmpIndexLetter letter = new EmpIndexLetter();
                        letter.extendedFirstField = botId;
                        //                        letter.extendedSecondField = botItem.subscriberId;
                        letter.extendedDataBizType = "SubscribedBotType";
                        letter.setIndexLetter(I18NHelper.getText("xt.select_emp_new_fragment.text.bot_letter", "助"));
                        remainingBots.add(letter);
                    }
                }
            }
            // 将剩余的bot列表放入Intent
            it.putExtra(SelectEmpForQixinActivity.INTENT_KEY_FOR_EXTRA_BOT_LIST, remainingBots);
        }
    }

    public static void rendAIBotIcon(ImageView imageView, SessionBotDefinition botDefinition) {
        if (botDefinition != null) {
            String iconPath = SessionBotDefinitionUtils.getBotIconPathByBotDefinition(botDefinition);
            ImageLoader.getInstance().displayImage(iconPath,
                    imageView,
                    ImageLoaderUtil.getAIBotSenderDisplayImageOptions(imageView.getContext()));
        }
    }

    public static void renderTextSimpleDes(TextView txtInfo, SessionBotDefinition botDefinition) {
        if (txtInfo == null || botDefinition == null) {
            return;
        }
        String simpleDes = I18NHelper.getText(botDefinition.getBotSimpleDescriptionKey(),
                botDefinition.getSimpleDescription());
        txtInfo.setText(simpleDes);
    }

    private static void openAIBotDescriptionPage(Context context, String fullSenderId) {
        AIAssistantDescriptionActivity.startIntentByFullBotId(context, fullSenderId);
    }

    public static void openAIBotDescriptionPage(Context context, SessionListRec mSessionInfo, String fullSenderId) {
        openAIBotDescriptionPage(context, fullSenderId);
    }

    public static boolean openAIBotDescriptionPageByMixedEmpViewData(Context context, MixedEmpViewData data) {
        if (isAIMixedEmpViewData(data)) {
            openAIBotDescriptionPageByBotDefinition(context, ((MixedEmpViewData.AIAssistantEmpInfo) data).getBotDefinition());
            return true;
        }
        return false;
    }

    public static void openAIBotDescriptionPageByBotDefinition(Context context, SessionBotDefinition botDefinition) {
        openAIBotDescriptionPageByBotId(context, botDefinition.getBotId());
    }

    public static void openAIBotDescriptionPageByBotId(Context context, String botId) {
        AIAssistantDescriptionActivity.startIntentByBotId(context, botId);
    }

    /**
     * 查找并进入到数字员工对应的会话
     *
     * @param activity
     * @param botId
     */
    public static void findAISessionAndStartChat(Activity activity, String botId) {
        //                if (mEmployeeInfo != null) {
        //                    ContactAction.sendQixin(context, mEmployeeID);
        //                } else {
        //                    ToastUtils.show(I18NHelper.getText("xt.xpersonbaseactivity.text.invalid_employee_data")/* 无效员工数据 */);
        //                }
        //  1. 先查本地会话，有对应的会话，就直接跳转；
        SessionListRec session = MsgDataController.getInstace(activity).getSessionByCategory(SessionTypeKey.Session_Cate_Bot,
                botId + "");
        if (session != null) {
            startSession(session.getSessionId());
        } else {//2.没查到本地对应会话，要先创建，然后再跳转
            requestCreateSessionAndStartSession(activity, botId);
        }
    }

    private static void startSession(String sessionId) {
        Intent intent = FsUrlUtils.buildSingleTaskForChatIntent();
        intent.putExtra(SessionConstants.INTENT_KEY_SESSION_ID, sessionId);
        intent.putExtra("isCanBackQixinList", true);
        intent.putExtra(Constants.Key.KEY_IM_UNREAD_COUNT, ShortMessageMainActivity.instance.getUnreadCount());
        if (intent != null) {
            intent.putExtra("isNeedManualBackQixin", false);
        }
        MainTabActivity.startActivityByAnim(intent);
    }

    private static void requestCreateSessionAndStartSession(Activity activity, String botId) {
        try {
            FeedProgressUtils.show(activity);
            new FindOrCreateBotSessionClient(activity, botId, null) {
                @Override
                public void onSuccess(FcpTaskBase task, SessionListRec session) {
                    if (session != null) {
                        startSession(session.getSessionId());
                    } else {
                        String error = I18NHelper.getText("qx.repost_session.result.no_data_return");/* 没有会话数据返回 */
                        ToastUtils.show(error);
                    }
                    FeedProgressUtils.hide(activity);
                }

                @Override
                public void onFailed(FcpTaskBase task, Object data) {
                    String error = FcpUtils.getFailedReason(data);
                    FCLog.i(TAG, "create BotSession( " + botId + ") onFailed " + error);
                    FeedProgressUtils.hide(activity);
                    ToastUtils.show(error);
                }
            }.execute();
        } catch (Exception e) {
            FeedProgressUtils.hide(activity);
            FCLog.e(TrustSessionConstant.TAG, "create BotSession( " + botId + ") Exception " + Log.getStackTraceString(e));
            String showTip = I18NHelper.getText("qx.cross_out_profile_person.result.create_session_failed")/* 创建新会话失败! */;
            ToastUtils.show(showTip + "(" + e.getMessage() + ")");
        }
    }
}
