package com.facishare.fs.biz_session_msg;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.BitmapDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.PopupWindow;

import com.facishare.fslib.R;
import com.fxiaoke.fxlog.FCLog;


public class ShareNewsFlashMenuProvider {

	private PopupWindow mPopupWindow = null;
	
	private WebMenuClickListener mWebMenuClickListener;
	
	
	private Activity mActivity = null;
	
	private LinearLayout mShareToWeChatCircleOfFriends;
	private LinearLayout mShareToWeChatFriend;
	private LinearLayout mShareToQQ;
	private LinearLayout mShareToCRMContacts;
	private LinearLayout mShareByShortMessage;
	private LinearLayout mShareByEmail;
	private LinearLayout mTranspondToEnterpriseChat;
	private LinearLayout mTranspondToSharing;
	private LinearLayout mOpenInBrowser;
	
	private LinearLayout mRefreshWebPage;
	private LinearLayout mCreateQRCode;
	private LinearLayout mCopyLink;
	
	
	private String mLoadUrl = "https://www.baidu.com/";
	
	public ShareNewsFlashMenuProvider(Activity activity){
		mActivity = activity;
		initView();
		initData();
		initListener();
	}
	
	public void showMenu(View view){
		
		WindowManager wm = (WindowManager) view.getContext().getSystemService(Context.WINDOW_SERVICE);
		int width = wm.getDefaultDisplay().getWidth();
		
		mPopupWindow.showAsDropDown(view,width-8,-16);
		//mPopupWindow.showAsDropDown(view);				
	}
	

	public void setWebMenuClickListener(WebMenuClickListener listener){
		FCLog.e("chenlw","cecccccc,listener="+listener);
		mWebMenuClickListener = listener;
	}
	
	private void initView(){
		LayoutInflater layoutInflater = (LayoutInflater) mActivity
				.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
		View view = layoutInflater.inflate(R.layout.view_share_news_flash_menu,null);
		
		mShareToWeChatCircleOfFriends = (LinearLayout)view.findViewById(R.id.share_to_wechat_circle_of_friends);
		mShareToWeChatFriend = (LinearLayout)view.findViewById(R.id.share_to_wechat_friend);
		mShareToQQ  = (LinearLayout)view.findViewById(R.id.share_to_qq);
		mTranspondToEnterpriseChat = (LinearLayout)view.findViewById(R.id.transpond_to_enterprise_chat);
		mTranspondToSharing = (LinearLayout)view.findViewById(R.id.transpond_to_sharing);
		mShareToCRMContacts = (LinearLayout)view.findViewById(R.id.share_to_crm_contacts);
		mShareByShortMessage = (LinearLayout)view.findViewById(R.id.share_by_short_message);
		mShareByEmail = (LinearLayout)view.findViewById(R.id.share_by_email);
		mOpenInBrowser = (LinearLayout)view.findViewById(R.id.open_in_browser);
		
		mCreateQRCode = (LinearLayout)view.findViewById(R.id.create_qr_code);
		mCopyLink = (LinearLayout)view.findViewById(R.id.copy_linker);	
		mRefreshWebPage = (LinearLayout)view.findViewById(R.id.refresh_webpage);	
		
		
		WindowManager wm = (WindowManager) view.getContext().getSystemService(Context.WINDOW_SERVICE);
		int width = wm.getDefaultDisplay().getWidth();
		int height = wm.getDefaultDisplay().getHeight();
		
		int popupWindowWidth = (int)(width*1/2);
		int popupWindowHeight = (int)(height*5/8);
		mPopupWindow = new PopupWindow(view, popupWindowWidth,popupWindowHeight);
		mPopupWindow.setFocusable(true);
		mPopupWindow.setBackgroundDrawable(new BitmapDrawable());
		mPopupWindow.setOutsideTouchable(true);			
	}
	
	private void initData(){
		
	}
	
	private void initListener(){
		mShareToWeChatCircleOfFriends.setOnClickListener(mClickListener);
		mShareToWeChatFriend.setOnClickListener(mClickListener);
		mShareToQQ.setOnClickListener(mClickListener);
		mTranspondToEnterpriseChat.setOnClickListener(mClickListener);
		mTranspondToSharing.setOnClickListener(mClickListener);
		mShareToCRMContacts.setOnClickListener(mClickListener);
		mShareByShortMessage.setOnClickListener(mClickListener);
		mShareByEmail.setOnClickListener(mClickListener);
		mOpenInBrowser.setOnClickListener(mClickListener);
		
		mCreateQRCode.setOnClickListener(mClickListener);
		mCopyLink.setOnClickListener(mClickListener);
		mRefreshWebPage.setOnClickListener(mClickListener);
	}
	
	
	private OnClickListener mClickListener = new OnClickListener() {

		@Override
		public void onClick(View v) {
			
			mPopupWindow.dismiss();

            int i = v.getId();
            if (i == R.id.share_to_wechat_circle_of_friends) {
                mWebMenuClickListener.onShareToWeChatCircleOfFriends();

            } else if (i == R.id.share_to_wechat_friend) {
                mWebMenuClickListener.onShareToWeChatFriend();

            } else if (i == R.id.share_to_qq) {
                mWebMenuClickListener.onShareToQQ();

            } else if (i == R.id.transpond_to_enterprise_chat) {
                mWebMenuClickListener.onTranspondToEnterpriseChat();

            } else if (i == R.id.transpond_to_sharing) {
                mWebMenuClickListener.onTranspondToSharing();

            } else if (i == R.id.share_to_crm_contacts) {
                mWebMenuClickListener.onShareToCRMContacts();

            } else if (i == R.id.share_by_short_message) {
                mWebMenuClickListener.onShareByShortMessage();

            } else if (i == R.id.share_by_email) {
                mWebMenuClickListener.onShareByEmail();

            } else if (i == R.id.open_in_browser) {
                mWebMenuClickListener.onOpenInBrowser();

            } else if (i == R.id.create_qr_code) {
                mWebMenuClickListener.onCreateQRCode();

            } else if (i == R.id.copy_linker) {
                mWebMenuClickListener.onCopyLink();

            } else if (i == R.id.refresh_webpage) {
                mWebMenuClickListener.onRefreshWebPage();

            } else {
            }

		}
	};	
	
	public static interface WebMenuClickListener{

		public void onShareToWeChatCircleOfFriends();
		public void onShareToWeChatFriend();
		public void onShareToQQ();
		
		public void onTranspondToEnterpriseChat();
		public void onTranspondToSharing();		
		
		public void onShareToCRMContacts();
		public void onShareByShortMessage();		
		public void onShareByEmail();		
		public void onOpenInBrowser();
		
		public void onCreateQRCode();
		public void onCopyLink();
		public void onRefreshWebPage();
	}
	
	
}
