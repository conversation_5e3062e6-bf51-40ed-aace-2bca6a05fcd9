/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.adapter;

import com.facishare.fs.i18n.I18NHelper;
import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import kotlin.reflect.jvm.internal.impl.resolve.constants.StringValue;

import com.facishare.fs.App;
import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.common_utils.time.NetworkTime;
import com.facishare.fs.pluginapi.pic.bean.IMediaGroupItem;
import com.facishare.fs.pluginapi.pic.bean.IPlayableGroupItem;
import com.facishare.fs.utils_fs.ImageLoaderUtil;
import com.facishare.fslib.R;
import com.fs.commonviews.stickygridview.StickyGridHeadersSimpleAdapter;
import com.fxiaoke.fxdblib.beans.SessionMessage;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.download.ImageDownloader;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created by tianyl on 2016/10/13.
 */
public class QixinMultiMediaGroupLookGridViewAdapter extends BaseAdapter
        implements StickyGridHeadersSimpleAdapter {
    protected final int TODAY_GROUP_TYPE = 1;
    protected final int YESTODAY_GROUP_TYPE = 2;
    protected final int WEEK_GROUP_TYPE = 3;
    protected final int OTHER_GROUP_TYPE = 4;
    FrameLayout.LayoutParams mCoulumItemLayoutParam = null;
    Context mContext;
    List<IMediaGroupItem> mDatas = new ArrayList<>();
    Date mCurrentTime = null;
    private long mTodayInitTime = 0;
    private long mYestodayInitTime = 0;
    private long mWeekInitTime = 0;

    public interface ItemClickListener{
        void onItemClick(List<? extends IMediaGroupItem> mDatas, int pos);
    }

    private ItemClickListener mItemClickListener;

    public QixinMultiMediaGroupLookGridViewAdapter(Context context, List<? extends IMediaGroupItem> datas, ItemClickListener itemClickListener) {
        mContext = context;
        mDatas.addAll(datas);
        long currentNetTime = NetworkTime.getInstance(mContext).getCurrentNetworkTime();
        mCurrentTime = new Date(currentNetTime);
        Date date = new Date();
        date.setTime(mCurrentTime.getTime());
        date.setHours(0);
        date.setMinutes(0);
        date.setSeconds(0);
        Calendar currentCalendar = Calendar.getInstance();
        currentCalendar.setTime(date);
        mTodayInitTime = currentCalendar.getTime().getTime();

        currentCalendar.add(Calendar.DATE, -1);
        mYestodayInitTime = currentCalendar.getTime().getTime();

        currentCalendar.clear();
        currentCalendar.setTime(date);
        currentCalendar.add(Calendar.DATE, -6);
        mWeekInitTime = currentCalendar.getTime().getTime();

        int columnWidth = (App.intScreenWidth - FSScreen.dip2px(2) * 3) / 4;
        mCoulumItemLayoutParam = new FrameLayout.LayoutParams(columnWidth, columnWidth);

        mItemClickListener = itemClickListener;
    }

    public void addList(List<? extends IMediaGroupItem> data) {
        mDatas.addAll(data);
        notifyDataSetChanged();
    }

    public void updateData(List data) {
        mDatas = data;
        notifyDataSetChanged();
    }

    @Override
    public int getCount() {
        return mDatas == null ? 0 : mDatas.size();
    }

    @Override
    public Object getItem(int position) {
        return mDatas == null ? null : mDatas.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public long getHeaderId(int position) {
        return getGroupingFlag(new Date(mDatas.get(position).getFileTime()), mCurrentTime);
    }

    private static String getDurationFormat(int duration){
        int min = duration / 60;
        int sec = duration - min * 60;
        String f = String.format("%02d:%02d", min, sec);
        return f;
    }
    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {
        ViewHolderChild holder = null;
        if (convertView == null) {
            holder = new ViewHolderChild();
            convertView = createConvertView(holder);
        } else {
            if (convertView.getTag() instanceof ViewHolderChild) {
                holder = (ViewHolderChild) convertView.getTag();
            } else {
                holder = new ViewHolderChild();
                convertView = createConvertView(holder);
            }
        }
        holder.mLayoutPlay.setVisibility(View.GONE);
        holder.mItemImageView.setImageResource(R.drawable.session_default_img);
        if (mDatas.get(position) != null) {
            final IMediaGroupItem mediaData = mDatas.get(position);
            ImageLoader.getInstance().displayImage(
                    ImageDownloader.Scheme.FS_Socket.wrap(mediaData
                            .getThumbPath()),
                    holder.mItemImageView,
                    ImageLoaderUtil.getSessionMsgMultiImageGroupLookOptions(mContext,
                            new SessionMessage()));
            holder.mItemImageView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mItemClickListener.onItemClick(mDatas, position);
                }
            });
            if (mediaData instanceof IPlayableGroupItem) {
                holder.mLayoutPlay.setVisibility(View.VISIBLE);
                holder.mTvDuration.setText(getDurationFormat(((IPlayableGroupItem) mediaData).getDuration()));
            } else {
                holder.mLayoutPlay.setVisibility(View.GONE);
            }

        }

        FrameLayout.LayoutParams lParams = (FrameLayout.LayoutParams) holder.mItemImageView.getLayoutParams();
        lParams.width = mCoulumItemLayoutParam.width;
        lParams.height = mCoulumItemLayoutParam.height;
        holder.mItemImageView.setLayoutParams(lParams);
        holder.mItemShader.setLayoutParams(lParams);
        holder.mLayoutPlay.setLayoutParams(lParams);

        return convertView;
    }

    private View createConvertView(ViewHolderChild holder) {
        View convertView = View.inflate(mContext, R.layout.qixin_multi_media_group_look_grid_item, null);
        if (holder != null) {
            holder.mItemImageView = (ImageView) convertView.findViewById(R.id.gride_item_image);
            holder.mItemShader = convertView.findViewById(R.id.grid_item_shader);
            holder.mItemShader.setAlpha(0.3f);
            holder.mLayoutPlay = (RelativeLayout) convertView.findViewById(R.id.layout_play);
            holder.mTvDuration = (TextView) convertView.findViewById(R.id.textView_duration);
            convertView.setTag(holder);
        }
        return convertView;
    }

    @Override
    public View getHeaderView(int position, View convertView, ViewGroup parent) {
        convertView = View.inflate(mContext, R.layout.qixin_multi_media_group_look_header_layout, null);
        TextView txtLetter = (TextView) convertView.findViewById(R.id.multi_img_group_letter_index);
        long messagetime = 0;
        if (mDatas.get(position) != null) {
            messagetime = mDatas.get(position).getFileTime();
        }
        txtLetter.setText(getGroupingDescription(new Date(messagetime)));
        return convertView;
    }

    class ViewHolderChild {
        public ImageView mItemImageView;
        public View mItemShader;
        public RelativeLayout mLayoutPlay;
        public TextView mTvDuration;
    }

    /**
     * 依据文件的上传日期和当前日期获取文件分组类型
     *
     * @param fileTime
     *
     * @return 类型值：1表示分组为“今天”类型；2表示分组为“昨天”类型，3表示分组为“一周内”类型（除去今天和昨天的5天）；4表示分组为“****年*月”的类型；
     */
    private int getGroupingType(Date fileTime) {
        int groupType = 1;
        if (fileTime != null) {
            long fileLongTime = fileTime.getTime();
            if (fileLongTime >= mTodayInitTime && fileLongTime < mTodayInitTime + 1000 * 60 * 60 * 24) {
                groupType = 1;
            } else if (fileLongTime >= mYestodayInitTime && fileLongTime < mTodayInitTime) {
                groupType = 2;
            } else if (fileLongTime >= mWeekInitTime && fileLongTime < mYestodayInitTime) {
                groupType = 3;
            } else {
                groupType = 4;
            }

        }
        return groupType;
    }

    /**
     * 依据文件的上传日期和当前日期获取文件分组描述
     * 文件分组的时间段可为“今天、昨天，一周内，2015年3月，2015年2月，2015年1月，2014年12月…”
     *
     * @param fileTime
     *
     * @return
     */
    private String getGroupingDescription(Date fileTime) {
        String timeDes = "";
        if (fileTime != null) {
            int type = getGroupingType(fileTime);
            switch (type) {
                case TODAY_GROUP_TYPE:
                    timeDes = I18NHelper.getText("xt.timing_message_create_layout.text.today")/* 今天 */;
                    break;
                case YESTODAY_GROUP_TYPE:
                    timeDes = I18NHelper.getText("xt.wyx_forward_item.text.yesterday")/* 昨天 */;
                    break;
                case WEEK_GROUP_TYPE:
                    timeDes = I18NHelper.getText("xt.biz_session_msg.GroupSessionNoticeAdapter.2")/* 一周内 */;
                    break;
                case OTHER_GROUP_TYPE:
                    Calendar fileCalendar = Calendar.getInstance();
                    fileCalendar.setTime(fileTime);

                    //session_attach_grouping_else_day_des	{0}年{1}月	qx.session.attach.grouping_des
                    String yearValue = String.valueOf(fileCalendar.get(Calendar.YEAR));
                    String monthValue = String.valueOf(fileCalendar.get(Calendar.MONTH) + 1);
                    timeDes = I18NHelper.getFormatText("qx.session.attach.grouping_des", yearValue, monthValue);
                    break;
                default:
                    break;
            }
        }
        return timeDes;
    }

    /**
     * 依据文件的上传日期和当前日期获取文件分组标示
     *
     * @param fileTime
     *
     * @return
     */
    private long getGroupingFlag(Date fileTime, Date currentTime) {
        long timeFlag = 0;
        if (currentTime != null && fileTime != null) {
            int type = getGroupingType(fileTime);
            switch (type) {
                case TODAY_GROUP_TYPE:
                    timeFlag = currentTime.getTime();
                    break;
                case YESTODAY_GROUP_TYPE:
                    Calendar yestodayCalendar = Calendar.getInstance();
                    yestodayCalendar.setTime(currentTime);
                    yestodayCalendar.add(Calendar.DATE, -1);
                    timeFlag = yestodayCalendar.getTime().getTime();
                    break;
                case WEEK_GROUP_TYPE:
                    Calendar weekCalendar = Calendar.getInstance();
                    weekCalendar.setTime(currentTime);
                    weekCalendar.add(Calendar.DATE, -6);
                    timeFlag = weekCalendar.getTime().getTime();
                    break;
                case OTHER_GROUP_TYPE://通过年和月拼
                    Calendar fileCalendar = Calendar.getInstance();
                    fileCalendar.setTime(fileTime);
                    int month = fileCalendar.get(Calendar.MONTH) + 1;
                    String fileDate = fileCalendar.get(Calendar.YEAR) + "" + month;
                    timeFlag = Long.valueOf(fileDate);
                    break;
                default:
                    break;
            }
        }
        return timeFlag;
    }

}

