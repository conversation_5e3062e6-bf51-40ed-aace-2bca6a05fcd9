package com.facishare.fs.biz_session_msg.adapter.sessionplugs;

import com.facishare.fs.biz_session_msg.constants.SessionConstants;
import com.facishare.fs.biz_session_msg.utils.SessionInfoUtils;
import com.facishare.fs.i18n.I18NHelper;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.SpannableStringBuilder;

import com.facishare.fs.biz_session_msg.SessionDefinitionUtils;
import com.facishare.fs.biz_session_msg.SessionMsgActivity;
import com.facishare.fs.biz_session_msg.customersession.CustomerSessionActivity;
import com.facishare.fs.biz_session_msg.customersession.ParentSessionCache;
import com.facishare.fs.contacts_fs.customerservice.StatService;
import com.facishare.fs.utils_fs.ToastUtils;
import com.fxiaoke.fscommon.util.FsUrlUtils;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.SessionTypeKey;
import com.fxiaoke.fxdblib.beans.sessiondefine.SessionDefinition;
import com.fxiaoke.stat_engine.StatEngine;

/**
 * Created by gyzhong on 2017/9/22.
 */

public class SessionOurUserPlug extends SessionBasePlug{
    @Override
    public String getPlugType() {
        return SessionTypeKey.Session_Out_User;
    }

    @Override
    public String getSessionIcon(Context context, SessionListRec slr) {
        return slr.getPortraitPath();
    }

    @Override
    public String getSessionName(Context context, SessionListRec slr) {
        if (SessionInfoUtils.isSupportGetDefaultNameByParticipantInfoTOList(slr)) {
            return SessionInfoUtils.getDefaultNameByParticipantInfoTOList(slr);
        }
        return slr.getSessionName();
    }

    @Override
    public SpannableStringBuilder getSessionLastSummery(Context context, SessionListRec slr) {
        return processSingleSessionLastSummary(context, slr);
    }

    @Override
    public void onClickSession(Context context, SessionListRec slr, int unreadCount, long locateMsgId) {
        SessionListRec parSession = ParentSessionCache.getInstance().getSession(slr.getRootParentSessionId()) ;
        if (parSession == null) {
            ToastUtils.show(I18NHelper.getText("qx.session_list.des.session_invaild")/* 该会话已不可用 */);
            return;
        }
//        Intent it = new Intent(context, SessionMsgActivity.class);
        Intent it = FsUrlUtils.buildSingleTaskForChatIntent();
        it.putExtra("isNeedManualBackQixin", false);
        it.putExtra(SessionConstants.INTENT_KEY_SESSION_INFO, slr);
        it.putExtra(SessionMsgActivity.Intent_key_Customer_statue, parSession.getStatus());
        SessionDefinition sessionDefinition = SessionDefinitionUtils.getDefinitionBySession(parSession);
        if (sessionDefinition != null && sessionDefinition.getData().getSessionCustomerInfo() != null) {
            SessionDefinitionUtils.appendDefinitionKeys2IntentBySession(parSession, it);
        }
        Activity activity = (Activity) context;
        activity.startActivityForResult(it, CustomerSessionActivity.CUSTOMER_SESSION_REQUEST_CODE);
        StatEngine.tick(StatService.FWH_WL_Wxkf_Second_Session_Click);
    }
}
