package com.facishare.fs.biz_session_msg.adapter.sessionplugs;

import com.facishare.fs.biz_session_msg.utils.SessionInfoUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.App;
import com.facishare.fs.MainTabActivity;
import com.facishare.fs.NoUpdateActivity;
import com.facishare.fs.biz_session_msg.SessionDefinitionUtils;
import com.facishare.fs.biz_session_msg.adapter.SessionViewType;
import com.facishare.fs.biz_session_msg.datactrl.ISessionPlugOnlongClickLis;
import com.facishare.fs.biz_session_msg.utils.MsgUtils;
import com.facishare.fs.contacts_fs.customerservice.mvp.model.CustomerDB;
import com.facishare.fs.contacts_fs.customerservice.util.CustomerStatistics;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.contact.beans.coustomer.CustomerService;
import com.fxiaoke.dataimpl.msg.MsgDataController;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.SessionTypeKey;
import com.fxiaoke.fxdblib.beans.sessiondefine.SessionDefinition;
import com.fxiaoke.fxdblib.beans.sessiondefine.SessionDefinitionData;
import com.fxiaoke.stat_engine.StatEngine;

import android.content.Context;
import android.content.Intent;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;

/**
 * Created by yangwg on 2016/2/14.
 */
public class SessionOpenServiceStagePlug extends SessionDefinitionPlug {
    /**
     * 限制可以使用的最大版本号，如果大于该值提示升级，如果小于等于该值才支持使用
     */
    private static final int LIMIT_MAX_VERSIONCODE = 542;//因本版本增加了队报表审阅消息，需要增加最大版本限制

    public SessionOpenServiceStagePlug() {
        super();
    }

    @Override
    public String getPlugType() {
        return SessionTypeKey.Session_Open_service_stage;
    }

    @Override
    public String getSessionIcon(Context context, SessionListRec slr) {
        String iconP = "";
        if (slr.isTempSession()) {
            if (!TextUtils.isEmpty(iconP)) {
                return iconP;
            }
        }
        if (slr == null) {
            return iconP;
        }

        return MsgUtils.setOSS1IconSize(super.getSessionIcon(context,slr));
    }

    @Override
    public String getSessionName(Context context, SessionListRec slr) {
        if (slr.isTempSession()) {
            String oos = slr.getSessionSubCategory();
            if (!TextUtils.isEmpty(oos) && oos.contains("open_")) {
                oos = oos.replace("open_", "");
                CustomerService mCustomerService = new CustomerDB().searchById(oos);
                if (mCustomerService != null) {
                    return mCustomerService.appName;
                }
            }
        }
        String sessionName = super.getSessionName(context, slr);
        if (TextUtils.isEmpty(sessionName)) {
            String oos = slr.getSessionSubCategory();
            if (!TextUtils.isEmpty(oos) && oos.contains("open_")) {
                oos = oos.replace("open_", "");
                // 需异步去拉取数据，不能影响主线程
                CustomerService mCustomerService = new CustomerDB().searchById(oos);
                if (mCustomerService != null) {
                    return mCustomerService.appName;
                }
            }
        }
        return sessionName;
    }

    @Override
    public SpannableStringBuilder getSessionLastSummery(Context context, SessionListRec slr) {
        SpannableStringBuilder ssbBuilder = new SpannableStringBuilder();
        if (processBaseLastMsgSummery(context, ssbBuilder, slr) == SUMMARY_TYPE_DRAFT) {
            return ssbBuilder;
        }
        //oss1有免打扰，多加免打扰判断
        if (ssbBuilder != null && (
                TextUtils.isEmpty(ssbBuilder.toString()) ||
                        slr.isSetNoStrongNotification() && slr.getNotReadCount() > 0 || slr.hasAtMe())) {
            ssbBuilder.append(getLastMsgSummeryByMsgType(context, slr, null));
        }
        return ssbBuilder;
    }

    @Override
    public void onClickSession(Context context, SessionListRec slr, int unreadCount, long locateMsgId) {
        boolean isOverMinVersionCode = false;
        SessionDefinition sd = SessionDefinitionUtils.getDefinitionBySession(slr);
        if (sd != null && sd.getData() != null) {
            if (sd.getData().getVersionCode() > LIMIT_MAX_VERSIONCODE) {
                isOverMinVersionCode = true;
            }
        }
        if (!isOverMinVersionCode) {
            String openId = slr.getSessionSubCategory();
            if (!TextUtils.isEmpty(openId) && openId.contains("open_")) {
                openId = openId.replace("open_", "");
                StatEngine.tick(CustomerStatistics.CUSTOMER_SERVICE_ENTER, openId, 0);
            }

            if (isFSMailSession(slr) || isEnterpriseMailSession(slr)) {
                gotoFSMail(context, slr);
            } else {
                onClickNormalSession(context, slr, unreadCount, locateMsgId);
            }
        } else {//
            Intent intent = new Intent(context, NoUpdateActivity.class);
            intent.putExtra(NoUpdateActivity.TITLE_NAME, slr.getSessionName());
            MainTabActivity.startActivityByAnim(intent);
        }
    }

    /**
     * 是否是纷享邮箱的session
     * add by wubb
     *
     * @param slr
     *
     * @return
     */
    private boolean isFSMailSession(SessionListRec slr) {
        String lastMessageFullSenderId = slr.getRealLastMessageFullSenderId();
        if (TextUtils.isEmpty(lastMessageFullSenderId)) {
            return false;
        }
        if (lastMessageFullSenderId.equalsIgnoreCase("OSS1.FSAID_989769")  //ceshi113
                || lastMessageFullSenderId.equalsIgnoreCase("OSS1.FSAID_989a7b") //fs
                //ceshi112
                || lastMessageFullSenderId.equalsIgnoreCase("OSS1.FSAID_989761")) {
            return true;
        }
        return false;
    }

    /**
     * 是否是企业邮箱的session
     * 备注，老的企业邮箱已经下线，现在fs环境还能搜到老的企业邮箱session,故把这个判断添加上
     * add by wubb
     *
     * @param slr
     *
     * @return
     */
    private boolean isEnterpriseMailSession(SessionListRec slr) {
        String lastMessageFullSenderId = slr.getRealLastMessageFullSenderId();
        if (TextUtils.isEmpty(lastMessageFullSenderId)) {
            return false;
        }
        if (lastMessageFullSenderId.equalsIgnoreCase("OSS1.FSAID_5f5e11a") //fs
                ) {
            return true;
        }
        return false;
    }

    /**
     * 跳转到纷享邮箱
     * add by wubb
     *
     * @param context
     * @param slr
     */
    private void gotoFSMail(Context context, SessionListRec slr) {
        if (slr.isNotReadFlag()) {
            slr.setNotReadCount(0);
            slr.setNotReadFlag(false);
            MsgDataController.getInstace(App.getInstance()).updateNotReadFlag(slr, null);
        }
        if (slr.hasAtMe()) {
            slr.maskAtMe();
            MsgDataController.getInstace(App.getInstance())
                    .updatePassiveFlags(slr, slr.getEraseAtMeFlag());
        }
        HostInterfaceManager.getHostInterface()
                .gotoFSMail(context, I18NHelper.getText("qixin.session.text.back_to_qixin")/* 企信 */);
    }
}
