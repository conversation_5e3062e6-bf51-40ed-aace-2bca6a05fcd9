
/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.views.msgtypes;

import com.facishare.fs.i18n.I18NHelper;
import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.facishare.fs.utils_fs.EmployeeUtils;
import com.facishare.fslib.R;
import com.facishare.fs.biz_session_msg.adapter.SessionMsgAdapter;
import com.facishare.fs.biz_session_msg.adapter.SessionMsgAdapter.IAdapterAction;
import com.facishare.fs.biz_session_msg.utils.AccountUtils;
import com.facishare.fs.biz_session_msg.utils.MsgUtils;
import com.facishare.fs.biz_session_msg.views.MsgCommonViewHolder;
import com.facishare.fs.contacts_fs.beans.EmployeeKey;
import com.fxiaoke.fxdblib.beans.CrossSysPrompt;
import com.fxiaoke.fxdblib.beans.MsgTypeKey;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.SessionMessage;
import com.fxiaoke.fxdblib.beans.SysPrompt;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

public class MsgCrossSysViewItem extends MsgViewBase {
    TextView mtvTextView;
    TextView mSendTimeView;
    public MsgCrossSysViewItem(int oritention){
        super(oritention);
    }
    public MsgCrossSysViewItem(Context ctx, LayoutInflater lif, int oritention, MsgViewBaseExtraData extraData) {
        super(ctx, oritention, extraData);
        View convertView = null;
        convertView = lif.inflate(R.layout.session_item_layout_system, null);

        initCommonView(convertView);
        mviewHolder.tag = convertView.findViewById(R.id.tv_chatcontent);
        mtvTextView = (TextView) convertView.findViewById(R.id.tv_system);
        mSendTimeView = (TextView) convertView.findViewById(R.id.tv_sendtime);
        mLayoutitemView = convertView;

        mLayoutitemView.setTag(this);
    }

    public View getViewLayout() {
        return mLayoutitemView;
    }

    public MsgCommonViewHolder getCommonViewHolder() {
        return mviewHolder;
    }
    @Override
    public void refreshViews(final SessionMessage sm){
        super.refreshViews(sm);
        refreshViews(sm, true);
    }

    /**
     * @param sm
     * @param isCanRefreshUnknownEmployInfo 是否响应更新停用员工信息
     */
    public void refreshViews(final SessionMessage sm, boolean isCanRefreshUnknownEmployInfo) {
        String proString = "";
        CrossSysPrompt prompt = null;
        if (sm.getMessageType().equals(MsgTypeKey.MSG_Cross_SystemPrompt_key)) {
            prompt = sm.getCrossSysPrompt();
            proString += makePromptString(prompt, mSessionListRec, this, Integer.MAX_VALUE);
        } /*else if (sm.getModifiedMethod()!=ModifiedMethod.MessageNotModified) {
        	String name=FSContextManager.getCurUserContext().getContactCache().getUser((int)sm.getSenderId()).getName();
			if (sm.getSenderId()==myid) {
				name=I18NHelper.getText("qx.session.msg_des.mysend");
			}
			if (sm.getModifiedMethod()==ModifiedMethod.MessageRevoked) {
				sm.setContent(name+I18NHelper.getText("qx.session.msg_des.revoke_a_msg"));
			}else if (sm.getModifiedMethod()==ModifiedMethod.MessageHarmonized) {
				sm.setContent(name+I18NHelper.getText("qx.session.msg_des.msg_blocked"));
			}
            proString += sm.getContent();
        }*/
        if (proString == null || "".equals(proString)) {
            mtvTextView.setVisibility(View.GONE);
            mSendTimeView.setVisibility(View.GONE);
        } else {
            mtvTextView.setVisibility(View.VISIBLE);
            mSendTimeView.setVisibility(View.VISIBLE);
            mtvTextView.setText(proString);
        }
        if (mViewStatus == SessionMsgAdapter.ViewStatus.normal) {
            mLayoutitemView.setClickable(true);
        } else if (mViewStatus == SessionMsgAdapter.ViewStatus.edit) {
            mLayoutitemView.setClickable(false);
            mLayoutitemView.setOnClickListener(null);
        }
    }

//    protected List<Integer> options = initMenuOptions(
//            LongMenuActionType.READ_STATUS
//    );

    @Override
    protected List<Integer> getMenuOptions() {
        checkInitMenuOptions(
                LongMenuActionType.READ_STATUS
        );
        return menuOptions;
    }

    public static String makeShortPromptString(String json, SessionListRec slr) {
        CrossSysPrompt prompt = JSON.parseObject(json, CrossSysPrompt.class);
        return makePromptString(prompt, slr, null, 4);
    }

    static String makePromptString(CrossSysPrompt prompt, SessionListRec slr, MsgViewBase msgViewBase, int maxNameCount) {
        String proString = "";
        if (prompt != null) {
            String type = prompt.getT();
            switch (type) {
                case "Break"://企业互联单聊 断开 的消息
                    proString = getBreakDes(prompt, msgViewBase);
                    break;
                case "Renew"://企业互联单聊 恢复 的消息
                    proString = getRenewDes(prompt, msgViewBase);
                    break;
                case "Invite"://讨论组 邀请加入 的消息
                    proString = getPromptUA(prompt, "qx.session.msg_des.join_talk"/* {0}邀请{1}加入群对话 */, msgViewBase, maxNameCount);
                    break;
                case "Exit"://讨论组 自我退出 的消息
                    proString = getPromptU(prompt, "qx.session.msg_des.someone_quit_talk"/* {0}退出群对话 */, msgViewBase);
                    break;
                case "Kick"://讨论组 踢人 的消息
                    proString = getPromptUA(prompt, "qx.session.msg_des.kick_talk"/* {0}将{1}移出群对话 */, msgViewBase, maxNameCount);
                    break;
                case "Name"://讨论组 改名 的消息
                    proString = getPromptUV(prompt, "qx.session.msg_des.change_session_name"/* {0}将群对话的名称修改为"{1}" */, msgViewBase);
                    break;
                case "SingleWarning"://单聊 发出警示 的消息
                    proString = I18NHelper.getText("qx.session.msg_des.be_discreet_in_cross_single_session")/* 对方为外公司员工，请谨言慎行 */;
                    break;
                case "DiscussionWarning"://讨论组 发出警示 的消息
                    proString = I18NHelper.getText("qx.session.msg_des.be_discreet_in_cross_group_session")/* 群内有外公司员工，请谨言慎行 */;
                    break;
            }
        }
        return proString;
    }

    /**
     * 企业互联单聊 断开
     */
    private static String getBreakDes(CrossSysPrompt prompt, MsgViewBase msgViewBase) {
        EmployeeKey employeeKey = MsgUtils.getEmployeeInfoFromFullSenderId(prompt.getU());
        String proString;
        if (AccountUtils.isMySelf(employeeKey)) {
            proString = I18NHelper.getText("qx.session.msg_des.lose_your_talk_permission")/* 你已不是对接人，当前会话已失效 */;
        } else {
            proString = I18NHelper.getText("qx.session.msg_des.lose_his_talk_permission")/* 对方已不是对接人，当前会话已失效 */;
        }
        return proString;
    }

    /**
     * 企业互联单聊 恢复
     */
    private static String getRenewDes(CrossSysPrompt prompt, MsgViewBase msgViewBase) {
        EmployeeKey employeeKey = MsgUtils.getEmployeeInfoFromFullSenderId(prompt.getU());
        String proString;
        if (AccountUtils.isMySelf(employeeKey)) {
            proString = I18NHelper.getText("qx.session.msg_des.get_your_talk_permission")/* 你的对接人身份已恢复，请继续对话吧 */;
        } else {
            proString = I18NHelper.getText("qx.session.msg_des.get_his_talk_permission")/* 对方对接人身份已恢复，请继续对话吧 */;
        }
        return proString;
    }

    /**
     * PATTERN_U
     * 用U字段的员工名称 替换 "{0}"
     */
    private static String getPromptU(CrossSysPrompt prompt, String i18nKey, MsgViewBase msgViewBase) {
        EmployeeKey employeeKey = MsgUtils.getEmployeeInfoFromFullSenderId(prompt.getU());
        String name = (AccountUtils.isMySelf(employeeKey)) ?
                I18NHelper.getText("qx.session.msg_des.replace_myself")/* 你 */ : MsgViewBase.getCrossEmployeeName(msgViewBase, employeeKey);
        return EmployeeUtils.adjustFirstMyselfEnDes(I18NHelper.getFormatText(i18nKey, name));
    }

    /**
     * PATTERN_U_V
     * 用U字段的员工名称 替换 "{0}"
     * 用V字段的文字 替换 "{1}"
     */
    private static String getPromptUV(CrossSysPrompt prompt, String i18nKey, MsgViewBase msgViewBase) {
        EmployeeKey employeeKey = MsgUtils.getEmployeeInfoFromFullSenderId(prompt.getU());
        String name = (AccountUtils.isMySelf(employeeKey)) ?
                I18NHelper.getText("qx.session.msg_des.replace_myself")/* 你 */ : MsgViewBase.getCrossEmployeeName(msgViewBase, employeeKey);
        return EmployeeUtils.adjustFirstMyselfEnDes(I18NHelper.getFormatText(i18nKey, name, prompt.getV()));
    }

    /**
     * PATTERN_U_A
     * 用U字段的员工名称 替换 "{0}"
     * 用A字段的多条员工名称 替换 "{1}"
     */
    private static String getPromptUA(CrossSysPrompt prompt, String i18nKey, MsgViewBase msgViewBase, int maxNameCount) {
        if (prompt.getA() == null) {
            return "";
        }
        boolean exceedNameLimit = false;
        int size = Math.min(prompt.getA().size() + 1, maxNameCount);
        List<EmployeeKey> employeeKeys = new ArrayList<>(size);
        EmployeeKey employeeKeyU = MsgUtils.getEmployeeInfoFromFullSenderId(prompt.getU());
        employeeKeys.add(employeeKeyU);
        for (String fullId : prompt.getA()) {
            // 过滤掉[你邀请你]和[A邀请A]的情况
            EmployeeKey employeeKey = MsgUtils.getEmployeeInfoFromFullSenderId(fullId);
            if (employeeKey.equals(employeeKeyU)) {
                continue;
            }
            if (employeeKeys.size() >= maxNameCount) {
                exceedNameLimit = true;
                break;
            }
            employeeKeys.add(employeeKey);
        }
        // 过滤不合理的显示数据
        if (employeeKeys.size() <= 1) {
            return "";
        }
        List<String> employeeNames = MsgViewBase.getCrossEmployeeNames(msgViewBase, employeeKeys);
        // 检查人员名称
        for (int i = 0; i < employeeKeys.size(); ++i) {
            if (AccountUtils.isMySelf(employeeKeys.get(i))) {
                employeeNames.set(i, I18NHelper.getText("qx.session.msg_des.replace_myself")/* 你 */);
            }
        }
        String partA = employeeNames.get(0);
        String partU = TextUtils.join("、", employeeNames.subList(1, employeeNames.size()));
        if (exceedNameLimit) {
            partU += I18NHelper.getText("crm.customfieldviews.LookupModel.1650")/* 等 */;
        }
        return EmployeeUtils.adjustFirstMyselfEnDes(I18NHelper.getFormatText(i18nKey, partA, partU));
    }

    @Override
    public boolean isMyType(SessionMessage sessionMessage, int orientation) {
        boolean isRet = false;
        if(sessionMessage!=null&&sessionMessage.getMessageType()!=null){
            String msgType = sessionMessage.getMessageType();
            if(MsgTypeKey.MSG_Cross_SystemPrompt_key.equals(msgType)){
                isRet = true;
            }
        }
        return isRet;
    }

    @Override
    public MsgViewBase newInstance(Context ctx,LayoutInflater lif, int or, MsgViewBaseExtraData extraData) {
        return new MsgCrossSysViewItem(ctx, lif, or, extraData);
    }
}
