package com.facishare.fs.biz_session_msg.views.msgtypes;

import com.facishare.fs.i18n.I18NHelper;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.facishare.fs.MainTabActivity;
import com.facishare.fs.ui.FeedsUitls;
import com.facishare.fslib.R;
import com.facishare.fs.biz_feed.XFeedDetailActivity;
import com.facishare.fs.biz_feed.subbizfavourite.FavouriteEditTagsActivity;
import com.facishare.fs.biz_session_msg.adapter.SessionMsgAdapter.ViewStatus;
import com.facishare.fs.biz_session_msg.views.MsgCommonViewHolder;
import com.facishare.fs.common_utils.DateTimeUtils;
import com.fs.beans.beans.EnumDef;
import com.fs.beans.beans.EnumDef.FeedType;
import com.fxiaoke.fxdblib.beans.MsgTypeKey;
import com.fxiaoke.fxdblib.beans.SessionMessage;
import com.fxiaoke.fxdblib.beans.WorkNoticeMsgData;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

@TargetApi(Build.VERSION_CODES.HONEYCOMB)
public class MsgWorkNoticeViewItem extends MsgViewBase {
    View mWorkNoticeLayout;
    TextView mtvTitle;
    TextView mtvSummaryTitle,mtvSummaryContent;
    ImageView image_work_item;
    TextView worknotice_time;

    public MsgWorkNoticeViewItem(int oritention) {
        super(oritention);
    }

    public MsgWorkNoticeViewItem(Context ctx, LayoutInflater lif, int oritention, MsgViewBaseExtraData extraData) {
        super(ctx, oritention, extraData);
        View convertView = null;
        View content = null;
        if (oritention == 0) {//left
            convertView = lif.inflate(R.layout.session_item_layout_left, null);
            content = lif.inflate(R.layout.session_item_layout_left_worknotice, null);
        } else {//right
            convertView = lif.inflate(R.layout.session_item_layout_right, null);
            content = lif.inflate(R.layout.session_item_layout_right_worknotice, null);
        }
        LinearLayout llc = (LinearLayout) convertView.findViewById(R.id.tv_msgcontent);
        llc.addView(content);
        initCommonView(convertView);
        setMaxWidth();
        mWorkNoticeLayout = convertView.findViewById(R.id.worknotice_content);
        mWorkNoticeLayout.setTag(this);
        mviewHolder.tag = mWorkNoticeLayout;
        mtvTitle = (TextView) convertView.findViewById(R.id.worknotice_title);
        mtvSummaryTitle = (TextView) convertView.findViewById(R.id.worknotice_sumary_title);
        mtvSummaryContent = (TextView) convertView.findViewById(R.id.worknotice_sumary_content);
        image_work_item = (ImageView) convertView.findViewById(R.id.image_work_item);
        worknotice_time = (TextView) convertView.findViewById(R.id.worknotice_time);
        mLayoutitemView = convertView;
        mLayoutitemView.setTag(this);
    }

    public static WorkNoticeMsgData getWorkNoticeMsgData(String json) {
        WorkNoticeMsgData ret = null;
        try {

            ret = JSON.parseObject(json, WorkNoticeMsgData.class);
        } catch (JSONException e) {
            // TODO: handle exception
        }
        return ret;
    }

    public View getViewLayout() {
        return mLayoutitemView;
    }

    public MsgCommonViewHolder getCommonViewHolder() {
        return mviewHolder;
    }
    @Override
    public void refreshViews(final SessionMessage sm) {
        super.refreshViews(sm);
        refreshViews_common(sm);
        if (sm.getWorkNoticeMsgData() == null) {
            return;
        }

        mtvTitle.setText(I18NHelper.getText("qx.session.msg_des.igt_title_for_notice")/* 通知 */);
        mtvTitle.setTextColor(0xff091E42);
        mtvSummaryTitle.setText(sm.getWorkNoticeMsgData().getT());
        mtvSummaryContent.setText(sm.getWorkNoticeMsgData().getC());
        image_work_item.setImageResource(R.drawable.msg_worknotice_icon);

        int sendId = sm.getWorkNoticeMsgData().getS();
        String empName = getInnerEmployeeName(this, sendId);
        String string = I18NHelper.getFormatText("bi.layout.item_checkbox_multi_layout_search.2150.v1"/* 来自{0} */ , empName + " ");
        worknotice_time
                .setText(string + DateTimeUtils.formatDateMsgCommonShow(new Date(sm.getWorkNoticeMsgData().getD())));

        if (mViewStatus == ViewStatus.normal) {
            mWorkNoticeLayout.setOnLongClickListener(mMsgContextMenu);
            mWorkNoticeLayout.setOnClickListener(new OnClickListener() {

                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(mLayoutitemView.getContext(), XFeedDetailActivity.getFeedDetailActivity());
                    intent.putExtra(XFeedDetailActivity.FEED_ID_KEY, sm.getWorkNoticeMsgData().getF());
                    intent.putExtra(XFeedDetailActivity.FEED_TYPE_KEY, EnumDef.FeedType.WorkNotice.value);
                    MainTabActivity.startActivityByAnim(mLayoutitemView.getContext(), intent);
                }
            });
        } else if (mViewStatus == ViewStatus.edit) {
            mWorkNoticeLayout.setLongClickable(false);
            mWorkNoticeLayout.setClickable(false);
        }
    }

//    protected List<Integer> options = initMenuOptions(
//            LongMenuActionType.REVOKE,
//            LongMenuActionType.READ_STATUS,
//            LongMenuActionType.ADD_TO_BOARD,
//            LongMenuActionType.REPLY,
//            LongMenuActionType.FORWARD,
//            LongMenuActionType.ADD_TO_FAVORITE,
//            LongMenuActionType.DELETE,
//            LongMenuActionType.MULTI_SELECT
//    );

    @Override
    protected List<Integer> getMenuOptions() {
        checkInitMenuOptions(
                LongMenuActionType.REVOKE,
                LongMenuActionType.READ_STATUS,
                LongMenuActionType.ADD_TO_BOARD,
                LongMenuActionType.REPLY,
                LongMenuActionType.FORWARD,
                LongMenuActionType.ADD_TO_FAVORITE,
                LongMenuActionType.DELETE,
                LongMenuActionType.MULTI_SELECT
        );
        return menuOptions;
    }

    @Override
    public void addToFavourite(SessionMessage msg) {
        Intent it = FavouriteEditTagsActivity.getIntent(context, msg.getWorkNoticeMsgData().getF() + "");
        context.startActivity(it);
    }

    @Override
    public boolean isMyType(SessionMessage sessionMessage, int orientation) {
        boolean isRet = false;
        if (sessionMessage != null && sessionMessage.getMessageType() != null) {
            String msgType = sessionMessage.getMessageType();
            if (MsgTypeKey.MSG_WorkNotice_key.equals(msgType) && mOrientation == orientation) {
                isRet = true;
            }
        }
        return isRet;
    }

    @Override
    public MsgViewBase newInstance(Context ctx, LayoutInflater lif, int oritention, MsgViewBaseExtraData extraData) {
        return new MsgWorkNoticeViewItem(ctx, lif, oritention, extraData);
    }
}
