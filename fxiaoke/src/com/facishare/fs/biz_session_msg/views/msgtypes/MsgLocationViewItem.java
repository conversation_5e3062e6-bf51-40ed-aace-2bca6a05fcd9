package com.facishare.fs.biz_session_msg.views.msgtypes;

import java.util.List;

import com.facishare.fs.biz_session_msg.utils.SendMsgHelper;
import com.facishare.fs.biz_session_msg.views.NewMaskImageView;
import com.facishare.fslib.R;
import com.facishare.fs.biz_session_msg.adapter.SessionMsgAdapter.ViewStatus;
import com.facishare.fs.biz_session_msg.views.MsgCommonViewHolder;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.location.PluginFsLocationResult;
import com.facishare.fs.utils_fs.ImageLoaderUtil;
import com.fxiaoke.fxdblib.beans.LocationMsgData;
import com.fxiaoke.fxdblib.beans.MsgTypeKey;
import com.fxiaoke.fxdblib.beans.SessionMessage;
import com.nostra13.universalimageloader.core.DisplayImageOptions;
import com.nostra13.universalimageloader.core.ImageLoader;

import android.content.Context;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.ImageView;
import android.widget.LinearLayout;

public class MsgLocationViewItem extends MsgViewBase {

    NewMaskImageView mivImageView;
    DisplayMetrics mDisplayMetrics;
    int mDefaultWdp = 167;
    int mDefaultHdp = 87;
    public DisplayImageOptions mapOption;

    public MsgLocationViewItem(int oritention) {
        super(oritention);
    }

    public MsgLocationViewItem(Context ctx, LayoutInflater lif, int oritention, MsgViewBaseExtraData extraData) {
        super(ctx, oritention, extraData);
        View convertView = null;
        View content = null;
        if (oritention == 0) {//left
            convertView = lif.inflate(R.layout.session_item_layout_left, null);
            content = lif.inflate(R.layout.session_item_layout_left_location, null);
        } else {//right
            convertView = lif.inflate(R.layout.session_item_layout_right, null);
            content = lif.inflate(R.layout.session_item_layout_right_location, null);
        }
        LinearLayout llc = (LinearLayout) convertView.findViewById(R.id.tv_msgcontent);
        llc.addView(content);
        initCommonView(convertView);
        mviewHolder.tag = convertView.findViewById(R.id.iv_LocationImg);
        mivImageView = (NewMaskImageView) convertView.findViewById(R.id.iv_LocationImg);
        mivImageView.setDpiFlag(false);
        mLayoutitemView = convertView;
        mivImageView.setTag(this);
        mLayoutitemView.setTag(this);
        mDisplayMetrics = ctx.getResources().getDisplayMetrics();
        if (mapOption == null) {
            mapOption = ImageLoaderUtil.getMapDisplayImageOptions(ctx);
        }
    }

    public void clearSrc() {
        super.clearSrc();
        mivImageView.clear();
        mivImageView.setTag(null);
        mivImageView = null;
    }

    public View getViewLayout() {
        return mLayoutitemView;
    }

    public MsgCommonViewHolder getCommonViewHolder() {
        return mviewHolder;
    }
    @Override
    public void refreshViews(SessionMessage entity) {
        super.refreshViews(entity);
        refreshViews_common(entity);
        LocationMsgData lmd = mSessionMessage.getLocationMsgData();
        if (lmd == null) {
            ImageLoader.getInstance().displayImage("", mivImageView);
            return;
        }

        if (mViewStatus == ViewStatus.normal) {
            mivImageView.setOnLongClickListener(mMsgContextMenu);
            mivImageView.setOnClickListener(new OnClickListener() {

                @Override
                public void onClick(View v) {
                    // open map with location
                    LocationMsgData msgData = mSessionMessage.getLocationMsgData();
                    if (SendMsgHelper.startShowAvaMapDetail(context, msgData.getLongitude() + "", msgData.getLatitude() + "", msgData.getText())) {
                        return;
                    }
                    PluginFsLocationResult address = new PluginFsLocationResult(msgData.getLatitude(), msgData.getLongitude
                            ());
                    address.setAddress(msgData.getText());
                    HostInterfaceManager.getIMap().showAddress(context, address);
                }
            });
        } else {
            mivImageView.setClickable(false);
            mivImageView.setLongClickable(false);
        }
//        mivImageView.setBottomText(lmd.getText());
//        mivImageView.setSize(mDefaultWdp, mDefaultHdp);
        String keyValue = HostInterfaceManager.getCloudCtrlManager().getStringConfig("location_item_map_key_875","a9790423dbe92f7c846dd5455f53ad3e");
        ImageLoader.getInstance().displayImage("http://restapi.amap.com/v3/staticmap?location=" +
                lmd.getLongitude() + "," + lmd.getLatitude() +
                "&zoom=15&size=" + (int) (mDefaultWdp * mDisplayMetrics.density) + "*" + (int) (mDefaultHdp * mDisplayMetrics.density) + "&markers=mid,,A:" + lmd.getLongitude() + "," + lmd
                .getLatitude() +
                "&key=" +keyValue
                + "", mivImageView, mapOption);
    }

//    protected List<Integer> options = initMenuOptions(
//            LongMenuActionType.REVOKE,
//            LongMenuActionType.READ_STATUS,
//            LongMenuActionType.REPLY,
//            LongMenuActionType.ADD_TO_FAVORITE,
//            LongMenuActionType.DELETE,
//            LongMenuActionType.MULTI_SELECT
//    );


    @Override
    protected List<Integer> getMenuOptions() {
        checkInitMenuOptions(
                LongMenuActionType.REVOKE,
                LongMenuActionType.READ_STATUS,
                LongMenuActionType.REPLY,
                LongMenuActionType.ADD_TO_FAVORITE,
                LongMenuActionType.DELETE,
                LongMenuActionType.MULTI_SELECT
        );
        return menuOptions;
    }

    @Override
    public boolean isMyType(SessionMessage sessionMessage, int orientation) {
        boolean isRet = false;
        if (sessionMessage != null && sessionMessage.getMessageType() != null) {
            String msgType = sessionMessage.getMessageType();
            if (MsgTypeKey.MSG_Location_key.equals(msgType) && mOrientation == orientation) {
                isRet = true;
            }
        }
        return isRet;
    }

    @Override
    public MsgViewBase newInstance(Context ctx, LayoutInflater lif, int oritention, MsgViewBaseExtraData extraData) {
        return new MsgLocationViewItem(ctx, lif, oritention, extraData);
    }
}
