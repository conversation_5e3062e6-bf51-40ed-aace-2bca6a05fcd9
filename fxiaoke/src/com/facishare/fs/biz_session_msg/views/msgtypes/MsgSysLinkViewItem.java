
package com.facishare.fs.biz_session_msg.views.msgtypes;

import com.billy.cc.core.component.CC;
import com.billy.cc.core.component.CCResult;
import com.billy.cc.core.component.IComponentCallback;
import com.facishare.fs.biz_session_msg.dialog.FunctionTipDialog;
import com.facishare.fs.biz_session_msg.subbiz.msg_page.QixinCCComponent;
import com.facishare.fs.biz_session_msg.subbiz.msg_page.imagetext.edit.SendMixMessageActivity;
import com.facishare.fs.biz_session_msg.utils.FeedBizUtils;
import com.facishare.fs.contacts_fs.XPersonBaseActivity;
import com.facishare.fs.contacts_fs.beans.UnknownDataUpdateEvent;
import com.facishare.fs.i18n.I18NHelper;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.alibaba.fastjson.JSON;
import com.facishare.fs.BaseActivity;
import com.facishare.fs.MainTabActivity;
import com.facishare.fs.biz_feed.FeedMeetingShowActivity;
import com.facishare.fs.biz_function.subbizmeetinghelper.MeetingDetailActivity;
import com.facishare.fs.biz_function.subbizmeetinghelper.MeetingQuestionActivity;
import com.facishare.fs.biz_function.subbizmeetinghelper.MeetingQuestionAddActivity;
import com.facishare.fs.biz_function.subbizmeetinghelper.MeetingQuestionDetailsActivity;
import com.facishare.fs.biz_function.subbizmeetinghelper.MeetingSummaryActivity;
import com.facishare.fs.biz_function.subbizmeetinghelper.beans.GetMeetingInfoResult;
import com.facishare.fs.biz_function.subbizmeetinghelper.beans.MeetingInfo;
import com.facishare.fs.biz_session_msg.SessionMsgActivity;
import com.facishare.fs.biz_session_msg.adapter.SessionMsgAdapter;
import com.facishare.fs.biz_session_msg.beans.MsgElementData;
import com.facishare.fs.biz_session_msg.beans.QixinStatisticsEvent;
import com.facishare.fs.biz_session_msg.datactrl.IEditTextProvider;
import com.facishare.fs.biz_session_msg.sessionsettings.SessionGroupManagementActivity;
import com.facishare.fs.biz_session_msg.utils.AccountUtils;
import com.facishare.fs.biz_session_msg.utils.MsgUtils;
import com.facishare.fs.biz_session_msg.utils.MsgWebApiUtils;
import com.facishare.fs.biz_session_msg.constants.SessionConstants;
import com.facishare.fs.biz_session_msg.utils.SessionInfoUtils;
import com.facishare.fs.biz_session_msg.views.MsgCommonViewHolder;
import com.facishare.fs.contacts_fs.PersonDetailFloatActivity;
import com.facishare.fs.contacts_fs.XPersonActivity;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.utils_fs.EmployeeUtils;
import com.facishare.fs.utils_fs.FeedConfig;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fslib.R;
import com.fs.beans.beans.EnumDef;
import com.fxiaoke.fscommon.util.FsUrlUtils;
import com.fxiaoke.fxdblib.beans.MixMessageContent;
import com.fxiaoke.fxdblib.beans.MsgTypeKey;
import com.fxiaoke.fxdblib.beans.SessionMessage;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.lib.qixin.related_biz.feed.AvatarFeedUtils;
import com.fxiaoke.stat_engine.StatEngine;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import de.greenrobot.event.core.PublisherEvent;

public class MsgSysLinkViewItem extends MsgViewBase {
    /**
     * 会议签到跳转链接类型
     */
    private final static String ACTION_TYPE_MEETING_LOCATION_DES = "meeting/register";
    /**
     * 会议助手发起提问跳转
     */
    private final static String ACTION_TYPE_MEETING_QUESTION_DES = "meeting/question";
    /**
     * 会议助手提问列表跳转
     */
    private final static String ACTION_TYPE_MEETING_QUESTION_LIST_DES = "meeting/meetingQuestion";
    /**
     * 会议助手查看提问跳转
     */
    private final static String ACTION_TYPE_MEETING_FINDQUESTION_DES = "meeting/findQuestion";
    /**
     * 会议助手查看回答跳转
     */
    private final static String ACTION_TYPE_MEETING_FINDANSWER_DES = "meeting/findAnswer";
    /**
     * 会议助手会议详情跳转
     */
    private final static String ACTION_TYPE_MEETING_DETAILS_DES = "meeting/details";
    /**
     * 会议助手会议纪要跳转
     */
    private final static String ACTION_TYPE_MEETING_SUMMARY_DES = "meeting/summary";
    /**
     * 会议助手会后代办跳转
     */
    private final static String ACTION_TYPE_MEETING_AGENT_DES = "meeting/agent";
    /**
     * 会议助手会后代办跳转
     */
    private final static String ACTION_TYPE_LOOKING_DES = "user/userInfo";
    /**
     * 项目详情跳转
     */
    private final static String ACTION_TYPE_PROJECT_INFO = "projectManage/info";
    /**
     * 新成员加入-共享历史消息
     */
    private final static String ACTION_TYPE_SHARE_HISTORY_MESSAGE = "message/shareHistory";
    /**
     * 新成员加入-授权历史消息
     */
    private final static String ACTION_TYPE_AUTHORIZE_HISTORY_MESSAGE = "message/authorizeHistory";
    /**
     * 在聊天输入框中插入文字
     */
    private final static String ACTION_TYPE_INSERT_TEXT = "input/insertText";
    /**
     * 跳转群管理
     */
    private final static String ACTION_TYPE_GROUP_MANAGE = "session/manage";
    /**
     *  跳转编辑撤回后的图文消息界面（本地用）
     */
    private static final String SEND_MIX_MSG_URL = "fs://qixin/sendmixmsg";
    /**
     *  跳转生成历史消息逻辑（本地用）
     */
    private static final String ACTION_GENERATE_HISTORY_SUMMARY = "fs://qixin/message/generateHistorySummary";
    TextView mtvTextView;

    public MsgSysLinkViewItem(int oritention) {
        super(oritention);
    }

    public MsgSysLinkViewItem(Context ctx, LayoutInflater lif, int oritention, MsgViewBaseExtraData extraData) {
        super(ctx, oritention, extraData);
        View convertView = null;
        convertView = lif.inflate(R.layout.session_item_layout_link_system, null);

        initCommonView(convertView);
        mviewHolder.tag = convertView.findViewById(R.id.tv_chatcontent);
        mtvTextView = (TextView) convertView.findViewById(R.id.tv_system);
        mLayoutitemView = convertView;

        mLayoutitemView.setTag(this);
    }

    public View getViewLayout() {
        return mLayoutitemView;
    }

    public MsgCommonViewHolder getCommonViewHolder() {
        return mviewHolder;
    }
    @Override
    public void refreshViews(SessionMessage sm) {
        super.refreshViews(sm);
        String proString = "";

        SpannableStringBuilder ssb = jointContext(sm.getContent(), true);
//        //过滤掉我
//        String sysContent=ssb.toString();
//        String name=FSContextManager. getCurUserContext(). getAccount().getEmployeeName();
//            if(sysContent.startsWith(name)){
//                sysContent=sysContent.replaceFirst(name,I18NHelper.getText("qx.session.msg_des.mysend")/* 你 */);
//            }

        mtvTextView.setText(ssb);
        if (mViewStatus == SessionMsgAdapter.ViewStatus.normal) {
            mLayoutitemView.setClickable(true);
        } else if (mViewStatus == SessionMsgAdapter.ViewStatus.edit) {
            mtvTextView.setClickable(false);
            mLayoutitemView.setOnClickListener(null);
        }
    }

    protected static List<MsgElementData> buildMsgElementFromTextMessage(SessionMessage msg) {
        com.alibaba.fastjson.JSONObject object = new com.alibaba.fastjson.JSONObject();
        object.put("content", msg.getContent());
        String linkAddress = "fs://qixin/input/insertText?" + object.toJSONString();

        List<MsgElementData> elementDataList = new ArrayList<>(2);
        elementDataList.add(new MsgElementData("#ffffff", "f4", I18NHelper.getText("xt.views.MsgSysLinkViewItem.1")/* 你撤回了一条消息  */, "false"));
        elementDataList.add(new MsgElementData("#3487E2", "f4", I18NHelper.getText("xt.views.MsgSysLinkViewItem.2")/* 重新编辑 */, "false", linkAddress));
        return elementDataList;
    }

    protected static List<MsgElementData> buildMsgElementFromMixMessage(SessionMessage msg) {
//        com.alibaba.fastjson.JSONObject object = new com.alibaba.fastjson.JSONObject();
//        object.put("content", msg.getContent());
        String linkAddress = SEND_MIX_MSG_URL/* + "?"+object.toJSONString()*/;
        List<MsgElementData> elementDataList = new ArrayList<>(2);
        elementDataList.add(new MsgElementData("#ffffff", "f4", I18NHelper.getText("xt.views.MsgSysLinkViewItem.1")/* 你撤回了一条消息  */, "false"));
        elementDataList.add(new MsgElementData("#3487E2", "f4", I18NHelper.getText("xt.views.MsgSysLinkViewItem.2")/* 重新编辑 */, "false", linkAddress));
        return elementDataList;
    }

    /**
     * 解析拼接字符串，添加点击事件
     *
     * @param content
     * @param isClickable
     * @return
     */
    private SpannableStringBuilder jointContext(String content, Boolean isClickable) {
        List<MsgElementData> ugtElementItemDataList;
        if (MsgTypeKey.MSG_Text_key.equals(mSessionMessage.getMessageType())) {
            ugtElementItemDataList = buildMsgElementFromTextMessage(mSessionMessage);
        } else if (MsgTypeKey.MSG_MIX.equals(mSessionMessage.getMessageType())) {
            ugtElementItemDataList = buildMsgElementFromMixMessage(mSessionMessage);
        } else {
            try {
                ugtElementItemDataList = JSON.parseArray(content, MsgElementData.class);
            } catch (com.alibaba.fastjson.JSONException e) {
                ugtElementItemDataList = null;
                FCLog.e(TAG, "JSON.parse: " + content);
            }
        }
        SpannableStringBuilder ssb = new SpannableStringBuilder();
        if (ugtElementItemDataList != null && ugtElementItemDataList.size() > 0) {
            int i = 0;
            for (MsgElementData itemData : ugtElementItemDataList) {
                int len = ssb.length();
                String newContent = itemData.content;
                if (i != 0 && "true".equals(itemData.newLine)) {
                    ssb.append("\n");
                }

//                String nameUser=FSContextManager. getCurUserContext(). getAccount().getEmployeeName();
//                if(newContent.equals(nameUser)){
//                    newContent=newContent.replaceFirst(nameUser,I18NHelper.getText("qx.session.msg_des.mysend")/* 你 */);
//                }

                //判断被操作的对象是不是你
                if (itemData.linkAddress != null && !TextUtils.isEmpty(itemData.linkAddress)) {
                    mtvTextView.setMovementMethod(LinkMovementMethod.getInstance());
                    final String linkAddress = itemData.linkAddress;
                    //判断是否是你
                    String action = null;
                    String paramsJson = null;
                    if (!TextUtils.isEmpty(linkAddress)) {
                        FCLog.i(linkAddress);
                        if (linkAddress.startsWith("fs://")) {//处理各种内部链接跳转
                            String[] datas = linkAddress.split("\\?");
                            action = datas[0];
                            paramsJson = "";
                            if (datas.length > 1) {
                                paramsJson = datas[1];
                            }
                            String name = isYourSelf(action, paramsJson);
                            if (name != null) {
                                newContent = name;
                            }
                        }
                    }
                }

                if (!TextUtils.isEmpty(newContent)) {
                    newContent = replaceFullIdByUserName(newContent);
                    ssb.append(newContent);
                }

                int color;
                try {
                    color = Color.parseColor(itemData.color);
                } catch (IllegalArgumentException e) {
                    color = Color.WHITE;
                    FCLog.e(TAG, "sys link Color.parseColor-IllegalArgumentException argument=" + itemData.color);
                }
                final int finalColor = color;
                if (itemData.linkAddress != null && !TextUtils.isEmpty(itemData.linkAddress) && isClickable) {
                    mtvTextView.setMovementMethod(LinkMovementMethod.getInstance());
                    final String linkAddress = itemData.linkAddress;
                    ssb.setSpan(new ClickableSpan() {
                                @Override
                                public void onClick(View widget) {
                                    if (mViewStatus == SessionMsgAdapter.ViewStatus.edit) {
                                        return;
                                    }
                                    if (TextUtils.isEmpty(linkAddress)) {
                                        return;
                                    }
                                    FCLog.i(TAG, "sys link " + linkAddress);
                                    if (!SessionInfoUtils.checkSessionCanSendMsg(mSessionListRec)) {
                                        ToastUtils.showToast(SessionInfoUtils.getSessionInvalidText(mSessionListRec));
                                        return;
                                    }
                                    if (FsUrlUtils.FsScheme.FS.belongsTo(linkAddress)) {//处理各种内部链接跳转
                                        String[] datas = linkAddress.split("\\?");
                                        String action = datas[0];
                                        String paramsJson = "";
                                        if (datas.length > 1) {
                                            paramsJson = datas[1];
                                        }
                                        processFsSkipAction(linkAddress, action, paramsJson);
                                    } else {
                                        MsgUtils.dealGotoAction(context, linkAddress, "", "", false);
                                    }
                                }

                                @Override
                                public void updateDrawState(TextPaint ds) {
                                    ds.setUnderlineText(false);
                                    ds.setColor(finalColor);
                                }
                            }, len, len + newContent.length(),
                            SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
                } else {
                    ssb.setSpan(new ForegroundColorSpan(color), len, len + newContent.length(),
                            SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
                }

                i++;
            }
        }
        return ssb;
    }

    /**
     * 判断是不是自己
     *
     * @param action
     * @param paramsJson
     * @return
     */
    private static String isYourSelf(String action, String paramsJson) {
        String name = null;
        JSONObject jsonObject = null;
        String userId = null;
        try {//{\"meetingId\":\"56d53f603c2d978634e27458\", \"questionId\":\"56d693803c2d973d76aebb57\"}"
            if(paramsJson!=null&&paramsJson.contains("{")&&paramsJson.contains("}")){
                jsonObject = new JSONObject(paramsJson);
                userId = getContentFromJson(jsonObject, "urserId");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (action.contains(ACTION_TYPE_LOOKING_DES)) {
            int accountId = Integer.parseInt(userId);
            if (accountId == FSContextManager.getCurUserContext().getAccount().getEmployeeIntId()) {
                name = I18NHelper.getText("qx.session.msg_des.replace_myself")/* 你 */;
            }
        }
        return name;
    }

    /**
     * 处理各种内部链接跳转
     *
     * @param action     链接类型
     * @param paramsJson 链接参数
     */
    private void processFsSkipAction(final String sourceLinkAddress,String action, String paramsJson) {
        JSONObject jsonObject = null;
        String meetingId = null;
        String questionId = null;
        String userId = null;
        try {//{\"meetingId\":\"56d53f603c2d978634e27458\", \"questionId\":\"56d693803c2d973d76aebb57\"}"
            jsonObject = new JSONObject(paramsJson);
            meetingId = getContentFromJson(jsonObject, "meetingId");
            questionId = getContentFromJson(jsonObject, "questionId");
            userId = getContentFromJson(jsonObject, "urserId");
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (!TextUtils.isEmpty(meetingId)) {
            if (action.contains(ACTION_TYPE_MEETING_LOCATION_DES)) {//会议签到链接类型
                MeetingDetailActivity.startMeetingDetail(context, meetingId);
            } else if (action.contains(ACTION_TYPE_MEETING_QUESTION_DES)) {//会议发起提问链接类型
                Intent intent = new Intent(context, MeetingQuestionAddActivity.class);
                intent.putExtra(MeetingQuestionAddActivity.MEETING_ID, meetingId);
                intent.putExtra(MeetingQuestionAddActivity.ADD_TYPE, MeetingQuestionAddActivity.ADD_QUESTION);
                context.startActivity(intent);//ACTION_TYPE_MEETING_QUESTION_LIST_DES
            } else if (action.contains(ACTION_TYPE_MEETING_QUESTION_LIST_DES)) {//会议提问列表链接类型
                StatEngine.tick("Meeting_156");
                MeetingInfo meetingInfo = new MeetingInfo();
                meetingInfo.meetingId = meetingId;
                if (mSessionListRec != null) {
                    meetingInfo.sessionId = mSessionListRec.getSessionId();
                }
                MeetingQuestionActivity.startMeetingQuestionAct(context, meetingInfo);
            } else if (action.contains(ACTION_TYPE_MEETING_FINDQUESTION_DES) || action.contains(ACTION_TYPE_MEETING_FINDANSWER_DES)) {
                //会议查看提问链接类型 //会议查看回答链接类型
                Intent intent = MeetingQuestionDetailsActivity.getIntent(context, questionId);
                MainTabActivity.startActivityByAnim(context, intent);
            } else if (action.contains(ACTION_TYPE_MEETING_DETAILS_DES)) {//会议详情链接类型
                MeetingDetailActivity.startMeetingDetail(context, meetingId);
            } else if (action.contains(ACTION_TYPE_MEETING_SUMMARY_DES)) {//会议纪要链接类型
                MeetingInfo info = new MeetingInfo();
                info.meetingId = meetingId;
                MeetingSummaryActivity.startMeetingSummaryActivity(context, info);
            } else if (action.contains(ACTION_TYPE_MEETING_AGENT_DES)) {//会后代办链接类型
                ((BaseActivity) context).showDialog(BaseActivity.DIALOG_WAITING_UPDATE_MODE);
                MsgWebApiUtils.sendRequestMeeting(mSessionListRec, context, 0, new MsgWebApiUtils.MeetingCallback() {
                    @Override
                    public void sendResult(boolean isSuccess, GetMeetingInfoResult response) {
                        ((BaseActivity) context).removeDialog(BaseActivity.DIALOG_WAITING_UPDATE_MODE);
                        if (isSuccess && response != null && response.code == 1) {
                            FeedConfig config = new FeedConfig.Builder().setTitle(I18NHelper.getText("xt.meeting_detail_header_layout.text.meeting_task")/* 会议任务 */).build();
                            config.workFeedFilterType = EnumDef.WorkFeedFilterType.MeetingAfterSolve.value;
                            config.meetingId = response.meetingInfo.meetingId;
                            config.feedType = EnumDef.FeedType.Task.value;
                            Intent intent = FeedMeetingShowActivity.getIntent(context, config, response.meetingInfo);
                            context.startActivity(intent);
                        }
                    }
                });

            }
        } else if (action.contains(ACTION_TYPE_LOOKING_DES)) {
            StatEngine.tick("welcome_new_systemlink_msg_click");
            int accountId = Integer.parseInt(userId);
            if (accountId == FSContextManager.getCurUserContext().getAccount().getEmployeeIntId()) {
                //MainTabActivity.startActivityByAnim(context, XPersonDetailActivity.getIntent(context, null,
                //accountId));
//                MainTabActivity.startActivityByAnim(PersonDetailFloatActivity.getIntent(context, accountId, true));
                PersonDetailFloatActivity.start(context, accountId,true);
            } else {
                MainTabActivity.startActivityByAnim(context, XPersonActivity.getCreateIntent(context, accountId));
            }
        } else if (action.contains(ACTION_TYPE_PROJECT_INFO)) { //跳转项目详情界面
            MsgUtils.dealGotoAction(context,action+"?"+paramsJson,I18NHelper.getText("qixin.OSS1.XMGL.name")/* 项目管理 */,"",false);
        } else if (action.contains(ACTION_TYPE_SHARE_HISTORY_MESSAGE)) {
            if (!SessionInfoUtils.checkSessionCanUse(mSessionListRec)) {
                ToastUtils.show(SessionInfoUtils.getSessionInvalidText(mSessionListRec));
                return;
            }
            SessionMsgActivity activity = (SessionMsgActivity) context;
            if (!activity.isEditMode()) {
                StatEngine.tick(QixinStatisticsEvent.SHARE_HISTORY_CLICK_MSG);
                activity.showShareHistoryMessage();
            }
        } else if (action.contains(ACTION_TYPE_AUTHORIZE_HISTORY_MESSAGE)) {
            if (!SessionInfoUtils.checkSessionCanUse(mSessionListRec)) {
                ToastUtils.show(SessionInfoUtils.getSessionInvalidText(mSessionListRec));
                return;
            }
            List<String> employeeIds = null;
            try {
                JSONArray array = jsonObject.getJSONArray("employeeIds");
                if (array.length() > 0) {
                    employeeIds = new ArrayList<>(array.length());
                    for (int i = 0; i < array.length(); ++i) {
                        employeeIds.add(array.getString(i));
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
            if (employeeIds != null) {
                SessionMsgActivity activity = (SessionMsgActivity) context;
                activity.showAuthorizeHistoryMessage(employeeIds, mSessionMessage.getMessageId());
            }
        } else if (action.contains(ACTION_TYPE_INSERT_TEXT)) {
            if (!SessionInfoUtils.checkSessionCanUse(mSessionListRec)) {
                ToastUtils.show(SessionInfoUtils.getSessionInvalidText(mSessionListRec));
                return;
            }
            try {
                if (context instanceof IEditTextProvider) {
                    IEditTextProvider editTextProvider = (IEditTextProvider) context;
                    String content = jsonObject.getString("content");
                    editTextProvider.getEditText().append(content);
                    editTextProvider.focusEditText();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (action.contains(ACTION_TYPE_GROUP_MANAGE)) {
            if (SessionInfoUtils.isDissolvedSession(mSessionListRec) ||
                    !SessionInfoUtils.isGroupAdministrator(mSessionListRec)) {
                ToastUtils.show(I18NHelper.getText("qx.session.msg_des.only_group_owner_permission")/* 仅当前群主可进行此操作 */);
                return;
            }
            if (!SessionInfoUtils.checkSessionCanUse(mSessionListRec)) {
                ToastUtils.show(SessionInfoUtils.getSessionInvalidText(mSessionListRec));
                return;
            }
            SessionMsgActivity activity = (SessionMsgActivity) context;
            SessionGroupManagementActivity.startGroupManagerAct(activity, mSessionListRec,
                    SessionConstants.ACTIVITY_RST_REQ_GROUP_MANAGE);
        } else if (action.contains("message/shareAppointedSessionHistory")) {
            MsgUtils.dealGotoAction(context, mSessionListRec.getSessionId(), sourceLinkAddress, null, null, false, false,
                    -1, mSessionListRec.getEnterpriseEnvType());
        } else if (action.contains(SEND_MIX_MSG_URL)) {
            processSendMixMsgUrl();
        } else if(action.startsWith(ACTION_GENERATE_HISTORY_SUMMARY)){
            //将fs://qixin/message/generateHistorySummary转为本地能处理的事件event//qixin/message/generateHistorySummary，
            //具体响应处理逻辑的代码位置： QixinCCComponent.processGenerateHistorySummary
            FsUrlUtils.ActionConfig config = new FsUrlUtils.ActionConfig((Activity) context, ACTION_GENERATE_HISTORY_SUMMARY.replaceFirst("fs:", "event:"));
            config.addParams(QixinCCComponent.KEY_FOR_SESSION_ID, mSessionListRec.getSessionId());
            config.addParams(QixinCCComponent.KEY_FOR_MESSAGE_ID, mSessionMessage.getMessageId());
            config.addParams(QixinCCComponent.KEY_FOR_ENTERPRISE_ENV_TYPE, mSessionListRec.getEnterpriseEnvType());
            config.setEventTypeCallBack(new IComponentCallback() {
                @Override
                public void onResult(CC cc, CCResult result) {
                    if (result != null && !result.isSuccess()) {
                        ToastUtils.show(result.getErrorMessage());
                    }
                }
            });
            FsUrlUtils.gotoAction(config);
        } else {
            MsgUtils.dealGotoAction(context, mSessionListRec.getSessionId(), sourceLinkAddress, null, null, false, false,
                    -1,mSessionListRec.getEnterpriseEnvType());
        }
    }
    //SEND_MIX_MSG_URL
    private void processSendMixMsgUrl() {
        SendMixMessageActivity.start(context, -1, mSessionListRec, null, mSessionMessage.getContent(), false);
    }

    private List<String> getNeedDownloadPaths(SessionMessage mSessionMessage) {
        List<String> resultList = new ArrayList<>();
        return resultList;
    }

//    protected List<Integer> options = initMenuOptions(
//            LongMenuActionType.READ_STATUS
//    );

    @Override
    protected List<Integer> getMenuOptions() {
        checkInitMenuOptions(
                LongMenuActionType.READ_STATUS
        );
        return menuOptions;
    }

    public static String makePromptString(String json) {
        int myID = AccountUtils.getMyID();
        List<MsgElementData> ugtElementItemDataList = JSON.parseArray(json, MsgElementData.class);
        SpannableStringBuilder ssb = new SpannableStringBuilder();
        if (ugtElementItemDataList != null && ugtElementItemDataList.size() > 0) {
            int i = 0;
            for (MsgElementData itemData : ugtElementItemDataList) {
                String newContent = itemData.content;
                if (i != 0 && "true".equals(itemData.newLine)) {
                    ssb.append("/n");
                }

                //判断被操作的对象是不是你
                if (itemData.linkAddress != null && !TextUtils.isEmpty(itemData.linkAddress)) {
                    final String linkAddress = itemData.linkAddress;
                    //判断是否是你
                    String action = null;
                    String paramsJson = null;
                    if (!TextUtils.isEmpty(linkAddress)) {
                        FCLog.i(linkAddress);
                        if (linkAddress.startsWith("fs://")) {//处理各种内部链接跳转
                            String[] datas = linkAddress.split("\\?");
                            action = datas[0];
                            paramsJson = "";
                            if (datas.length > 1) {
                                paramsJson = datas[1];
                            }
                            String name = isYourSelf(action, paramsJson);
                            if (name != null) {
                                newContent = name;
                            }
                        }
                    }
                }
                if(itemData.employeeId!=0){
                    newContent = itemData.content;
                    if(itemData.employeeId==myID){
                        newContent = I18NHelper.getText("qx.session.msg_des.replace_myself")/* 你 */;
                    }
                }
                if (!TextUtils.isEmpty(newContent)) {
                    ssb.append(newContent);
                }

                i++;
            }
        }

        return EmployeeUtils.adjustFirstMyselfEnDes(ssb.toString());
    }

    @Override
    public boolean isMyType(SessionMessage sessionMessage, int orientation) {
        boolean isRet = false;
        if (sessionMessage != null) {
            String msgType = sessionMessage.getMessageType();
            if (MsgTypeKey.MSG_SYSTEM_LINK_MESSAGE.equals(msgType)) {
                isRet = true;
            } else if (allowReeditTextMessage(sessionMessage)) {
                isRet = true;
            }
        }
        return isRet;
    }

    @Override
    public MsgViewBase newInstance(Context ctx, LayoutInflater lif, int or, MsgViewBaseExtraData extraData) {
        return new MsgSysLinkViewItem(ctx, lif, or, extraData);
    }
}
