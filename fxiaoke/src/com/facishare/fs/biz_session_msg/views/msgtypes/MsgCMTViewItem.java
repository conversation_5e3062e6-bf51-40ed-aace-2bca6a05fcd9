package com.facishare.fs.biz_session_msg.views.msgtypes;

import java.util.ArrayList;
import java.util.List;

import com.facishare.fs.biz_session_msg.adapter.SessionMsgAdapter;
import com.facishare.fs.biz_session_msg.views.custom_msg_template.CMTLayoutEngine;
import com.facishare.fs.biz_session_msg.views.custom_msg_template.CMTNodeLayoutJson;
import com.facishare.fs.biz_session_msg.views.custom_msg_template.CMTNodeLayoutProvider;
import com.facishare.fs.biz_session_msg.views.view_ctrl.MsgCMTViewClickHelper;
import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.logutils.QXLogUtils;
import com.facishare.fslib.R;
import com.fxiaoke.fxdblib.beans.MsgCMTNodeListData;
import com.fxiaoke.fxdblib.beans.MsgTypeKey;
import com.fxiaoke.fxdblib.beans.SessionMessage;
import com.fxiaoke.fxlog.FCLog;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

/**
 * Created by yangwg on 2018/11/9.
 */

public class MsgCMTViewItem extends MsgViewBase {
    View mContentLayout;

    public MsgCMTViewItem(int or) {
        super(or);
    }

    public MsgCMTViewItem(Context ctx, int or) {
        super(ctx, or,null);
    }

    public MsgCMTViewItem(Context ctx, LayoutInflater lif, int oritention, MsgViewBaseExtraData extraData) {
        super(ctx, oritention, extraData);
        View convertView = null;
        View content = null;

        if (oritention == ORIENTATION_TYPE_FROM) {//left
            convertView = lif.inflate(R.layout.session_item_layout_left, null);
            //            content = lif.inflate(R.layout.session_item_layout_left_ugt_template, null);
        } else {//right
            convertView = lif.inflate(R.layout.session_item_layout_right, null);
            //            content = lif.inflate(R.layout.session_item_layout_right_ugt_template, null);
        }
        //初始化根布局
        LinearLayout llc = (LinearLayout) convertView.findViewById(R.id.tv_msgcontent);
        int defaultWidth = ctx.getResources().getDimensionPixelSize(R.dimen.qx_msg_card_type_with);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(defaultWidth, LinearLayout
                .LayoutParams.WRAP_CONTENT);
        if (oritention == ORIENTATION_TYPE_FROM) {//left
            llc.setBackgroundResource(R.drawable.chat_cmt_from_bg_shader_selector);
            llc.setPadding(FSScreen.dip2px(7f), 0, 0, 0);
        } else {//right
            llc.setBackgroundResource(R.drawable.chat_cmt_to_bg_shader_selector);
            llc.setPadding(0, 0, FSScreen.dip2px(7f), 0);
        }
        llc.setLayoutParams(lp);
        llc.setOrientation(LinearLayout.VERTICAL);

        initCommonView(convertView);
        setMaxWidth();
        SessionMessage sm = extraData!=null?extraData.entity:null;
        MsgCMTNodeListData json = getCMTData(sm);
        if (json != null && !TextUtils.isEmpty(json.getUseTemplate())) {
            CMTNodeLayoutJson layoutJson = CMTNodeLayoutProvider.getLayoutJson(json.getUseTemplate(), sm);
            if (layoutJson != null) {
                mContentLayout = CMTLayoutEngine.parserLayout(ctx, layoutJson);
                if (mContentLayout != null) {
                    mContentLayout.setTag(R.id.cmt_use_template, json.getUseTemplate());
                    llc.addView(mContentLayout);
                }else{//xt.unknown_feed_display_plug.text.unkown_type
                    TextView view = new TextView(context);
                    view.setTextSize(14);
                    view.setTextColor(Color.parseColor("#333333"));
                    view.setText(I18NHelper.getText("xt.session_item_layout_right_no_text.text.get_a_new_msg")/* 你收到一条新消息，请升级最新版本 */);
                    llc.addView(view);
                }
            } else {
                FCLog.i(TAG, "Not found template data " + sm.toString());
            }
        }
        mLayoutitemView = convertView;
        mLayoutitemView.setTag(this);
        mContentLayout.setTag(this);
        //        mInteractiveViewItemHeight = ctx.getResources().getDimensionPixelOffset(R.dimen
        // .qx_msg_card_type_title_height);
    }

    private MsgCMTNodeListData getCMTData(SessionMessage sm) {
        return sm != null ? sm.getMsgCMTNodeListData() : null;
    }

    @Override
    public void refreshViews(final SessionMessage sm) {
        super.refreshViews(sm);
        refreshViews_common(sm);
        boolean isClickEnabled = mViewStatus == SessionMsgAdapter.ViewStatus.normal;
        CMTLayoutEngine.refreshNodeView(context, mContentLayout, sm, isClickEnabled);
        if (isClickEnabled && sm.getMsgCMTNodeListData() != null) {
            mContentLayout.setOnLongClickListener(mMsgContextMenu);
            mContentLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    String address = null;
                    String subMsgType = null;
                    if (sm.getMsgCMTNodeListData() != null) {
                        if (!TextUtils.isEmpty(sm.getMsgCMTNodeListData().getLink())) {
                            address = sm.getMsgCMTNodeListData().getLink();
                        }
                        if (!TextUtils.isEmpty(sm.getMsgCMTNodeListData().getBiz())) {
                            subMsgType = sm.getMsgCMTNodeListData().getBiz();
                        }
                    }
                    stickMsgClickEvent(address, subMsgType);
                    MsgCMTViewClickHelper.processClickEvent(context,sm);
                }
            });
        } else if (mViewStatus == SessionMsgAdapter.ViewStatus.edit) {
            mContentLayout.setLongClickable(false);
            mContentLayout.setClickable(false);
        }
    }

    @Override
    public boolean isMyType(SessionMessage sessionMessage, int orientation) {
        boolean isRet = false;
        if (sessionMessage != null && sessionMessage.getMessageType() != null) {
            String msgType = sessionMessage.getMessageType();
            if (MsgTypeKey.MSG_CMT.equals(msgType)&&mOrientation == orientation) {
                isRet = true;
                if(!checkHasSupportedTemplate(sessionMessage)){//支持未知模板时显示升级提示消息
                    isRet = false;
                }
            }

        }
        return isRet;
    }

    private boolean checkHasSupportedTemplate(SessionMessage sm) {
        MsgCMTNodeListData json = getCMTData(sm);
        boolean hasSupportedTemplate = true;
        if (json != null) {
            if (!TextUtils.isEmpty(json.getUseTemplate())) {
                CMTNodeLayoutJson layoutJson = CMTNodeLayoutProvider.getLayoutJson(json.getUseTemplate(), sm);
                if (layoutJson == null) {
                    hasSupportedTemplate = false;
                }
            } else {
                hasSupportedTemplate = false;
            }
        }
        return hasSupportedTemplate;
    }

//    @Override
//    public MsgViewBase newInstance(Context context, LayoutInflater lif, int orientation) {
//        throw new RuntimeException("need sessionMessage");
//    }

    @Override
    public MsgViewBase newInstance(Context context, LayoutInflater lif, int orientation, MsgViewBaseExtraData extraData) {
        if (extraData == null || extraData.entity == null) {
            throw new RuntimeException("need sessionMessage");
        }
        return new MsgCMTViewItem(context, lif, orientation, extraData);
    }

//    protected List<Integer> options = initMenuOptions(
//            LongMenuActionType.REVOKE,
//            LongMenuActionType.READ_STATUS,
//            LongMenuActionType.ADD_TO_BOARD,
//            LongMenuActionType.REPLY,
//            LongMenuActionType.FORWARD,
//            LongMenuActionType.DELETE,
//            LongMenuActionType.MULTI_SELECT
//    );

    @Override
    protected List<Integer> getMenuOptions() {
        checkInitMenuOptions(
                LongMenuActionType.REVOKE,
                LongMenuActionType.READ_STATUS,
                LongMenuActionType.ADD_TO_BOARD,
                LongMenuActionType.REPLY,
                LongMenuActionType.FORWARD,
                LongMenuActionType.DELETE,
                LongMenuActionType.MULTI_SELECT
        );
        return menuOptions;
    }

    @Override
    public boolean needRebuild(SessionMessage sm) {
        boolean needRebuild = super.needRebuild(sm);
        if (!needRebuild && MsgTypeKey.MSG_CMT.equals(sm.getMessageType()) && sm.getContent() != null) {
            if (mContentLayout != null && mContentLayout.getTag() instanceof String) {
                String target = sm.getMsgCMTNodeListData().getUseTemplate();//当前初始布局不是目标布局模板时，要重新构建
                if (!mContentLayout.getTag(R.id.cmt_use_template).equals(target)) {
                    needRebuild = true;
                    QXLogUtils.tLog("needRebuild by cmt UseTemplate changed: " + sm.getMessageId());
                }
            }
        }
        return needRebuild;
    }
}
