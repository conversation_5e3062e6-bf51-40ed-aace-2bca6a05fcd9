package com.facishare.fs.biz_session_msg.views;

import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fslib.R;
import com.fxiaoke.fxlog.FCLog;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Bitmap.Config;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Paint.FontMetricsInt;
import android.graphics.Path;
import android.graphics.PorterDuff.Mode;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.view.ViewGroup;
import android.widget.ImageView;

/**
 *  为适配新版UI的新气泡图view，去掉了原来的尖角，改为圆角+左上直角或右上直角
 */
public class NewMaskImageView extends ImageView {
    private static int CORNER_RADIUS_DP = FSScreen.dp2px(16); // 圆角半径（px） 目前对应 dimen/common_large_rectangle_rounded_corner_radius，即16dp值
    private static final int NINEPATCH_FILL_COLOR = 0xff424242;
    private static final int BOTTOM_TEXT_COLOR = 0xffffffff;
    private static final int BUBBLE_BACKGROUND_COLOR = 0xffe5e5e5; // 气泡背景色

    private int orientation;
    private String mBottomText;
    private int centerIconResId = 0;
    private boolean mIsMask = false;
    private boolean mDpiFlag = true;
    private boolean mCropImage = false;

    private DisplayMetrics displayMetrics;
    int mWdp, mHdp;

    private Rect mRect;
    private Bitmap mBitmap;
    private Bitmap mMaskBitmap;

    public NewMaskImageView(Context context) {
        this(context, null);
    }

    public NewMaskImageView(Context context, AttributeSet attr) {
        super(context, attr);
        TypedArray a = context.obtainStyledAttributes(attr, R.styleable.MasgImageView);// TypedArray是一个数组容器
        orientation = a.getInt(R.styleable.MasgImageView_oritention, 0);// 防止在XML文件里没有定义，就加上了默认值
        a.recycle();

        displayMetrics = getContext().getResources().getDisplayMetrics();
        CORNER_RADIUS_DP = getContext().getResources().getDimensionPixelSize(R.dimen.common_large_rectangle_rounded_corner_radius);
    }

    public void setOrientation(int orientation) {
        this.orientation = orientation;
    }

    public void setCenterIconResId(int centerIconResId) {
        this.centerIconResId = centerIconResId;
    }

    public void setDpiFlag(boolean dpiFlag) {
        mDpiFlag = dpiFlag;
    }

    public void setBottomText(String text) {
        mBottomText = text;
    }

    public void setCropImage(boolean cropImage) {
        mCropImage = cropImage;
    }

    public void clear() {
        if (mBitmap != null) {
            if (!mBitmap.isRecycled()) {
                mBitmap.recycle();
            }
            mBitmap = null;
        }
        if (mMaskBitmap != null) {
            if (!mMaskBitmap.isRecycled()) {
                mMaskBitmap.recycle();
            }
            mMaskBitmap = null;
        }
    }

    @Override
    protected void onDraw(Canvas canvas) { // 这里就是重写的方法了，想画什么形状自己动手
        super.onDraw(canvas);
    }

    public boolean greyMask(boolean isMask) {
        mIsMask = isMask;
        boolean isMasked = false;
        if (mBitmap == null) {
            FCLog.i(FCLog.debug_multipic_upload, "greyMask null " + this.hashCode());
            return isMasked;
        }
        if (isMask) {
            FCLog.i(FCLog.debug_multipic_upload, "greyMask " + this.hashCode());
            greyMask();
            isMasked = true;
            super.setImageBitmap(mMaskBitmap);
        } else {
            FCLog.i(FCLog.debug_multipic_upload, "greyMask cancel " + this.hashCode());
            if (mMaskBitmap != null) {
                mMaskBitmap.recycle();
            }
            super.setImageBitmap(mBitmap);
        }
        return isMasked;
    }

    public boolean isMask() {
        return mIsMask;
    }

    public void setSize(int wdp, int hdp) {
        mWdp = wdp;
        mHdp = hdp;
        ViewGroup.LayoutParams p = getLayoutParams();
        p.width = (int) (mWdp * displayMetrics.density);
        p.height = (int) (mHdp * displayMetrics.density);
        setLayoutParams(p);
    }

    void greyMask() {
        if (mMaskBitmap != null && !mMaskBitmap.isRecycled()) {
            mMaskBitmap.recycle();
        }
        mMaskBitmap = Bitmap.createBitmap(mBitmap.getWidth(), mBitmap.getHeight(), Config.ARGB_4444);

        Canvas canvas = new Canvas(mMaskBitmap);
        canvas.drawBitmap(mBitmap, 0, 0, new Paint());
        Rect rect = new Rect(0, 0, mMaskBitmap.getWidth(), mMaskBitmap.getHeight());
        Paint pb = new Paint();
        pb.setARGB(100, 0, 0, 0);
        pb.setAntiAlias(true);
        pb.setStyle(Paint.Style.FILL);
        pb.setXfermode(new PorterDuffXfermode(Mode.SRC_ATOP));
        canvas.drawRect(rect, pb);
    }

    /**
     * 绘制自定义气泡背景
     * 
     * @param canvas 画布
     * @param rect   绘制区域
     * @param isLeft 是否为左侧气泡
     */
    private void drawCustomBubbleBackground(Canvas canvas, Rect rect, boolean isLeft) {
        float cornerRadius = CORNER_RADIUS_DP;

        Path path = new Path();
        RectF rectF = new RectF(rect);

        // 根据方向决定哪个顶角为直角
        if (isLeft) {
            // 左侧气泡：左上角为直角，其他角为圆角
            path.moveTo(rectF.left, rectF.top); // 左上角起点
            path.lineTo(rectF.right - cornerRadius, rectF.top); // 顶边到右上角圆角开始
            path.quadTo(rectF.right, rectF.top, rectF.right, rectF.top + cornerRadius); // 右上角圆角
            path.lineTo(rectF.right, rectF.bottom - cornerRadius); // 右边到右下角圆角开始
            path.quadTo(rectF.right, rectF.bottom, rectF.right - cornerRadius, rectF.bottom); // 右下角圆角
            path.lineTo(rectF.left + cornerRadius, rectF.bottom); // 底边到左下角圆角开始
            path.quadTo(rectF.left, rectF.bottom, rectF.left, rectF.bottom - cornerRadius); // 左下角圆角
            path.lineTo(rectF.left, rectF.top); // 左边到起点（左上角直角）
        } else {
            // 右侧气泡：右上角为直角，其他角为圆角
            path.moveTo(rectF.left + cornerRadius, rectF.top); // 从左上角圆角开始
            path.lineTo(rectF.right, rectF.top); // 顶边到右上角（直角）
            path.lineTo(rectF.right, rectF.bottom - cornerRadius); // 右边到右下角圆角开始
            path.quadTo(rectF.right, rectF.bottom, rectF.right - cornerRadius, rectF.bottom); // 右下角圆角
            path.lineTo(rectF.left + cornerRadius, rectF.bottom); // 底边到左下角圆角开始
            path.quadTo(rectF.left, rectF.bottom, rectF.left, rectF.bottom - cornerRadius); // 左下角圆角
            path.lineTo(rectF.left, rectF.top + cornerRadius); // 左边到左上角圆角开始
            path.quadTo(rectF.left, rectF.top, rectF.left + cornerRadius, rectF.top); // 左上角圆角
        }
        path.close();

        Paint bgPaint = new Paint();
        bgPaint.setAntiAlias(true);
        bgPaint.setColor(BUBBLE_BACKGROUND_COLOR);
        bgPaint.setStyle(Paint.Style.FILL);

        canvas.drawPath(path, bgPaint);
    }

    @Override
    public void setImageResource(int resId) {
        Bitmap temp = BitmapFactory.decodeResource(getResources(), resId);
        setImageBitmap(temp, true);
    }

    @Override
    public void setImageBitmap(Bitmap bit) {
        setImageBitmap(bit, false);
    }

    void setImageBitmap(Bitmap bitmap, boolean isdefault) {
        if (bitmap == null || bitmap.isRecycled()) {
            super.setImageBitmap(null);
            return;
        }
        int viewWidth, viewHeight;
        if (mWdp != 0) {
            viewWidth = (int) (mWdp * displayMetrics.density);
            viewHeight = (int) (mHdp * displayMetrics.density);
        } else {
            viewWidth = mDpiFlag ? (int) (bitmap.getWidth() * displayMetrics.density) : bitmap.getWidth();
            viewHeight = mDpiFlag ? (int) (bitmap.getHeight() * displayMetrics.density) : bitmap.getHeight();
        }
        if (viewHeight <= 0 || viewWidth <= 0) {
            return;
        }

        if (mBitmap != null && !mBitmap.isRecycled()) {
            mBitmap.recycle();
        }
        mRect = new Rect(0, 0, viewWidth, viewHeight);
        mBitmap = Bitmap.createBitmap(viewWidth, viewHeight, Config.ARGB_4444);

        Rect rect;
        if (mCropImage) {
            float ratioWidth = ((float) bitmap.getWidth()) / mRect.width();
            float ratioHeight = ((float) bitmap.getHeight()) / mRect.height();
            float ratioMin = Math.min(ratioWidth, ratioHeight);

            float displayWidth = mRect.width() * ratioMin;
            float displayHeight = mRect.height() * ratioMin;

            int left = (int) ((bitmap.getWidth() - displayWidth) / 2);
            int right = (int) ((bitmap.getWidth() + displayWidth) / 2);
            int top = (int) ((bitmap.getHeight() - displayHeight) / 2);
            int bottom = (int) ((bitmap.getHeight() + displayHeight) / 2);

            rect = new Rect(left, top, right, bottom);
        } else {
            rect = new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight());
        }

        Canvas canvas = new Canvas(mBitmap);
        canvas.drawARGB(0, 0, 0, 0);

        // 绘制自定义气泡背景
        drawCustomBubbleBackground(canvas, new Rect(0, 0, viewWidth, viewHeight), orientation == 0);

        Paint paint = new Paint();
        paint.setAntiAlias(true);
        paint.setColor(NINEPATCH_FILL_COLOR);
        paint.setXfermode(new PorterDuffXfermode(Mode.SRC_IN));
        canvas.drawBitmap(bitmap, rect, mRect, paint);

        if (this.centerIconResId > 0) {
            Bitmap centerIconBitmap = BitmapFactory.decodeResource(getResources(), centerIconResId);
            int length = FSScreen.dip2px(getContext(), 36);

            int offset = FSScreen.dip2px(getContext(), (float) 8 / 3);
            if (orientation == 0) {
                offset = -offset;
            }
            Rect r2 = new Rect(viewWidth / 2 - length / 2 - offset, viewHeight / 2 - length / 2,
                    viewWidth / 2 + length / 2 - offset,
                    viewHeight / 2 + length / 2);
            canvas.drawBitmap(centerIconBitmap, null, r2, null);
        }
        if (mBottomText != null) {
            int bottomH = (int) (20 * displayMetrics.density);
            Rect targetRect = new Rect(0, viewHeight - bottomH, viewWidth, viewHeight);
            Paint pb = new Paint();
            pb.setAntiAlias(true);
            pb.setColor(BUBBLE_BACKGROUND_COLOR);
            pb.setStyle(Paint.Style.FILL);
            pb.setXfermode(new PorterDuffXfermode(Mode.SRC_ATOP));
            canvas.drawRect(targetRect, pb);
            int paddingLeft = (int) (3 * displayMetrics.density);
            if (orientation == 0) {
                paddingLeft += (int) (6 * displayMetrics.density);
            }
            int paddingTop = (int) (2 * displayMetrics.density);
            Paint pbt = new Paint();
            pbt.setTextSize(12 * displayMetrics.density);
            pbt.setSubpixelText(true);
            pbt.setAntiAlias(true);
            pbt.setColor(BOTTOM_TEXT_COLOR);
            pbt.setXfermode(new PorterDuffXfermode(Mode.SRC_IN));
            FontMetricsInt fontMetrics = paint.getFontMetricsInt();
            int baseline = targetRect.top
                    + (targetRect.bottom - targetRect.top - fontMetrics.bottom + fontMetrics.top) / 2 - fontMetrics.top
                    + paddingTop;
            canvas.drawText(mBottomText, paddingLeft, baseline, pbt);
        }

        if (!mIsMask) {
            FCLog.i(FCLog.debug_multipic_upload, "noMask " + this.hashCode());
            super.setImageBitmap(mBitmap);
        } else {
            FCLog.i(FCLog.debug_multipic_upload, "greyMask " + this.hashCode());
            greyMask();
            super.setImageBitmap(mMaskBitmap);
        }

    }

}
