/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_session_msg.filepreview;

import com.facishare.fs.common_utils.photo.ImageTypeUtils;
import com.facishare.fs.i18n.I18NHelper;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.TypeReference;
import com.facishare.fs.biz_function.subbiz_fsnetdisk.utils.FSNetDiskDataUtil;
import com.facishare.fs.biz_function.subbiz_fsnetdisk.utils.FSNetDiskFileUtil;
import com.facishare.fs.biz_personal_info.UserDownFileActivity;
import com.facishare.fs.biz_session_msg.SessionMsgActivity;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.beans.CreateNFileFromAFileResult;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.beans.CrossStatisticsEvent;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.enterprisefilesystem.FileConnectUtils;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.enterprisefilesystem.FileConnectWebUtils;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.enterprisefilesystem.bean
        .FileSave2NetDiskVOResult;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.enterprisefilesystem.bean.FileShare2QiXinVOResult;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.utils.CrossFileUtils;
import com.facishare.fs.filesdownload_center.filedownloadview.MyFileDownloadActivity;
import com.facishare.fs.new_crm.utils.FxCrmUtils;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.cloudctrl.ICloudCtrl;
import com.facishare.fs.pluginapi.file.beans.FileInfo;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fs.web_business_utils.AttachLoad;
import com.fs.beans.beans.EnumDef;
import com.fs.beans.beans.FeedAttachEntity;
import com.fxiaoke.dataimpl.msg.MsgDataController;
import com.fxiaoke.fscommon.files.FileUtil;
import com.fxiaoke.fscommon.util.CommonDataContainer;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.SessionMessageTemp;
import com.fxiaoke.fxlog.DebugEvent;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.stat_engine.StatEngine;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.text.TextUtils;

/**
 * Created by tianyl on 2016/7/13.
 */
public class FilePreviewUtils {

    private static final DebugEvent TAG = new DebugEvent(FilePreviewUtils.class.getSimpleName());

    /**
     * 通过通用控件预览文件，如果是预览图片，建议单独使用previewImgGroup方法进行预览
     *
     * @param context
     * @param dataSource
     * @param config
     */
    public static final void previewFile(Context context, FileDataSource dataSource, FilePreviewConfig config) {
        Intent intent = new Intent();
        intent.putExtra(FilePreviewBaseActivity.DATA_SOURCE, dataSource);
        intent.putExtra(FilePreviewBaseActivity.CONFIG, config);
        if (config.getType() == FileConnectUtils.FileType.txt) {
            intent.setClass(context, TxtPreview.class);
        } else if (config.getType() == FileConnectUtils.FileType.image || config.getType() == FileConnectUtils
                .FileType.svg) {
            intent.setClass(context, ImgPreview.class);
        } else if (config.getType() == FileConnectUtils.FileType.xls) {
            intent.setClass(context, ExcelPreview.class);
        } else {
            intent.setClass(context, NormalOfficePreview.class);
        }
        if (context instanceof Activity) {
            context.startActivity(intent);
        } else {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        }
    }

    public static final void previewImgGroup(Context context, ImageGroupDataSource dataSource, FilePreviewConfig config,
                                             ImgGroupPreviewConfig previewConfig) {

        Intent intent = new Intent();
//        intent.putExtra(FilePreviewBaseActivity.DATA_SOURCE, dataSource);
        CommonDataContainer.getInstance().saveData(FilePreviewBaseActivity.DATA_SOURCE, dataSource);
        intent.putExtra(ImgGroupPreviewAct.PREVIEW_CONFIG, previewConfig);
        intent.putExtra(FilePreviewBaseActivity.CONFIG, config);
        intent.setClass(context, ImgGroupPreviewAct.class);
        context.startActivity(intent);
    }

    /**
     * 分享到企信
     *
     * @param slr
     * @param dataSource
     */
    public static void share2QiXin(final Activity activity, final SessionListRec slr, final FileDataSource dataSource) {
        if (dataSource instanceof NetDiskDataSource) {
            //如果是网盘文件
            FSNetDiskFileUtil.shareFile2QiXin(activity, dataSource.attachId, dataSource.mAttach
                    .attachName, dataSource.mAttach.attachSize, slr);
        } else if (dataSource instanceof NetDiskImgGroupDataSource) {
            NetDiskImgGroupDataSource data = (NetDiskImgGroupDataSource) dataSource;
            FSNetDiskFileUtil.shareFile2QiXin(activity, data.infoItem.id, data.infoItem.name + "." + data.infoItem.ext
                    , data.infoItem.size, slr);
        } else if (dataSource instanceof EnterpriseDataSource) {
            //如果是互联文件
            FileConnectWebUtils.share2QiXin(((EnterpriseDataSource) dataSource).fileId,
                    new WebApiExecutionCallback<FileShare2QiXinVOResult>() {
                        @Override
                        public TypeReference<WebApiResponse<FileShare2QiXinVOResult>> getTypeReference() {
                            return new TypeReference<WebApiResponse<FileShare2QiXinVOResult>>(){};
                        }
                        @Override
                        public Class<FileShare2QiXinVOResult> getTypeReferenceFHE() {
                            return FileShare2QiXinVOResult.class;
                        }
                        @Override
                        public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                            FxCrmUtils.showToast(failureType, httpStatusCode, error);
                        }

                        @Override
                        public void completed(Date date, FileShare2QiXinVOResult response) {
                            //企业互联文件转发成功打点（包括企信和feed）
                            StatEngine.tick(CrossStatisticsEvent.MS_CROSS_OUR_FILE_DETAIL_TRANSMIT);
                            shareToQixin(activity, slr, dataSource, response.getNPath());
                        }

                    });
        } else if (dataSource instanceof EnterpriseImgGroupDataSource) {
            FileConnectWebUtils.share2QiXin(((EnterpriseImgGroupDataSource) dataSource).fileId,
                    new WebApiExecutionCallback<FileShare2QiXinVOResult>() {
                        @Override
                        public TypeReference<WebApiResponse<FileShare2QiXinVOResult>> getTypeReference() {
                            return new TypeReference<WebApiResponse<FileShare2QiXinVOResult>>(){};
                        }
                        @Override
                        public Class<FileShare2QiXinVOResult> getTypeReferenceFHE() {
                            return FileShare2QiXinVOResult.class;
                        }
                        @Override
                        public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                            FxCrmUtils.showToast(failureType, httpStatusCode, error);
                        }

                        @Override
                        public void completed(Date date, FileShare2QiXinVOResult response) {
                            //企业互联文件转发成功打点（包括企信和feed）
                            StatEngine.tick(CrossStatisticsEvent.MS_CROSS_OUR_FILE_DETAIL_TRANSMIT);
                            shareToQixin(activity, slr, dataSource, response.getNPath());
                        }
                    });
        } else if (dataSource instanceof NormalDataSource || dataSource instanceof NormalImgGroupDataSource) {
            // 互联企信转发企信内部群组时，不需要转Path，由服务端负责
            if(dataSource.mAttach.attachType == EnumDef.FeedAttachmentType.EnterpriseNormal.value) {
                shareToQixin(activity, slr, dataSource, dataSource.mAttach.attachPath);
            } else if(dataSource.mAttach.attachType == EnumDef.FeedAttachmentType.EnterpriseNotify.value) {
                CrossFileUtils.transFileAPath2NPath(dataSource.mAttach.attachPath, true,
                        new WebApiExecutionCallback<CreateNFileFromAFileResult>() {
                            @Override
                            public TypeReference<WebApiResponse<CreateNFileFromAFileResult>> getTypeReference() {
                                return new TypeReference<WebApiResponse<CreateNFileFromAFileResult>>(){};
                            }
                            @Override
                            public Class<CreateNFileFromAFileResult> getTypeReferenceFHE() {
                                return CreateNFileFromAFileResult.class;
                            }
                            @Override
                            public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                                ToastUtils.show(I18NHelper.getText("th.subbiz.base.share_failed")/* 分享失败 */);
                            }
                            @Override
                            public void completed(Date date, CreateNFileFromAFileResult response) {
                                shareToQixin(activity, slr, dataSource, response.nPath);
                            }
                        });
            } else {
                shareToQixin(activity, slr, dataSource, dataSource.mAttach.attachPath);
            }
        }
    }

    private static void shareToQixin(Activity activity, SessionListRec slr,
                                    FileDataSource dataSource, String nPath) {
        SessionMessageTemp temp = FSNetDiskDataUtil
                .createSessionMsgTemp(activity, slr.getSessionId(), slr.getTargetUserId(),
                        nPath, dataSource.mAttach.attachSize, dataSource.mAttach.attachName);
        MsgDataController.getInstace(activity).SendMsg(temp);
        SessionMsgActivity.startIntent(activity, slr);
    }

    /**
     * 分享到网盘
     *
     * @param attachPath
     * @param attachPathType
     * @param size
     * @param folderId
     * @param attachName
     */
    public static void share2NetDisk(String attachPath, int attachPathType, final long size, final String folderId,
                                     String attachName) {
        final String[] file = FSNetDiskFileUtil.getFileNameAndExt(attachName);
        if(CrossFileUtils.FileId == attachPathType){
            FileConnectWebUtils.save2NetDisk(attachPath, new WebApiExecutionCallback<FileSave2NetDiskVOResult>() {
                @Override
                public TypeReference<WebApiResponse<FileSave2NetDiskVOResult>> getTypeReference() {
                    return new TypeReference<WebApiResponse<FileSave2NetDiskVOResult>>() {
                    };
                }

                @Override
                public Class<FileSave2NetDiskVOResult> getTypeReferenceFHE() {
                    return FileSave2NetDiskVOResult.class;
                }

                @Override
                public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                    ToastUtils.show(I18NHelper.getText("wq.wq.fs_net_disk_file_util.text.file_saved_error")/* 文件保存失败 */);
                }

                @Override
                public void completed(Date time, FileSave2NetDiskVOResult response) {
                    FSNetDiskFileUtil.save2NetDisk(response.nPath, size, file[0], file[1], folderId, null);
                }
            });
        } else if(CrossFileUtils.APath == attachPathType) {
            CrossFileUtils.transFileAPath2NPath(attachPath, true,
                    new WebApiExecutionCallback<CreateNFileFromAFileResult>() {
                @Override
                public TypeReference<WebApiResponse<CreateNFileFromAFileResult>> getTypeReference() {
                    return new TypeReference<WebApiResponse<CreateNFileFromAFileResult>>() {
                    };
                }

                @Override
                public Class<CreateNFileFromAFileResult> getTypeReferenceFHE() {
                    return CreateNFileFromAFileResult.class;
                }

                @Override
                public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                    FCLog.e(TAG, "createNFileFromAFile failed");
                    ToastUtils.show(I18NHelper.getText("wq.wq.fs_net_disk_file_util.text.file_saved_error")/* 文件保存失败 */);
                }

                @Override
                public void completed(Date time, CreateNFileFromAFileResult response) {
                    if (response == null) {
                        FCLog.e(TAG, "createNFileFromAFile return null");
                        ToastUtils.show(I18NHelper.getText("wq.wq.fs_net_disk_file_util.text.file_saved_error")/* 文件保存失败 */);
                        return;
                    }
                    FSNetDiskFileUtil.save2NetDisk(response.nPath, size, file[0], file[1], folderId, null);
                }
            });
        } else {
            FSNetDiskFileUtil.save2NetDisk(attachPath, size, file[0], file[1], folderId, null);
        }
    }


    public static ImgGroupPreviewConfig getAttachImgGroupPreviewConfig() {
        ImgGroupPreviewConfig.Builder builder = new ImgGroupPreviewConfig.Builder();
        builder.setShowMore(true);
        return builder.build();
    }

    public static FilePreviewConfig getAttachFilePreviewConfig() {
        FilePreviewConfig.Builder builder = new FilePreviewConfig.Builder();
        builder.setHasDownload(true);
        builder.setHasLookDownloaded(true);
        builder.setHasSave2netdisk(true);
        builder.setHasShare2Feed(true);
        builder.setHasShare2QiXin(true);
        return builder.build();
    }

    public static List<FeedAttachEntity> getAttachImagesList(List<FeedAttachEntity> source) {

        ArrayList<FeedAttachEntity> attachEntities = new ArrayList<>();

        if (source != null && source.size() > 0) {

            for (FeedAttachEntity attach : source) {
                int type = FileConnectUtils.getFileType(attach.attachName);

                if (type == FileConnectUtils.FileType.image) {
                    attachEntities.add(attach);
                }

            }
        }

        return attachEntities;
    }

    public static int getAttachImageIndex(List<FeedAttachEntity> source, FeedAttachEntity attach) {
        int index = 0;

        for (int i = 0; i < source.size(); i++) {
            if (attach.attachID > 0) {
                if (source.get(i).attachID == attach.attachID) {
                    index = i;
                    break;
                }
            } else {
                if (TextUtils.equals(source.get(i).attachPath, attach.attachPath)) {
                    index = i;
                    break;
                }
            }
        }

        return index;
    }

    public static ImgGroupPreviewConfig getNDImgGroupPreviewConfig() {
        ImgGroupPreviewConfig.Builder builder = new ImgGroupPreviewConfig.Builder();
        builder.setShowMore(true);
        return builder.build();
    }

    public static FilePreviewConfig getNDFilePreviewConfig(int permission) {
        FilePreviewConfig.Builder builder = new FilePreviewConfig.Builder();
        builder.setHasDownload(true);
        builder.setHasLookDownloaded(true);
        builder.setHasSave2netdisk(true);
        builder.setHasShare2Feed(true);
        builder.setHasShare2QiXin(true);
        builder.setShowReadCount(true);
        builder.setPermission(permission);
        return builder.build();
    }

    public static boolean isSupportShare2WeChat() {
        ICloudCtrl cloudCtrlManager = HostInterfaceManager.getCloudCtrlManager();
        // 获取云控配置项key的值，如下方法每次调用都是获取的最新的值，即取即用，不要缓存。
        return cloudCtrlManager.getBooleanConfig("preview_share_to_wechat", false);
    }

    //是否支持复制到图库，默认支持
    public static boolean isSupportCopyToPublicDic() {
        ICloudCtrl cloudCtrlManager = HostInterfaceManager.getCloudCtrlManager();
        return cloudCtrlManager.getBooleanConfig("is_support_copy_to_public_dic_885", true);
    }

    /**
     * 判断文件是否已经下载到我的下载中
     *
     * @param context
     * @param fileName
     * @param fileSize
     *
     * @return 文件的本地路径，如果不存在则返回空字符串
     */
    public static String isFileAlreadyDown(Context context, String fileName, long fileSize) {
        String ret = "";
        //首先校验是否已下载
        String temp = "";
        int endIndex = 0;
        ArrayList<FileInfo> fileList = FileUtil.getFiles(context, AttachLoad.getAttachPath());
        for (FileInfo file : fileList) {
            if (file.Size == fileSize) {
                if (file.localName.equals(fileName)){
                    ret = file.Path;
                    break;
                }
                //有可能取到的某个文件没有后缀名
                endIndex = file.localName.lastIndexOf(".");
                if (endIndex>0){
                    temp = file.localName.substring(0,endIndex);
                    if (fileName.equals(temp)){
                        ret = file.Path;
                        break;
                    }
                }
                //有可能传入的文件没有后缀名
                endIndex = fileName.lastIndexOf(".");
                if (endIndex>0){
                    temp = fileName.substring(0,endIndex);
                    if (file.localName.equals(temp)){
                        ret = file.Path;
                        break;
                    }
                }
            }
        }
        return ret;
    }

    public static void goToDownFile(Context context) {
        Intent intent = new Intent(context, UserDownFileActivity.class);
        context.startActivity(intent);
    }

    public static void goToNewDownFile(Context context, boolean isDownloadedPage) {
        MyFileDownloadActivity.start(context);
    }

    public static void goToNewDownFile(Context context, String attachId) {
        MyFileDownloadActivity.start(context);

    }

    /**
     *  是否支持图片方式预览
     * @param attach
     * @return
     */
    public static boolean canPreviewImgGroup(FeedAttachEntity attach) {
        if (attach == null || attach.isAliCloud || attach.isLargeFilePath()) {
            return false;
        }
        int type = FileConnectUtils.getFileType(attach.attachName);
        return type == FileConnectUtils.FileType.image;
    }
}
