package com.facishare.fs.dialogs;




import com.facishare.fs.i18n.I18NHelper;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.facishare.fslib.R;
import com.facishare.fs.pluginapi.contact.beans.AEmpSimpleEntity;
import com.facishare.fs.utils_fs.ToastUtils;
import com.lidroid.xutils.util.SystemActionsUtils;

/**
 * 打电话 或 发短信 选择页面
 */
public class CallOrMessDialog extends BaseDialog {
    private Button call, mess;
    private TextView name;
    private AEmpSimpleEntity info;

    public CallOrMessDialog(Context context, int layout, int style, AEmpSimpleEntity info) {
        super(context, layout, style);
        this.info = info;
    }

    @Override 
    public void setViewValue(View view) {
        super.setViewValue(view);
        call = (Button) view.findViewById(R.id.call);
        mess = (Button) view.findViewById(R.id.mess);
        // if(checkPhone(this.info.getMobile())){
        call.setText(I18NHelper.getFormatText("crm.commondetail.BaseBottomBarMoreOpsWMController.v1.1643"/* 打电话 */ , addLine(this.info.getMobile())));
        mess.setText(I18NHelper.getFormatText("account.easy_login.oper.send_sms_with_phone"/* 发短信{0} */, addLine(this.info.getMobile())));
        name = (TextView) view.findViewById(R.id.name);
        call.setOnClickListener(this);
        mess.setOnClickListener(this);
        //}
        name.setText(I18NHelper.getFormatText("crm.utils.FxCrmUtils.v1.4384",this.info.name)/* 联系 */);
    }

    @Override 
    public void onClick(View v) {
        super.onClick(v);
        int i1 = v.getId();
        if (i1 == R.id.call) {
            if (checkPhone(this.info.getMobile())) {
                SystemActionsUtils.delPhone(context, this.info.getMobile());
            } else {
                ToastUtils.showToast(I18NHelper.getText("xt.callormessdialog.text.sorry,_the_phone_number_is_illegal")/* 对不起手机号不合法 */);
            }
            dismiss();

        } else if (i1 == R.id.mess) {
            if (checkPhone(this.info.getMobile())) {
                SystemActionsUtils.sendSMS(context, this.info.getMobile());
            } else {
                ToastUtils.showToast(I18NHelper.getText("xt.callormessdialog.text.sorry,_the_phone_number_is_illegal")/* 对不起手机号不合法 */);
            }
            dismiss();

        }
    }

    public boolean checkPhone(String phone) {
        Pattern pattern = Pattern.compile("^((13[0-9])|(15[^4,\\D])|(18[0-9]))\\d{8}$");
        Matcher matcher = pattern.matcher(phone);
        if (matcher.matches()) {
            return true;
        }
        return false;
    }

    public String addLine(String mobile) {
        StringBuffer b = new StringBuffer(mobile);
        b = b.insert(3, "-");
        b = b.insert(8, "-");

        return b.toString();
    }


}
