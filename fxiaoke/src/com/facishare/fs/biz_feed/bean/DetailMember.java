package com.facishare.fs.biz_feed.bean;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Created by zhangd on 2016/10/27.
 */
public class DetailMember implements Serializable {
    @JSONField(name="a")
    public int employeeId;
    @JSONField(name="b")
    public String name;
    @JSONField(name="c")
    public String department;
    @JSONField(name="d")
    public String post;
    @JSONField(name="e")
    public String profileImage;
    @JSONField(name="f")
    public boolean isStop;

    @JSONCreator
    public DetailMember(@JSONField(name="a") int employeeId,
                        @J<PERSON><PERSON>ield(name="b") String name,
                        @JSONField(name="c") String department,
                        @JSONField(name="d") String post,
                        @JSONField(name="e") String profileImage,
                        @<PERSON>SONField(name="f") boolean isStop) {
        this.employeeId = employeeId;
        this.name = name;
        this.department = department;
        this.post = post;
        this.profileImage = profileImage;
        this.isStop = isStop;
    }
}
