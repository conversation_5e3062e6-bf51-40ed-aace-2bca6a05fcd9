package com.facishare.fs.biz_feed.subbiz_remind.bean;

import com.facishare.fs.pluginapi.contact.beans.EmpShortEntity;
import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

import java.util.Date;

public class ATimingMessageRemaind {
    /**
     * TimingMessageRemaindId
     */
    @JSONField(name="a")
    public final int timingMessageRemaindId;
    /**
     * TimingMessageRemaindSubType : 1：自定义；2：分享；3：日志；4: 指令；5：审批；6：销售记录；7：群通知；8：第三方feed；9：日程；10：任务
     */
    @JSONField(name="b")
    public final int timingMessageRemaindSubType;
    /**
     * 摘要
     */
    @JSONField(name="c")
    public final String message;
    /**
     * 提醒创建人
     */
    @JSONField(name="d")
    public final EmpShortEntity timingMessageRemaindCreator;
    /**
     * 提醒时间
     */
    @JSONField(name="e")
    public final Date remaindTime;
    /**
     * FeedId
     */
    @JSONField(name="f")
    public final int feedId;
    /**
     * FeedType : 1：分享；2：计划；3：指令；4：审批；5：销售记录；99：第三方feed；
     */
    @JSONField(name="g")
    public final int feedType;
    /**
     * Feed创建人
     */
    @JSONField(name="h")
    public final EmpShortEntity feedCreator;
    /**
     * 是否已读
     */
    @JSONField(name="i")
    public final boolean readed;
    /**
     * DataId
     */
    @JSONField(name="j")
    public final String dataStringId;

    @JSONCreator
    public ATimingMessageRemaind(@JSONField(name="a") int timingMessageRemaindId,
                                 @JSONField(name="b") int timingMessageRemaindSubType,
                                 @JSONField(name="c") String message,
                                 @JSONField(name="d") EmpShortEntity timingMessageRemaindCreator,
                                 @JSONField(name="e") Date remaindTime,
                                 @JSONField(name="f") int feedId,
                                 @JSONField(name="g") int feedType,
                                 @JSONField(name="h") EmpShortEntity feedCreator,
                                 @JSONField(name="i") boolean readed,
                                 @JSONField(name="j") String dataStringId) {
        this.timingMessageRemaindId = timingMessageRemaindId;
        this.timingMessageRemaindSubType = timingMessageRemaindSubType;
        this.message = message;
        this.timingMessageRemaindCreator = timingMessageRemaindCreator;
        this.remaindTime = remaindTime;
        this.feedId = feedId;
        this.feedType = feedType;
        this.feedCreator = feedCreator;
        this.readed = readed;
        this.dataStringId = dataStringId;
    }
}
