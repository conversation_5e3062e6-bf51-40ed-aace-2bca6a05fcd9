package com.facishare.fs.biz_feed.subbiz_send.feedsendapi;

import com.facishare.fs.i18n.I18NHelper;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import com.facishare.fs.biz_feed.subbiz_send.datactrl.SendBaseDialogUtils;
import com.facishare.fs.biz_feed.utils.FeedStatisticsUtils;
import com.facishare.fs.common_datactrl.draft.BaseVO;
import com.facishare.fs.common_datactrl.draft.draft_fw.DraftState;
import com.facishare.fs.common_datactrl.draft.draft_fw.IDraft;
import com.facishare.fs.memory.FSObservableManager;
import com.facishare.fs.pluginapi.HostInterface;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.common_beans.Attach;
import com.facishare.fs.ui.FeedsUitls;
import com.facishare.fs.utils_fs.FsLogUtils;
import com.fxiaoke.fscommon.sandbox.SandboxContextManager;
import com.fxiaoke.fshttp.web.ParamValue3;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.stat_engine.events.session.UeEventSession;
import com.lidroid.xutils.http.HttpStatusEx;

import android.os.Handler;
import android.text.TextUtils;

/**
 * Created by wangyp on 2016/11/14.
 */
public class FeedSendTask implements Runnable ,IFeedSendTask{
    private UeEventSession mUeEventSession;
    public LinkedList<Attach> attachs;
    public ISendCallback mISendCallback;
    public IDraft mDraft;
    public WebApiExecutionCallback callback;
    protected FeedUpFileService mUpFileService;
//    Handler mhandler = new Handler();
    public FeedSendTask(IDraft mDraft, LinkedList<Attach> attachs) {
        FCLog.i(FsLogUtils.debug_feed_send,"FeedSendTask  attachs=");
        this.attachs = attachs;
        this.mDraft = mDraft;
        this.mUpFileService = mDraft.getFeedUpFileService();
    }

    public LinkedList getAttachs()
    {
        return attachs;
    }

    public FeedUpFileService getUpFileService() {
        return mUpFileService;
    }

    public void setUpFileService(FeedUpFileService mUpFileService) {
        this.mUpFileService = mUpFileService;
    }

    public int getID() {
        return this.mDraft.getId();
    }

    public interface ISendCallback {
        public void sendDraft(IFeedSendTask task,
                              List<ParamValue3<Integer, String, Integer, String>> response);
    }

    @Override
    public void run() {
        FCLog.i(FsLogUtils.debug_feed_send,"FeedSendTask run ");
        mUeEventSession = FeedStatisticsUtils.sendFeedStart(mDraft);
        if (mDraft != null) {
            if (mDraft.isInsertable()) {
                HostInterfaceManager.getHostInterface().showSendNotify(HostInterface.SendNotifyType.sending, I18NHelper.getText("pay.common.common.sending")/* 发送中... */);
            }

            if (mUpFileService == null) {
                mUpFileService = new FeedUpFileService(mDraft.getType());
                FCLog.i(FsLogUtils.debug_feed_send,"FeedSendTask new mUpFileService");
            }

            if (mUpFileService != null) {

                mUpFileService.setFeedSendCallback(new FeedUpFileService.FeedUploadCallback<Object>() {
                    @Override
                    public void saveSelf() {
                        mDraft.saveSelf();
                    }

                    @Override
                    public void completed(Date time, Object response) {
                        FCLog.i(FsLogUtils.debug_feed_send,"FeedSendTask setFeedSendCallback completed "+ FsLogUtils.checkNull(response));
                        ((BaseVO) mDraft).fileInfos = (List<ParamValue3<Integer, String, Integer, String>>)response;
                        mISendCallback.sendDraft(FeedSendTask.this, (List<ParamValue3<Integer, String, Integer,
                                String>>)response);
                    }

                    @Override
                    public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                        FCLog.i(FsLogUtils.debug_feed_send,"FeedSendTask setFeedSendCallback failed error=" +error +" failuretype="+failureType.getIndex());
                        mDraft.taskSendFailed(failureType,httpStatusCode,error);
                        sendFailed(failureType, httpStatusCode, error);
                        if (callback != null) {
                            callback.failed(failureType, httpStatusCode, error);
                        }
                    }
                });

                mUpFileService.addAttachs(attachs);

            }else {
                FCLog.i(FsLogUtils.debug_feed_send,"FeedSendTask mUpFileService = null");
//                mISendCallback.sendDraft(FeedSendTask.this, null);
            }

        }
    }

    public void sendSuccess(final Date serviceDate, final Object object) {
        FeedStatisticsUtils.sendFeedSuccess(mUeEventSession);
        HostInterfaceManager.getHostInterface().showSendNotify(HostInterface.SendNotifyType.success, I18NHelper.getText("xt.qq_share_helper.des.send_success")/* 发送成功 */);

        FSObservableManager.getInstance().onChangeSendProgressSuccess();
        FeedSenderTaskManger.getInstance().exeNextTask(mDraft);
        mDraft.setState(DraftState.STATE_SENDSUCCES);
        mDraft.setServiceDate(serviceDate);
        FCLog.i(FsLogUtils.debug_feed_send,"sendFeedSuccess deleteSelf");
        mDraft.deleteSelf();
        FSObservableManager.getInstance().onChangeSendEvent(mDraft, object);

    }

    /**
     * 客群--事件--回复 发送成功时页面调用
     *
     * @param serviceDate
     * @param object
     */
    public void sendEventReplySuccess(final Date serviceDate, final Object object) {
        FeedStatisticsUtils.sendFeedSuccess(mUeEventSession);
//        mhandler.post(new Runnable() {
//            @Override
//            public void run() {
                FeedSenderTaskManger.getInstance().exeNextTask(mDraft);
                mDraft.setState(DraftState.STATE_SENDSUCCES);
                mDraft.setServiceDate(serviceDate);
                HostInterfaceManager.getHostInterface().showSendNotify(HostInterface.SendNotifyType.success, I18NHelper.getText("xt.qq_share_helper.des.send_success")/* 发送成功 */);
                FSObservableManager.getInstance().onChangeSendEvent(mDraft, object);
                FSObservableManager.getInstance().onChangeSendProgressSuccess();
//            }
//        });

    }

    /**
     * * 客群--事件--回复 发送失败时调用
     */
    public void sendEventReplyFailed(final WebApiFailureType failureType, final int httpStatusCode,
                                     final String error) {
        FeedStatisticsUtils.sendFeedError(mUeEventSession, httpStatusCode,
                "sendEventReplyFailed, " + failureType.getDetailFailDesc(), failureType);
//        mhandler.post(new Runnable() {
//            @Override
//            public void run() {
                FeedSenderTaskManger.getInstance().exeNextTask(mDraft);
                mDraft.setState(DraftState.STATE_SENDFAILED);
                HostInterfaceManager.getHostInterface().showSendNotify(HostInterface.SendNotifyType.failed, I18NHelper.getText("crm.fragment.LeadsToCustomerFrag.1235")/* 发送失败 */);
                FSObservableManager.getInstance().onChangeSendEvent(mDraft);
                FSObservableManager.getInstance().onChangeSendProgressFailed();
//            }
//        });

    }

    public void sendSuccess(Date serviceDate) {
        sendSuccess(serviceDate, null);
    }

    public void sendFailed(final WebApiFailureType failureType, final int httpStatusCode, final String error) {
        FeedStatisticsUtils.sendFeedError(mUeEventSession, httpStatusCode,
                "sendFailed," + failureType.getDetailFailDesc(), failureType);

//        mhandler.post(new Runnable() {
//            @Override
//            public void run() {
           FeedSenderTaskManger.getInstance().exeNextTask(mDraft);
                if (mDraft.isInsertable()) {
                    if (failureType.getIndex() >=600000000 && failureType.getIndex() <=699999999) {
                        mDraft.setErrorStr(failureType.description());
                    }else if (failureType == WebApiFailureType.BusinessFailed) {
                        //                mDraft.setErrorStr(failureType.description() + "：" + error);
                        mDraft.setErrorStr(error);
                    } else {
                        if (httpStatusCode == HttpStatusEx.EX_TIMEOUT) {
                            mDraft.setErrorStr(I18NHelper.getText("xt.feed_send_task.text.send_error_net_weak")/* 发送失败：您当前的网络不佳或不可用，请稍后重试 */);
                        } else if (httpStatusCode == HttpStatusEx.EX_NONE_NETWORK
                                ||httpStatusCode==HttpStatusEx.EX_WEAK_NETWORK) {
                            mDraft.setErrorStr(I18NHelper.getText("xt.feed_send_task.text.send_error_net_outtime")/* 发送失败：网络超时，请稍后重试 */);
                        } else {
                            if (!TextUtils.isEmpty(error)) {
                                mDraft.setErrorStr(I18NHelper.getFormatText("xt.feed_send_task.text.send_error.1",error)/* 发送失败：{0} */);
                            } else if (failureType != null) {
                                mDraft.setErrorStr(I18NHelper.getFormatText("xt.feed_send_task.text.send_error",failureType.description())/* 发送失败：{0} */  );
                            }
                        }
                    }

                    if (failureType.getIndex() >=600000000 && failureType.getIndex() <=699999999) {
                        int code = failureType.getIndex();
                        int state = FeedsUitls.fetchCode(code,8);
                        if (state == 1){
                            mDraft.setState(DraftState.STATE_DELETE);
                        }else if(state == 2){
                            mDraft.setState(DraftState.SERVIDE_STATE_WRITE);
                        }else if(state == 0){
                            mDraft.setState(DraftState.STATE_SENDFAILED);
                        }
                        SendBaseDialogUtils.showDialog(mDraft,failureType);
                    }else {
                        mDraft.setState(DraftState.STATE_SENDFAILED);
                    }

                    mDraft.saveSelf();
                } else {
                    mDraft.setState(DraftState.STATE_SENDFAILED);
                }
                //        notification(R.drawable.send_failure, I18NHelper.getText("crm.fragment.LeadsToCustomerFrag.1235")/* 发送失败 */);
                HostInterfaceManager.getHostInterface().showSendNotify(HostInterface.SendNotifyType.failed, I18NHelper.getText("crm.fragment.LeadsToCustomerFrag.1235")/* 发送失败 */);
                // MainTabActivity.getInstance().checkDraftRemind();
                FSObservableManager.getInstance().onChangeSendEvent(mDraft);
                FSObservableManager.getInstance().onChangeSendProgressFailed();
//            }
//        });

    }




}