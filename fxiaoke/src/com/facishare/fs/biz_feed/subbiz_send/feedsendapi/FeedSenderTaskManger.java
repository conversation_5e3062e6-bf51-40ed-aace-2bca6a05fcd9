package com.facishare.fs.biz_feed.subbiz_send.feedsendapi;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.facishare.fs.biz_feed.utils.FeedStatisticsUtils;
import com.facishare.fs.common_datactrl.draft.BaseVO;
import com.facishare.fs.common_datactrl.draft.SenderManager;
import com.facishare.fs.common_datactrl.draft.draft_fw.Draft;
import com.facishare.fs.common_datactrl.draft.draft_fw.DraftManager;
import com.facishare.fs.common_datactrl.draft.draft_fw.DraftState;
import com.facishare.fs.common_datactrl.draft.draft_fw.IDraft;
import com.facishare.fs.ui.FeedsUitls;
import com.facishare.fs.utils_fs.FsLogUtils;
import com.fxiaoke.fshttp.web.ParamValue3;
import com.fxiaoke.fxlog.FCLog;

import android.os.Handler;
import android.os.HandlerThread;

/**
 * Created by wangyp on 2016/11/14.
 */
public class FeedSenderTaskManger {
    public final static String controller_sendFeed = "Feed";
    private static FeedSenderTaskManger mDraftManager = null;

    HandlerThread handlerThread = null;
    Handler mHandler=null;
    private FeedSenderTaskManger() {

        handlerThread = new HandlerThread("feed_send");
        handlerThread.start();
        mHandler = new Handler(handlerThread.getLooper());
    }

    public static void newInstance() {
        mDraftManager = new FeedSenderTaskManger();
    }

    public static FeedSenderTaskManger getInstance() {
        if (mDraftManager == null) {
            mDraftManager = new FeedSenderTaskManger();
        }
        return mDraftManager;
    }

    public void addTask(FeedSendTask task) {
        synchronized (task) {
            mHandler.post(task);
        }
    }

    public void close() {
        FCLog.i(FsLogUtils.debug_feed_send, "FeedSenderTaskManger close  ");
        clearTasks();
    }

    FeedSendTask t;
    void runTask(final IDraft draft)
    {
        FCLog.i(FsLogUtils.debug_feed_send, "FeedSenderTaskManger runTask start  id="+draft.getId());
        if (draft != null ) {
            t = new FeedSendTask(draft, ((BaseVO) draft).getUpLoadFiles());
            t.setUpFileService(draft.getFeedUpFileService());
            t.mISendCallback = new FeedSendTask.ISendCallback() {
                @Override
                public void sendDraft(final IFeedSendTask task, List<ParamValue3<Integer, String, Integer, String>> response) {
                    FCLog.i(FsLogUtils.debug_feed_send, "FeedSenderTaskManger runTask end  id="+draft.getId());
                    draft.sendDraft(task, response);
                }
            };
            mHandler.post(t);
        }
    }

    List<IDraft> iDraftList = new ArrayList<>();
    public synchronized void addTask(final IDraft draft) {

        if (FeedsUitls.isUpImageService(((BaseVO)draft).getTag())) {

        }else{
            SenderManager.getInstance().addTask(draft);
            return;
        }
        if (draft != null ) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    FCLog.i(FsLogUtils.debug_feed_send, "FeedSenderTaskManger addTask 开始插入数据库");
                    if (draft.isInsertable()) {
                        // 数据插入操作
                        draft.setState(DraftState.STATE_SENDING);
                        draft.saveSelf();
                        FCLog.i(FsLogUtils.debug_feed_send, "FeedSenderTaskManger addTask 数据插入成功 id="+draft.getId());
                    }
                    iDraftList.add(draft);
                    if (iDraftList.size() == 1) {
                        runTask(draft);
                    }
                }
            });

        }

    }

    public synchronized void exeNextTask(IDraft iDraft)
    {
        if (iDraftList != null && iDraftList.size() > 0) {
            Iterator<IDraft> it = iDraftList.iterator();
            while(it.hasNext()){
                IDraft x = it.next();
                int  id = x.getId();
                if(id == iDraft.getId()){
                    FCLog.i(FsLogUtils.debug_feed_send,"FeedSenderTaskManger exeNextTask  ");
                    it.remove();
                }
            }
        }

        if (iDraftList != null && iDraftList.size() > 0){
            runTask(iDraftList.get(0));
        }

    }

    public synchronized void clearTasks()
    {
        if (iDraftList != null && iDraftList.size() > 0)
        {
            iDraftList.clear();
        }

        if (t != null ) {
            FeedUpFileService fs = t.getUpFileService();
            if (fs!= null) {

                fs.closeProgress();

            }
        }
    }


    /**
     * 不入草稿箱
     *
     * @param draft
     */
    public synchronized void addTaskIgnorDraft(final IDraft draft) {
        FeedSendTask t = new FeedSendTask(draft, ((BaseVO) draft).getUpLoadFiles());
        t.mISendCallback = new FeedSendTask.ISendCallback() {
            @Override
            public void sendDraft(final IFeedSendTask task, List<ParamValue3<Integer, String, Integer, String>> response) {
                draft.sendDraft(task, response);
            }
        };
        addTask(t);
    }
}
