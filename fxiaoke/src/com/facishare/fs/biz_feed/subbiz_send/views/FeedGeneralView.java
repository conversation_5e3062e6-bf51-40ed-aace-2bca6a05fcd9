package com.facishare.fs.biz_feed.subbiz_send.views;

import com.facishare.fs.i18n.I18NHelper;
import android.app.Activity;
import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.facishare.fslib.R;
import com.facishare.fs.biz_feed.subbiz_send.ApproveCommonUtils;
import com.facishare.fs.biz_feed.subbiz_send.adapter.FeedApprovePicAdapter;
import com.facishare.fs.biz_feed.subbiz_send.bean.ApproveGeneralBill;
import com.facishare.fs.biz_feed.subbiz_send.bean.ApproveGeneralPic;
import com.facishare.fs.biz_feed.subbiz_send.bean.FeedApproveConfig;
import com.facishare.fs.biz_feed.subbiz_send.bean.FeedApprovePicEntity;
import com.facishare.fs.biz_feed.subbiz_send.datactrl.ApproveCtrl;
import com.facishare.fs.biz_feed.utils.EditTextLimitUtils;
import com.facishare.fs.biz_feed.utils.ImageUploadUtils;
import com.facishare.fs.biz_feed.utils.InputUtils;
import com.facishare.fs.dialogs.LoadingProDialog;
import com.facishare.fs.pluginapi.common_beans.Attach;
import com.facishare.fs.pluginapi.pic.bean.ImageBean;
import com.facishare.fs.pluginapi.pic.bean.ImageObjectVO;
import com.facishare.fs.pluginapi.pic.bean.ImgData;
import com.fs.beans.beans.EnumDef;
import com.fxiaoke.fshttp.web.ParamValue3;
import com.fxiaoke.fshttp.web.http.WebApiUtils;

import java.io.File;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.ListIterator;

/**
 * Created by hanlz on 2016/5/31.
 */
public class FeedGeneralView extends FeedCardBaseView
        implements FeedApprovePicAdapter.IClickListener, ApproveCtrl.IUploadListener {

    public static final String ATTACH_PIC_ADD = "attach_pic_add";

    private Activity mActivity;

    private LinearLayout mLlytLink;

    private TextView mTvGeneralHead;
    private TextView mTvDelete;
    private EditText mEtName;
    private EditText mEtMoney;
    private EditText mEtMark;
    private TextView mTvLink;
    private ImageView mIvLinkArrow;
    //图片布局
    private LinearLayout mPicContain;
    private TextView mTvTotel;
    private GridView mGvPic;

    private FeedApprovePicAdapter mPicAdapter;

    private List<ImgData> mPicData = new ArrayList<>();

    //上传用的队列
    private LinkedList<Attach> mUploadImage;

    /**
     * 区分是展示还是发送
     */
    private int mType;

    private boolean mIsShowAddView;

    private ApproveCtrl mApproveCtrl;

    private LoadingProDialog progressDialog = null;

    private List<ApproveGeneralPic> mUploadList = new ArrayList<>();

    public FeedGeneralView(Context ctx, int ind, int type) {
        super(ctx, ind);
        if (ctx instanceof Activity) {
            mActivity = (Activity) ctx;
        }
        this.mType = type;
        mLayoutitemView = lif.inflate(R.layout.feed_send_general_bill_fragment, null);
        initView();
        initEvent();
        initData();
    }

    private void initView() {
        mTvGeneralHead = (TextView) mLayoutitemView.findViewById(R.id.tv_general_matter);
        mTvDelete = (TextView) mLayoutitemView.findViewById(R.id.tv_delete_approve);
        mLlytLink = (LinearLayout) mLayoutitemView.findViewById(R.id.llyt_link);
        mEtName = (EditText) mLayoutitemView.findViewById(R.id.et_name);
        mEtMoney = (EditText) mLayoutitemView.findViewById(R.id.et_money);
        mEtMark = (EditText) mLayoutitemView.findViewById(R.id.et_mark);
        mTvLink = (TextView) mLayoutitemView.findViewById(R.id.tv_link);
        mPicContain = (LinearLayout) mLayoutitemView.findViewById(R.id.general_pic_contain);
        mTvTotel = (TextView) mLayoutitemView.findViewById(R.id.tv_pic_total);
        mGvPic = (GridView) mLayoutitemView.findViewById(R.id.attach_gv_container);

        mIvLinkArrow = (ImageView) mLayoutitemView.findViewById(R.id.iv_arrow_link_approve);

        mApproveCtrl = new ApproveCtrl();
    }

    public void limitInput(boolean isLimit) {
        if (isLimit) {
            EditTextLimitUtils.filterStringEditText(mEtName, I18NHelper.getText("xt.feed_send_general_bill_fragment.text.reimbursement")/* 报销事项 */, ApproveCommonUtils.APPROVE_MATTER);
            EditTextLimitUtils.filterStringEditText(mEtMark, I18NHelper.getText("crm.layout.feed_send_general_bill_fragment.7849")/* 备注 */, ApproveCommonUtils.APPROVE_REMARK);
            EditTextLimitUtils.limitNumEditText(mEtMoney, I18NHelper.getText("pay.enterprise.common.amount_of_money")/* 金额 */, I18NHelper.getText("pay.enterprise.common.amount_of_money")/* 金额 */, ApproveCommonUtils.APPROVE_MONEY_INTEGER,
                    ApproveCommonUtils.APPROVE_MONEY_DECIAL);
        }
    }

    private void initData() {

        mTvGeneralHead.setText(I18NHelper.getFormatText("xt.feed_send_approve_item_head.text.matter_s",NUMBER_MAP.get(index))/* 事项{0} */ );

        switch (mType) {
            case FeedApproveConfig.APPROVE_CREATE:
                initCreateData();
                break;
            case FeedApproveConfig.APPROVE_SHOW:
                initShowData();
                break;
        }
    }

    private void initShowData() {
        mIsShowAddView = false;
        mPicAdapter = new FeedApprovePicAdapter(context, mPicData, mIsShowAddView, false);
        mPicAdapter.setAddViewListener(this);
        mGvPic.setAdapter(mPicAdapter);
    }

    private void initCreateData() {
        mIsShowAddView = true;
        mPicAdapter = new FeedApprovePicAdapter(context, mPicData, mIsShowAddView, true);
        mPicAdapter.setAddViewListener(this);
        mGvPic.setAdapter(mPicAdapter);
    }

    private void initEvent() {
        mTvDelete.setOnClickListener(deleteListener);
        mLlytLink.setOnClickListener(linkListener);
        mEtMoney.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (mTextWatcher != null) {
                    mTextWatcher.afterTextChanged(mEtMoney.getId(), index, editable);
                }
            }
        });
    }

    @Override
    public <T> void refreshData(T data) {
        super.refreshData(data);
        if (data instanceof ApproveGeneralBill) {
            refreshLink((ApproveGeneralBill) data);
        }
    }

    @Override
    public <T> void initData(T data) {
        super.initData(data);
        if (data instanceof ApproveGeneralBill) {
            dealGeneralData((ApproveGeneralBill) data);
        }
    }

    private void dealGeneralData(ApproveGeneralBill generalBill) {
        if (generalBill != null) {
            if (!TextUtils.isEmpty(generalBill.title)) {
                mEtName.setText(generalBill.title);
            } else {
                mEtName.setText("");
            }
            if (generalBill.amount >= 0) {
                DecimalFormat format = new DecimalFormat("");
                double money = generalBill.amount;
                switch (mType) {
                    case FeedApproveConfig.APPROVE_CREATE:
                        format = new DecimalFormat("0.##");
                        break;
                    case FeedApproveConfig.APPROVE_SHOW:
                        format = new DecimalFormat("0.00");
                        break;
                }
                mEtMoney.setText(format.format(money));
            } else {
                mEtMoney.setText("");
            }
            if (!TextUtils.isEmpty(generalBill.remark)) {
                mEtMark.setText(generalBill.remark);
            } else {
                mEtMark.setText("");
                mEtMark.setHint(InputUtils.getInputNotReqHint(mType));
            }
            initCustomer(generalBill);

            initPic(generalBill);
        }
    }

    private void initCustomer(ApproveGeneralBill generalBill) {
        if (!TextUtils.isEmpty(generalBill.customerName)) {
            mTvLink.setText(generalBill.customerName);
        } else {
            String link = "";
            switch (mType) {
                case FeedApproveConfig.APPROVE_CREATE:
                    link = "";
                    break;
                case FeedApproveConfig.APPROVE_SHOW:
                    link = I18NHelper.getText("wq.fs_net_disk_permission_fragment.text.no")/* 无 */;
                    break;
            }
            mTvLink.setText(link);
            mTvLink.setHint(InputUtils.getSelectNotReqHint(mType));
        }
    }

    private void initPic(ApproveGeneralBill generalBill) {
        switch (mType) {
            case FeedApproveConfig.APPROVE_CREATE:
                initCreatePic(generalBill);
                break;
            case FeedApproveConfig.APPROVE_SHOW:
                initShowPic(generalBill);
                break;
        }
    }

    private void initCreatePic(ApproveGeneralBill generalBill) {
        mUploadList = generalBill.pic;
        if (generalBill.imageData != null) {
            //            refreshPicData(generalBill.imageData);
            for (ImageBean imageBean : generalBill.imageData) {
                mPicData.add(imgVoToImgDataFile(imageBean, imageBean.isSendByUnzipped()));
            }
        }

        if (generalBill.feedPic != null) {
            for (FeedApprovePicEntity attachEntity : generalBill.feedPic) {
                mPicData.add(url2ImgData(attachEntity.attachPath));
            }
            generalBill.feedPic = null;
        }
        refreshPicView();
//        if (mClickListener != null) {
//            mClickListener.itemClick(R.id.id_approve_upload_image, index);
//        }
    }

    private ImgData imgVoToImgDataFile(ImageBean imageBean, boolean isSendByUnzipped) {
        ImageObjectVO imgVo = imageBean.getImageObject();
        ImgData imgItem = new ImgData();
        imgItem.mDefaultThumbnailImgName = imgVo.thumbnail_data;
        imgItem.middleImgName = imgVo.data;
        imgItem.mHDImgName = imgVo.data;
        imgItem.mObject = imgVo;
        imgItem.mIsSendByUnzipped = isSendByUnzipped;
        imgItem.mImgType = imgVo.position;

        // anjx 修改 1009
        if (TextUtils.isEmpty(imgVo.display_name)) {
            String fullName = imgVo.data;
            if (fullName != null) {
                String fileName = fullName.substring(fullName.lastIndexOf("/") + 1, fullName.length()).toLowerCase();
                imgVo.display_name = fileName;
            }
        } else if (imgVo.display_name.contains("/weibo")) {//杨帅添加
            String fullName = imgVo.display_name;
            if (fullName != null) {
                String fileName = fullName.substring(fullName.lastIndexOf("/") + 1, fullName.length()).toLowerCase();
                imgVo.display_name = fileName;
            }
        }
        return imgItem;
    }

    private void initShowPic(ApproveGeneralBill generalBill) {
        if (generalBill.feedPic != null) {
            for (FeedApprovePicEntity attachEntity : generalBill.feedPic) {
                mPicData.add(url2ImgData(attachEntity.attachPath));
            }
        }
        refreshPicView();
    }

    private ImgData url2ImgData(String picUrl) {
        ImgData imgData = new ImgData();
        ImageObjectVO imageObjectVO = new ImageObjectVO();
        imgData.mObject = imageObjectVO;
        imgData.sourcePath = picUrl;
        imgData.mDefaultThumbnailImgName = WebApiUtils.getDownloadUrlForImg(picUrl, WebApiUtils.ImageType.SMALL);
        imgData.mHDImgName = WebApiUtils.getDownloadUrlForImg(picUrl, WebApiUtils.ImageType.LARGE);
        //        if (isCurrentFsNotice) {
        //            imgData.middleImgName = WebApiUtils.getDownloadGlobalUrlForImg(picUrl, WebApiUtils.ImageType
        // .MIDDLE);
        //        } else {
        imgData.middleImgName = WebApiUtils.getDownloadUrlForImg(picUrl, WebApiUtils.ImageType.MIDDLE);
        //        }
        imgData.mImgType = ImgData.HTTP_IMG_TYPE;
        imageObjectVO.thumbnail_data = imgData.mDefaultThumbnailImgName;
        imageObjectVO.data = imgData.middleImgName;
        imageObjectVO.data = imgData.mHDImgName;
        return imgData;
    }

    public void refreshLink(ApproveGeneralBill generalBill) {
        initCustomer(generalBill);
    }

    public void addPic(List<ImgData> images) {
        refreshPicData(images);
    }

    void delUPloadImage(ImgData imgData)
    {
        ListIterator<ApproveGeneralPic> uploadIterator = mUploadList.listIterator();
        for (; uploadIterator.hasNext(); ) {
            ApproveGeneralPic upload = uploadIterator.next();
            if (upload.value3 != null && upload.value3.equals(imgData.middleImgName)) {
                uploadIterator.remove();
            } else {
                if (upload.value1 != null && upload.value1.equals(imgData.sourcePath)) {
                    uploadIterator.remove();
                }
            }
        }
    }

    private void refreshPicData(List<ImgData> images) {
        if (mPicData == null) {
            mPicData = new ArrayList<>();
        }

        if (mUploadImage == null) {
            mUploadImage = new LinkedList<>();
        } else {
            mUploadImage.clear();
        }
        if (mUploadList == null) {
            mUploadList = new LinkedList<>();
        }

        if (images != null && !images.isEmpty()) {

            ListIterator<ImgData> picIterator = mPicData.listIterator();
            for (; picIterator.hasNext(); ) {
                ImgData imgData = picIterator.next();
                boolean flag = isContains(images, imgData, true);
                if (!flag) {
                    picIterator.remove();
                    delUPloadImage(imgData);
                }
            }

            ListIterator<ImgData> imgDataIterator = images.listIterator();
            for (; imgDataIterator.hasNext(); ) {
                ImgData imgData = imgDataIterator.next();
                boolean flag = isContains(mPicData, imgData, true);
                if (!flag) {
                    mUploadImage.add(ImageUploadUtils.imgData2Attach(imgData));
                    mPicData.add(imgData);
                }
            }

        } else {
            mPicData.clear();
            mUploadList.clear();
            mUploadImage.clear();
        }

        if (mUploadImage != null && !mUploadImage.isEmpty()) {
            mApproveCtrl.setUploadListener(this);
            mApproveCtrl.setUploadImage(mUploadImage);
        } else {
            refreshPicView();
            if (mClickListener != null) {
                mClickListener.itemClick(R.id.id_approve_upload_image, index);
            }
        }
    }

    private boolean isContains(List<ImgData> imgDatas, ImgData data, boolean isRemainHttpPic) {
        ListIterator<ImgData> picIterator = imgDatas.listIterator();
        boolean flag = false;
        for (; picIterator.hasNext(); ) {
            ImgData picData = picIterator.next();
            if (isRemainHttpPic && data.mImgType == ImgData.HTTP_IMG_TYPE) {
                return true;
            }
            if (TextUtils.equals(data.mDefaultThumbnailImgName, picData.mDefaultThumbnailImgName)
                    && TextUtils.equals(data.mHDImgName, picData.mHDImgName)
                    && TextUtils.equals(data.middleImgName, picData.middleImgName)) {
                flag = true;
            }
        }
        return flag;
    }

    public void removePic(List<ImgData> images) {
        if (mPicData == null) {
            mPicData = new ArrayList<>();
        }

        if (mUploadImage == null) {
            mUploadImage = new LinkedList<>();
        } else {
            mUploadImage.clear();
        }
        if (mUploadList == null) {
            mUploadList = new LinkedList<>();
        }

        if (images != null && !images.isEmpty()) {

            ListIterator<ImgData> picIterator = mPicData.listIterator();
            for (; picIterator.hasNext(); ) {
                ImgData imgData = picIterator.next();
                boolean flag = isContains(images, imgData, false);
                if (!flag) {
                    picIterator.remove();
                    delUPloadImage(imgData);
                }
            }

        } else {
            mPicData.clear();
            mUploadList.clear();
            mUploadImage.clear();
        }

        if (mUploadImage != null && !mUploadImage.isEmpty()) {
            mApproveCtrl.setUploadListener(this);
            mApproveCtrl.setUploadImage(mUploadImage);
        } else {
            refreshPicView();
            if (mClickListener != null) {
                mClickListener.itemClick(R.id.id_approve_upload_image, index);
            }
        }
    }

    private void ProDialog(String str) {
        if (progressDialog == null) {
            progressDialog = LoadingProDialog.creatLoadingPro(context);

        }
        progressDialog.setCanceledOnTouchOutside(false);
        progressDialog.setCancelable(false);
        //        progressDialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
        //
        //            @Override
        //            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
        //                // TODO Auto-generated method stub
        //                endProDialog();
        //                mApproveCtrl.stopUpload();
        //                mUploadList = null;
        //                return false;
        //            }
        //        });

        progressDialog.setMessage(str);
        progressDialog.show();

    }

    private void endProDialog() {
        if (progressDialog != null) {
            progressDialog.dismiss();
        }

    }

    private void handlePic(List<ParamValue3<Integer, String, Integer, String>> pic) {

    }

    @Override
    public void setCanDelete(boolean isCanDelete) {
        super.setCanDelete(isCanDelete);
        switch (mType) {
            case FeedApproveConfig.APPROVE_CREATE:
                if (isCanDelete) {
                    mTvDelete.setVisibility(View.VISIBLE);
                } else {
                    mTvDelete.setVisibility(View.GONE);
                }
                break;
            case FeedApproveConfig.APPROVE_SHOW:
                mTvDelete.setVisibility(View.GONE);
                break;
        }

    }

    @Override
    public void setIndex(int index) {
        super.setIndex(index);
        mTvGeneralHead.setText(I18NHelper.getFormatText("xt.feed_send_approve_item_head.text.matter_s",NUMBER_MAP.get(index))/* 事项{0} */ );
    }

    public List<ImgData> getmPicData() {
        return mPicData;
    }

    public String getApproveName() {
        return mEtName == null ? "" : mEtName.getText().toString();
    }

    public String getApproveMoney() {
        String money = mEtMoney == null ? "" : mEtMoney.getText().toString();
        //        double approveMoney = -1;
        //        try {
        //            approveMoney = Double.parseDouble(money);
        //        } catch (NumberFormatException e) {
        //            e.printStackTrace();
        //            approveMoney = -1;
        //        }
        return money;
    }

    public String getApproveMark() {
        return mEtMark == null ? "" : mEtMark.getText().toString();
    }

    private View.OnClickListener linkListener = new View.OnClickListener() {
        @Override
        public void onClick(View view) {
            if (view == null) {
                return;
            }
            if (mClickListener != null) {
                mClickListener.itemClick(view.getId(), index);
            }
        }
    };

    /**
     * 删除行程
     */
    private View.OnClickListener deleteListener = new View.OnClickListener() {
        @Override
        public void onClick(View view) {
            if (view == null) {
                return;
            }
            if (mClickListener != null) {
                mClickListener.itemClick(view.getId(), index);
            }
        }
    };

    public void setEnable(boolean isEnable) {
        mEtName.setEnabled(isEnable);
        mEtMoney.setEnabled(isEnable);
        mEtMark.setEnabled(isEnable);
        if (isEnable) {
            mIvLinkArrow.setVisibility(View.VISIBLE);
        } else {
            mIvLinkArrow.setVisibility(View.GONE);
            mClickListener = null;
        }
        setCanDelete(isEnable);
    }

    @Override
    public void viewClick(View view, boolean isAddView) {
        if (view == null) {
            return;
        }
        int id = view.getId();
        if (id == R.id.iv_pic) {
            if (mClickListener != null) {
                if (isAddView) {
                    mClickListener.itemClick(view.getId(), index);
                } else {
                    mClickListener.itemClick(-1, index);
                }
            }

        } else if (id == R.id.iv_delete) {
            deleteApprovePic(view);

        }

    }

    private void deleteApprovePic(View view) {
        int position = (int) view.getTag();
        mUploadList.remove(position);
        mPicData.remove(position);
        mPicAdapter.refreshData(mPicData);
        if (mClickListener != null) {
            mClickListener.itemClick(R.id.id_approve_upload_image, index);
        }
    }

    @Override
    public void uploadProgress(final int position) {
        mActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                dealUploadProgress(position);
            }
        });

    }

    private void dealUploadProgress(int position) {
        int size = mUploadImage.size();
        if (position > 0) {
            ProDialog("    " + position + "/" + size + "    ");
        } else {
            endProDialog();
            refreshPicView();
            if (mClickListener != null) {
                mClickListener.itemClick(R.id.id_approve_upload_image, index);
            }
        }
    }

    private void refreshPicView() {
        int size = mPicData.size();
        String totalStr = I18NHelper.getText("xt.feed_general_view.text.bill_image")/* 发票图片 */;
        if (size <= 0) {
            totalStr += I18NHelper.getText("xt.feed_general_view.text.no")/* (无) */;
        } else {
            totalStr +=
                    I18NHelper.getFormatText("xt.feed_general_view.text.sum_zhang",size+"");/* (共{0}张) */
        }
        mTvTotel.setText(totalStr);
        if (mPicAdapter != null) {
            mPicAdapter.refreshData(mPicData);
        }
    }

    public List<ApproveGeneralPic> getUploadList() {
        return mUploadList;
    }

    @Override
    public void backFile(String originPath, String uploadPath) {
        File file = new File(originPath);
        ApproveGeneralPic uploadFile = new ApproveGeneralPic();
        uploadFile.value = EnumDef.FeedAttachmentType.ImageFile.value;
        uploadFile.value1 = uploadPath;
        uploadFile.value3 = file.getAbsolutePath();
        if (mUploadList == null) {
            mUploadList = new ArrayList<>();
        } else {
            mUploadList.add(uploadFile);
        }
    }

    @Override
    public void failUploadPosition(String uploadPath) {
        ListIterator<ImgData> picDataIteretor = mPicData.listIterator();
        for (; picDataIteretor.hasNext(); ) {
            ImgData imgData = picDataIteretor.next();
            ImageObjectVO imageObject = null;
            if (imgData.mObject instanceof ImageObjectVO) {
                imageObject = (ImageObjectVO) imgData.mObject;
            }
            if (imageObject != null && TextUtils.equals(imageObject.data, uploadPath)) {
                picDataIteretor.remove();
            }
        }
    }
}
