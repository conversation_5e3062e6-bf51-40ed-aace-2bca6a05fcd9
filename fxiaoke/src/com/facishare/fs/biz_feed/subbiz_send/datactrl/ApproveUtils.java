/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_feed.subbiz_send.datactrl;

import com.facishare.fs.i18n.I18NHelper;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.TypeReference;
import com.facishare.fslib.R;
import com.facishare.fs.biz_feed.bean.ApproveDiscountEntity;
import com.facishare.fs.biz_feed.bean.ApprovePayEntity;
import com.facishare.fs.biz_feed.subbiz_send.bean.ApproveForm;
import com.facishare.fs.biz_feed.subbiz_send.bean.ApproveGeneralBill;
import com.facishare.fs.biz_feed.subbiz_send.bean.ApproveTravel;
import com.facishare.fs.biz_feed.subbiz_send.bean.Evection;
import com.facishare.fs.biz_feed.subbiz_send.bean.FlowTaskInfo;
import com.facishare.fs.biz_feed.subbiz_send.bean.Loan;
import com.facishare.fs.biz_function.subbiz_attendance_new.bean.HCorrectCheckInInfo;
import com.facishare.fs.common_utils.DateTimeUtils;
import com.facishare.fs.common_utils.JsonHelper;
import com.facishare.fs.common_view.flow_gridview.AddFlowItem;
import com.fs.beans.beans.EnumDef.FeedAppoveType;

import android.text.TextUtils;

/**
 * Created by hanlz on 2016/9/28.
 */
public class ApproveUtils {
    public static String getApproveTitle(int approveType, String customName) {
        String approveTip = "";
        switch (approveType) {
            case FeedAppoveType.APPROVE_NONE:
                approveTip = I18NHelper.getText("xt.approve_utils.text.common_approve_rule")/* 普通审批制度 */;
                break;
            case FeedAppoveType.APPROVE_LEAVE:
                approveTip = I18NHelper.getText("xt.approve_utils.text.leave_rule")/* 请假制度 */;
                break;
            case FeedAppoveType.APPROVE_REIMBURSEMENT:
                approveTip = I18NHelper.getText("xt.approve_utils.text.common_baoxiao_rule")/* 普通报销制度 */;
                break;
            case FeedAppoveType.APPROVE_TRAVEL_R:
                approveTip = I18NHelper.getText("xt.approve_utils.text.travel_baoxiao.rule")/* 差旅报销制度 */;
                break;
            case FeedAppoveType.APPROVE_TRAVEL:
                approveTip = I18NHelper.getText("xt.approve_utils.text.evection_apply_rule")/* 出差申请制度 */;
                break;
            case FeedAppoveType.APPROVE_BILL:
                approveTip = I18NHelper.getText("xt.approve_utils.text.borrow_money_apply_rule")/* 借款申请制度 */;
                break;
            case FeedAppoveType.APPROVE_PAYMENT:
                approveTip = I18NHelper.getText("xt.approve_utils.text.pay_apply_rule")/* 付款申请制度 */;
                break;
            case FeedAppoveType.APPROVE_DISCOUNT:
                approveTip = I18NHelper.getText("xt.approve_utils.text.discount_apply_rule")/* 折扣申请制度 */;
                break;
            case FeedAppoveType.APPROVE_OVER_TIME:
                approveTip = I18NHelper.getText("xt.approve_utils.text_overtime_apply_rule")/* 加班申请制度 */;
                break;
            case FeedAppoveType.ATTENDANCE_CORRECTION_APPROVAL:
                approveTip = I18NHelper.getText("xt.approve_utils.text.signin_change_apply_rule")/* 考勤修正申请制度 */;
                break;
            case FeedAppoveType.APPROVE_CUSTOM:
                approveTip = I18NHelper.getFormatText("xt.approve_utils.text.rule01",customName)/* {0}制度 */;
                break;
            default:
                approveTip = I18NHelper.getText("xt.approve_utils.text.approve_rule")/* 审批制度 */;
                break;
        }
        return approveTip;
    }

    /**
     * 获取审批条图标
     *
     * @param approveType
     *
     * @return
     */
    public static int getApproveIcon(int approveType) {
        int resIcon = R.drawable.feed_send_approve_reimbursement;
        switch (approveType) {
            //请假单
            case FeedAppoveType.APPROVE_LEAVE:
                resIcon = R.drawable.feed_send_approve_leave;
                break;
            //普通报销
            case FeedAppoveType.APPROVE_REIMBURSEMENT:
                resIcon = R.drawable.feed_send_approve_reimbursement;
                break;
            //出差申请
            case FeedAppoveType.APPROVE_TRAVEL:
                resIcon = R.drawable.feed_send_approve_trip;
                break;
            //借款单
            case FeedAppoveType.APPROVE_BILL:
                resIcon = R.drawable.feed_send_approve_loan;
                break;
            //付款单
            case FeedAppoveType.APPROVE_PAYMENT:
                resIcon = R.drawable.feed_send_approve_pay;
                break;
            //折扣申请
            case FeedAppoveType.APPROVE_DISCOUNT:
                resIcon = R.drawable.feed_send_approve_discount;
                break;
            //差旅报销
            case FeedAppoveType.APPROVE_TRAVEL_R:
                resIcon = R.drawable.feed_send_approve_reimbursement;
                break;
            //加班
            case FeedAppoveType.APPROVE_OVER_TIME:
                resIcon = R.drawable.feed_send_approve_overtime;
                break;
            //考勤修正
            case FeedAppoveType.ATTENDANCE_CORRECTION_APPROVAL:
                resIcon = R.drawable.feed_send_attendance_correction;
                break;
            default:
                resIcon = R.drawable.feed_send_approve_reimbursement;
                break;
        }
        return resIcon;
    }


    public static String getApproveDes(int type, String jsonValue) {
        String des = "";
        try {
            DecimalFormat format = new DecimalFormat("0.00");
            switch (type) {
                //请假单
                case FeedAppoveType.APPROVE_LEAVE:
                    ArrayList<ApproveForm> vacationList =
                            JsonHelper.fromJsonString(jsonValue, new TypeReference<ArrayList<ApproveForm>>() {
                            });
                    des = parseContent(vacationList);
                    break;
                //普通报销
                case FeedAppoveType.APPROVE_REIMBURSEMENT:
                    ArrayList<ApproveGeneralBill> billList =
                            JsonHelper.fromJsonString(jsonValue, new TypeReference<ArrayList<ApproveGeneralBill>>() {
                            });
                    des = parseJson(billList);
                    break;
                //出差申请
                case FeedAppoveType.APPROVE_TRAVEL:
                    Evection evection = JsonHelper.fromJsonString(jsonValue, Evection.class);
                    des =  I18NHelper.getFormatText("xt.approve_utils.text.journey01",evection.items.size()+"")/* {0}个行程, */;
                    String budget = "";
                    if (evection.approveBudget != null) {
                        double approveBudget = evection.approveBudget.doubleValue();
                        budget = I18NHelper.getFormatText("xt.feed_evection_fragment.text.title.1",format.format(approveBudget))/* 预算{0}元 */;
                    } else {
                        budget = I18NHelper.getText("xt.approve_utils.text.budget")/* 预算0.00元 */;
                    }
                    des = des + budget;
                    break;
                //借款单
                case FeedAppoveType.APPROVE_BILL:
                    final Loan mLoan = JsonHelper.fromJsonString(jsonValue, Loan.class);
                    double amount = mLoan.amount.doubleValue();
                    des = I18NHelper.getFormatText("xt.approve_utils.text.borrow_money01",format.format(amount))/* 借款{0}元 */ ;
                    break;
                //付款单
                case FeedAppoveType.APPROVE_PAYMENT:
                    final ApprovePayEntity payEntity = JsonHelper.fromJsonString(jsonValue, ApprovePayEntity.class);
                    des = I18NHelper.getFormatText("xt.approve_utils.text.pay_yuan",
                                    DateTimeUtils.formatSpaceDate(new Date(payEntity.payTime * 1000)),
                                    format.format(Double.parseDouble(payEntity.payMoney)));/* {0}付款{1}元 */

                    break;
                //折扣申请
                case FeedAppoveType.APPROVE_DISCOUNT:
                    final ApproveDiscountEntity discountEntity =
                            JsonHelper.fromJsonString(jsonValue, ApproveDiscountEntity.class);
                    des = I18NHelper.getFormatText("xt.approve_utils.text.get01",discountEntity.companyName,discountEntity.discountRate)/* 给{0}{1}%折扣*/ ;
                    break;
                //差旅报销
                case FeedAppoveType.APPROVE_TRAVEL_R:
                    ArrayList<ApproveTravel> list =
                            JsonHelper.fromJsonString(jsonValue, new TypeReference<ArrayList<ApproveTravel>>() {
                            });
                    des = parseTravel(list);
                    break;
                //加班
                case FeedAppoveType.APPROVE_OVER_TIME:
                    ArrayList<ApproveForm> overTimeList =
                            JsonHelper.fromJsonString(jsonValue, new TypeReference<ArrayList<ApproveForm>>() {
                            });
                    des = parseContent(overTimeList);
                    break;
                case FeedAppoveType.ATTENDANCE_CORRECTION_APPROVAL:
                     ArrayList<HCorrectCheckInInfo> hcorrectlist = JsonHelper.fromJsonString(jsonValue, new TypeReference<ArrayList<HCorrectCheckInInfo>>() {
                     });

                    //组装描述字符串
                    HCorrectCheckInInfo hc = hcorrectlist.get(0);
                    StringBuffer stringBuffer = new StringBuffer();

                    stringBuffer.append(hc.correctionDesc);
                    if(hcorrectlist.size()>1){
                        stringBuffer.append("...");
                    }

                    des = stringBuffer.toString();
//                    des = hcorrectlist.size() + "个修正事项";
                    break;
                //自定义审批
                case FeedAppoveType.APPROVE_CUSTOM:
                    des = I18NHelper.getText("xt.approve_utils.text.custom_approve")/* 自定义审批描述 */;
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return des;
    }

    public static String parseContent(ArrayList<ApproveForm> list) {
        StringBuffer sb = new StringBuffer();
        double sumAllDay = 0d;
        double sum = 0d;
        double sumDay = 0d;
        for (ApproveForm approveForm : list) {
            if (approveForm.reasonTypeDesc != null && sb.indexOf(approveForm.reasonTypeDesc) == -1) {
                sb.append(approveForm.reasonTypeDesc);
                sb.append("、");
            }
            if (approveForm.timeType == 3 || approveForm.timeType == 0) {
                sum += approveForm.hours;
            } else {
                sumDay += approveForm.day;
            }
            sumAllDay += approveForm.day;
        }
        int lastIndex = sb.lastIndexOf("、");
        if (lastIndex != -1) {
            sb.deleteCharAt(lastIndex);
        }

        DecimalFormat format = new DecimalFormat("0.##");
        return sb.append(
                I18NHelper.getFormatText("xt.approve_utils.text.sum_apply_a_hour",format.format(sum))/* ,共计申请{0}小时 */).toString();
    }

    public static String getVacationDesc(String jsonValue, float totalValue) {
        ArrayList<ApproveForm> list = new ArrayList<>();
        try {
            list = JsonHelper.fromJsonString(jsonValue, new TypeReference<ArrayList<ApproveForm>>() {
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        float td = 0;
        float th = 0;
        for (ApproveForm approveForm : list) {
            if (approveForm.timeType == 3 || approveForm.timeType == 0) {
                th += approveForm.hours;
            } else {
                td += approveForm.day;
            }
        }
        return I18NHelper.getFormatText("xt.approve_utils.text.sum_appluy01",getVacationShowTimeStr(totalValue, td, th))/* 共申请：{0} */ ;
    }

    public static String getVacationShowTimeStr(double totalAllDay, double totalDay, double totalHour) {
        DecimalFormat format = new DecimalFormat("0.##");

        if (totalDay <= 0 && totalAllDay <= 0) {
            return "";
        }
        if (totalDay <= 0) {
            return I18NHelper.getFormatText("xt.approve_utils.text.day_hours",
                    format.format(totalAllDay),format.format(totalHour))/* {0}天({1}小时) */;
        }

        if (totalHour <= 0) {
            return I18NHelper.getFormatText("crm.adapter.PipeListAdapter.1100.v1"/* {0}天 */,format.format(totalAllDay));
        }

        return I18NHelper.getFormatText(
                        "xt.approve_utils.text.day_daya_hours",
                format.format(totalAllDay),format.format(totalDay),format.format(totalHour));/*{0} 天（{1}天+{2}小时)*/
    }

    public static String parseOverTimeContent(ArrayList<ApproveForm> list) {
        StringBuffer sb = new StringBuffer();
        double sum = 0d;
        for (ApproveForm approveForm : list) {
            sum += approveForm.hours;
        }
        DecimalFormat format = new DecimalFormat("0.0");
        sb.append(I18NHelper.getFormatText("xt.approve_utils.text.overtime_sum_apply_hour",format.format(sum))/* 加班,共计申请{0}小时 */);
        return sb.toString();
    }

    public static String parseJson(ArrayList<ApproveGeneralBill> list) {
        StringBuffer sb = new StringBuffer();
        double sum = 0d;
        for (ApproveGeneralBill form : list) {
            if (sb.indexOf(form.title) == -1) {
                sb.append(form.title);
                sb.append("、");
            }
            sum += form.amount;
        }
        int lastIndex = sb.lastIndexOf("、");
        if (lastIndex != -1) {
            sb.deleteCharAt(lastIndex);
        }
        DecimalFormat format = new DecimalFormat("0.00");
        sb.append(I18NHelper.getFormatText("xt.approve_utils.text.sum01",format.format(sum)))/* 共计{0}元 */;
        return sb.toString();
    }

    public static String parseTravel(ArrayList<ApproveTravel> list) {
        StringBuffer sb = new StringBuffer();
        double sum = 0d;
        if (list != null) {
            for (ApproveTravel travel : list) {
                sum += travel.travelAmount;
            }
        }
        sb.append(I18NHelper.getFormatText("xt.approve_utils.text.baoxiao.project01",list.size()+"")/* {0}个报销事项, */);

        DecimalFormat format = new DecimalFormat("0.00");

        sb.append(I18NHelper.getFormatText("xt.seeindeplevelfragment.text.total03",format.format(sum))/* 共{0}元 */);
        return sb.toString();
    }

    public static AddFlowItem getAddFlowItemFromFlow(FlowTaskInfo flowTaskInfo) {
        AddFlowItem item = new AddFlowItem();
        try {
            item.setCanDelete(false);
            item.setName(flowTaskInfo.getTitle());
            item.setProfileImage(flowTaskInfo.getPath());

            String des = "";
            if (TextUtils.equals(flowTaskInfo.getType(),FlowTaskInfo.APPROVAL_TASK_TYPE_ALL_PASS)
                    || TextUtils.equals(flowTaskInfo.getType(),FlowTaskInfo.APPROVAL_TASK_TYPE_ONE_PASS)){
                des = flowTaskInfo.getTypeDescription();
            }
            item.setDes(des);

            if (flowTaskInfo.getHasException()) {
                item.setImageType(AddFlowItem.IMAGE_FILE);
                item.setImageResId(R.drawable.feed_send_approve_task_exception);
            } else {
                if(!TextUtils.isEmpty(flowTaskInfo.getType())){
                    switch (flowTaskInfo.getType()) {
                        case FlowTaskInfo.APPROVAL_TASK_TYPE_ONE_PASS:
                        case FlowTaskInfo.APPROVAL_TASK_TYPE_ALL_PASS:
                            item.setImageType(AddFlowItem.IMAGE_FILE);
                            item.setImageResId(R.drawable.feed_send_approve_multi);
                            break;
                        case FlowTaskInfo.APPROVAL_TASK_TYPE_EXCLUSIVE_GATEWAY:
                            item.setImageType(AddFlowItem.IMAGE_FILE);
                            item.setImageResId(R.drawable.feed_send_approve_fz);
                        default:
                            break;
                    }
                }
            }
            item.setTag(flowTaskInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return item;
    }
}
