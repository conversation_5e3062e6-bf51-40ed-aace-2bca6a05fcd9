package com.facishare.fs.biz_feed.subbiz_send.baseview.view;

import com.facishare.fs.i18n.I18NHelper;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.view.ViewGroup;

import com.facishare.fslib.R;
import com.facishare.fs.Shell;
import com.facishare.fs.biz_feed.subbiz_send.baseview.IShowView;
import com.facishare.fs.biz_feed.subbiz_send.datactrl.SendBaseUtils;
import com.facishare.fs.common_datactrl.draft.BaseVO;
import com.facishare.fs.pluginapi.crm.biz_api.ICrmData;

import java.text.SimpleDateFormat;
import java.util.Locale;

/**
 * Created by wangyp on 2017/6/8.
 */

public class CrmDataView implements IShowView {
    ViewGroup parentView;
    BaseVO mBaseVO;
    Context mContext;
    @Override
    public View createBackFill(final Context context, Object... objects) {
        parentView = (ViewGroup)objects[0];
        mBaseVO = (BaseVO) objects[1];
        mContext = context;


        if (mBaseVO.mStartTime != 0 && mBaseVO.mEndTime != 0) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.CHINA);
            String content = dateFormat.format(mBaseVO.mStartTime) + I18NHelper.getText("tx.feedpkassistantviewcontroler.text.to_")/*  至  */ + dateFormat.format(mBaseVO.mEndTime);
            mBaseVO.setCrmData(mBaseVO.mStartTime, mBaseVO.mEndTime);
            int icon = R.drawable.feed_send_crm_data;//

            final View itemView = SendBaseUtils.getBaseViewItem(context,parentView, R.layout.edit_item_layout,icon);

            SendBaseUtils.BaseViewHolder baseViewHolder = SendBaseUtils.getBaseView(itemView);
            baseViewHolder.ivIcon.setImageResource(icon);
            baseViewHolder.txtTitle.setText(I18NHelper.getText("xt.send_base_utils.text.crm_data")/* CRM数据 */);
            baseViewHolder.txtContent.setText(content);

            itemView.setTag(icon);
            baseViewHolder.deleteView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    parentView.removeView(itemView);
                    mBaseVO.removeCrmData();
                }
            });
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Shell.go2SelectCrmData((Activity) mContext, mBaseVO.mStartTime, mBaseVO.mEndTime, SendBaseUtils.REQUESTCODE_CRM_DATA);
                }
            });
        }


        return null;
    }

    @Override
    public View onResultBackFill(Context context, Object... objects) {
        parentView = (ViewGroup)objects[0];
        mBaseVO = (BaseVO) objects[1];
        mContext = context;
        Intent data = (Intent) objects[2];
        mBaseVO.mStartTime = data.getLongExtra(ICrmData.START_TIME, 0);
        mBaseVO.mEndTime = data.getLongExtra(ICrmData.END_TIME, 0);
        createBackFill(context,parentView,mBaseVO);
        return null;
    }

    @Override
    public void onClick(Context context, Object... objects) {
        Shell.go2SelectCrmData((Activity) context, SendBaseUtils.REQUESTCODE_CRM_DATA);
    }
}
