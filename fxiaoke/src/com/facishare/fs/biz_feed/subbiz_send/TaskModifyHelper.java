package com.facishare.fs.biz_feed.subbiz_send;

import com.facishare.fs.context.FSContextManager;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiParameterList;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.fxlog.FCLog;

import java.util.Date;

/** 
 *  
 * <AUTHOR> clw 
 */
public class TaskModifyHelper {
	private static final String TAG = TaskModifyHelper.class.getSimpleName();
	private OnTaskStatusChangedListener mOnTaskStatusChangedListener;
	private int mFeedId;
	
	public TaskModifyHelper(int feedId,OnTaskStatusChangedListener listener){
		mFeedId = feedId;
		mOnTaskStatusChangedListener = listener;
	}
	
	public void postExecuterFinishTask(){
		FCLog.d(TAG,"postExecuterFinishTask");
	   	if(mOnTaskStatusChangedListener != null){
    		mOnTaskStatusChangedListener.onStartChangeTaskStatus();
    	}
		WebApiUtils.postAsync("FeedTask", "ExecuterFinishTask"
                , WebApiParameterList.create()
                 .with("feedId",mFeedId)          
                , mExecuterFinishRequestCallback);
	}		
	
    public void postExecuterRedoTask(){
		FCLog.d(TAG,"postExecuterRedoTask");
	   	if(mOnTaskStatusChangedListener != null){
    		mOnTaskStatusChangedListener.onStartChangeTaskStatus();
    	}
		WebApiUtils.postAsync("FeedTask", "ExecuterRedoTask"
                , WebApiParameterList.create()
                 .with("feedId", mFeedId) 
                 .with("executerId", FSContextManager.getCurUserContext().getAccount().getEmployeeIntId())
                , mExecuterRedoRequestCallback);
	}		
	
	
	private WebApiExecutionCallback<Integer> mExecuterFinishRequestCallback =  new WebApiExecutionCallback<Integer>(){
        @Override 
        public TypeReference<WebApiResponse<Integer>> getTypeReference() {
            return new TypeReference<WebApiResponse<Integer>>() { };
        }
        @Override 
        public void completed(Date time, Integer response) {          
         	if(mOnTaskStatusChangedListener != null){
        		mOnTaskStatusChangedListener.onTaskStatusChangedSuccess();
        	}
           	FCLog.d(TAG,"postExecuterFinishTask success");  
          
        }
        @Override 
        public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {          
        	if(mOnTaskStatusChangedListener != null){
        		mOnTaskStatusChangedListener.onTaskStatusChangedFailed();
        	}
        	//Toast.makeText(mContext, "修改错误，请稍后再试", Toast.LENGTH_LONG).show();
        	FCLog.e(TAG,"postExecuterFinishTask failed,httpStatusCode="+httpStatusCode+",err="+error);
           
        }
    };
    
    

	private WebApiExecutionCallback<Integer> mExecuterRedoRequestCallback =  new WebApiExecutionCallback<Integer>(){
        @Override 
        public TypeReference<WebApiResponse<Integer>> getTypeReference() {
            return new TypeReference<WebApiResponse<Integer>>() { };
        }
        @Override 
        public void completed(Date time, Integer response) { 
        	if(mOnTaskStatusChangedListener != null){
        		mOnTaskStatusChangedListener.onTaskStatusChangedSuccess();
        	}
           	FCLog.e(TAG,"mExecuterRedoRequestCallback success");  
          
        }
        @Override 
        public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {          
        	if(mOnTaskStatusChangedListener != null){
        		mOnTaskStatusChangedListener.onTaskStatusChangedFailed();
        	}
          	//Toast.makeText(mContext, "修改错误，请稍后再试", Toast.LENGTH_LONG).show();
        	FCLog.e(TAG,"mExecuterRedoRequestCallback failed,httpStatusCode="+httpStatusCode+",err="+error);           
        }
    };
	
	public interface OnTaskStatusChangedListener{
		public void onStartChangeTaskStatus();
		public void onTaskStatusChangedSuccess();
		public void onTaskStatusChangedFailed();
	}	
    
}
