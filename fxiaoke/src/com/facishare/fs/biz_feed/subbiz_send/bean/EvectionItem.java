package com.facishare.fs.biz_feed.subbiz_send.bean;

import com.facishare.fs.i18n.I18NHelper;
import android.text.TextUtils;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Created by hanlz on 2016/6/6.
 */
public class EvectionItem implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1L;

    @JSONField(name="feedID")
    public int feedID;

    @J<PERSON>NField(name="startPoint")
    public String startPoint;

    @JSONField(name="endTime")
    public long endTime;

    @JSONField(name="startTime")
    public long startTime;

    @JSONField(name="endPoint")
    public String endPoint;

    @JSONField(name="vehicle")
    public int vehicle;

    @JSONField(name="strVehicle")
    public String strVehicle;

    @JSONField(name="daysNumber")
    public int daysNumber = -1;

    public static final String[] mEvectionTools = new String[]{
            I18NHelper.getText("xt.approve_travel.text.train")/* 火车 */,
            I18NHelper.getText("xt.approve_travel.text.airplane")/* 飞机 */,
            I18NHelper.getText("xt.approve_travel.text.car")/* 汽车 */,
            I18NHelper.getText("crm.checkresult.CheckRepeatOperType.1656")/*"其他"*/
    };

    public static final int[] mEvectionIndex = new int[]{
            1,
            2,
            3,
            4
    };

    public EvectionItem() {
    }
    @JSONCreator
    public EvectionItem(@JSONField(name="feedID") int feedID,
                        @JSONField(name="startPoint") String startPoint,
                        @JSONField(name="endTime") long endTime,
                        @JSONField(name="startTime") long startTime,
                        @JSONField(name="endPoint") String endPoint,
                        @JSONField(name="vehicle") int vehicle,
                        @JSONField(name="strVehicle") String strVehicle,
                        @JSONField(name="daysNumber") int daysNumber) {
        super();
        this.feedID = feedID;
        this.startPoint = startPoint;
        this.endTime = endTime;
        this.startTime = startTime;
        this.endPoint = endPoint;
        this.vehicle = vehicle;
        this.strVehicle = strVehicle;
        this.daysNumber = daysNumber;
        this.strVehicle = getStrVehicle(vehicle);
        this.startTime = dealTime(startTime);
        this.endTime = dealTime(endTime);
    }

    private long dealTime(long time) {
        Long timeLong = time;
        int length = timeLong.toString().length();
        if (length > 10){
            time = time / 1000;
        }
        return time;
    }

    private String getStrVehicle(int vehicle) {
        int size = mEvectionIndex.length;
        for (int i = 0; i < size; i++) {
            if (mEvectionIndex[i] == vehicle) {
                return mEvectionTools[i];
            }
        }
        return null;
    }

    public boolean isEmpty() {
        if (TextUtils.isEmpty(this.startPoint)
                && TextUtils.isEmpty(this.endPoint)
                && this.startTime == 0
                && this.endTime == 0
                && TextUtils.isEmpty(this.strVehicle)
                && this.daysNumber == -1
                ) {
            return true;
        }
        return false;
    }
}
