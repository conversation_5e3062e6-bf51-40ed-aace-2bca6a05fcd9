/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_feed.subbiz_send.bean;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Created by sunhq on 2017/9/4.
 */
public class HCorrectCheckInInfo implements Serializable {
    @JSONField(name="checkInId")
    public String checkInId;
    @JSONField(name="targetTime")
    public long targetTime;
    @JSONField(name="remark")
    public String remark;
    /**
     * 1 = today,2 = next day
     */
    @JSONField(name="correctDateType")
    public int correctDateType;
    @JSONField(name="correctTime")
    public long correctTime;
    public HCorrectCheckInInfo(){}
    @JSONCreator
    public HCorrectCheckInInfo(@JSONField(name="checkInId") String checkInId,
                               @JSONField(name="targetTime") long targetTime,
                               @JSONField(name="remark") String remark,
                               @JSONField(name="correctDateType") int correctDateType,
                               @JSONField(name="correctTime") long correctTime) {
        this.checkInId = checkInId;
        this.targetTime = targetTime;
        this.remark = remark;
        this.correctDateType = correctDateType;
        this.correctTime = correctTime;
    }
}
