package com.facishare.fs.biz_feed.subbiz_send.bean;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

public class SimpleVoteInfo {
	/**
	 * 信息源ID
	 */
	@JSONField(name="a")
	public int feedID;
	/**
	 * 已投票人数
	 */
	@JSONField(name="b")
	public  int voteEmpCount;
	/**
	 * 投票标题
	 */
	@JSONField(name="c")
	public String title;
	public SimpleVoteInfo(){
		super();
	}
	@JSONCreator
	public SimpleVoteInfo(@JSONField(name="a") int feedID,
			@JSONField(name="b") int voteEmpCount,
			@JSONField(name="c") String title) {
		this.feedID = feedID;
		this.voteEmpCount = voteEmpCount;
		this.title = title;
	}
}

