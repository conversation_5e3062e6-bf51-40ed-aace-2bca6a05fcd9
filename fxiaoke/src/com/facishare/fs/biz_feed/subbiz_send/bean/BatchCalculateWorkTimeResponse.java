package com.facishare.fs.biz_feed.subbiz_send.bean;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;

/**
 * Created by wangyp on 2017/4/27.
 */

public class BatchCalculateWorkTimeResponse implements Serializable {
    @JSONField(name="a")
    public List<CalculateWorkTimeResponse> result;

    @JSONCreator
    public BatchCalculateWorkTimeResponse(@JSONField(name="a") List<CalculateWorkTimeResponse> result) {
        this.result = result;
    }

    public BatchCalculateWorkTimeResponse(){}
}
