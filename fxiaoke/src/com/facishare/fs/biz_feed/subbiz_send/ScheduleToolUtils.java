package com.facishare.fs.biz_feed.subbiz_send;import com.facishare.fs.i18n.I18NHelper;


import android.content.Context;
import android.view.View;

import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.IdAndNameRuleLable;
import com.facishare.fs.dialogs.CustomListDialog;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;

/**
 * Created by zhujg on 2018/4/24.
 */

public class ScheduleToolUtils {

    private LinkedHashMap<Integer,String> repeatTypeMap;

    private LinkedHashMap<Integer,String> repeatDataMap;

    private static ScheduleToolUtils INSTANCE = new ScheduleToolUtils();

    public static ScheduleToolUtils getInstance(){
        return INSTANCE;
    }

    private ScheduleToolUtils(){
        init();
    }

    private void init(){
        if(repeatTypeMap==null){
            repeatTypeMap = new LinkedHashMap<>();
            repeatTypeMap.put(0,I18NHelper.getText("xt.x_send_schedule_activity.text.no_repetition")/* 不重复 */);
            repeatTypeMap.put(1,I18NHelper.getText("xt.schedule_tool_utils.text.day")/* 每日 */);
            repeatTypeMap.put(2,I18NHelper.getText("xt.schedule_repeat_setting_layout.text.week")/* 每周 */);
            repeatTypeMap.put(3,I18NHelper.getText("xt.schedule_tool_utils.text.two_week")/* 每两周 */);
            repeatTypeMap.put(4,I18NHelper.getText("xt.schedule_tool_utils.text.month")/* 每月 */);
        }

        if(repeatDataMap == null){
            repeatDataMap = new LinkedHashMap<>();
            repeatDataMap.put(1,I18NHelper.getText("xt.attendance_new_worktime_setting_item.text.on_monday")/* 周一 */);
            repeatDataMap.put(2,I18NHelper.getText("xt.datepickerview.text.on_tuesday")/* 周二 */);
            repeatDataMap.put(3,I18NHelper.getText("xt.datepickerview.text.on_wednesday")/* 周三 */);
            repeatDataMap.put(4,I18NHelper.getText("xt.datepickerview.text.thursday")/* 周四 */);
            repeatDataMap.put(5,I18NHelper.getText("xt.datepickerview.text.friday")/* 周五 */);
            repeatDataMap.put(6,I18NHelper.getText("xt.datepickerview.text.on_saturday")/* 周六 */);
            repeatDataMap.put(7,I18NHelper.getText("xt.datepickerview.text.sunday")/* 周日 */);
        }
    }

    public ArrayList<IdAndNameRuleLable> createRepreatTypeTagList(){
        ArrayList<IdAndNameRuleLable> list=  createTagList(repeatTypeMap);
        list.remove(0);
        return list;
    }
    public ArrayList<IdAndNameRuleLable> createRepreatDataTagList(){
        return createTagList(repeatDataMap);
    }

    public static ArrayList<IdAndNameRuleLable> createTagList(LinkedHashMap<Integer,String> map){
        ArrayList<IdAndNameRuleLable> repeatList = new ArrayList<>();
        Iterator<Integer> it = map.keySet().iterator();
        while(it.hasNext()){
            Integer key = it.next();
            String value = map.get(key);
            repeatList.add(new IdAndNameRuleLable(String.valueOf(key),value));
        }
        return repeatList;
    }

    public String getRepeatTypeText(Integer key){
        return repeatTypeMap.get(key);
    }

    public String getText(ArrayList<IdAndNameRuleLable> list){
        StringBuffer buffer = new StringBuffer();
        if(list != null){
            for(IdAndNameRuleLable tag: list){
                if(tag.isSeleced){
                    buffer.append(tag.name+",");
                }
            }
        }
        // 去除最后一个顿号
        if (buffer.length() > 0) {
            buffer.deleteCharAt(buffer.length() - 1);
        }
        return buffer.toString();
    }

    public ArrayList<Integer> getSelectedResult(ArrayList<IdAndNameRuleLable> list){
        ArrayList<Integer> result = new ArrayList<>();
        if(list != null){
            for(IdAndNameRuleLable tag: list){
                if(tag.isSeleced){
                    result.add(Integer.parseInt(tag.getId()));
                }
            }
        }
        return result;
    }

    public String toText(ArrayList<Integer> list){
        StringBuffer buffer = new StringBuffer();
        if(list != null){
            for(Integer key: list){
                String name = repeatDataMap.get(key);
                buffer.append(name+"，");
            }
        }
        // 去除最后一个顿号
        if (buffer.length() > 0) {
            buffer.deleteCharAt(buffer.length() - 1);
        }
        return buffer.toString();
    }

    public static Integer getCurrentWeek(){
        Calendar calendar = Calendar.getInstance();
        int week = calendar.get(Calendar.DAY_OF_WEEK);
        if(week == 1){
            return 7;
        }
        return week-1;
    }

    public static void show(Context cxt , View.OnClickListener onclick){
        String[] item = new String[]{I18NHelper.getText("xt.schedule_tool_utils.text.only_this_day")/* 仅此次日程 */,I18NHelper.getText("xt.schedule_tool_utils.text.later_happen_all_day")/* 以后发生的所有日程 */};
        CustomListDialog.createCustomContextMenuDialog(cxt, item, I18NHelper.getText("xt.schedule_tool_utils.text.please_choose_affect_range")/* 请选择影响范围 */, onclick).show();
    }
}
