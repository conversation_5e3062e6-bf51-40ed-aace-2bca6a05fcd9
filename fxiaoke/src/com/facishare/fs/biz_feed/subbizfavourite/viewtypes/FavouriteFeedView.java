/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_feed.subbizfavourite.viewtypes;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fslib.R;
import com.facishare.fs.biz_feed.subbizfavourite.FavouriteViewBase;
import com.facishare.fs.biz_feed.subbizfavourite.beans.MyFavouriteItem;
import com.facishare.fs.ui.FeedsUitls;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

/**
 * Created by tianyl on 2016/4/6.
 * 工作页面收藏的feed消息。包含类型定义在EnumDef的FeedType中，类型有：
 * 1 分享 2.日志 3. 指令 4. 审批 5. 销售记录 6. 任务 7. 日程 8.服务记录 9.销售流程 10000 公告 2003 群通知 101:互动 151 日程里的定时提醒
 * 1001 客户轨迹，2001 销售记录，9998 CRM信息，2002 分享，10 PK助手，99 外勤签到
 */
public class FavouriteFeedView extends FavouriteViewBase {

    ImageView favourite_feed_icon;
    TextView favourite_feed_name;
    TextView favourite_feed_content;
    View favourite_feed_parent;
    FrameLayout fl;

    public FavouriteFeedView() {
    }

    public FavouriteFeedView(Context context) {
        super(context);
        View convertView = null;
        View content = null;
        convertView = View.inflate(context, R.layout.favourite_list_item, null);
        content = View.inflate(context, R.layout.favourite_feed_item_layout, null);

        fl = (FrameLayout) convertView.findViewById(R.id.favuorite_content);
        fl.addView(content);
        //初始化公共视图
        initCommonView(convertView);
        //需要用来展示特定内容的部分
        mViewHolder.tag = convertView.findViewById(R.id.favourite_text_content);
        favourite_feed_parent = convertView.findViewById(R.id.favourite_feed_parent);
        favourite_feed_icon = (ImageView) convertView.findViewById(R.id.favourite_feed_icon);
        favourite_feed_name = (TextView) convertView.findViewById(R.id.favourite_feed_name);
        favourite_feed_content = (TextView) convertView.findViewById(R.id.favourite_feed_content);

        mLayoutitemView = convertView;
        mLayoutitemView.setTag(this);//将对应的数据控制类绑定在视图上
    }

    @Override
    public boolean isMyType(MyFavouriteItem item) {
        if (item == null) {
            return false;
        } else {
            if ("Feed".equals(item.type)) {
                return true;
            } else {
                return false;
            }
        }
    }

    @Override
    public void refreshViews(final MyFavouriteItem my) {
        super.refreshViews(my);
        if (my.content == null || TextUtils.isEmpty(my.content)) {
            return;
        }
        favourite_feed_content.setText(my.content);
        TypeData typeData = getFeedTypeData(my.subType);
        if (typeData == null) {
            return;
        }
        favourite_feed_name.setText(typeData.ftype);
        favourite_feed_icon.setImageResource(typeData.drawableid);
        mLayoutitemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 屏蔽跳转新Feed详情页：项目任务（201）、群通知（2003）
                int feedId = -1;
                int feedType = -1;
                try {
                    feedId = Integer.parseInt(my.feedId);
                    feedType = Integer.parseInt(my.subType);
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                }

                if (my.subType.equals("201") || my.subType.equals("2003")) {
                    FeedsUitls.showDetailsInfoOld(mContext, feedId);
                } else {
                    //FeedsUitls.showDetailsInfo(mContext, feedId);
                    FeedsUitls.showFeedDetail(mContext, feedId, feedType);
                }
            }
        });

    }

    public void clearSrc() {
        super.clearSrc();
        favourite_feed_icon = null;
        favourite_feed_name = null;
        favourite_feed_content = null;
        fl = null;
        favourite_feed_parent = null;
    }

    @Override
    public FavouriteViewBase newInstance(Context ctx) {
        return new FavouriteFeedView(ctx);
    }

    class TypeData {
        public int drawableid;
        public String ftype = "";
    }

    //    1 分享 2.日志 3. 指令 4. 审批 5. 销售记录 6. 任务 7. 日程 8.服务记录 9.销售流程 10 PK助手 10000 公告 2003 群通知 101:互动 151 日程里的定时提醒
    //     1001 客户轨迹，2001 销售记录，9998 CRM信息，2002 分享，99 外勤签到
    TypeData getFeedTypeData(String type) {
        TypeData data = new TypeData();

        if (type == null) {
            return data;
        }
        if (type.equals("1")) {
            data.drawableid = R.drawable.msg_share_icon;
            data.ftype = I18NHelper.getText("crm.layout.work_inc_header.7056")/* 分享 */;
        } else if (type.equals("2")) {
            data.drawableid = R.drawable.msg_log_icon;
            data.ftype = I18NHelper.getText("qx.session.board_type_des.plan")/* 日志 */;
        } else if (type.equals("3")) {
            data.drawableid = R.drawable.msg_order_icon;
            data.ftype = I18NHelper.getText("xt.session_layout2bc.text.instruct")/* 指令 */;
        } else if (type.equals("4")) {
            data.drawableid = R.drawable.qx_msg_ugt_approval;
            data.ftype = I18NHelper.getText("xt.approve_list_center_item.text.approval")/* 审批 */;
        } else if (type.equals("5")) {
            data.drawableid = R.drawable.msg_sales_records_icon;
            data.ftype = I18NHelper.getText("xt.x_feed_detail_activity.text.salesinfo")/* 销售记录 */;
        } else if (type.equals("6")) {
            data.drawableid = R.drawable.qx_msg_ugt_task;
            data.ftype = I18NHelper.getText("th.base.view.task")/* 任务 */;
        } else if (type.equals("7")) {
            data.drawableid = R.drawable.msg_schedule_icon;
            data.ftype = I18NHelper.getText("xt.schedule_feed_display_plug.text.day")/* 日程 */;
        } else if (type.equals("8")) {
            data.drawableid = R.drawable.msg_serve_record_icon;
            data.ftype = I18NHelper.getText("xt.favourite_feed_view.text.service_record")/* 服务记录 */;
        } else if (type.equals("9")) {
            data.drawableid = R.drawable.fcrm_icon_salesprocess;
            data.ftype = I18NHelper.getText("xt.favourite_Feed_view.text.sales_process")/* 销售流程 */;
        } else if (type.equals("10")) {
            data.drawableid = R.drawable.msg_pk_icon;
            data.ftype = I18NHelper.getText("xt.favourite_feed_view.text.pk_assistant")/* PK助手 */;
        } else if (type.equals("99")) {
            data.drawableid = R.drawable.msg_outdoor_sign_icon;
            data.ftype = I18NHelper.getText("xt.function_home_list_myfunction_item.text.field_attendance")/* 外勤签到 */;
        } else if (type.equals("101")) {
            data.drawableid = R.drawable.default_photo;
            data.ftype = I18NHelper.getText("xt.favourite_feed_view.text.interaction")/* 互动 */;
        } else if (type.equals("201")) {
            data.drawableid = R.drawable.qx_msg_ugt_task;
            data.ftype = I18NHelper.getText("qx.session.msg_des.igt_title_for_project_task")/* 项目任务 */;
        } else if (type.equals("1001")) {
            data.drawableid = R.drawable.default_photo;
            data.ftype = I18NHelper.getText("xt.favourite_feed_view.text.client_track")/* 客户轨迹 */;
        } else if (type.equals("2001")) {
            data.drawableid = R.drawable.msg_sales_records_icon;
            data.ftype = I18NHelper.getText("xt.x_feed_detail_activity.text.salesinfo")/* 销售记录 */;
        } else if (type.equals("2002")) {
            data.drawableid = R.drawable.default_photo;
            data.ftype = I18NHelper.getText("crm.layout.work_inc_header.7056")/* 分享 */;
        } else if (type.equals("2003")) {
            data.drawableid = R.drawable.msg_worknotice_icon;
            data.ftype = I18NHelper.getText("xt.activity_customer_session_setting.text.group_msg")/* 群通知 */;
        } else if (type.equals("2006")) {
            data.drawableid = R.drawable.qx_msg_ugt_task;
            data.ftype = I18NHelper.getText("th.base.view.task")/* 任务 */;
        } else if (type.equals("5002")) {
            data.drawableid = R.drawable.msg_notice_icon;
            data.ftype = I18NHelper.getText("xt.work_notice_item_new.text.notice")/* 公告 */;
        } else if (type.equals("5001")) {
            data.drawableid = R.drawable.msg_vote_icon;
            data.ftype = I18NHelper.getText("xt.session_layout2bc.text.group_vote")/* 群投票 */;
        } else if (type.equals("9998")) {
            data.drawableid = R.drawable.default_photo;
            data.ftype = I18NHelper.getText("xt.favourite_feed_view.text.crm_data")/* CRM信息 */;
        }
        return data;
    }
}
