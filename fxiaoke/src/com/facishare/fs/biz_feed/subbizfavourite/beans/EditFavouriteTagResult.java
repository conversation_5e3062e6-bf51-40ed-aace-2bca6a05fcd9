/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_feed.subbizfavourite.beans;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * Created by tianyl on 2016/4/12.
 */
public class EditFavouriteTagResult {
    /**
     * 结果标记
     */
    @JSONField(name="M1")
    public int code;

    public EditFavouriteTagResult() {
        super();
    }

    public EditFavouriteTagResult(@JSONField(name="M1") int code) {
        this.code = code;
    }
}
