package com.facishare.fs.biz_feed.newfeed;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.facishare.fs.biz_feed.newfeed.activity.SpeechToTextTranslucentActivity;
import com.fxiaoke.fscommon.cml.CMLFsURLEvent;
import com.fxiaoke.fxlog.FCLog;
import com.taobao.weex.annotation.JSMethod;
import com.taobao.weex.bridge.JSCallback;
import com.taobao.weex.common.WXModule;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import de.greenrobot.event.core.ISubscriber;
import de.greenrobot.event.core.MainSubscriber;

public class WeexFeedApiModule extends WXModule {
    private static final String TAG = "WeexFeedApiModule";
    private List<ISubscriber> mEvents = new ArrayList<>();
    private MainSubscriber<FeedUpdateUtils.FeedItemRefreshEvent> mFeedItemRefreshListener = null;
    private MainSubscriber<SpeechToTextTranslucentActivity.SpeechToTextChangeEvent> mSpeechToTextContentChangeListener = null;
    private MainSubscriber<CMLFsURLEvent> mFeedCMLFSLinkListener = null;

    /**
     *  “event://CML”开头的url交由卡梅龙页面处理
     */
    @JSMethod(uiThread = false)
    public void setFeedCMLFSLinkCallback(JSCallback jsCallback) {
        if (jsCallback != null) {//要让新回调生效，就需要废弃旧的，并重新构建event并注册
            unregisterEvent(mFeedCMLFSLinkListener);
            mFeedCMLFSLinkListener = new MainSubscriber<CMLFsURLEvent>() {
                @Override
                public void onEventMainThread(CMLFsURLEvent event) {
                    Context context = mWXSDKInstance.getContext();
                    String uniqueId = context == null ? null : String.valueOf(context.hashCode());
                    if (TextUtils.equals(uniqueId, event.getUniqueId())){
                        if (jsCallback != null) {
//                        String backStr = JSON.toJSONString(event);
                            jsCallback.invokeAndKeepAlive(event.getControlArg());
                            FCLog.w(TAG, "setFeedCMLFSLinkCallback backStr:" + event);
                        }
                    }
                }
            };
            registerEvent(mFeedCMLFSLinkListener);
        } else {
            FCLog.w(TAG, "setFeedCMLFSLinkCallback jsCallback set null call unregister");
            unregisterEvent(mFeedCMLFSLinkListener);
        }
    }

    /**
     * 在feed列表页面的单个feed有更新时通知回调
     */
    @JSMethod(uiThread = false)
    public void setFeedItemRefreshCallback(JSCallback jsCallback) {
        if (jsCallback != null) {//要让新回调生效，就需要废弃旧的，并重新构建event并注册
            unregisterEvent(mFeedItemRefreshListener);
            mFeedItemRefreshListener = new MainSubscriber<FeedUpdateUtils.FeedItemRefreshEvent>() {
                @Override
                public void onEventMainThread(FeedUpdateUtils.FeedItemRefreshEvent event) {
                    if (jsCallback != null) {
                        jsCallback.invokeAndKeepAlive(JSON.toJSONString(event));
                    }
                }
            };
            registerEvent(mFeedItemRefreshListener);
        } else {
            FCLog.w(TAG, "setFeedItemRefreshCallback jsCallback set null call unregister");
            unregisterEvent(mFeedItemRefreshListener);
        }
    }

    /**
     * 设置语音转文字的数据更新事件的监听回调
     *
     * @param jsCallback
     */
    @JSMethod(uiThread = false)
    public void setSpeechToTextCallback(JSCallback jsCallback) {
        if (jsCallback != null) {//要让新回调生效，就需要废弃旧的，并重新构建event并注册
            unregisterEvent(mSpeechToTextContentChangeListener);
            mSpeechToTextContentChangeListener = new MainSubscriber<SpeechToTextTranslucentActivity.SpeechToTextChangeEvent>() {
                @Override
                public void onEventMainThread(SpeechToTextTranslucentActivity.SpeechToTextChangeEvent event) {
                    if (jsCallback != null) {
                        Map<String, Object> result = new HashMap<>();
                        result.put("errorCode", 0);
                        result.put("errorMessage", "success");
                        result.put("json_data", event);
                        try {
                            jsCallback.invokeAndKeepAlive(JSON.toJSONString(result));
                        } catch (Exception e) {
                            FCLog.e("SpeechToText", Log.getStackTraceString(e));
                        }
                    }
                }
            };
            registerEvent(mSpeechToTextContentChangeListener);
        } else {
            FCLog.w(TAG, "setSpeechToTextCallback jsCallback set null call unregister");
            unregisterEvent(mSpeechToTextContentChangeListener);
        }
    }

    @Override
    public void onActivityDestroy() {
        super.onActivityDestroy();
        unregisterEvents();
    }

    private void registerEvent(ISubscriber event) {
        if (mEvents == null) {
            return;
        }
        if (!mEvents.contains(event)) {
            mEvents.add(event);
            event.register();
        }
    }

    private void unregisterEvent(ISubscriber event) {
        if (event != null) {
            event.unregister();
        }
    }

    private void unregisterEvents() {
        if (mEvents != null && mEvents.size() > 0) {
            for (ISubscriber event : mEvents) {
                unregisterEvent(event);
            }
        }
    }

}
