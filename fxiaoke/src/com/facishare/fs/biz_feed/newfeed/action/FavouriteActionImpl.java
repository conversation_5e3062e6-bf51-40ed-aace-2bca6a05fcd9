package com.facishare.fs.biz_feed.newfeed.action;

import android.app.Activity;
import android.content.Intent;

import com.alibaba.fastjson.TypeReference;
import com.billy.cc.core.component.CC;
import com.billy.cc.core.component.CCResult;
import com.billy.cc.core.component.IComponentCallback;
import com.facishare.fs.BaseActivity;
import com.facishare.fs.biz_feed.adapter.IFeedContentChanged;
import com.facishare.fs.biz_feed.newfeed.Cmpt;
import com.facishare.fs.biz_feed.newfeed.FeedUpdateUtils;
import com.facishare.fs.biz_feed.newfeed.cmpt.ActionButton;
import com.facishare.fs.biz_feed.subbizfavourite.FavouriteEditTagsActivity;
import com.facishare.fs.biz_feed.subbizfavourite.beans.RemoveFavouriteResult;
import com.facishare.fs.biz_feed.subbizfavourite.utils.FavouriteWebUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.utils_fs.ToastUtils;
import com.fxiaoke.dataimpl.msg.SingletonObjectHolder;
import com.fxiaoke.fscommon.sandbox.SandboxContextManager;
import com.fxiaoke.fscommon.util.FsUrlUtils;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fshttp.web.sandbox.ISandboxContext;

import java.util.Date;
import java.util.List;

/**
 * Created by zhujg on 2018/10/24.
 */

public class FavouriteActionImpl extends BaseAction {

    public static final String FAVOURITE = "FAVOURITE";
    public static final String CANCEL_FAVOURITE = "CANCEL_FAVOURITE";

    @Override
    public void action(Cmpt cmp, ActionData ac) {
        final ActionButton button = (ActionButton)cmp;
        final Activity ctx = (Activity) ac.context;
        if(CANCEL_FAVOURITE.equals(button.getAction())){
            removeArchive(ctx,button.feedId,button,ac);
        }else{//改为cc
            String actionUrl = "event://feed/action/" + button.action;
            FsUrlUtils.ActionConfig actionConfig = new FsUrlUtils.ActionConfig(ctx, actionUrl);
            actionConfig.addParams("feed_id", button.feedId + "");
            actionConfig.addParams("is_need_refresh", true);
            actionConfig.addParams("favourite_type", 2);
            if (ac != null && ac.feedView != null) {
                actionConfig.setEventTypeCallBack(new IComponentCallback() {
                    @Override
                    public void onResult(CC cc, CCResult result) {
                        if (result.isSuccess()) {
                            ac.feedView.update();
                            FeedUpdateUtils.refresh(button.feedId, FeedActions.FAVOURITE);
                        }
                    }
                });
            }

            FsUrlUtils.gotoAction(actionConfig);
//            int feedId = button.feedId;
//            Intent it = FavouriteEditTagsActivity.getIntent(ctx, feedId + "", true);
//            ctx.startActivity(it);
        }
    }

//    @Override
//    public void render(View rootView, Object cmp) {
//
//    }



    void removeArchive(final Activity ctx,final int feedID,final ActionButton button,ActionData ac) {
//        if (!App.netIsOK.get()) {
//            ToastUtils.netErrShow();
//            return;
//        }

        //添加等待进度 update by yangs
        showProgressDialog(ctx);
        FavouriteWebUtils.removeFavourite(0, feedID + "", new WebApiExecutionCallback<RemoveFavouriteResult>() {
            @Override
            public TypeReference<WebApiResponse<RemoveFavouriteResult>> getTypeReference() {
                return new TypeReference<WebApiResponse<RemoveFavouriteResult>>() {
                };
            }

            @Override
            public Class<RemoveFavouriteResult> getTypeReferenceFHE() {
                return RemoveFavouriteResult.class;
            }
            @Override
            public ISandboxContext getSandboxContext() {
                return SandboxContextManager.getInstance().getContext(ctx);
            }
            @Override
            public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                super.failed(failureType, httpStatusCode, error);
                hideProgressDialog(ctx);
            }

            @Override
            public void completed(Date time, RemoveFavouriteResult response) {
                hideProgressDialog(ctx);
                if (response.code == 0) {
                    if (ac != null && ac.feedView != null) {
                        ac.feedView.update();
                    }
                    FeedUpdateUtils.refresh(button.feedId, FeedActions.CANCEL_FAVOURITE);
                    button.modifyButton(FAVOURITE,I18NHelper.getText("th.base.view.store")/* 收藏 */);
                    ToastUtils.show(I18NHelper.getText("xt.my_favourite_adapter.text.cancle_succeed")/* 取消成功 */);
                    List<Object> lisObjects = SingletonObjectHolder.getInstance().findObjects(IFeedContentChanged.class);
                    for (Object ob : lisObjects) {
                        IFeedContentChanged lis = (IFeedContentChanged) ob;
                        lis.onFeedCancelArchive(feedID);
                    }
                }
            }
        });
    }
}
