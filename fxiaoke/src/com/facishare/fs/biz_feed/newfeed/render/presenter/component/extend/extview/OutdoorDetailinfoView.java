package com.facishare.fs.biz_feed.newfeed.render.presenter.component.extend.extview;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import com.alibaba.fastjson.JSON;
import com.facishare.fs.biz_feed.newfeed.Cmpt;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CheckType;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2Ctrl;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.outdoorv2ActionList.OutDoorv2ActionListViewExt;
import com.google.gson.Gson;

public class OutdoorDetailinfoView {
    private CheckType checkType;
    private ViewGroup rootview;
    private Context context;
    public OutdoorDetailinfoView(Context context, ViewGroup root,Cmpt cmpt){
        this.context = context;
        rootview = root;
        checkType = new CheckType();
        initData(cmpt);
    }

    private void initData(Cmpt cmpt){
        if (cmpt!=null&&cmpt.controlArg!=null){
            try {
                Gson gson = new Gson();
                checkType = gson.fromJson(cmpt.controlArg, CheckType.class);
//                checkType = JSON.parseObject(cmpt.controlArg, CheckType.class);
            }catch (Exception e){

            }
        }

        if (checkType!=null){
            OutDoorv2ActionListViewExt outDoorV2ActionListView = new OutDoorv2ActionListViewExt(context, (ViewGroup) rootview,new OutDoorV2Ctrl(context));
            if (checkType.actionList!=null&&checkType.actionList.size()>0){
                outDoorV2ActionListView.setData(checkType);
            }else {
                outDoorV2ActionListView.setNullActionData(checkType);
            }

            rootview.addView(outDoorV2ActionListView.getView());
        }
    }
}
