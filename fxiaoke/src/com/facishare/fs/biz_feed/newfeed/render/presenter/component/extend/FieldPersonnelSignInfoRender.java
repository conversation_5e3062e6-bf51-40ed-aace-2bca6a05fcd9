/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_feed.newfeed.render.presenter.component.extend;

import com.facishare.fs.biz_feed.bean.CheckinsActionObj;
import com.facishare.fs.biz_feed.bean.CheckinsFeedExtV2;
import com.facishare.fs.biz_feed.newfeed.IFeedView;
import com.facishare.fs.biz_feed.newfeed.cmpt.extend.FieldPersonnelSignInfo;
import com.facishare.fs.biz_feed.newfeed.feedenum.RenderCacheEnum;
import com.facishare.fs.biz_feed.newfeed.render.presenter.AbsFeedRender;
import com.facishare.fs.biz_feed.newfeed.render.presenter.component.extend.extview.ActionListSimple;
import com.facishare.fs.biz_feed.newfeed.render.presenter.component.extend.extview.SignInfo;
import com.facishare.fs.biz_feed.newfeed.utils.FeedRenderCacheUtils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.api.OutDoorUtils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2Activity;
import com.facishare.fs.common_utils.StringUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.location.PluginFsLocationResult;
import com.facishare.fs.pluginapi.outdoor.GetPlanInfoArgs;
import com.facishare.fslib.R;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

public class FieldPersonnelSignInfoRender extends AbsFeedRender<FieldPersonnelSignInfo> {
    public FieldPersonnelSignInfoRender(IFeedView feedView, View parent) {
        super(feedView,parent);
    }

    @Override
    public int getLayoutId() {
        return R.layout.feed_outdoorv2_view;
    }

    @Override
    protected String getRenderCacheKey() {
        return RenderCacheEnum.FIELD_PERSONNEL_SIGN_INFO;
    }

    @Override
    protected View startResetInner(View view, FieldPersonnelSignInfo data) {
        return view;
    }

    @Override
    protected void startRenderInner(View v, FieldPersonnelSignInfo data) {
        super.startRenderInner(v, data);
        final SignInfo signInfo = data.getControlArg(SignInfo.class);
//        View v = FeedRenderCacheUtils.getOrInflateView(mctx, RenderCacheEnum.ELEMENT_OUT_DOOR, R.layout.feed_outdoorv2_view, isCacheEnable());
//        v.setTag(R.id.tag_feed_render_type, RenderCacheEnum.ELEMENT_OUT_DOOR);
//        ((ViewGroup) view).addView(v);
        View ll_outdoorv2_feed_head_action = v.findViewById(R.id.ll_outdoorv2_feed_head_action);
        TextView tv_outdoorv2_action = v.findViewById(R.id.tv_outdoorv2_action);
        TextView tv_outdoorv2_feed_error = v.findViewById(R.id.tv_outdoorv2_feed_error);
        TextView tv_outdoorv2_feed_out_error = v.findViewById(R.id.tv_outdoorv2_feed_out_error);
        TextView tv_outdoorv2_title_head = v.findViewById(R.id.iv_outdoorv2_title_head);
        View ll_outdoorv2_in_layout = v.findViewById(R.id.ll_outdoorv2_in_layout);
        TextView tv_outdoorv2_in_addr = v.findViewById(R.id.tv_outdoorv2_in_addr);
        TextView tv_outdoorv2_in_time = v.findViewById(R.id.tv_outdoorv2_in_time);

        ll_outdoorv2_in_layout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                gotoOutDoorV2Map(v, signInfo, signInfo.checkinObj);
            }
        });

        View ll_feed_outdoorv2_line = v.findViewById(R.id.ll_feed_outdoorv2_line);
        ll_feed_outdoorv2_line.setVisibility(View.GONE);

        View ll_outdoorv2_out_layout = v.findViewById(R.id.ll_outdoorv2_out_layout);
        TextView tv_outdoorv2_out_addr = v.findViewById(R.id.tv_outdoorv2_out_addr);
        TextView tv_outdoorv2_out_time = v.findViewById(R.id.tv_outdoorv2_out_time);
        ll_outdoorv2_out_layout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                gotoOutDoorV2Map(v, signInfo, signInfo.checkoutObj);
            }
        });

        ll_outdoorv2_feed_head_action.setVisibility(View.GONE);
        ((ViewGroup.MarginLayoutParams) ll_outdoorv2_in_layout.getLayoutParams()).topMargin = 0;

//        if(TextUtils.isEmpty(v2.headTitle)){
//            ll_outdoorv2_feed_head_action.setVisibility(View.GONE);
//        }else{
//            tv_outdoorv2_title_head.setText(v2.headTitle);
//            ll_outdoorv2_feed_head_action.setVisibility(View.VISIBLE);
//        }
//
//        tv_outdoorv2_action.setText(v2.headStr);
//        ll_outdoorv2_feed_head_action.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                GetPlanInfoArgs args = new GetPlanInfoArgs();
//                args.checkinId = v2.checkinsId;
//                args.isAssistant = v2.isAssistant;
//                args.userId = v2.dataUserId;
//                args.routeId = v2.routeId;
//                args.dateStr = v2.dateStr;
//                mctx.startActivity(OutDoorV2Activity.getIntent(mctx, args));
//            }
//        });

        SimpleDateFormat mSimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        if (signInfo.checkinObj != null && !TextUtils.isEmpty(signInfo.checkinObj.timeStr)) {
            ll_outdoorv2_in_layout.setVisibility(View.VISIBLE);
            tv_outdoorv2_in_addr.setText(OutDoorUtils.getFeedAddressStr(signInfo.checkinObj));
            String t = signInfo.checkinObj.timeStr;
            if (signInfo.checkinObj.time >0){
                t = mSimpleDateFormat.format(new Date(signInfo.checkinObj.time));
            }
            tv_outdoorv2_in_time.setText(t);
        } else {
            ll_outdoorv2_in_layout.setVisibility(View.GONE);
        }
        if (signInfo.checkinObj!=null&&signInfo.checkinObj.distanceRisk) {
            tv_outdoorv2_feed_error.setVisibility(View.VISIBLE);
            tv_outdoorv2_feed_error.setText(I18NHelper.getFormatText("xt.feed_attatch_view_contrler.text.signin_location.1", StringUtils.calcDistance(signInfo.checkinObj.distance))/* 签到地址距离客户：{0} */);
        } else {
            tv_outdoorv2_feed_error.setVisibility(View.GONE);
        }

        if (signInfo.checkoutObj != null && !TextUtils.isEmpty(signInfo.checkoutObj.timeStr)) {
            ll_outdoorv2_out_layout.setVisibility(View.VISIBLE);
//                ll_feed_outdoorv2_line.setVisibility(View.VISIBLE);
            tv_outdoorv2_out_addr.setText(OutDoorUtils.getFeedAddressStr(signInfo.checkoutObj));
            String t = signInfo.checkoutObj.timeStr;
            if (signInfo.checkoutObj.time >0){
                t = mSimpleDateFormat.format(new Date(signInfo.checkoutObj.time));
            }
            tv_outdoorv2_out_time.setText(t);
        } else {
//                ll_feed_outdoorv2_line.setVisibility(View.GONE);
            ll_outdoorv2_out_layout.setVisibility(View.GONE);
        }

        if (signInfo.checkoutObj != null && signInfo.checkoutObj.distanceRisk) {
            tv_outdoorv2_feed_out_error.setVisibility(View.VISIBLE);
            tv_outdoorv2_feed_out_error.setText(I18NHelper.getFormatText("xt.feed_attatch_view_contrler.text.signin_location.1",
                    StringUtils.calcDistance(signInfo.checkoutObj.distance))/* 签到地址距离客户：{0} */);
        } else {
            tv_outdoorv2_feed_out_error.setVisibility(View.GONE);
        }


    }


    protected void gotoOutDoorV2Map(final View view, final SignInfo v2, CheckinsActionObj obj) {
        ArrayList<PluginFsLocationResult> addressList = new ArrayList();
        // 添加签到地址
        PluginFsLocationResult address = new PluginFsLocationResult(obj.lat, obj.lon);
        address.setAddress(obj.addressDesc);
        addressList.add(address);
        // 添加客户地址
        if(v2.customerLat> 0 || v2.customerLon>0){
            address = new PluginFsLocationResult(v2.customerLat, v2.customerLon);
            address.setAddress(v2.customerAddr);
            addressList.add(address);
        }

        HostInterfaceManager.getIMap().showCustomerAddress(view.getContext(), addressList);
    }
}
