package com.facishare.fs.biz_feed.newfeed.factory;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;

import com.alibaba.fastjson.TypeReference;
import com.facishare.fs.BaseActivity;
import com.facishare.fs.biz_feed.FeedTaskEmployeesActivity;
import com.facishare.fs.biz_feed.api.FeedTaskWebService;
import com.facishare.fs.biz_feed.newfeed.Cmpt;
import com.facishare.fs.biz_feed.newfeed.ControlArg;
import com.facishare.fs.biz_feed.newfeed.FeedUpdateUtils;
import com.facishare.fs.biz_feed.newfeed.IAction;
import com.facishare.fs.biz_feed.newfeed.action.ActionData;
import com.facishare.fs.biz_feed.newfeed.action.BaseAction;
import com.facishare.fs.biz_feed.newfeed.action.FeedActions;
import com.facishare.fs.biz_feed.newfeed.api.BizArg;
import com.facishare.fs.biz_feed.newfeed.IFeedView;
import com.facishare.fs.biz_feed.newfeed.utils.FeedProgressUtils;
import com.facishare.fs.biz_feed.subbiz_send.XSendReplyActivity;
import com.facishare.fs.biz_feed.view.TaskRemindView;
import com.facishare.fs.common_datactrl.draft.ReplyVO;
import com.facishare.fs.common_datactrl.draft.draft_fw.DraftType;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.new_crm.utils.FxCrmUtils;
import com.facishare.fs.utils_fs.NetUtils;
import com.facishare.fs.utils_fs.ToastUtils;
import com.fxiaoke.fscommon.sandbox.SandboxContextManager;
import com.fxiaoke.fscommon.sandbox.SandboxUtils;
import com.fxiaoke.fscommon_res.view.datepickerviews.base.BaseTimePickerDialog;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fshttp.web.sandbox.ISandboxContext;

import java.util.Calendar;
import java.util.Date;

public class DNetTaskActionFactory {

    static final int Req_Code_Change_Task_Executors = 2001;
    static final int Req_Code_Change_Task_Remindtime = 2002;
    static final int Req_Code_Change_Task_Time = 2003;

    static void putAllAction(ActionFactory factory) {
        //任务-执行人
        factory.put("DNET_TASK_CHOOSE_EMPLOYEE", new ViewEmployeesAction(true));//只用Action跳转
        factory.put("DNET_TASK_VIEW_EMPLOYEE", new ViewEmployeesAction(false));
        //任务-提醒
        factory.put("DNET_TASK_CHOOSE_REMIND", new ChooseRemindAction(true));
        factory.put("DNET_TASK_VIEW_REMIND", new ChooseRemindAction(false));
        //任务-截止时间
        factory.put("DNET_TASK_CHOOSE_TIME", new ChooseDeadlineAction());
        //任务-完成
        factory.put("DNET_TASK_FINISH", new TaskFinishAction());
        //任务-取消
        factory.put("DNET_TASK_CANCEL", new TaskCancelAction());
        //任务-拒绝
        factory.put("DNET_TASK_REFUSE", new TaskRefuseAction());
        //任务-自己重做
        factory.put("DNET_TASK_REDO", new TaskRedoAction());
        //任务-安排执行人重做
        factory.put("DNET_TASK_FINISHED_REDO", new TaskFinishedRedoAction());
        //任务-继续执行
        factory.put("DNET_CONTINUE_TASK", new TaskContinueAction());
        //任务-点评
        factory.put("DNET_COMMENT_TASK", new TaskCommentAction());
    }

    /**
     * 任务-执行人跳转
     */
    static class ViewEmployeesAction implements IAction {

        private boolean isAssigner;

        public ViewEmployeesAction(boolean isAssigner) {
            this.isAssigner = isAssigner;
        }

        @Override
        public void action(Cmpt cmpt, ActionData view) {
            ControlArg.TaskControlArg controlArg = cmpt.getControlArg(ControlArg.TaskControlArg.class);

            Intent it = FeedTaskEmployeesActivity.getStartIntent(view.context, controlArg.feedId, isAssigner, false, false, true);
            view.feedView.getActivity().startActivityForResult(it, Req_Code_Change_Task_Executors);
        }
    }

    /**
     * 任务-提醒跳转
     */
    static class ChooseRemindAction implements IAction<Cmpt> {

        private boolean isEditable;

        public ChooseRemindAction(boolean editable) {
            this.isEditable = editable;
        }

        @Override
        public void action(Cmpt cmpt, ActionData view) {
            ControlArg.TaskControlArg controlArg = cmpt.getControlArg(ControlArg.TaskControlArg.class);

            Intent intent = TaskRemindView.getNewFeedIntent(view.context, controlArg.feedId, controlArg.remindTimes, isEditable);
            view.feedView.getActivity().startActivityForResult(intent, Req_Code_Change_Task_Remindtime);
        }
    }

    /**
     * 任务-截止时间选择
     */
    static class ChooseDeadlineAction implements IAction<Cmpt> {

        @Override
        public void action(Cmpt cmpt, ActionData view) {
            ControlArg.TaskControlArg controlArg = cmpt.getControlArg(ControlArg.TaskControlArg.class);

            showDeadLineMenu(view.context, controlArg.feedId, controlArg.deadline, controlArg.isAllDay, new Callback() {
                @Override
                public void onSuccess() {
                    view.feedView.update();
                    FeedUpdateUtils.refresh(controlArg.feedId, cmpt.action);
                }
            } );
        }
    }

    static class TaskFinishAction extends BaseAction {

        @Override
        public void action(Cmpt cmpt, ActionData view) {
            ControlArg.TaskControlArg controlArg = cmpt.getControlArg(ControlArg.TaskControlArg.class);

            finishTask(view.context, controlArg.feedId, view.feedView);
        }
    }

    static class TaskCancelAction extends BaseAction {

        @Override
        public void action(Cmpt cmpt, ActionData view) {
            ControlArg.TaskControlArg controlArg = cmpt.getControlArg(ControlArg.TaskControlArg.class);
            cancelTask(view.context, controlArg.feedId, view.feedView);
        }
    }

    static class TaskRefuseAction extends BaseAction {

        @Override
        public void action(Cmpt cmpt, ActionData view) {
            ControlArg.TaskControlArg controlArg = cmpt.getControlArg(ControlArg.TaskControlArg.class);
            refuseTask(view.context, controlArg.feedId, view.feedView);
        }
    }

    static class TaskRedoAction extends BaseAction {

        @Override
        public void action(Cmpt cmpt, ActionData view) {
            ControlArg.TaskControlArg controlArg = cmpt.getControlArg(ControlArg.TaskControlArg.class);
            redoTask(view.context, controlArg.feedId, controlArg.employeeId, view.feedView);
        }
    }

    static class TaskFinishedRedoAction extends BaseAction {

        @Override
        public void action(Cmpt cmpt, ActionData view) {
            ControlArg.TaskControlArg controlArg = cmpt.getControlArg(ControlArg.TaskControlArg.class);

            ReplyVO vo = new ReplyVO();
            vo.feedID = controlArg.feedId;
            vo.feedType = controlArg.feedType;
            vo.replyToEmployeeID = controlArg.employeeId;
            vo.draftType = DraftType.DRAFT_REDO_TASK;
            XSendReplyActivity.startReply(view.context, vo);
        }
    }

    static class TaskContinueAction extends BaseAction {

        @Override
        public void action(Cmpt cmpt, ActionData view) {
            ControlArg.TaskControlArg controlArg = cmpt.getControlArg(ControlArg.TaskControlArg.class);

            ReplyVO vo = new ReplyVO();
            vo.feedID = controlArg.feedId;
            vo.feedType = controlArg.feedType;
            vo.replyToEmployeeID = controlArg.employeeId;
            vo.draftType = DraftType.DRAFT_CONTINUE_TASK;
            XSendReplyActivity.startReply(view.context, vo);
        }
    }

    static class TaskCommentAction extends BaseAction {

        @Override
        public void action(Cmpt cmpt, ActionData view) {
            final ControlArg.TaskControlArg controlArg = cmpt.getControlArg(ControlArg.TaskControlArg.class);
            ReplyVO vo = new ReplyVO();
            vo.arg = new BizArg();
            vo.arg.bizArg = cmpt.bizArg;
            vo.feedID = controlArg.feedId;
            vo.feedType = controlArg.feedType;
            vo.draftType = DraftType.DRAFT_TASK_SINGLE_COMMENT;
            vo.employeeIds = controlArg.executerIds;
            XSendReplyActivity.startNewFeedReply(view.context, vo);
        }
    }

    static void showDeadLineMenu(Context context, int feedId, long time, boolean isAllDay, Callback callback) {
        BaseTimePickerDialog mTimePicker = new BaseTimePickerDialog(context, BaseTimePickerDialog.DATE_TIME_PICKER);
        // 设置截止时间
        mTimePicker.setOnDateSetListener(c -> seqAssignerModifyDeadLine(context, feedId, c.getTime().getTime(), isAllDay, callback));
        // 清空截止时间
        mTimePicker.setOnDateClearListener(() -> seqAssignerModifyDeadLine(context, feedId, 0, isAllDay, callback));
        // set time
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);
        mTimePicker.setCalendar(calendar);
        mTimePicker.show();
    }

    //修改任务时间
    static void seqAssignerModifyDeadLine(Context context, int feedId, long deadline, boolean isAllDay, Callback callback) {
        if (((BaseActivity) context).isFinishing()) {
            return;
        }
        if (!NetUtils.checkNet(context)) {
            ToastUtils.netErrShow();
            return;
        }

        FeedProgressUtils.show((Activity) context);
        FeedTaskWebService.AssignerModifyDeadLine(feedId, deadline, isAllDay, new WebApiExecutionCallback<Integer>() {
            @Override
            public TypeReference<WebApiResponse<Integer>> getTypeReference() {
                return new TypeReference<WebApiResponse<Integer>>() {
                };
            }
            @Override
            public ISandboxContext getSandboxContext() {
                return SandboxContextManager.getInstance().getContext(SandboxUtils.getActivityByContext(context));
            }
            @Override
            public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                FeedProgressUtils.hide((Activity) context);
                FxCrmUtils.showToast(failureType, httpStatusCode, error);
            }

            @Override
            public void completed(Date time, Integer response) {
                FeedProgressUtils.hide((Activity) context);
                if (response > 0) {
                    ToastUtils.showToast(I18NHelper.getText("xt.feed_task_view_controller.text.update_endtime_succeed")/* 更改截止时间成功! */);
                    callback.onSuccess();
                } else {
                    ToastUtils.showToast(I18NHelper.getText("xt.feed_task_view_controller.text.update_endtime_error")/* 更改截止时间失败! */);
                }
            }
        });
    }

    interface Callback {
        void onSuccess();
    }

    static void finishTask(Context context, int feedId, IFeedView feedView) {
        if (!NetUtils.checkNet(context)) {
            ToastUtils.netErrShow();
            return;
        }
        FeedProgressUtils.show((Activity) context);
        FeedTaskWebService.ExecuterFinishTask(feedId, "", null,
                new WebApiExecutionCallback<Integer>() {
                    @Override
                    public TypeReference<WebApiResponse<Integer>> getTypeReference() {
                        return new TypeReference<WebApiResponse<Integer>>() {
                        };
                    }
                    @Override
                    public ISandboxContext getSandboxContext() {
                        return SandboxContextManager.getInstance().getContext(SandboxUtils.getActivityByContext(context));
                    }
                    @Override
                    public void completed(Date time, Integer response) {
                        FeedProgressUtils.hide((Activity) context);
                        if (response <= 0) {
                            ToastUtils.showToast(I18NHelper.getText("xt.x_feed_detail_activity.text.update_task_error")/* 更改任务状态失败! */);
                            return;
                        }
                        ToastUtils.showToast(I18NHelper.getText("xt.x_feed_detail_activity.text.operation_succeed")/* 操作成功! */);
                        feedView.update();
                        FeedUpdateUtils.refresh(feedId, FeedActions.DNET_TASK_FINISH);
                    }

                    @Override
                    public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                        FeedProgressUtils.hide((Activity) context);
                        FxCrmUtils.showToast(failureType, httpStatusCode, error);
                    }
                });
    }


    static void cancelTask(Context context, int feedId, IFeedView feedView) {
        if (!NetUtils.checkNet(context)) {
            ToastUtils.netErrShow();
            return;
        }
        FeedProgressUtils.hide((Activity) context);
        FeedTaskWebService.AssignerCancelTask(feedId, new WebApiExecutionCallback<Integer>() {
            @Override
            public TypeReference<WebApiResponse<Integer>> getTypeReference() {
                return new TypeReference<WebApiResponse<Integer>>() {
                };
            }
            @Override
            public ISandboxContext getSandboxContext() {
                return SandboxContextManager.getInstance().getContext(SandboxUtils.getActivityByContext(context));
            }
            @Override
            public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                FeedProgressUtils.hide((Activity) context);
                FxCrmUtils.showToast(failureType, httpStatusCode, error);
            }

            @Override
            public void completed(Date time, Integer integer) {
                FeedProgressUtils.hide((Activity) context);
                ToastUtils.showToast(I18NHelper.getText("xt.feed_moremenu_helper.text.cancle_task_succeed")/* 取消任务成功! */);
                feedView.update();
                FeedUpdateUtils.refresh(feedId, FeedActions.DNET_TASK_CANCEL);
            }
        });
    }

    static void refuseTask(Context context, int feedId, IFeedView feedView) {
        if (!NetUtils.checkNet(context)) {
            ToastUtils.netErrShow();
            return;
        }
        FeedProgressUtils.hide((Activity) context);
        FeedTaskWebService.ExecuterCancelTask(feedId, new WebApiExecutionCallback<Integer>() {
            @Override
            public TypeReference<WebApiResponse<Integer>> getTypeReference() {
                return new TypeReference<WebApiResponse<Integer>>() {
                };
            }
            @Override
            public ISandboxContext getSandboxContext() {
                return SandboxContextManager.getInstance().getContext(SandboxUtils.getActivityByContext(context));
            }
            @Override
            public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                FeedProgressUtils.hide((Activity) context);
                FxCrmUtils.showToast(failureType, httpStatusCode, error);
            }

            @Override
            public void completed(Date time, Integer integer) {
                FeedProgressUtils.hide((Activity) context);
                ToastUtils.showToast(I18NHelper.getText("xt.feed_moremenu_helper.text.refuse_task_succeed")/* 拒绝执行任务成功! */);
                feedView.update();
                FeedUpdateUtils.refresh(feedId, FeedActions.DNET_TASK_REFUSE);
            }
        });
    }

    static void redoTask(Context context, int feedId, int executorId, IFeedView feedView) {
        if (!NetUtils.checkNet(context)) {
            ToastUtils.netErrShow();
            return;
        }
        FeedProgressUtils.hide((Activity) context);
        WebApiExecutionCallback<Integer> callback = new WebApiExecutionCallback<Integer>() {
            @Override
            public TypeReference<WebApiResponse<Integer>> getTypeReference() {
                return new TypeReference<WebApiResponse<Integer>>() {
                };
            }
            @Override
            public ISandboxContext getSandboxContext() {
                return SandboxContextManager.getInstance().getContext(SandboxUtils.getActivityByContext(context));
            }
            @Override
            public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                FeedProgressUtils.hide((Activity) context);
                FxCrmUtils.showToast(failureType, httpStatusCode, error);
            }

            @Override
            public void completed(Date time, Integer response) {
                FeedProgressUtils.hide((Activity) context);
                ToastUtils.showToast(I18NHelper.getText("xt.x_feed_detail_activity.text.operation_succeed")/* 操作成功! */);
                feedView.update();
                FeedUpdateUtils.refresh(feedId, FeedActions.DNET_TASK_REDO);
            }
        };
        FeedTaskWebService.ExecuterRedoTask(feedId, executorId, "", null, callback);

    }

}
