package com.facishare.fs.biz_feed.newfeed.api.bean;

import com.facishare.fs.biz_feed.bean.FilterTab;
import com.facishare.fs.biz_feed.bean.GetFeedsResponse;
import com.facishare.fs.biz_feed.bean.PlanItem;
import com.facishare.fs.biz_feed.bean.TabItem;
import com.facishare.fs.biz_feed.bean.TodayPlan;
import com.facishare.fs.biz_feed.bean.WorkTodo;
import com.facishare.fs.i18n.I18NHelper;
import com.fs.beans.beans.AnnouncementEntity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class GetHeadInfoResult implements Serializable {

    public List<BulletinBoard> myBulletin; //我的公告

    public ScheduleBoard mySchedule;//我的日程

    public Todo todo;//待处理的工作

    public FilterTabs filterTabs;//筛选的 tab

    public static class BulletinBoard implements Serializable {
        public int feedId;

        public String text;//公告内容

        public long createTime;// 创建时间
    }

    public static class ScheduleBoard implements Serializable {
        public String title;//标题

        public int count;//总共的待办日程

        public String text;//没有 item 的时候显示的文本

        public String timeStr;//今天日期

        public List<ScheduleItem> items;//显示的日程数组
    }

    public static class ScheduleItem implements Serializable {
        public String tag;//日程图标

        public String text;//日程文本

        public String period;//日程的区间
    }

    public static class Todo implements Serializable {
        public List<TodoItem> items;
    }

    public static class TodoItem implements Serializable {
        public int unreadCount; //待办的未读飘数

        public String name; //待办标签的名称

        public int waitingCount; //等待处理白底数字

        public int todoId; //待办标签的唯一 id,1审批，2 日志，3 任务，4 指令
    }

    public static class FilterTabs implements Serializable {
        public List<FilterTabItem> items;
    }

    public static class FilterTabItem implements Serializable {
        public int feedType; //分页请求的参数

        public String tabText; //分页标签的名字

        public int tabId; //分页标签的 id，全部-1，我发出的-2，部门发出的-3，部门收到的-4，星标同事-5，分享-6，日志-7，审批-8，任务-9，指令-10，日程-11，外勤 12，公告-13，下属工作-14，汇报对象-15，我的观察-16，我关注的-17

        public int tabOrder; //终端显示排序

        public int filterType; //提交时的 filterType

        public int showType;// 1 表示显示在外，0 表示该标签放置在更多的页面内
    }

    /**
     * 从旧Feed对象转换为新Feed对象
     */
    public static GetHeadInfoResult parseFromOld(GetFeedsResponse getFeedsResponse) {
        if (getFeedsResponse == null) {
            return null;
        }
        GetHeadInfoResult result = new GetHeadInfoResult();
        if (getFeedsResponse.shownAnnouncements != null) {
            result.myBulletin = parseFromOld(getFeedsResponse.shownAnnouncements);
        }
        if (getFeedsResponse.todayPlan != null) {
            result.mySchedule = parseFromOld(getFeedsResponse.todayPlan);
        }
        if (getFeedsResponse.workTodo != null) {
            result.todo = parseFromOld(getFeedsResponse.workTodo);
        }
        if (getFeedsResponse.filterTab != null) {
            result.filterTabs = parseFromOld(getFeedsResponse.filterTab);
        }
        return result;
    }

    public static List<BulletinBoard> parseFromOld(Map<Integer, List<AnnouncementEntity>> shownAnnouncements) {
        if (shownAnnouncements == null) {
            return null;
        }
        List<BulletinBoard> bulletinBoards = new ArrayList<>(shownAnnouncements.size());
        for (List<AnnouncementEntity> announcements : shownAnnouncements.values()) {
            for (AnnouncementEntity announcement : announcements) {
                BulletinBoard bulletinBoard = new BulletinBoard();
                bulletinBoard.feedId = announcement.feedID;
                bulletinBoard.text = announcement.title;
                bulletinBoard.createTime = announcement.createTime.getTime();
                bulletinBoards.add(bulletinBoard);
            }
        }
        return bulletinBoards;
    }

    public static ScheduleBoard parseFromOld(TodayPlan todayPlan) {
        if (todayPlan == null) {
            return null;
        }
        ScheduleBoard scheduleBoard = new ScheduleBoard();
        scheduleBoard.title = I18NHelper.getText("xt.work_home_head.des.calendar"/* 我的日程 */);
        scheduleBoard.count = todayPlan.planCount;
        scheduleBoard.text = todayPlan.prompt;
        scheduleBoard.timeStr = todayPlan.serverTime;
        if (todayPlan.items != null) {
            List<ScheduleItem> scheduleItems = new ArrayList<>(todayPlan.items.size());
            for (PlanItem planItem : todayPlan.items) {
                ScheduleItem scheduleItem = new ScheduleItem();
                scheduleItem.tag = planItem.name;
                scheduleItem.text = planItem.detail;
                scheduleItem.period = planItem.time;
                scheduleItems.add(scheduleItem);
            }
            scheduleBoard.items = scheduleItems;
        }
        return scheduleBoard;
    }

    public static Todo parseFromOld(WorkTodo workTodo) {
        if (workTodo == null) {
            return null;
        }
        Todo todo = new Todo();
        if (workTodo.items != null) {
            List<TodoItem> todoItems = new ArrayList<>(workTodo.items.size());
            for (com.facishare.fs.biz_feed.bean.TodoItem _todoItem : workTodo.items) {
                TodoItem todoItem = new TodoItem();
                todoItem.todoId = _todoItem.type;
                todoItem.name = _todoItem.title;
                todoItem.waitingCount = _todoItem.count;
                todoItem.unreadCount = _todoItem.unreadCount;
                todoItems.add(todoItem);
            }
            todo.items = todoItems;
        }
        return todo;
    }

    public static FilterTabs parseFromOld(FilterTab filterTab) {
        if (filterTab == null) {
            return null;
        }
        FilterTabs tabs = new FilterTabs();
        if (filterTab.items != null) {
            List<FilterTabItem> filterTabItems = new ArrayList<>(filterTab.items.size());
            for (TabItem tabItem : filterTab.items) {
                FilterTabItem filterTabItem = new FilterTabItem();
                filterTabItem.feedType = tabItem.feedType;
                filterTabItem.tabText = tabItem.tabText;
                filterTabItem.tabId = tabItem.tabId;
                filterTabItem.tabOrder = tabItem.tabOrder;
                filterTabItem.filterType = tabItem.filterType;
                filterTabItem.showType = tabItem.tabType;
                filterTabItems.add(filterTabItem);
            }
            tabs.items = filterTabItems;
        }
        return tabs;
    }

}
