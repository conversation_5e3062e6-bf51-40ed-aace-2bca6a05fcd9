/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_feed.newfeed.tick;

import com.facishare.fs.biz_feed.FeedDetailActivity;
import com.facishare.fs.biz_feed.newfeed.IFeedView;
import com.facishare.fs.biz_feed.newfeed.IFeedViewAppendData;
import com.facishare.fs.biz_feed.newfeed.action.ActionData;
import com.facishare.fs.biz_feed.newfeed.cmpt.ActionButton;
import com.fxiaoke.fscommon.util.TickUtils;
import com.fxiaoke.stat_engine.beans.TickMode;
import com.fxiaoke.stat_engine.biztick.CrmBizTick;
import com.fxiaoke.stat_engine.events.BizLogEvent;
import com.fxiaoke.stat_engine.events.StatEvent;

public class FeedTickUtils {
    public static void tickCrmInfoListPV() {
        tickPV(FeedTickEvent.FEEDLIST_CRMFEEDLIST_VIEW, null, null, null, null);
    }
    public static void tickPV(String eventID, String extraData) {
        tickPV(eventID, extraData, null, null, null);
    }

    public static void tickPV(String eventID, Object extra0, Object extra1, Object extra2, Object extra3) {
        BizLogEvent logEvent = feedEvent(eventID);
        TickUtils.changeEventDataByEventId(logEvent, eventID);
        TickUtils.addExtraInfo(logEvent, extra0, extra1, extra2, extra3);
        logEvent.eventType(BizLogEvent.EventType.PV);
        logEvent.tick();
    }

    public static void tick(String eventID, String extra0, String extra1) {
        tick(eventID, extra0, extra1, null, null);
    }

    public static void tick(String eventID, String extra0, String extra1, String extra2, String extra3) {
        BizLogEvent logEvent = feedEvent(eventID);
        TickUtils.changeEventDataByEventId(logEvent, eventID);
        TickUtils.addExtraInfo(logEvent, extra0, extra1, extra2, extra3);
        logEvent.eventType(BizLogEvent.EventType.CL);
        logEvent.tick();
    }

    private static BizLogEvent feedEvent(String eventID) {
        // 默认先缓存后，WIFI网络下上传
        return StatEvent.bizEvent(CrmBizTick.ACTION_ID, eventID).tickMode(TickMode.WIFI).biz("FS-Social");
    }

    public static void tickActionButton(ActionData actionData, ActionButton button) {
        if (actionData != null ) {
            IFeedView feedView = actionData.feedView;
            String feedType = null;
            if (feedView.getFeedVo() != null) {
                feedType = feedView.getFeedVo().feedType + "";
            }
            String filterType =  null;
            if(actionData.feedView instanceof IFeedViewAppendData){
                filterType = ((IFeedViewAppendData) feedView).getFilterType()+"";
            }
            String action = button.action;
            String isFeedDetail;//1表示是当前页面是feed详情页面,0表示其他，暂不允许其他值；
            if (feedView.getActivity() instanceof FeedDetailActivity) {
                isFeedDetail = "1";
            } else {
                isFeedDetail = "0";
            }
            FeedTickUtils.tick(FeedTickEvent.FEEDITEM_ACTIONBUTTON_CLICK, feedType, filterType, action, isFeedDetail);
        }
    }
}
