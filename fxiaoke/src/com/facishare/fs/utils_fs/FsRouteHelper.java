package com.facishare.fs.utils_fs;

import com.alibaba.fastjson.JSON;
import com.facishare.fs.common_utils.Triple;
import com.facishare.fs.dialogs.LoadingProDialog;
import com.facishare.fs.i18n.I18NHelper;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;

import com.facishare.fs.biz_session_msg.QixinSpecialRouteInterceptor;
import com.facishare.fs.biz_session_msg.utils.QixinUrlUtils;
import com.facishare.fs.common_utils.JsonHelper;
import com.facishare.fs.common_utils.OpenPlatformUtils;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.ISpecialRouteInterceptor;
import com.fs.fsprobuf.ServerProtobuf;
import com.fxiaoke.fscommon.util.FsUrlUtils;
import com.fxiaoke.fxlog.DebugEvent;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.fxsocketlib.businessctrl.FcpTaskBase;
import com.fxiaoke.lib.qixin.client.impl.GetBizDataClient;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 外部唤起APP的路由Util，目前唤醒方式有AppLink、通知栏点击、Schema
 */
public class FsRouteHelper {

    private static final DebugEvent TAG = new DebugEvent(FsRouteHelper.class.getSimpleName());

    private List<ISpecialRouteInterceptor> mInterceptorList = new LinkedList<>();

    private FsRouteHelper() {
        // 本来想把注册放到maintab，但Index中冷热启动如果出错那么执行不到MainTab，因此放到了这里。
        registerInterceptor(new QixinSpecialRouteInterceptor());
    }

    public FsRouteHelper init() {
        mInterceptorList.clear();
        return this;
    }

    public FsRouteHelper registerInterceptor(ISpecialRouteInterceptor interceptor) {
        mInterceptorList.add(interceptor);
        return this;
    }

    private static final class Holder {
        public static final FsRouteHelper helper = new FsRouteHelper();
    }

    public static final FsRouteHelper getInstance() {
        return Holder.helper;
    }


    /**
     * 根据bizPath和bizData跳转到相应页面
     *
     * @param bizPath
     * @param bizData
     */
    public final boolean gotoAction(final Activity activity, String bizPath, String bizData) {
        bizData = Uri.decode(bizData);
        FCLog.d(TAG, "bizPath:" + bizPath + ",bizData:" + bizData);
        if (TextUtils.isEmpty(bizPath)) {
            return false;
        }
        if ("weex".equalsIgnoreCase(bizPath)) {
            try {
                HashMap<String, Object> map = JsonHelper.toHashMap(bizData);
                String bundle = (String) map.get("url");
                Intent intent = QixinUrlUtils.buildIntent(activity, bundle);
                HostInterfaceManager.getHostInterface().gotoAction(activity, intent);
            } catch (Exception e) {
                FCLog.e(TAG, Log.getStackTraceString(e));
                ToastUtils.show(I18NHelper.getText("xt.fsroutehelper.text.jump_to_weex_failed")/* 跳转至weex失败 */);
                return false;
            }
            return true;
        }
        if ("cml".equalsIgnoreCase(bizPath)) {
            try {
                JSONObject jsonObject = new JSONObject(bizData);
                String url = jsonObject.getString("url");

                String path;
                HashMap<String, Object> params;
                int index = url.indexOf("?");
                if (index == -1) {
                    path = url;
                    params = null;
                } else {
                    path = url.substring(0, index);
                    params = (HashMap) JSON.parseObject(url.substring(index + 1), HashMap.class);
                }
                Intent intent = FsUrlUtils.buildIntent(activity, path, params);
                HostInterfaceManager.getHostInterface().gotoAction(activity, intent);
            } catch (Exception e) {
                FCLog.e(TAG, Log.getStackTraceString(e));
                ToastUtils.show(I18NHelper.getText("xt.fsroutehelper.text.jump_to_cml_failed")/* 跳转至cml失败 */);
                return false;
            }
            return true;
        }
        if ("encrypt".equalsIgnoreCase(bizPath)) {

            final LoadingProDialog dialog = LoadingProDialog.creatLoadingPro(activity);
            dialog.show();
            dialog.setCanceledOnTouchOutside(false);
            dialog.setCancelable(false);
            try {
                HashMap<String, Object> map = JsonHelper.toHashMap(bizData);
                new GetBizDataClient(activity, ServerProtobuf.EnterpriseEnv.INNER, (String) map.get("id")) {
                    @Override
                    public void onSuccess(FcpTaskBase task, final Triple<Boolean, String, String> data) {
                        super.onSuccess(task, data);

                        activity.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (!activity.isFinishing()) {
                                    dialog.dismiss();
                                }
                            }
                        });

                        if (data.first) {
                            activity.runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    ToastUtils.show(I18NHelper.getText("qx.applink.bizdata.expired"));
                                }
                            });
                        } else {
                            gotoAction(activity, data.second, data.third);
                        }
                    }

                    @Override
                    public void onFailed(FcpTaskBase task, Object data) {
                        super.onFailed(task, data);
                        activity.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                dialog.dismiss();
                                ToastUtils.show(I18NHelper.getText("qx.applink.bizdata.decrypt_error"));
                            }
                        });
                    }
                }.execute();
            } catch (Exception e) {
                FCLog.e(TAG, Log.getStackTraceString(e));
                return false;
            }

            return true;
        }
        for (ISpecialRouteInterceptor interceptor : mInterceptorList) {
            if (interceptor.intecept(bizPath)) {
                FCLog.i(TAG, "use interceptor:" + interceptor.getClass().getSimpleName());
                interceptor.doSpecialRoute(activity, bizPath, bizData);
                return true;
            }
        }

        String action = OpenPlatformUtils.ACTION + bizPath.replace("/", "_");
        Intent intent = new Intent();
        intent.setPackage(activity.getPackageName());
        intent.setAction(action);
        intent.putExtra("bizData", bizData);

        boolean isSuccessStart = HostInterfaceManager.getHostInterface().gotoAction(activity, intent);
        if (!isSuccessStart) {
            String targetUrl = bizData;
            if ("fslink".equalsIgnoreCase(bizPath)) {
                try {
                    JSONObject jsonObject = new JSONObject(bizData);
                    targetUrl = jsonObject.getString("url");
                } catch (JSONException e) {
                    FCLog.e(TAG, Log.getStackTraceString(e));
                    return false;
                }
            }
            return FsUrlUtils.gotoAction(activity, targetUrl);// eg "fs://appserver/kaoqin/getRuleList"
        } else {
            return true;
        }
    }
}
