package com.facishare.fs.utils_fs;

import com.facishare.fs.i18n.I18NHelper;
import com.fxiaoke.fxlog.FCLog;

import android.content.Context;
import android.telephony.TelephonyManager;
import android.util.Log;

/**
 * class name：SIMCardInfo<BR>
 * class description：读取Sim卡信息<BR>
 * PS： 必须在加入各种权限 <BR>
 * Date:2012-3-12<BR>
 * 
 * @version 1.00
 * <AUTHOR>
 */
public class SIMCardInfo {
	/**
	 * TelephonyManager提供设备上获取通讯服务信息的入口。 应用程序可以使用这个类方法确定的电信服务商和国家 以及某些类型的用户访问信息。
	 * 应用程序也可以注册一个监听器到电话收状态的变化。不需要直接实例化这个类
	 * 使用Context.getSystemService(Context.TELEPHONY_SERVICE)来获取这个类的实例。
	 */
	private TelephonyManager telephonyManager;
	/**
	 * 国际移动用户识别码
	 */
	private String IMSI;

	public SIMCardInfo(Context context) {
		telephonyManager = (TelephonyManager) context
				.getSystemService(Context.TELEPHONY_SERVICE);
	}

	/**
	 * Role:获取当前设置的电话号码
	 * <BR>Date:2012-3-12
	 * <BR><AUTHOR>
	 */
	public String getNativePhoneNumber() {
		String NativePhoneNumber=null;
		try {
			NativePhoneNumber = telephonyManager.getLine1Number();
		}catch (Exception e){
			FCLog.e("TELEPHONY_SERVICE", Log.getStackTraceString(e));
		}
		return NativePhoneNumber==null?"":NativePhoneNumber;
	}

	/**
	 * Role:Telecom service providers获取手机服务商信息 <BR>
	 * 需要加入权限<uses-permission
	 * android:name="android.permission.READ_PHONE_STATE"/> <BR>
	 * Date:2012-3-12 <BR>
	 * 
	 * <AUTHOR>
	 */
	public String getProvidersName() {
		String ProvidersName = null;
		try {
			// 返回唯一的用户ID;就是这张卡的编号神马的
			IMSI = telephonyManager.getSubscriberId();
			// IMSI号前面3位460是国家，紧接着后面2位00 02是中国移动，01是中国联通，03是中国电信。
			if (IMSI == null) {
				return "";
			} else if (IMSI.startsWith("46000") || IMSI.startsWith("46002")) {
				ProvidersName = I18NHelper.getText("xt.simcardinfo.text.china mobile")/* 中国移动 */;
			} else if (IMSI.startsWith("46001")) {
				ProvidersName = I18NHelper.getText("xt.simcardinfo.text.china unicom")/* 中国联通 */;
			} else if (IMSI.startsWith("46003")) {
				ProvidersName = I18NHelper.getText("xt.simcardinfo.text.china telecom")/* 中国电信 */;
			}
		}catch (Exception e){
			FCLog.e("TELEPHONY_SERVICE", Log.getStackTraceString(e));
		}
		return ProvidersName;
	}
}