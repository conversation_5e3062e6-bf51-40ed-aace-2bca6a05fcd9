package com.facishare.fs.utils_fs;

import java.util.Calendar;
import java.util.Date;

import com.facishare.fs.App;
import com.facishare.fs.biz_session_msg.utils.AccountUtils;
import com.facishare.fs.contacts_fs.beans.EmployeeKey;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.logutils.QXLogUtils;
import com.facishare.fs.pluginapi.Account;
import com.fxiaoke.fscommon.files.PersistentGlobalDataBySP;
import com.fxiaoke.fscommon.util.AccountInfoUtils;

import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Pair;

/**
 * Created by anjx on 2016/2/17.
 */
public class SettingsSP {

    private static final String TAG = SettingsSP.class.getSimpleName();

    /******************************************************************/
    private static final String SP_PUSH_STATE = "PushState";

    private static final String KEY_SOUND = "sound_key";
    private static final String KEY_VIBRATE = "vibrate_key";

    private static  final String KEY_NO_REMIND="no_remind_key"; //设置消息免打扰
    private static final String KEY_NO_REMIND_START_TIME="no_remind_start_time_key"; //设置消息免打扰的开始时间
    private static final String KEY_NO_REMIND_END_TIME="no_remind_end_time_key"; //设置消息免打扰的结束时间

    private static final String KEY_SEND_AUDIO_WITH_TEXT = "send_audio_with_text";
    /******************************************************************/
    private static final String SP_USER_INFO = "UserInfo";

    private static final String KEY_UP_TIME = "up_time";
    private static final String KEY_UP_VERSION = "up_version";
    private static final String KEY_VERSION = "version";
    private static final String KEY_VERSION_NAME = "versionName";
    /******************************************************************/
    private static final String SP_NOW_USER = "nowUser";

    private static final String KEY_RECORDLOG_KEY = "recordlog_key";
    /******************************************************************/
    //键盘高度key（不需要和用户账号信息关联）
    private static final String KEYBORD_HEIGHT_SAVE_KEY = "keybord_height_save_key";
    //获取键盘高度
    private static final String KEYBORD_HEIGHT_KEY = "keybord_height_key";

    private static final String HOME_TYPE_DESCRIPTION = "home_type_description";
    /******************************************************************/
    // 第三方外部员工数据更新日期（7天失效）
    private static final String SP_DATA_UPDATE = "DataUpdate";

    private static final String KEY_THIRD_EMPLOYEE_DATA_UPDATE_TIME = "third_employee_data_update";
    private static final String KEY_QIXIN_WATER_MARK_TEXT = "qixin_water_mark_text";
    private static final String KEY_JS_API_WATER_MARK_STR = "js_api_water_mark_str";
    private static final String KEY_QIXIN_WATER_MARK_Ojb = "qixin_water_mark_Obj";
    /****************************************************************/
    //测试的时候用到的数据
    private static final String SP_DEBUG_INFO = "Debug_Settings";
    //HTTP请求 定位服务到个人ip
    private static final String KEY_HTTP_RESET_SERVICE_IP = "http_reset_service_ip";
    private static final String KEY_HTTP_RESET_SERVICE_URL = "http_reset_service_url";


    public static boolean hasViewedAudio2TextSwitch() {
        SharedPreferences sp =
                App.getInstance().getSharedPreferences(SP_USER_INFO, Activity.MODE_PRIVATE);
        return sp.getBoolean("ViewedAudio2TextSwitch", false);
    }

    public static void setViewedAudio2TextSwitch(boolean entered) {
        SharedPreferences sp =
                App.getInstance().getSharedPreferences(SP_USER_INFO, Activity.MODE_PRIVATE);
        sp.edit().putBoolean("ViewedAudio2TextSwitch", entered).commit();
    }


    public static String generateKey(String key) {
        Account account = FSContextManager.getCurUserContext().getAccount();
        return account.getEnterpriseAccount() + "_" + account.getEmployeeId() + "_"
                + key;
    }

    /**
     * 第三方外部员工数据检查日期（由原来的7天改为1天，且支持动态更改间隔时间）
     *
     * @return (needRefresh, init)
     */
    public static Pair<Boolean, Boolean> checkIfThirdEmployeeDataNeedRefresh() {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_DATA_UPDATE, Activity.MODE_PRIVATE);
        long lastUpdateTime = sp.getLong(getThirdEmployeeDataUpdateKey(), -1);
        long curTime = System.currentTimeMillis();
        boolean firstTime = lastUpdateTime < 0;
        if (firstTime) {
            return new Pair<>(true, true);
        }
//        Calendar lastCalendar = Calendar.getInstance();
//        lastCalendar.setTime(new Date(lastUpdateTime));
//        Calendar curCalendar = Calendar.getInstance();
//        curCalendar.setTime(new Date(curTime));
//
//        lastCalendar.add(Calendar.DATE, 7);
//        if (lastCalendar.before(curCalendar)) {
//            return new Pair<>(true, false);
//        }
        String timeStr = AccountInfoUtils.parseMapConfigStringValue("valid_data_time_interval_875",
                "value", "{\"value\":********}");
        long resultTimeInterval = 0;
        if (!TextUtils.isEmpty(timeStr)) {
            try {
                resultTimeInterval = Long.parseLong(timeStr);
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }
        if (resultTimeInterval <= 0) {//未设置时，认为超过20小时未更新，就是无效数据了
            resultTimeInterval = 24 * 60 * 60 * 1000;//********
        }
        if (curTime - lastUpdateTime > resultTimeInterval) {
            return new Pair<>(true, false);
        }
        return new Pair<>(false, false);
    }

    public static void putThirdEmployeeDataUpdateTime(long updateTime) {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_DATA_UPDATE, Activity.MODE_PRIVATE);
        sp.edit().putLong(getThirdEmployeeDataUpdateKey(), updateTime).commit();
    }

    private static String getThirdEmployeeDataUpdateKey() {
        EmployeeKey employeeKey = AccountUtils.getMyInfo();
        return KEY_THIRD_EMPLOYEE_DATA_UPDATE_TIME + "-" + employeeKey.enterpriseAccount + "-" + employeeKey.employeeId;
    }

    public static void saveQixinWaterMarkText(String waterMarkText) {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_DATA_UPDATE, Activity.MODE_PRIVATE);
        if (waterMarkText == null) {
            sp.edit().remove(getQixinWaterMarkTextKey()).commit();
        } else {
            sp.edit().putString(getQixinWaterMarkTextKey(), waterMarkText).commit();
        }
    }

    public static String getQixinWaterMarkText() {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_DATA_UPDATE, Activity.MODE_PRIVATE);
        return sp.getString(getQixinWaterMarkTextKey(), null);
    }

    private static String getQixinWaterMarkTextKey() {
        EmployeeKey employeeKey = AccountUtils.getMyInfo();
        return KEY_QIXIN_WATER_MARK_TEXT + "-" + employeeKey.enterpriseAccount + "-" + employeeKey.employeeId;
    }

    public static void saveQixinWaterMarkObjJson(String waterMarkObjJson) {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_DATA_UPDATE, Activity.MODE_PRIVATE);
        if (waterMarkObjJson == null) {
            sp.edit().remove(getQixinWaterMarkObjectKey()).commit();
        } else {
            sp.edit().putString(getQixinWaterMarkObjectKey(), waterMarkObjJson).commit();
        }
    }

    public static String getQixinWaterMarkObjJsonString() {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_DATA_UPDATE, Activity.MODE_PRIVATE);
        return sp.getString(getQixinWaterMarkObjectKey(), null);
    }

    private static String getQixinWaterMarkObjectKey() {
        EmployeeKey employeeKey = AccountUtils.getMyInfo();
        return KEY_QIXIN_WATER_MARK_Ojb + "-" + employeeKey.enterpriseAccount + "-" + employeeKey.employeeId;
    }

    /**
     * 缓存jsApi中水印
     * */
    public static void saveWaterMarkStr(String waterMarkStr) {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_DATA_UPDATE, Activity.MODE_PRIVATE);
        if (waterMarkStr == null) {
            sp.edit().remove(getWaterMarkStrKey()).commit();
        } else {
            sp.edit().putString(getWaterMarkStrKey(), waterMarkStr).commit();
        }
    }

    /**
     * 获取缓存的jsApi调用的水印
     * */
    public static String getWaterMarkStr() {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_DATA_UPDATE, Activity.MODE_PRIVATE);
        return sp.getString(getWaterMarkStrKey(), null);
    }

    private static String getWaterMarkStrKey() {
        EmployeeKey employeeKey = AccountUtils.getMyInfo();
        return KEY_JS_API_WATER_MARK_STR + "-" + employeeKey.enterpriseAccount + "-" + employeeKey.employeeId;
    }

    /**
     * 保存是否消息免打扰
     * @param noRemind
     */
    public static void setNORemind(boolean noRemind) {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_PUSH_STATE, Activity.MODE_PRIVATE);
        sp.edit().putBoolean(KEY_NO_REMIND, noRemind).commit();
    }

    /**
     * 获取保存的消息免打扰配置
     * @return
     */
    public static boolean isNORemind() {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_PUSH_STATE, Activity.MODE_PRIVATE);
        return sp.getBoolean(KEY_NO_REMIND, false);
    }

    /**
     * 保存是否消息免打扰开始时间
     * @param startTime
     */
    public static void setNORemindStartTime(String startTime) {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_PUSH_STATE, Activity.MODE_PRIVATE);
        sp.edit().putString(KEY_NO_REMIND_START_TIME, startTime).commit();
    }

    /**
     * 获取保存的消息免打扰开始时间
     * @return
     */
    public static String getNORemindStartTime() {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_PUSH_STATE, Activity.MODE_PRIVATE);
        return sp.getString(KEY_NO_REMIND_START_TIME, null);
    }

    /**
     * 保存是否消息免打扰结束时间
     * @param endTime
     */
    public static void setNORemindEndTime(String endTime) {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_PUSH_STATE, Activity.MODE_PRIVATE);
        sp.edit().putString(KEY_NO_REMIND_END_TIME, endTime).commit();
    }

    /**
     * 获取保存的消息免打扰结束时间
     * @return
     */
    public static String getNORemindEndTime() {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_PUSH_STATE, Activity.MODE_PRIVATE);
        return sp.getString(KEY_NO_REMIND_END_TIME, null);
    }


    public static void setSound(boolean sound) {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_PUSH_STATE, Activity.MODE_PRIVATE);
        sp.edit().putBoolean(KEY_SOUND, sound).commit();
    }

    public static boolean isSound() {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_PUSH_STATE, Activity.MODE_PRIVATE);
        return sp.getBoolean(KEY_SOUND, true);
    }

    public static void setVibrate(boolean sound) {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_PUSH_STATE, Activity.MODE_PRIVATE);
        sp.edit().putBoolean(KEY_VIBRATE, sound).commit();
    }

    public static boolean isVibrate() {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_PUSH_STATE, Activity.MODE_PRIVATE);
        return sp.getBoolean(KEY_VIBRATE, true);
    }

    public static void setSendAudioWithText(boolean sendAudioWithText) {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_PUSH_STATE, Activity.MODE_PRIVATE);
        sp.edit().putBoolean(KEY_SEND_AUDIO_WITH_TEXT, sendAudioWithText).commit();
    }

    public static boolean isSendAudioWithText() {
        if(PersistentGlobalDataBySP.isOverseas()){
            return false;
        }
        if(!AccountInfoUtils.canUseIFlytek()){
            return false;
        }
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_PUSH_STATE, Activity.MODE_PRIVATE);
        boolean isSendAudioWithText = sp.getBoolean(KEY_SEND_AUDIO_WITH_TEXT, false);
        QXLogUtils.tLog("Audio2Text isSendAudioWithText return  "+isSendAudioWithText);
        return isSendAudioWithText;
    }

    /**
     * 保存客户端版本号
     */
    public static void saveVersionCode(int versioncode) {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_USER_INFO, Activity.MODE_PRIVATE);
        sp.edit().putInt(KEY_VERSION, versioncode).commit();
    }

    /**
     * 获取客户端版本号
     * 可能不准确
     *
     * @see App#versionCode
     */
    @Deprecated
    public static int getVersionCode() {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_USER_INFO, Activity.MODE_PRIVATE);
        return sp.getInt(KEY_VERSION, 0);
    }


    public static void saveKeybordHeight(int height) {
        SharedPreferences oldSp = App.getInstance().getSharedPreferences(generateKey("keybord"), Activity
                .MODE_PRIVATE);
        if(oldSp!=null&&oldSp.edit()!=null){
            oldSp.edit().clear().commit();
        }
        SharedPreferences sp = App.getInstance().getSharedPreferences(KEYBORD_HEIGHT_SAVE_KEY, Activity
                .MODE_PRIVATE);
        sp.edit().putInt(KEYBORD_HEIGHT_KEY, height).commit();
    }

    public static int getKeybordHeight() {
        SharedPreferences sp = App.getInstance().getSharedPreferences(KEYBORD_HEIGHT_SAVE_KEY, Activity
                .MODE_PRIVATE);
        return sp.getInt(KEYBORD_HEIGHT_KEY, 0);
    }

    //保存获取到的最近服务端版本信息（时间戳作为版本标识）时用，仅用于区分crm状态变化通知是否重复，不可他用
    public static void putLatestServerEdition(long timeInHour) {
        Account account = FSContextManager.getCurUserContext().getAccount();
        String service = account.getEnterpriseAccount();
        String myID = account.getEmployeeId();
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_USER_INFO, Activity.MODE_PRIVATE);
        if (!TextUtils.isEmpty(service) && !TextUtils.isEmpty(myID)) {
            sp.edit()
                    .putLong(service + "_" + myID + "_latest_server_edition", timeInHour)
                    .commit();
        }
        sp.edit().putLong("latest_server_edition", timeInHour).commit();
    }

    //保存获取到的最近服务端版本信息（时间戳作为版本标识），仅用于区分crm状态变化通知是否重复，不可他用
    public static long getLatestServerEdition() {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_USER_INFO, Activity.MODE_PRIVATE);
        Account account = FSContextManager.getCurUserContext().getAccount();
        String service = account.getEnterpriseAccount();
        String myID = account.getEmployeeId();
        if (!TextUtils.isEmpty(service) && !TextUtils.isEmpty(myID)) {
            return sp.getLong(service + "_" + myID + "_latest_server_edition", 0);
        }
        return sp.getLong("latest_server_edition", 0);
    }

    //保存上次获取版本信息时间
    public static void putLastObtainEditionDataTime(long timeInHour) {
        Account account = FSContextManager.getCurUserContext().getAccount();
        String service = account.getEnterpriseAccount();
        String myID = account.getEmployeeId();
        putLastObtainEditionDataTime(timeInHour, service, myID);
    }

    //保存上次获取版本信息时间
    public static void putLastObtainEditionDataTime(long timeInHour, String service, String myID) {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_USER_INFO, Activity.MODE_PRIVATE);
        if (!TextUtils.isEmpty(service) && !TextUtils.isEmpty(myID)) {
            sp.edit()
                    .putLong(service + "_" + myID + "_last_obtain_edition_data_time", timeInHour)
                    .commit();
        }
        sp.edit().putLong("last_obtain_edition_data_time", timeInHour).commit();
    }

    public static long getLastObtainEditionDataTime() {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_USER_INFO, Activity.MODE_PRIVATE);
        Account account = FSContextManager.getCurUserContext().getAccount();
        String service = account.getEnterpriseAccount();
        String myID = account.getEmployeeId();
        if (!TextUtils.isEmpty(service) && !TextUtils.isEmpty(myID)) {
            return sp.getLong(service + "_" + myID + "_last_obtain_edition_data_time", 0);
        }
        return sp.getLong("last_obtain_edition_data_time", 0);
    }

    public static void putHomeTypeDescription(String str) {
        SharedPreferences sp =
                App.getInstance().getSharedPreferences(HOME_TYPE_DESCRIPTION, Context.MODE_APPEND);
        sp.edit().putString(HOME_TYPE_DESCRIPTION, str).commit();
    }

    public static String getHomeTypeDescription() {
        SharedPreferences sp =
                App.getInstance().getSharedPreferences(HOME_TYPE_DESCRIPTION, Activity.MODE_PRIVATE);
        return sp.getString(HOME_TYPE_DESCRIPTION, null);
    }
	
    /********************************************************************************/
    //和crm相关的一些全局配置
    private static final String CRM_GLOBAL_SETTING = "crm_global_setting";
    //具体的crm业务配置
    private static final String CRM_AUTO_SEND_CM = "crm_auto_send_cm";

    public static boolean isAutoSendCustomer() {
        SharedPreferences sp =
                App.getInstance().getSharedPreferences(CRM_GLOBAL_SETTING, Activity.MODE_PRIVATE);
        return sp.getBoolean(CRM_AUTO_SEND_CM, false);
    }

    public static void setAutoSendCustomer(boolean isAutoSend) {
        SharedPreferences sp =
                App.getInstance().getSharedPreferences(CRM_GLOBAL_SETTING, Activity.MODE_PRIVATE);
        sp.edit().putBoolean(CRM_AUTO_SEND_CM, isAutoSend).commit();
    }

    /******************************************************************/
	public static void saveHttpResetServiceIp(String ip){
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_DEBUG_INFO, Activity.MODE_PRIVATE);
        sp.edit().putString(KEY_HTTP_RESET_SERVICE_IP, ip).apply();
    }

    public static String getHttpResetServiceIp() {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_DEBUG_INFO, Activity.MODE_PRIVATE);
        return sp.getString(KEY_HTTP_RESET_SERVICE_IP, null);
    }

    public static void saveHttpResetServiceUrl(String ip){
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_DEBUG_INFO, Activity.MODE_PRIVATE);
        sp.edit().putString(KEY_HTTP_RESET_SERVICE_URL, ip).apply();
    }

    public static String getHttpResetServiceUrl() {
        SharedPreferences sp = App.getInstance().getSharedPreferences(SP_DEBUG_INFO, Activity.MODE_PRIVATE);
        return sp.getString(KEY_HTTP_RESET_SERVICE_URL, null);
    }
	/******************************************************************/

	/**anjx 记录系统信息****/
    private static final String DEVICE_TICK_INFO = "device_tick_version";

    public static void saveDeviceTickVersion(int version) {
        SharedPreferences sp =
                App.getInstance().getSharedPreferences(SP_USER_INFO, Activity.MODE_PRIVATE);
        sp.edit().putInt(DEVICE_TICK_INFO, version).commit();
    }

    public static int getDeviceTickVersion() {
        SharedPreferences sp =
                App.getInstance().getSharedPreferences(SP_USER_INFO, Activity.MODE_PRIVATE);
        return sp.getInt(DEVICE_TICK_INFO, 0);
    }
    /**anjx 记录系统信息 end****/

    public static final String enableTickSpaceUsage = "enableTickSpaceUsage";//是否上报app 占用空间，值不会空，并发生变化上报
    public static void saveEnableTickSpaceUsage(boolean b) {
        SharedPreferences sp =
                App.getInstance().getSharedPreferences(SP_USER_INFO, Activity.MODE_PRIVATE);
        sp.edit().putBoolean(enableTickSpaceUsage, b).apply();
    }

    public static boolean getEnableSpaceUsage() {
        SharedPreferences sp =
                App.getInstance().getSharedPreferences(SP_USER_INFO, Activity.MODE_PRIVATE);
        return sp.getBoolean(enableTickSpaceUsage, false);
    }
}
