package com.facishare.fs.ava;


import android.content.Context;
import android.os.Environment;

import com.facishare.fs.utils_fs.FsSdcardUtils;
import com.weidian.lib.hera.utils.AvaFileAdapter;

import java.io.File;


public class FsAvaFileAdapter extends AvaFileAdapter {

    @Override
    public String saveImageToPhotosAlbum(Context context, String tempFilePath) {
        return FsSdcardUtils.savePicturePath(context,tempFilePath);
    }
    @Override
    public String getPictureDir() {
       File dir =  FsSdcardUtils.getPictureDir();
       if(dir!=null){
           return dir.getAbsolutePath();
       }

       return Environment.getExternalStorageDirectory().getAbsolutePath();
    }


}
