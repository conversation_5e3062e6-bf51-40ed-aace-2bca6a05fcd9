package com.facishare.fs.filesdownload_center;


import com.facishare.fs.i18n.I18NHelper;
import com.alibaba.sdk.android.oss.ClientException;
import com.alibaba.sdk.android.oss.OSS;
import com.alibaba.sdk.android.oss.OSSClient;
import com.alibaba.sdk.android.oss.ServiceException;
import com.alibaba.sdk.android.oss.common.auth.OSSCredentialProvider;
import com.alibaba.sdk.android.oss.common.auth.OSSStsTokenCredentialProvider;
import com.alibaba.sdk.android.oss.model.GetObjectRequest;
import com.alibaba.sdk.android.oss.model.GetObjectResult;
import com.alibaba.sdk.android.oss.model.Range;
import com.facishare.fs.App;
import com.facishare.fs.biz_function.subbiz_fsnetdisk.utils.FSNetDiskDataUtil;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.pluginapi.fileserver.download.DownLoadFileInfo;
import com.facishare.fs.pluginapi.fileserver.download.DownloadStatus;
import com.facishare.fs.pluginapi.fileserver.download.FileDownBase;
import com.facishare.fs.pluginapi.fileserver.download.IGetDownloadInfoListener;
import com.facishare.fs.pluginapi.fileserver.download.INetDiskDownFileInterface;
import com.facishare.fs.utils_fs.FsLogUtils;
import com.fxiaoke.dataimpl.fileserver.download.FileDownloadImpl;
import com.fxiaoke.fxlog.FCLog;

import org.apache.http.HttpStatus;
import org.apache.http.conn.ConnectTimeoutException;

import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;

/**
 * Created by wangyp on 2017/1/19.
 */

public class BigFileDownLoadThread extends FileDownBase {

    private String domainString = ".aliyuncs.com";
    private OSS oss;

    private String fname;
    public BigFileDownLoadThread(DownLoadFileInfo fileInfo, INetDiskDownFileInterface callBack)
    {
        super(fileInfo,callBack);

        upData();
    }

    public BigFileDownLoadThread(DownLoadFileInfo fileInfo)
    {
        super(fileInfo);
    }


    public void upData()
    {
        String endpoint = "http://"+mfileinfo.getRegion()+domainString;
        FCLog.i(FileDownloadImpl.debug_big_file_key,"BigFileDownLoadThread url = "+endpoint);
        OSSCredentialProvider credentialProvider = new OSSStsTokenCredentialProvider(mfileinfo.getAccessKeyId(), mfileinfo.getAccessKeySecret(), mfileinfo.getStsToken());
        oss = new OSSClient(App.getInstance(), endpoint, credentialProvider);
        fname = FSContextManager.getCurUserContext().getSDOperator().getUserDownFilePath().getPath() + "/" + mfileinfo.getFilename();
        mfileinfo.setFilePathName(fname);

    }

    @Override
    public void run() {

        GetObjectRequest get = new GetObjectRequest(mfileinfo.getBucket(), mfileinfo.getObjectKey());
        FCLog.i(FsLogUtils.debug_big_file_key,"BigFileDownLoadThread mfileinfo.getCompeleteSize() ="+mfileinfo.getCompeleteSize()+" ,mfileinfo.getFileCount()= "+mfileinfo.getFileCount());
        long tempcount = mfileinfo.getCompeleteSize();

        if (tempcount == 0) {

            get.setRange(new Range(tempcount, mfileinfo.getFileCount()));//从开始到结束
        }else{
            get.setRange(new Range(tempcount, Range.INFINITE));//从开始到结束
        }

        InputStream inputStream = null;
        RandomAccessFile randomAccessFile = null;
        try {
            GetObjectResult getResult = oss.getObject(get);

            if (getResult.getStatusCode() == HttpStatus.SC_OK || getResult.getStatusCode() == HttpStatus.SC_PARTIAL_CONTENT) {

                inputStream = getResult.getObjectContent();
                byte[] buffer = new byte[2048];
                int readsize;
                long downloadCount = 0;
                int notifyFlag = 0;
                int saveFlag = 0;
                randomAccessFile = new RandomAccessFile(fname + DownloadFileCtrler.fkey, "rwd");
                randomAccessFile.seek(mfileinfo.getCompeleteSize());
                while ((readsize = inputStream.read(buffer)) != -1) {
                    randomAccessFile.write(buffer, 0, readsize);
                    downloadCount += readsize;// 时时获取下载到的大小
                    if (notifyFlag++ == 100 ) {
                        mfileinfo.setCompeleteSize(downloadCount+tempcount);
                        onProgress(mfileinfo,downloadCount+tempcount,mfileinfo.getFileCount());
                        notifyFlag = 0;
                    }
                    //每1m保存一次
                    if (saveFlag ++ == 500) {
                        DownloadFileCtrler.getInstance().saveSpData(mfileinfo);
                        saveFlag = 0;
                    }
                    if (getRunState() == DownloadStatus.PAUSE ||
                            getRunState() == DownloadStatus.DELETE||
                            getRunState() == DownloadStatus.FAILED||
                            getRunState() == DownloadStatus.CONTIUNE) {
                        break;
                    }
                }
                if (getRunState() == DownloadStatus.PAUSE||
                        getRunState() == DownloadStatus.DELETE||
                        getRunState() == DownloadStatus.FAILED||
                        getRunState() == DownloadStatus.CONTIUNE) {
                    FCLog.i(FsLogUtils.debug_big_file_key,"BigFileDownLoadThread getRunState() ="+getRunState());
                }else{
                    setRunState(DownloadStatus.FINISH);
                }
            }else{
                faild(mfileinfo,I18NHelper.getText("pay.fragment.common.network_exception")/* 网络异常 */);
//                faild(mfileinfo,"下载错误 http code = "+getResult.getStatusCode() );
            }

        }catch (ClientException e) {
            e.printStackTrace();
            faild(mfileinfo,I18NHelper.getText("pay.fragment.common.network_exception")/* 网络异常 */);
        } catch (ServiceException e) {
            faild(mfileinfo,I18NHelper.getText("xt.bigfiledownloadthread.text.service_exception")/* 服务异常 */);
        }catch (ConnectTimeoutException e){
            faild(mfileinfo,I18NHelper.getText("xt.bigfiledownloadthread.text.network_connection_timeout")/* 网络连接超时 */);
        }catch (IOException e){
            faild(mfileinfo,I18NHelper.getText("pay.fragment.common.network_exception")/* 网络异常 */);
        } catch (Exception e){
            faild(mfileinfo,I18NHelper.getText("xt.bigfiledownloadthread.text.execution_exception")/* 执行异常 */);
        }finally {
            try {
                if ( inputStream != null) {
                    inputStream.close();
                }

                if (randomAccessFile != null)
                {
                    randomAccessFile.close();
                }

            }catch (Exception e)
            {

            }
        }
    }

    @Override
    public void execute() {
        setRunState(DownloadStatus.DOWNLOADING);
        FSNetDiskDataUtil.getdownloadInfoListener(mfileinfo, new IGetDownloadInfoListener() {
            @Override
            public void complete(DownLoadFileInfo info) {
                upData();
                start();
            }

            @Override
            public void failed(String error) {
                mfileinfo.setError(error);
                setRunState(DownloadStatus.FAILED);
            }
        });
    }

    @Override
    public void pasue() {
        super.pasue();
    }
}
