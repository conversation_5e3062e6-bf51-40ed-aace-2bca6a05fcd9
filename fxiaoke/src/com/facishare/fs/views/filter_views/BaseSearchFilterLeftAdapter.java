package com.facishare.fs.views.filter_views;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.facishare.fslib.R;

/**
 * 搜索过滤的LeftAdapter， 可以通过覆盖重写{@link #getContentStr} {@link #getObjectId}
 * 来完成一些特殊的自定义
 * <b>创建时间</b> 2015/5/11
 *
 * <AUTHOR>
 */
public abstract class BaseSearchFilterLeftAdapter<T, ChildObject> extends BaseAdapter{
    protected Context mContext;
    protected List<T> objects;
    protected String selectedId;
    public Map<String, Integer> selectedCountMap = new HashMap<String, Integer>();

    public BaseSearchFilterLeftAdapter(Context context) {
        this(context, null);
    }

    public BaseSearchFilterLeftAdapter(Context context, List<T> objects) {
        this.mContext = context;
        this.objects = objects;
    }

    public void setObjects(List<T> objects) {
        this.objects = objects;
    }

    public void setSelectedPosition(int position) {
        this.selectedId = getObjectId(position);
    }
    @Override
    public int getCount() {
        return objects == null ? 0 : objects.size();
    }

    @Override
    public T getItem(int position) {
        return objects.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    /**
     * 获取显示内容 默认Object的toString，可以覆盖重写该方法实现定制
     * @param position 位置
     * @return 显示的内容字符串
     */
    protected abstract String getContentStr(int position);


    /**
     * 获取ObjectId 默认为getItemId
     * @param position 位置
     * @return id的字符串
     */
    public abstract String getObjectId(int position);

    /**
     * 返回position位置选中的数量
     * @param position 位置
     * @return 选中的数量
     */
    public int getSelectedCount(int position) {
        return getSelectedCount(getObjectId(position));
    }
    public int getSelectedCount(String objectId) {
        Integer countInt = selectedCountMap.get(objectId);
        return countInt == null ? 0 : countInt;
    }

    /**
     * 是否被选中
     * @param position 位置
     * @return true 选中 false 否
     */
    public boolean isSelected(int position) {
        return getObjectId(position).equals(selectedId);
    }
    public void updateSelectedCount(String objectId, int count) {
        selectedCountMap.put(objectId, count);
    }

    /**
     * 获取选中的Childs列表
     * @param position 位置
     * @return Childs
     */
    public abstract List<ChildObject> getChildren(int position);
    /**
     * 获取选择的总数
     */
    public int getTotalSelectedCount() {
        int totalCount = 0;
        for (String objectId : selectedCountMap.keySet()) {
            if (containsObject(objectId)) {
                totalCount += getSelectedCount(objectId);
            }
        }
        return totalCount;
    }

    public boolean containsObject(String objectId) {
        for (int i = 0; i < getCount(); i++) {
           if (getObjectId(i).equals(objectId)) return true;
        }
        return false;
    }
    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        Holder holder;
        if (convertView == null) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.left_select_filter_item, null);
            holder = createHolder(convertView);
            convertView.setTag(holder);
        } else {
            holder = (Holder)convertView.getTag();
        }
        holder.textView_content.setText(getContentStr(position));
        updateSelectedCountView(position, holder);

        if (isSelected(position)) {
            convertView.setBackgroundColor(0xffEEEEEE);
            holder.textView_line.setVisibility(View.GONE);
        }else {
            convertView.setBackgroundResource(R.drawable.customer_pop_select);
            holder.textView_line.setVisibility(View.VISIBLE);
        }
        return convertView;
    }

    /**
     * 更新选中的数字的界面
     * @param position 位置
     * @param holder View的holder
     */
    private void updateSelectedCountView(int position, Holder holder) {
        int selectedCount = getSelectedCount(position);
        if (selectedCount > 0) {
            holder.textView_count.setVisibility(View.VISIBLE);
            holder.textView_count.setText(""+selectedCount);
        }else {
            holder.textView_count.setVisibility(View.GONE);
        }
    }
    private Holder createHolder(View convertView) {
        Holder holder = new Holder();
        holder.textView_content = (TextView)convertView.findViewById(R.id.textView_content);
        holder.textView_count = (TextView)convertView.findViewById(R.id.textView_count);
        holder.textView_line = (TextView)convertView.findViewById(R.id.textView_line);
        return holder;
    }

    public class Holder {
        private TextView textView_content;
        private TextView textView_count;
        private TextView textView_line;
    }
}
