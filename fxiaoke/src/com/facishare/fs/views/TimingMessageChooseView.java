package com.facishare.fs.views;



import android.content.Context;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.TextView;

import com.facishare.fslib.R;

public class TimingMessageChooseView extends ChooseView {

	public TimingMessageChooseView(Context context, View layout, String title,
			boolean onlyOne, ChooseCallback mc) {
		super(context, layout, title, onlyOne, mc, false);
	}

	public TimingMessageChooseView(Context context, View layout, String title,
			ChooseCallback mc) {
		super(context, layout, title, mc);
	}

	public TimingMessageChooseView(Context context, View layout, String title,
			int shareType, boolean onlyOne, ChooseCallback mc) {
		super(context, layout, title, shareType, onlyOne, mc, false);
	}

	public interface SimpleChooseCallback {
		public void OnClickListener(View v, TextView txtDisplayAll);
	}

	protected SimpleChooseCallback mSimpleChooseCallback = null;

	/**
	 * 
	 * @param context
	 * @param layout
	 * @param title
	 */
	public TimingMessageChooseView(Context context, View layout, String title,String text,SimpleChooseCallback simpleChooseCallback) {
		super(context, layout, title, -1, true, null, false);
		this.mSimpleChooseCallback = simpleChooseCallback;
		mlinearLayout.setOnClickListener(new OnClickListener() {
			@Override 
			public void onClick(View v) {
				if (mSimpleChooseCallback != null) {
					mSimpleChooseCallback.OnClickListener(v, txtDisplay);
				}
			}
		});
		txtDisplay.setText(text);
		txtDisplay.setVisibility(View.VISIBLE);
		layoutDisplayAll.setVisibility(View.GONE);
		mlinearLayout.setBackgroundResource(R.drawable.invitation_no_semicircle_selector);
//		mlinearLayout.setBackgroundColor(Color.TRANSPARENT);
	}
	
	@Override 
	protected void initView() {
		super.initView();
//		if(App.intScreenWidth>=720){
//			txtDisplay.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
//		}else{
//			txtDisplay.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
//		}
	}

}
