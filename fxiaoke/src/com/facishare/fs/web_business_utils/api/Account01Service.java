package com.facishare.fs.web_business_utils.api;

import com.facishare.fs.contacts_fs.datactrl.AEmployeeData;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiUtils;

public class Account01Service {
	private final static String controller = "Account01";

	/**
	 * 获取指定员工的联系信息
	 * 
	 * @param employeeID
	 *            : int，员工ID
	 */
//	@Deprecated
//	public final void GetEmployeeByID(int employeeID,
//			WebApiExecutionCallback<AEmployeeBaseInfo> callback) {
//		WebApiUtils.getAsync(controller, "GetEmployeeByID", WebApiParameterList
//				.create().with("employeeID", employeeID), callback);
//	}

	/**
	 * 获取全公司所有员工（不含停用）的信息
	 */
//	@Deprecated
//	public final void GetAllNotStopEmployees(
//			WebApiExecutionCallback<List<AEmpSimpleEntity>> callback) {
//		WebApiUtils.getAsync(controller, "GetAllNotStopEmployees", callback);
//	}
	
	/**
	 * 获取员工相关数据（员工、部门及关系集合）。
	 */
	public final static void GetEmployeeData(WebApiExecutionCallback<AEmployeeData> callback){
		WebApiUtils.getAsync(controller, "GetEmployeeData", callback);
	}
}
