
package com.facishare.fs.web_business_utils.api;


import com.facishare.fs.i18n.I18NHelper;
import static android.R.attr.data;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.facishare.fs.biz_function.AttendanceTickHelper;
import com.facishare.fs.biz_function.subbiz_fsnetdisk.api.FSNetDiskApi;
import com.facishare.fs.common_datactrl.draft.BaseVO;
import com.facishare.fs.common_datactrl.draft.ShareVO;
import com.facishare.fs.common_datactrl.draft.draft_fw.DraftType;
import com.facishare.fs.common_datactrl.draft.draft_fw.IDraft;
import com.facishare.fs.memory.FSObservableManager;
import com.facishare.fs.memory.FSObservableManager.Notify;
import com.facishare.fs.memory.FSObservableManager.Progress;
import com.facishare.fs.pluginapi.HostInterface.SendNotifyType;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.common_beans.Attach;
import com.facishare.fs.pluginapi.common_beans.Attach.AttachType;
import com.facishare.fs.utils_fs.FsLogUtils;
import com.facishare.fs.utils_fs.ToolUtils;
import com.fs.beans.beans.EnumDef;
import com.fs.fsprobuf.FSNetDiskProtobuf;
import com.fs.fsprobuf.FSNetDiskProtobuf.ShareFileForFeedResult;
import com.fs.fsprobuf.ServerProtobuf;
import com.fxiaoke.dataimpl.msg.ITaskListener;
import com.fxiaoke.fshttp.web.ParamValue3;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.fxsocketlib.fcp.FcpTempFileUploader;
import com.fxiaoke.fxsocketlib.fcp.api.FcpUploadParam;
import com.fxiaoke.fxsocketlib.fcp.api.IFcpTempFileUploadListener;
import com.fxiaoke.fxsocketlib.fcp.api.IFcpTempFileUploader;
import com.fxiaoke.fxsocketlib.fcp.api.IMiniSandboxContext;
import com.fxiaoke.fxsocketlib.utils.FcpUtils;
import com.fxiaoke.stat_engine.StatEngine;
import com.fxiaoke.stat_engine.statuscode.ErrorType;

public class XFileUploadService extends FileUploadService {
    WebApiFailureType mLastWebApiFailureType = null;
    int mLastHttpStatusCode = 0;
    Notify notify = null;
    Progress pro = null;
    int uploadProgressLength = 0;
    IDraft mBaseVO;
    int p = 0;
    /**
     * 是否显示进度条
     */
    public boolean isShowProgress = true;

    public boolean isSendFailed = false;

    public static int MAX_SIZE = 150 * 1024;

    public XFileUploadService(IDraft baseVO) {
        init();
        mBaseVO = baseVO;
    }

    void init() {
        pro = Progress.getInstance();
        this.notify = new Notify(Notify.SEND_PROGRESS_TYPE, pro);
    }

    /**
     * @param error
     * @return
     */
    public String httpCodeToError(int httpCode, String error) {
        if (httpCode == 0) {
            return I18NHelper.getText("xt.xfileuploadservice.text.your_current_network_is_poor_or_unavailable")/* 您当前的网络不佳或不可用 */;
        }
        return error;
    }

    @Override
    public void uploadEx(final LinkedList<Attach> files,
            final List<ParamValue3<Integer, String, Integer, String>> upLoadingFileList) {

        mLastWebApiFailureType = null;
        mLastHttpStatusCode = 0;
        try {
            int size = files.size();
            for (int i = 0; i < size; i++) {
                if(isSendFailed){
                    break;
                }
                uploadProgressLength += 1;
                notifyProgress();
                // System.out.println("第" + i + "个附件上传开始.....");
                final Attach upfile = files.get(i);
                final boolean isNeedThumbnail=upfile.attachType==EnumDef.FeedAttachmentType.ImageFile.value?true:false;
                // 已经上传成功的附件直接添加到队列里面
                FCLog.i(FsLogUtils.debug_feed_send,"XFileUploadService uploadEx upfile.attachType="+ upfile.attachType);
                if(upfile.attachType==EnumDef.FeedAttachmentType.ForwardAttachFile.value){
                	//yangs add 转发附件

                    ParamValue3<Integer, String, Integer, String> p3 = new ParamValue3<Integer, String, Integer, String>(
                            EnumDef.FeedAttachmentType.ForwardAttachFile.value, upfile.attachLocalPath, upfile.getSize(),
                            upfile.attachName);
                    upLoadingFileList.add(p3);
                	
                }else if (upfile.attachType == EnumDef.FeedAttachmentType.NetDisk.value){
                    // anjx add
                    FCLog.i(FsLogUtils.debug_feed_send,"XFileUploadService uploadEx ForwardAttachFile "+ JSON.toJSON(upLoadingFileList).toString());
                    FSNetDiskApi.shareFile2Feed(upfile.attachLocalPath, new WebApiExecutionCallback<FSNetDiskProtobuf.ShareFileForFeedResult>() {
                        
                        @Override
                        public Class<ShareFileForFeedResult> getTypeReferenceFHE() {
                            return ShareFileForFeedResult.class;
                        }
                        @Override
                        public TypeReference<WebApiResponse<ShareFileForFeedResult>> getTypeReference() {
                            return new TypeReference<WebApiResponse<ShareFileForFeedResult>>() {
                            };
                        }
                        public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                            mLastWebApiFailureType = failureType;
                            mLastHttpStatusCode = httpStatusCode;
                            if (httpStatusCode == 200 && failureType == WebApiFailureType.Failure){
                                throw new RuntimeException(error);
                            }else{
                                throw new RuntimeException(I18NHelper.getText("crm.layout.function_fsnetdisk_uploading_list_item2.7785"));
                            }
                        }
                        
                        @Override
                        public void completed(Date time, ShareFileForFeedResult response) {
                            if (response == null){
                                throw new RuntimeException(I18NHelper.getText("crm.layout.function_fsnetdisk_uploading_list_item2.7785"));
                            }
                            ParamValue3<Integer, String, Integer, String> p3 = new ParamValue3<Integer, String, Integer, String>(
                                    EnumDef.FeedAttachmentType.AttachFile.value, response.getTNPath(), upfile.getSize(),
                                    upfile.attachName);
                            upLoadingFileList.add(p3);
                        }
                    }, true);
                    // anjx add end
                    
                }else if (upfile.attachType == EnumDef.FeedAttachmentType.EnterpriseNetDisk.value){
                    // tianyl add
                    FSNetDiskApi.ShareFileForFeedByNPath(upfile.attachLocalPath, new
                            WebApiExecutionCallback<FSNetDiskProtobuf.ShareFileForFeedByNPathResult>() {

                        @Override
                        public Class<FSNetDiskProtobuf.ShareFileForFeedByNPathResult> getTypeReferenceFHE() {
                            return FSNetDiskProtobuf.ShareFileForFeedByNPathResult.class;
                        }
                        @Override
                        public TypeReference<WebApiResponse<FSNetDiskProtobuf.ShareFileForFeedByNPathResult>>
                        getTypeReference() {
                            return new TypeReference<WebApiResponse<FSNetDiskProtobuf.ShareFileForFeedByNPathResult>>
                                    () {
                            };
                        }
                        public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                            mLastWebApiFailureType = failureType;
                            mLastHttpStatusCode = httpStatusCode;
                            if (httpStatusCode == 200 && failureType == WebApiFailureType.Failure){
                                throw new RuntimeException(error);
                            }else{
                                throw new RuntimeException(I18NHelper.getText("crm.layout.function_fsnetdisk_uploading_list_item2.7785"));
                            }
                        }

                        @Override
                        public void completed(Date time, FSNetDiskProtobuf.ShareFileForFeedByNPathResult response) {
                            if (response == null){
                                throw new RuntimeException(I18NHelper.getText("crm.layout.function_fsnetdisk_uploading_list_item2.7785"));
                            }
                            ParamValue3<Integer, String, Integer, String> p3 = new ParamValue3<Integer, String, Integer, String>(
                                    EnumDef.FeedAttachmentType.AttachFile.value, response.getTNPath(), upfile.getSize(),
                                    upfile.attachName);
                            upLoadingFileList.add(p3);
                        }
                    });
                    // tianyl add end

                } else if (upfile.attachLocalState == AttachType.ATTACH_NETWORK_TYPE) {
                    final File file = new File(upfile.attachLocalPath);
                    uploadProgressLength += file.length();
                    notifyProgress();
                    upLoadingFileList.add(new ParamValue3<Integer, String, Integer, String>(
                            upfile.attachType, upfile.attachPath, upfile.attachSize,
                            upfile.attachName));
                    // System.out.println("------------文件已经上传,不需要上传,直接加入队列-------------->" + i);
                } else if (upfile.attachType == EnumDef.FeedAttachmentType.location.value
                        || upfile.attachType == EnumDef.FeedAttachmentType.SignIn.value) {
                    upLoadingFileList.add(new ParamValue3<Integer, String, Integer, String>(
                            upfile.attachType, upfile.attachLocalPath, 0, null));
                    mBaseVO.saveSelf();
                    // upfile.updateToDB(upfile.attachLocalPath, AttachType.ATTACH_NETWORK_TYPE);
                    // System.out.println("------------定位类型附件,不需要上传,直接加入队列-------------->" + i);
                } else {
                    // System.out.println("------------文件开始上传-------------->" + i);
                    final File file = new File(upfile.attachLocalPath);
                    p = 0;
                    FCLog.e("XFileUploadService", i + "开始上传:" + upfile.attachLocalPath);
                    FCLog.i(FsLogUtils.debug_feed_send,"XFileUploadService 开始上传 upfile.attachLocalPath="+upfile.attachLocalPath);
                    ITaskListener listener = new ITaskListener() {
                        @Override
                        public void onSuccess(Object data) {
                            FCLog.i(FsLogUtils.debug_feed_send,"XFileUploadService onSuccess data="+FsLogUtils.checkNull(data));
                            if (data != null) {
                                String storagePath = (String) data;
                                ParamValue3<Integer, String, Integer, String> p3 = new ParamValue3<Integer, String, Integer, String>(
                                        upfile.attachType, storagePath, upfile.getSize(),
                                        upfile.attachName);
                                // System.out.println("------------未上传的文件,上传完毕-------------->");
                                FCLog.e("XFileUploadService", "上传数量:" + files.size() + "上传完毕:"
                                        + upfile.attachLocalPath);
                                boolean isHave = false;
                                synchronized (upLoadingFileList) {
                                    try {
                                        Iterator<ParamValue3<Integer, String, Integer, String>> it = upLoadingFileList
                                                .iterator();
                                        while (it.hasNext()) {
                                            ParamValue3<Integer, String, Integer, String> pv = it
                                                    .next();
                                            if (pv.value1 != null
                                                    && pv.value1.equalsIgnoreCase(p3.value1)) {
                                                isHave = true;
                                                break;
                                            }
                                        }
                                    } catch (Exception e) {
                                        FCLog.w(FsLogUtils.debug_feed_send,"XFileUploadService uploadEx  上传文件出现异常:" +
                                                e.getMessage()+" file="+upfile.attachLocalPath);
                                        StatEngine.tickEx("FieldLocation_106","client error 3:"+e.getMessage());
                                    }
                                    if (!isHave) {
                                        upLoadingFileList.add(p3);
                                    }
                                }
//                                upfile.updateToDB(storagePath, AttachType.ATTACH_NETWORK_TYPE);
                                upfile.attachLocalState = AttachType.ATTACH_NETWORK_TYPE;
                                upfile.attachPath = storagePath;
                                mBaseVO.saveSelf();
                            }
                            deleteImageFile(upfile);
                        }

                        @Override
                        public void onProgress(Object data, int cur, int total) {
                            int l = (int) (file.length() * ((double) cur / (double) total));
                            uploadProgressLength += l - p;
                            p = l;
                            notifyProgress();
                            FCLog.e("XFileUploadService", "/" + uploadProgressLength + ",上传大小:" + cur + ",总数:"  + total + "," + (file.length() / 1024 + 1) + ","
                                    + ToolUtils.suffix(file.getName()).toLowerCase());
                        }

                        @Override
                        public void onFailed(Object data) {
                            String error = (String) FcpUtils.getFailedReason(data);
                            FCLog.i(FsLogUtils.debug_feed_send,"uploadEx onFailed file="+upfile.attachLocalPath+","+error);
                            StatEngine.tickEx("FieldLocation_101","socket upload error:"+error);
                            mLastWebApiFailureType = WebApiFailureType.SocketError;
                            mLastHttpStatusCode = (int)ErrorType.getFcpFailCode(data);
                            if(data instanceof String) {
                                mLastWebApiFailureType.setExceptionText((String) data);
                            }
                            isSendFailed = true;
//                            throw new RuntimeException(httpCodeToError(0, error));
                        }
                    };

                    IFcpTempFileUploadListener fcpTempFileUploadListener = new IFcpTempFileUploadListener() {
                        @Override
                        public void onSuccess(String storagePath) {
                            FCLog.i(FsLogUtils.debug_feed_send,"XFileUploadService onSuccess data="+FsLogUtils.checkNull(data));
                            if (storagePath != null) {
                                ParamValue3<Integer, String, Integer, String> p3 = new ParamValue3<Integer, String, Integer, String>(
                                        upfile.attachType, storagePath, upfile.getSize(),
                                        upfile.attachName);
                                // System.out.println("------------未上传的文件,上传完毕-------------->");
                                FCLog.e("XFileUploadService", "上传数量:" + files.size() + "上传完毕:"
                                        + upfile.attachLocalPath);
                                boolean isHave = false;
                                synchronized (upLoadingFileList) {
                                    try {
                                        Iterator<ParamValue3<Integer, String, Integer, String>> it = upLoadingFileList
                                                .iterator();
                                        while (it.hasNext()) {
                                            ParamValue3<Integer, String, Integer, String> pv = it
                                                    .next();
                                            if (pv.value1 != null
                                                    && pv.value1.equalsIgnoreCase(p3.value1)) {
                                                isHave = true;
                                                break;
                                            }
                                        }
                                    } catch (Exception e) {
                                        FCLog.w(FsLogUtils.debug_feed_send,"XFileUploadService uploadEx  上传文件出现异常:" +
                                                e.getMessage()+" file="+upfile.attachLocalPath);
                                        StatEngine.tickEx("FieldLocation_106","client error 3:"+e.getMessage());
                                    }
                                    if (!isHave) {
                                        upLoadingFileList.add(p3);
                                    }
                                }
                                //                                upfile.updateToDB(storagePath, AttachType.ATTACH_NETWORK_TYPE);
                                upfile.attachLocalState = AttachType.ATTACH_NETWORK_TYPE;
                                upfile.attachPath = storagePath;
                                mBaseVO.saveSelf();
                            }
                            deleteImageFile(upfile);
                        }

                        @Override
                        public void onFailed(Object o) {
                            String error = (String) FcpUtils.getFailedReason( data);
                            FCLog.i(FsLogUtils.debug_feed_send,"uploadEx onFailed file="+upfile.attachLocalPath+","+error);
                            StatEngine.tickEx("FieldLocation_101","socket upload error:"+error);
                            mLastWebApiFailureType = WebApiFailureType.SocketError;
                            mLastHttpStatusCode = (int)ErrorType.getFcpFailCode(o);
                            if(o instanceof String) {
                                mLastWebApiFailureType.setExceptionText((String) o);
                            }
                            isSendFailed = true;
                        }

                        @Override
                        public void onProgress(FcpUploadParam fcpUploadParam, int cur, int total) {
                            int l = (int) (file.length() * ((double) cur / (double) total));
                            uploadProgressLength += l - p;
                            p = l;
                            notifyProgress();
                            FCLog.e("XFileUploadService", "/" + uploadProgressLength + ",上传大小:" + cur + " ,总数:" + total + "," + (file.length() / 1024 + 1) + ","
                                    + ToolUtils.suffix(file.getName()).toLowerCase());
                        }

                        @Override
                        public IMiniSandboxContext getSandboxContext() {
                            //暂时不处理prm上游问题
                            return null;
                        }
                    };

                    if (mBaseVO.getType() == DraftType.DRAFT_SEND_BAICHUAN_NOTICE_REPLEY) {//业务不再支持
//                        MsgDataControllerBC.getInstace(App.getInstance()).uploadTempFile_sync(
//                                upfile.attachLocalPath,
//                                ToolUtils.suffix(file.getName()).toLowerCase(), listener);
                    } else {
                        IFcpTempFileUploader fcpTempFileUploader = new FcpTempFileUploader(fcpTempFileUploadListener);

                        ServerProtobuf.EnterpriseEnv env;
                        if (mBaseVO.getType() == DraftType.DRAFT_QIXIN_NOTIFY_REPLY) {
                            env = ServerProtobuf.EnterpriseEnv.CROSS;

                        } else {
                            env = ServerProtobuf.EnterpriseEnv.INNER;
                        }
                        fcpTempFileUploader.uploadTempFile_sync(upfile.attachLocalPath,
                                ToolUtils.suffix(file.getName()).toLowerCase(), env,
                                ((BaseVO) mBaseVO).getTag(), isNeedThumbnail, false, 0);
                    }

                }
            }
            if(isSendFailed){
                FCLog.i(FsLogUtils.debug_feed_send,"XFileUploadService uploadEx isSendFailed 上传文件失败!!已中断上传");
                notifyProgressFailed(mLastWebApiFailureType , mLastHttpStatusCode, I18NHelper.getText("xt.xfileuploadservice.text.uploading_a_file_failed")/* 上传文件失败! */);
                return;
            }
            if (mFileUploadCallback != null) {
                if (mBaseVO != null && mBaseVO instanceof BaseVO) {
                    ((BaseVO) mBaseVO).fileInfos = upLoadingFileList;
                }
                mFileUploadCallback.completed(null, upLoadingFileList);
            }
        } catch (Exception e) {
            isSendFailed = true;
            FCLog.i(FsLogUtils.debug_feed_send,"XFileUploadService uploadEx 上传文件失败!!已中断上传 error="+e.getMessage());
            StatEngine.tickEx("FieldLocation_106","client error 2:"+e.getMessage());
            notifyProgressFailed(mLastWebApiFailureType, mLastHttpStatusCode, e.getMessage());
            FCLog.d("上传文件失败!!已中断上传............");
        } finally {
            if (isSendFailed) {
                /**
                 * 增加打点用来统计
                 * 当外勤签到中上传附件失败时的次数
                 */
                if(mBaseVO instanceof ShareVO){
                    BaseVO svo = (BaseVO)mBaseVO;
                    if(svo.lastLocationTime!=0){
                        StatEngine.tickEx(AttendanceTickHelper.FIELDLOCATION_UPLOAD_ATTACH_FAILED);
                    }
                }
            }
        }

    }

    /**
     * 同步压缩图片
     */
    @Override
    public void upload(
            final LinkedList<Attach> files,
            final FileUploadCallback<List<ParamValue3<Integer, String, Integer, String>>> mFileUploadCallback) {
        mLastWebApiFailureType = null;
        mLastHttpStatusCode = 0;
        this.mFileUploadCallback = mFileUploadCallback;
        List<String> fileTempPaths = new ArrayList<String>();
        // int length = 0;
        uploadProgressLength = 1;
        int count = 0;
        for (Attach attach : files) {
            if (attach.attachLocalState == AttachType.ATTACH_NETWORK_TYPE
                    && attach.attachType != EnumDef.FeedAttachmentType.location.value
                    && attach.attachType != EnumDef.FeedAttachmentType.SignIn.value) {
                FCLog.d("草稿箱中临时文件:" + attach.attachPath);
                fileTempPaths.add(attach.attachPath);
            }
            if (attach.attachType != EnumDef.FeedAttachmentType.location.value
                    && attach.attachType != EnumDef.FeedAttachmentType.SignIn.value) {
                count++;
            }
        }
        if (count == 0) {
            pro.max = files.size() + 2;
        } else {
            pro.max = 10;
        }
        notifyProgress();
        FCLog.d("检查临时文件是否存在于服务器");
        FCLog.i(FsLogUtils.debug_feed_send,"XFileUploadService mBaseVO.getType()="+ mBaseVO.getType() +" fileTempPaths="+FsLogUtils.checkNull(fileTempPaths));
        try {
            if (mBaseVO.getType() == DraftType.DRAFT_SEND_BAICHUAN_NOTICE_REPLEY) {
                // if (fileTempPaths != null && fileTempPaths.size() > 0) {
                FCLog.d("开始压缩");
                try {
                    compressImage(files);
                    int length = 0;
                    for (Attach attach : files) {
                        if (attach.attachType != EnumDef.FeedAttachmentType.location.value
                                && attach.attachType != EnumDef.FeedAttachmentType.SignIn.value) {
                            length += new File(attach.attachLocalPath).length();
                        }
                    }
                    if (length != 0) {
                        uploadProgressLength = (int) ((double) length / 8);
                        pro.max = (int) (((double) length / 4) + length + 2);
                    }
                    FCLog.d("开始上传");
                    uploadFiles(files, mFileUploadCallback);
                } catch (Exception e) {
                    e.printStackTrace();
                    notifyProgressFailed(WebApiFailureType.ClientError, 0, e.getMessage());
                    FCLog.d("检查服务器文件失败!!已中断上传............");
                    FCLog.i(FsLogUtils.debug_feed_send,"XFileUploadService error 1="+e.getMessage());
                }
            } else {
                FCLog.i(FsLogUtils.debug_feed_send,"CheckUploadFilesExist start request");
                FCLog.i(FsLogUtils.debug_feed_send,FsLogUtils.checkNull(fileTempPaths));
                FileService.CheckUploadFilesExist(fileTempPaths,
                        new WebApiExecutionCallback<List<String>>() {
                            @Override
                            public TypeReference<WebApiResponse<List<String>>> getTypeReference() {
                                return new TypeReference<WebApiResponse<List<String>>>() {
                                };
                            }

                            @Override
                            public void failed(WebApiFailureType failureType, int httpStatusCode,
                                    String error) {
                                mLastWebApiFailureType = failureType;
                                mLastHttpStatusCode = httpStatusCode;
                                FCLog.i(FsLogUtils.debug_feed_send,"XFileUploadService CheckUploadFilesExist failed error=" +error +" failuretype="+failureType.getIndex());
                                StatEngine.tickEx("FieldLocation_104","CheckUploadFilesExist_failed:"+"WebApiFailureType:"+failureType.description()+",httpStatusCode:"+httpStatusCode+",error:"+error);
                                throw new RuntimeException(httpCodeToError(httpStatusCode, error));
                            }

                            @Override
                            public void completed(Date time, List<String> response) {
                                FCLog.i(FsLogUtils.debug_feed_send,"XFileUploadService CheckUploadFilesExist completed ");
                                FCLog.i(FsLogUtils.debug_feed_send,FsLogUtils.checkNull(response));
                                if (response != null) {
                                    for (Attach attach : files) {
                                        boolean has = false;
                                        for (int i = 0; i < response.size(); i++) {
                                            String path = response.get(i);
                                            if (path != null && attach.attachPath != null
                                                    && path.contains(attach.attachPath)) {
                                                has = true;
                                                FCLog.i(FsLogUtils.debug_feed_send,path+"该文件已经存在,不需要上传!");
                                                break;
                                            }
                                        }
                                        if (!has) {
                                            FCLog.i(FsLogUtils.debug_feed_send,"已不存在的文件:" + attach.attachLocalPath);
//                                            attach.updateToDB(null, AttachType.ATTACH_LOCAL_TYPE);
                                            attach.attachLocalState = AttachType.ATTACH_LOCAL_TYPE;
                                            mBaseVO.saveSelf();
                                        }
                                    }
                                }
                                FCLog.d("开始压缩");
                                try {
                                    compressImage(files);
                                    int length = 0;
                                    for (Attach attach : files) {
                                        if (attach.attachType != EnumDef.FeedAttachmentType.location.value
                                                && attach.attachType != EnumDef.FeedAttachmentType.SignIn.value) {
                                            length += new File(attach.attachLocalPath).length();
                                        }
                                    }
                                    if (length != 0) {
                                        uploadProgressLength = (int) ((double) length / 8);
                                        pro.max = (int) (((double) length / 4) + length + 2);
                                    }
                                } catch (Exception e) {
                                    FCLog.i(FsLogUtils.debug_feed_send,"XFileUploadService error 2="+e.getMessage());
                                    e.printStackTrace();
                                    StatEngine.tickEx("FieldLocation_106","client error 4:"+e.getMessage());
                                    mLastWebApiFailureType = WebApiFailureType.ClientError;
                                    mLastHttpStatusCode= 0;
                                    throw new RuntimeException(e.getMessage());
                                }
                                FCLog.d("开始上传");
                                uploadFiles(files, mFileUploadCallback);
                            }
                        });
            }
        } catch (Exception e) {
            FCLog.i(FsLogUtils.debug_feed_send,"XFileUploadService error 3="+e.getMessage());
            StatEngine.tickEx("FieldLocation_106","client error 1:"+e.getMessage());
            notifyProgressFailed(mLastWebApiFailureType, mLastHttpStatusCode, e.getMessage());
            FCLog.d("检查服务器文件失败!!已中断上传............");
        }
    }

    private void notifyProgress() {
        if (isShowProgress) {
            pro.progress = uploadProgressLength;
            FSObservableManager.getInstance().onChangeSendProgressBarEvent(notify);
        }
    }

    private void notifyProgressFailed(WebApiFailureType failureType, int httpStatusCode, String error) {
        if (mFileUploadCallback != null) {
            mFileUploadCallback.failed(failureType == null ? WebApiFailureType.ClientError : failureType, httpStatusCode, error == null ? I18NHelper.getText("xt.xfileuploadservice.text.uploading_a_file_failed")/* 上传文件失败! */
                    : error);
        }
        HostInterfaceManager.getHostInterface().showSendNotify(SendNotifyType.failed, I18NHelper.getText("crm.fragment.LeadsToCustomerFrag.1235")/* 发送失败 */);
        FSObservableManager.getInstance().onChangeSendProgressFailed();
    }

    public static byte[] copyOfRange(byte[] original, int start, int end) {
        if (start > end) {
            throw new IllegalArgumentException();
        }
        int originalLength = original.length;
        if (start < 0 || start > originalLength) {
            throw new ArrayIndexOutOfBoundsException();
        }
        int resultLength = end - start;
        int copyLength = Math.min(resultLength, originalLength - start);
        byte[] result = new byte[resultLength];
        System.arraycopy(original, start, result, 0, copyLength);
        return result;    }}