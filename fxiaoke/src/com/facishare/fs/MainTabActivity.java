package com.facishare.fs;

import android.Manifest;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.DialogInterface;
import android.content.DialogInterface.OnDismissListener;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.MessageQueue;
import android.os.Parcel;
import android.os.Parcelable;
import android.os.Process;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.View.OnTouchListener;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.LinearLayout.LayoutParams;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TabHost;
import android.widget.TabHost.TabSpec;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.util.Pair;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.maps.MapsInitializer;
import com.amap.api.services.core.ServiceSettings;
import com.baidu.location.LocationClient;
import com.facishare.fs.account_system.ExperienceLoginUtils;
import com.facishare.fs.account_system.LoginUitls;
import com.facishare.fs.account_system.LoginUitls.AccountTypeCallBack;
import com.facishare.fs.account_system.PasslockActivity;
import com.facishare.fs.account_system.ShowGuideMapUtils;
import com.facishare.fs.account_system.ShowPicExActivity;
import com.facishare.fs.account_system.beans.IndustryResultsVo;
import com.facishare.fs.biz_function.appcenter.util.AppCenterSPUtil;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2Utils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scan.ScanUtils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scan.pda.PdaManager;
import com.facishare.fs.biz_personal_info.datactrl.ContactSelectionCustomFieldsHandler;
import com.facishare.fs.biz_session_msg.beans.QixinStatisticsEvent;
import com.facishare.fs.biz_session_msg.subbiz.msg_page.watermark.WatermarkService;
import com.facishare.fs.biz_session_msg.utils.SessionInfoUtils;
import com.facishare.fs.common_utils.cheatrisk.AntiCheatUtils;
import com.facishare.fs.i18n.LayoutUtils;
import com.facishare.fs.main_tab_lis.MainTabLifeListenersManager;
import com.facishare.fs.ui.memory.MemoryFloatWindowManager;
import com.fxiaoke.app.AppCustomManager;
import com.facishare.fs.contacts_fs.public_emp.PublicEmployeesEntryCtr;
import com.facishare.fs.contacts_fs.datactrl.PublicEmployeesEntryPersistentHandlerProxy;
import com.fxiaoke.dataimpl.contacts.ContactConfigProvider;
import com.fxiaoke.avatar.predict.AppStartPredictManager;
import com.fxiaoke.dataimpl.feed.gray.persist.FeedGrayDataProvider;
import com.facishare.fs.js.ava.FsMPActivity;
import com.facishare.fs.common_utils.time.NetworkTimeReceiver;
import com.facishare.fs.utils_fs.MainTabBottomItemUtils;
import com.fxiaoke.dataimpl.msg.SocketDataController;
import com.fxiaoke.fscommon.avatar.prefetchv2.prefetch.prefetchManagerV2;
import com.fxiaoke.fscommon.brandcolor.BrandColorDataHandler;

import com.facishare.fs.account_system.datactr.AccountAttributeNotifyerImpl;
import com.facishare.fs.account_system.datactr.DeviceAuthorizationCtr;
import com.facishare.fs.account_system.datactr.EmployeeEditioinUtils;
import com.facishare.fs.account_system.datactr.IAccountAttributeNotifyer;
import com.facishare.fs.account_system.datactr.IGetIndustriesLis;
import com.facishare.fs.account_system.nps.NpsPageHandler;
import com.facishare.fs.account_system.screenshot.ScreenShotShareViewUtils;
import com.facishare.fs.account_system.webpai.LoginServices;
import com.facishare.fs.account_system.xlogin.SSOLoginProcessor;
import com.facishare.fs.appconfig.AppConfig;
import com.facishare.fs.appconfig.AppConfigConstants;
import com.facishare.fs.appconfig.AppConfigManager;
import com.facishare.fs.appconfig.AppConfigPersist;
import com.facishare.fs.appconfig.AppConfigRenderer;
import com.facishare.fs.appconfig.AppConfigWebUtils;
import com.facishare.fs.biz_feed.bean.HomeActivityTabPopupEvent;
import com.facishare.fs.biz_feed.subbiz_send.BaseFsSendActivity;
import com.facishare.fs.biz_feed.subbiz_send.GridViewPopupWindowFactory;
import com.facishare.fs.biz_feed.subbiz_send.SendApproveCenterActivity;
import com.facishare.fs.biz_feed.subbiz_send.XSendShareActivity;
import com.facishare.fs.biz_feed.subbiz_send.feedsendapi.FeedSenderTaskManger;
import com.facishare.fs.biz_feed.utils.FeedSP;
import com.facishare.fs.biz_feed.work_home.WorkHomeActivity;
import com.facishare.fs.biz_function.FunctionSP;
import com.facishare.fs.biz_function.appcenter.AppCenterActivity;
import com.facishare.fs.biz_function.appcenter.util.AppStatistics;
import com.facishare.fs.biz_function.kwq.KWQTrackLogTool;
import com.facishare.fs.biz_function.service.action.BaseServiceAction;
import com.facishare.fs.biz_function.service.action.InnerServiceAction;
import com.facishare.fs.biz_function.subbiz_fsnetdisk.ui.FSNetDiskSaveActivity;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.appcustom.AppCustomUtils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.OutDoor2CacheManger;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.OutDoorUploaderManager;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.CheckFaceHander;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.outdoor_offline.OfflineUtils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.utils.OutdoorLocationPolling;
import com.facishare.fs.biz_personal_info.DraftActivity;
import com.facishare.fs.biz_personal_info.MyActivity;
import com.facishare.fs.biz_session_msg.ShortMessageMainActivity;
import com.facishare.fs.biz_session_msg.datactrl.LoadALevelContactFinishEvent;
import com.facishare.fs.biz_session_msg.datactrl.SessionUpdateEventCtr;
import com.facishare.fs.biz_session_msg.extra.vipnotice.VipNewNoticeDialog;
import com.facishare.fs.biz_session_msg.extra.vipnotice.VipNoticeLoader;
import com.facishare.fs.biz_session_msg.extra.vipnotice.VipNoticeShowEvent;
import com.facishare.fs.biz_session_msg.filepreview.FilePreviewUtils;
import com.facishare.fs.biz_session_msg.persistent.PersistentBySP;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.utils.CrossFileUtils;
import com.facishare.fs.biz_session_msg.subbiz.msg_page.plus_plugin.PlusDataProvider;
import com.facishare.fs.biz_session_msg.subbiz.scan.QrFeedDetailProcessor;
import com.facishare.fs.biz_session_msg.subbiz.scan.QrScanCmlLinkProcessor;
import com.facishare.fs.biz_session_msg.subbiz.scan.QrScanCrmLinkProcessor;
import com.facishare.fs.biz_session_msg.subbiz.scan.QrScanLoginProcessor;
import com.facishare.fs.biz_session_msg.subbiz.scan.QrScanPayProcessor;
import com.facishare.fs.biz_session_msg.subbiz.scan.QrScanRouteProcessor;
import com.facishare.fs.biz_session_msg.subbiz.scan.QrScanShortAppLinkProcessor;
import com.facishare.fs.biz_session_msg.subbiz.scan.QrScanWeexLinkProcessor;
import com.facishare.fs.biz_session_msg.uipaas.NavigationPaaSStatHandler;
import com.facishare.fs.biz_session_msg.utils.UnreadMsgUtils;
import com.facishare.fs.common_datactrl.LogUtilCommon;
import com.facishare.fs.common_datactrl.draft.ApprovalVO;
import com.facishare.fs.common_datactrl.draft.SenderManager;
import com.facishare.fs.common_datactrl.draft.ShareVO;
import com.facishare.fs.common_datactrl.draft.draft_fw.DraftManager;
import com.facishare.fs.common_utils.CalendarTimeZoneUtils;
import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.common_view.GridViewPopupWindow;
import com.facishare.fs.config.ISPOperator;
import com.facishare.fs.contacts_fs.datactrl.LoadContactDataCtr;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.crmupdate.CRMPresenter;
import com.facishare.fs.crmupdate.ICRMPresenter;
import com.facishare.fs.crmupdate.ICRMView;
import com.facishare.fs.dialogs.AdvertisementDialog;
import com.facishare.fs.dialogs.CommonDialog;
import com.facishare.fs.dialogs.DemonstrationDialog;
import com.facishare.fs.dialogs.LoadingProDialog;
import com.facishare.fs.filesdownload_center.DownStateEventbus;
import com.facishare.fs.filesdownload_center.DownloadFileCtrler;
import com.facishare.fs.i18n.I18NDataLoader;
import com.facishare.fs.i18n.I18NDataUtil;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.js.AvaJsapiTabActivity;
import com.facishare.fs.js.fsminiapp.business.FSMiniAppBusiness;
import com.facishare.fs.js.service.JsApiProcessService;
import com.facishare.fs.js.utils.FeedsHelper;
import com.facishare.fs.memory.FSObservableManager;
import com.facishare.fs.memory.FSObservableManager.Notify;
import com.facishare.fs.pluginapi.AccountManager;
import com.facishare.fs.pluginapi.HostFunction;
import com.facishare.fs.pluginapi.HostInterface;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.account.bean.EmployeeEditionType;
import com.facishare.fs.pluginapi.account.bean.LoginUserInfo;
import com.facishare.fs.pluginapi.bean.AppLinkData;
import com.facishare.fs.pluginapi.cloudctrl.ICloudCtrl;
import com.facishare.fs.pluginapi.cloudctrl.OnConfigChangeListener;
import com.facishare.fs.pluginapi.common_beans.Attach;
import com.facishare.fs.pluginapi.constants.AccountType;
import com.facishare.fs.pluginapi.crm.beans.CrmMenuMsgChangeEvent;
import com.facishare.fs.pluginapi.crm.launchaction.CrmHome;
import com.facishare.fs.pluginapi.fileserver.download.DownloadStatus;
import com.facishare.fs.pluginapi.jsapi.RestartJsApiProcess;
import com.facishare.fs.pluginapi.jsapi.ServiceChatModel;
import com.facishare.fs.pluginapi.main.beans.ApprovalVOBean;
import com.facishare.fs.pluginapi.main.beans.NewFeedMenuBean;
import com.facishare.fs.pluginapi.main.beans.PreviewDocumentModel;
import com.facishare.fs.pluginapi.main.beans.ShareToFeedModel;
import com.facishare.fs.pluginapi.main.events.MainMenuClickEvent;
import com.facishare.fs.pluginapi.main.events.MainMenuRemindEvent;
import com.facishare.fs.pluginapi.refresh_event.LoginEvent;
import com.facishare.fs.pluginapi.refresh_event.LogoutEvent;
import com.facishare.fs.pluginapi.session_command.IDeviceAuthorizationListener;
import com.facishare.fs.pluginapi.trainhelper.H5UrlUtils;
import com.facishare.fs.qixin.IQiXinDataController;
import com.facishare.fs.qr.LocalPicQrScanEvent;
import com.facishare.fs.shortcut.FsShortcutManager;
import com.facishare.fs.ui.adapter.SyncImageLoader;
import com.facishare.fs.ui.setting.AboutActivity;
import com.facishare.fs.ui.setting.SettingActivity;
import com.facishare.fs.ui.setting.bean.PhoneAssistantEvent;
import com.facishare.fs.ui.setting.utils.SettingUtils;
import com.facishare.fs.utils_fs.Accounts;
import com.facishare.fs.utils_fs.AppStateHelper;
import com.facishare.fs.utils_fs.AppStateListener;
import com.facishare.fs.utils_fs.FSPreference;
import com.facishare.fs.utils_fs.FsRouteHelper;
import com.facishare.fs.utils_fs.FsUtils;
import com.facishare.fs.utils_fs.OutDoorGetProductInfoV2Utils;
import com.facishare.fs.utils_fs.PhoneRecognizeUtils;
import com.facishare.fs.utils_fs.PollingUtils;
import com.facishare.fs.utils_fs.SettingsSP;
import com.facishare.fs.utils_fs.ShowPicConfigUtils;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fs.utils_fs.task.ITaskProcessListener;
import com.facishare.fs.web_business_utils.api.FileService;
import com.facishare.fs.weex.MainTabPermissionManager;
import com.facishare.fs.weex.bean.MyInfoRemindChangeEvent;
import com.facishare.fs.weex.module.Api30Utils;
import com.facishare.fslib.R;
import com.fs.beans.beans.EnumDef;
import com.fs.beans.beans.FeedAttachEntity;
import com.fs.beans.beans.ShowPicConfig;
import com.fs.beans.uipaas.NavigationPaaSStatUpdateEvent;
import com.fs.beans.uipaas.QueryWebPaaSStatResult;
import com.fs.beans.uipaas.WebPaaSStat;
import com.fxiaoke.avatar.main.MPManager;
import com.fxiaoke.dataimpl.msg.MsgDataController;
import com.fxiaoke.dataimpl.msg.ObservableCenter;
import com.fxiaoke.dataimpl.msg.ObservableResult;
import com.fxiaoke.dataimpl.msg.ObservableResult.ObservableResultType;
import com.fxiaoke.dataimpl.msg.SingletonObjectHolder;
import com.fxiaoke.fscommon.avatar.prefetch.prefetchManager;
import com.fxiaoke.fscommon.base.MainTabProcessorManager;
import com.fxiaoke.fscommon.floatwindow.FloatWindowManager;
import com.fxiaoke.fscommon.floatwindow.floatball.FloatBallManager;
import com.fxiaoke.fscommon.floatwindow.floatball.FloatBallViewUtil;
import com.fxiaoke.fscommon.sandbox.SandboxContextManager;
import com.fxiaoke.fscommon.sandbox.SandboxUtils;
import com.fxiaoke.fscommon.share.ShareHelper;
import com.fxiaoke.fscommon.util.AccountInfoUtils;
import com.fxiaoke.fscommon.util.AvatarIntentUtils;
import com.fxiaoke.fscommon.util.FsUrlUtils;
import com.fxiaoke.fscommon.util.ImmerseLayoutUtil;
import com.fxiaoke.fscommon.util.WeexIntentUtils;
import com.fxiaoke.fscommon.view.Watermark;
import com.fxiaoke.fscommon.weex.bundle.cmlFsBundleManager;
import com.fxiaoke.fscommon_res.activity.FCBaseActivity;
import com.fxiaoke.fscommon_res.avatar.AvaOpener;
import com.fxiaoke.fscommon_res.avatar.QrScanAvatarDebugProcessor;
import com.fxiaoke.fscommon_res.avatar.QrScanAvatarOpenerProcessor;
import com.fxiaoke.fscommon_res.permission.PermissionExecuter;
import com.fxiaoke.fscommon_res.qrcode.IQrScanProcessor;
import com.fxiaoke.fscommon_res.qrcode.QrScanProcessorHolder;
import com.fxiaoke.fscommon_res.swipe_back.SwipeBackActivityHelper;
import com.fxiaoke.fscommon_res.utils.BrandColorRenderUtils;
import com.fxiaoke.fscommon_res.utils.HWDeviceUtils;
import com.fxiaoke.fscommon_res.weex.QrScanWeexDebugProcessor;
import com.fxiaoke.fscommon_res.utils.ScreenUtil;
import com.fxiaoke.fshttp.web.http.WebApiDownloadFileCallback;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.SessionTypeKey;
import com.fxiaoke.fxlog.DebugEvent;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.fxlog.FCTimePoint;
import com.fxiaoke.intelliOperation.DynamicBizOpNode;
import com.fxiaoke.intelliOperation.OperationManager;
import com.fxiaoke.intelliOperation.type.RedGravity;
import com.fxiaoke.lib.qixin.biz_ctrl.CrmBizUtils;
import com.fxiaoke.lib.qixin.biz_ctrl.SessionCommonUtils;
import com.fxiaoke.lib.qixin.biz_ctrl.SessionMsgHelper;
import com.fxiaoke.lib.qixin.event.BaseShareEvent;
import com.fxiaoke.lib.qixin.event.FirstSessionListChangeEvent;
import com.fxiaoke.lib.qixin.event.SessionSandwichPaasRemindEvent;
import com.fxiaoke.lib.qixin.event.SessionUpdateRawEvent;
import com.fxiaoke.lib.qixin.event.Share2NetDiskEvent;
import com.fxiaoke.lib.qixin.event.WorkRemindSessionChangeEvent;
import com.fxiaoke.lib.qixin.notify.impl.KickOutNotifyProcessor;
import com.fxiaoke.lib.qixin.notify.impl.SessionUpdatedNotifyProcessor;
import com.fxiaoke.location.api.FsLocationResult;
import com.fxiaoke.location.impl.FsMultiLocationManager;
import com.fxiaoke.stat_engine.MemoryUsageTickManager;
import com.fxiaoke.stat_engine.SpaceUsageTickManager;
import com.fxiaoke.stat_engine.StatEngine;
import com.fxiaoke.stat_engine.biz_interface.EventsConfig;
import com.fxiaoke.stat_engine.events.custevents.AppStartPerformanceUtil;
import com.fxiaoke.stat_engine.events.custevents.TinkerStateEvent;
import com.fxiaoke.stat_engine.http.HttpLogTickManager;
import com.gyf.immersionbar.ImmersionBar;
import com.heytap.mcssdk.PushManager;
import com.huawei.agconnect.config.AGConnectServicesConfig;
import com.huawei.hms.aaid.HmsInstanceId;
import com.huawei.hms.common.ApiException;
import com.lidroid.xutils.util.AppInfoUtils;
import com.lidroid.xutils.util.FSDeviceID;
import com.lidroid.xutils.util.FsIOUtils;
import com.lidroid.xutils.util.MotionEventUtil;
import com.rockerhieu.emojicon.EmojiconsFragment;
import com.rockerhieu.emojicon.datactr.EmoticonCtr;

import com.taobao.weex.WXSDKEngine;
import com.tencent.map.geolocation.TencentLocationManager;
import com.weidian.lib.hera.utils.StorageAdapter;
import com.weidian.lib.hera.utils.StorageUtil;

import java.io.File;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Observable;
import java.util.Observer;
import java.util.Set;
import java.util.concurrent.Executors;

import de.greenrobot.event.EventBus;
import de.greenrobot.event.core.ISubscriber;
import de.greenrobot.event.core.MainSubscriber;
import de.greenrobot.event.core.PublisherEvent;


public class MainTabActivity extends AvaJsapiTabActivity
        implements Observer, ICRMView, TabHost.OnTabChangeListener  {

    private static final DebugEvent TAG = new DebugEvent("MainTabActivity");
    //获取通讯录
    LoadContactDataCtr mLoadContactDataCtr;
    private static boolean isDebug = false;
    private AppConfigManager mAppConfigManager;
    private AppConfigPersist mAppConfigPersist;
    private AppConfigRenderer mAppConfigRenderer;
    private AppConfig mAppConfig;
    SessionUpdateEventCtr mSessionUpdateEventCtr;
    /**
     * Crm 最新更新机制解析器
     */
    private ICRMPresenter mCRMPresenter = null;
    /**
     * 图片中识别二维码
     */
    private MainSubscriber<LocalPicQrScanEvent> mSimpleImgQrCodeScanSubscriber;

    private MainSubscriber<BaseShareEvent> mShareSubscriber;

    private MainSubscriber<PhoneAssistantEvent> mPhoneAssistantEvent;

    private com.fs.beans.beans.FeedAttachEntity mShareFae;

    public class INotifyCountChanged {
        public void onUnReadMsgCount(int count) {
            FCLog.i(LogUtilCommon.debug_fs, "main tab activity onUnReadMsgCount:" + MainTabActivity.this.hashCode());
            UnreadMsgUtils.checkToUpdateCachedFirstSessionListUnreadCountData(MainTabActivity.this, count);
            notifyRemind(count, TAB_MSG_ACCESS_KEY);
        }
    }

    public static INotifyCountChanged mNotifyCountChanged;
    static long s_LastClickTime;

    public static MainTabActivity instance = null;

    public static void clearInstance(){
        instance = null;
        AvaOpener.setPreLoadActivity(null);
    }

    private TabHost tabHost;

    // 企信
    private final String TAB_MSG_ACCESS_KEY = "message";
    // 工作
    public static final String TAB_HOME_ACCESS_KEY = "xt.oa.collabration";
    // CRM
    private final String TAB_CRM_ACCESS_KEY = "fs.crm";
    // 应用
    private final String TAB_FUNCTION_ACCESS_KEY = "fs.common.appcenter";
    // 我
    private final String TAB_PERSON_INFO_ACCESS_KEY = "fs.common.profile.me";
    /**
     * 底部crm的tab操作入口
     */
    private View mCrmView;

    private CommonDialog mydialog;

    public IMaintabNav iMaintabNavHome = null;
    View btnSwitchExperienceRole;//切换角色
//    Button btnMainLeft;
//    Button btnMainQuit;//退出按钮
//    Button btnMainRegister;//注册按钮

    //显示我tab小红点的 user list
    private final static List<String> personal_info_tab_red_list = Collections.synchronizedList(new ArrayList<String>());

    public final Handler mHandler = new Handler();
    private final HashMap<String, ButtonHolder> mTabMap = new HashMap<String, ButtonHolder>(8);
    private final HashMap<String, Intent> mIntentsInTabhost = new HashMap<String, Intent>();
    private ArrayList<ButtonState> mTabListSave = new ArrayList<ButtonState>(8);
    private FloatBallViewUtil mFloatBallViewUtil;

    private SwipeBackActivityHelper mSwipeBackHelper;

    /**
     * 草稿箱提示布局
     */
    RelativeLayout draftLayout;
    TextView remindDraft;
    IDeviceAuthorizationListener mIDeviceAuthorizationListener;
    IAccountAttributeNotifyer mAccountAttributeNotifyer;
    private List<ISubscriber> mEvents;
    protected boolean mIgnoreMultitouch = true;
    public static final String ACTION_MAIN_TAB_SELECTED = "com.facishare.fs.MAIN_TAB_SELECTED";
    private final BroadcastReceiver mSelectedTabReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (TextUtils.equals(intent.getAction(), ACTION_MAIN_TAB_SELECTED)) {
                String tabId = intent.getStringExtra("tabId");
                selectTab(tabId, mTabMap);
            }
        }
    };

    private final BroadcastReceiver mTinkerResultReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if( intent != null){
                boolean succ = intent.getBooleanExtra("result", false);
                if(!succ){
                    String version = intent.getStringExtra("version");
                    String fail_reason = intent.getStringExtra("fail_reason");
                    TinkerStateEvent.commitPatchedFailed(version,fail_reason );
                }
            }
        }
    };

    private final BroadcastReceiver mNetworkTimeReceiver = new NetworkTimeReceiver();


    private LocalBroadcastManager mLocalBroadcastManager;
    private HomeActivityTabPopupEvent mHomeTabPopupEvent;

    private void doWorkInBackground() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                AccountManager.initAccountGet();
                OperationManager.getInstance().doInitCache();
                PollingUtils.getInstance().regPollingLs();
                OfflineUtils.initOfflineFile();
                if(instance!=null){
                    MsgDataController.getInstace(instance);
                }
                OutDoorGetProductInfoV2Utils.checkProduct();
                //考外勤初始化数据
                OutDoor2CacheManger.inItKqData(mHandler);
                OutdoorLocationPolling.getInstance().startPolling();
                showAd();

                //加载或者更新表情数据
                if (AccountManager.isLogin(App.getInstance())) {//如果未登录不执行
                    //删除旧的表情文件夹
                    File file = FSContextManager.getGlobalContext().getSDOperator().getExternalDir();

                    if (file != null) {
                        String path = file.getAbsolutePath() + "/fsgif";
                        FsIOUtils.deleteFileOrDir(new File(path));
                    }
                    EmoticonCtr ctr = EmoticonCtr.getInstance(MainTabActivity.this);
                    ctr.check2Update();
                }

                // 第一次更新CRM菜单红点
                handleCrmTabRemindIcon();

                //缓存HeraAppService
                cacheHeraApp();


                Api30Utils.copyAndDeleteOldMyDownLoad();
                //请求服务端数据，更新品牌色的数据
                BrandColorDataHandler.updateBrandColorsFromServer(MainTabActivity.this);
                //请求服务端，更新用于选人组件的自定义字段配置数据
                ContactSelectionCustomFieldsHandler.checkUpdateDataFromServer();
                tickSpaceUsage();
                initHttpTickManager();
                PublicEmployeesEntryCtr.getInstance().checkInit();//因为可能要进入到通讯录页面，所以需要提前检查并初始化需要的外部联系人参数，此处作为冗余保证
                ContactConfigProvider.getInstance().updateByServer(MainTabActivity.this);//冷启动触发
                MainTabLifeListenersManager.getInstance().triggerOnDoWorkInBackground();
            }
        }, "doWorkInBackground").start();
    }

    //add by wubb
    private void showApprovalActivity(Activity activity, ApprovalVOBean bean){
        ApprovalVO approvalVO = new ApprovalVO();
        approvalVO.mIsToDraft = bean.isToDraft;
        approvalVO.approveDetail=bean.approveDetail;
        Intent intent = SendApproveCenterActivity.getIntent(activity, approvalVO);
        activity.startActivity(intent);
    }

    private void showNewFeedMenu(final Activity activity, final NewFeedMenuBean bean){
        GridViewPopupWindowFactory.showPopupWindow(GridViewPopupWindowFactory.PopupWindowType.SELECT_FEED, activity, bean.view, new GridViewPopupWindow.OnGridViewPopupWindowClickListener() {
            @Override
            public void onItemClick(String key, GridViewPopupWindow.GridItemData data) {
                switch(GridViewPopupWindowFactory.FeedMenuType.valueOf(key)){
                    case SHARE:
                        FeedsHelper.showShareActivity(activity,bean.text);
                        break;
                    case JOURNAL:
                        FeedsHelper.showDiaryActivity(activity,bean.text);
                        break;
                    case APPROVE:
                        FeedsHelper.showApprovalActivity(bean.text);
                        break;
                    case MISSION:
                        FeedsHelper.showTaskActivity(activity,bean.text);
                        break;
                    case SCHEDULE:
                        FeedsHelper.showScheduleActivity(activity,bean.text);
                        break;
                    case ORDER:
                        FeedsHelper.showOrderActivity(activity,bean.text);
                        break;
                    default:
                        break;
                }
            }
        });
    }

    private void previewDocument(Activity activity,PreviewDocumentModel model){
        FeedAttachEntity entity = new FeedAttachEntity();
        entity.canPreview = true;
        entity.previewFormat = 1;
        entity.isAliCloud=model.isAliCloud;
        entity.failureTime=model.failureTime;
        entity.hideMoreOption=model.hideMoreOption;
        entity.showWatermark=model.showWatermark;
        entity.ext = model.ext;
        if(model.fileSize>0){
            entity.attachSize = model.fileSize;
        }

        if(TextUtils.isEmpty(model.fileToken)){
            String filePath=TextUtils.isEmpty(model.fileAPath)?model.fileNPath:model.fileAPath;
            entity.attachPath = filePath;
            entity.attachName=TextUtils.isEmpty(model.fileName)?filePath:model.fileName;
            entity.previewUrl = model.previewUrl;
            entity.downloadUrl = model.downloadUrl;
            FsUtils.openAttach(activity, entity);
        }
        else{
            entity.attachPath = model.fileToken;
            entity.attachName = model.fileName;
            FsUtils.openNetDiskFile(activity, model.fileId, null, entity, null);
        }
    }

    private void shareFileToFeed(Activity activity,ShareToFeedModel model){
        Intent send = new Intent(activity, XSendShareActivity.class);
        ShareVO shareVO = new ShareVO();
        Attach attach = new Attach(model.name, model.npath, EnumDef.FeedAttachmentType.NetDisk.value, Integer.valueOf(model.size));
        shareVO.getUpLoadFiles().add(attach);
        send.putExtra(BaseFsSendActivity.VO_KEY, shareVO);
        send.putExtra("js_shareToFeed", true);
        activity.startActivityForResult(send, model.requestCode);
    }

    @Override
    protected void onPostCreate(Bundle icicle) {
        super.onPostCreate(icicle);
        mSwipeBackHelper.onPostCreate();
        tinkerTick();
    }


    private void tinkerTick(){


    }

    // 主题引导管理器
    private ThemeGuide mThemeGuide;

    I18NHelper.LoadListener mi18NLoaderListener = new I18NHelper.LoadListener() {
        @Override
        public void onLoaded() {
            //重新加载下多语言展示，确保能在多租户下正常展示多语言信息，eg:中船企业要把crm展示为“商情系统”
            //只需更改内置的底部按钮显示，app自定义的由用户自行修改
            updateAllTabText();
        }
    };

    /**
     *  从网络上单独获取一下内置tab需要的多语言词条（因为可能有租户级别的更新）
     */
    private void requestNetI18NTabTex() {
        boolean isOpenDefault = false;
        String ea = FSContextManager.getCurUserContext().getAccount().getEnterpriseAccount();
        if ("zhongchuan".equals(ea)) { //如果企业账号是中船，就默认允许从网络更新
            isOpenDefault = true;
        }
        boolean needRequestTabText = HostInterfaceManager.getCloudCtrlManager().getBooleanConfig("800_need_update_tab_text", isOpenDefault);
        if (!needRequestTabText) {
            return;
        }
        List<String> needRequestKes = new ArrayList<>();
        needRequestKes.add("xt.x_person_activity_copy.text.qixin");
        needRequestKes.add("xt.tag_layout.text.work");
        needRequestKes.add("xt.tag_layout.text.crm");
        needRequestKes.add("common.main_tab.entries_des.app");
        needRequestKes.add("weex.qixin.me");
        new I18NDataLoader(I18NHelper.getInstance().getCurrentLang()).requestKeyData(needRequestKes, new I18NDataUtil.I18NDataLoadListener() {
                    @Override
                    public void success() {
//                        FCLog.i(I18NHelper.TAG,
//                                "requestNetI18NTabTex success update TAB_CRM = " + TAB_CRM + " to " + I18NHelper.getText("xt.tag_layout.text.crm") + " " + App.versionCode);
                        updateAllTabText();
                    }

                    @Override
                    public void fail() {
                        FCLog.i(I18NHelper.TAG,
                                "requestNetI18NTabTex fail update " + I18NHelper.getText("xt.tag_layout.text.crm") + " " + App.versionCode);
                        updateAllTabText();
                    }
                }
        );
    }

    private void updateAllTabText() {
        // 企信
        String TAB_MSG = I18NHelper.getText("xt.x_person_activity_copy.text.qixin");//* 企信 *//*;
        // 工作
        String TAB_HOME = I18NHelper.getText("xt.tag_layout.text.work");//* 工作 *//*;
        // CRM
        String TAB_CRM = I18NHelper.getText("xt.tag_layout.text.crm", "CRM");//* CRM *//*;//"";
        // 应用
        String TAB_FUNCTION = I18NHelper.getText("common.main_tab.entries_des.app");//* 应用 *//*;
        // 我
        String TAB_PERSON_INFO = I18NHelper.getText("weex.qixin.me");//* 我 *//*;
        updateTabText(TAB_MSG, "xt.x_person_activity_copy.text.qixin", TAB_MSG_ACCESS_KEY);
        updateTabText(TAB_HOME, "xt.tag_layout.text.work", TAB_HOME_ACCESS_KEY);
        updateTabText(TAB_CRM, "xt.tag_layout.text.crm", TAB_CRM_ACCESS_KEY);
        updateTabText(TAB_FUNCTION, "common.main_tab.entries_des.app", TAB_FUNCTION_ACCESS_KEY);
        updateTabText(TAB_PERSON_INFO, "weex.qixin.me", TAB_PERSON_INFO_ACCESS_KEY);
    }

    private void updateTabText(String oldTabValue, String tabI18nKey, String accessKey) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                ButtonHolder buttonHolder = null;
                if (accessKey != null) {
                    buttonHolder = mTabMap.get(accessKey);
                }
                if (buttonHolder == null || buttonHolder.tabText == null) {
                    FCLog.w(I18NHelper.TAG, "updateTabText is failed by not find button with oldTabValue= " + oldTabValue + " and mTabMap="
                            + mTabMap + " " + App.versionCode + " accessKey=" + accessKey);
                    return;
                }
                final String newTabValue = I18NHelper.getText(tabI18nKey);
                if (oldTabValue.equals(newTabValue)) {
                    if (oldTabValue.equals("CRM")) {
                        FCLog.w(I18NHelper.TAG, "updateTabText CRM failed by oldTabValue.equals(newTabValue) ");
                    }
                    return;//文本内容未改变就不响应刷新
                }
                FCLog.i(I18NHelper.TAG, "updateTabText old is " + oldTabValue + " new is " + newTabValue);
                buttonHolder.tabText.setText(newTabValue);
            }
        });
    }

    private long mCreateTime;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        ScreenUtil.setActivityOrientationSensor(this);
        AntiCheatUtils.lookThrowable(this, Log.getStackTraceString(new Throwable()));
        mCreateTime = System.currentTimeMillis();
        Log.e("tinkerpatch", "zdstinkperpatch3");
        if(!HostFunction.getInstance().isAppPrepared()){ //恢复actvity 由于没有权限app没有初始化
            finish();
            android.os.Process.killProcess(android.os.Process.myPid());
            return;
        }
        MainTabLifeListenersManager.getInstance().init(this, mHandler);
        MainTabLifeListenersManager.getInstance().triggerOnPreCreate();
//        HostFunction.getInstance().setCurrentActivity(this);//默认只在onresume时更新currentActivity，但登录后的业务可能会立刻使用，故这里设置下
        // polling轮询操作延迟到这里启动，避免登录过早，拿不到互联身份信息
//        PollingManager.getInstance().startOnTimer();
        AppStartPerformanceUtil util= com.fxiaoke.host.App.getG_app().getAppStartPerformanceUtil();
        if(util!=null){
            util.appInitEnd();
            util.maintabInitStart();
        }
        FCTimePoint.start("10.MainTabActivity onCreate");
        super.onCreate(savedInstanceState);
        PublicEmployeesEntryCtr.getInstance().setPersistentHandler(new PublicEmployeesEntryPersistentHandlerProxy(this));
//        ActionRouterManager  manager = new ActionRouterManager();
//        manager.getActionRouter(false);

//        String isCheat = antiCheatingHelper.getInstance().isCheat();
//        Log.e("zds", "ischeat: " + isCheat);
        markUpdateAppCenterData();//冷启动后，标记一下应用列表的数据需要再次更新
        mSwipeBackHelper = new SwipeBackActivityHelper(this);
        mSwipeBackHelper.onActivityCreate();
        if (mSwipeBackHelper.getSwipeBackLayout() != null) {
            mSwipeBackHelper.getSwipeBackLayout().setEnableGesture(false);
        }
        instance = this;
        AvaOpener.setPreLoadActivity(this);
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                OutDoorUploaderManager.initInstance();

                //TokenManager.getInstance().setLogin(true);
                PlusDataProvider.getInstance().updatePayPermissionFromServer(null);
                initShortcut();

            }
        }, 200);

        ShowGuideMapUtils.getInstance(this).registerConfigChanged();
        MapsInitializer.updatePrivacyShow(instance,true,true);
        MapsInitializer.updatePrivacyAgree(instance,true);
        AMapLocationClient.updatePrivacyShow(instance,true,true);
        AMapLocationClient.updatePrivacyAgree(instance,true);
        ServiceSettings.updatePrivacyShow(instance,true,true);
        ServiceSettings.updatePrivacyAgree(instance,true);
        TencentLocationManager.setUserAgreePrivacy(true);
        LocationClient.setAgreePrivacy(true);
        BaseActivity.checkUIInit(App.getInstanceApp());
        EmojiconsFragment.setKeyboardH(SettingsSP.getKeybordHeight());
        setContentView(R.layout.main_tab_dynamic_bottom);
        BrandColorRenderUtils.changeStatusBarWithSkinColor(this);
        mNotifyCountChanged = new INotifyCountChanged();

        FSObservableManager.getInstance().addUpdateEmployee(this);
        FSObservableManager.getInstance().addSendEvent(this);
        SingletonObjectHolder.getInstance().addObject(this);
        mIDeviceAuthorizationListener = new DeviceAuthorizationCtr(this);
        SingletonObjectHolder.getInstance().addObject(mIDeviceAuthorizationListener);

        mAccountAttributeNotifyer = new AccountAttributeNotifyerImpl(this);
        SingletonObjectHolder.getInstance().addObject(mAccountAttributeNotifyer);
        registerEvents();
        CalendarTimeZoneUtils.setCalendarLayoutTimeZone(FSContextManager.getCurUserContext().getAccount().getTimeZone(HostInterfaceManager.getHostInterface().getApp()));
        FeedGrayDataProvider.getInstance().init();//初始化获取审批、指令等是否可见的灰度数据，初始化请求需要提前，否则会有获取灰度数据过晚导致业务判断错误的问题
        updateWaterMarkPaint();
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (PdaManager.isPda()){
                    ScanUtils.loadConfig();
                }
                FeedSenderTaskManger.newInstance();
                SenderManager.newInstance();
                downloadFileCtrlerStart();

                doWorkInBackground();
                initSyncAccount();
                AppStateHelper.registerAppStateListener(mAppStateListener);//注册前后台和亮屏事件监听
                MainTabProcessorManager.getInstance().executeOnCreateProcessors();//处理需要在MainTab onCreate 中执行的processor

 				initDeviceInfo();
                if(!uiPaasIsDefaultTab()){
                    FCLog.i(TAG, "#predictload_after_uipaas# uiPass isnot default tab so exec cachePredictApp");
                    cachePredictApp();
                }else{

                }
            }
        }, 50);
        if (savedInstanceState != null) {
            mTabListSave = savedInstanceState.getParcelableArrayList("tab_map_key");
        }

        if(TextUtils.isEmpty(H5UrlUtils.myProfileUrl)){
//            H5UrlUtils.initH5Url();
        }

        initMenuTab();

        // 初始化主题引导功能
        initThemeGuide();

        I18NHelper.getInstance().addLoadListener(mi18NLoaderListener);
        requestNetI18NTabTex();

        btnSwitchExperienceRole = findViewById(R.id.btnSwitchExperienceRole);
        btnSwitchExperienceRole.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                ITaskProcessListener lis = new ITaskProcessListener() {
                    @Override
                    public void onStart() {
                        //提示进度
                        showDialog(WAIT_NORMAL_TYPE);
                    }

                    @Override
                    public void onSuccess(Object object) {
                        //隐藏提示的进度
                        removeDialog(WAIT_NORMAL_TYPE);
                        ExperienceLoginUtils.proceessSwitch(MainTabActivity.this, object);
                    }

                    @Override
                    public void onFailed(String errorInfo, Object object) {
                        //隐藏提示的进度
                        removeDialog(WAIT_NORMAL_TYPE);
                    }
                };
                ExperienceLoginUtils.requestIndustBySwitch(MainTabActivity.this, true, lis);//testbtnSwitchExperienceRole
            }
        });
//        btnMainLeft = (Button) findViewById(R.id.btnMainLeft);
//        btnMainQuit = (Button) findViewById(R.id.btnMainQuit);
//        btnMainRegister = (Button) findViewById(R.id.btnMainRegister);
//        btnMainLeft.setOnClickListener(new OnClickListener() {
//            @Override
        //            public void onClick(View v) {
        //                LoginUitls.sendOldUserSwitch(MainTabActivity.this);
        //            }
        //        });
//        btnMainQuit.setOnClickListener(new OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                logout();
//            }
//        });
//        btnMainRegister.setOnClickListener(new OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                startActivityByAnim(new Intent(MainTabActivity.this, XRegGuideActivity.class));
//            }
//        });
        LoginUitls.handlerAccount(new AccountTypeCallBack() {
            @Override
            public void handler2() {
                showDemonstration(DemonstrationDialog.Demon_Type_New_Login);//因没有了返回正式账号的按钮，所以改用同一弹框图
            }

            @Override
            public void handler1() {
                showDemonstration(DemonstrationDialog.Demon_Type_New_Login);
            }

            @Override
            public void handler() {
                hideControlButton();
            }
        });

        draftLayout = (RelativeLayout) findViewById(R.id.draftLayout);
        remindDraft = (TextView) draftLayout.findViewById(R.id.tv_draft_remind_count);
        draftLayout.setVisibility(View.GONE);

        mLocalBroadcastManager = LocalBroadcastManager.getInstance(getApplicationContext());

        mLocalBroadcastManager.registerReceiver(mSelectedTabReceiver,
                new IntentFilter(ACTION_MAIN_TAB_SELECTED));
        registerReceiver(mTinkerResultReceiver,
                new IntentFilter("tinker_patched_result"));

        registerReceiver(mNetworkTimeReceiver,
                new IntentFilter("android.net.conn.CONNECTIVITY_CHANGE"));

        if (mTabListSave != null && mTabListSave.size() > 0) {
            showDefaultTab(mTabMap);
        }
        mCRMPresenter = new CRMPresenter(MainTabActivity.this, this);
        // 获取缓存CRM的展示和隐藏
        mCRMPresenter.notifyCrmFromCache();
        // 启动界面，相当于重新登录，需要更新CRM状态
        mCRMPresenter.updateCrmStatue();

        EventBus.getDefault().register(homesub);





        sendBroadcast(new Intent("com.facishare.fs.attendance.foreground").setPackage(App.getInstance().getPackageName()));
        FCTimePoint.end("10.MainTabActivity onCreate");

        ICloudCtrl icc=HostInterfaceManager.getCloudCtrlManager();
        //保证切换账号后，也能顺利走合并
        AvatarIntentUtils.initMergeToUipaasAppIds(icc.getStringConfig("mergeToUipaasAppIds897",AvatarIntentUtils.mergeToUiPaasAppIdsDefault));

        boolean isSupportCustomLogo=icc.getBooleanConfig("isSupportCustomLogo",true);
        if(isSupportCustomLogo){
            customLogo(true);
        }else{
            customLogo(false);
        }

        icc.registerConfigChangedListener(mOnConfigChangeListener);
        ShareHelper.getWXShareHelper(this).registerAppToWX();//add by wubb
//        check2StartBizPage();//改在通讯录加载完后的时机再去检查，否则，直接跳转的话也没有意义，通讯录数据都没有

        mSimpleImgQrCodeScanSubscriber = new MainSubscriber<LocalPicQrScanEvent>() {
            @Override
            public void onEventMainThread(LocalPicQrScanEvent localPicQrScanEvent) {
                Collection<IQrScanProcessor> processorList4LocalPic = QrScanProcessorHolder.getInstance().getProcessorList();
                if (processorList4LocalPic.size() == 0) {
                    initQrScanHolder();
                }
            }
        };
        mSimpleImgQrCodeScanSubscriber.register();

        mShareSubscriber = new MainSubscriber<BaseShareEvent>() {
            @Override
            public void onEventMainThread(BaseShareEvent event) {
                if (event instanceof Share2NetDiskEvent) {
                    Share2NetDiskEvent e = (Share2NetDiskEvent) event;
                    Intent data = e.getIntent();
                    String mAttachName = data.getStringExtra(FSNetDiskSaveActivity.KEY_ATTACHNAME);
                    String mAttachPath = data.getStringExtra(FSNetDiskSaveActivity.KEY_ATTACHPATH);
                    long mSize = data.getLongExtra(FSNetDiskSaveActivity.KEY_SIZE, 0);
                    String mFolderId = data.getStringExtra(FSNetDiskSaveActivity.KEY_FOLDERID);
                    int mPathType = data.getIntExtra(CrossFileUtils.KEY_TYPE_PATH, CrossFileUtils.Unknown);

                    FilePreviewUtils.share2NetDisk(mAttachPath, mPathType,  mSize, mFolderId, mAttachName);
                }

            }
        };
        mShareSubscriber.register();

        mPhoneAssistantEvent = new MainSubscriber<PhoneAssistantEvent>() {
            @Override
            public void onEventMainThread(PhoneAssistantEvent event) {
                if (event.isSwitchOpen) {
                    startFloatService();
                } else {
                    stopFloatService();
                }
            }
        };
        mPhoneAssistantEvent.register();

        boolean needNotify = mAppConfig != null && mAppConfig.hasMenu() && !mAppConfig.hasMenu(AppConfigConstants.ActionValue.APP_MESSAGE);
        mSessionUpdateEventCtr = new SessionUpdateEventCtr();
        mSessionUpdateEventCtr.init(needNotify);

        // 获取通讯录 阻塞
        mLoadContactDataCtr = new LoadContactDataCtr(this,mHandler);
        mLoadContactDataCtr.getEmpDatas(I18NHelper.getText("common.main_tab.guide.downloading_contacts")/* 通讯录下载中 */);

        // 语言切换
        String settingLang = com.facishare.fs.i18n.I18NSP.getInstance(this).getCurrentLang();
        if (TextUtils.isEmpty(settingLang)) {
            settingLang = I18NHelper.Language.ZH_CN.value;
        }
        if (!settingLang.equals(I18NHelper.getInstance().getCurrentLang())) {
            exeCurrentLangChange(settingLang);

        }
        logoutEventMainSubscriber.register();
        //startJsApiProcessAsync();

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            ImmersionBar.with(this)
                    .fitsSystemWindows(true)  //使用该属性,必须指定状态栏颜色
                    .statusBarColor(R.color.statusbar_bg_less_m)
                    .statusBarDarkFont(true, 0.2f)
                    .init();
        }

        doShowPic();

        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                new PermissionExecuter().requestManifestPermissions(HostInterfaceManager.getHostInterface().getApp(),MainTabActivity.this,
                        MainTabPermissionManager.getInstance().getPermissions(mAppConfig));
                cmlFsBundleManager.getMemoryCache().preload();

                if(FCLog.getIsBeta()||FCLog.isDebugMode()) {
                    I18NHelper.setI18NReturnTag(FSPreference.getInstance().getBoolean(FSPreference.PrefID.PREF_18N_RETURN_TAG));
                    if(FSPreference.getInstance().getBoolean(FSPreference.PrefID.PREF_MEMORY_FLOAT_TAG)){
                        MemoryFloatWindowManager.getInstance().showPopupWindow();
                    }

                }

            }
        }, 3000);

        FSMiniAppBusiness.initConfig();
        FSMiniAppBusiness.updateConfig(this);


        pushInit();


        AppCustomManager.customCall(new AppCustomManager.ICall(){

            @Override
            public void call(int result) {
                if(result != AppCustomManager.ICall.OK_Result){ // 蒙牛不执行这段代码
                    checkStartFloatService();//检查来电身份识别功能是否开启，如开启需要申请足够权限后开启悬浮窗view，如果不同意授权就需要关闭来电身份识别的开关
                }
            }
        });

//        if (!isCurrentTabShowQXSessionList()) {//企信不在首次展示时才需要从缓存中获取
            UnreadMsgUtils.queryFirstSessionListUnreadCount(this, new UnreadMsgUtils.QuerySessionListUnreadCountCallBack() {
                @Override
                public void onResult(int unreadCount) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            notifyRemind(unreadCount, TAB_MSG_ACCESS_KEY);
                        }
                    });
                }
            });
//        }
        if(FSContextManager.getCurUserContext().getAccount().isVisitorLogin()){
            UnreadMsgUtils.queryCrossSessionListUnreadCount(this, new UnreadMsgUtils.QuerySessionListUnreadCountCallBack() {
                @Override
                public void onResult(int unreadCount) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            notifyRemind(unreadCount, SessionInfoUtils.App_Menu_Item_Access_Key_For_Cross_Message_List);
                        }
                    });
                }
            });
        }

        NavigationPaaSStatHandler.updateAlNavigationPaaSStat(this, mAppConfig);
        ScreenShotShareViewUtils.startScreenShotFileObserver();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && ImmerseLayoutUtil.isMenuPageFullScreen()) {//设置首页全屏
            getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            getWindow().setStatusBarColor(Color.TRANSPARENT);
        }
        MainTabLifeListenersManager.getInstance().triggerOnCreate();
    }

    private void updateWaterMarkPaint() {
        String jsonString = SettingsSP.getQixinWaterMarkObjJsonString();
        if (!TextUtils.isEmpty(jsonString)) {
            try {
                WatermarkService.WaterMarkArgs result = WatermarkService.WaterMarkArgs.parse(jsonString);
                if (result == null) {
                    FCLog.w(TAG, "updateWaterMarkPaint failed by parser cache error-1 " + jsonString);
                    return;
                }
                Pair<Integer,Integer> pair = Watermark.getInstance().transPaintTextColor(result.getFontColor());
                int configTextColor = pair.first;
                int alphaValue = pair.second;
                int configTextSize = Watermark.getInstance().transPaintTextSize(result.getFontSize());
                int configVerticalSpace = Watermark.getInstance().transVerticalSpace(result.getFontSpacing());
                int configHorizontalSpace = Watermark.getInstance().transHorizontalSpace(result.getFontSpacing());
                boolean isBold = Watermark.getInstance().transBold(result.getFontWeight());
                Watermark.getInstance().setTextColor(configTextColor);
                Watermark.getInstance().setAlphaValue(alphaValue);
                Watermark.getInstance().setTextSize(configTextSize);
                Watermark.getInstance().setVerticalSpace(configVerticalSpace);
                Watermark.getInstance().setHorizontalSpace(configHorizontalSpace);
                Watermark.getInstance().setBold(isBold);
                FCLog.i(TAG, "updateWaterMarkPaint by cache is " + result != null ? "true" : "false");
            } catch (Exception e) {
                FCLog.w(TAG, "updateWaterMarkPaint failed by parser cache error-2 " + jsonString + "____" + Log.getStackTraceString(e));
            }
        }
    }

    //更改标记，以让打开应用列表页面时，重新更新应用列表的数据
    private void markUpdateAppCenterData() {
        AppCenterSPUtil.setAppRequestFlag(false);
    }

    private boolean mGotoSetting = false;//是否打开了设置去授权打开悬浮窗了

    private void checkStartFloatService() {
        if (PhoneRecognizeUtils.isSwitchOpen()) {
            boolean hasFloatWindowPermission = PhoneRecognizeUtils.hasWindowPermission(this);
            Runnable failedCallBack = new Runnable() {
                @Override
                public void run() {
                    PhoneRecognizeUtils.updateOpenPhoneAssistant(false);
                    ToastUtils.showToast(PhoneRecognizeUtils.getNoPermissionTip());
                }
            };
            if (!hasFloatWindowPermission) {
                Runnable successCallBack = new Runnable() {
                    @Override
                    public void run() {
                        mGotoSetting = true;
                        FloatWindowManager.getInstance().applyPermissionWithoutConfirm(HostInterfaceManager.getHostInterface().getApp());
                    }
                };
                PhoneRecognizeUtils.showApplyAllPermissionDialog(this, successCallBack, failedCallBack);
                return;
            }
            boolean hasCallLogPermissions = PermissionExecuter.hasPermission(this, Manifest.permission.READ_CALL_LOG);
            if (!hasCallLogPermissions) {
                //满足了悬浮窗权限后，还得提示用户读取通话记录权限；
                PhoneRecognizeUtils.checkApplyReadCallLogPermission(this, new Runnable() {
                    @Override
                    public void run() {
                        //读取通话记录权限也允许了，就可以监听来电号码进行识别了，否则即使打开收到监听，但监听不到号码
                        postDelayedStartFloatService();
                    }
                }, failedCallBack);
                return;
            } else {
                postDelayedStartFloatService();
            }
        }
    }

    private void postDelayedStartFloatService() {
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                FCTimePoint.start("Runnable startFloatService");
                startFloatService();
                FCTimePoint.end("Runnable startFloatService");
            }
        }, 3000);
    }

    private void exeCurrentLangChange(String lang){
        I18NDataLoader i18NDataLoader =  new I18NDataLoader(lang);
        if(I18NHelper.getInstance().isInAssets(lang) || i18NDataLoader.getDataVersion() !=0){
//            I18NDataUtil.doChangeLang(this, lang);
            doChangeLang(this, lang);
        }else{ // 如果不在内置语言列表并且本地没有拉取过此语言数据从网络同步 （仅仅保存文件，内存中的数据不保存）
            i18NDataLoader.requestI18NData(0, null, new I18NDataUtil.I18NDataLoadListener() {
                @Override
                public void success() {
//                    I18NDataUtil.doChangeLang(MainTabActivity.this, lang);
                    doChangeLang(MainTabActivity.this, lang);
                }

                @Override
                public void fail() {
                    i18NDataLoader.savePollingVersion(0);//清空polling version 保证下次进入app 拉取多语
//                    I18NDataUtil.doChangeLang(MainTabActivity.this, lang);
                    doChangeLang(MainTabActivity.this, lang);
                    ToastUtils.show(I18NHelper.getText("maintab.synchronize_multilingual_data.success_tip", "同步多语言数据失败！"));
                }

                @Override
                public boolean onlySaveFile() {
                    return true;
                }
            });
        }

    }
    //封裝语言切换时要做的操作（目前语言切换时，要提前拉取新语言对应的app自定义的数据，保存新语言的标识）
    private void doChangeLang(Activity activity, String lang) {
        AppConfigWebUtils.requestAppConfig(this, lang, new AppConfigWebUtils.IRequestAppConfigCallback() {
            @Override
            public void onSuccess() {
                I18NDataUtil.doChangeLang(activity, lang);
            }

            @Override
            public void onFailed(String error) {
                I18NDataUtil.doChangeLang(activity, lang);
            }
        });
    }

    private boolean isCurrentTabShowQXSessionList() {
        if (tabHost != null) {
            String currentTag = tabHost.getCurrentTabTag();
            String qxTag = I18NHelper.getText("xt.x_person_activity_copy.text.qixin", "企信");
            return qxTag.equals(currentTag) || TAB_MSG_ACCESS_KEY.equals(currentTag);
        }
        return false;
    }

    /**
     * 初始化长按图标的快捷方式
     */
    private void initShortcut(){

        FsShortcutManager.getInstance().initDynamicShortcuts(MainTabActivity.this);
        FsShortcutManager.getInstance().gotoShortcutIntent(MainTabActivity.this,getIntent());
    }

    //检查是否要启动对应的业务页面（比如第三方应用唤起我们的应用这个场景下）
    private void check2StartBizPage() {
        AppLinkData appLinkData = com.fxiaoke.host.App.getG_app().consumeAppLinkData();
        if (appLinkData != null) {
            if (SSOLoginProcessor.startSSOLogin(this, true, appLinkData)) {
                return;
            }
            String bizPath = appLinkData.getBizPath();
            String bizData = appLinkData.getBizData();
            if (!TextUtils.isEmpty(bizPath) && !TextUtils.isEmpty(bizData)) {
                FsRouteHelper.getInstance().gotoAction(this, bizPath, bizData);
            }
        }
    }

    private void startFloatService() {
        FCLog.i(TAG, "startFloatService");
        try {

            Intent srv = new Intent();
            srv.setComponent(new ComponentName(com.fxiaoke.host.App.g_pkgName, "com.fxiaoke.host.service.FloatWindowService"));
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                MainTabActivity.this.startForegroundService(srv);
            } else {
                MainTabActivity.this.startService(srv);
            }
        } catch (Exception e) {
            FCLog.w(TAG, Log.getStackTraceString(e));
        }
    }

    private void stopFloatService() {
        FCLog.e(TAG, "stopFloatService");
        try {
            Intent srv = new Intent();
            srv.setComponent(new ComponentName(com.fxiaoke.host.App.g_pkgName, "com.fxiaoke.host.service.FloatWindowService"));
            MainTabActivity.this.stopService(srv);
        } catch (Exception e) {
            FCLog.w(TAG, Log.getStackTraceString(e));
        }
    }

    void pushInit(){
        if(HWDeviceUtils.isHwDevice()&&HostFunction.getInstance().isHwGetTokenException()){
            Executors.newSingleThreadExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        String appId = AGConnectServicesConfig.fromContext(MainTabActivity.this).getString("client/app_id");
                        HmsInstanceId.getInstance(MainTabActivity.this).getToken(appId,"HCM");
                    } catch (ApiException e) {
                        FCLog.e(TAG,Log.getStackTraceString(e));
                        e.printStackTrace();
                    }
                }
            });
        }
        if(PushManager.isSupportPush(this)){
            PushManager.getInstance().requestNotificationPermission();
        }
    }
    private void doShowPic() {
//        AppStateHelper.registerAppStateListener(new AppStateListener() {
//            @Override
//            public void onRunTopChanged(boolean isRunTop) {
//                if(isRunTop){
//                    isShowPic();
//                    handleFromBackToForeground();
//                }
//            }
//
//            @Override
//            public void onScreenOn(boolean b) {
//
//            }
//        });
    }

    protected void handleFromBackToForeground(){
        OutdoorLocationPolling.getInstance().startReport();
        OutDoorUploaderManager.getInstance().autoRetry(3000);
        sendBroadcast(new Intent("com.facishare.fs.attendance.foreground").setPackage(App.getInstance().getPackageName()));
    }

    private void isShowPic() {
        ShowPicConfig showPicConfig = ShowPicConfigUtils.ReadDataAll();
        if (showPicConfig != null) {
            if (showPicConfig.showType == 0) {
                return;
            }
            if (showPicConfig.showType == 2) {// showType = 2时 显示扉页

                if (ShowPicConfigUtils.isGogoImage(showPicConfig)) {// 如果图片存在
                    goToPicAct();
                } else {
                    String imagename = showPicConfig.picUrls.get(2);
                    ShowPicConfigUtils.downloadConfigImageEx(imagename);
                }
            }
        }
    }

    private void goToPicAct() {
        Intent mIntent = new Intent(this, ShowPicExActivity.class);
        startActivity(mIntent);
    }

    VipNoticeLoader mVipNoticeLoader = null;
    //是否在收到 VipNoticeShowEvent 后，检查过一次是否显示通知对话框，没检查过要补偿一次，
    // 避免首次进入后，polling推送的这个noticeevent事件过晚，没有让对话框显示的问题
    boolean needCheckAgainAfterGetNoticeEvent = true;
    private void addVipNoticeEvent() {
        mEvents.add(new MainSubscriber<VipNoticeShowEvent>() {
            @Override
            public void onEventMainThread(VipNoticeShowEvent vipNoticeShowEvent) {
                if(MainTabActivity.this.isFinishing()){
                    return;
                }
                mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        initVipNoticeLoader();
                        mVipNoticeLoader.reset();
//                        checkShowVipNoticeDialog();//轮询不触发重要通知展示，但第一次需要展示下
                        showVipNoticeDialogOnlyOnce();
                    }
                });
            }
        });
    }

    private void showVipNewNoticeDialog(){
        Context context = this;
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                VipNewNoticeDialog.show(context);
//                startActivity(VipNewNoticeActivity.getIntent(context));
//                ((Activity)context).overridePendingTransition(R.anim.bottom_in,
//                        R.anim.bottom_out);
            }
        },5000);

    }

    private void showVipNoticeDialogOnlyOnce() {
        if (needCheckAgainAfterGetNoticeEvent) {
            needCheckAgainAfterGetNoticeEvent = false;
            checkShowVipNoticeDialog();
        }
    }

    private void checkShowVipNoticeDialog() {
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                initVipNoticeLoader();
                boolean isShowing = mVipNoticeLoader.check2ShowVipNoticesDialog();
                if (isShowing) {
                    needCheckAgainAfterGetNoticeEvent = false;
                }
            }
        }, 10);
    }

    //App前后台状态监听
    private AppStateListener mAppStateListener = new AppStateListener() {
        @Override
        public void onRunTopChanged(boolean isRunTop) {
            //应用打开
            if (isRunTop) {
                MPManager.getInstance().onAppForeground();
                if (AccountManager.getAccount().isLogin()) {
                    checkShowVipNoticeDialog();
                }
                isShowPic();
                handleFromBackToForeground();
                checkToSendStickEvents();
                LoginUserInfo loginUserInfo = FSContextManager.getCurUserContext().getAccount().getLoginUserInfo();
                //判断enterpriseType=1时是沙盒企业，需打开沙盒悬浮框
                if (loginUserInfo != null && loginUserInfo.enterpriseType == 1) {
                    if (mFloatBallViewUtil == null) {
                        mFloatBallViewUtil = new FloatBallViewUtil(MainTabActivity.this,
                                R.layout.float_ball_layout);
                    }
                    mFloatBallViewUtil.showView();
                }
            } else {
                FCLog.flushPersistent();
                MPManager.getInstance().onAppBackground();
                if (mFloatBallViewUtil != null) {
                    mFloatBallViewUtil.hideView();
                }
                startTickMemoryUsage();
            }
            MainTabLifeListenersManager.getInstance().triggerOnRunTopChanged(isRunTop, isOnResume);
        }

        @Override
        public void onScreenOn(boolean b) {
        }
    };


    /**
     * 显示悬浮的AI助手入口view
     */


    long mLastCheckSendTickEventTime = 0L;
    //间隔检查是否上传打点数据的时间 默认为10分钟 （内置有一个20分钟的检查机制）
    final long Default_Interval_Time_For_Check_Send_Tick_Event = 10 * 60 * 1000;
    // 云控设置“间隔检查是否上传打点数据的时间”的key
    final String Cloud_Key_Set_Interval_Time_For_Check_Send_Tick_Event = "Interval_Time_For_Check_Send_Tick_Event";

    private void checkToSendStickEvents() {
        long current = System.currentTimeMillis();
        long intervalTime = HostInterfaceManager.getCloudCtrlManager().getLongConfig(Cloud_Key_Set_Interval_Time_For_Check_Send_Tick_Event,
                Default_Interval_Time_For_Check_Send_Tick_Event);
        if (intervalTime > 0 && (current - mLastCheckSendTickEventTime) > intervalTime) {
            StatEngine.checkToSend();
            mLastCheckSendTickEventTime = current;
            FCLog.i(TAG, "checkToSendStickEvents call checkToSend");
        }
    }

    /**
     *  必须在UI线程里初始化
     */
    private void initVipNoticeLoader() {
        if (mVipNoticeLoader == null) {
            mVipNoticeLoader = new VipNoticeLoader(MainTabActivity.this);
        }
    }

    String getLastLogo(ISPOperator user,ISPOperator global){
        String ret=null;
        String temp=global.getString("custom_logo","logo_default");
//        if (TextUtils.isEmpty(temp)){
//            ret=user.getString("custom_logo");
//        }else{
        ret=temp;
//        }
        return ret;
    }
    void saveLogo(ISPOperator user,ISPOperator global,String logo){
//        if (logo.equals("logo_default")){
        global.save("custom_logo",logo);
        user.save("custom_logo",logo);
//        }else{
//            user.save("custom_logo",logo);
//            global.remove("custom_logo");
//        }
    }
    CommonDialog mCommonDialog;
    void customLogo(final boolean isSupport){
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if(!isSupport){
                    FCLog.i(TAG,"not support customLogo");
                    String logo="logo_default";
                    String lastlogo=null;
                    ISPOperator spuser=FSContextManager.getCurUserContext().getSPOperator("nowUser");
                    ISPOperator spglobal=FSContextManager.getGlobalContext().getSPOperator("nowUser");
                    lastlogo=getLastLogo(spuser,spglobal);
                    saveLogo(spuser,spglobal,logo);
                    if(!logo.equals(lastlogo)){
                        FCLog.i(TAG,"not support customLogo force default");
                        try{
                            HostInterfaceManager.getHostInterface().enableHomeIcon(logo);
                        }catch (Exception e){
                            e.printStackTrace();
                        }
                        if(mCommonDialog!=null){
                            mCommonDialog.dismiss();
                        }
                        mCommonDialog=CommonDialog.createOneButtonDialog(
                                MainTabActivity.this,null,
                                I18NHelper.getText("common.main_tab.guide.customelogo_notsupport"),I18NHelper.getText("common.main_tab.guide.known")/* 了解了 */,
                                false);
                        mCommonDialog.show();
                    }

                    return;
                }
                FCLog.i(TAG,"support customLogo");
                String temp = mAppConfig != null && mAppConfig.app != null ? mAppConfig.app.logo : null;
                String logo=null;
                final String logo_default= "logo_default";
                if (TextUtils.isEmpty(temp)){
                    logo=logo_default;
                }else{
                    logo=temp.substring(10);
                }
                ISPOperator spuser=FSContextManager.getCurUserContext().getSPOperator("nowUser");

                ISPOperator spglobal=FSContextManager.getGlobalContext().getSPOperator("nowUser");

                String lastlogo=null;
                lastlogo=getLastLogo(spuser,spglobal);
                saveLogo(spuser,spglobal,logo);
//                if (TextUtils.isEmpty(lastlogo)&&logo.equals(logo_default)){
//
//                }else if(TextUtils.isEmpty(lastlogo)||!lastlogo.equals(logo)){
                if(!lastlogo.equals(logo)){
                    try{
                        HostInterfaceManager.getHostInterface().enableHomeIcon(logo);
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                    String msg=null;
                    if (!logo.equals(logo_default)){
                        msg=I18NHelper.getText("common.main_tab.guide.changed_lauch_icon")/* 您的企业定制了桌面图标 */;
                    }else {
                        msg=I18NHelper.getText("common.main_tab.guide.restore_lauch_icon")/* 桌面图标将切换回默认图标 */;
                    }
                    mCommonDialog=CommonDialog.createOneButtonDialog(MainTabActivity.this,I18NHelper.getText("common.main_tab.guide.enterprise_settings")/* 企业配置 */,msg,I18NHelper.getText("common.main_tab.guide.known")/* 了解了 */,false);
                    ImageView iv=new ImageView(MainTabActivity.this);
                    float den=getResources().getDisplayMetrics().density;
                    LinearLayout.LayoutParams lp=new LinearLayout.LayoutParams((int)(50*den),(int)(50*den));
                    lp.gravity=Gravity.CENTER_HORIZONTAL;
                    iv.setLayoutParams(lp);
                    int resid=getResources().getIdentifier(logo,"drawable",getPackageName());
                    if (resid!=0){
                        iv.setImageResource(resid);
                    }else {
                        FCLog.d(TAG,"logo not valid");
                    }

                    mCommonDialog.setCustomContentView(iv);
                    mCommonDialog.show();
                }



            }
        },300);
    }
    MainSubscriber homesub = new MainSubscriber<HomeActivityTabPopupEvent>() {
        @Override
        public void onEventMainThread(HomeActivityTabPopupEvent homeActivityTabPopupEvent) {
            if(homeActivityTabPopupEvent != null) {
                mHomeTabPopupEvent = homeActivityTabPopupEvent;
            }
        }
    };
    OnConfigChangeListener mOnConfigChangeListener=new OnConfigChangeListener() {

        @Override
        public void onConfigChanged(String key, String oldVal, String newVal) {
            if(key.equals("push_alive_ctrl_sync")){
                initByConfig(Boolean.valueOf(newVal));
            }else if("isSupportCustomLogo".equals(key)){
                if(!Boolean.valueOf(newVal)){
                    FCLog.d(TAG,"change logo to default");
                    customLogo(false);
                }
            }else if("spaceUsageTickKey".equals(key)){
                if(!TextUtils.isEmpty(newVal) && !newVal.equals(oldVal)){
                    FCLog.i(TAG,"spaceUsageTickKey:"+newVal);
                    SettingsSP.saveEnableTickSpaceUsage(true);
                }else if(TextUtils.isEmpty(newVal)){
                    SettingsSP.saveEnableTickSpaceUsage(false);
                }
            }

        }
    };


    private void initHttpTickManager(){
        final ICloudCtrl ctrlConfig = HostInterfaceManager.getCloudCtrlManager();
        HttpLogTickManager.getInstance().initHttpMonitorConfig(ctrlConfig.getStringConfig("httpMonitorConfig",null));
    }

    private void startTickMemoryUsage(){
        if(HostInterfaceManager.getCloudCtrlManager().getBooleanConfig("memoryUsagePercent",true)) {
            FCLog.i(TAG, "startTickMemoryUsage");
            MemoryUsageTickManager.tick(App.getInstance(),null,null);
        }
    }

//    private void appStartTick(){
//        CrmBizTick.crmEvent("")
//    }


    /**
     * 上报应用空间使用情况
     */
    private void tickSpaceUsage(){
        if(SettingsSP.getEnableSpaceUsage()){
            SpaceUsageTickManager.tick();
            SettingsSP.saveEnableTickSpaceUsage(false);
        }
    }
    void initSyncAccount(){
        final ICloudCtrl ctrlConfig = HostInterfaceManager.getCloudCtrlManager();

        ctrlConfig.registerConfigChangedListener(mOnConfigChangeListener);
        initByConfig(ctrlConfig.getBooleanConfig("push_alive_ctrl_sync",false));

    }
    void initByConfig(boolean issync){
//        try {
//            if (!issync){
//                    HostInterfaceManager.getHostInterface().clearSyncAccount(AccountManager.getAccount().getEmployeeName(),AccountManager.getAccount().getLoginUserInfo().ticket);
//            }else {
//                HostInterfaceManager.getHostInterface().initSyncAccount(AccountManager.getAccount().getEmployeeName(),AccountManager.getAccount().getLoginUserInfo().ticket);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    @Override
    protected void onRestoreInstanceState(Bundle state) {
        //super.onRestoreInstanceState(state);
        FCLog.i(TAG, "onRestoreInstanceState");
    }

    @Override
    public void onRequestPermissionsResult(int requestCode,
                                           @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        PermissionExecuter.notifyPermissionsChange(permissions,
                grantResults);
    }

    private void registerEvents() {
        mEvents = new ArrayList<>();
        mEvents.add(new MainSubscriber<CrmMenuMsgChangeEvent>() {
            @Override
            public void onEventMainThread(CrmMenuMsgChangeEvent event) {
                notifyCrmTabMsgChange();
            }
        });
        mEvents.add(new MainSubscriber<WorkRemindSessionChangeEvent>() {
            @Override
            public void onEventMainThread(WorkRemindSessionChangeEvent event) {
                notifyCrmTabMsgChange();
            }
        });
        mEvents.add(new MainSubscriber<MainMenuRemindEvent>() {
            @Override
            public void onEventMainThread(MainMenuRemindEvent event) {
                if (event == null) {
                    return;
                }
                if (event.isOnlyShowRemindDot()) {
                    // 显示红点
                    notifyRemindIcon(event.hasNotReadCount(), event.getAccessKey());
                } else {
                    // 显示飘数
                    notifyRemind(event.getNotReadCount(), event.getAccessKey());
                }
            }
        });

        mEvents.add(new MainSubscriber<DownStateEventbus>() {
            @Override
            public void onEventMainThread(DownStateEventbus downStateEventbus) {
                if (downStateEventbus.state == DownloadStatus.FINISH){
                    checkNetDiskFileDownChangePosRemind();
                }
            }
        });

        mEvents.add(new MainSubscriber<PreviewDocumentModel>() {
            @Override
            public void onEventMainThread(PreviewDocumentModel model) {
                Activity previewActivity = HostInterfaceManager.getHostInterface().getCurrentActivity();
                if(previewActivity == null || previewActivity.isFinishing()){
                    previewActivity = MainTabActivity.this;
                }
                previewDocument(previewActivity,model);
            }
        });

        mEvents.add(new MainSubscriber<ApprovalVOBean>() {
            @Override
            public void onEventMainThread(ApprovalVOBean bean) {
                showApprovalActivity(MainTabActivity.this,bean);
            }
        });

        mEvents.add(new MainSubscriber<NewFeedMenuBean>() {
            @Override
            public void onEventMainThread(NewFeedMenuBean bean) {
                showNewFeedMenu(MainTabActivity.this,bean);
            }
        });

        mEvents.add(new MainSubscriber<ShareToFeedModel>() {
            @Override
            public void onEventMainThread(ShareToFeedModel bean) {
                shareFileToFeed(MainTabActivity.this,bean);
            }
        });

        mEvents.add(new MainSubscriber<RestartJsApiProcess>() {
            @Override
            public void onEventMainThread(RestartJsApiProcess bean) {
                startJsApiProcessAsync();
            }
        });

        mEvents.add(new MainSubscriber<ServiceChatModel>() {
            @Override
            public void onEventMainThread(ServiceChatModel model) {
                BaseServiceAction action = new InnerServiceAction() ;
                action.serviceClick(MainTabActivity.this,model.serviceChannelId);
            }
        });
        mEvents.add(new MainSubscriber<FirstSessionListChangeEvent>() {
            @Override
            public void onEventMainThread(FirstSessionListChangeEvent event) {
               processFirstSessionListUnreadCountChangeEvent(event);
            }
        });
        //由于需求的变更，暂时去掉
//        mEvents.add(new MainSubscriber<NotifyRecommendAppListChangedEvent>() {
//            @Override
//            public void onEventMainThread(NotifyRecommendAppListChangedEvent event) {
//                new RecommendAppListPresenter(null).refreshRecommendAppList();
//            }
//        });
        addVipNoticeEvent();
        addCheckStartBizPageEvent();
        addNavigationPaaSStatUIUpdateEvent();
        addNavigationPaaSStatUpdateEvent();
        addSessionSandwichPaasRemindEvent();
        for (ISubscriber event : mEvents) {
            event.register();
        }
    }

    private void addSessionSandwichPaasRemindEvent() {
        mEvents.add(new MainSubscriber<SessionSandwichPaasRemindEvent>() {
            @Override
            public void onEventMainThread(SessionSandwichPaasRemindEvent event) {

                PollingUtils.processSessionSandwichPaasRemindEvent(event);
            }
        });
    }

    /**
     * 获取到主导航飘数数据后，刷新界面显示
     */
    private void addNavigationPaaSStatUIUpdateEvent() {
        mEvents.add(new MainSubscriber<QueryWebPaaSStatResult>() {
            @Override
            public void onEventMainThread(QueryWebPaaSStatResult event) {
                if (event == null) {
                    return;
                }
                notifyRemindNavigationUIByPaaSStatList(event.getPaaSStatList());
            }
        });
    }

    /**
     * polling监听后触发的某个底部主导航的飘数更新
     */
    private void addNavigationPaaSStatUpdateEvent() {
        mEvents.add(new MainSubscriber<NavigationPaaSStatUpdateEvent>() {
            @Override
            public void onEventMainThread(NavigationPaaSStatUpdateEvent event) {
                if (event == null) {
                    return;
                }
                NavigationPaaSStatHandler.updateNavigationPaaSStat(MainTabActivity.this, event, mAppConfig);
            }
        });
    }

    private void processFirstSessionListUnreadCountChangeEvent(FirstSessionListChangeEvent event) {
        if (event == null) {
            return;
        }
        int newNotReadCount = -1;
        List<SessionListRec> allNewSessions = event.getAllNewSessions();
        if (allNewSessions != null && allNewSessions.size() > 0) {
            for (SessionListRec sessionInfo : allNewSessions) {
                if (!sessionInfo.isSetNoStrongNotification()) {
                    newNotReadCount += sessionInfo.getNotReadCount();
                }
            }
        } else {
            try {
                newNotReadCount = SessionMsgHelper.getChatHelper(this).getFirstSessionListUnreadCount();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        final int resultAllNotReadCount = newNotReadCount;
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                UnreadMsgUtils.checkToUpdateCachedFirstSessionListUnreadCountData(MainTabActivity.this, resultAllNotReadCount);
                notifyRemind(resultAllNotReadCount, TAB_MSG_ACCESS_KEY);
            }
        });
    }

    ISubscriber mCheckStartBizPageEvent;

    private void addCheckStartBizPageEvent() {
        mCheckStartBizPageEvent = new MainSubscriber<LoadALevelContactFinishEvent>() {
            @Override
            public void onEventMainThread(LoadALevelContactFinishEvent finishEvent) {
                if (MainTabActivity.this.isFinishing()) {
                    return;
                }
                mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        check2StartBizPage();
                        unregisterCheckStartEvent();//因为这个事件只应该触发一次，故响应过一次后即移除注册
                    }
                });
            }
        };
        mEvents.add(mCheckStartBizPageEvent);//仍然加到这里，等着页面销毁时释放，是因为怕一直没消费，漏移除了
    }

    private void unregisterCheckStartEvent() {
        if (mCheckStartBizPageEvent != null) {
            mCheckStartBizPageEvent.unregister();
        }
    }

    /**
     * 通知 CRM 未读消息
     */
    private void notifyCrmTabMsgChange(){
        AsyncTask.execute(new Runnable() {
            @Override
            public void run() {
                handleCrmTabRemindIcon();
            }
        });
    }

    /**
     * 处理CRM Tab 未读消息提醒
     */
    private void handleCrmTabRemindIcon() {
        Pair<Integer, Integer> remindCount = CrmBizUtils.getSessionRemindInfo(SessionTypeKey.Session_CRM_Remind, "");
        Pair<Integer, Integer> todoCount = CrmBizUtils.getCrmTodoRemindInfo();
        Pair<Integer, Integer> appendWorkItemsRemindInfo = CrmBizUtils.getAppendWorkRemindItemsCountInfo();
        int remindNotReadCount = remindCount == null ? 0 : remindCount.first;
        int todoNotReadCount = todoCount == null ? 0 : todoCount.first;
        int appendWorkItemsNotReadCount = appendWorkItemsRemindInfo == null ? 0 : appendWorkItemsRemindInfo.first;
        //需要同时判断CRM提醒和待办两个未读消息数量，另外还需判断追加的工作提醒列表的item飘数
        boolean isShow = remindNotReadCount > 0 || todoNotReadCount > 0 || appendWorkItemsNotReadCount > 0;
        notifyRemindIcon(isShow, TAB_CRM_ACCESS_KEY);
    }

    /**
     * 检查草稿箱是否需要显示
     */
    public void checkDraftRemind() {
        new Thread() {
            @Override
            public void run() {
                synchronized(DraftManager.class) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            notifyMeRemindIcon("true".equals(Accounts.getCache(MyActivity.SHOW_RED_DIAN)), "DRAFT");
                        }
                    });
                }
            }
        }.start();
    }

    private void checkSettingsRemind() {
        notifyMeRemindIcon(SettingUtils.isShowNotifyAtSettingsEntry(this), SettingActivity.ACTION_CHANGE_POS);
    }

    private void checkNetDiskFileDownChangePosRemind() {
        notifyMeRemindIcon(DownloadFileCtrler.getInstance().isDownloadFileOkSp(), "netdisk_file_ok");
    }

    /**
     * 显示草稿箱的按钮
     */
    private void show() {
        if (remindDraft != null) {
            if (!TextUtils.isEmpty(remindDraft.getText())) {
                String mCurrentTab = tabHost.getCurrentTabTag();
                if (TAB_CRM_ACCESS_KEY.equalsIgnoreCase(mCurrentTab) || TAB_FUNCTION_ACCESS_KEY.equalsIgnoreCase(mCurrentTab) || TAB_MSG_ACCESS_KEY
                        .equalsIgnoreCase(mCurrentTab) || TAB_HOME_ACCESS_KEY.equalsIgnoreCase(mCurrentTab)) {
                    draftLayout.setVisibility(View.VISIBLE);
                    if (!FunctionSP.isShowState("draft_event_show_key")) {
                        showDraftGuideDialog(draftLayout, R.drawable.guidance_unsend_message);
                        FunctionSP.saveShowState("draft_event_show_key", true);
                    }
                } else {
                    draftLayout.setVisibility(View.INVISIBLE);
                }
            } else {
                if (draftLayout != null) {
                    draftLayout.setVisibility(View.INVISIBLE);
                }
                if (popupWindow != null) {
                    popupWindow.dismiss();
                }
            }
        }
    }
    @Override
    public String getTabCurrentClassName(){
        try {
            Intent intent = mIntentsInTabhost.get(tabHost.getCurrentTabTag());
            return intent.getComponent().getClassName();
        }catch (Exception e){
            FCLog.e(TAG,Log.getStackTraceString(e));
        }
        return null;
    }

    private void showDraftLayout() {
        // draftLayout.setVisibility(View.VISIBLE);
        show();
        draftLayout.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivityByAnim(new Intent(MainTabActivity.this, DraftActivity.class));
            }
        });
    }

    /**
     * 更新草稿箱提醒数
     *
     * @param remindTextView
     * @param count
     */
    public final void showDraftRemind(TextView remindTextView, int count) {
        remindTextView.setText(count > 99 ? "N" : String.valueOf(count));
        remindTextView.setVisibility(count > 0 ? View.VISIBLE : View.INVISIBLE);
    }

    /**
     * 引导图变量
     */
    protected PopupWindow popupWindow = null;

    public void showDraftGuideDialog(final View view, int res) {
        if (popupWindow != null && popupWindow.isShowing()) {
            popupWindow.dismiss();
        }
        LayoutInflater layoutInflater = (LayoutInflater) getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View layout = layoutInflater.inflate(R.layout.draft_guide_dialog, null);
        // 原图res(guidance_unsend_message)是582*111 控制包裹其的view大小为
        // xxdpi:(291dp,55dp) 其他:(194dp,37dp)时显示位置比较合适
        int popDimenWidth = (int) (App.intScreenWidth * 0.75);
        int popDimenHeight = (int) (popDimenWidth * 0.2);
        popupWindow = new PopupWindow(layout, popDimenWidth, popDimenHeight);
        ImageView iv = (ImageView) layout.findViewById(R.id.ivGuide);
        LayoutParams lp = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
        iv.setLayoutParams(lp);
        iv.setImageResource(res);
        int pos[] = {-1, -1}; // 保存当前坐标的数组
        view.getLocationOnScreen(pos);
        if (!popupWindow.isShowing()) {
            handlerPopupWindow();
            popupWindow.update();
            int offsetX = FSScreen.dip2px(20);// (int) (popWidth * 45 / 582 +
            // viewWidth * 36 / 240);
            int offsetY = App.intScreenHeight - FSScreen.dip2px(80 + 65 + 20);
            popupWindow.showAtLocation(view, Gravity.NO_GRAVITY, offsetX, offsetY);
        }
    }

    public void handlerPopupWindow() {
        popupWindow.setAnimationStyle(android.R.style.Animation_Dialog);
        popupWindow.setTouchable(true);
        popupWindow.setOutsideTouchable(true);
        popupWindow.setFocusable(true);
        popupWindow.setBackgroundDrawable(new BitmapDrawable());
        popupWindow.setInputMethodMode(PopupWindow.INPUT_METHOD_NOT_NEEDED);
        View v = popupWindow.getContentView();
        if (v != null) {
            v.setOnTouchListener(new OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    popupWindow.dismiss();
                    return false;
                }
            });
        }
    }

    /**
     * 展示广告图
     */
    private void showAd() {
        // 广告图在maintab页面展示，在此处判断下是否需要显示
        if (!Accounts.getBoolean(App.getInstance(), "isNeedShowAd")) {
            return;
        }
        // 下载广告图的弹框内部不建议进行图片下载，易造成图片回收的不同步而崩溃
        final String fileName = AccountManager.getAccount().getAdvertisePictureKey();// 广告图片Key
        if (new File(SyncImageLoader.getImageFilePath2(fileName)).exists()) {
            showAdInMain();
        } else {
            downloadAdPicToshow(fileName);
        }
    }

    private void showAdInMain() {
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                AdvertisementDialog adDialog = new AdvertisementDialog(MainTabActivity.this);
                adDialog.show();
                Accounts.putBoolean(App.getInstance(), "isNeedShowAd", false);
            }
        }, 500);
    }

    private void downloadAdPicToshow(final String fileName) {
        FileService.GetAdvertisementPicture(fileName, new WebApiDownloadFileCallback() {
            public void completed(byte[] content, String type) {
                if (content != null) {
                    saveAdPic(content, fileName);
                    showAdInMain();
                }
            }
        });
    }

    private void saveAdPic(byte[] content, String fileName) {
        final BitmapFactory.Options options = new BitmapFactory.Options();
        options.inPreferredConfig = Bitmap.Config.RGB_565;
        if (content != null) {
            Bitmap bmp = BitmapFactory.decodeByteArray(content, 0, content.length, options);
            if (bmp != null) {
                SyncImageLoader.writeBitmapToFile(fileName, bmp);
            }
        }
    }

    DemonstrationDialog mSwitchInfoDialog = null;

    /**
     * 展示进入模拟帐号的提示图
     *
     * @param pImageIndex 取值
     *                    ：1 选择demonstration1（直接进入体验帐号的图） ；2 选择demonstration2（从正式帐号切入的图）
     */
    private void showDemonstration(final int pImageIndex) {
        hideControlButton();
        mSwitchInfoDialog = new DemonstrationDialog(this, pImageIndex);
        mSwitchInfoDialog.setOnDismissListener(new OnDismissListener() {

            @Override
            public void onDismiss(DialogInterface dialog) {
                resumeControlButton(pImageIndex);
            }
        });
        mSwitchInfoDialog.show();
    }

    public void resumeControlButton(int pIndex) {
        if(pIndex == 1|| pIndex==2){
//            btnSwitchExperienceRole.setVisibility(View.VISIBLE);//需联网检查一下，有数据再显示
            checkIndustryInfo2ShowSwitchBtn();
//            int account_type = AccountManager.getAccount().getAccountType();
//            switch (account_type) {
//                case AccountType.ACCOUNT_EXPERIENCE:
//                    btnMainRegister.setVisibility(View.VISIBLE);
//                    break;
//                case AccountType.ACCOUNT_EXPERIENCE_FROM_OFFICIAL:
//                    btnMainQuit.setVisibility(View.VISIBLE);
//                    break;
//            }
        }else{
            hideControlButton();
        }
        /*if (pIndex == 1) {
            btnMainQuit.setVisibility(View.VISIBLE);

            //            btnMainRegister.setVisibility(View.VISIBLE);
        } else if (pIndex == 2) {
            btnMainLeft.setVisibility(View.VISIBLE);
        } else {
            hideControlButton();
        }*/
    }
    private void checkIndustryInfo2ShowSwitchBtn() {
        IGetIndustriesLis industriesLis = new IGetIndustriesLis() {
            @Override
            public void onSuccess(IndustryResultsVo resultsVo) {
                if (resultsVo != null && ExperienceLoginUtils.hasSwitchIndustryOrRole(resultsVo.getIndustryVoList())) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnSwitchExperienceRole.setVisibility(View.VISIBLE);
                        }
                    });
                }else{
                    FCLog.i(TAG,"checkIndustryInfo2ShowSwitchBtn resultsVo get failed with null");
                }
            }

            @Override
            public void onFailed(String errorInfo) {
                FCLog.i(TAG,"checkIndustryInfo2ShowSwitchBtn failed with "+errorInfo);
            }
        };
        LoginServices.reqGetIndustryList(industriesLis);
    }
    public void hideControlButton() {
//        btnMainLeft.setVisibility(View.GONE);
//        btnMainQuit.setVisibility(View.GONE);
//        btnMainRegister.setVisibility(View.GONE);
        btnSwitchExperienceRole.setVisibility(View.GONE);
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        if (mTabListSave == null) {
            mTabListSave = new ArrayList<MainTabActivity.ButtonState>();
        }
        saveButtonState(mTabMap, mTabListSave);
        outState.putParcelableArrayList("tab_map_key", mTabListSave);
        super.onSaveInstanceState(outState);
    }

    private void saveButtonState(HashMap<String, ButtonHolder> mTabMap, ArrayList<ButtonState> btnList) {
        btnList.clear();
        Set<String> keySet = mTabMap.keySet();
        for (String key : keySet) {
            ButtonHolder bh = mTabMap.get(key);
            ButtonState bs = new ButtonState();
            bs.isSelected = bh.isSelected;
            bs.remindCount = bh.remindCount;
            bs.key = key;
            btnList.add(bs);
        }
    }


    private void changeDefaultTabHost(int index){
        if(mAppConfig!= null && mAppConfig.getDefaultMenuIndex() !=0){
            Field mCurrentTab = null;
            try {
                mCurrentTab = tabHost.getClass()
                        .getDeclaredField("mCurrentTab");
                mCurrentTab.setAccessible(true);
                mCurrentTab.setInt(tabHost, index);
            } catch (SecurityException e) {
                e.printStackTrace();
            } catch (IllegalArgumentException e) {
                e.printStackTrace();
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    /**
     * ********************************页签设置开始***************************
     */

    public void initMenuTab() {

        tabHost = getTabHost();


        HostInterface hi=HostInterfaceManager.getHostInterface();
        if (hi==null){
            return;
        }
        if (mAppConfigManager == null) {
            mAppConfigManager = new AppConfigManager(MainTabActivity.this);
            mAppConfigPersist = new AppConfigPersist(MainTabActivity.this);
            mAppConfig = mAppConfigPersist.getConfigData();
        }
        changeDefaultTabHost(0);//取消tabhost默认加载第一个tab

        boolean hasConfig = mAppConfig != null && mAppConfig.hasMenu();
        if (hasConfig) {
            mAppConfigRenderer = new AppConfigRenderer(MainTabActivity.this, mAppConfig);
        }
        if (hasConfig){
            try {
                initMenuTabConfig();
                return;
            }catch (Exception e){
                e.printStackTrace();
            }

        }
        FCTimePoint.start("initMenuTab");
        // Msg
//        if (hasconfig){
//            sourceIt=hi.getPlugIntent(instance, null);
//            sourceIt.setAction(mAppConfigData.getMenu(defaultMenu).getString("action"));
//        }else{
//            hi.getPlugIntent(instance, ShortMessageMainActivity.class);
//        }
        Intent sourceIt = hi.getPlugIntent(this, ShortMessageMainActivity.class);
        Intent it = getPlugIntent(sourceIt);
        mIntentsInTabhost.put(TAB_MSG_ACCESS_KEY, it);
        TabSpec moreSpec = tabHost.newTabSpec(TAB_MSG_ACCESS_KEY).setIndicator(TAB_MSG_ACCESS_KEY).
                setContent(it);
        tabHost.addTab(moreSpec);

        // HOME
//        sourceIt = hi.getPlugIntent(instance, HomeActivity.class);
        sourceIt = hi.getPlugIntent(this, WorkHomeActivity.class);
        it = getPlugIntent(sourceIt);
        mIntentsInTabhost.put(TAB_HOME_ACCESS_KEY, it);
        TabSpec homeSpec = tabHost.newTabSpec(TAB_HOME_ACCESS_KEY).setIndicator(TAB_HOME_ACCESS_KEY).
                setContent(it);
        tabHost.addTab(homeSpec);
        // CRM
        sourceIt = hi.getPlugIntent(this,(Class<?>) null);
        sourceIt.setAction(CrmHome.home);
        sourceIt.setPackage(getPackageName());
        it = getPlugIntent(sourceIt);
        mIntentsInTabhost.put(TAB_CRM_ACCESS_KEY, it);
        TabSpec tab_Crm = tabHost.newTabSpec(TAB_CRM_ACCESS_KEY).setIndicator(TAB_CRM_ACCESS_KEY).
                setContent(it);
        tabHost.addTab(tab_Crm);
        // REMIND
        sourceIt = hi.getPlugIntent(this, AppCenterActivity.class);
        it = getPlugIntent(sourceIt);
        mIntentsInTabhost.put(TAB_FUNCTION_ACCESS_KEY, it);
        TabSpec remind_Spec = tabHost.newTabSpec(TAB_FUNCTION_ACCESS_KEY).setIndicator(TAB_FUNCTION_ACCESS_KEY).
                setContent(it);
        tabHost.addTab(remind_Spec);

        if (SettingUtils.isOpenOldMyPage()) {
            sourceIt = hi.getPlugIntent(instance, MyActivity.class);
        } else {
            sourceIt = WeexIntentUtils.buildIntent("bundle://qixin/personal_info");
        }

        it = getPlugIntent(sourceIt);
        mIntentsInTabhost.put(TAB_PERSON_INFO_ACCESS_KEY, it);
        TabSpec tab_Spec = tabHost.newTabSpec(TAB_PERSON_INFO_ACCESS_KEY).setIndicator(TAB_PERSON_INFO_ACCESS_KEY).
                setContent(it);
        tabHost.addTab(tab_Spec);
        tabHost.setOnTabChangedListener(this);

        LinearLayout tabLayout = (LinearLayout) findViewById(R.id.tabLayout);

        tabLayout.addView(createContentTabLayout());
        FCTimePoint.end("initMenuTab");
    }

    public void initMenuTabConfig() {
        FCTimePoint.start("initMenuTabConfig");
        FsUrlUtils.setIsNewOutDoorActivity(OutDoorV2Utils.isNewOutDoorActivity());
        HostInterface hostInterface = HostInterfaceManager.getHostInterface();
        boolean preload_start=false;
//        //test begin
//        AppConfig.MenuItem msgItem = new AppConfig.MenuItem();
//        msgItem.imgStyle = "";
//        msgItem.title = "企信";
//        msgItem.titleStyle = "title1";
//        msgItem.action = "fs://app/message";
//        msgItem.accessKey = "message";
//        mAppConfig.menu.items.add(msgItem);
//        if(FSContextManager.getCurUserContext().getAccount().isVisitorLogin()){
//            AppConfig.MenuItem msgItem = new AppConfig.MenuItem();
//            msgItem.imgStyle = "crossMessage";
//            msgItem.title = I18NHelper.getText("lib.enumdef.text.connected_enterprise","互联企信");
//            msgItem.titleStyle = "title1";
//            msgItem.needChangeIconColor = true;
//            //"fs://app/crossMessage";
//            msgItem.action = "fs://app/crossMessage";
//            //"crossMessage";
//            msgItem.accessKey = SessionInfoUtils.App_Menu_Item_Access_Key_For_Cross_Message_List;
//            if (mAppConfig.menu.items == null) {
//                mAppConfig.menu.items = new ArrayList<>();
//            }
//            mAppConfig.menu.items.add(0, msgItem);
//            if (mAppConfig.style == null) {
//                mAppConfig.style = new HashMap<>();
//            }
//            if (mAppConfig.style.get("crossMessage") == null) {
//                JSONObject icon0JsonObject = new JSONObject();
////                icon0JsonObject.put("background", "https://a9.fspage.com/FSR/app-config/resource/icon/App/MessageApp_tab.svg");
//                icon0JsonObject.put("background", "https://a9.fspage.com/FSR/weex/uipaas/images/cross_message_selected.svg");
//                JSONObject icon0SelectedJsonObject = new JSONObject();
////                icon0SelectedJsonObject.put("background", "https://a9.fspage.com/FSR/app-config/resource/icon/App/MessageApp_tab.svg");
//                icon0SelectedJsonObject.put("background", "https://a9.fspage.com/FSR/weex/uipaas/images/cross_message_selected.svg");
//                mAppConfig.style.put("crossMessage", icon0JsonObject);
//                mAppConfig.style.put("crossMessage.selected", icon0SelectedJsonObject);
//            }
//
////            AppConfig.MenuItem msgItem2 = new AppConfig.MenuItem();
////            msgItem2.imgStyle = "crossMessage";
////            msgItem2.title = "测试企信";
////            msgItem2.titleStyle = "title1";
////            msgItem2.needChangeIconColor = true;
////            //"fs://app/crossMessage";
////            msgItem2.action = "fs://app/MessagecrossMessage2";
////            //"crossMessage";
////            msgItem2.accessKey = "crossMsg2";
////            if (mAppConfig.menu.items == null) {
////                mAppConfig.menu.items = new ArrayList<>();
////            }
////            mAppConfig.menu.items.add(msgItem2);
////            if (mAppConfig.style == null) {
////                mAppConfig.style = new HashMap<>();
////            }
////            if (mAppConfig.style.get("crossMsg2") == null) {
////                JSONObject icon0JsonObject = new JSONObject();
////                icon0JsonObject.put("background", "https://a9.fspage.com/FSR/app-config/resource/icon/App/MessageApp_tab.svg");
////                JSONObject icon0SelectedJsonObject = new JSONObject();
////                icon0SelectedJsonObject.put("background", "https://a9.fspage.com/FSR/app-config/resource/icon/App/MessageApp_tab.svg");
////                mAppConfig.style.put("crossMsg2", icon0JsonObject);
////                mAppConfig.style.put("crossMsg2.selected", icon0SelectedJsonObject);
////            }
//        }
        //        //test end
        List<String> matchSchemeList = new ArrayList();
        try{
            matchSchemeList = HostInterfaceManager.getCloudCtrlManager().getArrayConfig("appStartPreloadMatchScheme", String.class);
            FCLog.i("appStartPreloadMatchScheme", " cloud control appStartPreloadMatchScheme:  " + matchSchemeList.toString());
        }catch (Exception e){
            FCLog.i("appStartPreloadMatchScheme", " cloud control appStartPreloadMatchScheme exception: " + e.getMessage() );
        }

        for (AppConfig.MenuItem menuItem : mAppConfig.menu.items) {//cml://uipaas/appCustom 工作台
            boolean isUseNativie = AppCustomUtils.isUseNative();
            FCLog.e("appcustom", "initMenuTabConfig action: " + menuItem.action+",isUseNativie:"+isUseNativie);
            String action = menuItem.action;

            if(!preload_start){
                boolean matchScheme = false;
                if(matchSchemeList != null){
                    for(int i = 0; i < matchSchemeList.size(); i++){
                        if(action != null && action.startsWith(matchSchemeList.get(i))){
                            matchScheme = true;
                            break;
                        }
                    }
                }
                if(matchScheme){
                    FCLog.i("appStartPreloadMatchScheme", " menu item match scheme : " + action);
                    preload_start=true;
                    SharedPreferences sp1 = this.getSharedPreferences("TestSetting", Activity.MODE_PRIVATE);
                    sp1.edit().putString(AccountManager.getAccount().getEmployeeAccount()+"_"+
                            AccountManager.getAccount().getEmployeeId()+"_"+
                            "ava_preload_process_start","uipaas_custom").commit();
                }else{
                    FCLog.i("appStartPreloadMatchScheme", " menu item  doesn't match scheme : " + action);
                }

            }
            if(isUseNativie && action!=null && (action.startsWith("cml://uipaas/appCustom")
                    ||action.startsWith("ava://uipaas_custom/pages/appcustom/appcustom"))){
                int pos = action.indexOf("?");
                if(pos!=-1){
                    action = "native://com.facishare.fs.biz_function.subbiz_outdoorsignin.appcustom.AppCustomActivity"+action.substring(pos,action.length());;
                }
            }
            HashMap params = new HashMap();
            params.put("__isFromTab",true);
            params.put("__mainTabFullScreen",ImmerseLayoutUtil.isMenuPageFullScreen());
            params.put("__mainTabHeight", 56);
            Intent intent = FsUrlUtils.buildIntent(this, action,params);
            if (intent != null) {
                intent = hostInterface.getPlugIntent(this, intent);
                intent = getPlugIntent(intent);
            }
            String tag = menuItem.getMenuItemId();;
            if (intent != null && !TextUtils.isEmpty(menuItem.rightActions)) {
                FCLog.e("appcustom", "rightActions: " + menuItem.rightActions);
                intent.putExtra(AppConfigConstants.MenuKey.RIGHT_ACTIONS, menuItem.rightActions);
            }
            //将当前页面的互联身企业和appid信息传递给下一页面：
            // 1.先确保传递的登录过程中保存的身份信息）
            SandboxUtils.transEaAppId(getIntent(), intent);
            if (AccountManager.isVisitorLogin(this)) {
                FCLog.w("appcustom", "sandbox_ea_appid : " + intent.getStringExtra(SandboxContextManager.Sandbox_EA_APPID)
                        + " appid: " + intent.getStringExtra(SandboxContextManager.Sandbox_APPID));
            }
            /*//2.从自定义的app链接中解析要传递的互联身份信息（2比前面的1优先级高） 经过和丽敏讨论确定，链接上的参数比较业务化，不做特殊解析传递；
            //            String queryParams = AvatarSchemeUtils.getQuerys(action);
            if (queryParams != null && queryParams.contains("{") && queryParams.contains("}")) {
                if (queryParams.contains("\\")) {
                    queryParams = queryParams.replace("\\", "");
                }
                HashMap<String, Object> jsonToMap = BaseIntentUtils.parseJsonToMap(queryParams);
                if (jsonToMap != null && jsonToMap.containsKey(SandboxContextManager.Sandbox_APPID)) {
                    String appId = (String) jsonToMap.get(SandboxContextManager.Sandbox_APPID);
                    String upstreamEa = getIntent().getStringExtra(SandboxContextManager.Sandbox_EA);
                    intent.putExtra(SandboxContextManager.Sandbox_APPID, appId);
                    intent.putExtra(SandboxContextManager.Sandbox_EA_APPID, SandboxUtils.joinEaAppId(upstreamEa, appId));
                }
            }*/
            if (!checkAppConfigIntentExist(intent, tag, action)) {
                intent = FsUrlUtils.getUpdateActIntent();
            }
            mIntentsInTabhost.put(tag, intent);

            TabSpec moreSpec = tabHost.newTabSpec(tag).setIndicator(tag).setContent(intent);
            tabHost.addTab(moreSpec);
        }
        tabHost.setOnTabChangedListener(this);
        LinearLayout tabLayout = (LinearLayout) findViewById(R.id.tabLayout);

        tabLayout.addView(createContentTabLayout());
        int defaultMenuIndex = mAppConfig.getDefaultMenuIndex();
        changeDefaultTabHost(-1);//mCurrentTab恢复到－1状态

        if (appCustomerSelectTabIndex!=-1){
            defaultMenuIndex = appCustomerSelectTabIndex;
        }

        if (defaultMenuIndex!=0){
            tabHost.setCurrentTab(defaultMenuIndex);
        }else{
            tabHost.setCurrentTab(0);
        }
        FCTimePoint.end("initMenuTabConfig");
    }

    private boolean checkAppConfigIntentExist(Intent intent,String  accessKey, String action) {
        try{
            FCTimePoint.start("initMenuTabConfig checkExist");
            ResolveInfo ri = this.getApplicationContext().getPackageManager().resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY);
            if (ri == null) {
                FCTimePoint.end("initMenuTabConfig checkExist");
                FCLog.w("appcustom", "initMenuTabConfig checkExist result ResolveInfo is null with accessKey:"+accessKey+",action:"+ action);
                //                OpenPlatformUtils.OpenPlatformVO vo = OpenPlatformUtils.extractJson(url);
//                ret = OpenPlatformUtils.createIntent(vo);
//                appendDataToIntent(params, ret);
            } else {
                FCTimePoint.end("initMenuTabConfig checkExist");
                return true;
            }
        }catch (Exception e){
            FCLog.w("appcustom",Log.getStackTraceString(e));
            return true;
        }
        return false;
    }

    public void renderNode(ImageView imageView, JSONObject nodeItem) {
        if (mAppConfigRenderer != null) {
            mAppConfigRenderer.renderImageByNode(imageView, nodeItem);
        }
    }

    @Override
    public void onTabChanged(String tabId) {
//        mIgnoreMultitouch = true;
        View currentView = tabHost.getCurrentView();
        FCLog.i(TAG, "onTabChanged tabId= " + tabId + ", width= " + currentView.getWidth()
                + ",height="+currentView.getHeight());
        if (currentView.getMeasuredWidth() == 0 || currentView.getMeasuredHeight() == 0) {
            getWindow().getDecorView().requestLayout();
        }

        if (isDebug) {
            try {
                Method m_debug = View.class.getDeclaredMethod("debug");
                m_debug.setAccessible(true);
                m_debug.invoke(currentView);
            } catch (IllegalAccessException e) {
                //ignore
            } catch (NoSuchMethodException e) {
                //ignore
            } catch (InvocationTargetException e) {
                //ignore
            }
        }
    }

    public Intent getPlugIntent(Intent srcit) {
        srcit.putExtra(FCBaseActivity.Intent_flag_isFromTab, true);
        return srcit;
    }
    //    private static final int MenuKey = R.id.key;
    public View createContentTabLayout() {
        LinearLayout lay = new LinearLayout(this);
        lay.setLayoutParams(new LayoutParams(android.view.ViewGroup.LayoutParams.FILL_PARENT,
                android.view.ViewGroup.LayoutParams.FILL_PARENT));
        lay.setOrientation(LinearLayout.HORIZONTAL);
//        lay.setBackgroundColor(Color.TRANSPARENT);

        lay.setBackgroundColor(Color.WHITE);
        if (mAppConfig != null && mAppConfig.hasMenu()) {
            int defaultMenuIndex = mAppConfig.getDefaultMenuIndex();
            if (appCustomerSelectTabIndex!=-1){
                defaultMenuIndex = appCustomerSelectTabIndex;
            }
            for (int index = 0; index < mAppConfig.getMenuSize(); ++index) {
                AppConfig.MenuItem menuItem = mAppConfig.getMenu(index);
                boolean isSelected = index == defaultMenuIndex;
                //在compostLayout内部会按如下方式校正app自定义时的选中状态，有重复，故注释掉重复逻辑；
//                if (mAppConfig != null && mAppConfig.hasMenu()) {
//                    int defaultTab = mAppConfig.getDefaultMenuIndex();
//                    AppConfig.MenuItem menuItem = mAppConfig.getMenu(defaultTab);
//                    if (menuItem != null && TextUtils.equals(key, menuItem.title)) {
//                        holder.isSelected = true;
//                    }
//                }
                View v = createAppConfigTabItemLayout(menuItem, "", "", isSelected);
                v.setTag(R.id.frameLayout, menuItem);
                saveCrmView(menuItem, v);
                lay.addView(v);
            }
        }else {
            lay.addView(
                    createDefaultTabItemLayout(TAB_MSG_ACCESS_KEY, I18NHelper.getText("xt.x_person_activity_copy.text.qixin")/* 企信 */, "assets://fs_qixin_nor.svg", "assets://fs_qixin_sel.svg",
                            true));
            lay.addView(createDefaultTabItemLayout(TAB_HOME_ACCESS_KEY, I18NHelper.getText("xt.tag_layout.text.work")/* 工作 */, "assets://fs_work_nor.svg", "assets://fs_work_sel.svg", false));
            mCrmView = createDefaultTabItemLayout(TAB_CRM_ACCESS_KEY, I18NHelper.getText("xt.tag_layout.text.crm")/* CRM */, "assets://fs_crm_nor.svg", "assets://fs_crm_sel.svg", false);
            lay.addView(mCrmView);
            lay.addView(
                    createDefaultTabItemLayout(TAB_FUNCTION_ACCESS_KEY, I18NHelper.getText("common.main_tab.entries_des.app")/* 应用 */, "assets://fs_app_nor.svg", "assets://fs_app_sel.svg",
                            false));
            lay.addView(createDefaultTabItemLayout(TAB_PERSON_INFO_ACCESS_KEY, I18NHelper.getText("weex.qixin.me")/* 我 */, "assets://fs_me_nor_new.svg", "assets://fs_me_sel_new.svg", false));

        }
//        setCrmEntryVisiableState();

        return lay;
    }
    //保存当前主频道中crm对应的view，以方便控制crm频道Tab的显示与隐藏
    private void saveCrmView(AppConfig.MenuItem menuItem, View currentTagView) {
        boolean isCurrentCrmItem = false;//当前是否crm的频道数据
        if (!TextUtils.isEmpty(menuItem.accessKey)) {
            isCurrentCrmItem = isMatchCrmAccessKey(menuItem.accessKey);
        }
        if (isCurrentCrmItem) {
            mCrmView = currentTagView;
        }
    }

    JSONObject mCrmAccessKeyList;//暂存从云控拉取（或默认构建）的判断crm的条件

    private boolean isMatchCrmAccessKey(String accessKey) {
        try {
            if (mCrmAccessKeyList == null) {
                mCrmAccessKeyList = HostInterfaceManager.getCloudCtrlManager().getObjConfig("fsappcustom_crm_new_rule", JSONObject.class);
                if (mCrmAccessKeyList == null) {
                    mCrmAccessKeyList = new JSONObject();
                    JSONArray jsonArray = new JSONArray();
                    jsonArray.add("fs.crm");
                    jsonArray.add("CRM");
                    mCrmAccessKeyList.put("list", jsonArray);
                }
            }
            if (mCrmAccessKeyList != null) {
                JSONArray crmList = mCrmAccessKeyList.getJSONArray("list");
                boolean isCrmItemMatched = false;
                if (crmList != null && crmList.size() > 0) {
                    for (Object item : crmList) {
                        if (item != null && item.equals(accessKey)) {
                            isCrmItemMatched = true;
                            break;
                        }
                    }
                }
                return isCrmItemMatched;
            }
        } catch (Exception e) {
            FCLog.e(TAG, Log.getStackTraceString(e));
        }
        return false;
    }

    /**
     * 显示Crm的tab入口
     */
    private void showCrmEntry() {
        if (mCrmView != null) {
            mCrmView.setVisibility(View.VISIBLE);
        }
        if (mAppConfig != null && mAppConfig.hasMenu()) {
            String accessKey = mAppConfig.getMenu(mAppConfig.getDefaultMenuIndex()).accessKey;
            if (TAB_CRM_ACCESS_KEY.equals(accessKey)) {
                selectTab(TAB_CRM_ACCESS_KEY, mTabMap);
                return;
            }
        }
    }

    /**
     * 隐藏Crm的tab入口
     */
    private void hideCrmEntry() {
        if (mCrmView != null) {
            mCrmView.setVisibility(View.GONE);
        }

        String mCurrentTab = tabHost.getCurrentTabTag();
        if (TAB_CRM_ACCESS_KEY.equalsIgnoreCase(mCurrentTab)) {
            if (mAppConfig != null && mAppConfig.hasMenu()) {
                for (AppConfig.MenuItem menuItem : mAppConfig.menu.items) {
                    if (!TAB_CRM_ACCESS_KEY.equals(menuItem.accessKey)) {
                        selectTab(menuItem.accessKey, mTabMap);
                        return;
                    }
                }
            } else {
                selectTab(TAB_MSG_ACCESS_KEY, mTabMap);
            }
        }
    }

    /**
     * 依据版本，设置CRM入口是否显示
     */
    private void setCrmEntryVisiableState() {
        EmployeeEditionType type = EmployeeEditioinUtils.getCurrentVersionType();
        switch (type) {
            case MARKET_VERSION:
            case TRIAL_VERSION:
                showCrmEntry();
                break;
            case WORK_VERSION:
            case UNKNOW_VERSION:
            default:
                hideCrmEntry();
        }
    }

    public ButtonState getButtonStateByKey(String key, ArrayList<ButtonState> lists) {
        if (lists != null) {
            for (ButtonState bs : lists) {
                if (bs.key.equalsIgnoreCase(key)) {
                    return bs;
                }
            }
        }
        return null;
    }


    //构建默认的底部主频道按钮布局（区别于通过app自定义接口获取的数据）
    public View createDefaultTabItemLayout(final String key, final String s, String norsvg, String selsvg, boolean isSelected) {
        return preCompostLayout(key, s, null, norsvg, selsvg, isSelected, null, null,false, null);
    }
    //依据app自定义接口返回的menuItem数据构建底部tab
    public View createAppConfigTabItemLayout(AppConfig.MenuItem menuItem, String norsvg, String selsvg, boolean isSelected) {
        String key = menuItem.getMenuItemId();
        View v = preCompostLayout(key, menuItem.title, menuItem.appId,
                norsvg, selsvg, isSelected, menuItem.titleStyle, menuItem.imgStyle, menuItem.needChangeIconColor, menuItem.defaultColor);
        return v;
    }

    private View preCompostLayout(final String key, final String s, String appId, String norsvg, String selsvg, boolean isSelected, String textstyle, String imgstyle,
                                                 boolean needChangeIconColor, String defaultColor) {
        ButtonState bs = getButtonStateByKey(key, mTabListSave);
        ButtonHolder holder = new ButtonHolder();
        if (bs != null) {//依据页面被杀前缓存的数据校正选中和飘数信息
            FCLog.i(LogUtilCommon.debug_fs,
                    "main tab activity compostLayout from saved, tab-" + key + " count-" + bs.remindCount);
            isSelected = bs.isSelected;
            holder.isSelected = bs.isSelected;
            holder.remindCount = bs.remindCount;
        }
        holder.needChangeIconColor = needChangeIconColor;
        holder.defaultColor = defaultColor;
        mTabMap.put(key, holder);
        return compostLayout(key, s, appId, norsvg, selsvg, isSelected, holder, textstyle, imgstyle);
    }

    public View compostLayout(final String key, final String s, String appId, String norsvg, String selsvg, boolean isSelected,
                              ButtonHolder holder, String textstyle, String imgstyle) {
        LinearLayout layout = (LinearLayout) getLayoutInflater().inflate(R.layout.tag_layout, null);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(android.view.ViewGroup.LayoutParams.FILL_PARENT,
                android.view.ViewGroup.LayoutParams.FILL_PARENT);
        lp.gravity = Gravity.CENTER_VERTICAL;
        lp.weight = 1;
        layout.setLayoutParams(lp);
        layout.setGravity(Gravity.CENTER);
        layout.setContentDescription("tab_"+key);
        holder.layout = layout;
        holder.tabImage = (ImageView) layout.findViewById(R.id.tabImage);
        holder.tabImagev5 = (ImageView) layout.findViewById(R.id.tabImagev5);
        holder.tabText = (TextView) layout.findViewById(R.id.tabText);
        holder.tabRemind = (TextView) layout.findViewById(R.id.tabRemind);
        // 注意：tabRemindIcon设置不可见时使用INVISIBLE，不要使用GONE，智能运营小红点会在此View基础上做显示处理
        holder.tabRemindIcon = layout.findViewById(R.id.tabRemindIcon);
        holder.isSelected = isSelected;
        holder.defaultsvg = norsvg;
        holder.selectsvg = selsvg;

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            holder.tabImage.setVisibility(View.GONE);
            holder.tabImage = holder.tabImagev5;
        } else {
            holder.tabImagev5.setVisibility(View.GONE);
        }
        holder.tabText.setText(s);

        holder.text = s;
        holder.tab = key;
        holder.titleStyle = textstyle;
        holder.imgStyle = imgstyle;
        layout.setTag(holder);

        updateTabItemDisplay(holder);

        if (TextUtils.isEmpty(appId)) {
            appId = getButtonID(key);
        }
        DynamicBizOpNode bizOpNode = new DynamicBizOpNode(appId);
        bizOpNode.initDynamicViewRender(layout, holder.tabRemindIcon);
        // 与原有tabRemindIcon红点位置保持一致8(红点的宽)
        bizOpNode.updateRedDotLocation(FSScreen.dp2px(this, 8), 0, RedGravity.LTOP);
        bizOpNode.register();
        mBizOpNodeList.add(bizOpNode);
        layout.setBackground(null);
        return layout;
    }

    /**
     *  更新底部主频道按钮布局的显示效果
     * @param holder
     */
    private void updateTabItemDisplay(ButtonHolder holder) {
        if (holder == null) {
            return;
        }
        boolean isSelected = holder.isSelected;
        if (mAppConfigRenderer != null) {//app自定义下发的按钮数据
            if (isSelected) {
                boolean changeTextColor = MainTabBottomItemUtils.renderTabTextColor(holder.tabText, true, true);
                if (!changeTextColor && !TextUtils.isEmpty(holder.titleStyle)) {
                    mAppConfigRenderer.renderText_selected(holder.tabText, holder.titleStyle);
                }
                mAppConfigRenderer.renderImage_selected(holder.tabImage, holder.imgStyle, holder.needChangeIconColor);
            } else {
                mAppConfigRenderer.renderText(holder.tabText, holder.titleStyle);
                mAppConfigRenderer.renderImage(holder.tabImage, holder.imgStyle, holder.defaultColor);
            }
        } else {//默认的tab按钮数据
            boolean changeTextColor = MainTabBottomItemUtils.renderTabTextColor(holder.tabText, isSelected, true);
            if (!changeTextColor) {
                holder.tabText.setTextColor(isSelected ? Color.parseColor("#FF8000") : Color.parseColor("#545861"));
            }
            String path = isSelected ? holder.selectsvg : holder.defaultsvg;
            MainTabBottomItemUtils.renderTabImageColor(holder.tabImage, path, isSelected, true);
        }
    }

    private List<DynamicBizOpNode> mBizOpNodeList = new ArrayList<>();

    private String getButtonID(String tabKey) {
        if (TAB_MSG_ACCESS_KEY.equals(tabKey)) {
            return "QX_Tab";
        } else if (TAB_HOME_ACCESS_KEY.equals(tabKey)) {
            return "XT_Tab";
        } else if (TAB_CRM_ACCESS_KEY.equals(tabKey)) {
            return "CRM_Tab";
        } else if (TAB_FUNCTION_ACCESS_KEY.equals(tabKey)) {
            return "AC_Tab";
        } else if (TAB_PERSON_INFO_ACCESS_KEY.equals(tabKey)) {
            return "PRO_Tab";
        } else {
            return "";
        }
    }

    long ztime = 0;
    long appcustomtime = 0;
    long appcustomclickCount = 0;
    int appCustomerSelectTabIndex = -1;
    int personInfoClickCount = 0;
    long personInfoClickTime = 0;
    public void onClickTab(View view) {
        AppConfig.MenuItem menu = (AppConfig.MenuItem) view.getTag(R.id.frameLayout);
        ButtonHolder holder = (ButtonHolder) view.getTag();
        if (holder == null) {
            FCLog.w(TAG, "view not attach ButtonHolder, onClickTab error!");
            return;
        }
        String clickTab = holder.tab;
        String currentTab = tabHost.getCurrentTabTag();
        String action = "";
        if (menu != null) {
            action = menu.action;
        } else {
            action = AppConfigRenderer.getPreparedTabActionByAccessKey(clickTab);
        }
        // 首页下各个tab的点击事件
        QixinStatisticsEvent.tick(QixinStatisticsEvent.APP_HOME_CLICK_TAB, action, FSDeviceID.getDeviceID(this));

        // 重复点击同一个菜单项
        boolean menuClickAgain = currentTab.equalsIgnoreCase(clickTab);
        if (TAB_MSG_ACCESS_KEY.equalsIgnoreCase(clickTab)) {
            if (!menuClickAgain) {
                checkShowDemoBtn();
            }
        } else {
            hideDemoBtn();
        }

        if (menuClickAgain) {
            // Tab菜单二次点击
            if (TAB_MSG_ACCESS_KEY.equalsIgnoreCase(clickTab)) {
                if (s_LastClickTime == 0) {
                    s_LastClickTime = System.currentTimeMillis();
                } else {
                    long cur = System.currentTimeMillis();
                    if ((cur - s_LastClickTime) < 1000) {
                        s_LastClickTime = 0;
                        ObservableResult or = new ObservableResult();
                        or.type = ObservableResultType.doubleClickQixin;
                        ObservableCenter.getInstance().notifyObservers(or);
                    } else {
                        s_LastClickTime = System.currentTimeMillis();
                    }
                }
            } else if (TAB_PERSON_INFO_ACCESS_KEY.equalsIgnoreCase(clickTab)) {
                personInfoClickCount++;
                if (personInfoClickTime == 0) {
                    personInfoClickTime = System.currentTimeMillis();
                } else {
                    long cur = System.currentTimeMillis();
                    if ((cur - personInfoClickTime) < 1000 && personInfoClickCount>=7) {
//                        ToastUtils.show(""+menu.title);
                        personInfoClickCount = 0;
                        personInfoClickTime = 0;
                        AboutActivity.startWithUnknownUser(this);
                    }else{
                        personInfoClickTime = System.currentTimeMillis();
                    }
                }
            } else if (TAB_HOME_ACCESS_KEY.equalsIgnoreCase(clickTab)) {
                if ((System.currentTimeMillis() - ztime) > 1000) {
                    ztime = System.currentTimeMillis();
                    toHomePageFromClick();
                }
            }else if(menu!=null && menu.action!=null && menu.action.contains("uipaas/appCustom")){
                appcustomclickCount++;
                if (appcustomtime == 0) {
                    appcustomtime = System.currentTimeMillis();
                } else {
                    long cur = System.currentTimeMillis();
                    if ((cur - appcustomtime) < 1000 && appcustomclickCount>2) {
//                        ToastUtils.show(""+menu.title);
                        appcustomclickCount = 0;
                        appcustomtime = 0;
                        replaceMenu(menu);
                    }else{
                        appcustomtime = System.currentTimeMillis();
                    }
                }
            }
        } else {
            // Tab菜单切换
            selectTab(clickTab, mTabMap);
            show();
            checkShowNpsPage(this);
        }

        // 发送Tab菜单点击事件
        PublisherEvent.post(new MainMenuClickEvent(clickTab, menuClickAgain));
        if (TAB_FUNCTION_ACCESS_KEY.equalsIgnoreCase(clickTab)) {
            appCenterTick();
        }
        MainTabLifeListenersManager.getInstance().triggerOnClickTab(currentTab, clickTab, menuClickAgain);
    }

//

    private void replaceMenu(AppConfig.MenuItem menu){
        if(AppCustomUtils.isCanSwtch()){

            boolean isUseNativie = !AppCustomUtils.isNative();
            AppCustomUtils.saveIsUseNative(isUseNativie);

            appCustomerSelectTabIndex=tabHost.getCurrentTab();
            LinearLayout tabLayout =  findViewById(R.id.tabLayout);
            tabLayout.removeAllViews();
            mTabMap.clear();
            tabHost.clearAllTabs();
            initMenuTab();
        }

    }

    /**
     * 检查体验帐号的操作按钮是否可以显示
     */
    private void checkShowDemoBtn() {
        int account_type = AccountManager.getAccount().getAccountType();
        if(AccountType.ACCOUNT_EXPERIENCE == account_type
                || AccountType.ACCOUNT_EXPERIENCE_FROM_OFFICIAL == account_type){
            hideControlButton();
            checkIndustryInfo2ShowSwitchBtn();
//            switch (account_type) {
//                case AccountType.ACCOUNT_EXPERIENCE:
//                    btnMainRegister.setVisibility(View.VISIBLE);
//                    break;
//                case AccountType.ACCOUNT_EXPERIENCE_FROM_OFFICIAL:
//                    btnMainQuit.setVisibility(View.VISIBLE);
//                    break;
//            }
        }
    }

    /**
     * 隐藏体验帐号的操作按钮
     */
    private void hideDemoBtn() {
        hideControlButton();
    }

    public void selectTab(String tagId, HashMap<String, ButtonHolder> map) {
        if (!map.isEmpty()) {
            Set<String> keys = map.keySet();
            for (String key : keys) {
                ButtonHolder holder = map.get(key);
                if (key!=null&&key.equals(tagId)) {
                    holder.isSelected = true;
                } else {
                    holder.isSelected = false;
                }
                updateTabItemDisplay(holder);
            }
        }
        try {
            tabHost.setCurrentTabByTag(tagId);
			
            Activity ctx =this.getLocalActivityManager().getCurrentActivity();
            if(ctx instanceof FsMPActivity  ||  ctx.getClass().getSimpleName().equals("AppCustomMultiWebViewActivity")){
                this.setIgnoreMultitouch(false);
            }else{
                this.setIgnoreMultitouch(true);
            }
        } catch (Exception e) {
            FCLog.w(TAG, Log.getStackTraceString(e));
        }
    }

    public void showDefaultTab(HashMap<String, ButtonHolder> map) {
        if (!map.isEmpty()) {
            Set<String> keys = map.keySet();
            for (String key : keys) {
                ButtonHolder holder = map.get(key);
                if (holder.isSelected) {
                    // tabHost.setCurrentTabByTag(holder.tab);
                    selectTab(holder.tab, map);
                    return;
                }
            }
        }
    }

    /**
     * *************************************************************************
     * ************************ 页签设置结束
     * ***************************************************************
     */

    public final static MainTabActivity getInstance() {
        return instance;
    }

    private void logout() {
        mydialog = new CommonDialog(this, new CommonDialog.myDiaLogListener() {

            public void onClick(View view) {
                int i = view.getId();
                if (i == R.id.button_mydialog_cancel) {
                    mydialog.dismiss();

                } else if (i == R.id.button_mydialog_enter) {
                    mydialog.dismiss();
                    final int accountType = AccountManager.getAccount().getAccountType();
                    if (AccountType.ACCOUNT_EXPERIENCE_FROM_OFFICIAL == accountType) {
                        LoginUitls.sendOldUserSwitchEx(MainTabActivity.this);
                    } else {
                        ITaskProcessListener lis = new ITaskProcessListener() {
                            @Override
                            public void onStart() {
                                showDialog(WAIT_LOGOUT_TYPE);
                            }

                            @Override
                            public void onSuccess(Object object) {
                                if (!isFinishing()) {
                                    removeDialog(WAIT_LOGOUT_TYPE);
                                }
                            }

                            @Override
                            public void onFailed(String errorInfo, Object object) {
                                if (!isFinishing()) {
                                    removeDialog(WAIT_LOGOUT_TYPE);
                                }
                            }
                        };
                        LoginUitls.reqLogOff(MainTabActivity.this, lis);

                            /*SettingsSP.putHomeTypeDescription(EnumDef.FeedType.All.description);
                            showDialog(WAIT_LOGOUT_TYPE);
                            AuthorizeService.logOff(instance, new WebApiExecutionCallback<Boolean>() {
                                @Override
                                public TypeReference<WebApiResponse<Boolean>> getTypeReference() {
                                    return new TypeReference<WebApiResponse<Boolean>>() {
                                    };
                                }

                                @Override
                                public void completed(Date time, Boolean response) {
                                    removeDialog(WAIT_LOGOUT_TYPE);
                                    if (isFinishing()) {
                                        return;
                                    }
                                    if (response != null && response) {
										LoginUitls.logoutDemoSuccessHandle();
                                        LoginUitls.resetToLogoutState();
                                        logoutDemoSuccessHandle(time, response);

                                    } else {
                                        ToastUtils.showToast(I18NHelper.getText("common.main_tab.oper.logout_failed"));
                                        FCLog.i(TAG, "注销失败:" + response + "," + time.toLocaleString());
                                    }
                                }

                                @Override
                                public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                                    removeDialog(WAIT_LOGOUT_TYPE);
                                    ToastUtils.showToast(I18NHelper.getText("common.main_tab.oper.logout_failed"));
                                    FCLog.w(TAG, "注销.Fail:{failureType=" + failureType.description()
                                            + ",httpStatusCode:" + httpStatusCode + ",error:" + error + "}");
                                }
                            });*/
                    }

                } else {
                }

            }
        });
        mydialog.setMessage(I18NHelper.getText("common.main_tab.oper.logout_experience_confirm")/* 确定退出体验环境吗? */);
        mydialog.setCanceledOnTouchOutside(true);
        mydialog.show();

    }

    // 注销方法
    public void logoutSuccessHandle() {
        if (instance != null && !instance.isFinishing()) {
            //instance.finish();
            closeActivity();
        }
    }

    // 快速体验的注销方法，需要返回导导航页
    public void logoutDemoSuccessHandle(Date time, Boolean response) {
        if (instance != null && !instance.isFinishing()) {
            instance.finish();
        }
    }

    private void appCenterTick() {
        // 统计应用中心展示的次数
        StatEngine.tick(AppStatistics.APP_CENTER_SHOW);
    }

    public interface IMaintabNav {
        public void navClick();
    }

    public static class ButtonState implements Parcelable {
        public boolean isSelected;
        public int remindCount;
        public String key;

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeInt(isSelected ? 1 : 0);
            dest.writeInt(remindCount);
            dest.writeString(key);
        }

        public ButtonState() {
        }

        public ButtonState(Parcel source) {
            this.isSelected = source.readInt() != 0;
            this.remindCount = source.readInt();
            this.key = source.readString();
        }

        public static final Parcelable.Creator<ButtonState> CREATOR = new Parcelable.Creator<ButtonState>() {

            @Override
            public ButtonState createFromParcel(Parcel source) {
                return new ButtonState(source);
            }

            @Override
            public ButtonState[] newArray(int size) {
                return new ButtonState[size];
            }

        };
    }

    /**
     * <AUTHOR>
     */
    public static class ButtonHolder implements java.io.Serializable {

        /**
         *
         */
        private static final long serialVersionUID = -6648665795728329582L;

        public ButtonHolder() {
        }

        public boolean isSelected;
        public String tab;
        public String defaultsvg;
        public String selectsvg;
        public String text;
        public int remindCount;

        public View layout;
        public ImageView tabImage;
        public ImageView tabImagev5;
        public TextView tabText;
        public TextView tabRemind;
        public View tabRemindIcon;
        String titleStyle;
        String imgStyle;
        boolean needChangeIconColor = false;
        String defaultColor = null;
        @Override
        public String toString() {
            return tab + " " + remindCount + " " + isSelected;
        }
    }

    private void toHomePageFromClick() {// 2
        if (iMaintabNavHome != null) {
            iMaintabNavHome.navClick();
        }
    }

    // 工作提醒
    public final void setHomeRemindCount(int count) {
        notifyRemindIcon(count > 0, TAB_HOME_ACCESS_KEY);

        long vs = HostInterfaceManager.getPollingManager().getVersion(PollingUtils.HASNewfeed);
        if (vs > 0) {
            FeedSP.saveLongType(PollingUtils.HASNewfeed,vs);
        }

    }
    /**
     *  更新主导航飘数
     * @param uiPaaSDataList
     */
    public final void notifyRemindNavigationUIByPaaSStatList(List<WebPaaSStat> uiPaaSDataList) {
        if (uiPaaSDataList == null || uiPaaSDataList.size() == 0) {
            return;
        }
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (WebPaaSStat item : uiPaaSDataList) {
                    notifyRemindNavigationUIByPaaSStatItem(item);
                }
            }
        });
    }

    /**
     *  更新主导航飘数
     * @param uiPaaSData
     */
    public final void notifyRemindNavigationUIByPaaSStatItem(WebPaaSStat uiPaaSData) {
        if (uiPaaSData == null) {
            return;
        }

        int notReadCount = uiPaaSData.notReadCount;
        boolean isNotReadFlag = uiPaaSData.notReadFlag;
        int notDealCount = uiPaaSData.notDealCount;
        boolean needClearRemindInfo = true;//是否需要清空飘数和飘红点的信息
        //        FCLog.d(TAG,"notifyRemindNavigationUIByPaaSStatItem uiPaaSData:"+uiPaaSData);
        if (notReadCount > 0) {
            notifyRemind(notReadCount, uiPaaSData.accessKey);
            needClearRemindInfo = false;
        } else {
            if (isNotReadFlag) {
                notifyRemindIcon(true, uiPaaSData.accessKey);
                needClearRemindInfo = false;
            } else {
                if (notDealCount > 0) {
                    notifyRemind(notDealCount, true, uiPaaSData.accessKey);
                    needClearRemindInfo = false;
                }
            }
        }
        if (needClearRemindInfo) {
            notifyRemindIcon(false, uiPaaSData.accessKey);
        }
    }
    public final void notifyRemind(int count, String accessKey) {
        notifyRemind(count, false, accessKey);
    }
    public final void notifyRemind(int count, boolean isNotDealStyle, String accessKey) {
        FCLog.i(AppConfigManager.DEBUG_EVENT, "notifyRemind ( " + count + ",  " + accessKey + ",  " + isNotDealStyle + ")" +
                " tabmap-" + mTabMap.toString());
        updateBadgeNumber(count, accessKey);

        ButtonHolder holder = null;
        if (accessKey != null) {
            holder = mTabMap.get(accessKey);
        }
        if (holder != null) {
            if (isNotDealStyle) {
                holder.tabRemind.setBackgroundResource(R.drawable.list_badge_approval);//白底
                holder.tabRemind.setTextColor(Color.RED);//红字
            } else {
                holder.tabRemind.setBackgroundResource(R.drawable.list_badge);
                holder.tabRemind.setTextColor(Color.WHITE);//白字
            }
            holder.tabRemind.setText(count > 999 ? "999+" : String.valueOf(count));
            boolean showRemindCount = count > 0;
            holder.tabRemind.setVisibility(showRemindCount ? View.VISIBLE : View.GONE);
            if (showRemindCount) {
                holder.tabRemindIcon.setVisibility(View.GONE);
            }

            holder.remindCount = count;
            float density = FSScreen.getScreenDensity();
            if (count > 999) {
                holder.tabRemind.setPadding((int) (4.5 * density), 0, (int) (3.5 * density), 0);
            } else {
                holder.tabRemind.setPadding((int) (4 * density), 0, (int) (4 * density), 0);
            }
        }
    }

    private void updateBadgeNumber(int count, String tabName) {
        if (TAB_MSG_ACCESS_KEY.equals(tabName)) {// 更新应用角标，目前只是企业的飘数飘角标到桌面上，如果后期是要把所有tab的飘数汇总一下校正飘数，需要更改count的计算逻辑
            try {
                HostInterfaceManager.getHostInterface().setBadgeNumber(MainTabActivity.this, count, null);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    boolean mLastShowedRemindIconByQixin = false;

    public void onEventMainThread(SessionUpdateRawEvent sessionUpdateEvent) {
        boolean hasNew = SessionCommonUtils.checkAndUpdateUnreadSessionStatusIncludedDb();
        mLastShowedRemindIconByQixin = hasNew;
        notifyRemindIcon(hasNew, TAB_FUNCTION_ACCESS_KEY);
    }

    /**
     * 清除由于企信业务引起的红点展示
     */
    public void clearFunctionRemindByQixin() {
        if (mLastShowedRemindIconByQixin) {//显示红点是企业业务导致的，清除的时候就需要对应清除
            notifyRemindIcon(false, TAB_FUNCTION_ACCESS_KEY);
        }
    }
    /**
     * 只提示红点不提示数
     *
     * @param accessKey
     */
    public final void notifyRemindIcon(final boolean show, String accessKey) {
        if (accessKey == null) {
            return;
        }
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                ButtonHolder holder = mTabMap.get(accessKey);
                if (holder != null) {
                    holder.tabRemindIcon.setVisibility(show ? View.VISIBLE : View.INVISIBLE);
                    holder.tabRemind.setVisibility(View.GONE);
                }
            }
        });
    }

    /**
     * 只提示红点不提示数(personal info tab 专用)
     *
     * @param username 调用方法的唯一名称，例如草稿箱调用（DRAFT），app升级提醒调用(APP_UPGRADE)。
     */
    public final void notifyMeRemindIcon(boolean show, String username) {
        ButtonHolder holder = mTabMap.get(TAB_PERSON_INFO_ACCESS_KEY);
        synchronized(personal_info_tab_red_list) {
            if (show) {
                if (!personal_info_tab_red_list.contains(username)) {
                    personal_info_tab_red_list.add(username);
                }
            } else {
                if (personal_info_tab_red_list.contains(username)) {
                    personal_info_tab_red_list.remove(username);
                }
            }
            if (holder != null) {
                if (personal_info_tab_red_list.isEmpty()) {
                    holder.tabRemindIcon.setVisibility(View.INVISIBLE);
                } else {
                    holder.tabRemindIcon.setVisibility(View.VISIBLE);
                }
                holder.tabRemind.setVisibility(View.GONE);
                // 通知weex版的我页面更新漂数
                EventBus.getDefault().post(new MyInfoRemindChangeEvent());
            }
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        // changed by ywg 20171221 以下的注册是为了LoginQiXinDataController添加处理推送类，否则会收不到推送变化
        // changed by pangc 20181212 注册到onStart方法是为了保证在MainTabActivity通过onNewIntent回调恢复显示时也能成功注册
        IQiXinDataController qiXinDataController = FSContextManager.getCurUserContext().getQiXinDataController();
        if (!qiXinDataController.hasNotifyProcessor()) {
            qiXinDataController.registerNotifyProcessor("Messenger.SessionUpdated",
                    new SessionUpdatedNotifyProcessor(getApplicationContext()));
            qiXinDataController.registerNotifyProcessor("UserEntry.KickOut",
                    new KickOutNotifyProcessor(getApplicationContext()));
//            qiXinDataController.registerNotifyProcessor("AV.EventUpdate",
//                    new AVEventUpdateNotifyProcessor());
        }

        CheckFaceHander.isCheckFace(instance);
    }
    boolean isOnResume = false;
    @Override
    protected void onResume() {
        super.onResume();
        isOnResume = true;
        MainTabLifeListenersManager.getInstance().updateOnResumeStatus(isOnResume);
        if (mGotoSetting) {
            checkStartFloatService();//如果跳转过设置界面，就需要再次检查下是否完成了来电身份识别需要的所有权限
            mGotoSetting = false;
        }
        checkShowNpsPage(this);
        final PersistentBySP persistentBySP = new PersistentBySP(this);
        boolean isLoading = persistentBySP.isReqALevelData();
        //通讯录加载过了，可以去检查下是否需要sso登录，否则走通讯录加载后的通知流程触发检查；见：addCheckStartBizPageEvent()
        if (!isLoading) {
            check2StartBizPage();
        }

        checkDraftRemind();
        checkSettingsRemind();
        checkNetDiskFileDownChangePosRemind();

        // 根据tab切换
        String tag = getIntent().getStringExtra("tabTag");
        if (!TextUtils.isEmpty(tag)) {
            selectTab(tag, mTabMap);
            getIntent().removeExtra("tabTag");
        }

        // 更新CRM失败后，需要频繁获取CRM的展示状态
        mCRMPresenter.reUpdateCrm();
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (PasslockActivity.needStartPasslock(MainTabActivity.this)) {
                    PasslockActivity.start(MainTabActivity.this);
                } else {
                    FSPreference.getInstance().putBoolean(FSPreference.PrefID.PREF_RESTART_APP, false);
                    FSPreference.getInstance().putLong(FSPreference.PrefID.PREF_APP_INTO_BACKGROUND_LAST_TIME, Long.MAX_VALUE);
                }
            }
        },100);

        //add by wubb 每次APP自动登录都要刷新支付的token
        EventBus.getDefault().post(new LoginEvent());
        AppStartPerformanceUtil util= com.fxiaoke.host.App.getG_app().getAppStartPerformanceUtil();
        if(util!=null){
            util.maintabInitEnd();
        }

        // add by zhaodsh  open weex log
        ICloudCtrl icc=HostInterfaceManager.getCloudCtrlManager();
        boolean openweexlog=icc.getBooleanConfig("openweexlog",false);
        WXSDKEngine.setLogSwitch(openweexlog);

        Looper.myQueue().addIdleHandler(new MessageQueue.IdleHandler() {
            @Override
            public boolean queueIdle() {
                prefetchManager.getInstance().init(MainTabActivity.this);
                prefetchManagerV2.getInstance().init(MainTabActivity.this);
                return false;
            }
        });
        SocketDataController.getInstace(getApplicationContext()).start();
        MainTabLifeListenersManager.getInstance().triggerOnResume();
    }

    private void checkShowNpsPage(Activity activity) {
        NpsPageHandler.checkShowNpsPage(activity);
    }

    private void initQrScanHolder(){
        QrScanProcessorHolder.getInstance().init()
                .addProcessor(new QrScanLoginProcessor())
                .addProcessor(new QrScanWeexLinkProcessor())
                .addProcessor(new QrScanPayProcessor())
                .addProcessor(new QrFeedDetailProcessor())
                .addProcessor(new QrScanCrmLinkProcessor())
                .addProcessor(QrScanRouteProcessor.INSTANCE)
                .addProcessor(new QrScanShortAppLinkProcessor())
                .addProcessor(new QrScanWeexDebugProcessor())
                .addProcessor(new QrScanAvatarDebugProcessor())
                .addProcessor(new QrScanAvatarOpenerProcessor())
                .addProcessor(new QrScanCmlLinkProcessor());
    }




    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if(System.currentTimeMillis() - mCreateTime<2000){//onCreate 后2秒就被destroy，标记是被以外关闭的
            FCLog.f("MainTabAccidentFinish",Log.getStackTraceString(new Exception("MainTabAccidentFinish")));
        }
        ScreenShotShareViewUtils.stopScreenShotFileObserver();
        if(mFloatBallViewUtil !=null){
            mFloatBallViewUtil.hideView();
        }
        logoutEventMainSubscriber.unregister();
        FSObservableManager.getInstance().deleteUpdateEmployee(this);
        FSObservableManager.getInstance().deleteSendEvent(this);
        SingletonObjectHolder.getInstance().removeObject(this);
        SingletonObjectHolder.getInstance().removeObject(mAccountAttributeNotifyer);
        SingletonObjectHolder.getInstance().removeObject(mIDeviceAuthorizationListener);
        mLocalBroadcastManager.unregisterReceiver(mSelectedTabReceiver);
        unregisterReceiver(mTinkerResultReceiver);
        unregisterReceiver(mNetworkTimeReceiver);

        EmoticonCtr.getInstance(this).close();
        if (mCRMPresenter != null) {
            mCRMPresenter.onDestroy();
        }
        unregisterEvents();
        KWQTrackLogTool.getInstance().quit();
        for (DynamicBizOpNode opNode : mBizOpNodeList) {
            opNode.destroy();
        }
        mBizOpNodeList.clear();
        EventBus.getDefault().unregister(homesub);
        ICloudCtrl ctrl=HostInterfaceManager.getCloudCtrlManager();
        if (ctrl!=null){
            ctrl.unregisterConfigChangedListener(mOnConfigChangeListener);
            ctrl.unregisterConfigChangedListener(mOnConfigChangeListener);
        }
        if (mAppConfigManager != null) {
            mAppConfigManager.close();
        }
        SwipeBackActivityHelper.onDestroy(this);
        if (mSessionUpdateEventCtr != null) {
            mSessionUpdateEventCtr.release();
        }

        if (mSimpleImgQrCodeScanSubscriber != null) {
            mSimpleImgQrCodeScanSubscriber.unregister();
        }
        if (mShareSubscriber != null) {
            mShareSubscriber.unregister();
        }
        if (mPhoneAssistantEvent != null) {
            mPhoneAssistantEvent.unregister();
        }
        AppStateHelper.unregisterAppStateListener(mAppStateListener);//移除前后台和亮屏事件监听
        stopFloatService();
        MainTabProcessorManager.getInstance().destroy();
        MainTabLifeListenersManager.getInstance().triggerOnDestroy();
        
        // 清理主题引导资源
        if (mThemeGuide != null) {
            mThemeGuide = null;
        }
    }

    @Override
    public void finish() {
        super.finish();
        //防止杀应用时Dialog没有取消而导致的Leak和不可复现的IllegalException
        if (mLoadContactDataCtr != null) {
            mLoadContactDataCtr.release();
        }
    }

    private void unregisterEvents() {
        if (mEvents != null) {
            for (ISubscriber event : mEvents) {
                event.unregister();
            }
        }
    }

    @Override
    protected void onStop() {
        try {
            super.onStop();
        } catch (Exception e) {
            FCLog.w(TAG, "onStop error: " + Log.getStackTraceString(e));
        }
        isOnResume = false;
        MainTabLifeListenersManager.getInstance().updateOnResumeStatus(isOnResume);
        if (!AppStateHelper.isAppRunTop()) {
            if (FSPreference.getInstance().getBoolean(FSPreference.PrefID.PREF_OPEN_PASSWORD_LOCK_SCREEN)
                    && FSPreference.getInstance().getInt(FSPreference.PrefID.PREF_OPEN_PASSWORD_LOCK_SCREEN_COUNT)
                    == 0) {
                FSPreference.getInstance().putInt(FSPreference.PrefID.PREF_OPEN_PASSWORD_LOCK_SCREEN_COUNT, 1);
            }
            FSPreference.getInstance()
                    .putLong(FSPreference.PrefID.PREF_APP_INTO_BACKGROUND_LAST_TIME, System.currentTimeMillis());

        }

    }

    /**
     * 动画跳转
     *
     * @param intent
     */
    public static void startActivityByAnim(Intent intent) {
        if (instance != null) {
            instance.startActivity(intent);
            // instance.overridePendingTransition(R.anim.push_left_in,
            // R.anim.push_left_out);
        } else {
            try {
                FCLog.w(TAG, "startActivityByAnim failed by null instance and use app context start intent " + intent);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                App.getInstance().startActivity(intent);
            } catch (Exception e) {
                FCLog.e(TAG, e.getMessage() + Log.getStackTraceString(e));
            }
        }
    }

    public static void startActivityByAnim(Context context, Intent intent) {
        if ((context instanceof WorkHomeActivity) || (context instanceof ShortMessageMainActivity)
                || (context instanceof MyActivity)
                || (context instanceof MainTabActivity)) {
            if (instance != null) {
                instance.startActivity(intent);
                // instance.overridePendingTransition(R.anim.push_left_in,
                // R.anim.push_left_out);
            }
        } else {
            context.startActivity(intent);
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent != null) {
            boolean isShowQixin = intent.getBooleanExtra("showqixin", false);
            boolean isShowCrm = intent.getBooleanExtra("showcrm", false);
            if (isShowQixin) {
                selectTab(TAB_MSG_ACCESS_KEY, mTabMap);
            } else if (isShowCrm) {
                selectTab(TAB_CRM_ACCESS_KEY, mTabMap);
            }
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    FsShortcutManager.getInstance().gotoShortcutIntent(MainTabActivity.this,intent);
                }
            },200);

        }
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
            if (event.getAction() == KeyEvent.ACTION_DOWN && event.getRepeatCount() == 0 && tabHost != null) {
                String currentTab = tabHost.getCurrentTabTag();
                if (TAB_HOME_ACCESS_KEY.equals(currentTab)) {
                    if (mHomeTabPopupEvent != null && mHomeTabPopupEvent.mIsShow) {
                        return super.dispatchKeyEvent(event);
                    }
                }
//                boolean ret=super.dispatchKeyEvent(event);
//                if (!ret){
//
//                }else{
//                    return true;// jsapiactivity可能会消费该事件
//                }
                try {
                    ActivityManager mAm = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
                    List<ActivityManager.RunningTaskInfo> tasks = mAm.getRunningTasks(2);
                    if (tasks != null && tasks.size() > 0 && tasks.get(0).baseActivity.getPackageName()
                            .equals(HostInterfaceManager.getHostInterface().getApp().getPackageName())) {
                        try {
                            Intent intent = new Intent(Intent.ACTION_MAIN);
                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            intent.addCategory(Intent.CATEGORY_HOME);
                            startActivity(intent);
                        } catch (Exception e) {
                            FCLog.e(TAG, Log.getStackTraceString(e));
                            finish();
                        }
                        return true;
                    }
                } catch (Exception e) {
                    FCLog.e(TAG, Log.getStackTraceString(e));
                }
            }
            return true;
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        if (mIgnoreMultitouch) {
            if (MotionEventUtil.isMultitouch(event)) {
                return true;
            }
        }
        return super.dispatchTouchEvent(event);
    }

    @Override
    public void update(Observable observable, Object data) {
        if (data != null && data instanceof Notify) {
            Notify notify = (Notify) data;
            if (notify.type == Notify.UPDATE_EMPLOYEE_TYPE) {
                FCLog.d(TAG, "侧滑页面头像刷新.....");
                // initUserData(Global.getUserInfo());
            }
            if (notify.type == Notify.SEND_TYPE) {
                checkDraftRemind();
            }
        }
    }
    /**
     * 提示弹框类型：请稍后...
     */
    private final int WAIT_NORMAL_TYPE = 0;
    /**
     * 提示弹框类型：退出中,请稍后...
     */
    private final int WAIT_LOGOUT_TYPE = 2;

    private final int WAIT_NOT_CLOSE_TYPE = 3;
    @Override
    protected Dialog onCreateDialog(int id) {
        LoadingProDialog progress = LoadingProDialog.creatLoadingPro(this);
        switch (id) {
            case WAIT_LOGOUT_TYPE:
                progress.setMessage(I18NHelper.getText("common.main_tab.guide.waiting_logout")/* 退出中,请稍候... */);
                progress.setCanceledOnTouchOutside(false);
                break;
            case WAIT_NORMAL_TYPE:
            case 1:
                progress.setMessage(I18NHelper.getText("pay.wxpay.common.wait_a_while")/* 请稍候... */);
                progress.setCanceledOnTouchOutside(false);
                break;
            case WAIT_NOT_CLOSE_TYPE:
                progress.setMessage(I18NHelper.getText("pay.wxpay.common.wait_a_while")/* 请稍候... */);
                progress.setCanceledOnTouchOutside(false);
                progress.setCancelable(false);
                break;
            default:
                break;
        }

        return progress;
    }

    public static void closeActivity() {
        if (instance != null) {
            FCLog.i(LogUtilCommon.debug_fs, "main tab activity cloase:" + instance.hashCode());
            instance.finish();
            mNotifyCountChanged = null;//xiongtj 避免注销内存泄漏
            instance = null;
        }
    }

    @Override
    public void crmShow(int type) {
        showCrmEntry();
    }

    @Override
    public void crmHide() {
        if (WebApiUtils.requestUrl.contains("pte")) {
            showCrmEntry();
        } else {
            hideCrmEntry();
        }
    }

    public HashMap<String, ButtonHolder> getTabHash() {
        return mTabMap;
    }

    void downloadFileCtrlerStart() {
        try {
            DownloadFileCtrler.getInstance().autoRunTasks();
            checkNetDiskFileDownChangePosRemind();
        } catch (Exception e) {
            FCLog.e(TAG, "downloadFileCtrlerStart with error: " + Log.getStackTraceString(e));
        }
    }



    private void startJsApiProcessAsync(){
        mHandler.postDelayed(()->{
            if(!AccountManager.isLogin(MainTabActivity.this)) return;
            try{
                Intent service=new Intent(MainTabActivity.this,JsApiProcessService.class);
                startService(service);
            } catch (Exception e){
                e.printStackTrace();
            }
        },3000);
    }

    private void exitJsApiProcess(){
        ActivityManager am = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> list = am.getRunningAppProcesses();
        for(ActivityManager.RunningAppProcessInfo info : list){
            if(TextUtils.equals(info.processName, AppInfoUtils.getJsapiProcessName(this))){
                Process.killProcess(info.pid);
                break;
            }
        }
    }

    private MainSubscriber<LogoutEvent> logoutEventMainSubscriber=new MainSubscriber<LogoutEvent>() {
        @Override
        public void onEventMainThread(LogoutEvent event) {
            exitJsApiProcess();
        }
    };

    void initDeviceInfo(){


        FsLocationResult locationResult = FsMultiLocationManager.getInstance().getLastLocation();
        if(locationResult!=null){
            EventsConfig.initLocationData(FsMultiLocationManager.isInChina(),locationResult.getContinent(),locationResult.getCountryName(),locationResult.getProvince(),locationResult.getCity());
        }else{
            Locale locale = Locale.getDefault();
            if(locale!=null){
                EventsConfig.setContinent(FsMultiLocationManager.getInstance().getContinentByCountryCode(locale.getCountry()));
                EventsConfig.setCountry(locale.getDisplayCountry());
            }
        }
        EventsConfig.setLangName(I18NHelper.getInstance().getCurrentLang());
        EventsConfig.setTimeZoneName(FSContextManager.getCurUserContext().getAccount().getTimeZone(this));
        EventsConfig.setRegionName(FSContextManager.getCurUserContext().getAccount().getRegion(this));
        EventsConfig.setMcCurrencyName(FSContextManager.getCurUserContext().getAccount().getMcCurrency(this));
    }


    void cachePredictApp(){

        boolean checkedPRef = FSPreference.getInstance().getBoolean(FSPreference.PrefID.PREF_AVATAR_service_preload);
        if(checkedPRef){
            FCLog.e(TAG, "tag:preloadpage do not cachePredictApp duo to test tools switch");
            return;
        }

        ICloudCtrl icc=HostInterfaceManager.getCloudCtrlManager();
        if(icc != null){
            boolean usePredict = icc.getBooleanConfig("appStartUsePredict", false);
            FCLog.i(TAG,"cachePredictApp: usePredict " + usePredict);
            if(!usePredict){
                return;
            }
        }else{
            return;
        }

        List<String> pre_load=icc.getArrayConfig("appStartUsePredictBundle",String.class);

        try{
            SharedPreferences sp1 = this.getSharedPreferences("TestSetting", Activity.MODE_PRIVATE);
            String preAppId=sp1.getString(AccountManager.getAccount().getEmployeeAccount()+"_"+
                    AccountManager.getAccount().getEmployeeId()+"_"+
                    FsMPActivity.Key_sp_ava_preload_process_start,null);

            FCLog.i(TAG, "preload_MainTabActivity  preload_app_config " + pre_load.toString());

            if(pre_load!=null && !TextUtils.isEmpty(preAppId)){
                FCLog.i(TAG, "preload_MainTabActivity  preload  remove app : " + preAppId);
                pre_load.remove(preAppId);
            }
        }catch (Exception e){

        }

        if(pre_load!=null&&pre_load.size()>0){
            for(int i = 0; i < pre_load.size(); i++){
                FCLog.i(TAG, "preload_MainTabActivity  loadPredictApp: " +  pre_load.get(i));
                AppStartPredictManager.getInstance().loadPredictApp(this, pre_load
                        .get(i));
            }
        }else{
            FCLog.i(TAG, "MainTabActivity  no apps need to be preloaded ");
        }
    }

    /**
     * 缓存Hera appService
     */
    void cacheHeraApp() {
        ICloudCtrl icc=HostInterfaceManager.getCloudCtrlManager();

        if(icc != null){
            boolean usePredict = icc.getBooleanConfig("appStartUsePredict", false);
            FCLog.i(TAG,"cacheHeraAppService: usePredict " + usePredict);
            if(usePredict){
                return;
            }
        }else{
            return;
        }

        SharedPreferences sp1 = this.getSharedPreferences("TestSetting", Activity.MODE_PRIVATE);
        boolean b1 = sp1.getBoolean("is_enable_avatar_cache", true);
        if(!b1){
            return;
        }

        List<String> pre_down = new ArrayList<>();
        pre_down.add("object_form");
        List<String> pre_load = new ArrayList<>();
        pre_load.add("object_form");

        if(icc!=null){
            List<String> pre_downtt=icc.getArrayConfig("ava_pre_download",String.class);
            if(pre_downtt!=null&&pre_downtt.size()>0){
                pre_down=pre_downtt;
            }else{

            }
            List<String> pre_loadtt=icc.getArrayConfig("ava_pre_load",String.class);
            if(pre_loadtt!=null&&pre_loadtt.size()>0){
                pre_load=pre_loadtt;
            }else{

            }
        }
        int l=pre_down.size();
        final List preLoadlist=pre_load;
        for(int i=0;i<l;i++){
            final String downapp=pre_down.get(i);
            FCLog.i(TAG,"cacheHeraAppService: " + downapp);
            StorageUtil.prepareApp(this, downapp, new StorageAdapter.IAppStatusListener() {
                @Override
                public void onReady() {
                    if(preLoadlist.contains(downapp)){
                        FCLog.i(TAG,"cacheHeraAppService: getOrCreateMP " + downapp);
                        MPManager.getInstance().getOrCreateMP(MainTabActivity.this,downapp, AvatarIntentUtils.getUserId(),null,null,false);

                    }
                }

                @Override
                public void onFail(String name, String s) {

                }

                @Override
                public void onAppNeedUpgrade() {
                }
            });
        }

    }

    final public void setIgnoreMultitouch(boolean ignoreMultitouch) {
        this.mIgnoreMultitouch = ignoreMultitouch;
    }


    public boolean uiPaasIsDefaultTab(){
        try{
            if(mAppConfig != null){
                AppConfig.MenuItem  item = mAppConfig.getMenu( mAppConfig.getDefaultMenuIndex());
                if(item != null && item.action != null && item.action.startsWith("ava://uipaas_custom")){
                    FCLog.i(TAG, "#predictload_after_uipaas# uipaas is default tab");
                    return true;
                }else{
                    FCLog.i(TAG, "#predictload_after_uipaas# uipaas is not default tab");
                }
            }
        }catch (Exception e){

        }
        return false;

    }


    public void cachePredictAfterUIPaasRenderEnd(){
            if(uiPaasIsDefaultTab()){
                FCLog.i(TAG, "#predictload_after_uipaas# maintab exec cachePredictApp");
                cachePredictApp();
            }else{
            }
    }


    /**
     * 初始化主题引导功能
     */
    private void initThemeGuide() {
        mThemeGuide = new ThemeGuide(this);
        showThemeSettings();
    }
    
    /**
     * 显示主题设置界面
     */
    public void showThemeSettings() {
        if (mThemeGuide != null) {
            mThemeGuide.showThemeSettings();
        }
    }
    
    /**
     * 隐藏主题设置界面
     */
    public void hideThemeSettings() {
        if (mThemeGuide != null) {
            mThemeGuide.hideThemeSettings();
        }
    }
    
    /**
     * 获取主题引导管理器
     */
    public ThemeGuide getThemeGuide() {
        return mThemeGuide;
    }

    @Override
    protected void attachBaseContext(Context newBase) {
        super.attachBaseContext(LayoutUtils.createNewContextWhenNeedRTL(newBase));
    }
}
