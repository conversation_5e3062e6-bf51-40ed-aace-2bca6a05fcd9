package com.facishare.fs.new_crm.beans;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Created by changl on 2015/12/10.
 */
public class CustomerCombineSalerShortInfo implements Serializable {
    /**	* 类型：负责人=1；联合跟进人=2；售后服务人员=3	*/
    @JSONField(name="M1")
    public int type;
    /**	* 员工ID	*/
    @JSONField(name="M2")
    public int employeeID;

    public CustomerCombineSalerShortInfo() {}

    @JSONCreator
    public CustomerCombineSalerShortInfo(@JSONField(name="M1") int type,
                                         @JSONField(name="M2") int employeeID) {
        this.type = type;
        this.employeeID = employeeID;
    }
}
