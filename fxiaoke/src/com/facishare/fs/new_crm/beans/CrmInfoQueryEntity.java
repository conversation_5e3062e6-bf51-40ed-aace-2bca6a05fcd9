/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.new_crm.beans;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.fs.metadata.list.beans.search_query.SearchQueryInfo;

/**
 * Author:  wangrz
 * Date:    2019/3/29 16:48
 * Remarks: 查询 feed 条件
 */
public class CrmInfoQueryEntity {
    /**
     * 筛选场景
     */
    @JSONField(name = "templateId")
    public String templateId;
    /**
     * 筛选项，使用自定义的
     */
    @JSONField(name = "queryInfo")
    public SearchQueryInfo queryInfo;
    /**
     * 分页大小
     */
    @JSONField(name = "pageSize")
    public int pageSize = 10;
    /**
     * 当前页最小FeedID
     */
    @JSONField(name = "sinceId")
    public int sinceId;
}
