package com.facishare.fs.ui;

import android.content.Intent;
import android.os.Bundle;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;

import com.facishare.fs.App;
import com.facishare.fs.BaseActivity;
import com.facishare.fslib.R;
import com.facishare.fs.account_system.ShowPicExActivity;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.utils_fs.AppStateHelper;
import com.fxiaoke.fxlog.FCLog;

/**
 * User: lijian
 * Date: 13-6-5
 * Time: 上午8:35
 */
public abstract class GestureActivity extends BaseActivity {
    //    private static final int SWIPE_MIN_DISTANCE = 120;
    protected static int SWIPE_MAX_OFF_PATH = 50;
    protected static final int SWIPE_THRESHOLD_VELOCITY = 200;
    protected GestureDetector gestureDetector;
    protected View.OnTouchListener gestureListener;
    public IGestureClose iGestureClose;
    public IGestureSliding iGestureSliding;
    protected int currentView = 0;
    protected static int maxTabIndex = 2;
    public int slidingDistance = 50;
    @Override 
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        gestureDetector = new GestureDetector(new MyGestureDetector());
        gestureListener = new View.OnTouchListener() {
            public boolean onTouch(View v, MotionEvent event) {
                if (gestureDetector.onTouchEvent(event)) {
                    return true;
                }
                return false;
            }
        };
    }

    public class MyGestureDetector extends GestureDetector.SimpleOnGestureListener {
        @Override 
        public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
            try {
                if (Math.abs(e1.getY() - e2.getY()) > SWIPE_MAX_OFF_PATH) return false;
                // right to left swipe
                boolean isDis = e1.getX() - e2.getX() > slidingDistance;
                boolean isEnoughTime = Math.abs(velocityX) > SWIPE_THRESHOLD_VELOCITY;
                FCLog.d("isDis=" + isDis);
                FCLog.d("isEnoughTime=" + isEnoughTime);
                if (isDis && isEnoughTime) {
//                    FCLog.d("test-right");

                    if (iGestureSliding != null) {
                        iGestureSliding.gestureSliding();
                        return true;
                    }
                    if (currentView == maxTabIndex) {
                        currentView = 0;
                    } else {
                        currentView++;
                    }
                } else if (e2.getX() - e1.getX() > slidingDistance && isEnoughTime) {
//                    FCLog.d("test-left");
                    if (iGestureClose != null) {
                        iGestureClose.gestureFinish();
                    }
                    if (currentView == 0) {
                        currentView = maxTabIndex;
                    } else {
                        currentView--;
                    }
                }
            } catch (Exception e) {
                FCLog.e("MyGestureDetector.Err:" + e.getMessage());
            }
            return false;
        }
    }

    @Override 
    public boolean dispatchTouchEvent(MotionEvent event) {
        if (gestureDetector.onTouchEvent(event)) {
            event.setAction(MotionEvent.ACTION_CANCEL);
            return true;
        }
        return super.dispatchTouchEvent(event);
    }

    public void startEnterAnim() {
        overridePendingTransition(R.anim.in_from_right, R.anim.src_in_out);
    }

    public interface IGestureClose {
        public void gestureFinish();
    }

    public interface IGestureSliding {
        public void gestureSliding();
    }

    @Override 
    protected void onResume() {
        super.onResume();
        isShowPic();
        App.isShowpic = true;
    }

    @Override 
    protected void onPause() {
        super.onPause();
    }

    @Override 
    protected void onStop() {
        super.onStop();
        if (!AppStateHelper.isAppRunTop()) {
            App.isShowpic = false;
        }
    }

    private void isShowPic() {
//        String[] tmp = ShowPicConfigUtils.ReadDataAll();
//        if (tmp != null) {
//            if (Integer.parseInt(tmp[2]) == 0) return;
//            if (Integer.parseInt(tmp[2]) == 2) {
//                if (!App.isShowpic) {
//                    goToPicAct();
//                }
//            }
//        }
    }

    private void goToPicAct() {
        Intent mIntent = new Intent(this, ShowPicExActivity.class);
        startActivity(mIntent);
    }


    @Override 
    protected void onDestroy() {
        super.onDestroy();
        HostInterfaceManager.getHostInterface().clearPushNotify();
    }
}
