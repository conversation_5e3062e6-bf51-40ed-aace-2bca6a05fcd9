package com.facishare.fs.ui.me;

import java.util.Date;

import com.alibaba.fastjson.TypeReference;
import com.facishare.fs.App;
import com.facishare.fs.BaseActivity;
import com.facishare.fs.account_system.LoginUitls;
import com.facishare.fs.account_system.passwordverify.LevelShowView;
import com.facishare.fs.account_system.passwordverify.PasswordRegex;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.dialogs.ComDia;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.pluginapi.Account;
import com.facishare.fs.pluginapi.AccountManager;
import com.facishare.fs.utils_fs.Accounts;
import com.facishare.fs.utils_fs.PushSP;
import com.facishare.fs.utils_fs.SysUtils;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fs.web_business_utils.api.Account02Service;
import com.facishare.fslib.R;
import com.fs.fsprobuf.AccountSystemProtobuf.GetUserInitialDataResult;
import com.fxiaoke.dataimpl.msg.MsgLogDefine;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.stat_engine.StatEngine;
import com.lidroid.xutils.util.SystemActionsUtils;

import android.content.Context;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.Editable;
import android.text.InputType;
import android.text.TextWatcher;
import android.text.method.PasswordTransformationMethod;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.EditText;
import android.widget.TextView;

/**
 * 密码更新
 */
public class UpdatePwdActivity extends BaseActivity {

	private Context context;
	EditText et_new_password;
	private LevelShowView levelShowView;

//	/**
//	 * 字符限定
//	 * @param str
//	 * @return
//	 * @throws PatternSyntaxException
//	 */
//	public static String StringFilter(String str)throws PatternSyntaxException {
//		String regEx = "[/\\:*?<>|\"\n\t]"; //要过滤掉的字符
//		Pattern p = Pattern.compile(regEx);
//		Matcher m = p.matcher(str);
//		return m.replaceAll("").trim();
//	}

	@Override
	public void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		setContentView(R.layout.update_pwd);
		if ("fs.intent.action.fs_oss1_aqzs_mmxg".equals(getIntent().getAction())){
			StatEngine.tick("Session_55");
		}
		initTitle();
		context = this;

		Button btn_update_pwd = (Button) findViewById(R.id.btn_update_pwd);
		final EditText et_src_password = (EditText) findViewById(R.id.et_src_password);
		et_src_password.setTypeface(Typeface.DEFAULT);
		levelShowView= (LevelShowView) findViewById(R.id.password_level_update);
		et_new_password = (EditText) findViewById(R.id.et_new_password);
		et_new_password.setTypeface(Typeface.DEFAULT);
		final EditText et_new_password_2 = (EditText) findViewById(R.id.et_new_password_2);
		et_new_password_2.setTypeface(Typeface.DEFAULT);

		et_new_password.addTextChangedListener(new TextWatcher() {
			@Override
			public void beforeTextChanged(CharSequence s, int start, int count, int after) {

			}

			@Override
			public void onTextChanged(CharSequence s, int start, int before, int count) {
			}

			@Override
			public void afterTextChanged(Editable s) {
				levelShowView.setContext(s.toString().replace(" ",""));
			}
		});

		CheckBox pBox = (CheckBox) findViewById(R.id.checkbox_et_src_password);

		pBox.setOnCheckedChangeListener(new OnCheckedChangeListener() {

			@Override
			public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
				// TODO Auto-generated method stub
				if (isChecked) {
					// 显示密码
					et_src_password.setTransformationMethod(null);
				} else {
					// 隐藏密码
					et_src_password.setTransformationMethod(new PasswordTransformationMethod());
				}
			}
		});
		CheckBox newBox = (CheckBox) findViewById(R.id.checkbox_et_new_password);

		newBox.setOnCheckedChangeListener(new OnCheckedChangeListener() {

			@Override
			public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
				// TODO Auto-generated method stub
				if (isChecked) {
					// 显示密码
					et_new_password.setTransformationMethod(null);
				} else {
					// 隐藏密码
					et_new_password.setTransformationMethod(new PasswordTransformationMethod());
				}
			}
		});
		CheckBox new2Box = (CheckBox) findViewById(R.id.checkbox_et_new_password_2);

		new2Box.setOnCheckedChangeListener(new OnCheckedChangeListener() {

			@Override
			public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
				// TODO Auto-generated method stub
				if (isChecked) {
					// 显示密码
					et_new_password_2.setTransformationMethod(null);
				} else {
					// 隐藏密码
					et_new_password_2.setTransformationMethod(new PasswordTransformationMethod());
				}
			}
		});
		final boolean isLogin = AccountManager.isLogin(App.getInstance());
		btn_update_pwd.setOnClickListener(new View.OnClickListener() {
			@Override
			public void onClick(View v) {
				String srcPwd = et_src_password.getText().toString().trim();
				String newPwd = et_new_password.getText().toString().trim();
				String newPwd2 = et_new_password_2.getText().toString().trim();

				if (srcPwd.length() <= 0 || newPwd.length() <= 0 || newPwd2.length() <= 0) {
					SysUtils.showDialogOneBtn(context, I18NHelper.getText("xt.updatepwdactivity.text.password_is_required")/* 密码为必填项! */);
					return;
				}
				if (!PasswordRegex.basisRulePassword(newPwd)) {
					SysUtils.showDialogOneBtn(context, I18NHelper.getText("qx.register.des.passwor_combination")/* 密码须为6位以上，数字和字母的组合 */);
					return;
				}
				if (PasswordRegex.ruleBlackSpace(newPwd)) {
					SysUtils.showDialogOneBtn(context, I18NHelper.getText("account.set_new_pwd.guide.forbid_space_in_pwd")/* 密码中不可以有空格 */);
					return;
				}
				if (!newPwd.equals(newPwd2)) {
					SysUtils.showDialogOneBtn(context, I18NHelper.getText("xt.updatepwdactivity.text.the_password_input_is_inconsistent_twice")/* 两次密码输入不一致! */);
					return;
				}
				if (newPwd.length() > 40) {
					SysUtils.showDialogOneBtn(context, I18NHelper.getText("xt.updatepwdactivity.text.the_password_cannot_exceed_40_characters_in_length")/* 密码长度不能超过40个字符! */);
					return;
				}
				showDialog(DIALOG_WAITING_BASE);
				Account02Service.ChangeMyPassword(srcPwd, newPwd, PushSP.getRegPushID(), new WebApiExecutionCallback<Boolean>() {
					@Override
					public TypeReference<WebApiResponse<Boolean>> getTypeReference() {
						return new TypeReference<WebApiResponse<Boolean>>() {
						};
					}

					@Override
					public void completed(Date time, Boolean response) {
						FCLog.d("pwd update successful");
						if(isLogin){
							removeDialog(DIALOG_WAITING_BASE);
							finish();
						}else{
							startLogin();
						}
						ToastUtils.showToast(I18NHelper.getText("xt.updatepwdactivity.text.password_updated_successfully")/* 密码更新成功 */);
					}

					@Override
					public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
						FCLog.d("pwd update fail:" + error);
						removeDialog(DIALOG_WAITING_BASE);
                        ComDia.ShowFailure(UpdatePwdActivity.this, failureType, httpStatusCode,
                            error);
						//ToastUtils.showToast("密码更新失败");
					}
				});

			}
		});
		View.OnClickListener callPhoneListener = new View.OnClickListener() {
			@Override
			public void onClick(View v) {
				TextView textView = (TextView) findViewById(R.id.phone_number);
				SystemActionsUtils.delPhone(context, textView.getText().toString());
			}
		};
		findViewById(R.id.phone_number_layout).setOnClickListener(callPhoneListener);
	}

	private void initTitle() {
		initTitleCommon();
		mCommonTitleView.setMiddleText(I18NHelper.getText("xt.device_manage_browser_list_header_layout.text.change_password")/* 修改密码 */);

		mCommonTitleView.addLeftBackAction(new View.OnClickListener() {
			@Override
			public void onClick(View v) {
				finish();
			}
		});

	}

	public void startLogin() {

		String regPushID = PushSP.getRegPushID();
        Account account = FSContextManager.getCurUserContext().getAccount();
		final String businessAccount = account.getEnterpriseAccount();
		final String personalAccount = Accounts.getLastUser();
		final String pwd = et_new_password.getText().toString().trim();

		// AuthorizeService.LoginEx(businessAccount, personalAccount,
		// LoginUitls.encodeRsa(pwd), "", regPushID, new
		// WebApiExecutionCallback<LoginUserInfo>() {
		// @Override
		// public TypeReference<WebApiResponse<LoginUserInfo>>
		// getTypeReference() {
		// return new TypeReference<WebApiResponse<LoginUserInfo>>() {
		// };
		// }
		//
		// @Override
		// public void completed(Date time, LoginUserInfo userInfo) {
		//
		// Global.saveUserInfo(userInfo);
		// WebApiUtils.saveCookie();
		//
		//
		// removeDialog(DIALOG_WAITING_BASE);
		// finish();
		// }
		//
		// @Override
		// public void failed(WebApiFailureType failureType, int httpStatusCode,
		// String error) {
		// removeDialog(DIALOG_WAITING_BASE);
		//
		// }
		// });
		if (!App.netIsOK.get()) {
			ToastUtils.netErrShow();
			endProgress();
			return;
		}
		reqGetUserInitialData(businessAccount, personalAccount, pwd);
		/*
		 * WebApiExecutionCallback<LoginResult> callBack = new
		 * WebApiExecutionCallback<LoginResult>() {
		 * 
		 * @Override public TypeReference<WebApiResponse<LoginResult >>
		 * getTypeReference() { return new
		 * TypeReference<WebApiResponse<LoginResult>>() { }; } public
		 * Class<LoginResult> getTypeReferenceFHE(){ return LoginResult.class; }
		 * 
		 * @Override public void failed(WebApiFailureType failureType, int
		 * httpStatusCode, String error) { //
		 * ToastUtils.showToast("Login failed：error="
		 * +error+",code="+httpStatusCode); endProgress();
		 * ComDialog.showConfirmDialog(context, "登录失败：" +
		 * error+",状态码："+httpStatusCode +",\r\n "+failureType.description());
		 * FCLog.i(MsgLogDefine.debug_account_security,
		 * "UpdatePwdAct reqAuthorizeLogin error=" +
		 * error+",statusCode="+httpStatusCode
		 * +",failureType="+failureType.description()); }
		 * 
		 * @Override public void completed(Date time, LoginResult response) {
		 * LoginStatus status = response.getResult(); //
		 * ToastUtils.showToast("Login completed：status="+status.getNumber());
		 * if(status==null||status!=null&&status.getNumber()!=LoginStatus.
		 * Succeed_VALUE){ endProgress();
		 * FCLog.i(MsgLogDefine.debug_account_security
		 * ,"UpdatePwdAct reqAuthorizeLogin completed with LoginResult="
		 * +response); ToastUtils.showToast("登录失败，请重新登录"); }else{
		 * reqGetUserInitialData(businessAccount, personalAccount,pwd); } } };
		 * String versionCode = ""; // String deviceID =
		 * FcpUtils.getDeviceID(this);
		 * LoginUitls.reqAuthorizeLogin(businessAccount, personalAccount, false,
		 * pwd, versionCode, callBack);
		 */
		// AccountSecurityWebApiUtils.reqFHELogin(deviceID, businessAccount,
		// personalAccount, false, pwd,versionCode,callBack);
	}

	private void beginProgress() {
		showDialog(DIALOG_WAITING_BASE);
	}

	private void endProgress() {
		removeDialog(DIALOG_WAITING_BASE);
	}

	private void reqGetUserInitialData(final String businessAccount, final String personalAccount, final String pwd) {
		if (!App.netIsOK.get()) {
			ToastUtils.netErrShow();
			endProgress();
			return;
		}
		final long requestTime = SystemClock.elapsedRealtime();
		WebApiExecutionCallback<GetUserInitialDataResult> callback = new WebApiExecutionCallback<GetUserInitialDataResult>() {
			@Override
			public TypeReference<WebApiResponse<GetUserInitialDataResult>> getTypeReference() {
				return new TypeReference<WebApiResponse<GetUserInitialDataResult>>() {
				};
			}

			public Class<GetUserInitialDataResult> getTypeReferenceFHE() {
				return GetUserInitialDataResult.class;
			}

			@Override
			public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
				endProgress();
				// ToastUtils.showToast("GetUserInitialData failed：error="+error+",code="+httpStatusCode);
				ComDia.ShowFailure(context, failureType, httpStatusCode, error);
				FCLog.i(MsgLogDefine.debug_account_security, "UpdatePwdAct GetUserInitialData error=" + error + ",statusCode=" + httpStatusCode + ",failureType=" + failureType.description());
			}

			@Override
			public void completed(final Date time,final GetUserInitialDataResult response) {
				// ToastUtils.showToast("GetUserInitialData completed ");
				LoginUitls.reqGetAppConfig(context, response, new LoginUitls.IGetAppConfigLis(){
					@Override
					public void onCompleted() {
						endProgress();
						if (response != null) {
							FCLog.d(MsgLogDefine.debug_account_security, "UpdatePwdAct GetUserInitialData completed ...");
							WebApiUtils.saveCookie();
							// Global.saveUserInfo(LoginUitls.translateUserInitDataToLoginUserInfo(response.getInitialData()));
							// String unencryptedPwd=
							// et_password.getText().toString().trim();
							// boolean isSavePwd = image_switch.getChecked();
							LoginUitls.updateV3EntryByInitialData(response);
							LoginUitls.processLoginCompleted4ChangePwd(context, businessAccount, personalAccount, pwd, pwd, false, LoginUitls.translateUserInitDataToLoginUserInfo(response.getInitialData()), 0);
							LoginUitls.netWorkTimeRef(context, requestTime, time);
							finish();
						} else {
							FCLog.i(MsgLogDefine.debug_account_security, "UpdatePwdAct GetUserInitialData completed with InitialDataResult is null");
							ToastUtils.showToast(I18NHelper.getText("xt.updatepwdactivity.text.the_current_login_is_invalid._please_log_in_again")/* 当前登录失效，请重新登录 */);
						}
					}
				});
			}
		};
		LoginUitls.reqGetUserInitialData(callback);	}}