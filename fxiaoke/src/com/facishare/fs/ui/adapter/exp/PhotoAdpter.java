package com.facishare.fs.ui.adapter.exp;



import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.ImageView;
import android.widget.TextView;

import com.facishare.fslib.R;
import com.facishare.fs.pluginapi.pic.bean.ImageObjectVO;
import com.fxiaoke.fscommon_res.adapter.SyncPhotoBaseAdapter;

import java.util.List;

public class PhotoAdpter extends SyncPhotoBaseAdapter {

	private List<ImageObjectVO> list;
	private Context context;

	public PhotoAdpter(Context context, AbsListView listView,
			List<ImageObjectVO> list) {
		super(context, listView, list);
		this.list = list;
		this.context = context;
	}

	@Override 
	public int getCount() {
		return this.list == null ? 0 : this.list.size();
	}

	@Override 
	public Object getItem(int position) {
		return this.list == null ? null : this.list.get(position);
	}

	@Override 
	public long getItemId(int position) {
		return 0;
	}

	@Override 
	public View getView(int position, View convertView, ViewGroup parent) {
		Holder holder = null;
		if (convertView == null) {
			holder = new Holder();
			convertView = LayoutInflater.from(context).inflate(
					R.layout.image_item, null);
			holder.txtDisplayName = (TextView) convertView
					.findViewById(R.id.txtDisplayName);
			holder.imgThumbnail = (ImageView) convertView
					.findViewById(R.id.imgThun);
			convertView.setTag(holder);
		} else {
			holder = (Holder) convertView.getTag();
		}
		ImageObjectVO imgObj = this.list.get(position);
		holder.txtDisplayName.setText(imgObj.bucket_display_name + "("
				+ imgObj.count + ")");
		imgObj.position = position;
		setImageView(imgObj, holder.imgThumbnail, imgObj.data);
		return convertView;
	}

	public class Holder {
		public TextView txtDisplayName;
		public ImageView imgThumbnail;
	}
}
