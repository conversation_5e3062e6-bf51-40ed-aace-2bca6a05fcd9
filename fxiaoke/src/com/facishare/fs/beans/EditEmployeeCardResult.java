package com.facishare.fs.beans;

import java.io.Serializable;
import com.alibaba.fastjson.annotation.JSONField;

public class EditEmployeeCardResult implements Serializable {

    public EditEmployeeCardResult() {

    }

    /**
     * employeeCard
     */
    private EmployeeCard employeeCard;

    @JSONField(name = "M1")
    public EmployeeCard getEmployeeCard() {
        return employeeCard;
    }

    @JSONField(name = "M1")
    public void setEmployeeCard(EmployeeCard employeeCard) {
        this.employeeCard = employeeCard;
    }

}
