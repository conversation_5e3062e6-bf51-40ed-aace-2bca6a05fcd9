/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.beans;

import java.io.Serializable;
import com.alibaba.fastjson.annotation.JSONField;

public class CheckInvitePersonFailReason implements Serializable {

    public CheckInvitePersonFailReason() {

    }

    /**
     * 受邀约人id
     */
    private int invitePersonId;

    /**
     * 失败原因
     */
    private String failReason;

    @JSONField(name = "M1")
    public int getInvitePersonId() {
        return invitePersonId;
    }

    @JSONField(name = "M1")
    public void setInvitePersonId(int invitePersonId) {
        this.invitePersonId = invitePersonId;
    }

    @JSONField(name = "M2")
    public String getFailReason() {
        return failReason;
    }

    @JSONField(name = "M2")
    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

}
