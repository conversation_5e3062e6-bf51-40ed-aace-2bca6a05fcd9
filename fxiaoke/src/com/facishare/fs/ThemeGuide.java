package com.facishare.fs;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.os.Handler;
import android.os.Process;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.HorizontalScrollView;

import com.facishare.fs.dialogs.CommonDialog;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fslib.R;
import com.fxiaoke.fscommon_res.activity.FCBaseActivity;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.fxiaoke.skin.background.ThemeManager;
import com.lidroid.xutils.util.AppInfoUtils;

import java.util.List;

public class ThemeGuide {
    
    // SharedPreferences 相关常量
    private static final String THEME_GUIDE_PREFS = "theme_guide_prefs";
    private static final String KEY_THEME_GUIDE_SHOWN = "theme_guide_shown";
    
    private Activity activity;
    private RelativeLayout themeSettingLayout;
    private LinearLayout coverLayout;
    private ImageView ivThemeHeader;
    private TextView tvThemeTitle;
    private LinearLayout llThemeDemo;
    private ImageView ivThemeDemo1;
    private ImageView ivThemeDemo2;
    private LinearLayout buttonsLayout;
    private HorizontalScrollView hsvThemeButtons;
    private LinearLayout llThemeButtonsContainer;
    private Button btnTheme1;
    private Button btnTheme2;
    private Button btnTheme3;
    private Button btnTheme4;
    private Button btnTheme5;
    private Button btnTheme6;
    private Button btnTheme7;
    private Button btnTheme8;
    private Button btnTheme9;
    private Button btnTheme10;
    private Button btnTheme11;
    private Button btnTheme12;
    private Button btnSave;
    private ImageView ivThemeClose;
    
    private int currentSelectedTheme = 1; // 默认选中碧海金沙主题

    String[] themeNames = {"碧海金沙", "经典灰", "普罗旺斯", "霓虹甜心", "鸢尾紫", "天青蓝",
            "暮色橙", "蔻丹粉", "薄荷绿", "苍穹灰", "青葛灰", "墨韵黑"};

    String[] themeEnNames = {"Azure Sands", "Classic Gray", "Provence", "Neon Sweetheart", "Iris Purple", "Sky Blue",
            "Twilight Orange", "Cody Powder", "Mint Green", "Gray Sky", "Blue Wisteria Ash", "Ink Charm Black"};


    public ThemeGuide(Activity activity) {
        this.activity = activity;
        initViews();
        setupClickListeners();
    }
    
    private void initViews() {
        themeSettingLayout = activity.findViewById(R.id.theme_setting_layout);
        coverLayout = activity.findViewById(R.id.cover_layout);
        ivThemeHeader = activity.findViewById(R.id.iv_theme_header);
        tvThemeTitle = activity.findViewById(R.id.tv_theme_title);
        llThemeDemo = activity.findViewById(R.id.ll_theme_demo);
        ivThemeDemo1 = activity.findViewById(R.id.iv_theme_demo_1);
        ivThemeDemo2 = activity.findViewById(R.id.iv_theme_demo_2);
        buttonsLayout = activity.findViewById(R.id.buttons_layout);
        hsvThemeButtons = activity.findViewById(R.id.hsv_theme_buttons);
        llThemeButtonsContainer = activity.findViewById(R.id.ll_theme_buttons_container);
        btnTheme1 = activity.findViewById(R.id.btn_theme_1);
        btnTheme2 = activity.findViewById(R.id.btn_theme_2);
        btnTheme3 = activity.findViewById(R.id.btn_theme_3);
        btnTheme4 = activity.findViewById(R.id.btn_theme_4);
        btnTheme5 = activity.findViewById(R.id.btn_theme_5);
        btnTheme6 = activity.findViewById(R.id.btn_theme_6);
        btnTheme7 = activity.findViewById(R.id.btn_theme_7);
        btnTheme8 = activity.findViewById(R.id.btn_theme_8);
        btnTheme9 = activity.findViewById(R.id.btn_theme_9);
        btnTheme10 = activity.findViewById(R.id.btn_theme_10);
        btnTheme11 = activity.findViewById(R.id.btn_theme_11);
        btnTheme12 = activity.findViewById(R.id.btn_theme_12);
        btnSave = activity.findViewById(R.id.btn_save);
        ivThemeClose = activity.findViewById(R.id.iv_theme_close);


        if(isLangZh()){
            ivThemeHeader.setImageResource(R.drawable.theme_guide_header_cn);

            int demo1ResId = getDrawableResourceId("theme_bhjs_list_cn");
            int demo2ResId = getDrawableResourceId("theme_bhjs_detail_cn");
            ivThemeDemo1.setImageResource(demo1ResId);
            ivThemeDemo2.setImageResource(demo2ResId);

            tvThemeTitle.setText(themeNames[0]);



        }else{
            ivThemeHeader.setImageResource(R.drawable.theme_guide_header_en);
            int demo1ResId = getDrawableResourceId("theme_bhjs_list_en");
            int demo2ResId = getDrawableResourceId("theme_bhjs_detail_en");
            ivThemeDemo1.setImageResource(demo1ResId);
            ivThemeDemo2.setImageResource(demo2ResId);

            tvThemeTitle.setText(themeEnNames[0]);
        }

        btnSave.setText(I18NHelper.getText("setting_set_as_theme"));
        // 设置主题头图的动态高度
        setupThemeHeaderSize();

        updateThemeButtonsState(currentSelectedTheme);
        
        // 初始化主题设置布局为隐藏状态
//        if (themeSettingLayout != null) {
//            themeSettingLayout.setVisibility(View.GONE);
//        }
    }
    
    private void setupClickListeners() {
        // 主题按钮点击事件
        if (btnTheme1 != null) {
            btnTheme1.setOnClickListener(v -> selectTheme(1, "碧海金沙"));
        }
        if (btnTheme2 != null) {
            btnTheme2.setOnClickListener(v -> selectTheme(2, "经典灰"));
        }
        if (btnTheme3 != null) {
            btnTheme3.setOnClickListener(v -> selectTheme(3, "普罗旺斯"));
        }
        if (btnTheme4 != null) {
            btnTheme4.setOnClickListener(v -> selectTheme(4, "霓虹甜心"));
        }
        if (btnTheme5 != null) {
            btnTheme5.setOnClickListener(v -> selectTheme(5, "鸢尾紫"));
        }
        if (btnTheme6 != null) {
            btnTheme6.setOnClickListener(v -> selectTheme(6, "天青蓝"));
        }
        if (btnTheme7 != null) {
            btnTheme7.setOnClickListener(v -> selectTheme(7, "暮色橙"));
        }
        if (btnTheme8 != null) {
            btnTheme8.setOnClickListener(v -> selectTheme(8, "蔻丹粉"));
        }
        if (btnTheme9 != null) {
            btnTheme9.setOnClickListener(v -> selectTheme(9, "薄荷绿"));
        }
        if (btnTheme10 != null) {
            btnTheme10.setOnClickListener(v -> selectTheme(10, "苍穹灰"));
        }
        if (btnTheme11 != null) {
            btnTheme11.setOnClickListener(v -> selectTheme(11, "青葛灰"));
        }
        if (btnTheme12 != null) {
            btnTheme12.setOnClickListener(v -> selectTheme(12, "墨韵黑"));
        }
        
        // 保存按钮点击事件
        if (btnSave != null) {
            btnSave.setOnClickListener(v -> saveTheme());
        }
        
        // 关闭按钮点击事件
        if (ivThemeClose != null) {
            ivThemeClose.setOnClickListener(v -> hideThemeSettings());
        }
        
        // 点击背景关闭主题设置
        if (themeSettingLayout != null) {
            themeSettingLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                }
            });
        }
    }
    
    /**
     * 显示主题设置界面（只显示一次）
     */
    public void showThemeSettings() {
        // 检查是否已经显示过
        if (hasShownThemeGuide()) {
            return;
        }
        
        if (themeSettingLayout != null) {
            themeSettingLayout.setVisibility(View.VISIBLE);
            // 标记为已显示
            markThemeGuideAsShown();
        }
    }
    
    /**
     * 强制显示主题设置界面（忽略显示状态）
     */
    public void forceShowThemeSettings() {
        if (themeSettingLayout != null) {
            themeSettingLayout.setVisibility(View.VISIBLE);
        }
    }
    
    /**
     * 隐藏主题设置界面
     */
    public void hideThemeSettings() {
        if (themeSettingLayout != null) {
            themeSettingLayout.setVisibility(View.GONE);
        }
    }
    
    /**
     * 检查主题引导是否已经显示过
     */
    private boolean hasShownThemeGuide() {
        SharedPreferences prefs = activity.getSharedPreferences(THEME_GUIDE_PREFS, Context.MODE_PRIVATE);
        return prefs.getBoolean(KEY_THEME_GUIDE_SHOWN, false);
    }
    
    /**
     * 标记主题引导为已显示
     */
    private void markThemeGuideAsShown() {
        SharedPreferences prefs = activity.getSharedPreferences(THEME_GUIDE_PREFS, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean(KEY_THEME_GUIDE_SHOWN, true);
        editor.apply();
    }
    
    /**
     * 重置主题引导显示状态（用于测试或重新显示）
     */
    public void resetThemeGuideShownStatus() {
        SharedPreferences prefs = activity.getSharedPreferences(THEME_GUIDE_PREFS, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean(KEY_THEME_GUIDE_SHOWN, false);
        editor.apply();
    }
    
    /**
     * 选择主题
     */
    private void selectTheme(int themeId, String themeName) {
        currentSelectedTheme = themeId;
        
        // 更新标题
        if (tvThemeTitle != null) {

            if(isLangZh()){
                tvThemeTitle.setText(themeNames[themeId-1]);
            }else{
                tvThemeTitle.setText(themeEnNames[themeId-1]);
            }


        }
        
        // 更新按钮选中状态
        updateThemeButtonsState(themeId);
        
        // 更新预览图片
        updateThemePreview(themeId);
    }
    
    /**
     * 更新主题按钮选中状态
     */
    private void updateThemeButtonsState(int selectedTheme) {
        Button[] buttons = {btnTheme1, btnTheme2, btnTheme3, btnTheme4, btnTheme5, btnTheme6, 
                           btnTheme7, btnTheme8, btnTheme9, btnTheme10, btnTheme11, btnTheme12};


        int[] normalResId = {
                R.drawable.button_gradient_1,
                R.drawable.button_gradient_2,
                R.drawable.button_gradient_3,
                R.drawable.button_gradient_4,
                R.drawable.button_gradient_5,
                R.drawable.button_gradient_6,
                R.drawable.button_gradient_7,
                R.drawable.button_gradient_8,
                R.drawable.button_gradient_9,
                R.drawable.button_gradient_10,
                R.drawable.button_gradient_11,
                R.drawable.button_gradient_12,
        };


        int[] selectorResId = {
                R.drawable.button_gradient_1_selector,
                R.drawable.button_gradient_2_selector,
                R.drawable.button_gradient_3_selector,
                R.drawable.button_gradient_4_selector,
                R.drawable.button_gradient_5_selector,
                R.drawable.button_gradient_6_selector,
                R.drawable.button_gradient_7_selector,
                R.drawable.button_gradient_8_selector,
                R.drawable.button_gradient_9_selector,
                R.drawable.button_gradient_10_selector,
                R.drawable.button_gradient_11_selector,
                R.drawable.button_gradient_12_selector,
        };



        for (int i = 0; i < buttons.length; i++) {
            if (buttons[i] != null) {
                if (i + 1 == selectedTheme) {
                    // 选中状态
                    buttons[i].setBackground(activity.getResources().getDrawable(selectorResId[i]));
                } else {
                    // 未选中状态
                    buttons[i].setBackground(activity.getResources().getDrawable(normalResId[i]));
                }

                if(isLangZh()){
                    buttons[i].setText(themeNames[i]);
                }else{
                    buttons[i].setText(themeEnNames[i]);
                }

                if( i == 10 || i == 11){
                    buttons[i].setTextColor(0xffffffff);
                }

            }
        }
    }
    
    /**
     * 更新主题预览图片
     */
    private void updateThemePreview(int themeId) {
        // 主题名称到拼音缩写的映射
        String[] themePinyinCodes = {"bhjs", "jdh", "plws", "nhtx", "ywz", "tql", 
                                   "msc", "kdf", "bhl", "cqh", "qgh", "myh"};

        String list_suffix = isLangZh() ? "_list_cn":"_list_en";
        String detail_suffix = isLangZh() ? "_detail_cn":"_detail_en";

        if (themeId >= 1 && themeId <= 12) {
            String themeCode = themePinyinCodes[themeId - 1];
            
            // 动态获取图片资源ID
            int demo1ResId = getDrawableResourceId("theme_" + themeCode + list_suffix);
            int demo2ResId = getDrawableResourceId("theme_" + themeCode + detail_suffix);
            
            // 设置预览图片
            if (ivThemeDemo1 != null && demo1ResId != 0) {
                ivThemeDemo1.setImageResource(demo1ResId);
            }
            if (ivThemeDemo2 != null && demo2ResId != 0) {
                ivThemeDemo2.setImageResource(demo2ResId);
            }
        }
    }
    
    /**
     * 根据资源名称获取drawable资源ID
     */
    private int getDrawableResourceId(String resourceName) {
        try {
            return activity.getResources().getIdentifier(resourceName, "drawable", activity.getPackageName());
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
    
    /**
     * 保存主题设置
     */
    private void saveTheme() {
        ThemeManager.getInstance().saveThemeByName(themeNames[currentSelectedTheme-1]);
        showRestartDialog();
    }
    
    /**
     * 获取当前选中的主题
     */
    public int getCurrentSelectedTheme() {
        return currentSelectedTheme;
    }
    
    /**
     * 设置当前主题
     */
    public void setCurrentTheme(int themeId) {
        if (themeId >= 1 && themeId <= 12) {
            selectTheme(themeId, themeNames[themeId - 1]);
        }
    }
    
    /**
     * 设置主题头图的动态高度
     * 图片宽高比 483:1059
     */
    private void setupThemeHeaderSize() {
        if (ivThemeHeader != null) {
            ivThemeHeader.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    // 移除监听器避免重复调用
                    ivThemeHeader.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                    
                    // 获取ImageView的实际宽度
                    int width = ivThemeHeader.getWidth();
                    if (width > 0) {
                        // 根据宽高比 483:1059 计算高度
                        int height = (int) (width * 483.0f / 1059.0f);
                        
                        // 设置新的高度
                        ViewGroup.LayoutParams params = (ViewGroup.LayoutParams) ivThemeHeader.getLayoutParams();
                        params.height = height;
                        ivThemeHeader.setLayoutParams(params);
                    }
                }
            });
        }
    }



    private void showRestartDialog() {
        Activity ac = this.activity;
        String message = I18NHelper.getText("theme_apply_restart_app", "主题设置需重启APP生效，是否重启？");
        String okButtonTile = I18NHelper.getText("timezone.restartdialog.confirm_button", "重启");
        Runnable cancelClickCallback = new Runnable() {
            @Override
            public void run() {
            }
        };
        Runnable confirmClickCallback = new Runnable() {
            @Override
            public void run() {
                popAllActivity();
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        kill(ac);
                        android.os.Process.killProcess(android.os.Process.myPid());
                    }
                }, 300);
            }
        };
        showTwoButtonDialog(message, okButtonTile, cancelClickCallback, confirmClickCallback);
    }


    public static void kill(Context context){
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> processInfoList=manager.getRunningAppProcesses();
        for (ActivityManager.RunningAppProcessInfo info:processInfoList) {
            if(AppInfoUtils.getJsapiProcessName(context).equals(info.processName)){
                Process.killProcess(info.pid);
                break;
            }
        }
    }


    public  void popAllActivity() {
        if(WebApiUtils.isSwitchingAccounts()){
            return;
        }
        Intent intent = new Intent();
        intent.setPackage(this.activity.getApplication().getPackageName());
        intent.setAction(FCBaseActivity.ACTION_EXIT);
        this.activity.getApplication().sendBroadcast(intent);
    }

    private void showTwoButtonDialog(String message, String okButtonTitle, Runnable cancelClickCallback, Runnable confirmClickCallback) {
        final CommonDialog commonDialog=CommonDialog.createTwoButtonDialog(this.activity,null);
        //时区业务涉及的弹框需要用户明确操作才可以关闭，故不允许弹框外侧的取消操作
        commonDialog.setCanceledOnTouchOutside(false);
        commonDialog.setBackCancelable(false);
        commonDialog.setCancelable(false);
        commonDialog.setShowType(CommonDialog.FLAG_TwoButton);
        commonDialog.setTitle(I18NHelper.getText("timezone.tipdialog.title", "提示"));
        commonDialog.setMessage(message);
        commonDialog.setOkButtonTitle(okButtonTitle);
        commonDialog.setPositiveButtonColor(Color.parseColor("#FF8000"));
        commonDialog.initTwoButtonDialogListenerTShow(new CommonDialog.myDiaLogListener() {
            @Override
            public void onClick(View view) {
                commonDialog.dismiss();
                if (view.getId() == com.fxiaoke.fscommon_res.R.id.button_mydialog_cancel) {
                    if(cancelClickCallback!=null){
                        cancelClickCallback.run();
                    }
                } else if (view.getId() == com.fxiaoke.fscommon_res.R.id.button_mydialog_enter) {
                    if(confirmClickCallback!=null){
                        confirmClickCallback.run();
                    }
                }
            }
        });
    }

    public boolean isLangZh() {
        String lang = I18NHelper.getInstance().getCurrentLang();
        return lang.equals("zh-CN") ;
    }

}
