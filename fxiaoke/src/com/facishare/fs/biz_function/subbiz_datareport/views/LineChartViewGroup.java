package com.facishare.fs.biz_function.subbiz_datareport.views;

import java.util.List;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;

import com.facishare.fslib.R;
import com.facishare.fs.biz_function.subbiz_datareport.beans.ATrendAnalysisInfo;
import com.facishare.fs.biz_function.subbiz_datareport.utils.ChartUtils;
import com.facishare.fs.biz_function.subbiz_datareport.utils.ChartUtils.AxisLabel;
import com.facishare.fs.common_utils.FSScreen;
import com.fxiaoke.fxlog.FCLog;

public class LineChartViewGroup extends ViewGroup {
	
	private static String TAG = "LineChartViewGroup" ;
		
	private Context mContext;
	private ChartRangeView mRangeView;
	private LineChartScrollView mScrollView;

	/**
	 * 初始化趋势图
	 * @param context
	 * 注意：根据设计，该趋势图的高度必须为240dp，宽度自适应屏幕！
	 */
	public LineChartViewGroup(Context context) {
		super(context);
		// TODO Auto-generated constructor stub
		mContext = context;
		init();
	}

	public LineChartViewGroup(Context context, AttributeSet attrs){
    	super(context, attrs) ;
    	mContext = context ;
    	init() ;
    }
	
	/**
	 * 更新趋势图数据
	 * @param dataList 数据数组，包括一组数据和对应文本，参考ATrendAnalysisInfo类。
	 * 				      长度必须大于0。
	 * @param dateType 当前日期类型。
	 * @return void
	 */
	public void updateViewData(List<ATrendAnalysisInfo> dataList, int dateType) {
		if (dataList != null && dataList.size() > 0) {
			// calculate max mark in Y-axis
			long maxValue = 0;
			if (dataList != null && dataList.size() > 0) {
				for (ATrendAnalysisInfo data : dataList) {
					double value = data.infoValue;
					if (value > maxValue) {
						maxValue = (long)value;
					}
				}
			}
			
	        AxisLabel optionalLabel1 = AxisLabel.normalizeAxisLabel(0.0, maxValue, 5);
            AxisLabel optionalLabel2 = AxisLabel.normalizeAxisLabel(0.0, maxValue, 6);
            AxisLabel optionalLabel3 = AxisLabel.normalizeAxisLabel(0.0, maxValue, 4);
            if (optionalLabel1 == null && optionalLabel2 == null && optionalLabel3 == null) {
            	mRangeView.updateRangeData(null);
            	mScrollView.updateChartData(null, null, 0);
    			mScrollView.scrollTo(0, 0);
                return;
            }
            
            AxisLabel label = null;
            double upperBound1 = optionalLabel1.getUpperBound();
            double upperBound2 = optionalLabel2.getUpperBound();
            double upperBound3 = optionalLabel3.getUpperBound();
            if (!Double.valueOf(Double.NaN).equals(upperBound1)&&upperBound1 <= upperBound2 ) {
                label = optionalLabel1;
            } else {
                label = optionalLabel2;
            }
            double upperBoundTemp = label.getUpperBound();
            if(!Double.valueOf(Double.NaN).equals(upperBoundTemp)){
                if (upperBoundTemp > upperBound3) {
                    label = optionalLabel3;
                }
            }else{
                if(label.equals(optionalLabel1)){
                    if(upperBound2 > upperBound3){
                        label = optionalLabel3;
                    }else{
                        label = optionalLabel2;
                    }
                }else{
                    if(upperBound1 > upperBound3){
                        label = optionalLabel3;
                    }else{
                        label = optionalLabel1;
                    }
                }
            }
            
            mRangeView.updateRangeData(label);
            mScrollView.updateChartData(dataList, label, dateType);
            mScrollView.scrollTo(0, 0);
		} else {
			mRangeView.updateRangeData(null);
			mScrollView.updateChartData(null, null, 0);
			mScrollView.scrollTo(0, 0);
			FCLog.v(TAG, "Data list is empty!");
		}
		
		postInvalidate();
	}

	private void init(){
		setWillNotDraw(false);
    	
		mRangeView = new ChartRangeView(mContext);
    	addView(mRangeView);
    	
    	mScrollView = new LineChartScrollView(mContext);
    	mScrollView.setWillNotDraw(false);
    	addView(mScrollView);

    	setBackgroundColor(Color.parseColor(getResources().getString(R.color.dr_chart_bg_color)));
    }
	
	private int getPixel(int dp) {
		return FSScreen.dip2px(mContext, dp);
	}
    
    @Override 
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec){
    	int childCount = getChildCount();
    	int specWidth = MeasureSpec.getSize(widthMeasureSpec);
    	int specHeight = MeasureSpec.getSize(heightMeasureSpec) ;
    	
    	setMeasuredDimension(specWidth, specHeight);

    	for(int i = 0; i < childCount; i++){
    		View child = getChildAt(i);
    		if (i == 0) {
    			child.measure(specWidth - (getPixel(ChartUtils.VIEW_MARGIN) << 1),
    					specHeight);
    		}
    		else if (i == 1) {
    			child.measure(specWidth - getPixel(ChartUtils.CHART_LEFT_MARGIN), 
    					getPixel(ChartUtils.CHART_AREA_HEIGHT));
    		}
    	}
    }
    
	@Override 
	protected void onLayout(boolean changed, int l, int t, int r, int b) {
		// TODO Auto-generated method stub
    	int childCount = getChildCount();
    	int width = getWidth();
    	for(int i = 0; i < childCount; i++){
    		View child = getChildAt(i);
    		if (i == 0) {
    			child.layout(getPixel(ChartUtils.VIEW_MARGIN), 0, 
    					getPixel(ChartUtils.VIEW_MARGIN) + child.getMeasuredWidth(), 
    					child.getMeasuredHeight());
    		}	
    		else if (i == 1) {
    			child.layout(getPixel(ChartUtils.CHART_LEFT_MARGIN + ChartUtils.VIEW_MARGIN), 
    					getPixel(ChartUtils.SAMPLE_AREA_HEIGHT), width - getPixel(ChartUtils.VIEW_MARGIN), 
    					getPixel(ChartUtils.CHART_TOP_MARGIN) + child.getMeasuredHeight());
    		}
    	}   	   	
	}
}
