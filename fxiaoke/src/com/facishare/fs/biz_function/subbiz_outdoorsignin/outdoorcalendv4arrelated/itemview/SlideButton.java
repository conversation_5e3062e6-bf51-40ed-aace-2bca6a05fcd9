package com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorcalendv4arrelated.itemview;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import android.text.TextPaint;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import com.facishare.fs.biz_feed.utils.FeedSP;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.OutdoorCalendarFragment;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorcalendv4arrelated.Slide2TheEndListener;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.outdoor_offline.OfflineUtils;
import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fslib.R;
import com.fxiaoke.fscommon.brandcolor.BrandColorDataProvider;
import com.fxiaoke.fxlog.FCLog;

public class SlideButton extends View implements View.OnTouchListener {
    public Slide2TheEndListener listener;
    public float startX;
    private int backgroundColor, circleColor;
    private String textContent;
    private Context context;
    private int ncolor;
    public SlideButton(Context context) {
        super(context);
    }

    public SlideButton(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initButton(context, attrs);
    }

    public SlideButton(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initButton(context, attrs);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public SlideButton(Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initButton(context, attrs);
    }

    public void bindListener(Slide2TheEndListener listener) {
        this.listener = listener;
    }

    private void initButton(Context context, AttributeSet attrs) {
        setOnTouchListener(this);
        this.context = context;
        ncolor = Color.parseColor(BrandColorDataProvider.getInstance().getNormalColor(context));
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.SlideButton);
        backgroundColor = typedArray.getColor(R.styleable.SlideButton_bg, Color.BLUE);
        circleColor = typedArray.getColor(R.styleable.SlideButton_circleColor, Color.WHITE);
        textContent = I18NHelper.getText("FSFieldWork.newCalendarCell.FSCLActionView.109_315");//滑动完成拜访
        if(textContent == null)//Swipe to complete the visit
            textContent = "";
        typedArray.recycle();
    }

    @Override
    public boolean onTouch(View view, MotionEvent motionEvent) {
        float offsetX;
//        switch (motionEvent.getAction()) {
//            case MotionEvent.ACTION_DOWN:
//                startX = motionEvent.getX();
//
////                if (startX > this.spaceX*7 + 2 * r)
////                    return false;
//
//                break;
//            case MotionEvent.ACTION_MOVE: //手指按下，滑动的过程
//                offsetX = motionEvent.getX() - startX;
//
//                if(offsetX > 0) {
//                    offset = offsetX;
//                    refreshUI();
//                }
//
//                return false;
//            case MotionEvent.ACTION_UP:
//                if(r + offset  >= maxOffsetX ){
//                    listener.changeState();
//                }else{
//                    for(; offset > 0; offset--)
//                        refreshUI();
//                }
//                break;
//        }
        return false;
    }
    public boolean downaction = true;

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        float offsetX;
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                startX = event.getX();

                if (startX > this.spaceX*7 + 2 * r)
                {
                    return false;
                }else{
                    downaction = false;
                    offset = 1;
                    refreshUI();
                }
                break;
            case MotionEvent.ACTION_MOVE: //手指按下，滑动的过程
                offsetX = event.getX() - startX;

                if(offsetX > 0) {
                    offset = offsetX;
                    refreshUI();
                }
                return false;
            case MotionEvent.ACTION_UP:
                downaction = true;
                if(r + offset  >= maxOffsetX){
                    listener.changeState();
                }else{
                    for(; offset >= 0; offset--)
                        refreshUI();
                }
                break;
        }
        return true;
    }


    public void refreshUI() {
        Message message = new Message();
        message.obj = "refresh";
        myHandler.sendMessage(message);
    }

    @SuppressLint("HandlerLeak")
    Handler myHandler = new Handler() {
        //接收到消息后处理
        public void handleMessage(Message msg) {
            switch ((String) msg.obj) {
                case "refresh":
                    invalidate();//刷新界面
                    break;
            }
            super.handleMessage(msg);
        }
    };

    public void setStatrTag(){
        downaction = true;
        width = 0;
        offset = 0;
        offset = 0.0f;
        invalidate();
    }


    public float offset = 0.0f, maxOffsetX, r;
    public float width, height, spaceX, spaceY;
    Canvas canvas;
    @SuppressLint("DrawAllocation")
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        this.canvas = canvas;

        if((width == 0 || offset == 0 || offset < 1) && downaction) {
            width = getWidth();
            height = getHeight();
            spaceY = 0; //y轴留空距离
            spaceX = width / 21; //x轴留空距离，我这里把宽度分割成了21份，在下面取2份为左右两边的留空距离
            r = (getHeight() - (height / 8) * 2) / 2; //圆形按钮的半径，即为整个view的高度减去上下两部分留空区域的一半
            maxOffsetX = getWidth()/2-10;// - //2 * (spaceX + r)-(height / 8)*21; //圆形按钮圆心最远移动距离，即为整个控件的宽度减去 2 * 左右两侧留空 ＋ 2 * 半径
            paintStart();
        }else{
            paint();
        }


    }

    private void paint() {
//        printCircleonDraw();
        printBackground();
        printTextContent();

        printCircleStart(r);

    }

    private void paintStart() {
//        printCircleonDraw();
        printBackground();
        printTextContent();
        printCircle(r);

    }

    float bottom, top, centerX;
    private void printTextContent() {
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        Paint.FontMetricsInt fontMetrics = paint.getFontMetricsInt();
        paint.setColor(Color.parseColor("#c2c2c2"));
        int baseline = (int)(bottom + top - fontMetrics.bottom - fontMetrics.top) / 2;
        // 下面这行是实现水平居中，drawText对应改为传入targetRect.centerX()
        paint.setTextAlign(Paint.Align.CENTER);
        paint.setStrokeWidth(5);
        paint.setTextSize(FSScreen.dip2px(context, 14));
        paint.setAlpha(getTextAlpha());
//        textContent = TextUtils.ellipsize(textContent, new TextPaint(paint), getWidth()/3, TextUtils.TruncateAt.END).toString();
        if(textContent.length()>6){
            textContent = TextUtils.ellipsize(textContent, new TextPaint(paint), (getWidth()/2)-40, TextUtils.TruncateAt.END).toString();
            canvas.drawText(textContent, getWidth()/2+getWidth()/4-10, baseline + 10, paint);
        }else{
            textContent = TextUtils.ellipsize(textContent, new TextPaint(paint), getWidth()/3, TextUtils.TruncateAt.END).toString();
            canvas.drawText(textContent, getWidth()/2+getWidth()/4, baseline + 10, paint);
        }
    }

    private void printBackground() {
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        paint.setColor(backgroundColor);
        RectF background = new RectF(0, spaceY, getWidth() , getHeight() - spaceY);
        canvas.drawRoundRect(background, 0xfff, 0xfff, paint);
        bottom = background.bottom;
        top = background.top;
        centerX = background.centerX();

    }

    private int getTextAlpha() {
        //描述文字半透明为 （当前移动距离：最远移动距离）* 255
        int base = (int) (255 - (255 * (offset >= maxOffsetX ? maxOffsetX : offset) / maxOffsetX));
        return base <= 235 ? base + 20 : base; //若半透明度<235时候
    }

    private void printCircle(float r) {
        Paint paint = new Paint();
        paint.setColor(circleColor);
        paint.setAntiAlias(true);

        //给偏移量设置一个阈值，即不能超过圆形的最大移动距离，否则会出现圆形控件移出整个滑动部件空间的情况
        if(offset >= maxOffsetX)
            offset = maxOffsetX;

        //cx：圆心x坐标，为r + x轴留空距离 + 移动的距离，cy：圆心y点坐标，为r＋y轴留空距离
        //整个圆形的滑动过程即为圆形在x轴上的运动，因此只改变圆心的x坐标即可
//        canvas.drawCircle(r + spaceX + offset, r + spaceY, r, paint);
//
        paint.setColor(circleColor);
        RectF background = new RectF( 10, spaceY+10, offset + getWidth()/2, getHeight() - spaceY-10);//offset + spaceY*20
        canvas.drawRoundRect(background, 0xfff, 0xfff, paint);
        bottom = background.bottom;
        top = background.top;
        centerX = background.centerX();


        Paint.FontMetricsInt fontMetrics = paint.getFontMetricsInt();
        paint.setColor(ncolor);
        int baseline = (int)(bottom + top - fontMetrics.bottom - fontMetrics.top) / 2;
        // 下面这行是实现水平居中，drawText对应改为传入targetRect.centerX()
        paint.setTextAlign(Paint.Align.CENTER);
        paint.setStrokeWidth(5);
        paint.setTextSize(FSScreen.dip2px(context, 16));
//        paint.setAlpha(getTextAlpha());
        String finish = I18NHelper.getText("fs.wq.fieldwork.terminal.checkFinish")/*完成*/;
        canvas.drawText(finish, offset +(getWidth() / 4 ), baseline+10, paint);
    }

    private void printCircleStart(float r) {
        Paint paint = new Paint();
        paint.setColor(circleColor);
        paint.setAntiAlias(true);

        //给偏移量设置一个阈值，即不能超过圆形的最大移动距离，否则会出现圆形控件移出整个滑动部件空间的情况
        if(offset >= maxOffsetX)
            offset = maxOffsetX;

        //cx：圆心x坐标，为r + x轴留空距离 + 移动的距离，cy：圆心y点坐标，为r＋y轴留空距离
        //整个圆形的滑动过程即为圆形在x轴上的运动，因此只改变圆心的x坐标即可
//        canvas.drawCircle(r + spaceX + offset, r + spaceY, r, paint);
//
        paint.setColor(ncolor);
        RectF background = new RectF( 10, FSScreen.dip2px(context, 3), offset + getWidth()/2, getHeight() - FSScreen.dip2px(context, 3));
        canvas.drawRoundRect(background, 0xfff, 0xfff, paint);
        bottom = background.bottom;
        top = background.top;
        centerX = background.centerX();


        Paint.FontMetricsInt fontMetrics = paint.getFontMetricsInt();
        paint.setColor(Color.parseColor("#ffffff"));
        int baseline = (int)(bottom + top - fontMetrics.bottom - fontMetrics.top) / 2;
        // 下面这行是实现水平居中，drawText对应改为传入targetRect.centerX()
        paint.setTextAlign(Paint.Align.CENTER);
        paint.setStrokeWidth(5);
        paint.setTextSize(FSScreen.dip2px(context, 16));
        paint.getFontMetrics();
//        paint.setAlpha(getTextAlpha());
        String finish = I18NHelper.getText("fs.wq.fieldwork.terminal.checkFinish")/*完成*/;
        canvas.drawText(finish, offset +(getWidth() / 4 ), baseline + 10, paint);
    }

}
