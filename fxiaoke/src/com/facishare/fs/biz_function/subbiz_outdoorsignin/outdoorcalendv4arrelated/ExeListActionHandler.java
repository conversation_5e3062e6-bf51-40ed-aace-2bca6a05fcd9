package com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorcalendv4arrelated;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.text.TextUtils;
import android.view.View;

import com.facishare.fs.biz_function.subbiz_outdoorsignin.FsTickUtils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.IOutdoorData;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.OutdoorCalendarFragment;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.OutdoorRecordListActivity;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CheckType;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CheckinsInfo;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CheckinsV2Result;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CustomerAction;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CustomerActionSimple;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.GetDailyInfoByIdResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.GetPlanArgs;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.GetPlanInfoResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.GetTotalIntegralResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.IdAndNameEx;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.ObjectInfo;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.UpdateCheckinsResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.OutDoor2CacheManger;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.near.OutdoorBehavior;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.near.OutdoorNearUtils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.near.OutdoorSearchActivity;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.near.OutdoorV2ListFragment;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.newrecordlist.OutDoorNewRecordListActivity;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.AvaCacheAddHandler;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.ICustomerGeoUpdate;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2ActionListGroup;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2Activity;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2Constants;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2Ctrl;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2PlanActivity;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2Presenter;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2Utils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.cmlReport.FMCGoderSaveUtils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.outdoor_offline.OfflineUtils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.outdoorv2ActionList.OutDoorV2ActionListView;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.OnClickListenerProxy;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.OutDoorV2FieldDetailsHeadView;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.OutDoorV2GetCustomer;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.OutDoorV2OkView;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.OutDoorV2SignInView;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.OutDoorV2SignOutView;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.Outdoorv2GotoInfo;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scene.PuHuoOfflineManager;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.utils.OutdoorCommonUtils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.utils.OutdoorLocationManager;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.utils.TimeZoneUtils;
import com.facishare.fs.common_utils.JmlCustomUtils;
import com.facishare.fs.dialogs.CommonDialog;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.pluginapi.outdoor.GetPlanInfoArgs;
import com.facishare.fs.utils_fs.OutDoorHomeStyleSetUtils;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fslib.R;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.location.impl.FsMultiLocationManager;
import com.fxiaoke.stat_engine.events.custevents.KwqEventUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import de.greenrobot.event.EventBus;

public class ExeListActionHandler implements OutDoorV2Presenter.IOutdoorCallBack{
    private Context context;
    private CheckType checkType;
    private CheckinsInfo checkinsInfo;
    private OutDoorV2Ctrl mOutDoorV2Ctrl;
    private OutDoorV2Presenter outDoorV2Presenter;
    private IOutdoorData mIOutdoorData;
    private String TAG = ExeListActionHandler.class.getSimpleName();
    public ExeListActionHandler(Context con, CheckinsInfo checkinsInfo){
        context = con;
        this.checkinsInfo = checkinsInfo;
    }
    public void setIOutdoorData(IOutdoorData mIOutdoorData){
        this.mIOutdoorData= mIOutdoorData;
    }

    private void initData(){
        if (mOutDoorV2Ctrl == null) {
            mOutDoorV2Ctrl = new OutDoorV2Ctrl(context);
            outDoorV2Presenter = mOutDoorV2Ctrl.getOutDoorV2Presenter();
            outDoorV2Presenter.setLS(this);

            mOutDoorV2Ctrl.getViewMaps().clear();
            mOutDoorV2Ctrl.addView(new OutDoorV2SignInView(context,null,mOutDoorV2Ctrl));
            mOutDoorV2Ctrl.addView(new OutDoorV2SignOutView(context,null,mOutDoorV2Ctrl));
            mOutDoorV2Ctrl.addView(new OutDoorV2FieldDetailsHeadView(context,null));
            mOutDoorV2Ctrl.addView(new OutDoorV2ActionListView(context,null,mOutDoorV2Ctrl));
            mOutDoorV2Ctrl.addView(new OutDoorV2OkView(context,null,mOutDoorV2Ctrl));
            mOutDoorV2Ctrl.addView(new Outdoorv2GotoInfo(context,checkinsInfo,mOutDoorV2Ctrl));
        }

    }

    private void setAllData(){
        FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"setAllData checkType  ==> "+checkType.printf());
        mOutDoorV2Ctrl.refCheckTypeData(checkType);
        mOutDoorV2Ctrl.setLocation(OutdoorLocationManager.getOutdoorLastLocation(checkinsInfo.mainObject!=null?checkinsInfo.mainObject:null));
    }
    GetPlanInfoArgs gotoArgs;
    int actionPos = -1;
    private void updataOkui(String atype,String str){
        FCLog.i(OfflineUtils.OUTDOOR_OFFLINE, TAG,"updataOkui "+",type = "+str+",atype="+atype);
        if (OutDoorV2Constants.OK_KEY.equals(atype)){
            EventBus.getDefault().post(new OutdoorRefreshBean(OutdoorRefreshBean.REFRESH_LIST_ITEM_OK_VIEW_TAG));
        }
    }
    private boolean checkGotoCaInfo(String subtype){
        if (!TextUtils.isEmpty(subtype)&&checkinsInfo.customerActionSimples.size()>0){
            CustomerActionSimple tcas = null;
            for (CustomerActionSimple cas : checkinsInfo.customerActionSimples
                 ) {
                if (cas.actionId.equals(subtype)){
                    tcas = cas;
                    break;
                }
            }

            if (tcas!=null&&tcas.forbiddenGoDetail==1&&tcas.dataStatus==1){
                return true;
            }
        }

        return false;

    }
    public void gotoAction(String actionType,String subType,int pos){
        actionPos = pos;
        gotoAction(actionType,subType);
    }

    public void gotoAction(String actionType,String subType){
        if (checkGotoCaInfo(subType)){
            ToastUtils.show(I18NHelper.getText("wq.action.not.access.text"));
            return;
        }
        if (!OutDoorV2Utils.getIsDiviceId(context,checkinsInfo.mainObject)) {
            updataOkui(actionType,"getIsDiviceId");
            return;
        }
//        if (!OutDoorV2Utils.isExeToday(checkinsInfo)) {
//            return;
//        }
//        if(!(checkinsInfo.checkinsScene!=null
//                && IdAndNameEx.WORKPROOF.equals(checkinsInfo.checkinsScene.id))){//举证排除在外
            if(!OutDoorV2Utils.checiIsCanDo(context,checkinsInfo.checkinId)){
                updataOkui(actionType,"checiIsCanDo");
                return;
            }
//        }

//        if(this.mIOutdoorData!=null && !this.mIOutdoorData.isCanDo()){
//            FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"");
//            return;
//        }
        FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"gotoAction checkinsInfo ==> "+checkinsInfo.printfAll()+",actionType="+actionType+",subType="+subType);
        initData();

        gotoArgs = new GetPlanInfoArgs();
        gotoArgs.actionType = actionType;
        gotoArgs.subType = subType;


        if (!exeCustomer2me(actionType,subType)){
            isSendGetPlanInfo();
            checkGotoAction();
        }

    }

    private boolean isNeartj(){
//        if(context !=null&&context instanceof OutdoorSearchActivity){
//            String str = ((OutdoorSearchActivity) context).getIntent().getStringExtra(OutdoorV2ListFragment.SCENEID_KEY);
//            if (!IdAndNameEx.PUHUO.equals(str)){// 判断附近推荐页面，不是铺货就是附近推荐
//                return true;
//            }        }


        return false;
    }

    private boolean exeCustomer2me(String actionType,String subType){
        if((OutDoorV2Constants.TAKE_CUSTOMER_KEY.equals(actionType)||
                OutDoorV2Constants.TAKE_CUSTOMER_SIGNINKEY.equals(actionType))){

            if (isImageAction(subType)&&isNeartj()){
                gotoArgs.actionType = OutDoorV2Constants.ACTION_LIST_KEY;
                FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"isImageAction ---");
                return false;
            }

            if (checkinsInfo.isAdd2Route == 2&&OutDoor2CacheManger.getCheckinsInfoHashMapByid(checkinsInfo.indexId)==null){
                showIsAddDialog(new ICustomerGeoUpdate() {
                    @Override
                    public void onResult(int result) {
                        if (result ==1){
                            OutDoor2CacheManger.setCheckinsInfoHashMap(checkinsInfo,result);
                            OutDoorV2GetCustomer c = new OutDoorV2GetCustomer(context,checkinsInfo,mOutDoorV2Ctrl);
                            c.gotoAction(actionType,subType);
                        }

                    }
                });
            }else {
                OutDoorV2GetCustomer c = new OutDoorV2GetCustomer(context,checkinsInfo,mOutDoorV2Ctrl);
                c.gotoAction(actionType,subType);
            }

            return true;
        }

        return false;
    }

    private boolean isImageAction(String subType){
        if (checkinsInfo!=null&&checkinsInfo.customerActionSimples!=null&&checkinsInfo.customerActionSimples.size()>0){

            for (CustomerActionSimple cas:checkinsInfo.customerActionSimples) {
                if (cas.actionId.equals(subType)&&"photo_first".equals(cas.actionCode)){
                    return true;
                }
            }
        }
        return false;
    }

    private GetPlanInfoArgs checkAcId(GetPlanInfoArgs args){

        boolean isSub = false;
        if (!TextUtils.isEmpty(args.subType)) {
            if (checkType.actionList != null) {
                for (CustomerAction ca:checkType.actionList) {
                    if (ca.actionId.equals(args.subType)) {
                        isSub = true;
                    }
                }
                if (!isSub) {
                    if (actionPos>-1&&actionPos < checkType.actionList.size()) {
                        args.subType = checkType.actionList.get(actionPos).actionId;
                    }else {
                        args.actionType = OutDoorV2Constants.GOTO_OUTDOOR_INFO_KEY;
                    }
                }

            }else {
                args.actionType = OutDoorV2Constants.GOTO_OUTDOOR_INFO_KEY;
            }
        }
        return args;
    }
    private int refcount =0;
    private boolean isRefData(CheckType ct ,CheckinsInfo info){

        List caslist = new ArrayList();

        if (info !=null&&info.customerActionSimples!=null){
            for (CustomerActionSimple casl:info.customerActionSimples) {
                if (casl.dataStatus==1){
                    caslist.add(casl.sourceActionId);
                }
            }
        }
        if (ct !=null&&ct.actionList!=null&&caslist.size()>0){
            for (CustomerAction c:ct.actionList) {
                if (caslist.contains(c.sourceActionId)&&c.dataStatus==0){
                    FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"isRefData 0 ");
                    return true;
                }
            }
        }

        int ccsize =0;
        if (info!=null&&info.customerActionSimples!=null){
            for (CustomerActionSimple cc:info.customerActionSimples
                 ) {
                if (!cc.actionId.equals("many")){
                    ccsize++;
                }
            }
        }

        if (ct!=null&&info!=null&&ct.actionList!=null&&ccsize!=0&&ct.actionList.size()!=ccsize){
            FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"isRefData checkinid="+(checkinsInfo!=null?checkinsInfo.indexId:"null")+", ct.actionList.size()="+ct.actionList.size()+",ccsize="+ccsize);
            return true;
        }

        return false;
    }

    private void isSendGetPlanInfo(){
        if (checkType!=null&&context!=null&& (context instanceof OutdoorRecordListActivity || context instanceof OutDoorNewRecordListActivity)
                && checkinsInfo!=null&& checkinsInfo.locationForceRefresh==1 && !checkinsInfo.isOkAction()){
            OutDoor2CacheManger.deleteRecord(checkType.indexId);
            checkType = null;
            FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"isSendGetPlanInfo ");
        }
    }

    private void checkGotoAction(){
        if (!OutDoorV2Utils.getLoctionIsOK()){
            return;
        }
        if (checkinsInfo == null) return;
        String id = checkinsInfo.indexId;
        if (TextUtils.isEmpty(id)){
            id = checkinsInfo.checkinId;
        }
        //防止反复读写
        if(checkType==null){
            checkType = OutDoor2CacheManger.readCacheById(id);
        }

        if (checkType == null){
            //附近
            if (context!=null&&isNeartj()){
                checkType = OutdoorNearUtils.convertCheckType(checkinsInfo);
                checkType.needUpdateOwner = 1;
                checkType.checkinsInfo = checkinsInfo;
                if (checkType!=null){
                    OutDoor2CacheManger.saveCheckType(checkType);
                }
                checkGotoAction();
                return;
            }
            //铺货
            if(checkinsInfo.checkinsScene!=null
                    && IdAndNameEx.PUHUO.equals(checkinsInfo.checkinsScene.id)
                    && "offline".equals(checkinsInfo.targetCode)){
                checkType = PuHuoOfflineManager.convertCheckType(checkinsInfo);
                if(checkType!=null){
                    OutDoor2CacheManger.saveCheckType(checkType);
                    checkGotoAction();
                }else{
                    GetPlanInfoArgs args = OutdoorCalendarFragment.onListviewItemClick(checkinsInfo);
                    outDoorV2Presenter.getPlanInfoReq(OutDoorV2Presenter.planInfo_type,args);
                }

            }else{
                GetPlanInfoArgs args = OutdoorCalendarFragment.onListviewItemClick(checkinsInfo);
                outDoorV2Presenter.getPlanInfoReq(OutDoorV2Presenter.planInfo_type,args);
            }
        }else {
            if (isRefData(checkType,checkinsInfo)
                    &&context!=null&&(context instanceof OutdoorRecordListActivity||context instanceof OutDoorNewRecordListActivity)
            &&(OutDoorHomeStyleSetUtils.getRuleHomeStyleResultBykey(OutDoorHomeStyleSetUtils.kkIsCheckActionSize) == 1)
            &&refcount<3){
                FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG, "isRefData "+checkinsInfo.printfAll());
                checkType = null;
                GetPlanArgs args = new GetPlanArgs();
                args.checkId = checkinsInfo.checkinId;
                outDoorV2Presenter.getDailyInfoById(OutDoorV2Presenter.get_DailyInfoById_type2,args);
                return;
            }
            if (!isNeartj()){
                checkType.checkinsInfo = null;
            }
            FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG, "isHasMustField = "+checkinsInfo.isHasMustField);
            if (checkinsInfo.isHasMustField == 1){
                GetPlanInfoArgs getPlanInfoArgs = OutdoorCalendarFragment.onListviewItemClick(checkinsInfo);
                context.startActivity(OutDoorV2Activity.getIntent(context, getPlanInfoArgs));
            }else {
                checkAcId(gotoArgs);
                setAllData();
                if (!isNotDoubleGoto()){
//                    if (checkinsInfo.isAdd2Route == 2&&OutDoor2CacheManger.getCheckinsInfoHashMapByid(checkinsInfo.indexId)==null){
//                        showIsAddDialog(new ICustomerGeoUpdate() {
//                            @Override
//                            public void onResult(int result) {
//                                if (result ==1){
//                                    OutDoor2CacheManger.setCheckinsInfoHashMap(checkinsInfo,result);
//                                    mOutDoorV2Ctrl.gotoAction(gotoArgs,0);
//                                }
//
//                            }
//                        });
//                    }else {
//                   if (!checkType.isOperate) {
//                        if(!TextUtils.isEmpty(checkType.operateMessage)){
//                            ToastUtils.show(checkType.operateMessage);
//                        }else{
//                            ToastUtils.show(OutDoorV2Activity.NOT_OPERATE_STR);
//                        }
//                    }else {
                       mOutDoorV2Ctrl.gotoAction(gotoArgs, 0);
//                   }

//                    }

                }

            }

        }
    }

    private long lastClickTime = 0;
    private boolean isNotDoubleGoto(){
        if (checkType!=null&&checkType.autoSkipFirstAction==1
                &&checkinsInfo!=null&&checkinsInfo.customerActionSimples!=null&&checkinsInfo.customerActionSimples.size()>0){
            return false;
        }
        long currentTime = TimeZoneUtils.getCalendarTimeZone().getTimeInMillis();
        if (currentTime - lastClickTime > OnClickListenerProxy.MIN_CLICK_DELAY_TIME) {
            lastClickTime = currentTime;
            return false;
        }else {
            ToastUtils.show(OnClickListenerProxy.MIN_CLICK_DELAY_STRING);
            FCLog.i(OfflineUtils.OUTDOOR_OFFLINE, TAG,"  isNotDoubleGoto true ,currentTime="+currentTime+",lastClickTime = "+lastClickTime);
            return true;
        }
    }

    private void getDailyInfoByIdData(){
        GetPlanArgs args = new GetPlanArgs();
        args.checkId = checkinsInfo.checkinId;
        outDoorV2Presenter.getDailyInfoById(OutDoorV2Presenter.get_DailyInfoById_type,args,false);
    }

    private void showIsAddDialog(ICustomerGeoUpdate iCustomerGeoUpdate) {
        final CommonDialog myDialog = new CommonDialog(context);
        CommonDialog.myDiaLogListener myDiaLogListener = new CommonDialog.myDiaLogListener() {
            @Override
            public void onClick(View view) {
                int i = view.getId();
                if (i == R.id.button_mydialog_cancel) {
                    iCustomerGeoUpdate.onResult(0);
                    myDialog.dismiss();

                } else if (i == R.id.button_mydialog_enter) {
                    iCustomerGeoUpdate.onResult(1);
                    myDialog.dismiss();
                }
            }
        };
        myDialog.setDialogListener(myDiaLogListener);
//        myDialog.setTitle(I18NHelper.getText("xt.dledfilefragment.text.note")/* 注意 */);
        myDialog.setMessage(I18NHelper.getText("wq.is.not.add.route.text.msg"));//"是否要加入路线？");
        myDialog.setCanceledOnTouchOutside(true);
        myDialog.show();
    }

    public void exeActionOk(CustomerAction ca){
        checkType = OutDoor2CacheManger.readCacheById(checkinsInfo.indexId);
        if (checkType == null){
            FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"exeActionOk checkType is null ");
            return;
        }
        initData();
        setAllData();
        OutDoorV2OkView outDoorV2OkView = (OutDoorV2OkView) mOutDoorV2Ctrl.getMapView(OutDoorV2OkView.class.getSimpleName());
        if (outDoorV2OkView != null) {
            outDoorV2OkView.exeOk(ca);
        }
    }
    public void exeActionOrderOk(){
        checkType = OutDoor2CacheManger.readCacheById(checkinsInfo.indexId);
        if (checkType == null){
            FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"exeActionOrderOk checkType is null ");
            return;
        }
        initData();
        setAllData();
        OutDoorV2OkView outDoorV2OkView = (OutDoorV2OkView) mOutDoorV2Ctrl.getMapView(OutDoorV2OkView.class.getSimpleName());
        if (outDoorV2OkView != null) {
            outDoorV2OkView.sendTask();
        }
    }
    @Override
    public void complete(int type, Object o) {
        FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"complete type==> "+type);
        if (o == null) {
            return;
        }
        switch (type){
            case OutDoorV2Presenter.get_customer_me:{
                checkinsInfo.needUpdateOwner = 0;
                if(checkinsInfo.isNoCheckins==0){
                    gotoAction(OutDoorV2Constants.SIGNIN_KEY,gotoArgs.subType);
                }else{
                    gotoAction(OutDoorV2Constants.ACTION_LIST_KEY,gotoArgs.subType);
                }
                EventBus.getDefault().post(new
                        OutdoorRefreshBean(checkinsInfo,OutdoorRefreshBean.BACK_TODAY));
                break;
            }
            case OutDoorV2Presenter.createCheckinsV3_type :{
                if(context instanceof OutdoorSearchActivity){
                    EventBus.getDefault().post(new OutdoorBehavior(OutdoorBehavior.CLOSE_SEARCH_ACTIVITY));//在查询页面搜出结果中执行签到时关闭页面
                }

                CheckinsV2Result createCheckinsResult = (CheckinsV2Result) o;
                if (createCheckinsResult!=null&&createCheckinsResult.getBizCode()!=0){
                    FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"getBizCode,"+ createCheckinsResult.getBizCode());
                }else {
                    OutDoorV2ActionListGroup.setmintime(checkType,createCheckinsResult.checkinTimeLong);
                    checkType.chekinInfoData.checkInTime = createCheckinsResult.checkinTime;
                    checkType.chekinInfoData.checkinId = createCheckinsResult.checkinId;
                    if (!TextUtils.isEmpty(createCheckinsResult.checkinPlanTimeStr)){
                        checkType.chekinInfoData.checkinPlanTime = createCheckinsResult.checkinPlanTimeStr;
                    }
                    checkType.indexId  = createCheckinsResult.checkinId;
                    checkType.chekinInfoData.checkinAddress = createCheckinsResult.checkinAddress;
                    checkType.actionList = CustomerAction.getCustomerActionList(createCheckinsResult.customerActionList);
//                    AvaCacheAddHandler.AddOutDoorCache(context,checkType);
                    checkType.setStopjumpShop(0);
                    OutDoor2CacheManger.saveCheckType(checkType, OutDoor2CacheManger.checkTypeToGetPlanInfoArgs(checkType));
                }

                getDailyInfoByIdData();
                if (checkType!=null&&checkType.autoSkipFirstAction==1
                        &&checkinsInfo!=null&&checkinsInfo.customerActionSimples!=null&&checkinsInfo.customerActionSimples.size()>0){
                    gotoAction(OutDoorV2Constants.ACTION_LIST_KEY,checkinsInfo.customerActionSimples.get(0).actionId,0);
                }
                break;
            }
            case OutDoorV2Presenter.createCheckOut_type :{
                UpdateCheckinsResult updateCheckinsResult = (UpdateCheckinsResult) o;
                checkType.chekinInfoData.checkOutTime = updateCheckinsResult.checkinTime;
                checkType.chekinInfoData.checkOutAddress = updateCheckinsResult.checkinAddress;
                CheckType.setNotJumpShop(checkType);
                OutDoor2CacheManger.saveCheckType(checkType, OutDoor2CacheManger.checkTypeToGetPlanInfoArgs(checkType));
                getDailyInfoByIdData();
                if (checkType.checkOutFlag == 2) {
                    FMCGoderSaveUtils.delReptor(checkType);
                    ToastUtils.show(I18NHelper.getText("wq.outdoorv2_action_listgroup.text.operation_succeed")/*  操作成功，外勤结束！  */);
                }

                break;
            }
            case OutDoorV2Presenter.get_DailyInfoById_type:{
                GetDailyInfoByIdResult getDailyInfoByIdResult = (GetDailyInfoByIdResult) o;
                OutdoorRefreshBean bean = new OutdoorRefreshBean(OutdoorRefreshBean.REFRESH_LIST_ITEM);
                bean.o = getDailyInfoByIdResult.checkinsInfo;
                OutDoor2CacheManger.saveCheckType(getDailyInfoByIdResult.checkType, OutDoor2CacheManger.checkTypeToGetPlanInfoArgs(checkType));
                //刷新item
                EventBus.getDefault().post(bean);
                if (!TextUtils.isEmpty(getDailyInfoByIdResult.totalIntegral)){
                    OutdoorRefreshBean bean2 = new OutdoorRefreshBean(OutdoorRefreshBean.REFRESH_LIST_Total_Integral);
                    bean2.o = getDailyInfoByIdResult.totalIntegral;
                    EventBus.getDefault().post(bean2);
                }
                break;
            }
            case OutDoorV2Presenter.get_DailyInfoById_type2:{
                GetDailyInfoByIdResult getDailyInfoByIdResult = (GetDailyInfoByIdResult) o;
                OutdoorRefreshBean bean = new OutdoorRefreshBean(OutdoorRefreshBean.REFRESH_LIST_ITEM);
                bean.o = getDailyInfoByIdResult.checkinsInfo;
                checkType = getDailyInfoByIdResult.checkType;
                checkinsInfo = getDailyInfoByIdResult.checkinsInfo;
                print2(checkType,checkinsInfo);
                OutDoor2CacheManger.saveCheckType(getDailyInfoByIdResult.checkType, OutDoor2CacheManger.checkTypeToGetPlanInfoArgs(checkType));
                //刷新item
//                EventBus.getDefault().post(bean);
                refcount++;
                checkGotoAction();
                break;
            }
            case OutDoorV2Presenter.updateCheckReq_ok_type:{
                FsTickUtils.tickWQ(FsTickUtils.advance_finish_click);
                FMCGoderSaveUtils.delReptor(checkType);
                CheckType.setNotJumpShop(checkType);
                getDailyInfoByIdData();
//                EventBus.getDefault().post(new OutdoorRefreshBean());
                UpdateCheckinsResult updateCheckinsResult = (UpdateCheckinsResult) o;
                if(updateCheckinsResult.isSendRedPacket == 1){
                    OutDoorV2Utils.showRedPacketDiaLog(context, updateCheckinsResult, new DialogInterface.OnDismissListener() {
                        @Override
                        public void onDismiss(DialogInterface dialog) {
                            com.facishare.fs.utils_fs.ToastUtils.show(I18NHelper.getText("wq.outdoorv2_action_listgroup.text.operation_succeed")/*  操作成功，外勤结束！  */);
                        }
                    });
                }else{
                    com.facishare.fs.utils_fs.ToastUtils.show(I18NHelper.getText("wq.outdoorv2_action_listgroup.text.operation_succeed")/*  操作成功，外勤结束！  */);
                }
//                getTotalIntegral();
                break;
            }
            case OutDoorV2Presenter.get_TotalIntegral :{
                GetTotalIntegralResult result = (GetTotalIntegralResult) o;
                OutdoorRefreshBean bean = new OutdoorRefreshBean(OutdoorRefreshBean.REFRESH_LIST_Total_Integral);
                bean.o = result;
                EventBus.getDefault().post(bean);
                break;
            }
            case OutDoorV2Presenter.planInfo_type :{
                checkType = ((GetPlanInfoResult)o).checkType;
                FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"planInfo_type checkType==> "+(checkType!=null?checkType.printf():"null"));
                if (checkType != null) {
                    OutDoor2CacheManger.saveCheckType(checkType);
                    checkGotoAction();
                }
                break;
            }

        }
    }

    private void print2(CheckType ct,CheckinsInfo info){
        int csize = 0;
        int infoszie =0;
        if (ct!=null&&ct.actionList!=null){
            csize = ct.actionList.size();
        }
        if (info!=null&&info.customerActionSimples!=null){
            infoszie = info.customerActionSimples.size();
        }
        FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"print2 csize="+csize+",infoszie="+infoszie);

    }
    @Override
    public void faild(int type, WebApiFailureType failureType,int errorCode, String error) {
        FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"faild type==> "+type+",error="+error+",errorCode:"+errorCode);
        if (failureType!=null&&(failureType.getIndex()==WebApiFailureType.NetworkDisableError.getIndex())){
            if (type == OutDoorV2Presenter.updateCheckReq_ok_type){
                ToastUtils.show(error);
            }
        }else {
            if (errorCode==177){
                OutdoorCommonUtils.showErrorDialog((Activity) context,error,false);
            }else if(errorCode == 1086){
                OutDoorV2PlanActivity.showerrorDialog(context,error);
            }else{
                ToastUtils.show(error);
            }
        }

        if (type == OutDoorV2Presenter.updateCheckReq_ok_type){
            EventBus.getDefault().post(new OutdoorRefreshBean(OutdoorRefreshBean.REFRESH_LIST_ITEM_OK_VIEW_TAG));
        }else if (type == outDoorV2Presenter.planInfo_type){
            if (gotoArgs!=null&&OutDoorV2Constants.OK_KEY.equals(gotoArgs.actionType)){
                EventBus.getDefault().post(new OutdoorRefreshBean(OutdoorRefreshBean.REFRESH_LIST_ITEM_OK_VIEW_TAG));
            }
        }


    }

//    private void listTickTag(String actionid){
//        if (!TextUtils.isEmpty(actionid)&&checkinsInfo!=null&&checkinsInfo.customerActionSimples!=null&&checkinsInfo.customerActionSimples.size()>0){
//            String sid = null;
//            for (CustomerActionSimple ca: checkinsInfo.customerActionSimples
//                 ) {
//                if (!TextUtils.isEmpty(ca.actionId) && ca.actionId.equals(actionid)){
//                    sid = ca.sourceActionId;
//                }
//            }
//            if (!TextUtils.isEmpty(sid)){
//                FsTickUtils.tickWQ(FsTickUtils.getCalendaroutleakageaction(sid));
//            }
//
//        }
//
//    }
}
