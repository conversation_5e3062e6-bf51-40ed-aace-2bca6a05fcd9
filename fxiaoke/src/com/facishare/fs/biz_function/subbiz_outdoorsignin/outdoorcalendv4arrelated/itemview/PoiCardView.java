package com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorcalendv4arrelated.itemview;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.os.Build;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.facishare.fs.App;
import com.facishare.fs.BaseActivity;
import com.facishare.fs.biz_feed.utils.FeedSP;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.FsTickUtils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.OutdoorRecordListActivity;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.PoiData;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CheckinsInfo;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.GetEmployeeRuleResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.InsertCustomer2TodayRouteArgs;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.ObjectInfo;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.SaveUnRecommendDataArg;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.OutDoor2CacheManger;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.near.OutdoorNearUtils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.newrecordlist.INearListCb;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.newrecordlist.OutDoorAddcutomer;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.newrecordlist.OutDoorNewRecordListActivity;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorcalendv4arrelated.OutdoorRefreshBean;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorcalendv4arrelated.OutdoorV4RecordListAdapter;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.ICustomerGeoUpdate;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2Presenter;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2Utils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.utils.OutdoorCommonUtils;
import com.facishare.fs.biz_session_msg.views.TitlePopWindow;
import com.facishare.fs.bpm.data.source.RequestCallBack;
import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.dialogs.CommonDialog;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.actions.BaseAddAction;
import com.facishare.fs.metadata.actions.MetaDataAddContext;
import com.facishare.fs.metadata.actions.MetaDataBaseAddAction;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.config.MetaDataConfig;
import com.facishare.fs.metadata.config.factory.AddNewObjectSource;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.facishare.fs.pluginapi.crm.type.ICrmBizApiName;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fslib.R;
import com.fxiaoke.fscommon.sandbox.SandboxUtils;
import com.fxiaoke.fscommon_res.activity.FCBaseActivity;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.location.api.FsLocationResult;
import com.fxiaoke.location.impl.FsMultiLocationManager;
import com.google.gson.internal.LinkedTreeMap;
import com.nostra13.universalimageloader.core.DisplayImageOptions;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.assist.ImageScaleType;

import java.util.ArrayList;
import java.util.List;

import de.greenrobot.event.EventBus;

public class PoiCardView extends AbsView implements OutDoorV2Presenter.IOutdoorCallBack{

    private Context mContext;

    private PoiData mPoiData;

    private View mRootView;

    private BaseAddAction mAddAction;
    private OutDoorV2Presenter mPresenter;
    private ObjectData currentCustomer;

    private FCBaseActivity activity;
    private OutdoorV4RecordListAdapter adapter;
    public static final String LOCATION_DIV = "#%$";
    TitlePopWindow popWindow;
    INearListCb iNearListCb;
    public PoiCardView(Context cxt, PoiData poi){
        activity = (FCBaseActivity) cxt;
        mPresenter = new OutDoorV2Presenter(cxt);
        mPresenter.setLS(this);
        this.mContext = cxt;
        this.mPoiData = poi;
        this.mRootView = LayoutInflater.from(cxt).inflate(R.layout.outdoor_poi_list_layout,null,false);
        findView(mRootView);
        initView();
    }

    public void setiNearListCb(INearListCb iNearListCb) {
        this.iNearListCb = iNearListCb;
    }

    public void setada(OutdoorV4RecordListAdapter adapter){
        this.adapter = adapter;
    }

    private void addInfo( TextView tv_info ,String info,String title){
        if(!TextUtils.isEmpty(info)){
            int length = tv_info.getText().length();
            String content = title+"："+info;
            if (length==0){
                tv_info.append(content);
            }else{
                tv_info.append("\n"+content);
            }
        }
        int length = tv_info.getText().length();
        tv_info.setVisibility(length>0?View.VISIBLE:View.GONE);
    }

    private void initView(){
        ImageView door_photo_img = findViewById(R.id.door_photo_img);

        TextView tv_address = findViewById(R.id.tv_address);

        TextView tv_shopName = findViewById(R.id.tv_shopname);

        TextView tv_info = findViewById(R.id.tv_info);
        Button btnAddCustomer = findViewById(R.id.btnAddCustomer);

        if (mPoiData.showButApiNames!=null&&mPoiData.showButApiNames.size()>0){
            LinkedTreeMap map =  (LinkedTreeMap) mPoiData.showButApiNames.get(0);
            btnAddCustomer.setText(map.get("label")!=null?(String)map.get("label"):"");
            if (map.get("colour")!=null) {
                GradientDrawable myGrad = (GradientDrawable)btnAddCustomer.getBackground();
                myGrad.setColor(Color.parseColor((String)map.get("colour")));
            }
        }
        btnAddCustomer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                FsTickUtils.tickWQ(FsTickUtils.recommendcustomer_addtomycustomer_click);
                CardUtils.onClickBtnNear(mPoiData, new ICustomerGeoUpdate() {
                    @Override
                    public void onResult(int result) {
                        if (result==1){
                            if (iNearListCb!=null){
                                addCustomerEx();
                            }else {
                                addCustomer();
                            }
                        }
                    }
                });


            }
        });

        tv_shopName.setText(mPoiData.name);

        addInfo(tv_info,mPoiData.ztype,I18NHelper.getText("pay.common.common.type"));//"类型");
        addInfo(tv_info,mPoiData.getTel(),I18NHelper.getText("wq.phone.text.msg"));//"电话");

         if(TextUtils.isEmpty(mPoiData.iconUrl) ){
             door_photo_img.setVisibility(View.GONE);
         }
        DisplayImageOptions.Builder builder = new DisplayImageOptions.Builder()
                .context(App.getInstance())
                .showImageOnLoading(R.drawable.img_placeholder)
                .showImageForEmptyUri(R.drawable.img_placeholder)
                .showImageOnFail(R.drawable.img_placeholder)
                .cacheInMemory(true)
                .cacheOnDisk(true)
                .imageScaleType(ImageScaleType.EXACTLY_STRETCHED)
                .bitmapConfig(Bitmap.Config.RGB_565)
                ;
        ImageLoader.getInstance()
                .displayImage(OutDoorV2Utils.getDownLoadUrl(SandboxUtils.getActivityByContext(mContext), mPoiData.iconUrl),
                        door_photo_img,builder.build());

        if(mPoiData.location!=null && mPoiData.location.length()>0){
            String [] strings = mPoiData.location.split(",");
            double lat = Double.parseDouble(strings[1]);
            double lng = Double.parseDouble(strings[0]);

            FsLocationResult result = FsMultiLocationManager.getInstance().getLastLocation();
            tv_address.setHighlightColor(mContext.getResources().getColor(android.R.color.transparent));

            String content =   CardUtils.getDis(mPoiData.distanceBetween)+" | "+getAddress();
            String direction= "";

            if(result != null){
                 direction = CardUtils.getDirection(result.getLatitude(),result.getLongitude(),lat,lng);
            }

            CheckinsInfo tempinfo = new CheckinsInfo();
            tempinfo.customerAddress = getAddress();
            tempinfo.customerLon = lng;
            tempinfo.customerLat = lat;

            OutDoorV2Utils.insertData(mContext,tv_address,
                    (direction==null?"":(direction +" ")+content)+" ", I18NHelper.getText("kwq.FSAdvanceFieldworkListCell.navi")/*导航*/,
                    tempinfo);
        }
        View dview = findViewById(R.id.name_del);
        GetEmployeeRuleResult ruleResult = OutDoor2CacheManger.getCacheRule();
        if (ruleResult!=null&&ruleResult.nearConfig!=null
                &&ruleResult.nearConfig.isOpenNotRecommend==1){
            dview.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
//                    del(activity);
                    if (popWindow == null) {
                        TitlePopWindow.DisplayConfig config = TitlePopWindow.DisplayConfig.create(TitlePopWindow.DisplayConfig.Orientation.RIGHT);
                        if (Build.VERSION.SDK_INT < 24){
                            config = config.setWindowYOffset(FSScreen.dip2px(-85))
                                    .build();
                        }

                        popWindow = new TitlePopWindow(mContext, config);
                        List<TitlePopWindow.ItemData> data = new ArrayList<TitlePopWindow.ItemData>();
                        TitlePopWindow.ItemData data1 = new TitlePopWindow.ItemData();
                        data1.name = I18NHelper.getText("wq.nearBy.text.not_recommond");//"不再推荐";
                        data1.id = 1;
                        data.add(data1);
                        popWindow.setData(data, new TitlePopWindow.OnItemClickLis() {
                            @Override
                            public void onItemClick(int pos) {
                                del(activity);
                            }
                        });
                    }
                    popWindow.show(dview,null);
                }
            });


        }else {
            dview.setVisibility(View.GONE);
        }


    }

    private void del(Context context){
//        String tname = "确定不在推荐该客户吗？";
//        String okbt = "确定";
//        ComDialog.showConfirmDialog(context,tname , "",okbt,true, new View.OnClickListener() {
//                    @Override
//                    public void onClick(View v) {
//                        SaveUnRecommendDataArg arg = new SaveUnRecommendDataArg();
//                        arg.data = mPoiData;
//                        mPresenter.saveUnRecommendData(OutDoorV2Presenter.saveUnRecommendData_code,arg);
//                    }
//                }
//        );
        if (FeedSP.getBooleanType("poicard_del")){
            SaveUnRecommendDataArg arg = new SaveUnRecommendDataArg();
            arg.data = mPoiData;
            mPresenter.saveUnRecommendData(OutDoorV2Presenter.saveUnRecommendData_code,arg);
        }else {
            showIsCleanOwnerDialog();
        }


    }
    private void showIsCleanOwnerDialog(){
        CommonDialog commonDialog = new CommonDialog(mContext);
        commonDialog.setMessage(I18NHelper.getText("ava.field_work.near_customer_norecommend"));//"确定不在推荐该客户吗？");
        commonDialog.setShowType(commonDialog.FLAG_no_checkbox_no_prompt);
        commonDialog.setCancelable(false);
        commonDialog.setCanceledOnTouchOutside(false);
        commonDialog.setDialogListener(new CommonDialog.myDiaLogListener() {
            @Override
            public void onClick(View view) {
                commonDialog.dismiss();
                if(view != null){
                    int id = view.getId();
                    if (id == R.id.button_mydialog_enter) {
                        FeedSP.saveBooleanType("poicard_del",commonDialog.checkboxIsSelect());
                        SaveUnRecommendDataArg arg = new SaveUnRecommendDataArg();
                        arg.data = mPoiData;
                        mPresenter.saveUnRecommendData(OutDoorV2Presenter.saveUnRecommendData_code,arg);

                    }
                }
            }
        });
        commonDialog.show();
    }
    public final <T extends View> T findViewById(int id){
        return mRootView.findViewById(id);
    }

    @Override
    public View getView() {
        return  this.mRootView;
    }

    @Override
    public void findView(View view) {

    }

    @Override
    public void updateData(Object o) {

    }


    public void addCustomer(){
        if (mAddAction == null) {
            mAddAction = MetaDataConfig.getOptions().getMetaBizImplFactories()
                    .getOperationFactory(ICrmBizApiName.ACCOUNT_API_NAME)
                    .getAddAction(activity.getMultiContext(), AddNewObjectSource.LIST)
                    .setCallBack(new MetaDataBaseAddAction.AddActionCallBack() {
                        @Override
                        public void onAddSuccess(ObjectData result) {
                            currentCustomer = result;
                            FsTickUtils.tickWQ(FsTickUtils.recommendcustomer_addtomycustomer_add);
                            customerToRoute(result);
                        }
                    });
        }
        ObjectData sourceData = new ObjectData();
        sourceData.setName(mPoiData.name);//客户名称
        fillData(sourceData,"tel",mPoiData.getTel());//电话

        String [] strings = mPoiData.location.split(",");
        double lat = Double.parseDouble(strings[1]);
        double lng = Double.parseDouble(strings[0]);
        fillData(sourceData,"location",lng+LOCATION_DIV+lat+LOCATION_DIV+getAddress());//定位
        fillData(sourceData,"address",getAddress());//详细地址
        fillData(sourceData,"poi_information",mPoiData.id);
        fillData(sourceData,"account_source","poi");
        mAddAction.setMasterObjectData(sourceData);
        activity.showDialog(activity.WAIT_NORMAL_TYPE);
        new Thread(new Runnable() {
            @Override
            public void run() {
                FsLocationResult result = FsMultiLocationManager.getInstance().getLastLocation();
                String country = result.getCountryName();
                String province = mPoiData.province;
                String city = mPoiData.city;
                String district =mPoiData.area ;
                MetaDataUtils.getAreaCodeByName(activity, country, province, city, district, new RequestCallBack.DataCallBack<MetaDataUtils.AreaCodes>() {
                    @Override
                    public void onDataLoaded(MetaDataUtils.AreaCodes result) {
                        activity.removeDialog(activity.WAIT_NORMAL_TYPE);
                        fillData(sourceData,"country",result.country);//国家
                        fillData(sourceData,"province",result.province);//省
                        fillData(sourceData,"city",result.city);//市
                        fillData(sourceData,"district",result.district);//区
                        startCrm();
                    }

                    @Override
                    public void onDataNotAvailable(String error) {
                        activity.removeDialog(activity.WAIT_NORMAL_TYPE);
                        startCrm();
                    }
                });
            }
        }).start();


    }

    private void startCrm(){
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mAddAction.start(new MetaDataAddContext() {
                    @Override
                    public String getTargetApiName() {
                        return ICrmBizApiName.ACCOUNT_API_NAME;
                    }
                });
            }
        });
    }



    private String getAddress(){
        StringBuffer address = new StringBuffer();
        ArrayList<String> list = new ArrayList<String>(8);
        address.append(addAddressItem(list,mPoiData.province));
        address.append(addAddressItem(list,mPoiData.city));
        address.append(addAddressItem(list,mPoiData.area));
        address.append(addAddressItem(list,mPoiData.address));
        return address.toString();
    }

    private String addAddressItem(ArrayList<String> list, String s) {
        if (TextUtils.isEmpty(s)) {
            return "";
        }
        if (!list.contains(s)) {
            list.add(s);
            return s;
        } else {
            return "";
        }
    }
    public void fillData(ObjectData sourceData,String key,String value){
        if(!TextUtils.isEmpty(value)){
            sourceData.put(key, value);
        }
    }

    /**
     *
     * @param result
     */
    public void customerToRoute(ObjectData result){
        InsertCustomer2TodayRouteArgs args = new InsertCustomer2TodayRouteArgs();
        args.customerName = result.getName();
        args.customerId = result.getID();
//        args.sceneId = IdAndNameEx.ROUTEASSISTANT;
        String locaiton = result.getString("location");
        if(!TextUtils.isEmpty(locaiton)){
            String[] address = locaiton.split("\\#\\%\\$");
            args.customerLon = Double.parseDouble(address[0]);
            args.customerLat = Double.parseDouble(address[1]);
            args.customerAddr = address[2];
        }
        args.checkTypeId= OutdoorNearUtils.getSelectCheckTypeId();
        mPresenter.insertCustomer2TodayRoute(OutDoorV2Presenter.insert_customer_todayroute,args);
    }

    @Override
    public void complete(int type, Object o) {
        if (activity!=null&&!activity.isFinishing()) {
            if (type == OutDoorV2Presenter.saveUnRecommendData_code){
                adapter.delbydata(mPoiData);
            }else{
                EventBus.getDefault().post(new
                        OutdoorRefreshBean(OutdoorRefreshBean.REFRESH_SELECT_DAY));

                if((activity instanceof OutdoorRecordListActivity||activity instanceof OutDoorNewRecordListActivity)){

                    if (activity instanceof OutdoorRecordListActivity){
                        OutdoorRecordListActivity oact = (OutdoorRecordListActivity) activity;
                        oact.showDialog(BaseActivity.DIALOG_WAITING_UPDATE_MODE);
                        oact.addCustomer(mPoiData,currentCustomer);
                    }else if (activity instanceof OutDoorNewRecordListActivity){
                        OutDoorNewRecordListActivity oact = (OutDoorNewRecordListActivity) activity;
                        oact.showDialog(BaseActivity.DIALOG_WAITING_UPDATE_MODE);
                    }
                }else{
                    activity.finish();
                }
            }


        }else {
            FCLog.i("OutdoorV2ListFragment","OutdoorV2ListFragment null");
        }
    }

    @Override
    public void faild(int type, WebApiFailureType failureType, int errorCode, String error) {
        if (activity!=null&&!activity.isFinishing()) {
            if(errorCode==177){
                OutdoorCommonUtils.showErrorDialog(activity,error,false);
            }else if(failureType == WebApiFailureType.BusinessFailed){
                CommonDialog.createOneButtonDialog(activity, null, error, I18NHelper.getText("meta.modify"
                        + ".LookupMView.1"/*我知道了*/)).show();
            }else{
                ToastUtils.show(error);
            }
        }
    }


    private ObjectInfo getCreateMainobj(String apiname){
        ObjectInfo mainobj = new ObjectInfo();
        mainobj.apiName = apiname;
        String [] strings = mPoiData.location.split(",");
        double lat = Double.parseDouble(strings[1]);
        double lng = Double.parseDouble(strings[0]);
        mainobj.info = lng+LOCATION_DIV+lat+LOCATION_DIV+getAddress();
        mainobj.locationFieldApiName = "123";
        mainobj.name = "555";

        return mainobj;
    }

    OutDoorAddcutomer outDoorAddcutomer;
    private void addCustomerEx(){
        if (outDoorAddcutomer==null){
            outDoorAddcutomer = new OutDoorAddcutomer(activity,mPoiData);
        }
        GetEmployeeRuleResult result = OutDoor2CacheManger.getCacheRule();
        String apiname = "";
        if (result!=null&&result.nearConfig!=null&&!TextUtils.isEmpty(result.nearConfig.addObjApiName)){

            if (!ICrmBizApiName.ACCOUNT_API_NAME.equals(result.nearConfig.addObjApiName)&&result.nearConfig.addObjFieldMap==null){
                ToastUtils.show(result.nearConfig.addObjApiName+" map is null");
                return;
            }

            apiname = result.nearConfig.addObjApiName;
            outDoorAddcutomer.setApiName(apiname);
            outDoorAddcutomer.setObjectData(result.nearConfig.addObjFieldMap);
        }

        CardUtils.checkLocation(mContext, getCreateMainobj(apiname), null, new ICustomerGeoUpdate() {
            @Override
            public void onResult(int result) {
                if (result>-1){
                    outDoorAddcutomer.createObjCustomer();
                }
            }
        });


    }

}
