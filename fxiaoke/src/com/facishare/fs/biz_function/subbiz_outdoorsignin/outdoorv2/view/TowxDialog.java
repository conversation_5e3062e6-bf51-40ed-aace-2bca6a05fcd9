package com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2Utils;
import com.facishare.fs.camera.BaseFsCameraActivity;
import com.facishare.fs.camera.utils.ViewUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.utils_fs.FsSdcardUtils;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fslib.R;
import com.fxiaoke.fscommon.share.ShareHelper;
import com.fxiaoke.fxlog.FCLog;

import java.io.File;
import java.io.FileOutputStream;

public class TowxDialog extends Dialog {
    Context context;
    public TowxDialog(@NonNull Context context) {
        super(context, R.style.LoadingProDialog);
        this.context = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        View view = LayoutInflater.from(context).inflate(R.layout.attendance_towx_popup, null);
        Window window = this.getWindow() ;
        window.setContentView(view);
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

        findViewById(R.id.towx_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();

            }
        });
        findViewById(R.id.towx_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                towx(true);
                dismiss();
            }
        });
        findViewById(R.id.down_save_LinearLayout).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                savelocal();
                dismiss();
            }
        });

    }

    private void savelocal() {
        towx(false);
    }


    private void towx(boolean bl){
        LinearLayout linearLayout = ((Activity)context).findViewById(R.id.LinearLayout_all);
        Bitmap bitmap = OutDoorV2Utils.getViewGroupBitmap(linearLayout);
        String path = ViewUtils.getThumbnailDir()+ViewUtils.getPhotoFilename();
        File imgFile=null;
        try {
            imgFile = new File(path);
            FileOutputStream fos = new FileOutputStream(imgFile);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 80, fos);
            fos.flush();
            fos.close();
        } catch (Exception e) {

        }
        if (bl){
            ShareHelper.getWXShareHelper(context).sendImage(bitmap,path,true);
        }else {
            File srcFile = new File(path);
            File destFile = new File(FsSdcardUtils.getPictureDir(),srcFile.getName());
            FsSdcardUtils.savePicture(context,path,destFile);
            ToastUtils.show(I18NHelper.getText("xt.subbiz_outdoorsignin.DOutdoorFragment.2"));//"保存成功");
            if (imgFile!=null&&imgFile.isFile() && imgFile.exists()) {
                imgFile.delete();
            }
        }

        if(bitmap!=null&&!bitmap.isRecycled()){
            bitmap.recycle();
        }
    }

}
