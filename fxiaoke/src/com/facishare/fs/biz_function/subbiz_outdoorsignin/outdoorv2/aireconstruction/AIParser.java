package com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.aireconstruction;

import android.text.TextUtils;

import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.ImageFile;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.aidetectbean.AIdataVaule;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.outdoor_offline.tasktype.Outdoorv2AUTOSaveNumberFormActionTask;
import com.fs.commonviews.ai.AINeedDealWithData;
import com.fxiaoke.fxlog.FCLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AIParser {

    public static class AIData{
        public List<ImageFile> path;
        public List<Map<String,Object>> formData;
        public List<Map<String,Object>> posm;
        public Map<String,Object> productObj;
        public Map<String,Object> spuData;
        public Map<String,Object> objData;
        public Map<String,String> fanpaiPath;
        public Map<String,String> cuowuPath;
        public Map<String,List<Map<String,Object>>> similarImageInfoMap;
        public List<Map<String,Object>> sceneInfoMap;//陈列形式明细
    }



    public static AIData parse(List<AIdataVaule> list,String apiname,int filterRemakeData,int isOpenRemake){
        if(list!=null && list.size()>0){
            AIData aiData = new AIData();
            aiData.path = new ArrayList<>();
            aiData.formData = new ArrayList<>();
            aiData.posm = new ArrayList<>();
            aiData.productObj = new HashMap<>();
            aiData.spuData = new HashMap<>();
            aiData.objData = new HashMap<>();
            aiData.fanpaiPath = new HashMap<>();
            aiData.cuowuPath = new HashMap<>();
            aiData.similarImageInfoMap = new HashMap<>();
            aiData.sceneInfoMap = new ArrayList<>();
            for(AIdataVaule  av: list){
                if(av.objectData!=null ){
                    boolean cuowu = false;
                    if(av.recapture || av.sceneMismatch){
                        cuowu = true;
                    }
                    if(filterRemakeData == 0 && cuowu){
                        FCLog.i(Outdoorv2AUTOSaveNumberFormActionTask.OUTDOOR_AI_EVENT, "是翻拍的 av.recapture   ===>"+ av.recapture);
                        FCLog.i(Outdoorv2AUTOSaveNumberFormActionTask.OUTDOOR_AI_EVENT, "是翻拍的 av.scene_error   ===>"+ av.sceneMismatch);
                    }else{
                        JSONObject skus = (JSONObject) av.objectData.get("ProductObj");
                        JSONObject spus = (JSONObject) av.objectData.get("SPUObj");
                        JSONObject object = null ;
                        if(!TextUtils.isEmpty(apiname)){
                            object = (JSONObject) av.objectData.get(apiname);
                        }
                        if(skus!=null){
                            aiData.productObj.putAll(skus);
                        }
                        if(spus!=null){
                            aiData.spuData.putAll(spus);
                        }
                        if(object!=null){
                            aiData.objData.putAll(object);
                        }
                    }

                }

                if(av.data!=null){
                    if(av.data.path!=null){
                        ImageFile imageFile = new ImageFile();
                        imageFile.filename = TextUtils.isEmpty(av.data.fileName)?av.data.path:av.data.fileName;
                        imageFile.path = av.data.path;
                        imageFile.apiName = av.data.senceapiname;
                        imageFile.ext = "jpg";
                        imageFile.recapture = av.recapture;
                        imageFile.scene_error = av.sceneMismatch;
                        imageFile.extraData = av.extraData;
                        if(av.data.similarImageInfoList != null && av.data.similarImageInfoList.size()>0){
                            aiData.similarImageInfoMap.put(av.data.path,av.data.similarImageInfoList);
                            imageFile.similar_error = true;
                        }else{
                            imageFile.similar_error = false;
                        }
                        aiData.path.add(imageFile);
                        if(av.data.similarImageInfoList != null && av.data.similarImageInfoList.size()>0){
                            aiData.similarImageInfoMap.put(av.data.path,av.data.similarImageInfoList);
                            imageFile.similar_error = true;
                        }else{
                            imageFile.similar_error = false;
                        }
                        if(av.recapture){
                            aiData.fanpaiPath.put(av.data.path,av.data.path);
                        }
                        if(av.sceneMismatch){
                            aiData.cuowuPath.put(av.data.path,av.data.path);
                        }
                    }
                    if(av.data.formData!=null){
                        boolean cuowu = false;
                        if(av.recapture || av.sceneMismatch){
                            cuowu = true;
                        }
                        if(filterRemakeData == 0 && cuowu && isOpenRemake == 1){
                            FCLog.i(Outdoorv2AUTOSaveNumberFormActionTask.OUTDOOR_AI_EVENT, "是翻拍的 formData av.recapture   ===>"+ av.recapture+",av.scene_error="+av.sceneMismatch);
                        }else {
                            setPublicData(av.data.formData,av.data);
                            aiData.formData.addAll(av.data.formData);

                        }
                    }
                    if(av.data.posm!=null){
                        boolean cuowu = false;
                        if(av.recapture || av.sceneMismatch){
                            cuowu = true;
                        }
                        if(filterRemakeData == 0 && cuowu){
                            FCLog.i(Outdoorv2AUTOSaveNumberFormActionTask.OUTDOOR_AI_EVENT, "是翻拍的 posm av.recapture   ===>"+ av.recapture+",av.scene_error="+av.sceneMismatch);
                        }else {
                            setPublicData(av.data.posm,av.data);
                            aiData.posm.addAll(av.data.posm);
                        }
                    }
                    if (av.data.sceneInfoMap!=null&&av.data.sceneInfoMap.size()>0){
                        List<Map<String,Object>> valueList = new ArrayList<>();
                        for (String key : av.data.sceneInfoMap.keySet()) {
                            List<Map<String,Object>> values = new ArrayList<>(av.data.sceneInfoMap.get(key));
                            setPublicData(values,av.data);
                            valueList.addAll(values);
                        }


                        aiData.sceneInfoMap.addAll(valueList);
                    }
                }
            }
            return aiData;
        }
        return null;
    }

    private static void setPublicData(List<Map<String,Object>> list, AINeedDealWithData aiNeedDealWithData){
        for (Object obj:list
        ) {
            if (obj instanceof JSONObject){
                JSONObject job = (JSONObject) obj;
                job.put("path",aiNeedDealWithData.path);
                job.put("checkin_field_apiname",aiNeedDealWithData.senceapiname);
            }else if (obj instanceof Map){
                Map job = (Map) obj;
                job.put("path",aiNeedDealWithData.path);
                job.put("checkin_field_apiname",aiNeedDealWithData.senceapiname);
            }

        }

    }

}
