package com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.cmlReport;

import android.content.Context;
import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CheckType;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CommonConfigResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CustomerAction;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.GetEmployeeRuleResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.GetIdsByWhereResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.GetLocalProductInfoResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.GetProductInfoResultV2;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.NewFormSetting;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.OutDoor2CacheManger;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.aiselfratingorderrelated.AIsaveDsplayPhotoUtiles;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.product.OutdoorProductManager;
import com.facishare.fs.js.utils.OutdoorLog;
import com.facishare.fs.utils_fs.OutDoorgetCommonConfigUtils;
import com.fxiaoke.fscommon.avatar.IAvaOpenerCallback;
import com.fxiaoke.fscommon_res.avatar.AvaOpener;
import com.fxiaoke.fxlog.FCLog;

import java.util.ArrayList;
import java.util.HashMap;

public class ReportAvaDataUtils {

    protected  static final String TAG = "ReportAvaDataUtils";

    public static NewFormSetting setAction(CustomerAction currentAction, CheckType mCheckType,HashMap<String,Object> maps){
        GetEmployeeRuleResult employeeRuleResult =  OutDoor2CacheManger.getCacheRule();
        String json = "";
        if(currentAction.newFormSetting != null){
            currentAction.newFormSetting.isSupportAIReport = getisSupportAIReport(currentAction);
            currentAction.newFormSetting.productRange = currentAction.productRange;
            currentAction.newFormSetting.isShowProductImg = currentAction.isShowProductImg;
            currentAction.newFormSetting.imgApiName = currentAction.imgApiName;
            currentAction.newFormSetting.dataStatus = currentAction.dataStatus;
            currentAction.newFormSetting.modelId = currentAction.modelId;
            currentAction.newFormSetting.sourceActionId = currentAction.sourceActionId;
            FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"sourceActionId = "+currentAction.sourceActionId);
            currentAction.newFormSetting.actionCode = currentAction.actionCode;
//            currentAction.newFormSetting.isSupportAIReport = 2;
            currentAction.newFormSetting.H5SaveNewFormID = (currentAction.indexId!=null?currentAction.saveId()+"newform":"");
            if(maps!=null&&maps.size()>0&& maps.get("objinfo")!=null){
                currentAction.newFormSetting.isEdit = false;
            }else{
                if(!CheckType.isActionStop(mCheckType)||currentAction.isFreeAction == 1){
                    currentAction.newFormSetting.isEdit = true;
                }else {
                    currentAction.newFormSetting.isEdit = false;
                }
            }

            if(mCheckType.shelfReportConfigMap!= null&&mCheckType.shelfReportConfigMap.size()>0){
                currentAction.newFormSetting.shelfReportConfig = mCheckType.shelfReportConfigMap.get(currentAction.newFormSetting.relateRefObj);
                if(TextUtils.isEmpty(currentAction.newFormSetting.shelfReportConfig)){
                    currentAction.newFormSetting.shelfReportConfig = mCheckType.shelfReportConfigMap.get("ShelfReportDetailObj");
                }
                FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"mCheckType.shelfReportConfigMap = "+mCheckType.shelfReportConfigMap);
            }else{
                GetEmployeeRuleResult getEmployeeRuleResult = OutDoor2CacheManger.getCacheRule();
                if(getEmployeeRuleResult != null ){
                    FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"getEmployeeRuleResult.shelfReportConfigMap = " + getEmployeeRuleResult.shelfReportConfigMap);
                    currentAction.newFormSetting.shelfReportConfig =getEmployeeRuleResult.spickhelfReportConfigMap(currentAction.newFormSetting.relateRefObj);
                }
            }
            if(employeeRuleResult != null && employeeRuleResult.formDataURL != null ){
                currentAction.newFormSetting.addStyle = employeeRuleResult.addStyle;
                FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"employeeRuleResult.addStyle = "+employeeRuleResult.addStyle);
            }
            if(currentAction.newFormSetting.keyboardList == null){
                currentAction.newFormSetting.keyboardList = new ArrayList<>();
            }
            CommonConfigResult commonConfigResult =  OutDoorgetCommonConfigUtils.getCommonConfigResult();
            if(commonConfigResult!=null &&  commonConfigResult.keyboardSimpleList!=null
                &&commonConfigResult.keyboardSimpleList.size()>0){
                currentAction.newFormSetting.keyboardList.addAll(commonConfigResult.keyboardSimpleList);
            }

//            GetLocalProductInfoResult getLocalProductInfoResult = JmlDisplayUtils.batch2LocalProduct(currentAction);
//            if(getLocalProductInfoResult !=null){
//                currentAction.newFormSetting.listMap = getLocalProductInfoResult.listMap;
//                FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"getLocalProductInfoResult batch  "+ JSONObject.toJSON(getLocalProductInfoResult.productIds).toString() );
//            }
            FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"1111111currentAction.newFormSetting.isEdit = "+currentAction.newFormSetting.isEdit);
            json = JSON.toJSONString(currentAction.newFormSetting.getMaps(currentAction.newFormSetting)) ;
            AIsaveDsplayPhotoUtiles.saveCustomerAction(currentAction);
        }

        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"javascript:setAction "+json);
        return currentAction.newFormSetting;

    }
    private static int getisSupportAIReport(CustomerAction currentAction){
        GetEmployeeRuleResult employeeRuleResult =  OutDoor2CacheManger.getCacheRule();
        int i = currentAction.newFormSetting.isSupportAIReport;
        if( OutDoor2CacheManger.getCacheRule()!=null){
            i = currentAction.newFormSetting.isSupportAIReport>employeeRuleResult.isSupportAIReport?
                    currentAction.newFormSetting.isSupportAIReport
                    :employeeRuleResult.isSupportAIReport;
        }
        return i;
    }


}
