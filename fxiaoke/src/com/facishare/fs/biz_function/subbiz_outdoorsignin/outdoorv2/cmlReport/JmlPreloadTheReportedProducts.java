package com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.cmlReport;

import com.alibaba.fastjson.TypeReference;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.api.WaiqinServiceV2;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CustomerAction;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.GetLocalProductInfoArgs;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.GetLocalProductInfoResult;
import com.facishare.fs.js.utils.OutdoorLog;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fxlog.FCLog;

import java.util.Date;

public class JmlPreloadTheReportedProducts {
    private static final String TAG = "JmlPreloadTheReportedPr";

//    public static void getLocalProductInfo(CustomerAction customerAction){
//        GetLocalProductInfoResult getLocalProductInfoResult = JmlDisplayUtils.batch2LocalProduct(customerAction);
//        if(getLocalProductInfoResult!=null
//                && getLocalProductInfoResult.productIds!=null
//                &&getLocalProductInfoResult.productIds.size()>0){
//            return;
//        }
//        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"预加载"   );
//        GetLocalProductInfoArgs getLocalProductInfoArgs = new GetLocalProductInfoArgs();
//        if(customerAction!= null){
//            if(customerAction.mainObjectInfo!=null){
//                getLocalProductInfoArgs.customerId =customerAction.mainObjectInfo.dataId;
//            }
//            getLocalProductInfoArgs.productRange = customerAction.productRange;
//            if(customerAction.newFormSetting!=null){
//                getLocalProductInfoArgs.productSortOrder = customerAction.newFormSetting.productSortOrder;
//                getLocalProductInfoArgs.relateMainObj = customerAction.newFormSetting.relateMainObj;
//                getLocalProductInfoArgs.relateRefObj = customerAction.newFormSetting.relateRefObj;
//                getLocalProductInfoArgs.reportMethod = customerAction.newFormSetting.getReportMethod();
//            }
//        }
//
//        WaiqinServiceV2.getLocalProductInfo(getLocalProductInfoArgs,new WebApiExecutionCallback<GetLocalProductInfoResult>() {
//            @Override
//            public TypeReference<WebApiResponse<GetLocalProductInfoResult>> getTypeReference() {
//                return null;
//            }
//            @Override
//            public Class<GetLocalProductInfoResult> getTypeReferenceFHE() {
//                return GetLocalProductInfoResult.class;
//            }
//
//            @Override
//            public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
//                super.failed(failureType, httpStatusCode, error);
//                FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"预加载失败"   );
//            }
//
//            @Override
//            public void completed(Date date, GetLocalProductInfoResult result) {
//                FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"预加载成功"   );
//                if(result != null&& result.productIds!=null && result.productIds.size()>0){
//                    JmlDisplayUtils.saveBatchGetLocalProductInfoResult(result,customerAction.mainObjectInfo.dataId,customerAction.newFormSetting.relateMainObj,customerAction.newFormSetting.getReportMethod());
//                }
//            }
//        });
//    }
}
