package com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.outdoor_offline.tasktype;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import com.facishare.fs.NoProguard;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CheckinsLog;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CustomerAction;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.AttachUploadState;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.IOutDoorUploadListener;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.IOutdoorListener;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.OutDoorAttachUploader;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.SaveFilesState;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OffLineOutDoorHandler;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.outdoor_offline.OfflineUtils;
import com.facishare.fs.common_datactrl.draft.OutDoorVO;
import com.facishare.fs.common_datactrl.draft.draft_fw.IAttach;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.js.utils.OutdoorLog;
import com.facishare.fs.pluginapi.Account;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.common_beans.Attach;
import com.facishare.fs.utils_fs.NetUtils;
import com.fs.beans.beans.EnumDef;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.fxsocketlib.envctrl.FcpConnectEnvCtrl;
import com.fxiaoke.fxsocketlib.envctrl.FcpConnectEnvListener;
import com.fxiaoke.stat_engine.events.StatEvent;
import com.fxiaoke.stat_engine.events.custevents.KwqEventUtils;
//import com.fxiaoke.stat_engine.events.session.UeEventSession;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;

import de.greenrobot.event.core.AsyncSubscriber;

/**
 * Created by zhujg on 2017/2/15.
 */
public class OutDoorV2ImageTaskHandler {

    public static final String TAG = "OutDoorV2ImageTaskHandler";
    IImageHandlerCB imageHandlerCB;
    private static class OutDoorAccount {
        final int enterpriseId;
        final int employeeIntId;

        OutDoorAccount(int enterpriseId, int employeeIntId) {
            this.enterpriseId = enterpriseId;
            this.employeeIntId = employeeIntId;
        }
    }

    public void setImageHandlerCB(IImageHandlerCB imageHandlerCB) {
        this.imageHandlerCB = imageHandlerCB;
    }

    public interface IImageHandlerCB{
        void imageUpFaild(String id,boolean bl);
    }


    public synchronized static OutDoorV2ImageTaskHandler getInstance() {

        Account account = FSContextManager.getCurUserContext().getAccount();
        int enterpriseId = account.getEnterpriseId();
        int employeeIntId = account.getEmployeeIntId();

        return new OutDoorV2ImageTaskHandler(enterpriseId, employeeIntId);

    }

    /**
     * 外勤签到存储在本地安装目录的路径
     * 以企业ID_员工ID_outdoor作为目录
     *
     * @return
     */
    public File getAccountOutdoorRecordDir() {
        File dir = new File(OutDoorVO.getAccountOutDoorDir(mAccount.enterpriseId, mAccount.employeeIntId) + "recordoffline");
        if (!dir.exists() || !dir.isDirectory()) {
            dir.mkdirs();
        }
        return dir;
    }

    private Handler mHandler = new Handler();
    private Thread mUiThread;
    private OutDoorAccount mAccount;
    private Queue<String> idQueue = new LinkedList<>();
    private List<String> failedIdQueue = new LinkedList<>();
    private Map<String, IAttach> VOMap = new HashMap<>();
    private Map<String, Object> stateMap = new HashMap<>();
    private IOutdoorListener outdoorListeners ;
//    private FSNetObserver mFsNetObserver;
    private boolean abandoned = false;
    private final int MSG_AUTO_RETRY = 0;
    private FcpConnectEnvListener mFcpConnectEnvListener = null;
//    private AsyncSubscriber mDeleteOutdoorSubscriber = new AsyncSubscriber<FeedDeleteEvent>() {
//        @NoProguard
//        @Override
//        public void onEventAsync(FeedDeleteEvent event) {
//            ArrayList<CheckinsLog> cache = getCheckinsLogCache();
//            boolean changed = false;
//            if (cache != null) {
//                for (int i = 0; i < cache.size(); i++) {
//                    CheckinsLog re = cache.get(i);
//                    if (re.getFeedId() == event.feedId) {
//                        cache.remove(i);
//                        changed = true;
//                        break;
//                    }
//                }
//                if (changed) {
//                    saveCheckinsLogCache(cache);
//                }
//            }
//        }
//    };

    private OutDoorV2ImageTaskHandler(int enterpriseId, int employeeIntId) {
        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,
                "OutDoorUploaderManager initor " + enterpriseId + "_" + employeeIntId);

        mUiThread = Thread.currentThread();
        mAccount = new OutDoorAccount(enterpriseId, employeeIntId);

    }


    public String getAttachCheckInId(String id){
        IAttach a = VOMap.get(id);
        if(a!=null && a instanceof CustomerAction){
            CustomerAction act = (CustomerAction) a;
            return act.checkinId;
        }
        return id;
    }

//    private void abandon() {
//        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, "abandon");
//        abandoned = true;
//        if(mFcpConnectEnvListener!=null){
//            FcpConnectEnvCtrl.getInstance().removeEnvListener(mFcpConnectEnvListener);
//        }
////        FSNetUtils.getInstance().delObserver(mFsNetObserver);
//        mDeleteOutdoorSubscriber.unregister();
//    }

    public void saveRecord(String id, IAttach vo) {
        File file = new File(getAccountOutdoorRecordDir(), id);
        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, "saveRecord file:" + id);
        ObjectOutputStream oos = null;
        try {
            oos = new ObjectOutputStream(new FileOutputStream(file, false));
            oos.writeObject(vo);
            oos.flush();
        } catch (IOException e) {
            FCLog.e(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, Log.getStackTraceString(e));
        } finally {
            if (oos != null) {
                try {
                    oos.close();
                } catch (IOException e) {
                    FCLog.e(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, Log.getStackTraceString(e));
                }
            }
        }
    }

    public void deleteRecord(String id) {
        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, "deleteRecord file:" + id);
        if (id!=null){
            File file = new File(getAccountOutdoorRecordDir(), id);
            file.delete();
        }
    }

    public void registerListener(IOutdoorListener uploadListener) {
        this.outdoorListeners = uploadListener;
    }


    public IAttach loadRecordById(String id){
        FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"loadRecordById id==> "+id);

        File file = new File(getAccountOutdoorRecordDir(), id);//存储目录
        if(file.exists()){
            ObjectInputStream ois = null;
            try {
                ois = new ObjectInputStream(new FileInputStream(file));
                IAttach outDoorVO = (IAttach) ois.readObject();
                return outDoorVO;
            }catch (Exception e){
                FCLog.e(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, Log.getStackTraceString(e));
            }finally {
                if (ois != null) {
                    try {
                        ois.close();
                    } catch (IOException e) {
                        FCLog.e(OutdoorLog.OUTDOOR_DEBUG_EVENT, TAG,Log.getStackTraceString(e));
                    }
                }
            }
        }
        return null;
    }

    private Map<String, IAttach> loadRecord() {
        File dir = getAccountOutdoorRecordDir();
        Map<String, IAttach> records = new HashMap<>();
        File[] files = dir.listFiles();
        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, "loadRecord dir:" + dir + ":files:" + Arrays.toString(files));
        if (files != null) {
            for (File file : files) {
                ObjectInputStream ois = null;
                try {
                    ois = new ObjectInputStream(new FileInputStream(file));
                    IAttach outDoorVO = (IAttach) ois.readObject();
                    String checkinId = file.getName();
                    records.put(checkinId, outDoorVO);
                } catch (IOException e) {
                    FCLog.e(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, Log.getStackTraceString(e));
                } catch (ClassNotFoundException e) {
                    FCLog.e(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, Log.getStackTraceString(e));
                } finally {
                    if (ois != null) {
                        try {
                            ois.close();
                        } catch (IOException e) {
                            FCLog.e(OutdoorLog.OUTDOOR_DEBUG_EVENT, TAG,Log.getStackTraceString(e));
                        }
                    }
                }
            }
        }
        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT, "loadRecord records:" + records);
        return records;
    }

    public void add(final String id, final IAttach vo) {
        FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"add id ==> "+id);

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (idQueue.contains(id)) {
                    IAttach attach = VOMap.get(id);
                    OutDoorAttachUploader uploader = attach.getUploader();
                    //已经开始上传
                    if(uploader != null){
                        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, "already in task queue:" + id + "---uploader:" + uploader);
                        addOnly(id,vo);
                        uploader.reStart(vo);
                    }else{//未开始上传,只是在队列里面
                        addOnly(id,vo);
                    }
                    return;
//                    throw new IllegalStateException(checkinId + " already in task queue. Can't be added!");
                }
                addOnly(id,vo);
                idQueue.offer(id);
                int size = idQueue.size();
                FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, "after add checkinId:" + id + "---queue:" + idQueue);
                if (size == 1) {
                    executeNext();
                }
            }
        });
    }

    public void delDataByid(String key){
        if (VOMap != null && VOMap.size() >0) {
            VOMap.remove(key);
        }
    }

    void addOnly(final String id, final IAttach vo){
        final int total = vo.getUpLoadFiles() == null ? 0 : vo.getUpLoadFiles().size();
//        if (total == 0) {
//            return;
//        }
        VOMap.put(id, vo);
        int uploadedAttachNum = 0;
        boolean isAllPhoto = true;
        for (Attach attach : vo.getUpLoadFiles()) {
            if (attach.attachLocalState == Attach.AttachType.ATTACH_NETWORK_TYPE) {
                uploadedAttachNum++;
            }
        }
        for (Attach attach : vo.getUpLoadFiles()) {
            if (attach.getFileType() != EnumDef.FeedAttachmentType.ImageFile.value) {
                isAllPhoto = false;
                break;
            }
        }
        AttachUploadState uploadState =
                new AttachUploadState(AttachUploadState.STATE_WAITING, uploadedAttachNum, total, isAllPhoto);
        notifyState(id, uploadState);
    }

    /**
     * 主线程执行
     */
    private void executeNext() {
//        if (Looper.myLooper() != mHandler.getLooper()) {
//            throw new IllegalStateException("Must run in mHandler thread!");
//        }
        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, "executeNext abandoned =" + abandoned);
        if (abandoned) {
            idQueue.clear();
            failedIdQueue.clear();
            VOMap.clear();
            stateMap.clear();
            outdoorListeners = null;
            return;
        }
        String id = idQueue.peek();
        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, "executeNext id:" + id);
        if (id == null) {
            return;
        }
        IAttach vo = VOMap.get(id);
        if (vo == null) {
            FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT, TAG,"executeNext vo is null. idQueue:" + idQueue + ":VOMap:" + VOMap);
            VOMap.remove(id);
            idQueue.poll();
            executeNext();
            return;
        }
        IOutDoorUploadListener mUploadListener = new IOutDoorUploadListener() {
            @Override
            public void onSuccess() {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        String id = idQueue.poll();
                        //成功后，队列中移除本次任务，继续下一个
//                        VOMap.get(checkinId).deleteOutDoorImage();
                        VOMap.remove(id);
                        deleteRecord(id);
                        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,
                                "OutdoorUploaderManager onSuccess----" + id + ":left queue:" + idQueue);
                        executeNext();
                    }
                });
            }

            @Override
            public void onFailed() {
                onFailed(false);
            }

            @Override
            public void onFailed(final boolean deleteRecord) {
                //失败后，队列中移除本次任务，继续下一个。失败的放到失败队列，等待下一次自动全部添加到执行队列，用户手动将某一个添加到执行队列
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        String id = idQueue.poll();
                        if (deleteRecord) {
//                            VOMap.get(checkinId).deleteOutDoorImage();
                            VOMap.remove(id);
                            stateMap.remove(id);
                            deleteRecord(id);
                        } else {
                            failedIdQueue.add(id);
                        }
                        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,
                                "OutdoorUploaderManager onFailed---" + id + ":deleteRecord:" + deleteRecord
                                        + ":left queue:" + idQueue);
                        imageHandlerCB.imageUpFaild(id,deleteRecord);
                        executeNext();
                    }
                });
            }

            @Override
            public void notifyState(String id, Object state) {
                OutDoorV2ImageTaskHandler.this.notifyState(id, state);
            }

            @Override
            public void saveRecord(String id, IAttach vo) {
                OutDoorV2ImageTaskHandler.this.saveRecord(id, vo);
            }

            @Override
            public void endAndRemoveSession(String id, String key, Object object) {
//                OutDoorV2ImageTaskHandler.this.endAndRemoveSession(checkinId, key, object);
            }
        };
        OutDoorAttachUploader uploader = new OutDoorAttachUploader(id, vo, mUploadListener, mHandler);
        uploader.launch();
    }

    public void notifyState(final String checkinId, final Object state) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                stateMap.put(checkinId, state);
                if (outdoorListeners !=null){
                    outdoorListeners.onStateNotified(checkinId, state);
                }

            }
        });
    }

    private Object queryState(String checkinId) {
        Object state = stateMap.get(checkinId);
        return state;
    }

    private Object getState(String checkinId){
        Object state = stateMap.get(checkinId);
        if(state!=null){
            return state;
        }
        Collection<IAttach> list =  VOMap.values();
        for (IAttach attach :list) {
            if (attach instanceof CustomerAction){
                CustomerAction action = (CustomerAction) attach;
                if(action!=null){
                    if(checkinId.equals(action.checkinId)){
                        state = stateMap.get(action.actionId);
                    }
                }
            }
            if(state!=null){
                break;
            }
        }
        return state;
    }

    private int getStateByPos(String checkinId,int pos){
        Object state = stateMap.get(checkinId);
        if(state!=null){
            return getAttachState(state);
        }
        Collection<IAttach> list =  VOMap.values();
        for (IAttach attach :list) {
            if (attach instanceof CustomerAction){
                CustomerAction action = (CustomerAction) attach;
                if(action!=null){
                    if(checkinId.equals(action.checkinId)&&action.actionIndex == pos){
                        state = stateMap.get(action.actionId);
                    }
                }
            }
            if(state!=null){
                break;
            }
        }
        return getAttachState(state);
    }

    private boolean getIsCheckinId(String checkinId){

        Collection<IAttach> list =  VOMap.values();
        if (list != null && list.size()>0) {
            for (IAttach attach :list) {
                if (attach instanceof CustomerAction){
                    CustomerAction action = (CustomerAction) attach;
                    if(action!=null&&checkinId.equals(action.checkinId)){
                        return true;
                    }
                }

            }
        }
        return false;
    }
    public void manualRetry(final String checkinId) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                IAttach vo = VOMap.get(checkinId);
                FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, "manualRetry " + checkinId + "----" + vo);
                failedIdQueue.remove(checkinId);
                if (vo!=null)
                    add(checkinId, vo);
            }
        });
    }

//    public void autoRetry(long delay) {
//        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT, TAG,"autoRetry delay:" + delay);
//        mHandler.sendEmptyMessageDelayed(MSG_AUTO_RETRY, delay);
//
//    }

    public final void runOnUiThread(Runnable action) {
        if (Thread.currentThread() != mUiThread) {
            mHandler.post(action);
        } else {
            action.run();
        }
    }

    public void saveCheckinsLogCache(ArrayList<CheckinsLog> checkinsLogs) {
        ObjectOutputStream oos = null;
        try {
            File file = new File(
                    FSContextManager.getCurUserContext().getSDOperator().getExternalDirForRecord() + File.separator
                            + "outdoor_checkin_cache");
            if (checkinsLogs == null || checkinsLogs.size() == 0) {
                file.delete();
            } else {
                oos = new ObjectOutputStream(new FileOutputStream(file, false));
                oos.writeObject(checkinsLogs);
                oos.flush();
            }
        } catch (Exception e) {
            FCLog.e(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, Log.getStackTraceString(e));
        } finally {
            if (oos != null) {
                try {
                    oos.close();
                } catch (IOException e) {
                    FCLog.e(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, Log.getStackTraceString(e));
                }
            }
        }
    }

    public ArrayList<CheckinsLog> getCheckinsLogCache() {
        ObjectInputStream ois = null;
        ArrayList<CheckinsLog> result = null;
        try {
            File file = new File(
                    FSContextManager.getCurUserContext().getSDOperator().getExternalDirForRecord() + File.separator
                            + "outdoor_checkin_cache");
            if (file.exists()) {
                ois = new ObjectInputStream(new FileInputStream(file));
                result = (ArrayList<CheckinsLog>) ois.readObject();
            }
        } catch (Exception e) {
            FCLog.e(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, Log.getStackTraceString(e));
        } finally {
            if (ois != null) {
                try {
                    ois.close();
                } catch (IOException e) {
                    FCLog.e(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG, Log.getStackTraceString(e));
                }
            }
        }
        return result;
    }

//    /**
//     * 进行无用外勤照片删除的处理
//     */
//    private void deleteWastePicture(Map<String, OutDoorVO> records) {
//        if (!OutDoorVO.isCanDeleteAllOutDoorImage()) {
//            return;
//        }
//        Collection<OutDoorVO> outDoorVOs = records.values();
//        List<File> attachList = new ArrayList<>();
//        for (OutDoorVO outDoorVO : outDoorVOs) {
//            if (outDoorVO.upLoadFiles != null) {
//                attachList.addAll(outDoorVO.toFile());
//            }
//        }
//
//        List<IDraft> drafts = DraftManager.getAllDraft();
//        for (IDraft draft : drafts) {
//            if (draft instanceof OutDoorVO) {
//                OutDoorVO outDoorVO = (OutDoorVO) draft;
//                if (outDoorVO.upLoadFiles != null) {
//                    attachList.addAll(outDoorVO.toFile());
//                }
//            }
//        }
//        File outdoorDir = new File(OutDoorVO.getAccountOutDoorDir());
//        File[] files = outdoorDir.listFiles();
//        if (files != null) {
//            for (File file : files) {
//                boolean isHave = false;
//                for (File attach : attachList) {
//                    if (file.equals(attach)) {
//                        isHave = true;
//                    }
//                }
//                if (!isHave) {
//                    file.delete();
//                    FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT, "delete image not used:" + file.getPath());
//                }
//            }
//        }
//    }

//    private Map<String, UeEventSession> sessionMap = new HashMap<>();
//
//    public void addSession(String checkinId, UeEventSession session) {
//        sessionMap.put(checkinId, session);
//    }
//
//    public void endAndRemoveSession(String checkinId, String key, Object object) {
//        UeEventSession session = sessionMap.get(checkinId);
//        if (session != null) {
//            session.addExData(key, object);
//            session.endTick();
//            sessionMap.remove(checkinId);
//        }
//    }


    /***
     *
//     * @param id
     * @return 1 成功 , -1 失败, 0 上传中 , -2 照片丢失 ,  -3 本地数据无状态
     */
    public int getAttachStateById(String id){
        Object state = queryState(id);
        return getAttachState(state);
    }
    public IAttach getIAttachById(String id){
        IAttach a = VOMap.get(id);
        if(a != null){
            return a;
        }
        return loadRecordById(id);
    }
    private int getAttachState(Object state){
        int result = -3;
        if (state != null) {
            if (state instanceof SaveFilesState) {
                SaveFilesState ss = (SaveFilesState) state;
                switch (ss.state){
                    case SaveFilesState.STATE_SUCCESS:
                        result = 1;
                        break;
                    case SaveFilesState.STATE_WAITING:
                    case SaveFilesState.STATE_SENDING:
                        result = 0;
                        break;
                    case SaveFilesState.STATE_FAIL:
                        result = -2;
                        break;
                }

            }
            if (state instanceof AttachUploadState) {
                AttachUploadState as = (AttachUploadState) state;
                switch (as.state){
                    case AttachUploadState.STATE_WAITING:
                    case AttachUploadState.STATE_CHECKING:
                    case AttachUploadState.STATE_UPLOADING:
                        result = 0;
                        break;
                    case AttachUploadState.STATE_FAIL:
                        result = -1;
                        break;
                    case AttachUploadState.STATE_FILE_NOT_EXIST:
                        result = -2;
                        break;
                }
            }
        }
        return result;
    }

//    public boolean checkIsComplete(String id){
//        int result = getAttachStateById(id);
//        if (result == 1||result ==-3) {
//            return true;
//        }
//
//        return false;
//    }
//
//    public boolean isImageUploadFailed(String id){
//        int result = getAttachStateById(id);
//        if(result==-1){
//            return true;
//        }
//        return false;
//    }

    public boolean isHasAttach(String attachId){
        return VOMap.containsKey(attachId);
    }
}
