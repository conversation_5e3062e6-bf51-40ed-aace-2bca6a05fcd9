package com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.NonNull;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CustomerDataforRoute;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.RouteDetailv2;
import com.facishare.fs.dialogs.CommonDialog;
import com.facishare.fslib.R;
import com.fxiaoke.fscommon.brandcolor.BrandColorDataProvider;

import java.util.List;

public class SelectCostomerDialog extends Dialog {
    CommonDialog.myDiaLogListener mListener;
    Context context;
    public SelectCostomerDialog(@NonNull Context context) {
        super(context, com.fxiaoke.fscommon_res.R.style.LoadingProDialog);
        this.context = context;
    }


    public void setDialogListener(RouteDetailv2 detail, CommonDialog.myDiaLogListener myDiaLogListener) {
        mListener = myDiaLogListener;
        routeDetail = detail;
    }
    LinearLayout llroot;
    TextView lx_name_tv,lx_name_sub_tv,tv_ok_ol;
    LinearLayout ll_costomer_bd;
    RouteDetailv2 routeDetail;
    ListView listview_c_bd;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.select_costomer_dialog_);
//        ll_costomer_bd = findViewById(R.id.ll_costomer_bd);
        listview_c_bd = findViewById(R.id.listview_c_bd);
        lx_name_tv = findViewById(R.id.lx_name_tv);
        tv_ok_ol = findViewById(R.id.tv_ok_ol);
        tv_ok_ol.setTextColor(Color.parseColor(BrandColorDataProvider.getInstance().getNormalColor(context)));
        lx_name_sub_tv = findViewById(R.id.lx_name_sub_tv);
        initData(routeDetail);
    }

    public void initData(RouteDetailv2 routeDetail){
        lx_name_tv.setText(routeDetail.routeName);
        if (routeDetail.customerList!=null){
            lx_name_sub_tv.setText(routeDetail.LastCreateDesc);
//            for (Customer4Route cu:routeDetail.customerList) {
//                View view = LayoutInflater.from(context).inflate(R.layout.select_costomer_dialog_item,null,false);
//                TextView tv_costomer_n = view.findViewById(R.id.tv_costomer_n);
//                TextView tv_costomer_id = view.findViewById(R.id.tv_costomer_id);
//
//                tv_costomer_n.setText(cu.customerName);
//                if (!TextUtils.isEmpty(cu.accountNum)){
//                    tv_costomer_id.setText(cu.accountNum);
//                }else {
//                    tv_costomer_id.setVisibility(View.GONE);
//                }
//
//                ll_costomer_bd.addView(view);
//
//            }
            listview_c_bd.setAdapter(new myCostomerAda(routeDetail.customerList));
            tv_ok_ol.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mListener.onClick(v);
                }
            });
        }



    }

    public class myCostomerAda extends BaseAdapter{
        public List<CustomerDataforRoute> datas;
        public myCostomerAda(List<CustomerDataforRoute> d){
            this.datas = d;
        }

        @Override
        public int getCount() {

            return datas.size();
        }

        @Override
        public Object getItem(int position) {
            return datas.get(position);
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {

            CustomerDataforRoute cu = datas.get(position);
            View view = LayoutInflater.from(context).inflate(R.layout.select_costomer_dialog_item,null,false);
            TextView tv_costomer_n = view.findViewById(R.id.tv_costomer_n);
            TextView tv_costomer_id = view.findViewById(R.id.tv_costomer_id);

            tv_costomer_n.setText(cu.customerName);
            tv_costomer_id.setText(cu.accountNum);
            return view;
        }
    }

}
