package com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.facerecognition;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.RequiresApi;

import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.CircleProgressBarView;
import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.common_utils.StringUtils;
import com.facishare.fs.dialogs.CommonDialog;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fslib.R;
import com.fxiaoke.cmviews.view.DynamicViewStub;
import com.fxiaoke.stat_engine.events.custevents.DialogPopEvent;

/**
 * Created by sunhq on 2020/2/13.
 */

public class FaceRecogintionDialog extends Dialog implements View.OnClickListener {

    private TextView content, gap, titleBlock, txtTip,lineHe;
    private Button cancel, enter;
    private CommonDialog.myDiaLogListener mListener;
    private String messageStr, warnMsgStr;
    private EditText editText;
    private CheckBox checkBox;
    private int type = 0;
    private int imgType = 0;
    private RelativeLayout image_layout;
    private ImageView img,addface_success,addface_faild,face_recognition_success,face_recognition_faild;
    private LinearLayout layout,circle_layout;
    private View titleLayout;
    private CircleProgressBarView circleProgressBarView;
    /**
     * 传入相应的显示类型
     */
    public static final int FLAG_EditView = 1;  //带有Edittext可编辑的对话框
    public static final int FLAG_SingleButton = 2;  //只有一个按钮的对话框
    public static final int FLAG_TwoButton = 3;     //常用的,也是默认的两个按钮的对话框
    public static final int FLAG_PasswordEditView = 4; // 带有密码输入样式的对话框
    public static final int FLAG_scrollView_And_SingleButton = 5; //带有可滚动的,一个按钮的对话框
    public static final int FLAG_Message_And_Tip = 6;
    public static final int FLAG_no_checkbox_no_prompt = 7;//有一个checkbox，两个按钮的对话框
    public static final int FLAG_Imgview_prompt = 8;//有一个图片信息的对话框
    public static final int FLAG_NoButton = 9;

    public static final int FLAG_addface_start = 1;  //第一次添加人脸数据提示对话框
    public static final int FLAG_addface_success = 2;  //添加人脸数据成功
    public static final int FLAG_addface_faild = 3;     //添加人脸数据失败
    public static final int FLAG_face_recognitioning = 4; // 正在进行人脸验证
    public static final int FLAG_face_recognition_success = 5; //人脸验证成功
    public static final int FLAG_face_recognition_faild = 6;//人脸验证失败
    public static final int FLAG_face_no_img = 7;//人脸验证失败
    private boolean isOneButton = false;
    private boolean isPositiveLeft = false;
    private Context con;
    public FaceRecogintionDialog(Context context) {
        super(context, com.fxiaoke.fscommon_res.R.style.LoadingProDialog);
    }

    public FaceRecogintionDialog(Context context, CommonDialog.myDiaLogListener myDiaLogListener) {

        super(context, com.fxiaoke.fscommon_res.R.style.LoadingProDialog);
        mListener = myDiaLogListener;
        // TODO Auto-generated constructor stub
    }

    public FaceRecogintionDialog(Context context, int t,int itype){
        super(context, com.fxiaoke.fscommon_res.R.style.LoadingProDialog);
        con = context;
        type = t;
        imgType = itype;
    }

    public FaceRecogintionDialog(Context context, CommonDialog.myDiaLogListener myDiaLogListener, int t) {

        super(context, com.fxiaoke.fscommon_res.R.style.LoadingProDialog);
        mListener = myDiaLogListener;
        type = t;
        // TODO Auto-generated constructor stub
    }

    public void setDialogListener(CommonDialog.myDiaLogListener myDiaLogListener) {
        mListener = myDiaLogListener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // TODO Auto-generated method stub
        super.onCreate(savedInstanceState);
        // setContentView(R.layout.mydialog);
        setContentView(R.layout.my_face_recogintion_dialog_);
        InitView();
        setMessage(messageStr);
//        setMessageEx(messageStr);
        setWarnMessage(warnMsgStr);
        updateButtonText();
        // 设置ContentView
        if (mCustomContentView != null) {
            layout.setVisibility(View.VISIBLE);
            layout.addView(mCustomContentView);
        }
        setBtnViewShow();
    }

    public interface myDiaLogListener {
        public void onClick(View view);
    }

    private void InitView() {
        checkBox = (CheckBox) findViewById(R.id.checkbox_no_prompt);
        editText = (EditText) findViewById(R.id.editText_mydialog);
        cancel = (Button) findViewById(R.id.button_mydialog_cancel);
        enter = (Button) findViewById(R.id.button_mydialog_enter);
        lineHe = findViewById(R.id.textView_mydialog_gap_orgion);
        gap = (TextView) findViewById(R.id.textView_mydialog_gap);
        layout = (LinearLayout) findViewById(R.id.LinearLayout_mydialog);
        image_layout = findViewById(R.id.image_layout);
        img = findViewById(R.id.img);
        addface_success = findViewById(R.id.addface_success);
        addface_faild = findViewById(R.id.addface_faild);
        face_recognition_success = findViewById(R.id.face_recognition_success);
        face_recognition_faild = findViewById(R.id.face_recognition_faild);
        circle_layout = findViewById(R.id.circle_layout);
        titleLayout = findViewById(R.id.titleLayout);
        titleBlock = (TextView) findViewById(R.id.titleBlock);
        txtTip = (TextView) findViewById(R.id.txtTip);
        enter.setOnClickListener(this);
        cancel.setOnClickListener(this);
        setPositiveButtonLeft();
        setDiaLogType();
        setImgType();
        setOneBtn();
    }

    /**
     * Positive按钮默认在左侧，需要交换按钮位置（引用和点击事件）
     */
    private void setPositiveButtonLeft() {
        if (isPositiveLeft) {
            Button temp = enter;
            enter = cancel;
            cancel = temp;

            enter.setOnClickListener(view -> onClick(cancel));
            cancel.setOnClickListener(view -> onClick(enter));
        }
    }

    boolean isProceesCancelClickWhenBackBtn = true;

    public void setProceesCancelClickWhenBackBtn(boolean isProceesCancelClickWhenBackBtn) {
        this.isProceesCancelClickWhenBackBtn = isProceesCancelClickWhenBackBtn;
    }

    boolean isCancelable = false;

    public void setBackCancelable(boolean isCancelable) {
        //setCancelable(isCancelable);
        this.isCancelable = isCancelable;
    }

    @Override
    public void show() {
        if (con != null&&!((Activity)con).isFinishing()) {
            super.show();
            //重写show方法，增加弹窗打点
            DialogPopEvent.getInstance().tick(messageStr);
        }

    }

    @Override
    public void onBackPressed() {
        // TODO Auto-generated method stub
        if (mListener != null && isProceesCancelClickWhenBackBtn) {

            mListener.onClick(cancel);
        } else {

            if (isCancelable)
                cancel();
        }
    }

    private void setDiaLogType() {
        if (!TextUtils.isEmpty(mTitleString)) {
            titleLayout.setVisibility(View.VISIBLE);
            titleBlock.setText(mTitleString);
        }
        switch (type) {
            case FLAG_EditView:
                editText.setVisibility(View.VISIBLE);// 含有editview的dialog
                editText.setHint("");
                editText.setFocusable(true);
                editText.requestFocus();
                changeInputState();
                break;
            case FLAG_SingleButton:
                gap.setVisibility(View.GONE);
                editText.setVisibility(View.GONE);
                cancel.setVisibility(View.GONE);// 只有一个确定的dialog
                break;
            case FLAG_TwoButton:
                layout.setVisibility(View.GONE);// 标准的默认的的两个按钮的  确定和取消
                break;
            case FLAG_NoButton://没有button
                gap.setVisibility(View.GONE);
                editText.setVisibility(View.GONE);
                cancel.setVisibility(View.GONE);
                enter.setVisibility(View.GONE);
                lineHe.setVisibility(View.GONE);
                break;
            case FLAG_PasswordEditView:
                editText.setVisibility(View.VISIBLE);// 含有editview的输入密码 dialog
                editText.setInputType(0x81);

                editText.setFocusable(true);
                editText.requestFocus();
                changeInputState();
                break;
            case FLAG_scrollView_And_SingleButton:
                gap.setVisibility(View.GONE);
                layout.setVisibility(View.GONE);//
                cancel.setVisibility(View.GONE);// 只有一个确定的dialog
                break;
            case FLAG_Message_And_Tip:
                txtTip.setVisibility(View.VISIBLE);
                editText.setVisibility(View.GONE);
                break;
            case FLAG_no_checkbox_no_prompt:
                checkBox.setVisibility(View.VISIBLE);
                break;
            case FLAG_Imgview_prompt:
                checkBox.setVisibility(View.VISIBLE);
                break;
            default:      //当都没有符合的时候默认使用正常的两个按钮的
                layout.setVisibility(View.GONE);//标准的默认的的两个按钮的  确定和取消
                break;
        }
    }

    private void setImgType() {

        switch (imgType) {
            case FLAG_addface_start:
                img.setVisibility(View.VISIBLE);
                break;
            case FLAG_addface_success:
                addface_success.setVisibility(View.VISIBLE);
                break;
            case FLAG_addface_faild:
                addface_faild.setVisibility(View.VISIBLE);
                break;
            case FLAG_face_recognitioning:
                circle_layout.setVisibility(View.VISIBLE);
                ScanAnimationView ani = new ScanAnimationView(getContext());
                circle_layout.addView(ani.getContentView());
                ani.startAnimation();
                break;
            case FLAG_face_recognition_success:
                face_recognition_success.setVisibility(View.VISIBLE);
                break;
            case FLAG_face_recognition_faild:
                face_recognition_faild.setVisibility(View.VISIBLE);
                break;
            case FLAG_face_no_img:
                break;
            default:      //当都没有符合的时候默认使用正常的两个按钮的

                break;
        }
    }

    public  boolean checkboxIsSelect(){
        return checkBox.isChecked();
    }
    /**
     * 更改输入法的状态，以保证初始即让pEditText上弹出软键盘
     */
    private void changeInputState() {
        // 更改状态为SOFT_INPUT_STATE_ALWAYS_VISIBLE 当窗口获得焦点时，显示输入法区域
        this.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
    }

    /**
     * 重置回弹软键盘时设置的状态，以避免干扰其他界面操作
     */
    public void resetInputState() {
        // 回复到初始状态，edittext默认不显示软键盘，只有edittext被点击时，软键盘才弹出
        this.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE | WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
    }
    // 获取EditText
    public EditText getEditTextView() {
        editText = (EditText) findViewById(com.fxiaoke.fscommon_res.R.id.editText_mydialog);
        return editText;
    }
    //设置edittext的hint显示文字   默认是:请输入密码
    public void setEditTextHint(String hint) {
        if (editText == null) {
            editText = (EditText) findViewById(com.fxiaoke.fscommon_res.R.id.editText_mydialog);
        }
        editText.setHint(hint);
    }
    // 设置显示界面的类型
    public void setShowType(int t) {
        this.type = t;
    }

    public void setMessage(String str) {
        messageStr = str;
        content = (TextView) findViewById(R.id.textView_mydialog_content);
        if (content != null) {
            if(StringUtils.isNullString(messageStr))
            {
                content.setVisibility(View.GONE);
            }else{
                content.setVisibility(View.VISIBLE);
            }
            content.setText(str);
        }

    }
    public String getMessageStr(){
        return  messageStr;
    }
    public void setWarnMessage(String wranMsg) {
        warnMsgStr = wranMsg;
        txtTip = (TextView) findViewById(com.fxiaoke.fscommon_res.R.id.txtTip);
        if (txtTip != null && wranMsg != null) {
            txtTip.setVisibility(View.VISIBLE);
            txtTip.setText(wranMsg);
        }
    }

//    public View inFlateViewStub(View view){
//        if(mViewStub==null){
//            return null;
//        }
//        mViewStub.setInflatedView(view);
//        return mViewStub.inflate();
//    }
//
//    public View inFlateViewStub(int viewID){
//        return inFlateViewStub(View.inflate(getContext(),viewID,null));
//    }
//
//    public void setMessageEx(String str) {
//        messageStr = str;
//        if(mViewStub==null){
//            return;
//        }
//        mViewStub.setInflatedView(View.inflate(getContext(), com.fxiaoke.fscommon_res.R.layout.my_dialog_content,null));
//        View stubView=mViewStub.inflate();
//        content = (TextView) stubView.findViewById(com.fxiaoke.fscommon_res.R.id.textView_mydialog_content1);
//        if (content != null) {
//            if(StringUtils.isNullString(messageStr))
//            {
//                content.setVisibility(View.GONE);
//            }else{
//                content.setVisibility(View.VISIBLE);
//            }
//            content.setText(str);
//
//            int maxWidth =  FSScreen.dip2px((280-20));
//            content.setMaxWidth(maxWidth);
//            content.measure(View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED), View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
//            int maxHeight = FSScreen.getScreenHeight() - FSScreen.dip2px(50+70+50+50);
//            int contentHeight = content.getMeasuredHeight();
//            LinearLayout.LayoutParams lParams = (LinearLayout.LayoutParams)scrollView.getLayoutParams();
//            if(contentHeight > maxHeight){
//                lParams.height = maxHeight;
//                scrollView.setLayoutParams(lParams);
//            }
//        }
//
//    }

    public void setPositiveButtonColor(int color) {
        mEnterBtnColor = color;
    }

    public void setNegativeButtonColor(int color) {
        mCancelBtnColor = color;
    }

    /**
     * 设置确定取消的按钮
     * @param str
     */
    public void setPositiveButton(String str) {
        mEnterBtnName = str;
    }

    public void setNegativeButton(String str) {
        mCancleBtnName = str;
    }

    /**
     * 设置Neutral Button text 并显示
     * */
    public void setNeutralButton(String str) {
        mNeutralBtnName = str;
    }

    /**
     * 设置自定义 Custom View
     * */
    public void setCustomContentView(View view) {
        mCustomContentView = view;
    }

    public void updateButtonText() {
        if (!TextUtils.isEmpty(mCancleBtnName)) {
            cancel.setText(mCancleBtnName);
            if (mCancelBtnColor != 0) {
                cancel.setTextColor(mCancelBtnColor);
            }
        }

        if (!TextUtils.isEmpty(mEnterBtnName)) {
            enter.setText(mEnterBtnName);
            if (mEnterBtnColor != 0) {
                enter.setTextColor(mEnterBtnColor);
            }
        }

//        if (mNeutralBtnName.length() > 0) {
//            neutral.setVisibility(View.VISIBLE);
//            neutral.setText(mNeutralBtnName);
//        }
    }

    /**
     * 根据字数设置button的宽度和padding值
     * @param btn
     * @param str
     */
    private void setBtn_paddingAndWeigh(Button btn ,String str)
    {
		/*if (str.length() > 2 ){
			btn.setPadding(btn_padding,btn_padding,btn_padding,btn_padding);
			// 设置包裹内容或者填充父窗体大小
			LinearLayout.LayoutParams lp2 = new LinearLayout.LayoutParams(
					LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
		//	btn.setLayoutParams(linearParams);
		}else if (str.length() == 2 ){
		//	btn.setWidth(FSScreen.dp2px(64));
			LinearLayout.LayoutParams linearParams =(LinearLayout.LayoutParams) btn.getLayoutParams(); //取控件textView当前的布局参数

			LinearLayout.LayoutParams lp2 = new LinearLayout.LayoutParams(
					LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
			lp2.width = FSScreen.dp2px(64);

		//	linearParams.width =FSScreen.dp2px(64);// 控件的宽强制设成30
			btn.setLayoutParams(lp2);
		}*/



    }
    private void setOneBtn() {
        if (isOneButton) {
            gap.setVisibility(View.GONE);
            editText.setVisibility(View.GONE);
            cancel.setVisibility(View.GONE);
        }
    }

    /**
     * 只有一个按钮的   例如:知道了
     */
    public void setUpdateOneButton() // 只有一个按钮
    {
        if (gap != null) {

            gap.setVisibility(View.GONE);
        }
        if (editText != null) {

            editText.setVisibility(View.GONE);
        }
        if (cancel != null) {

            cancel.setVisibility(View.GONE);
        }
        isOneButton = true;
    }

    public void setPositiveLeft(boolean isPositiveLeft) {
        this.isPositiveLeft = isPositiveLeft;
    }

    @Override
    public void onClick(View v) {
        // TODO Auto-generated method stub
        if (mListener != null) {
            // dismiss();
            mListener.onClick(v);
        } else {
            // dismiss();
            cancel();
        }
    }

    /**
     * 创建只有一个button的dialog
     *
     * @param context
     * @param message
     * @param myDiaLogListener
     * @param mOnDismissListener
     * @return
     */
    public static Dialog createDialog(Context context, String message, final CommonDialog.myDiaLogListener myDiaLogListener, OnDismissListener mOnDismissListener) {
        final CommonDialog myDialog = new CommonDialog(context, myDiaLogListener, FLAG_SingleButton);
        myDialog.setDialogListener(myDiaLogListener);
        myDialog.setTitle(I18NHelper.getText("xt.dledfilefragment.text.note")/* 注意 */);
        myDialog.setMessage(message);
        myDialog.setCanceledOnTouchOutside(true);
        myDialog.show();
        myDialog.setOnDismissListener(mOnDismissListener);
        return myDialog;
    }

    public static CommonDialog createOneButtonDialog(Context context, String title, String message, String okBtnName) {
        CommonDialog myDialog = new CommonDialog(context, FLAG_SingleButton);
        if (!TextUtils.isEmpty(title)) {
            myDialog.setTitle(title);
        }
        myDialog.setMessage(message);
        myDialog.setTitle(title);
        myDialog.setOkButtonTitle(okBtnName);
        myDialog.setCanceledOnTouchOutside(true);
        return myDialog;
    }
    public static CommonDialog createOneButtonDialog(Context context, String title, String message, String okBtnName, boolean isCanceledOnTouchOutside) {
        CommonDialog myDialog = new CommonDialog(context, FLAG_SingleButton);
        if (!TextUtils.isEmpty(title)) {
            myDialog.setTitle(title);
        }
        myDialog.setMessage(message);
        myDialog.setTitle(title);
        myDialog.setOkButtonTitle(okBtnName);
        myDialog.setCanceledOnTouchOutside(isCanceledOnTouchOutside);
        return myDialog;
    }

    /**
     * 创建有两个button的dialog
     *
     * @param context
     * @param message
     * @return
     */
    public static CommonDialog createTwoButtonDialog(Context context, String message) {
        CommonDialog myDialog = new CommonDialog(context);
        myDialog.setMessage(message);
        myDialog.setCanceledOnTouchOutside(true);
        return myDialog;
    }

    /**
     * 设置两个button的监听（button_mydialog_enter和button_mydialog_cancel）
     *
     * @param myDiaLogListener
     */
    public void initTwoButtonDialogListenerTShow(CommonDialog.myDiaLogListener myDiaLogListener) {
        setDialogListener(myDiaLogListener);
        show();
    }

    private String mCancleBtnName = "";
    private String mEnterBtnName = "";
    private String mNeutralBtnName = "";
    private int mCancelBtnColor;
    private int mEnterBtnColor;
    private int mNeutralBtnColor;

    String mTitleString = "";
    View mCustomContentView = null;
    int    btn_padding = FSScreen.dp2px(8);

    /**
     * 设置两个button的监听（button_mydialog_enter和button_mydialog_cancel），并设置按钮名称
     *
     * @param myDiaLogListener
     */
    public void initTwoButtonDialogListenerTShow(String cancleBtnName, String enterBtnName, CommonDialog.myDiaLogListener myDiaLogListener) {
        btn_padding = FSScreen.dp2px(8);
        if (cancel != null && !TextUtils.isEmpty(cancleBtnName)) {

            cancel.setText(cancleBtnName);
        }
        if (enter != null && !TextUtils.isEmpty(enterBtnName)) {
            enter.setText(enterBtnName);
        }
        mCancleBtnName = cancleBtnName;
        mEnterBtnName = enterBtnName;
        setDialogListener(myDiaLogListener);
        show();
    }

    public void setCancelButtonTitle(String cancelTitle) {
        if (cancel != null && !TextUtils.isEmpty(cancelTitle)) {
            cancel.setText(cancelTitle);
        }

        mCancleBtnName = cancelTitle;
    }
    /**
     * 设置确定取消的按钮
     * @param okTitle
     */
    public void setOkButtonTitle(String okTitle) {

        if (enter != null && !TextUtils.isEmpty(okTitle)) {
            enter.setText(okTitle);
        }
        mEnterBtnName = okTitle;
    }

    public void setTitleGravity(int gravity){
        if(titleBlock!=null){
            titleBlock.setGravity(gravity);
        }
    }

    public void setTitleColor(int titleColor){
        if(titleBlock!=null){
            titleBlock.setTextColor(titleColor);
        }
    }


    public void setTitle(String title) {
        if (!TextUtils.isEmpty(title) && titleBlock != null && titleLayout != null) {
            titleLayout.setVisibility(View.VISIBLE);
            titleBlock.setText(title);
        }else{
            setNoTitle();
        }
        mTitleString = title == null ? "" : title.toString();
    }
    public  void setNoTitle() {
        if ( titleLayout != null) {
            titleLayout.setVisibility(View.GONE);
        }
    }

    /**
     * @param message
     *            弹窗显示信息
     * @param title
     *            弹窗标题
     * @param okButtonTitle
     *            右侧取消按钮显示文字 默认确定
     * @param cancelButtonTitle
     *            左侧取消按钮显示文字 默认取消
     * @param isCancelable
     *            是否响应返回操作
     * @param isProcessCancleClickWhenBack
     *            在点击了返回键后，是否执行cancel按钮的点击事件
     * @param setCanceledOnTouchOutside
     *            是否允许点击弹框窗体外侧让对话框消失
     * @param showType
     *            显示中间文本的类型
     * @param confirmListener
     *            确认按钮监听事件
     * @param cancelListener
     *            取消按钮监听事件
     */
    public static CommonDialog showDialog(final Context context,
                                          String message,
                                          String title,
                                          String okButtonTitle,
                                          String cancelButtonTitle,
                                          boolean isCancelable,
                                          boolean isProcessCancleClickWhenBack,
                                          boolean setCanceledOnTouchOutside,
                                          int showType,
                                          final android.view.View.OnClickListener confirmListener,
                                          final android.view.View.OnClickListener cancelListener) {

        return showDialog(context,message,title,okButtonTitle,cancelButtonTitle,isCancelable,isProcessCancleClickWhenBack,setCanceledOnTouchOutside,showType,true,confirmListener,cancelListener);
    }

    public static CommonDialog showDialog(final Context context,
                                          String message,
                                          String title,
                                          String okButtonTitle,
                                          String cancelButtonTitle,
                                          boolean isCancelable,
                                          boolean isProcessCancleClickWhenBack,
                                          boolean setCanceledOnTouchOutside,
                                          int showType,
                                          boolean dismiss,
                                          final android.view.View.OnClickListener confirmListener,
                                          final android.view.View.OnClickListener cancelListener) {
        if (context instanceof Activity) {
            Activity act = (Activity) context;
            if (act.isFinishing()) {
                return null;
            }
        }
        final CommonDialog confirmDialog = new CommonDialog(context, null, showType);
        confirmDialog.setDialogListener(new CommonDialog.myDiaLogListener() {

            @Override
            public void onClick(View view) {
                if (view.getId() == com.fxiaoke.fscommon_res.R.id.button_mydialog_cancel) {
                    if(dismiss)
                        confirmDialog.dismiss();
                    if (cancelListener != null) {
                        cancelListener.onClick(view);
                    }

                } else if (view.getId() == com.fxiaoke.fscommon_res.R.id.button_mydialog_enter) {
                    if(dismiss)
                        confirmDialog.dismiss();
                    if (confirmListener != null)
                        confirmListener.onClick(view);
                }

            }
        });

        if (!TextUtils.isEmpty(title))
            confirmDialog.setTitle(title);
        if (!TextUtils.isEmpty(okButtonTitle))
            confirmDialog.setOkButtonTitle(okButtonTitle);
        if (!TextUtils.isEmpty(cancelButtonTitle))
            confirmDialog.setCancelButtonTitle(cancelButtonTitle);

        confirmDialog.setProceesCancelClickWhenBackBtn(isProcessCancleClickWhenBack);
        confirmDialog.setBackCancelable(isCancelable);
        confirmDialog.setCanceledOnTouchOutside(setCanceledOnTouchOutside);
        confirmDialog.setMessage(message);
        confirmDialog.show();

        return confirmDialog;
    }
    private CommonDialog.ShowBtnConfig btnConfig;
    public void setBtnConfig(CommonDialog.ShowBtnConfig config)
    {
        btnConfig = config;
    }

    void setBtnViewShow()
    {
        if (btnConfig != null){
//            if (btnConfig.isLeft){
//                neutral.setVisibility(View.GONE);
//            }
            if (btnConfig.center){
                cancel.setVisibility(View.GONE);
            }
            if (btnConfig.right){
                enter.setVisibility(View.GONE);
            }

//            if (!TextUtils.isEmpty(btnConfig.colorLeft)){
//                neutral.setTextColor(Color.parseColor(btnConfig.colorLeft));
//            }
//            if (!TextUtils.isEmpty(btnConfig.colorCenter)){
//                neutral.setTextColor(Color.parseColor(btnConfig.colorCenter));
//            }
//            if (!TextUtils.isEmpty(btnConfig.colorRight)){
//                neutral.setTextColor(Color.parseColor(btnConfig.colorRight));
//            }
        }
    }

    public static class ShowBtnConfig{
        public boolean isLeft;//true 左边按钮隐藏
        public String colorLeft;
        public boolean center;//true 中间隐藏
        public String colorCenter;
        public boolean right;//true 右边隐藏
        public String colorRight;
    }

}