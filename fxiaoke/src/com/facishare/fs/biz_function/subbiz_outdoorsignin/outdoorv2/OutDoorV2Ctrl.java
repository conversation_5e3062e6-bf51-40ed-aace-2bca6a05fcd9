package com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;

import com.facishare.fs.biz_function.KwqLocaionHandler;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CheckType;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.ICheckCustomer;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.ICloseDestroy;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.IGetObjArgs;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.IListToAction;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.IOutDoorFinished;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.IOutDoorV2Location;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.IOutDoorV2Restore;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.IOutDoorV2View;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.IOutDoorV2ViewOnActivityResult;
import com.facishare.fs.pluginapi.outdoor.GetPlanInfoArgs;
import com.facishare.fslib.R;
import com.fxiaoke.location.api.FsLocationResult;

import java.util.LinkedHashMap;
import java.util.Map;


/**
 * Created by wangyp on 2018/1/4.
 */

public class OutDoorV2Ctrl {

    Context context;
    OutDoorV2Presenter outDoorV2Presenter;
    LinkedHashMap<String ,IOutDoorV2View> viewMaps;
    KwqLocaionHandler mKwqLocaionHandler ;
    public OutDoorV2Ctrl(Context con){
        context = con;
        viewMaps = new LinkedHashMap<>();
        outDoorV2Presenter = new OutDoorV2Presenter(con);
        if(con instanceof OutDoorV2Presenter.IOutdoorCallBack){
            outDoorV2Presenter.setLS((OutDoorV2Presenter.IOutdoorCallBack) con);
        }
        mKwqLocaionHandler = new KwqLocaionHandler((Activity) con);
    }

    public KwqLocaionHandler getKwqLocaionHandler(){
        return mKwqLocaionHandler;
    }
    public OutDoorV2Presenter getOutDoorV2Presenter(){
        return outDoorV2Presenter;
    }
    public Map<String ,IOutDoorV2View> getViewMaps(){
        return viewMaps;
    }
    public void addView(IOutDoorV2View view)
    {
        if (view == null) return;
        viewMaps.put(view.getClass().getSimpleName(),view);
    }
    public IOutDoorV2View getMapView(String key){
        if (viewMaps != null && viewMaps.size() > 0) {
            return viewMaps.get(key);
        }
        return null;
    }
    public void removeView(String key){
        if (viewMaps != null && viewMaps.size() > 0) {
            viewMaps.remove(key);
        }
    }
    public void destroy(){
        if (viewMaps != null && viewMaps.size() > 0) {
            for (String key:viewMaps.keySet()) {
                Object object = viewMaps.get(key);
                if (object instanceof ICloseDestroy) {
                    ICloseDestroy iCloseDestroy = (ICloseDestroy) object;
                    iCloseDestroy.destroy();
                }
            }

            viewMaps.clear();

        }
    }
    public void onActivityResultData(int rCode,int resultCode,Intent data){
        if (viewMaps != null && viewMaps.size() > 0) {
            for (String key:viewMaps.keySet()) {
                Object object = viewMaps.get(key);
                if (object instanceof IOutDoorV2ViewOnActivityResult) {
                    IOutDoorV2ViewOnActivityResult result = (IOutDoorV2ViewOnActivityResult)object;
                    result.onActivityResultData(rCode,resultCode,data);
                }
            }
        }
    }
    public void setLocation(FsLocationResult address){
        if (viewMaps != null && viewMaps.size() > 0) {
            for (String key:viewMaps.keySet()) {
                Object object = viewMaps.get(key);
                if (object instanceof IOutDoorV2Location) {
                    IOutDoorV2Location iOutDoorV2Location = (IOutDoorV2Location) object;
                    iOutDoorV2Location.setLocation(address);
                }
            }
        }
    }
    public boolean isLocation(){
        if (viewMaps != null && viewMaps.size() > 0) {
            for (String key:viewMaps.keySet()) {
                Object object = viewMaps.get(key);
                if (object instanceof IOutDoorFinished) {
                    IOutDoorFinished iOutDoorFinished = (IOutDoorFinished) object;
                    if (!iOutDoorFinished.isLoctionFinished()) {
                        return false;
                    }
                }
            }
        }
        return true;
    }
    public void onRestore(Object o){
        if (viewMaps != null && viewMaps.size() > 0) {
            for (String key:viewMaps.keySet()) {
                Object object = viewMaps.get(key);
                if (object instanceof IOutDoorV2Restore) {
                    IOutDoorV2Restore iOutDoorV2Restore = (IOutDoorV2Restore) object;
                    iOutDoorV2Restore.onRestore(o);
                }
            }
        }
    }
    private void setListToAction(String type,String subtype){
        if (viewMaps != null && viewMaps.size() > 0) {
            for (String key:viewMaps.keySet()) {
                Object object = viewMaps.get(key);
                if (object instanceof IListToAction) {
                    IListToAction listToAction = (IListToAction) object;
                    listToAction.gotoAction(type,subtype);
                }
            }
        }
    }
    public void gotoAction(GetPlanInfoArgs getPlanInfoArgs,int type){
        if (getPlanInfoArgs != null &&!TextUtils.isEmpty(getPlanInfoArgs.actionType)) {
            switch (type){
                case 0:
                    setListToAction(getPlanInfoArgs.actionType,getPlanInfoArgs.subType);
                    getPlanInfoArgs.actionType = null;
                    break;
                case 1:
                    if (!OutDoorV2Constants.SIGNIN_KEY.equals(getPlanInfoArgs.actionType)
                            &&!OutDoorV2Constants.SIGNOUT_KEY.equals(getPlanInfoArgs.actionType)) {
                        setListToAction(getPlanInfoArgs.actionType,getPlanInfoArgs.subType);
                        getPlanInfoArgs.actionType = null;
                    }
                    break;
                case 2:
                    if (OutDoorV2Constants.SIGNIN_KEY.equals(getPlanInfoArgs.actionType)
                            ||OutDoorV2Constants.SIGNOUT_KEY.equals(getPlanInfoArgs.actionType)) {
                        setListToAction(getPlanInfoArgs.actionType,getPlanInfoArgs.subType);
                        if (OutDoorV2Constants.SIGNOUT_KEY.equals(getPlanInfoArgs.actionType)) {
                            OutDoorV2Utils.gotoHead(((Activity)context).findViewById(R.id.ScrollView_outdoorV2_root), View.FOCUS_DOWN);
                        }
                        getPlanInfoArgs.actionType = null;
                    }
                    break;
            }
//            finish();
        }
    }
    public void refCheckTypeData(CheckType ct){
        if (viewMaps != null && viewMaps.size()> 0) {
            for (String key:viewMaps.keySet()) {
                IOutDoorV2View iv = viewMaps.get(key);
                if (key.equals("OutDoorV2FieldDetailsHeadView")
//                        ||key.equals("OutDoorV2ChoosesCustomerView")
                        ||key.equals("OutDoorV2InterviewFeedbacklView")
                        ||key.equals("OutDoorV2AssociateslView")
                        ||key.equals("OutDoorV2MetaDataView2")
                        ||key.equals("OutDoorV2ActionListView")
                        ||key.equals("OutDoorV2SignInView")
                        ||key.equals("OutDoorV2SignOutView")
                        ||key.equals("OutDoorV2HFlowComponentView")
                        ||key.equals("OutDoorV2OkView")) {
                    iv.setData(ct);
                }
            }
        }
    }
    public IGetObjArgs getObjArgs(){
        if (viewMaps != null && viewMaps.size() > 0) {
            for (String key:viewMaps.keySet()) {
                Object object = viewMaps.get(key);
                if (object instanceof IGetObjArgs) {
                    return (IGetObjArgs) object;
                }
            }
        }
        return null;
    }
    public ICheckCustomer getCheckCustomerObj(){
        if (viewMaps != null && viewMaps.size() > 0) {
            for (String key:viewMaps.keySet()) {
                Object object = viewMaps.get(key);
                if (object instanceof ICheckCustomer) {
                    return (ICheckCustomer) object;
                }
            }
        }
        return null;
    }

}
