package com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.alibaba.fastjson.JSON;
import com.facishare.fs.biz_feed.utils.FeedSP;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.GetCalendarTemplateResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.GetEmployeeRuleResult;

import java.util.ArrayList;
import java.util.List;

public class GetCalendarTemplateHandler {
    Context context;
    static GetCalendarTemplateResult getCalendarTemplateResult;
    private String titlename;
    public GetCalendarTemplateHandler(Context con){
        context = con;
        getCalendarTemplateResult=null;
        getIntentData();
    }

    private void getIntentData(){
        FeedSP.saveStringType("GetCalendarTemplateResult","");
        Intent it = ((Activity)context).getIntent();
        titlename = it.getStringExtra("listTitle");
        GetCalendarTemplateResult d = (GetCalendarTemplateResult) it.getSerializableExtra("GetCalendarTemplateResult");
        if (d !=null&&d.hideModules!=null&&d.hideModules.size()>0){
            String json = JSON.toJSONString(d);
            if (!TextUtils.isEmpty(json)){
                FeedSP.saveStringType("GetCalendarTemplateResult",json);
            }
        }
    }

    public static List<String> getData(){
        if (getCalendarTemplateResult!=null&&getCalendarTemplateResult.hideModules!=null){
            return getCalendarTemplateResult.hideModules;
        }
        String json = FeedSP.getStringType("GetCalendarTemplateResult");
        if (!TextUtils.isEmpty(json)){
            getCalendarTemplateResult = JSON.parseObject(json,GetCalendarTemplateResult.class);
            if (getCalendarTemplateResult!=null&&getCalendarTemplateResult.hideModules!=null){
                return getCalendarTemplateResult.hideModules;
            }
        }

        return new ArrayList<String>();
    }

    public void setView(TextView txtCenter,View addView, View more,View searchbar){
        if (!TextUtils.isEmpty(titlename)){
            txtCenter.setText(titlename);
        }
        if (more!=null&&getData().contains("createButton")){
            addView.setVisibility(View.GONE);
        }

        if (more!=null&&getData().contains("moreButton")){
            more.setVisibility(View.GONE);
        }
        if (searchbar!=null&&getData().contains("nearRecommended")){
            searchbar.setVisibility(View.GONE);
        }

    }
}
