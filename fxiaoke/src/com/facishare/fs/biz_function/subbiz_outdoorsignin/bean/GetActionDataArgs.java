package com.facishare.fs.biz_function.subbiz_outdoorsignin.bean;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class GetActionDataArgs {

    @JSONField(name = "M10")
    public String actionId;// 唯一的动作 id

    @JSONField(name = "M11")
    public String dataId;//  自定义对象的数据ID

    @JSONField(name = "M12")
    public String apiName;//  apiname

    @JSONField(name = "M13")
    public String actionCode;//  actionCode

    @JSONField(name = "M14")
    public String checkId;//

    @JSONField(name = "M15")
    public List<String> fields;//
    @JSONField(name = "M16")
    public List<String> dataIds;//  自定义对象多条数据IDs    getActionData的时候 加个参数。 值就从custmerAction里面取那个 dataIds

    public int needFuncActionText;//1-需要函数返回值
}
