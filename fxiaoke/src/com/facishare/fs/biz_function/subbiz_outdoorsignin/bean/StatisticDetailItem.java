package com.facishare.fs.biz_function.subbiz_outdoorsignin.bean;

import android.os.Parcel;
import android.os.Parcelable;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

public class StatisticDetailItem implements Parcelable{

	
	/**
	 * 
	 */
	/**
	 * optional string userAccount = 1;
 optional string userName = 2;
 optional int64 timestamp = 3;
 optional string address = 4;
 optional int32 customerId = 5;
 optional string customerName = 6;
 optional bool distantRisk = 7;
 optional int32 distantRiskDesc = 8;
	 */
	
	@JSONField(name="M1")
    public int userAccount;
	@JSONField(name="M2")
    public String userName;
	@JSO<PERSON>ield(name="M3")
    public long timestamp;
	@JSONField(name="M4")
    public String address;
	@JSONField(name="M5")
    public int customerId;
	@JSONField(name="M6")
    public String customerName;
	@JSONField(name="M7")
    public boolean distantRisk;
	@JSONField(name="M8")
    public int distantRiskDesc;
	@JSONField(name="M9")
    public String sortKey;
	
	@JSONCreator
	public StatisticDetailItem(@JSONField(name="M1") int userAccount,
			@JSONField(name="M2") String userName,
			@JSONField(name="M3") long timestamp,
			@JSONField(name="M4") String address,
			@JSONField(name="M5") int customerId,
			@JSONField(name="M6") String customerName,
			@JSONField(name="M7") boolean distantRisk,
			@JSONField(name="M8") int distantRiskDesc,
			@JSONField(name="M9") String sortKey){
		
		this.userAccount = userAccount;
		this.userName = userName;
		this.timestamp = timestamp;
		this.address = address;
		this.customerId = customerId;
		this.customerName = customerName;
		this.distantRisk = distantRisk;
		this.distantRiskDesc = distantRiskDesc;
		this.sortKey = sortKey;
		
	}

	public StatisticDetailItem(Parcel in){
		this.userAccount = in.readInt();
		this.userName = in.readString();
		this.timestamp = in.readLong();
		this.address = in.readString();
		this.customerId = in.readInt();
		this.customerName = in.readString();
		this.distantRisk = in.readByte() != 0;
		this.distantRiskDesc = in.readInt();
		this.sortKey = in.readString();
	}

	@Override
	public int describeContents() {
		// TODO Auto-generated method stub
		return 0;
	}
	@Override
	public void writeToParcel(Parcel dest, int flags) {
		// TODO Auto-generated method stub
		dest.writeInt(userAccount);
		dest.writeString(userName);
		dest.writeLong(timestamp);
		dest.writeString(address);
		dest.writeInt(customerId);
		dest.writeString(customerName);
		dest.writeByte((byte) (distantRisk ? 1 : 0));
		dest.writeInt(distantRiskDesc);
		dest.writeString(sortKey);
		
	}
	public static final Parcelable.Creator<StatisticDetailItem> CREATOR = new Creator<StatisticDetailItem>() {
		
		@Override
		public StatisticDetailItem[] newArray(int size) {
			// TODO Auto-generated method stub
			return new StatisticDetailItem[size];
		}
		
		@Override
		public StatisticDetailItem createFromParcel(Parcel source) {
			// TODO Auto-generated method stub
			 return new StatisticDetailItem(source);
		}
	};
}
