package com.facishare.fs.biz_function.subbiz_outdoorsignin.bean;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.outdoor_offline.OfflineUtils;
import com.fxiaoke.fxlog.FCLog;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class GetBaseInfoDataForReportResult  extends BaseResult implements Serializable {
    private static final long serialVersionUID = -8520624362719948087L;


    public Map<String,List<JSONObject>> baseInfoMap;//分类-ProductCategoryObj，竞品（竞争产品）-CompetitiveProductsObj，品牌（竞争对手）-CompetitorObj

    public List<CheckinsFields> showFields; //竞品的移动端布局

    public Map<String,List<CheckinsFields>> showFieldMap; //其他对象的移动端布局 数据已经有字端了
    public int folg;

    /**
     * 打印日志
     * @param v2
     */
    public static String printLog(GetBaseInfoDataForReportResult v2){
        if(v2==null){
            return "null";
        }
        StringBuffer sb = new StringBuffer();
        if(v2.baseInfoMap!=null){
            for (Map.Entry<String,List<JSONObject>> entry : v2.baseInfoMap.entrySet()) {

                sb.append( JSON.toJSONString(entry.getValue())+",");
            }
        }
        return sb.toString();
    }

}
