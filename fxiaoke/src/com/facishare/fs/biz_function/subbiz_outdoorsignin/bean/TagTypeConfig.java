package com.facishare.fs.biz_function.subbiz_outdoorsignin.bean;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class TagTypeConfig implements Serializable {
    private static final long serialVersionUID = -4315425998635595192L;
    @JSONField(name = "M10")
    public int isOpenTagConfig;//是否开启标签配置  0未开启   1 开启

    @JSONField(name = "M11")
    public String tagApiName;//标签ApiName

    @JSONField(name = "M12")
    public Map<String,TagOptionLink> optionLink;//标签ApiName

    public List<TagFieldConfig> tagFieldConfigs;//标签ApiName  --改成多选用这个

    public List<TagOptionLink> tagOptionLinks;//配置跳转也能多选选项
}
