
package com.facishare.fs.biz_function.subbiz_outdoorsignin.bean;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

public class CustomerVisitsStatisticsArgs {

    @J<PERSON><PERSON>ield(name="M10")
    public  List<Integer> customerTagIds;
    @JSONField(name="M11")
    public  Long timeGT;
    @JSONField(name="M12")
    public  Long timeLT;
    @JSONField(name="M13")
    public  int pageNum;
    @J<PERSON><PERSON>ield(name="M14")
    public  int pageSize;
    @JSONField(name="M15")
    public  String searchText;

    /**
     * @param customerTagIds
     * @param timeGT
     * @param timeLT
     * @param pageNum
     * @param pageSize
     * @param searchText
     */
    public CustomerVisitsStatisticsArgs(){
        super();
    }

}
