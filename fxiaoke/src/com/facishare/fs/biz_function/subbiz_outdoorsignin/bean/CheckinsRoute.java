package com.facishare.fs.biz_function.subbiz_outdoorsignin.bean;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;

/**
 * Created by sunhq on 2018/8/1.
 */

public class CheckinsRoute implements Serializable{

    @JSONField(name = "M1")
    public String routeId;

    @JSONField(name = "M2")
    public String routeName;//路线名称

    @JSONField(name = "M3")
    public int routeLoopType;//路线循环类型 1-每周循环 2-每月循环

    @JSONField(name = "M4")
    public int userId;//执行人id

    @JSONField(name = "M5")
    public List<Integer> routeLoopValueList;//路线循环类型 按周循环 存的周几 按月循环存的日期

    @JSONField(name = "M6")
    public String checkTypeId;//签到类型id

    @JSONField(name = "M7")
    public List<Customer4Route> customerList;

}
