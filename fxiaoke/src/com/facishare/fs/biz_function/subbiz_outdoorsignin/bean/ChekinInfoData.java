package com.facishare.fs.biz_function.subbiz_outdoorsignin.bean;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Created by wangyp on 2018/1/24.
 */

public class ChekinInfoData implements Serializable{

    private static final long serialVersionUID = -3542332681519701404L;
    @JSONField(name = "M1")
    public String checkinId;//外勤签到 id

    @JSONField(name = "M2")
    public String checkinPlanTime;//外勤计划时间

    @JSONField(name = "M3")
    public String checkInTime;//签到时间

    @JSONField(name = "M4")
    public String checkinAddress;//外勤签到地址

    @JSONField(name = "M5")
    public String checkOutTime;//签退时间

    @JSONField(name = "M6")
    public String checkOutAddress;//外勤签退地址

    /**
     * -1已删除
     * //迁到记录状态 0 初始状态；1入库成功；2图片附件入库成功；3feed发送成功,
     * -2待审批
     * -3待申请
     * 4.这个状态也代指高级外勤完成状态；
     */
    @JSONField(name = "M7")
    public int status;

    @JSONField(name = "M8")
    public int userId;//负责人ID
    public String userName;

    @JSONField(name = "M9")
    public long planTimeLong;//计划时间时间戳

    @JSONField(name = "M10")
    public String routeId;//路线id  用于路线编辑
    @JSONField(name = "M17")
    public long endTimeLong;

    //不为空的时候展示处理 (用时间判断的话不为0-- 跳店的话这两个字段肯定有值)

    @JSONField(name = "M19")
    public long jumpTime; //跳店时间

    @JSONField(name = "M20")
    public String jumpAddress; //跳店地址
    /**
     * 计划类型
     * null,1 默认计划,正常计划 . 2跨天计划
     */
    public int planType;

}
