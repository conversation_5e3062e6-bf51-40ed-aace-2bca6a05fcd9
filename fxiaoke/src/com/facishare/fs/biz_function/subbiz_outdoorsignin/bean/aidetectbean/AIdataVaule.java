package com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.aidetectbean;

import com.alibaba.fastjson.JSONObject;
import com.fs.commonviews.ai.AINeedDealWithData;

import java.io.Serializable;
import java.util.Map;

public class AIdataVaule implements Serializable {
    public int errorCode;
    public String errorMessage;
    public AINeedDealWithData data;
    public Map<String,Object> objectData;
    public String logId;
    public boolean recapture;//是否翻拍
    public boolean sceneMismatch;//是否错误场景
    public Map<String,Object> extraData;//透传参数

}
