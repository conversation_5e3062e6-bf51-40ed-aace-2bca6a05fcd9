package com.facishare.fs.biz_function.subbiz_outdoorsignin.bean;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;

public class TagOptionLink implements Serializable {
    private static final long serialVersionUID = 8518437913650084906L;
    @JSONField(name = "M10")
    public int linkType;//0 不跳   -1 今麦郎  1 对象列表

    @JSONField(name = "M11")
    public String tagValue;//value

    @JSONField(name = "M12")
    public String objApiName;//对象apiName

    @JSONField(name = "M13")
    public String referenceApiName;//对象查找关联客户的字段

    @JSONField(name = "M14")
    public String wheres;//where条件

    public List<String> tagValues;// 配置多个标签值
//    public JSONArray getWheresByStr(){
//        if(StringUtils.isEmpty(this.wheres)){
//            return null;
//        }
//        return JSONArray.parseArray(this.wheres);
//    }
//    public void setWheresStr(JSONArray array){
//        if(CollectionUtils.isEmpty(array)){
//            return;
//        }
//        this.wheres = array.toJSONString();
//    }
}