package com.facishare.fs.biz_function.subbiz_outdoorsignin.bean;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;

/**
 * Created by wangyp on 2018/1/24.
 */

public class CrmInfoData implements Serializable{

    private static final long serialVersionUID = 1933224552605984180L;
    @JSONField(name = "M1")
    public String crmObject;//外勤对象
    /**
     * 关联客户
     */
    
    @JSONField(name = "M2")
    public String customerId;

    
    @JSONField(name = "M3")
    public String customerName;//客户名称

    /**
     * 客户坐标Longitude
     */
    
    @JSONField(name = "M4")
    public double customerLon;//客户经度
    /**
     * 客户位置Latitude
     */
    
    @JSONField(name = "M5")
    public double customerLat;//客户纬度

    /**
     * 客户地址
     */
    
    @JSONField(name = "M6")
    public String customerAddress;


    /**
     * 主对象 6.3.3
     */
    
    @JSONField(name = "M7")
    public ObjectInfo mainObject;


    /**
     * 从对象 6.3.3
     */
    
    @JSONField(name = "M8")
    public List<ObjectInfo> referenceObject;
}
