package com.facishare.fs.biz_function.subbiz_outdoorsignin.bean;

import com.alibaba.fastjson.annotation.JSONField;

import org.json.JSONArray;

import java.io.Serializable;
import java.util.List;

public class VisitObjInfo implements Serializable {

    /**
     * 标题名称
     */
    
    @JSONField(name = "M10")
    
    public String titleName;

    /**
     * 展示信息类型 (main/reference)
     */
    
    @JSONField(name = "M11")
    
    public String infoType;

    /**
     * 主对象ApiName（AccountObj、OpportunityObj）
     */
    
    @JSONField(name = "M12")
    
    public String mainObjApiName;



    /**
     * 主对象的布局ApiName
     */
    
    @JSONField(name = "M13")
    
    public String mainObjLayoutApiName;

    /**
     * 展示的字段
     */
    
    @JSONField(name = "M14")
    
    public List<String> showFiledApiNames;




    /**
     * 关联主对象的对象ApiName
     */
    
    @JSONField(name = "M15")
    
    public String referenceObjApiName;

    /**
     * 关联主对象的查找关联字段ApiName
     */
    
    @JSONField(name = "M16")
    
    public String referenceFieldApiName;

    /**
     * 关联对象显示的数据范围
     */
    
    @JSONField(name = "M17")
    
    public List<Object> referenceObjWheres;

    /**
     * 查找关联的对象布局的ApiName
     */
    
    @JSONField(name = "M18")
    
    public String referenceObjLayoutApiName;


}