package com.facishare.fs.biz_function.subbiz_outdoorsignin.bean;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

public class QueryCheckinDataFeedIdResult implements Serializable {

    public QueryCheckinDataFeedIdResult() {

    }

    /**
     * checkins
     */
    private List<CheckinsSimple> checkins;

    @JSONField(name = "M11")
    public List<CheckinsSimple> getCheckins() {
        return checkins;
    }

    @JSONField(name = "M11")
    public void setCheckins(List<CheckinsSimple> checkins) {
        this.checkins = checkins;
    }

}
