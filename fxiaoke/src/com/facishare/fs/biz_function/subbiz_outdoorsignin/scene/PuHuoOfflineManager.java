package com.facishare.fs.biz_function.subbiz_outdoorsignin.scene;

import android.content.Context;
import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.AbsOutdoorRecord;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CheckType;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CheckinsFields;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CheckinsInfo;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.ChekinInfoData;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CrmInfoData;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CustomerAction;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CustomerActionSimple;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.DownAccountData;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.DownLoadAccountDataResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.GetDailyInfoV4Result;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.GetEmployeeRuleResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.IdAndNameEx;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.IntegralBean;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.ObjectInfo;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.OutDoor2CacheManger;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.OutdoorLimit;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2Constants;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2CreateActivity;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2Utils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.outdoor_offline.IOutDoorv2Task;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.outdoor_offline.OfflineUtils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.outdoor_offline.OutDoorV2OffLineManager;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.outdoor_offline.download.ObjectId;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.selectobj.CheckCustomer;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.view.OutDoorV2PlanTimeView;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.utils.OutdoorCommonUtils;
import com.facishare.fs.common_datactrl.draft.OutDoorVO;
import com.facishare.fs.common_utils.DateTimeUtils;
import com.facishare.fs.common_utils.time.NetworkTime;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.crm.type.CoreObjType;
import com.facishare.fs.pluginapi.crm.type.ICrmBizApiName;
import com.facishare.fs.utils_fs.OutDoorHomeStyleSetUtils;
import com.facishare.fs.utils_fs.ToastUtils;
import com.fxiaoke.fscommon_res.utils.FsMapUtils;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.location.api.FsLocationResult;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;

public class PuHuoOfflineManager {

    private final static String TAG = PuHuoOfflineManager.class.getSimpleName();

    public static CheckinsInfo pickCheckinsInfoById(String checkinId){
        ScenData scenData = OfflineDataManager.getInstance().
                get(OfflineDataManager.Puhuo_Offline_Checkinsinfo,new ScenData());//当前执行中的铺货记录
        if(scenData!=null&&scenData.checkinsList!=null){
            for(CheckinsInfo checkinsInfo:scenData.checkinsList){
                if(TextUtils.equals(checkinId,checkinsInfo.checkinId)){
                    if(isToday(checkinsInfo)){
                       return checkinsInfo;
                    }
                }
            }
        }
        return null;
    }

    public static CheckinsInfo pickCheckinsInfo(ScenData scenData,String customerId){
        if(scenData!=null&&scenData.checkinsList!=null){
            for(CheckinsInfo checkinsInfo:scenData.checkinsList){
                if(checkinsInfo.mainObject!=null
                        &&checkinsInfo.mainObject.dataId!=null)
                    if(customerId!=null
                            &&customerId.equals(checkinsInfo.mainObject.dataId)){
                        if(!isToday(checkinsInfo)){
                            FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG, "不是当天:"+
                                    checkinsInfo.printf()+",checkinsTime:"+
                                    DateTimeUtils.formatSpaceDate(new Date(checkinsInfo.checkinsTime)));
                            continue;
                        }
                        return checkinsInfo;
                    }
            }
        }
        return null;
    }

    public static CheckinsInfo peekCheckinsInfo( ArrayList<CheckinsInfo> cacheList,String customerId){
        Iterator<CheckinsInfo> it = cacheList.iterator();
        while(it.hasNext()) {
            CheckinsInfo info = it.next();
            if (info.mainObject != null
                    && info.mainObject.dataId != null)
                if (customerId != null
                        && customerId.equals(info.mainObject.dataId)) {
                    it.remove();
                    return info;
                }
        }

        return null;
    }

//    public static

    public static List<CheckinsInfo> pickCheckinsInfoList(){
        ScenData scenData = OfflineDataManager.getInstance().
                get(OfflineDataManager.Puhuo_Offline_Checkinsinfo,new ScenData());//当前执行中的铺货记录
        return pickCheckinsInfoList(scenData);
    }


    public static List<CheckinsInfo> pickCheckinsInfoList(ScenData scenData){
        List<CheckinsInfo> items = new ArrayList<>();
        if(scenData!=null&&scenData.checkinsList!=null){
            for(CheckinsInfo checkinsInfo:scenData.checkinsList){
                if(isToday(checkinsInfo)){
                    items.add(checkinsInfo);
                }
            }
        }
        return items;
    }


    public static boolean isToday(CheckinsInfo checkinsInfo){

        FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG, "isToday:"+ checkinsInfo.printf()+","+
                DateTimeUtils.formatSpaceDate(new Date(checkinsInfo.checkinsTime)));
        if(checkinsInfo.checkinsTime!=0){
            if(!isToday(checkinsInfo.checkinsTime)){
                //只加载当天数据,之前未执行的不显示
                FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG, "不是当天:"+
                        checkinsInfo.printf()+",checkinsTime:"+
                        DateTimeUtils.formatSpaceDate(new Date(checkinsInfo.checkinsTime)));
                return false;
            }
        }
        return true;
    }

    /**
     * 搜索1公里范围 由近及远 所有符合要求的门店
     * @param location
     */
    public static List<CheckinsInfo> search(FsLocationResult location){
        FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG, "search....->" );

        DownLoadAccountDataResult data = OfflineDataManager.getInstance().
                get(OfflineDataManager.Puhuo_Offlihe_Customer,new DownLoadAccountDataResult());//缓存的所有客户数据

        /**
         * 执行中的数据,
         * 包含:
         * 1.本地构造的数据
         * 2.网络构造的数据
         */
        ScenData scenData = OfflineDataManager.getInstance().
                get(OfflineDataManager.Puhuo_Offline_Checkinsinfo,new ScenData());//当前执行中的铺货记录

        HashMap<String,PuhuoDoneData> doneMap = pickDataOfDone();


        ArrayList<CheckinsInfo> result = new ArrayList<>();

        ArrayList<CheckinsInfo> exeingList = new ArrayList<>();

        ArrayList<CheckinsInfo> cacheList = new ArrayList<>(pickCheckinsInfoList(scenData));
        ArrayList<CheckinsInfo> doneList = new ArrayList<>();

        CheckType checkType = OutDoorV2Utils.pickPuhuoCheckType();
        if(checkType==null){
            ToastUtils.show(I18NHelper.getText("outdoor.puhuo.tip"));
            return result;
        }
        if(data!=null && data.datas!=null){//从本地帅选出数据
            for(DownAccountData dad : data.datas){
                FsLocationResult loc = new FsLocationResult(dad.lat,dad.lon);
                float distance1 = FsMapUtils.calculateLineDistance(loc, location);
                if(distance1<=100000){
                    CheckinsInfo info = peekCheckinsInfo(cacheList,dad.accountId);//pickCheckinsInfo(scenData,dad.accountId);//查询是否存在执行中的
                    if(info!=null){//执行中
                        CheckType ct = OutDoor2CacheManger.readCacheById(info.checkinId);
                        checkType2Checkinsinfo(ct,info);
                        if (info.isCheckinFinish==1){
                            FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG, "is exeing but is checkin finish ->"+ info.printf());
                            doneList.add(info);
                        }else{
                            FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG, "is exeing ....->"+ info.printf());
                            exeingList.add(info);
                        }
                        continue;
                    }

                    PuhuoDoneData puhuo = doneMap.get(dad.accountId);
                    if(puhuo!=null){//已完成
                        CheckType ct = OutDoor2CacheManger.readCacheById(puhuo.checkId);
                        if(ct!=null){
                            info =  accountToChckinsInfo(dad,data,distance1,puhuo.checkId);
//                            info=OfflineUtils.CheckTypeToCheckinsinfo(ct);
                            checkType2Checkinsinfo(ct,info);
                            PuhuoOfflineUtils.taskDataToCheckinsInfo(info);
                            info.isCheckinFinish = 1;//此处出现必然已完成
                            doneList.add(info);
                        }
                        continue;
                    }
                    info = createCheckinsInfo(checkType,dad,data,distance1);
                    result.add(info);

                }
            }
        }

        exeingList.addAll(cacheList);//本地没有缓存的客户列表时

        Collections.sort(result, (o1, o2) -> (int) (o1.distanceBetween - o2.distanceBetween));

        result.addAll(0,exeingList);

        result.addAll(doneList);

        return result;
    }
//
//    /**
//     * 获取铺货的CheckType
//     * @return
//     */
//    private static CheckType getCheckType(){
//        GetEmployeeRuleResult ruleResult = OutDoor2CacheManger.getCacheRule();
//        if(ruleResult!=null){
//            if(ruleResult.checkTypeList!=null && ruleResult.checkTypeList.size()>0){
//                return ruleResult.checkTypeList.get(5);
//            }
//        }
//        return null;
//    }
    public static CheckType createChecktype(CheckType checkType,CheckinsInfo info){
        if(checkType!=null){
            checkType.chekinInfoData = new ChekinInfoData();
            checkType.chekinInfoData.checkinId = info.checkinId;
            checkType.indexId = info.indexId;
            checkType.typeId = info.checkTypeId;
            checkType.typeName = info.checkTypeName;
            checkType.chekinInfoData.checkinPlanTime = null;//OutDoorV2PlanTimeView.getShowPlanTimeStr(info.planTime);
            checkType.remark = info.remark;
            checkType.executorId = info.userId;
            checkType.crmInfoData = new CrmInfoData();
            checkType.crmInfoData.mainObject = info.mainObject;
            checkType.crmInfoData.referenceObject = info.referenceObject;
            checkType.planRepeater = info.planRepeater;
            checkType.isRecycler = info.isRecycler;
            checkType.checkinsScene=info.checkinsScene;
            checkType.targetCode = info.targetCode;
            checkType.deletable = true;
            checkType.checkinsScene=info.checkinsScene;
            checkType.checkOutFlag= info.checkOutFlag ;
            if(info.jmlFormData!=null){
                checkType.mainObjQuoteData=new JSONObject(info.jmlFormData);
            }

            if(checkType.actionList!=null && info.customerActionSimples!=null){

                int size =  info.customerActionSimples.size();
                Iterator<CustomerAction> iterator= checkType.actionList.iterator();
                int i = 0;
                while(iterator.hasNext() && i<size){
                    CustomerAction ca = iterator.next();
                    ca.actionId =info.customerActionSimples.get(i).actionId;
                    setOtherConfigInfo(checkType,ca);
                    i++;
                }

            }

            FCLog.d(OfflineUtils.OUTDOOR_OFFLINE,TAG, "convertCheckType:"+checkType.printfAll());

            return checkType;
        }

        return null;
    }

    public static CheckType convertCheckType(CheckinsInfo info){
        CheckType checkType = OutDoorV2Utils.pickPuhuoCheckType();
        createChecktype(checkType,info);
        return checkType;
    }

    /**
     * 离线生成checkType时,给otherConfigInfo赋值
     * @param checkType
     * @param ca
     */
    public static void setOtherConfigInfo(CheckType checkType,CustomerAction ca){
        if("new_number_form".equals(ca.actionCode) && ca.newFormSetting!=null && checkType.mainObjQuoteData!=null){
            if(ca.newFormSetting.otherConfigInfo!=null){
                JSONObject jsonObject = JSON.parseObject( ca.newFormSetting.otherConfigInfo);
                jsonObject.putAll(checkType.mainObjQuoteData);
                ca.newFormSetting.otherConfigInfo= JSON.toJSONString(jsonObject);
            }else{
                ca.newFormSetting.otherConfigInfo= JSON.toJSONString(checkType.mainObjQuoteData);
            }
        }
    }

    public static boolean isToday(long time){
        Date currentTime = new Date(NetworkTime.getInstance(HostInterfaceManager.getHostInterface().getApp()).getServiceDateTime());
        Date checkInTime = new Date(time);
        if(currentTime.getYear() == checkInTime.getYear()
                && currentTime.getMonth() == checkInTime.getMonth()
                && currentTime.getDate() == checkInTime.getDate()){
            return true;
        }else{
            return false;
        }
    }


    public static HashMap<String,PuhuoDoneData> pickDataOfDone(){
        ScenData scenData = OfflineDataManager.getInstance().
                get(OfflineDataManager.Puhuo_Offline_Checkinsinfo,new ScenData());//当前执行中的铺货记录

        HashMap<String,PuhuoDoneData> exeMap =
                OfflineDataManager.getInstance().get(OfflineDataManager.Puhuo_Offline_Record_State,
                        new HashMap<>());//执行中的数据状态

        HashMap<String,PuhuoDoneData> doneMap = new HashMap<>();//完成的数据

        if(exeMap!=null){
            Iterator<String> iter =  exeMap.keySet().iterator();
            while(iter.hasNext()){
                String key = iter.next();
                PuhuoDoneData pdd = exeMap.get(key);
                if(pdd!=null){
                    CheckinsInfo info = pickCheckinsInfo(scenData,pdd.dataId);
                    if(info==null&&isToday(pdd.doneTime)){
                        FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG, "已完成....->"+pdd);
                        doneMap.put(key,pdd);
                    }
                }
            }
        }
        return doneMap;
    }


    public static void handlerNetWorkAndCacheData(Context context,FsLocationResult location,
                             GetDailyInfoV4Result result,
                             List<AbsOutdoorRecord> items){

//        OutdoorCommonUtils.puhuoByDistance(result,location);//排序

        FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG, "handlerNetWorkAndCacheData--------->");

        HashMap<String,CheckinsInfo> NetWorkDataMap = new HashMap<>();
        HashMap<String,CheckinsInfo> doneListMap = new HashMap<>();//已完成的map

        HashMap<String,CheckinsInfo> exeListMap = new HashMap<>();//已完成的map

        if(result!=null &&
                result.sceneDataList!=null&&
                result.sceneDataList.size()>0){
            List<CheckinsInfo> infos = result.sceneDataList.get(0).checkinsList;
            if(infos!=null){
                FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"network of data size  is :"+infos.size());
                //后台下发的数据中, 可能在本地已经离线完成了,此时要过滤掉
                Iterator<CheckinsInfo>  it = infos.iterator();
                HashMap<String,PuhuoDoneData> doneMap = pickDataOfDone();
                while(it.hasNext()){
                    CheckinsInfo info = it.next();
                    FCLog.d(OfflineUtils.OUTDOOR_OFFLINE,TAG,"all network data: " + info.printf());
                    if(info.mainObject!=null&&
                            info.mainObject.dataId!=null){
                        PuhuoDoneData ph = doneMap.get(info.mainObject.dataId);
                        boolean isAdd = true;
                        boolean isExeing = OutdoorLimit.isExeing(info);

                        if(isExeing){
                            FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"network of data is exeing  !" + info.printf());
                            exeListMap.put(info.mainObject.dataId,info);
                            it.remove();
                            isAdd = false;
                        }

                        //先判断本地是否存在完成
                        if(info.isCheckinFinish==1 || (ph!=null && isToday(ph.doneTime) && !isExeing)){
                            FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG,"network of data is done ,so  remove it !" + info.printf());
                            it.remove();
//                            doneList.add(info);
                            doneListMap.put(info.mainObject.dataId,info);
                            isAdd = false;
                        }

                        if(isAdd){
                            if(PuHuoOfflineManager.isOpenOffline(context)){
                                info.needUpdateOwner = 0;
                                info.targetCode = OutDoorV2CreateActivity.OUTDOOR_OFFLINE_ID_KEY;
                            }
                            info.distanceBetween = CheckCustomer.distanceBetween(info.customerLon+"|"+info.customerLat,location);
                            NetWorkDataMap.put(info.mainObject.dataId,info);
                        }
                    }

                }
            }
        }

//        ArrayList<CheckinsInfo> differList = new ArrayList<>();
        //网络下发了数据,去重
        if(NetWorkDataMap.size()>0){
            Iterator<AbsOutdoorRecord> iter = items.iterator();
            while(iter.hasNext()){
                AbsOutdoorRecord record = iter.next();
                CheckinsInfo info = (CheckinsInfo) record;
                FCLog.d(OfflineUtils.OUTDOOR_OFFLINE,TAG,"local cache data: " + info.printf());
                if(info.mainObject!=null&&
                        info.mainObject.dataId!=null){
                    boolean isExeing = OutdoorLimit.isExeing(info);//执行中的数据,不能被网络数据无状态的数据替换
                    boolean isDelete = false;
                    CheckinsInfo netInfo = NetWorkDataMap.get(info.mainObject.dataId);
                    if(netInfo!=null){
//                        boolean netExeing = OutdoorLimit.isExeing(info);
                        if(!isExeing){//执行中的数据,不能被网络数据无状态的数据替换
                            FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG, "重复 remove local cache->"+netInfo.printf());
                            iter.remove();
                        }else{
                            NetWorkDataMap.remove(info.mainObject.dataId);
                            FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG, "重复 remove net data->"+netInfo.printf());
                        }
                        isDelete = true;
                    }
                    //网络执行中的数据.去重
                    if(!isDelete){
                        if(exeListMap.containsKey(info.mainObject.dataId)){
                            FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG, "重复 remove local cache [net exeing]->"+info.printf());
                            iter.remove();
                            isDelete = true;
                        }
                    }
                    //网络完成的数据,去重
                    if(!isDelete){
                        if(doneListMap.containsKey(info.mainObject.dataId)){
                            FCLog.i(OfflineUtils.OUTDOOR_OFFLINE,TAG, "重复 remove local cache [net done]->"+info.printf());
                            iter.remove();
                            isDelete = true;
                        }
                    }
                }
            }
        }

        /**
         * 循环本地数据,过滤 状态不符合的数据
         * 本地数据和网络数据存在不同步的情况;
         * 1. 本地数据已经完成,但是网络数据执行中,此时已本地数据状态为主,删除网络数据
         * 2.本地数据执行中,但是网络数据完成,此时已网络数据为主,删除本地数据
         */
        Iterator<AbsOutdoorRecord> it =  items.iterator();
        while(it.hasNext()){
            AbsOutdoorRecord re = it.next();
//            StringBuffer sb = new StringBuffer();
            if(re instanceof CheckinsInfo){
                CheckinsInfo cInfo = (CheckinsInfo) re;
                if(cInfo.isCheckinFinish==1){//本地数据已经完成时,网络数据可能是执行中
                    CheckinsInfo dc = doneListMap.get(cInfo.mainObject.dataId);
                    if(dc==null){
                        FCLog.d(OfflineUtils.OUTDOOR_OFFLINE,TAG,"local done of data put :" + cInfo);
                        doneListMap.put(cInfo.mainObject.dataId,cInfo);
                    }else{
                        if(OutdoorLimit.isExeing(dc)){//说明网络数据是执行中,并且在列表当中了 ,需要删除
                            FCLog.d(OfflineUtils.OUTDOOR_OFFLINE,TAG,"local is done but  network is exeing ,so remove from exelist :" + cInfo);
                            exeListMap.remove(cInfo.mainObject.dataId);
                        }
                    }
                    it.remove();
                }else if(OutdoorLimit.isExeing(cInfo)){//本地数据执行中时

                        if(doneListMap.containsKey(cInfo.mainObject.dataId)){//但是网络状态是完成
                            FCLog.d(OfflineUtils.OUTDOOR_OFFLINE,TAG,"local exeing of data but network is done ,so remove from exelist :" + cInfo);
                            exeListMap.remove(cInfo.mainObject.dataId);//此时删除本地执行中的数据;
                        }else{
                            if(!exeListMap.containsKey(cInfo.mainObject.dataId)){
                                FCLog.d(OfflineUtils.OUTDOOR_OFFLINE,TAG,"local data is exeing and network isnot exeing ,so put to exelist :" + cInfo);
                                exeListMap.put(cInfo.mainObject.dataId,cInfo);
                            }
                        }

                    it.remove();
                }
//                if(sb.length()!=0){
//                    FCLog.d(OfflineUtils.OUTDOOR_OFFLINE,TAG,"exeing " + sb.toString());
//                }
            }
        }

        items.addAll(NetWorkDataMap.values());//网络数据

        //执行中的数据 置顶
        //执行中的数据可能已经完成了,所以需要过滤掉已经完成的数据
        ArrayList<AbsOutdoorRecord> exeingList = new ArrayList<>(exeListMap.values());
        ArrayList<AbsOutdoorRecord> doneList = new ArrayList<>(doneListMap.values());//已完成的数据,放在最后
        AbsOutdoorRecord.sort(items,location);

        AbsOutdoorRecord.sort(exeingList,location);

        AbsOutdoorRecord.sort(doneList,location);

        items.addAll(0,exeingList);

        items.addAll(doneList);

        if(!items.isEmpty()){
            if(OutDoorHomeStyleSetUtils.getRuleHomeStyleResultBykey(OutDoorHomeStyleSetUtils.kkIsShowStatus_key) == 1){
                items.add(0,new IntegralBean(result.integralTotal));
            }
        }

    }



    public static void handlerNetWorkData(FsLocationResult location, GetDailyInfoV4Result result, List<AbsOutdoorRecord> items){


        if(result!=null &&
                result.sceneDataList!=null&&
                result.sceneDataList.size()>0) {
            List<CheckinsInfo> infos = result.sceneDataList.get(0).checkinsList;
            if (infos != null) {
                for(CheckinsInfo checkinsInfo:infos){
                    checkinsInfo.distanceBetween = CheckCustomer.distanceBetween(checkinsInfo.customerLon+"|"+checkinsInfo.customerLat,location);
                }
                items.addAll(infos);
            }
        }

        if(!items.isEmpty()){
            if(OutDoorHomeStyleSetUtils.getRuleHomeStyleResultBykey(OutDoorHomeStyleSetUtils.kkIsShowStatus_key) == 1){
                items.add(0,new IntegralBean(result.integralTotal));
            }
        }

    }


    public static List<AbsOutdoorRecord> sortDone(ArrayList<AbsOutdoorRecord> mDatas,FsLocationResult location){
        ArrayList<AbsOutdoorRecord> newData = new ArrayList<>();
        ArrayList<AbsOutdoorRecord> doneList = new ArrayList<>();
        if(mDatas!=null){
            Iterator<AbsOutdoorRecord> it =  mDatas.iterator();
            while(it.hasNext()){
                AbsOutdoorRecord re = it.next();
                if(re instanceof CheckinsInfo) {
                    CheckinsInfo cInfo = (CheckinsInfo) re;
                    if(cInfo.isCheckinFinish == 1){
                        it.remove();
                        doneList.add(cInfo);
                    }
                }
            }
        }

        newData.addAll(mDatas);

        AbsOutdoorRecord.sort(doneList,location);

        newData.addAll(doneList);

        return newData;
    }

    public static String getLocation(String location){
        if(location!=null){
            String[] resultArr = TextUtils.split(location, MetaDataUtils.LOCATION_DIV_REG);
            if (resultArr.length != 3) {
                return "";
            }
            String [] addr = TextUtils.split(resultArr[2], ";");
            if(addr.length>0){
                return addr[0];
            }
        }
        return location;
    }

    public static CheckinsInfo accountToChckinsInfo(DownAccountData dad,DownLoadAccountDataResult data, float distance1,String objId){
        CheckinsInfo info = new CheckinsInfo();
        info.distanceBetween = distance1;
        info.customerLat = dad.lat;
        info.customerLon = dad.lon;
        info.customerAddress = getLocation(dad.location);
        info.isHigh=1;
        info.checkinId = objId;
        info.indexId = objId;
        info.userId = FSContextManager.getCurUserContext().getAccount().getEmployeeIntId();
        info.mainObject =  new ObjectInfo();
        info.mainObject.dataId = dad.accountId;
        info.mainObject.name = dad.name;
        info.mainObject.objName= data.objName;
        info.mainObject.apiName= data.objApiName;
        info.mainObject.locationFieldApiName = data.locationFieldApiName;
        info.mainObject.source = data.source;
        info.mainObject.info=dad.location;
        info.jmlFormData=dad.jmlFormData;
        //客户名称
        info.CheckinsFields = new ArrayList<>();
        CheckinsFields checkinsFields = new CheckinsFields();
        checkinsFields.fieldApiName = CheckinsFields.CheckInMainName_Field;
        checkinsFields.fieldLabel = data.objName;
        checkinsFields.fieldValue = dad.name;
        info.CheckinsFields.add(checkinsFields);

        if(data.showFields!=null){
            for(CheckinsFields filed:data.showFields){
                filed.fieldValue = dad.getFieldValue(filed.fieldApiName);
                info.CheckinsFields.add(filed);
            }
        }
        info.checkinsScene = getPuhuoScene();
        info.doorPhoto = dad.getDoorPhoto();

        return info;
    }

    public static CheckinsInfo createCheckinsInfo(CheckType checkType,DownAccountData dad,DownLoadAccountDataResult data, float distance1 ){
        String objId = new ObjectId().toHexString();
        CheckinsInfo info = accountToChckinsInfo(dad,data,distance1,objId);
        info.targetCode=OutDoorV2CreateActivity.OUTDOOR_OFFLINE_ID_KEY;
        info.isHigh = 1;
        info.isNoCheckins = checkType.isHasCheckins;
        info.isOpenCheckOut = checkType.checkOutFlag >0?1:0;
        info.checkOutFlag = checkType.checkOutFlag;
        info.checkTypeId = checkType.typeId;
        info.checkTypeName = checkType.typeName;
        info.customerActionSimples = new ArrayList<>();
//        StringBuffer stringBuffer = new StringBuffer(info.printf());
        FCLog.d(OfflineUtils.OUTDOOR_OFFLINE,TAG, info.printf());
        FCLog.d(OfflineUtils.OUTDOOR_OFFLINE,TAG, "----------start-----------");
        if (checkType.actionList!=null&&checkType.actionList.size()>0){
            for (CustomerAction ca :checkType.actionList){
                CustomerActionSimple cas = new CustomerActionSimple();
                cas.actionId = new ObjectId().toHexString();//ca.actionId;
                FCLog.d(OfflineUtils.OUTDOOR_OFFLINE,TAG, ""+cas.actionName+"["+ cas.actionId+"],");
                cas.actionName = ca.actionName;
                cas.sourceActionId = ca.sourceActionId;
                cas.isRequired = ca.isRequired;
                info.customerActionSimples.add(cas);
            }
        }
//        FCLog.d(OfflineUtils.OUTDOOR_OFFLINE,TAG, stringBuffer.toString());
        FCLog.d(OfflineUtils.OUTDOOR_OFFLINE,TAG, "----------end-----------");
        return info;
    }
    public static CheckinsInfo objectInfo2CheckInfo(DownLoadAccountDataResult data,FsLocationResult location){

        CheckType checkType = OutDoorV2Utils.pickPuhuoCheckType();
        if(checkType==null){
            ToastUtils.show(I18NHelper.getText("outdoor.puhuo.tip"));
            return null;
        }

        DownAccountData dad =null;
        if(data!=null && data.datas!=null&& data.datas.size()>0){
            dad = data.datas.get(0);
        }
        if(dad==null){
            ToastUtils.show(I18NHelper.getText("bi.ui.BiOpportunityListAct.2124")/* 缺少必要参数 */);
            return null;
        }
        FsLocationResult loc = new FsLocationResult(dad.lat,dad.lon);
        float distance1 = FsMapUtils.calculateLineDistance(loc, location);
        return createCheckinsInfo(checkType,dad,data,distance1);
    }



    public static void checkType2Checkinsinfo(CheckType checkType,  CheckinsInfo checkinsInfo){
        if(checkType!=null){
            if (checkType.chekinInfoData!=null){
                if(!TextUtils.isEmpty(checkType.chekinInfoData.checkInTime)){
                    checkinsInfo.isCheckin = 1;
                }

                if(!TextUtils.isEmpty(checkType.chekinInfoData.checkOutTime)){
                    checkinsInfo.isCheckOut = 1;
                }

                checkinsInfo.isCheckinFinish= CheckType.isActionStop(checkType)?1:0;

            }

            IOutDoorv2Task task = OutDoorV2OffLineManager.getInstance().getTaskById(checkType.indexId, OutDoorV2Constants.SIGNOUT_KEY);
            if (task!=null){
                checkinsInfo.isCheckOut = 1;
            }
//            checkType.is

            checkinsInfo.customerActionSimples = new ArrayList<>();
            if (checkType.actionList!=null&&checkType.actionList.size()>0){
                for (CustomerAction ca :checkType.actionList){
                    CustomerActionSimple cas = new CustomerActionSimple();
                    cas.actionId = ca.actionId;
                    cas.actionName = ca.actionName;
                    cas.sourceActionId = ca.sourceActionId;
                    cas.isRequired = ca.isRequired;
                    cas.dataStatus = ca.dataStatus;
                    cas.sourceActionId=ca.sourceActionId;
//                    cas.iconPath = ca.actionCode;
                    checkinsInfo.customerActionSimples.add(cas);
                }
            }
        }
    }


//    public static boolean isOpenOffline(){
//        return (OutDoorHomeStyleSetUtils.getRuleHomeStyleResultBykey(OutDoorHomeStyleSetUtils.kkIsPUHUOOffline) == 1)
//                &&
//                OutDoorV2Constants.getKKisOffline()
//                ;
//    }
    public static boolean isOpenOffline(Context context){
        return (OutDoorHomeStyleSetUtils.getRuleHomeStyleResultBykey(OutDoorHomeStyleSetUtils.kkIsPUHUOOffline) == 1)
                &&
                OutDoorV2Constants.getKKisOffline(context)
                ;
    }

    /**
     * 0 是普通企业    1 是今麦郎企业进来需要调接口
     * 如果是今麦郎企业,那么默认拉取接口
     * 否则默认不拉取接口
     * @return
     */
    public static boolean isJMLOffline(){
        return (OutDoorHomeStyleSetUtils.getRuleHomeStyleResultBykey(OutDoorHomeStyleSetUtils.kkIsJMLPUHUOOffline) == 1);
    }
    public static boolean isJMLOffline(Context context){
        return (OutDoorHomeStyleSetUtils.getRuleHomeStyleResultBykey(OutDoorHomeStyleSetUtils.kkIsJMLPUHUOOffline) == 1)
                &&OutDoorV2Constants.getKKisOffline(context);
    }

    public static IdAndNameEx getPuhuoScene(){
        GetEmployeeRuleResult result = OutDoor2CacheManger.getCacheRule();
        if(result!=null){
            IdAndNameEx ex = result.getSceneByid(IdAndNameEx.PUHUO);
            if (ex!=null){
                return ex;
            }
        }
        return IdAndNameEx.PuHuoIdAndName;
    }

}
