package com.facishare.fs.biz_function.subbiz_outdoorsignin.utils;

import android.content.Context;
import android.text.TextUtils;

import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.i18n.I18NHelper;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.location.impl.FsMultiLocationManager;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.TimeZone;

import static com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.outdoor_offline.OfflineUtils.OUTDOOR_OFFLINE;

public class TimeZoneUtils {

    private static String TAG = TimeZoneUtils.class.getSimpleName();

    /**
     * 默认是服务器设置的时区
     * 如果没有设置, 那么默认使用手机时区
     * @param pattern
     * @return
     */
    public static SimpleDateFormat getDateFormat(String pattern){
        String timeZone = FSContextManager.getCurUserContext().getAccount().getTimeZone(FsMultiLocationManager.getAppContext());
        FCLog.i(OUTDOOR_OFFLINE,TAG,"getDateFormat timezone:"+ timeZone);
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        if(!TextUtils.isEmpty(timeZone)){
            sdf.setTimeZone(TimeZone.getTimeZone(timeZone));
        }
        return sdf;
    }

    /**
     * 如果设置时区了 那么按照服务器时区
     * 如果没有设置时区,那么默认按照东八区 来显示
     * @param pattern
     * @return
     */
    public static SimpleDateFormat getDateFormatGMT8(String pattern){
        String timeZone = FSContextManager.getCurUserContext().getAccount().getTimeZone(FsMultiLocationManager.getAppContext());
        FCLog.i(OUTDOOR_OFFLINE,TAG,"getDateFormatGMT8 timezone:"+ timeZone);
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        if(!TextUtils.isEmpty(timeZone)){
            sdf.setTimeZone(TimeZone.getTimeZone(timeZone));
        }else{
            sdf.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
        }
        return sdf;
    }

    public static SimpleDateFormat getDateFormatTimeZone(){//相机拍照使用,格式化时区
        String timeZone = FSContextManager.getCurUserContext().getAccount().getTimeZone(FsMultiLocationManager.getAppContext());
        FCLog.i(OUTDOOR_OFFLINE,TAG,"getDateFormatTimeZone timezone:"+ timeZone);
        SimpleDateFormat sdf = new SimpleDateFormat();
        if(!TextUtils.isEmpty(timeZone)){
            sdf.applyPattern(I18NHelper.getText("jsapi.image.upload.date_time_format_timezone")/* yyyy年MM月dd日  E HH:mm z*/);
            sdf.setTimeZone(TimeZone.getTimeZone(timeZone));
        }else{
            sdf.applyPattern(I18NHelper.getText("jsapi.image.upload.date_time_format")/* yyyy年MM月dd日  E HH:mm */);
            sdf.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
        }
        return sdf;
    }

    /**
     *
     * Calendar.getInstance()
     *
     * 统一改为 设置时区的接口
     *
     * @return
     */
    public static Calendar getCalendarTimeZone(){
        String timeZone = FSContextManager.getCurUserContext().getAccount().getTimeZone(FsMultiLocationManager.getAppContext());
        FCLog.i(OUTDOOR_OFFLINE,TAG,"getDateFormat timezone:"+ timeZone);
        if(!TextUtils.isEmpty(timeZone)){
            return Calendar.getInstance(TimeZone.getTimeZone(timeZone));
        }else{
            return Calendar.getInstance();
        }
    }
}
