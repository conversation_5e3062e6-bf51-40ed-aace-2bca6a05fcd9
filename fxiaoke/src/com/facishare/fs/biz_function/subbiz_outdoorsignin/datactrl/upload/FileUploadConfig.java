package com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.upload;

import android.text.TextUtils;

import com.facishare.fs.context.FSContextManager;
import com.fs.fshttp.CookiesManager;
import com.fxiaoke.fshttp.web.http.WebApiUtils;

import java.io.Serializable;

/**
 * extension	Head	String	必须	存在企业上传类型白名单	文件扩展名 不带 . 如 jpg png xlsx docx zip 等
 * Content-Length	Head	Integer	必须	1-104857600	单位：字节 支持上传的文件大小 1字节-100M (大于5MB建议使用分片上传)
 * business	Query	String	否(默认unknown)	各自业务类型,如Feed,qixin…	不传递业务类型，在做数据迁移或文件GC时会误杀。
 * originFileName	Query	String	否(建议传递)		文件名，下载或展示时显示原始文件名。
 * needThumbnail	Query	Boolean	否(默认为false)	false,true,1(后续会取缔)	是否生成默认尺寸的缩略图 （注：极不推荐使用，会导致水印参数无效，如果需要使用图片缩略图，推荐使用 图片统一接入，想要多大的缩略图都可以动态请求生成。
 * needCdn	Query	Boolean	否(默认为false)	false,true	返回Cpath 可享受CDN加速，可通过CDN地址访问（仅文件类型为图片类型时生效) ,生成Cpath 该Cpath本身不具有任何隐私特性拿到该Cpath访问链接任意人员可访问（慎用）cpath使用
 * expireDay	Query	Integer	否（默认为3)	3—30（超出30会重置为30） 单位:天	临时文件过期时间 注意：文件专属企业文件过期时间一般最大为7天（由客户存储策略控制）
 * words	Query	String	否（默认为空)	可多行,换行使用英文逗号分隔 示例：fxiaoke,andy,20240220-23:08	图片水印（保存源文件时即打上水印，后续临时转正式同样会携带该水印字样）
 */
public class FileUploadConfig implements Serializable {
   private String extension;
   private String resourceType;
   private String business;
   private int expireDay = 3;
   private String filePath;
   private String filename;
   private String appid;
   private Object cookie;

   public FileUploadConfig(Builder builder){
      this.extension = builder.extension;
      this.resourceType = builder.resourceType;
      this.filePath = builder.filePath;
      this.expireDay = builder.expireDay;
      this.business = builder.business;
      this.filename = builder.filename;
      this.appid = builder.appid;
      this.cookie = builder.cookie;
   }

   public String toString(){
      StringBuilder sb = new StringBuilder();
      sb.append("?");
      sb.append("business="+business);
      sb.append("&");
      sb.append("expireDay="+expireDay);
      if (!TextUtils.isEmpty(appid)){
         sb.append("&");
         sb.append("appid="+appid);
      }
      sb.append("&");
      sb.append("traceId="+WebApiUtils.getTraceId(false));
      String result = sb.toString();
      return result;
   }

   public Object getCookie() {
      return cookie;
   }

   public String getAppid() {
      return appid;
   }

   public String getFilename() {
      return filename;
   }

   public String getExtension() {
      return extension;
   }

   public String getResourceType() {
      return resourceType;
   }

   public String getBusiness() {
      return business;
   }

   public int getExpireDay() {
      return expireDay;
   }

   public String getFilePath() {
      return filePath;
   }

   public static final class Builder {
      private String extension;
      private String resourceType;
      private String business;
      private int expireDay = 3;
      private String filePath;
      private String filename;
      private String appid;
      private Object cookie;

      public Builder setCookie(Object cookie) {
         this.cookie = cookie;
         return this;
      }

      public Builder setResourceType(String resourceType) {
         this.resourceType = resourceType;
         return this;
      }

      public Builder setAppid(String appid) {
         this.appid = appid;
         return this;
      }

      public Builder setFilename(String filename) {
         this.filename = filename;
         return this;
      }

      public Builder setExtension(String extension) {
         this.extension = extension;
         return this;
      }
      public Builder setBusiness(String business) {
         this.business = business;
         return this;
      }
      public Builder setExpireDay(int expireDay) {
         this.expireDay = expireDay;
         return this;
      }
      public Builder setFilePath(String filePath) {
         this.filePath = filePath;
         return this;
      }

      public FileUploadConfig builder(){
         return new FileUploadConfig(this);
      }
   }
}
