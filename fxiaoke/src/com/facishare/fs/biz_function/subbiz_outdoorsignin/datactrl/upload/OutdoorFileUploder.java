package com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.upload;

import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.core.http.HttpCore;
import com.core.http.config.InitConfig;
import com.facishare.fs.biz_feed.newfeed.cmpt.Text;
import com.facishare.fs.biz_feed.utils.FeedSP;
import com.facishare.fs.js.utils.OutdoorLog;
import com.facishare.fs.pluginapi.common_beans.Attach;
import com.facishare.fs.utils_fs.ToastUtils;
import com.fs.fshttp.CookiesManager;
import com.fxiaoke.fshttp.web.http.OkHttpCookieItem;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.fxiaoke.fxlog.FCLog;
import com.lidroid.xutils.HttpUtils;
import com.lidroid.xutils.exception.HttpException;
import com.lidroid.xutils.http.RequestParams;
import com.lidroid.xutils.http.ResponseInfo;
import com.lidroid.xutils.http.callback.RequestCallBack;
import com.lidroid.xutils.http.client.HttpRequest;
import com.lzy.okgo.model.HttpHeaders;
import com.lzy.okgo.model.HttpParams;

import org.apache.http.entity.BasicHttpEntity;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Cookie;
import okhttp3.FormBody;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class OutdoorFileUploder implements IOutdoorFileUploader{

    public static List<Cookie> cookies = null;

    private static final String URL = "/FSC/EM/File/UploadByStream";
    String TAG = OutdoorFileUploder.class.getSimpleName();
    @Override
    public void upload(Attach attach, IUploadListener listener) {
        final File file = new File(attach.attachLocalPath);
        String business = "Feed.WQ";//attach.tag;
        String fname = file.getName();
        if (!TextUtils.isEmpty(fname)){
            String extension = fname.substring(fname.lastIndexOf(".")+1);
            boolean isN = FeedSP.getType("isUploadNpath_key",0)==1;
            FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"isUploadNpath_key isN = "+isN);
            FileUploadConfig config = new FileUploadConfig.Builder()
                    .setBusiness(business)
                    .setResourceType(isN?"N":"TN")
                    .setExpireDay(7)
                    .setExtension(extension)
                    .setAppid(attach.appid)
                    .setCookie(attach.cookie)
                    .builder();
            uploadOkHttp(config,file,listener);
        }

    }

    public void uploadOkHttp(final FileUploadConfig config, File file,IUploadListener listener){
//        OkHttpClient mClient = null;
//        if (cookies!=null){
//            CookiesManager cookiesManager = new CookiesManager();
//            cookiesManager.setSandboxCookies(cookies);
//            mClient = new OkHttpClient.Builder()
//                    .cookieJar(cookiesManager)
//                    .build();
//        }else {
//            mClient = HttpCore.getInstance().getClient();
//        }
        OkHttpClient mClient = null;
        InitConfig cc = HttpCore.getInstance().mDefaultInitConfig.cloneConfig();
        if (config.getCookie()!=null){
            String cjson = (String) config.getCookie();
            List<Cookie> objList = parseCookies(cjson);
            cc.setSandBoxCookies(objList);

            mClient  = HttpCore.getInstance().cloneClient(cc);
            FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"cookies  = true");
            FCLog.d(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"cookies  = "+cjson);
        }else {
            mClient = HttpCore.getInstance().getClient();
        }

        String url = WebApiUtils.getFSCHost(WebApiUtils
                .requestUrl) + URL+config.toString();

        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"uploadOkHttp url = "+url);
        RequestBody requestBody = RequestBody.create(HttpParams.MEDIA_TYPE_STREAM, file);
        String length = "-1";
        try {
            length = String.valueOf(requestBody.contentLength());
        }catch (Exception e){
            FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"uploadOkHttp Exception = "+e.getMessage());
        }

        if (TextUtils.isEmpty(length)||length.equals("-1")){
            fail(file,"file length is "+length,listener);
            return;
        }
        Request request = new Request.Builder()
                .url(url)
                .addHeader("extension", config.getExtension())
                .addHeader(HttpHeaders.HEAD_KEY_CONTENT_LENGTH,length)
                .addHeader(HttpHeaders.HEAD_KEY_RESOURCE_TYPE,config.getResourceType())
                .post(requestBody)
                .build();
        mClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"onFailure->"+e.getMessage());
                fail(file,"onFailure["+e.getMessage()+"]",listener);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"onResponse success->"+response.isSuccessful());
                if(response.isSuccessful()){
                    String json = response.body().string();
                    FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,TAG,"onResponse success->"+json);
                    JSONObject jo = JSONObject.parseObject(json);
                    String tempFile = jo.getString("TempFileName");
                    listener.onSuccess(file,tempFile);
                }else{
                    fail(file,"onResponse["+response.code()+"]",listener);
                }
            }
        });

    }

    private void fail( File file,String msg ,IUploadListener listener){
        if(file.exists()){
            listener.onFail(msg, IOutdoorFileUploader.ERROR_CODE_OTHER);
        }else{
            listener.onFail("image is null ["+msg+"]",IOutdoorFileUploader.ERROR_CODE_FILE_NO_EXTIE);
        }
    }

    public static String cookiesToString(List<Cookie> list){
        ArrayList<OkHttpCookieItem> saveList = new ArrayList<>();
        for (okhttp3.Cookie item : list) {
            OkHttpCookieItem cookie = OkHttpCookieItem.trans2OkHttpCookieItem(item);
            saveList.add(cookie);
        }
        String cookies = JSON.toJSONString(saveList);

        return cookies;
    }

    private static List<Cookie> parseCookies(String cookieString) {

        List<okhttp3.Cookie> retList = null;
        try {
            List<OkHttpCookieItem> saveList = JSON.parseArray(cookieString, OkHttpCookieItem.class);
            if (saveList != null && !saveList.isEmpty()) {
                retList = new ArrayList<>();
                for (OkHttpCookieItem item : saveList) {
                    okhttp3.Cookie cookie = OkHttpCookieItem.trans2okttp3Cookie(item);
//                    logokhttp3Cookie(cookie, "getSandboxCookie");
                    retList.add(cookie);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return retList;
    }
}
