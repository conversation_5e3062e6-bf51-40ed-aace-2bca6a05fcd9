package com.facishare.fs.biz_function.subbiz_outdoorsignin.scan;

import android.Manifest;
import android.app.Activity;
import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.device.ScanManager;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Rect;
import android.media.AudioManager;
import android.media.SoundPool;
import android.os.Build;
import android.os.Bundle;

import android.os.Handler;
import android.os.Vibrator;
import android.text.TextUtils;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;

import android.view.WindowManager;

import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alipay.android.phone.scancode.export.adapter.MPRecognizeType;
import com.alipay.android.phone.scancode.export.adapter.MPScanError;
import com.alipay.android.phone.scancode.export.adapter.MPScanResult;
import com.alipay.android.phone.scancode.export.adapter.MPScanner;
import com.alipay.android.phone.scancode.export.listener.MPImageGrayListener;
import com.alipay.android.phone.scancode.export.listener.MPScanListener;
import com.custom.Utils;

import com.custom.outdoor.widget.APTextureView;
import com.custom.outdoor.widget.ScanView;
import com.facishare.fs.App;
import com.facishare.fs.BaseActivity;

import com.facishare.fs.biz_function.subbiz_outdoorsignin.SendOutdoorSigninActivity;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.AnalysisResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.printer.PrinterUtils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.api.WaiqinServiceV2;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.AnalysisResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CodeConfigResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scan.bean.PrintBean;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scan.cache.SnCacheManager;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scan.pda.OEMSymbologyId;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scan.pda.PdaBean;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scan.pda.PdaManager;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scan.processor.AbsScanProcessor;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scan.processor.IScanProcessor;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scan.processor.ProcessorFactory;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scan.processor.ScanOrderProcessor;

import com.facishare.fs.biz_function.subbiz_outdoorsignin.scan.processor.SnResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scene.BarCodeControl;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scene.BarCodeScanActivityAliyun;
import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.common_utils.permission.GrantedExecuter;
import com.facishare.fs.dialogs.CommonDialog;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.js.handler.util.UtilOpenActionHandler;
import com.facishare.fs.metadata.actions.EscPrintAction;
import com.facishare.fs.metadata.actions.EscPrintContext;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.IPicService;

import com.facishare.fs.pluginapi.pic.bean.ImageBean;
import com.facishare.fs.qr.QrCodeScanActivity;
import com.facishare.fs.qr.QrImgUtils;
import com.facishare.fslib.R;
import com.fxiaoke.fscommon.util.ImmerseLayoutUtil;
import com.fxiaoke.fscommon.util.TickUtils;
import com.fxiaoke.fscommon.util.WeexIntentUtils;
import com.fxiaoke.fscommon_res.avatar.AvaOpener;
import com.fxiaoke.fscommon_res.common_view.CommonTitleView;
import com.fxiaoke.fscommon_res.permission.PermissionExecuter;

import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fxlog.DebugEvent;
import com.fxiaoke.fxlog.FCLog;
import com.google.android.material.tabs.TabLayout;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import de.greenrobot.event.EventBus;
import de.greenrobot.event.core.MainSubscriber;


public class OutdoorScanBarActivity extends BaseActivity implements IScanView{
    PrinterUtils printerUtils ;
    public static Intent createIntent(Context cxt) {
        Intent intent = new Intent(cxt, OutdoorScanBarActivity.class);
        return intent;
    }

    public static void startInMock(Activity cxt,SnResult result){
        Intent intent = new Intent(cxt, OutdoorScanBarActivity.class);
        HashMap<String,Object> param = new HashMap<String,Object> ();


        param.put("recordType",IScanProcessor.ScanRecordType_Add);


        param.put("selectDatas",result);


        HashMap<String,Object> info = new HashMap<String,Object> ();
            info.put("apiName","");
            info.put("dataId","64a4e9ee351a8f0001adf213");
            info.put("objectData",new HashMap<>());
        param.put("info",info);
        intent.putExtra("scan_param",param);
        cxt.startActivityForResult(intent,1001);
    }


    public static void startOutMock(Activity cxt, SnResult result){
        Intent intent = new Intent(cxt, OutdoorScanBarActivity.class);
        HashMap<String,Object> param = new HashMap<String,Object> ();


        param.put("recordType",IScanProcessor.ScanRecordType_Order);


        param.put("selectDatas",result);


        HashMap<String,Object> info = new HashMap<String,Object> ();
        info.put("apiName","");
        info.put("dataId","64a56796f7200b00018880d7");
        info.put("objectData",new HashMap<>());
        param.put("info",info);
        intent.putExtra("scan_param",param);
        cxt.startActivityForResult(intent,1001);
    }

    //扫描条码服务广播
    //Scanning barcode service broadcast.
    public static final String SCN_CUST_ACTION_SCODE = "com.android.server.scannerservice.broadcast";

    public static final String SCANER_ACTION_CODE = "com.scanner.broadcast";
    //条码扫描数据广播
    //Barcode scanning data broadcast.
    public static final String SCN_CUST_EX_SCODE = "scannerdata";

    public static final DebugEvent TAG = new DebugEvent(OutdoorScanBarActivity.class.getSimpleName());

    private static final int REQUEST_CODE_PERMISSION = 1;

    private static final int REQUEST_CODE_PHOTO = 2;

    public static final int DIALOG_WAITING_CREATE_SCAN_BAR = 101;
    private int REQUEST_CODE_SELECT_PIC = 1001;
    private CommonTitleView mCommonTitleView;

    private ImageView mTorchBtn;
    private APTextureView mTextureView;
    private ScanView mScanView;
    private boolean isFirstStart = true;
    private Rect scanRect;
    private MPScanner mpScanner;
    private boolean isScanning;
    private boolean isPaused;
    private IScanProcessor processor = null;

    protected Handler mHandler = new Handler();


    private boolean isPDA = false;

    private int soundID;
    private int soundErrorID;
    private int soundGroup;
    private SoundPool mSoundPoll = new SoundPool(100, AudioManager.STREAM_MUSIC, 0);
    private Vibrator vibrator ;
    private static final long VIBRATE_DURATION = 200L;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_bar_code_scan_new);
        vibrator = (Vibrator) getSystemService(VIBRATOR_SERVICE);
        isPDA = PdaManager.isPda();
//        PdaManager.isOpenScanPage=true;
//        ImmerseLayoutUtil.setImmerseTitleView(this, com.zbar.lib.R.id.title);
        soundID = mSoundPoll.load(this, R.raw.scan_right_1, 0);
        soundErrorID = mSoundPoll.load(this, R.raw.scan_error_1, 0);
        soundGroup = mSoundPoll.load(this, R.raw.scan_group_1, 0);

        processor = ProcessorFactory.create(this);
//        initData();
        initTitle();
        initAliyunView(savedInstanceState);
//        dealView();

        disableSwipeBack();
        ScanUtils.readConfig();
        EventBus.getDefault().register(scanEvent);
    }

    @Override
    protected void onStart() {
        super.onStart();
        if (PdaManager.isPda()){
            printerUtils = new PrinterUtils(OutdoorScanBarActivity.this);
        }
    }

    public void initTitle() {
        super.initTitleCommon();
        mCommonTitleView = this.findViewById(R.id.title);
        mCommonTitleView.setMiddleText(processor.getTitle());//"wq.outdoorv2_step_base_activity.text.quit"
        mCommonTitleView.addLeftAction( I18NHelper.getText("commonfunc.dialog_fragment_fsmail_choose_attachment_menu.text.cancel")/* 取消 */, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                close();
            }
        });

        if(!isPDA && processor.getScanRecordType() == IScanProcessor.ScanRecordType_OnlyScan){
            TextView view = mCommonTitleView.addRightAction(I18NHelper.getText("crm.old.CrmDetailAttachMoreOpsCtrl.1786")/* 相册 */, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    IPicService picService = HostInterfaceManager.getIPicService();
                    picService.selectLocalPic(OutdoorScanBarActivity.this, null, 1, I18NHelper.getText("av.common.string.confirm")/* 确定 */, false, REQUEST_CODE_SELECT_PIC);
                }
            });
        }

    }

    public void printTag(Map<String, Object> data){
//        Map<String, Object> parmas = new HashMap<>();
//
//        parmas.put("apiName", "CRBIAOQIAN");
//        parmas.put("dataId", "000");
//        parmas.put("title","此托");
//        parmas.put("context","已入库");
//        parmas.put("width",60);
//        parmas.put("height",40);
//
//        if(data!=null){
//            parmas.putAll(data);
//        }
        //ava://checkin-hera/checkin-hera/pages/print-label/index
        AvaOpener.getInstance().openAvaPage(this,"ava://checkin-hera/checkin-hera/pages/print-label/index",null);
//          printerUtils.btnPrint(parmas);
//        WeexIntentUtils.startWeexAct(this,"bundle://metadata/detail/BleEscPrintPage",parmas);
    }

    @Override
    public void autoPrint(Map<String, Object> data) {
        ToastUtils.show(I18NHelper.getText("wq.scancode.text.scan_autoprint")/* 自动打印 */);
        Map<String, Object> parmas = new HashMap<>();

        parmas.put("apiName", "CRBIAOQIAN");
        parmas.put("dataId", "000");
        parmas.put("title",I18NHelper.getText("wq.scancode.text.tip4")/* 此托 */);
        parmas.put("context",I18NHelper.getText("wq.scancode.text.scan_received")/* 已入库 */);
        parmas.put("width",60);
        parmas.put("height",40);

        if(data!=null){
            parmas.putAll(data);
        }
        if(printerUtils!=null){
            printerUtils.btnPrint(parmas);
        }
    }


    public void close() {
        if (processor.isHasNewData()) {
            processor.showStorageTip();
        } else {
            finish();
        }
    }
    public void finish() {
        super.finish();
        processor.clickCancel();
    }

    @Override
    public void onBackPressed() {
        if (processor.isHasNewData()) {
            processor.showStorageTip();
            return;
        }

        super.onBackPressed();
    }

    @Override
    protected void onResume() {
        super.onResume();
        processor.onResume();
        if(!isScanning && !isFirstStart){
            startScan();
        }

    }

    @Override
    protected void onPause() {
        super.onPause();
        if (isScanning) {
            stopScan();
            if(mpScanner!=null){
                mpScanner.closeCameraAndStopScan();
            }
        }

    }


    @Override
    protected void onStop() {
        super.onStop();
        if(printerUtils!=null){
            printerUtils.onStop();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        processor.onDestroy();
        try{
            if(mpScanner != null){
                mpScanner.release();
            }
//            printerUtils.onDestroy();
        }catch (Exception e){

        }
//        PdaManager.isOpenScanPage=false;
        EventBus.getDefault().unregister(scanEvent);

    }

    @Override
    protected Dialog onCreateDialog(int id) {
        switch (id) {
            case DIALOG_WAITING_CREATE_SCAN_BAR: {
                return createLoadingProDialog(I18NHelper.getText("wq.action.text.distinguishing")/* 识别中 */);
            }
        }
        return super.onCreateDialog(id);
    }


    public void showStorageTip() {
        CommonDialog.showDialog(this, I18NHelper.getText("ava.checkin_hera.code_back_tip")/* 已有新扫描的产品待入库,确定要放弃此次操作? */, "", I18NHelper.getText("av.common.string.confirm")/* 确定 */,
                I18NHelper.getText("wx.crm.goal_obj_complete_board.5"/*取消*/),
                true,
                false,
                true,
                CommonDialog.FLAG_TwoButton,
                new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        processor.clickCancel();
                        finish();
                    }
                }, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                    }
                });

    }



    protected void initAliyunView(Bundle savedInstanceState) {
        if(!isPDA){
            if (!PermissionExecuter.hasAllPermissions(this, new String[]{Manifest.permission.CAMERA, Manifest.permission
                    .WRITE_EXTERNAL_STORAGE})) {
                String requestPermissionsTip = I18NHelper.getText("qx.qr_scan.request_camera_permission.tip", "请开启纷享销客的相机和存储权限，以便使用扫描二维码功能");

                new PermissionExecuter().requestPermissionsWithBusinessMessage(this, new String[]{Manifest.permission.CAMERA, Manifest.permission
                                .WRITE_EXTERNAL_STORAGE}, true,
                        new GrantedExecuter() {

                            @Override
                            public void exe() {
                                initAliyunView(savedInstanceState);
                            }
                        },requestPermissionsTip,     I18NHelper.getText("crm.Mp2Activity.open_camera_storage_permission_title"));
                return;
            }
        }

        mTextureView = findViewById(R.id.surface_view);
        mScanView = findViewById(R.id.scan_view);
//        mScanView.hideScanTitle();
        mTorchBtn = findViewById(com.zbar.lib.R.id.torch);
        mTorchBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                switchTorch();
            }
        });

        View cameraPreview = findViewById(R.id.cameraPreview);
        View padLayout = findViewById(R.id.padLayout);

        if (isPDA)
        {
//            padLayout.setVisibility(View.VISIBLE);
            cameraPreview.setBackgroundColor(Color.parseColor("#F2F3F5"));
        }else{
            padLayout.setVisibility(View.GONE);
            initMPScanner();
            startScan();
        }
    }


    private void initMPScanner() {
        mpScanner = new MPScanner(this);
        mpScanner.setRecognizeType(
                MPRecognizeType.QR_CODE,
                MPRecognizeType.BAR_CODE,
                MPRecognizeType.DM_CODE,
                MPRecognizeType.PDF417_CODE
        );

        mpScanner.setMPScanListener(new MPScanListener() {
            @Override
            public void onConfiguration() {
                mpScanner.setDisplayView(mTextureView);
            }

            @Override
            public void onStart() {
                if (!isPaused) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (!isFinishing()) {
                                initScanRect();
                                mScanView.onStartScan();
                            }
                        }
                    });
                }
            }

            @Override
            public void onSuccess(List<MPScanResult> list) {
                MPScanResult mpScanResult = list.get(0);
                mpScanner.beep();
                mpScanner.startScan();
                final String result_str = mpScanResult.getText();
                FCLog.e("qrscan-aliyun", "result: " + result_str);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
//                        Utils.toast(OutdoorScanBarActivity.this, result_str);
                        processor.handleCode(mpScanResult.getMPRecognizeType(),result_str);
                    }
                });
            }

            @Override
            public void onError(MPScanError mpScanError) {
                if (!isPaused) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            Utils.toast(OutdoorScanBarActivity.this, I18NHelper.getText("av.logic.device.open_camera_failed_tips"));
                        }
                    });
                }
            }
        });
        mpScanner.setMPImageGrayListener(new MPImageGrayListener() {
            @Override
            public void onGetImageGray(int gray) {
                // 注意：该回调在昏暗环境下可能会连续多次执行
                if (gray < MPImageGrayListener.LOW_IMAGE_GRAY) {
//                    runOnUiThread(new Runnable() {
//                        @Override
//                        public void run() {
//                            Utils.toast(OutdoorScanBarActivity.this, "光线太暗，请打开手电筒");
//                        }
//                    });
                }
            }
        });
    }

    private void pickImageFromGallery() {
        TickUtils.toolsBaseTick("qrscan_choosefromgallery");
        IPicService picService = HostInterfaceManager.getIPicService();
        picService.selectLocalPic(OutdoorScanBarActivity.this, null, 1, I18NHelper.getText("av.common.string.confirm")/* 确定 */, false, REQUEST_CODE_PHOTO);

    }

    private void switchTorch() {
        boolean torchOn = mpScanner.switchTorch();
        mTorchBtn.setSelected(torchOn);
    }


    private void scanFromUri(Bitmap bitmap) {
        if (bitmap == null) {
            notifyScanResult(true, null);
            finish();
        } else {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    MPScanResult mpScanResult = mpScanner.scanFromBitmap(bitmap);
                    mpScanner.beep();
                    onScanSuccess(mpScanResult);
                }
            }, "scanFromUri").start();
        }
    }

    private void onScanSuccess(final MPScanResult result) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (result == null) {
                    notifyScanResult(true, null);
                } else {
                    processor.handleCode(result.getMPRecognizeType(),result.getText());
                }
            }
        });
    }

    public void startScan() {
        try {
            if(mpScanner!=null){
                mpScanner.startScan();
                mpScanner.openCameraAndStartScan();
                isScanning = true;
            }

        } catch (Exception e) {
            isScanning = false;
        }
    }

    public void stopScan() {
        if (mpScanner!=null){
            mpScanner.stopScan();
//            mpScanner.closeCameraAndStopScan();
//            mScanView.onStopScan();
            isScanning = false;
        }

        if (isFirstStart) {
            isFirstStart = false;
        }
    }

    private void initScanRect() {
        if (scanRect == null) {
            scanRect = mScanView.getScanRect(
                    mpScanner.getCamera(), mTextureView.getWidth(), mTextureView.getHeight());

            float cropWidth = mScanView.getCropWidth();
            if (cropWidth > 0) {
                // 预览放大 ＝ 屏幕宽 ／ 裁剪框宽
                WindowManager wm = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
                float screenWith = wm.getDefaultDisplay().getWidth();
                float screenHeight = wm.getDefaultDisplay().getHeight();
                float previewScale = screenWith / cropWidth;
                if (previewScale < 1.0f) {
                    previewScale = 1.0f;
                }
                if (previewScale > 1.5f) {
                    previewScale = 1.5f;
                }
                Matrix transform = new Matrix();
                transform.setScale(previewScale, previewScale, screenWith / 2, screenHeight / 2);
                mTextureView.setTransform(transform);
            }
        }
        mpScanner.setScanRegion(scanRect);
    }

    private void notifyScanResult(boolean isProcessed, Intent resultData) {
//        ScanHelper.getInstance().notifyScanResult(isProcessed, resultData);
        BarCodeControl.ToastNotice(this, I18NHelper.getText("wq.scancode.text.no_identified")/* 未识别到数据 */);
        play(Sound_Error);
    }


    @Override
    public Activity getActivity() {
        return this;
    }

    @Override
    public void reStartScan() {
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                try{
                    stopScan();
                    startScan();
                }catch (Exception e){
                    FCLog.i("qrscan-aliyun", "onRescan"+ Log.getStackTraceString(e));
                }
            }
        },100);


    }

    @Override
    public IScanProcessor getIScanProcessor() {
        return processor;
    }


    @Override
    public void play(int type){
        switch (type){
            case Sound_Right:{//正常
                mSoundPoll.play(soundID, 0.3f, 0.3f, 0, 0, 1);
                break;
            }
            case Sound_Error:{//错误
                mSoundPoll.play(soundErrorID, 0.3f, 0.3f, 0, 0, 1);
                break;
            }
            case Sound_Group:{//组托
                mSoundPoll.play(soundGroup, 0.3f, 0.3f, 0, 0, 1);
                break;
            }
        }

    }
    protected boolean enableHugeIntentStartAct(Intent intent) {
        return true;
    }


    public static class ScanEvent{
        public ScanEvent(String  code){
            this.code=code;
        }
        public String code;
        public Object type;
    }

    private MainSubscriber scanEvent = new MainSubscriber<ScanEvent>() {
        @Override
        public void onEventMainThread(ScanEvent scanData) {
            processor.handleCode(MPRecognizeType.QR_CODE,scanData.code);
        }
    };


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == PrinterUtils.BLUETOOTH_REQUEST_CODE){
            if(printerUtils!=null){
                printerUtils.onActivityResult(requestCode,resultCode,data);
            }
        }
        if (data == null) {
            return;
        }
        final List<ImageBean> images = data
                .getParcelableArrayListExtra(IPicService.KEY_IMAGES);
        if (images == null || images.size() == 0) {
            return;
        }
        String path = images.get(0).getPath();
        final Bitmap bitmap = QrImgUtils.scareBitmap2fit(path);

        if (requestCode == REQUEST_CODE_SELECT_PIC) {
            scanFromUri(bitmap);
        }
    }

    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_MOVE || ev.getAction() == MotionEvent.ACTION_UP) {
            View v = getCurrentFocus();
            if (v!= null && v instanceof EditText) {
                hideInput();
                v.clearFocus();
            }

        }
        return super.dispatchTouchEvent(ev);
    }
    public void vibrator(){
        if(vibrator!=null){
            vibrator.vibrate(VIBRATE_DURATION);
        }
    }
    public Handler getHandler(){
        return this.mHandler;
    }

}
