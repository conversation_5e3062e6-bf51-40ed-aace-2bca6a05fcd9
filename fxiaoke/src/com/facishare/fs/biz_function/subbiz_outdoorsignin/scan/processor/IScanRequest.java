package com.facishare.fs.biz_function.subbiz_outdoorsignin.scan.processor;

import android.view.View;

import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.AnalysisResult;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scan.IScanView;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scan.pda.PdaConfig;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.scene.BarCodeControl;
import com.facishare.fs.dialogs.CommonDialog;
import com.facishare.fs.i18n.I18NHelper;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;

import java.util.Date;
import java.util.List;

public interface IScanRequest <T> {



    String BarCodeType_SN = "SN";
    String BarCodeType_BARCODE = "BARCODE";
    String BarCodeType_SALES_BARCODE = "SALES_BARCODE";
    String BarCodeType_SALES_SN = "SALES_SN";
    String BarCodeType_DELIVERY_SN = "DELIVERY_SN";
    String BarCodeType_RECEIVED_SN = "RECEIVED_SN";
    String BarCodeType_SN_TO_GROUP = "SN_TO_GROUP"; //组托
    String BarCodeType_REQUISITION_SN = "REQUISITION_SN"; //装卸车

    String BarCodeType_STOCK_CHECK_SN = "STOCK_CHECK_SN"; // 盘点
    String BarCodeType_STORE_STOCK_CHECK_SN = "STORE_STOCK_CHECK_SN"; // 门店盘点
    String BarCodeType_RETURN_BACK_SN = "RETURN_BACK"; // 退换货
    String BarCodeType_PURCHASE_RETURN = "PURCHASE_RETURN"; // 采购退货
    String BarCodeType_NORMAL_OBJECT_SN = "NORMAL_OBJECT_SN"; // 采购退货

    void failed(WebApiFailureType failureType, int httpStatusCode, String error);
    boolean completed(Date time, T response) ;

    abstract class ScanRequest<T> implements IScanRequest<T>{

        IScanView scanView;

        public ScanRequest(IScanView scanView){
            this.scanView=scanView;
        }

        @Override
        public void failed(WebApiFailureType failureType, int errorCode, String error) {
            if(errorCode < 0){//本地错误码

            }else{

            }
            BarCodeControl.tastMsg(scanView.getIScanProcessor(), error);
            scanView.play(IScanView.Sound_Error);
            scanView.reStartScan();
        }
        @Override
        public boolean completed(Date time, T response) {
            if(response instanceof AnalysisResult){
                AnalysisResult result = (AnalysisResult) response;
                result.dataHandle();
                PdaConfig.configUnit(result);
                /**
                 *  //1001 弹窗提示
                 *  当前只对 1001
                 */
                if(result.bizCode==1001){
                    requestCompletd(result);
                    return false;
                }
                if(result.bizCode==1008){
                    requestCompletd(result);
                    return false;
                }
                if(result.bizCode==1007){
                    requestCompletd(result);
                    return true;
                }
            }
            return true;
        }
        public void requestCompletd(AnalysisResult response){

                if(response.bizCode==1008){
                    IScanProcessor processor = scanView.getIScanProcessor();
                    processor.handleAnalysisResult(response);
                }
                scanView.play(IScanView.Sound_Error);
                CommonDialog.showDialog(scanView.getActivity(), response.showTip(),
                        response.pickTitle(), I18NHelper.getText("qx.session.guide.dialog_btn_known")/* 知道了 */,
                        null, false,
                        false,
                        false,
                        CommonDialog.FLAG_SingleButton, new View.OnClickListener(){
                            @Override
                            public void onClick(View v) {
                                scanView.reStartScan();
                            }
                        }, null);
        }

    }

}
