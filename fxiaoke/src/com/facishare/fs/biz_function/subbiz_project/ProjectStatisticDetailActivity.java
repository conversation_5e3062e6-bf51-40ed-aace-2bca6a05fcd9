package com.facishare.fs.biz_function.subbiz_project;

import com.facishare.fs.i18n.I18NHelper;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import com.facishare.fs.BaseActivity;
import com.facishare.fslib.R;
import com.facishare.fs.biz_function.subbiz_project.bean.GetProjectDetailPersonResult;
import com.facishare.fs.biz_function.subbiz_project.bean.StatisticConfig;
import com.facishare.fs.biz_function.subbiz_project.datactrl.ProjectStatisticControl;
import com.fxiaoke.cmviews.viewpager.ViewPagerCtrl;
import com.facishare.fs.common_view.fragment.BaseListFragment;

/**
 * Created by hanlz on 2016/7/4.
 */
public class ProjectStatisticDetailActivity extends BaseActivity implements ProjectStatisticControl.IQuerySuccessListener, BaseListFragment.IRefreshListener {

    public static final String KEY_PROJECT_DATA = "key_project_data";

    private ViewPagerCtrl mPagerCtrl;

    private String mProjectId;
    private int mType;

    private ProjectStatisticControl mStatisticControl;
    private StatisticConfig mConfig;
    private ProjectStatisticFragment mTodoFragment;
    private ProjectStatisticFragment mDoingFragment;
    private ProjectStatisticFragment mCompleteFragment;
    private ProjectStatisticFragment mOverTimeFragment;

    public static Intent getIntent(Context context, StatisticConfig config) {
        Intent intent = new Intent(context, ProjectStatisticDetailActivity.class);
        if (config != null) {
            intent.putExtra(KEY_PROJECT_DATA, config);
        }
        return intent;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.project_statistic_detail_activity);
        getIntentArgs();
        initTitle();
        initView();
        initData();
    }

    private void getIntentArgs() {
        Intent intent = getIntent();
        if (intent != null) {
            mConfig = (StatisticConfig) intent.getSerializableExtra(KEY_PROJECT_DATA);
            if (mConfig != null) {
                mProjectId = mConfig.projectId;
                mType = mConfig.type;
            }
        }
    }

    private void initTitle() {
        initTitleCommon();
        mCommonTitleView.setMiddleText(I18NHelper.getText("wq.projectstatisticdetailactivity.text.statistical_details")/* 统计详情 */);
        mCommonTitleView.addLeftBackAction(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
    }

    private void initView() {
        mPagerCtrl = (ViewPagerCtrl) findViewById(R.id.pager);

        initPagerCtrl();
    }

    private void initPagerCtrl() {
        mPagerCtrl.setVisibility(View.VISIBLE);
        mPagerCtrl.init(this);

        StatisticConfig todoConfig = new StatisticConfig();
        todoConfig.type = StatisticConfig.TASK_TO_DO;
        mTodoFragment = ProjectStatisticFragment.getInstance(todoConfig);
        mTodoFragment.setRefreshListener(this);
        mPagerCtrl.addTab(0, I18NHelper.getText("xt.project_statistic_block.des.not_start")/* 未开始 */, mTodoFragment);

        StatisticConfig doingConfig = new StatisticConfig();
        doingConfig.type = StatisticConfig.TASK_DOING;
        mDoingFragment = ProjectStatisticFragment.getInstance(doingConfig);
        mDoingFragment.setRefreshListener(this);
        mPagerCtrl.addTab(1, I18NHelper.getText("meta.beans.InstanceState.3072")/* 进行中 */, mDoingFragment);

        StatisticConfig completeConfig = new StatisticConfig();
        completeConfig.type = StatisticConfig.TASK_COMPLETE;
        mCompleteFragment = ProjectStatisticFragment.getInstance(completeConfig);
        mCompleteFragment.setRefreshListener(this);
        mPagerCtrl.addTab(2, I18NHelper.getText("crm.layout.adapter_outdoor_record_item.8193")/* 已完成 */, mCompleteFragment);

        StatisticConfig overTimeConfig = new StatisticConfig();
        overTimeConfig.type = StatisticConfig.TASK_OVER_TIME;
        mOverTimeFragment = ProjectStatisticFragment.getInstance(overTimeConfig);
        mOverTimeFragment.setRefreshListener(this);
        mPagerCtrl.addTab(3, I18NHelper.getText("wq.projectstatisticdetailactivity.text.expired")/* 已过期 */, mOverTimeFragment);

        mPagerCtrl.commitTab();

        switch (mType) {
            case StatisticConfig.TASK_TO_DO:
                mPagerCtrl.setCurrentItem(0);
                break;
            case StatisticConfig.TASK_DOING:
                mPagerCtrl.setCurrentItem(1);
                break;
            case StatisticConfig.TASK_COMPLETE:
                mPagerCtrl.setCurrentItem(2);
                break;
            case StatisticConfig.TASK_OVER_TIME:
                mPagerCtrl.setCurrentItem(3);
                break;
            default:
                mPagerCtrl.setCurrentItem(0);
                break;
        }
    }

    private void initData() {
        mStatisticControl = new ProjectStatisticControl();
        mStatisticControl.setConfig(mConfig);
        mStatisticControl.setQueryListener(this);
        refresh();
    }

    @Override
    public <T> void onResult(T result) {
        endProgress();
        if (result instanceof GetProjectDetailPersonResult) {
            dealGetTaskList((GetProjectDetailPersonResult) result);
        }
    }

    private void dealGetTaskList(GetProjectDetailPersonResult result) {
        if (mTodoFragment != null) {
            mTodoFragment.refreshDataStopRefresh(result.todoList);
        }
        if (mDoingFragment != null) {
            mDoingFragment.refreshDataStopRefresh(result.doingList);
        }
        if (mCompleteFragment != null) {
            mCompleteFragment.refreshDataStopRefresh(result.completeList);
        }
        if (mOverTimeFragment != null) {
            mOverTimeFragment.refreshDataStopRefresh(result.overTimeList);
        }
    }

    @Override
    public void onFailed() {
        endProgress();
    }

    @Override
    public void refresh() {
        beginProgress();
        mStatisticControl.queryTasks();
    }

    private void beginProgress() {
        showDialog(BaseActivity.DIALOG_WAITING_BASE);
    }

    private void endProgress() {
        removeDialog(BaseActivity.DIALOG_WAITING_BASE);
    }
}
