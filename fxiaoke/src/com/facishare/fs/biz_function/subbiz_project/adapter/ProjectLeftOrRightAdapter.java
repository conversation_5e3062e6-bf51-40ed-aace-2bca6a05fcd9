package com.facishare.fs.biz_function.subbiz_project.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.facishare.fslib.R;
import com.facishare.fs.biz_function.subbiz_project.bean.ProjectConditionType;

import java.util.List;

/**
 * Created by changl on 2016/7/12.
 */
public class ProjectLeftOrRightAdapter extends BaseAdapter {


    Context mCtx;

    List<ProjectConditionType> mData;
    public ProjectLeftOrRightAdapter(Context ctx, List<ProjectConditionType> types){
        mCtx = ctx;
        mData = types;
    }

    @Override
    public int getCount() {
        return mData == null ? 1 : mData.size();
    }

    @Override
    public ProjectConditionType getItem(int i) {
        return mData == null ? null : mData.get(i);
    }

    @Override
    public long getItemId(int i) {
        return i;
    }

    @Override
    public View getView(int i, View view, ViewGroup viewGroup) {
        ViewHolder viewHolder;
        if (view == null){
            viewHolder = new ViewHolder();
            view = LayoutInflater.from(mCtx).inflate(R.layout.project_detail_condition_item,viewGroup,false);
            viewHolder.selected = (ImageView) view.findViewById(R.id.selected_iv);
            viewHolder.type = (TextView) view.findViewById(R.id.type_name);
            view.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) view.getTag();
        }

        viewHolder.refresh(getItem(i));

        return view;
    }

    class ViewHolder {
        ImageView selected;
        TextView type;

        public void refresh(ProjectConditionType conditionType){
            if (conditionType != null){
                if (conditionType.isSelected){
                    selected.setVisibility(View.VISIBLE);
                } else {
                    selected.setVisibility(View.INVISIBLE);
                }
                type.setText(conditionType.typeName);
            }
        }
    }
}
