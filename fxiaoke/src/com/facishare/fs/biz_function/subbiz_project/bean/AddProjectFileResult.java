/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_function.subbiz_project.bean;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * Created by changl on 2016/4/11.
 */
public class AddProjectFileResult implements Serializable {

    //
    @JSONField(name="M1")
    public int mStatus;
    //
    @JSONField(name="M2")
    public String mMessage;
    //
    @JSONField(name="M3")
    public long mServiceTime;
    //图片路径
    @JSONField(name="M10")
    public ArrayList<AddProjectFileInfo> mFileInfos;

    public AddProjectFileResult(){}

    @JSONCreator
    public AddProjectFileResult(
            @JSONField(name="M1")
            int status,
            @JSONField(name="M2")
            String message,
            @JSONField(name="M3")
            long serviceTime,
            @JSONField(name="M10")
            ArrayList<AddProjectFileInfo> mFileInfos) {
        this.mStatus = status;
        this.mMessage = message;
        this.mServiceTime = serviceTime;
        this.mFileInfos = mFileInfos;
    }
}
