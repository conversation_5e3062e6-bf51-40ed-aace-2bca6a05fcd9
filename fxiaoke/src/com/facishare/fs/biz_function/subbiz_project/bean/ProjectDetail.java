/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_function.subbiz_project.bean;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

public class ProjectDetail implements Serializable{
	//
	@JSO<PERSON>ield(name="M1")
	public String id;
	//
	@JSONField(name="M2")
	public String name;
	//
	@JSONField(name="M3")
	public String description;
	//
	@J<PERSON>NField(name="M4")
	public ProjectCategory category;
	//
	@J<PERSON>NField(name="M5")
	public String groupId;
	//
	@JSONField(name="M6")
	public List<EmployeeInfo> admins;
	//
	@JSONField(name="M7")
	public List<EmployeeInfo> members;
	//
	@JSONField(name="M8")
	public long createTime;
	//
	@J<PERSON>NField(name="M9")
	public int creator;
	//
	@J<PERSON><PERSON>ield(name="M10")
	public int modifier;
	//
	@J<PERSON><PERSON>ield(name="M11")
	public long modifyTime;
	//
	@J<PERSON><PERSON><PERSON>(name="M12")
	public String backgroundID;
	//
	@JSONField(name="M13")
	public boolean archived;
	//
	@JSONField(name="M18")
	public int projectContentLimit;

	public ProjectDetail(){}
	@JSONCreator
	public ProjectDetail(
			@JSONField(name="M1")
			String id,
			@JSONField(name="M2")
			String name,
			@JSONField(name="M3")
			String description,
			@JSONField(name="M4")
			ProjectCategory category,
			@JSONField(name="M5")
			String groupId,
			@JSONField(name="M6")
			List<EmployeeInfo> admins,
			@JSONField(name="M7")
			List<EmployeeInfo> members,
			@JSONField(name="M8")
			long createTime,
			@JSONField(name="M9")
			int creator,
			@JSONField(name="M10")
			int modifier,
			@JSONField(name="M11")
			long modifyTime,
			@JSONField(name="M12")
			String backgroundID,
			@JSONField(name="M13")
			boolean archived,
			@JSONField(name="M18")
			int projectContentLimit) {
		this.id = id;
		this.name = name;
		this.description = description;
		this.category = category;
		this.groupId = groupId;
		this.admins = admins;
		this.members = members;
		this.createTime = createTime;
		this.creator = creator;
		this.modifier = modifier;
		this.modifyTime = modifyTime;
		this.backgroundID = backgroundID;
		this.archived = archived;
		this.projectContentLimit = projectContentLimit;
	}
}
