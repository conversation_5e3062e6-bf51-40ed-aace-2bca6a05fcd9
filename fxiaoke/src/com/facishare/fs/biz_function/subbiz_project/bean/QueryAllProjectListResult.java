/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_function.subbiz_project.bean;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

public class QueryAllProjectListResult implements Serializable{
	//返回状体信息
	@JSONField(name="M1")
	public int status;
	//返回消息
	@JSONField(name="M2")
	public String message;
	//服务时间
	@JSONField(name="M3")
	public long serviceTime;
	//我的进行中项目数量
	@JSONField(name="M10")
	public int myProjectCount;
	//我的进行中项目列表
	@JSONField(name="M11")
	public List<ProjectProfile> myProjectList;
	//归档项目数量
	@JSONField(name="M12")
	public int filingProjectCount;
	//归档项目数量
	@JSONField(name="M13")
	public int unFinishedTaskCount;
 
	public QueryAllProjectListResult(){}
	@JSONCreator
	public QueryAllProjectListResult(
			@JSONField(name="M1")
			int status,
			@JSONField(name="M2")
			String message,
			@JSONField(name="M3")
			long serviceTime,
			@JSONField(name="M10")
			int myProjectCount,
			@JSONField(name="M11")
			List<ProjectProfile> myProjectList,
			@JSONField(name="M12")
			int filingProjectCount,
			@JSONField(name="M13")
			int unFinishedTaskCount) {
		this.status = status;
		this.message = message;
		this.serviceTime = serviceTime;
		this.myProjectCount = myProjectCount;
		this.myProjectList = myProjectList;
		this.filingProjectCount = filingProjectCount;
		this.unFinishedTaskCount= unFinishedTaskCount;
	}
}
