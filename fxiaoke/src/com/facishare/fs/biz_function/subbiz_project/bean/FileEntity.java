/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_function.subbiz_project.bean;

import java.io.Serializable;
import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

public class FileEntity implements Serializable{
	//
	@J<PERSON><PERSON>ield(name="fileId")
	public String fileId;
	//
	@JSONField(name="name")
	public String name;
	//
	@J<PERSON>NField(name="path")
	public String path;
	//
	@J<PERSON>NField(name="type")
	public String type;
	//
	@JSONField(name="size")
	public int size;
	//
	@J<PERSON><PERSON>ield(name="createTime")
	public long createTime;
	//
	@J<PERSON><PERSON><PERSON>(name="creator")
	public int creator;
 
	public FileEntity(){}
	@JSONCreator
	public FileEntity(
			@JSONField(name="M2")
			String fileId,
			@JSONField(name="M3")
			String name,
			@<PERSON>SONField(name="M4")
			String path,
			@<PERSON><PERSON>NField(name="M5")
			String type,
			@JSONField(name="M6")
			int size,
			@<PERSON><PERSON><PERSON><PERSON>(name="M7")
			long createTime,
			@<PERSON><PERSON><PERSON><PERSON>(name="M8")
			int creator) {
		this.fileId = fileId;
		this.name = name;
		this.path = path;
		this.type = type;
		this.size = size;
		this.createTime = createTime;
		this.creator = creator;
	}
}
