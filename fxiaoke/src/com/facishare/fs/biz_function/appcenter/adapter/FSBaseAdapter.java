package com.facishare.fs.biz_function.appcenter.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;

import java.util.List;

/**
 * Created by 钟光燕 on 2016/3/4.
 * 对ListView 的Adapter进行一次封装，
 * 把重复代码进行提取，建议以后每个
 * listView的Adapter都继承此类
 */
public abstract class FSBaseAdapter<D> extends BaseAdapter {

    private List<D> mListData;
    private Context mContext;
    private LayoutInflater mInflater;

    public FSBaseAdapter(Context mContext, List<D> mListData) {
        this.mListData = mListData;
        this.mContext = mContext;
        mInflater = LayoutInflater.from(mContext);
    }

    @Override
    public int getCount() {
        return mListData.size();
    }

    @Override
    public D getItem(int position) {
        return mListData.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    public List<D> getListData(){
        return mListData ;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        BaseHolder baseHolder;
        if (convertView == null) {
            baseHolder = createViewHolder(mInflater, position,parent) ;
            if (baseHolder == null){
                baseHolder = createViewHolder(mInflater, position);
            }
            convertView = baseHolder.item;
            convertView.setTag(baseHolder);
        } else {
            baseHolder = (BaseHolder) convertView.getTag();
        }

        onBindViewHolder(baseHolder, mListData.get(position), position);
        return convertView;
    }

    public Context getContext() {
        return mContext;
    }

    public LayoutInflater getInflater() {
        return mInflater;
    }

    public abstract BaseHolder createViewHolder(LayoutInflater inflater, int position);
    public  BaseHolder createViewHolder(LayoutInflater inflater, int position,ViewGroup parent){
        return null ;
    }

    public abstract void onBindViewHolder(BaseHolder holder, D data, int position);

    public abstract class BaseHolder {
        public View item;

        public BaseHolder(View item) {
            this.item = item;
        }

        public <T extends View> T findView(int id) {
            return (T) item.findViewById(id);
        }
    }
}
