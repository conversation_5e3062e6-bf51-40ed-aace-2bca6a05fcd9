/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_function.appcenter.mvp.presenter;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.biz_function.appcenter.mvp.model.bean.CenterApp;
import com.facishare.fs.biz_function.appcenter.mvp.model.biz.AppManagerBiz;
import com.facishare.fs.biz_function.appcenter.mvp.model.biz.IAppManagerBiz;
import com.facishare.fs.biz_function.appcenter.mvp.view.IAppManagerView;
import com.facishare.fs.biz_function.appcenter.util.EventBusBean;
import com.facishare.fs.contacts_fs.customerservice.StatService;
import com.facishare.fs.utils_fs.ToastUtils;
import com.fxiaoke.stat_engine.events.session.UeEventSession;

import java.util.ArrayList;
import java.util.List;

import de.greenrobot.event.EventBus;

/**
 * Created by 钟光燕 on 2016/3/16.
 * ===================================================
 * <p/>
 * code is m h l
 * <p/>
 * ===================================================
 */
public class AppManagerPresenter implements IAppManagerPresenter{

    private IAppManagerView mAppManagerView ;
    private IAppManagerBiz mAppManagerBiz ;
    private UeEventSession mEventSession ;
    private List<CenterApp> origin =  new ArrayList<>() ;


    public AppManagerPresenter(IAppManagerView mAppManagerView) {
        this.mAppManagerView = mAppManagerView;
        mAppManagerBiz = new AppManagerBiz() ;

    }

    public void loadData(){
        mAppManagerView.startLoading();
        mAppManagerBiz.loadEditPageApps(new AppManagerBiz.OnAppManagerLoadListener() {
            @Override
            public void onSuccess(List<CenterApp> visibleApps) {
                refreshData(visibleApps);

            }

            @Override
            public void onFailed() {
                mAppManagerView.loadFailed();

            }

            @Override
            public void onFinish() {
            }
        });
    }

    private void refreshData(List<CenterApp> visibleApps){
        List<CenterApp> centerApps = null;
        if (visibleApps != null){
            centerApps = new ArrayList<>() ;
            for (CenterApp app : visibleApps){
                if (app.getPosition() == 1){
                    centerApps.add(app) ;
                }
            }
            origin.clear();
            origin.addAll(centerApps) ;
        }
        mAppManagerView.refreshData(centerApps);
    }

    public void updateSortApps(final List<CenterApp> visibleApps){
        mEventSession = StatService.sendStart(StatService.EDIT_APP);
        if (!checkChange(visibleApps)){
            mAppManagerView.updateSuccess();
            StatService.sendEnd(mEventSession);
            return;
        }

        mAppManagerView.showDialogLoading(I18NHelper.getText("ac.appcenter.presenter.updating_app")/* 正在更新应用... */);
        mAppManagerBiz.updateSortApps(getAppIds(visibleApps), new AppManagerBiz.OnAppManagerLoadListener() {
            @Override
            public void onSuccess(List<CenterApp> vApps) {
                ToastUtils.show(I18NHelper.getText("ac.appcenter.presenter.update_success")/* 更新成功 */);
                mAppManagerView.updateSuccess();
                EventBus.getDefault().post(new EventBusBean.UpdateApp());
                StatService.sendEnd(mEventSession);
            }

            @Override
            public void onFailed() {
                mAppManagerView.updateFailed();
                ToastUtils.show(I18NHelper.getText("ac.appcenter.presenter.update_failed")/* 更新失败 */);
                StatService.sendError(mEventSession,StatService.EDIT_APP,I18NHelper.getText("ac.appcenter.presenter.edit_failed")/* 编辑失败 */);
            }

            @Override
            public void onFinish() {
                mAppManagerView.hideDialogLoading();
            }
        });
    }

    /**
     * 检测是否数据源发生了改变，指显示和隐藏的数据源
     * @param apps
     * @return
     */
    private boolean checkChange(List<CenterApp> apps){
        boolean change = false ;
        if (apps.size() == origin.size()){
            for (int i = 0 ; i < apps.size() ; i ++){
                if (!apps.get(i).getComponentId().equals(origin.get(i).getComponentId())){
                    change = true ;
                    break;
                }
            }
        }else {
            change = true ;
        }
        return change ;
    }

    private List<String> getAppIds(List<CenterApp> apps){
        List<String> ids = new ArrayList<>() ;
        for (CenterApp app : apps){
            ids.add(app.getComponentId());
        }
        return ids ;
    }
}
