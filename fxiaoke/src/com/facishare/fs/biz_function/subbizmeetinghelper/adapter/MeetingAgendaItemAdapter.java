package com.facishare.fs.biz_function.subbizmeetinghelper.adapter;

import com.facishare.fs.i18n.I18NHelper;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.facishare.fslib.R;
import com.facishare.fs.biz_function.subbizmeetinghelper.MeetingAgendaAttachChooseActivity;
import com.facishare.fs.biz_function.subbizmeetinghelper.MeetingAgendaChooseActivity;
import com.facishare.fs.biz_function.subbizmeetinghelper.MeetingAgendaItemEditActivity;
import com.facishare.fs.biz_function.subbizmeetinghelper.beans.MeetingAgenda;
import com.facishare.fs.biz_function.subbizmeetinghelper.datactrl.IDateTimeChangeLis;
import com.facishare.fs.biz_function.subbizmeetinghelper.datactrl.MeetingAgendaComparator;
import com.facishare.fs.biz_function.subbizmeetinghelper.utils.MeetingUtils;
import com.fxiaoke.fscommon.adapter.NormalBaseAdapter;
import com.fxiaoke.cmviews.sticky_listview.StickyListHeadersAdapter;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * Created by yangwg on 2016/2/22.
 */
public class MeetingAgendaItemAdapter extends NormalBaseAdapter implements StickyListHeadersAdapter {
    Context mContext;
    SimpleDateFormat mFormat ;
    SimpleDateFormat mFormatDisplay ;
    SimpleDateFormat mFormatOnlyTime ;
    long mStartTime,mEndTime;
    String meetingId;
    public MeetingAgendaItemAdapter(Context context, List<MeetingAgenda> data,long startTime,long endTime,String meetingId) {
        super(context, data);
        mContext = context;
        mFormat = new SimpleDateFormat("yyyy-MM-dd");
        mFormatDisplay = new SimpleDateFormat(I18NHelper.getText("meta.adapters.TimeLineAdapter.3076")/* yyyy年MM月dd日 */);
        mFormatOnlyTime = new SimpleDateFormat("HH:mm");
        mStartTime = startTime;
        mEndTime = endTime;
        this.meetingId = meetingId;
        updateData(data);
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder viewHolder;
        if(convertView == null){
            convertView = View.inflate(mContext, R.layout.meeting_agendas_list_item, null);
            viewHolder = new ViewHolder();
            viewHolder.agendaTitleTV = (TextView) convertView.findViewById(R.id.meeting_agenda_title);
            viewHolder.agendaDeleteLayout = convertView.findViewById(R.id.agenda_delete_layout);
            viewHolder.agendaTopicLayout =  convertView.findViewById(R.id.meeting_agenda_topic_rl);
            viewHolder.agendaTopic = (TextView) convertView.findViewById(R.id.meeting_agenda_topic_tv);
            viewHolder.agendaBeginTimeLayout = convertView.findViewById(R.id.meeting_agenda_begintime_rl);
            viewHolder.agendaBeginTimeTV = (TextView) convertView.findViewById(R.id.meeting_agenda_begintime_tv);
            viewHolder.agendaEndTimeLayout = convertView.findViewById(R.id.meeting_agenda_endtime_rl);
            viewHolder.agendaEndTimeTV = (TextView) convertView.findViewById(R.id.meeting_agenda_endtime_tv);
            viewHolder.agendaAttachLayout = convertView.findViewById(R.id.meeting_agenda_attach_rl);
            viewHolder.agendaAttachTV = (TextView) convertView.findViewById(R.id.meeting_agenda_attach_tv);
            viewHolder.agendaMarginDivider = convertView.findViewById(R.id.agenda_margin_divider);
            viewHolder.agendaAddLayout = convertView.findViewById(R.id.meeting_agenda_add_ll);
            viewHolder.agendaMainLayout = convertView.findViewById(R.id.agenda_main_ll);
            convertView.setTag(viewHolder);
        }else{
            viewHolder = (ViewHolder)convertView.getTag();
        }
        final MeetingAgenda currentItem = (MeetingAgenda) mData.get(position);
        viewHolder.agendaTitleTV.setText(I18NHelper.getFormatText("mt.subbizmeetinghelper.MeetingAgendaItemAdapter.agenda",String.valueOf(currentItem.orderNum))/* 议程{0} */);
        viewHolder.agendaTopic.setText(currentItem.agendaContent);
        if (currentItem.startTime!=0){
            viewHolder.agendaBeginTimeTV.setText(mFormatOnlyTime.format(new Date(currentItem.startTime)));
        }else {
            viewHolder.agendaBeginTimeTV.setText("");
        }
        if (currentItem.endTime!=0){
            viewHolder.agendaEndTimeTV.setText(mFormatOnlyTime.format(new Date(currentItem.endTime)));
        }else {
            viewHolder.agendaEndTimeTV.setText("");
        }
        if(currentItem.files!=null&&currentItem.files.size()>0){//附件信息，目前仅支持一个议程挂一个附件
            viewHolder.agendaAttachTV.setText(currentItem.files.get(0).fileName);
        }else {
            viewHolder.agendaAttachTV.setText("");
        }
        //删除
        if(!currentItem.isAddItem){
            viewHolder.agendaDeleteLayout.setVisibility(View.VISIBLE);
        }
        viewHolder.agendaDeleteLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                deleteCurItem(currentItem);
            }
        });
        //议题布局
        viewHolder.agendaTopicLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent it = MeetingAgendaItemEditActivity.getStartIntent(mContext, currentItem);
                ((Activity) mContext).startActivityForResult(it, MeetingAgendaChooseActivity.REQUESTCODE_CHANGE_TOPIC);
            }
        });
        //选择变更开始时间
        final TextView agendaBeginTimeTV = viewHolder.agendaBeginTimeTV;
        viewHolder.agendaBeginTimeLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Date date = null;
                if(agendaBeginTimeTV.getTag() instanceof  Long){
                    date = new Date((long)agendaBeginTimeTV.getTag());
                }else{
                }
                Dialog dialog = MeetingUtils.getChooseTimeDialog(mContext, I18NHelper.getText("xt.newmessageremindsettingactivity.text.choose_start_time")/* 选择开始时间 */, date, agendaBeginTimeTV, true, new IDateTimeChangeLis() {
                    @Override
                    public void onClickBack(TextView timeShowView, String choosedTimeDes, long choosedTimeVaule) {
                        //不依据会议开始和截止时间拦截议程选择的时间
//                        if(choosedTimeVaule < mStartTime){
//                            ToastUtils.showToast("议程开始时间不能早于会议开始时间");
//                        }else if(choosedTimeVaule > mEndTime){
//                            ToastUtils.showToast("议程开始时间不能晚于会议结束时间");
//                        }else{
                            MeetingAgenda newItem = new MeetingAgenda();
                            newItem.startTime = choosedTimeVaule;
                            timeShowView.setText(choosedTimeDes);
                            timeShowView.setTag(choosedTimeVaule);
                            updateCurData(currentItem, newItem);
//                        }
                    }
                });
                dialog.show();
            }
        });
        //选择变更结束时间
        final TextView agendaEndTimeTV = viewHolder.agendaEndTimeTV;
        viewHolder.agendaEndTimeLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Date date = null;
                if(agendaEndTimeTV.getTag() instanceof  Long){
                    date = new Date((long)agendaEndTimeTV.getTag());
                }else{
                }
                Dialog dialog = MeetingUtils.getChooseTimeDialog(mContext, I18NHelper.getText("xt.newmessageremindsettingactivity.text.select_end_time")/* 选择结束时间 */,date, agendaEndTimeTV,true,new IDateTimeChangeLis() {
                    @Override
                    public void onClickBack(TextView timeShowView, String choosedTimeDes, long choosedTimeVaule) {
                        //不依据会议开始和截止时间拦截议程选择的时间
//                        if(choosedTimeVaule < mStartTime){
//                            ToastUtils.showToast("议程结束时间不能早于会议开始时间");
//                        }else if(choosedTimeVaule > mEndTime){
//                            ToastUtils.showToast("议程结束时间不能晚于会议结束时间");
//                        }else{
                            MeetingAgenda newItem = new MeetingAgenda();
                            newItem.endTime = choosedTimeVaule;
                            timeShowView.setText(choosedTimeDes);
                            timeShowView.setTag(choosedTimeVaule);
                            updateCurData(currentItem, newItem);
//                        }
                    }
                });
                dialog.show();
            }
        });
        //附件选择布局
        viewHolder.agendaAttachLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = MeetingAgendaAttachChooseActivity.getStartIntent(mContext,currentItem,meetingId);
                ((Activity)mContext).startActivityForResult(intent, MeetingAgendaChooseActivity.REQUESTCODE_CHANGE_ATTACH);
            }
        });
        viewHolder.agendaMarginDivider.setVisibility(View.VISIBLE);
        viewHolder.agendaAddLayout.setVisibility(View.GONE);
        boolean isShowAdd = currentItem.isAddItem;
        boolean isShowMargin = false;
        if(mData.size() > position+1){//有下一条时：1.下一条和当前一样时，出现间隔布局；不一样，出现新增布局
            MeetingAgenda nextItem = (MeetingAgenda) mData.get(position+1);
            if(nextItem!=null){
                final long currentGroupType = getGroupType(currentItem);
                long nextGroupType = getGroupType(nextItem);
                if(currentGroupType == nextGroupType){
                    isShowMargin = true;
                }else {
                }
            }
        }
        viewHolder.agendaMarginDivider.setVisibility(View.GONE);
        if(isShowMargin){
            viewHolder.agendaMarginDivider.setVisibility(View.VISIBLE);
        }
        if(isShowAdd){
            viewHolder.agendaAddLayout.setVisibility(View.VISIBLE);//议程新增布局
            viewHolder.agendaAddLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    addNewAgendaItem(currentItem);
                }
            });
            viewHolder.agendaMainLayout.setVisibility(View.GONE);
        }else {
            viewHolder.agendaMainLayout.setVisibility(View.VISIBLE);
            viewHolder.agendaAddLayout.setVisibility(View.GONE);
            viewHolder.agendaAddLayout.setOnClickListener(null);
        }
        return convertView;
    }

    private void updateCurData(MeetingAgenda currentItem, MeetingAgenda newItem) {
        if(currentItem!=null&&newItem!=null){
            if(newItem.startTime!=0){
                currentItem.startTime = newItem.startTime;
            }
            if(newItem.endTime!=0){
                currentItem.endTime = newItem.endTime;
            }
        }
        notifyDataSetChanged();
    }
    private void deleteCurItem(MeetingAgenda currentItem){
        mData.remove(currentItem);
        updateData(mData);
    }
    /**
     * 新增议程条目
     * @param currentItem
     */
    private void addNewAgendaItem(MeetingAgenda currentItem) {
        MeetingAgenda newItem = new MeetingAgenda();
        newItem.orderNum = currentItem.orderNum;
        currentItem.orderNum = currentItem.orderNum+1;
        newItem.agendaDate = currentItem.agendaDate;
        if(!TextUtils.isEmpty(this.meetingId)){
            newItem.meetingId = meetingId;
        }
//        newItem.startTime = new Date().getTime();
//        currentItem.startTime = new Date().getTime()+1;
        mData.add(newItem);
        updateData(mData);
    }

    @Override
    public void updateData(List data) {//调整排序，先按agendaDate排序，再按orderNum排序
        Collections.sort(data, new MeetingAgendaComparator());
        int currentIndx = 1;
        List<MeetingAgenda> meetingAgendaList = data;
        for(int i=0;i<meetingAgendaList.size();i++){//校正索引
            MeetingAgenda currentItem = meetingAgendaList.get(i);
            MeetingAgenda nextItem = null;
            if(i+1<meetingAgendaList.size()){
                nextItem = meetingAgendaList.get(i+1);
            }
            if(!currentItem.isAddItem){
                if(nextItem!=null){
                    if(TextUtils.equals(currentItem.agendaDate,nextItem.agendaDate)){
                        currentItem.orderNum = currentIndx;
                        currentIndx++;
                    }else {
                        currentIndx=1;
                    }
                }else{
                    currentItem.orderNum = currentIndx;
                }
            }else{
                currentItem.orderNum = currentIndx;
                currentIndx = 1;
            }
        }
        super.updateData(data);
    }

    @Override
    public View getHeaderView(int position, View convertView, ViewGroup parent) {
        convertView = View.inflate(mContext, R.layout.meeting_agenda_list_header_layout, null);
        TextView groupTitleTV = (TextView)convertView.findViewById(R.id.meeting_group_title);
        MeetingAgenda agenda = (MeetingAgenda) mData.get(position);
        String dayDes = getGroupDayDes(mStartTime,mEndTime,agenda);
        String dayDeatailDes = mFormatDisplay.format(getGroupType(agenda));
        //R.string.meeting_agenda_group_title   第{0}天-{1}
        String groupDes = I18NHelper.getFormatText("meeting.agenda.group.title",dayDes,dayDeatailDes);
        groupTitleTV.setText(groupDes);
        return convertView;
    }

    private String getGroupDayDes(long startTime, long endTime, MeetingAgenda agenda) {
        String dayDes = "";
        if(agenda!=null){//议程日期 yyyy-MM-dd 格式
            try {
                if(!TextUtils.isEmpty(agenda.agendaDate)){
                    Date date = mFormat.parse(agenda.agendaDate);
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(date);
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.SECOND,0);
                    long curGroupIndexTime = calendar.getTime().getTime();

                    Calendar startCalendar = Calendar.getInstance();
                    startCalendar.setTime(new Date(startTime));
                    startCalendar.set(Calendar.HOUR_OF_DAY, 0);
                    startCalendar.set(Calendar.MINUTE,0);
                    startCalendar.set(Calendar.SECOND,0);
                    long ajustStartTime = startCalendar.getTime().getTime();
                    long divider = curGroupIndexTime - ajustStartTime;
                    int day = (int)(divider/(1000 * 60 * 60 * 24)+1);
                    dayDes = day+"";
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return dayDes;
    }

    @Override
    public long getHeaderId(int position) {
        MeetingAgenda agenda = (MeetingAgenda) mData.get(position);
        return getGroupType(agenda);
    }

    protected long getGroupType(MeetingAgenda agenda){
        if(agenda==null){
            return 0;
        }
        try {
            if(!TextUtils.isEmpty(agenda.agendaDate)){
                Date date = mFormat.parse(agenda.agendaDate);
                return date.getTime();
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return 0;
    }
    class ViewHolder {
        /**
         * 议程标题，比如议程1，还是议程2
         */
        public TextView agendaTitleTV;
        /**
         * 删除当前议程
         */
        public View agendaDeleteLayout;
        public View agendaTopicLayout;
        /**
         * 议题
         */
        public TextView agendaTopic;
        public View agendaBeginTimeLayout;
        public TextView agendaBeginTimeTV;
        public View agendaEndTimeLayout;
        public TextView agendaEndTimeTV;
        /**
         * 附件选择
         */
        public View agendaAttachLayout;
        /**
         * 附件展示
         */
        public TextView agendaAttachTV;
        /**
         * 新增议程
         */
        public View agendaAddLayout;
        /**
         * 组内条目间隔
         */
        public View agendaMarginDivider;
        public View agendaMainLayout;
    }
}
