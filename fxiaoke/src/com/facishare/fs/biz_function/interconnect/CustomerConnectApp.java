package com.facishare.fs.biz_function.interconnect;

import com.facishare.fs.i18n.I18NHelper;

import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ScrollView;
import android.widget.TextView;

import com.facishare.fs.BaseActivity;
import com.facishare.fs.biz_function.interconnect.bean.CustomerAppWxServiceVO;
import com.facishare.fs.biz_function.interconnect.bean.InterApp;
import com.facishare.fs.biz_function.interconnect.view.CompAppView;
import com.facishare.fslib.R;

import java.util.LinkedList;
import java.util.List;

/**
 * Created by gyzhong on 2017/4/11.
 */

/**
 * 互联应用入口
 */
public class CustomerConnectApp extends BaseActivity implements ICustomerView, CompAppView.OnItemClickListener {


    private ViewGroup mAppContainer;
    private IManagerPresent mPresenter;
    private View mErrorView;
    private boolean mShowData;

    private ScrollView mScrollView;
    private View mLoadingView ;
    private ImageView mDrawableAnim ;
    private AnimationDrawable mAnim ;
    private List<CompAppView> mCompAppViews ;
    private TextView mTips;
    public static final String KEY_INTER_NAME="KEY_INTER_NAME";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_customer_connect);
        initView();
    }

    private void initView() {
        initTitleCommon();
        initBackBtn(getIntent().getStringExtra(KEY_INTER_NAME));

        mAppContainer = (ViewGroup) findViewById(R.id.id_inter_connect_container);
        mErrorView = findViewById(R.id.id_inter_connect_empty_view);
        mTips = findViewById(R.id.tip_name);
        mScrollView = (ScrollView) findViewById(R.id.id_inter_connect_scrollview);
        mLoadingView =  findViewById(R.id.id_inter_load_view);
        mDrawableAnim = (ImageView) findViewById(R.id.id_app_center_anim);
        mAnim = (AnimationDrawable) mDrawableAnim.getDrawable();
        mPresenter = new CustomerPresenterImpl(this);
        startLoading();
        mPresenter.loadData();
        mPresenter.checkNet();

    }

    private void initBackBtn(String title) {
        if (TextUtils.isEmpty(title)){
            title = I18NHelper.getText("xt.biz_function.CustomerConnectApp.1")/* 客户互联 */ ;
        }
        mCommonTitleView.setMiddleText(title);
        mCommonTitleView.addLeftAction(R.string.return_before_new_normal, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }




    private void destroyView(){
        if (mCompAppViews != null){
            for (CompAppView view : mCompAppViews){
                view.destroy();
            }
            mCompAppViews.clear();
        }
    }

    private void addCompAppView(CompAppView view){
        if (mCompAppViews == null){
            mCompAppViews = new LinkedList<>() ;
        }
        mCompAppViews.add(view) ;
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        destroyView();
    }


    @Override
    public void showData(List<CustomerAppWxServiceVO> data) {
        mShowData = true;
        stopLoading();
        destroyView() ;
        int scrollY = mScrollView.getScrollY();
        mAppContainer.removeAllViews();
        if (data.size() == 0){
            mErrorView.setVisibility(View.VISIBLE);
            mTips.setText(I18NHelper.getText("pay.common.common.no_data")/* 暂无数据 */);
        }else {
            CompAppView view = new CompAppView();
            addCompAppView(view);
            mAppContainer.addView(view.bindData(this, data, new CompAppView.OnItemClickListener() {
                @Override
                public void onInterAppClick(InterApp app) {
                    mPresenter.openInterApp(CustomerConnectApp.this, app);
                }
            }));
        }

        mScrollView.scrollTo(0, scrollY);
    }

    @Override
    public void showError() {
        if (!mShowData) {
            mTips.setText(I18NHelper.getText("xt.customer_connectapp.text.net_error")/* 网络出问题了 */);
            mErrorView.setVisibility(View.VISIBLE);
        }
        stopLoading();
    }

    @Override
    public void clearNewFlag(InterApp app) {
        if (mCompAppViews != null){
            for (CompAppView view : mCompAppViews){
                view.clearFlag(app);
            }
        }
    }

    @Override
    public void onInterAppClick(InterApp app) {

    }


    private void startLoading(){
        mLoadingView.setVisibility(View.VISIBLE);
        mAnim.start();
    }

    private void stopLoading(){
        mLoadingView.setVisibility(View.GONE);
        mAnim.stop();
    }
}
