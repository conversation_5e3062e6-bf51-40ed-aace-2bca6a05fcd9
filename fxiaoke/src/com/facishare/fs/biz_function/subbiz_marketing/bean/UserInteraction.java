
package com.facishare.fs.biz_function.subbiz_marketing.bean;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

public class UserInteraction {
    @J<PERSON><PERSON>ield(name="M11")
    public int sequence;
    @J<PERSON><PERSON>ield(name="M12")
    public String userName;
    @JSO<PERSON>ield(name="M13")
    public String bcUser;
    @JSONField(name="M14")
    public String department;
    @JSONField(name="M15")
    public long postTimestamp;
    @JSONField(name="M16")
    public String postContent;
    @JSONField(name="M17")
    public String operation;
    @J<PERSON><PERSON>ield(name="M18")
    public String postID;

    /**
     * @param sequence
     * @param userName
     * @param bcUser
     * @param department
     * @param postTimestamp
     * @param postContent
     * @param operation
     * @param postID
     */
    @JSONCreator
    public UserInteraction(
            @JSONField(name="M11")
            int sequence,
            @JSONField(name="M12")
            String userName, 
            @JSONField(name="M13")
            String bcUser, 
            @J<PERSON><PERSON><PERSON>(name="M14")
            String department, 
            @JSONField(name="M15")
            long postTimestamp, 
            @JSONField(name="M16")
            String postContent,
            @JSONField(name="M17")
            String operation, 
            @JSONField(name="M18")
            String postID) {
        super();
        this.sequence = sequence;
        this.userName = userName;
        this.bcUser = bcUser;
        this.department = department;
        this.postTimestamp = postTimestamp;
        this.postContent = postContent;
        this.operation = operation;
        this.postID = postID;
    }

}
