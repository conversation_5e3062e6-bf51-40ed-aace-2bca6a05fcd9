package com.facishare.fs.biz_function.subbiz_marketing;

import com.facishare.fs.i18n.I18NHelper;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;
import androidx.viewpager.widget.ViewPager.OnPageChangeListener;
import android.view.View;
import android.view.ViewTreeObserver.OnGlobalLayoutListener;

import com.facishare.fs.BaseActivity;
import com.facishare.fs.MainTabActivity;
import com.facishare.fslib.R;
import com.facishare.fs.biz_function.subbiz_marketing.bean.QueryWyxArgs;
import com.fxiaoke.fscommon_res.common_view.CommonTitleView;
import com.viewpagerindicator.TabPageIndicator;

import java.util.ArrayList;
import java.util.List;

public class WyxActivity extends BaseActivity{
    
    TabPageIndicator mindicatorPager;
    
    TabPageIndicatorAdapter madapterPager;
    
    WyxControler mWyxControler;
    
    ViewPager pager ;
    private static final String TYPE = "type";
    private static final String SUM = "sum";

    public static final int WYX_SESSION = 1;
    
    ArrayList<WyxContentListFrag> fragments = new ArrayList<WyxContentListFrag>();
    private int mCurrentPos;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.wyx_act_layout);
        initTitleViews();
        pager = (ViewPager)findViewById(R.id.pager);
        mWyxControler = new WyxControler();
        ArrayList<QueryWyxArgs> types = mWyxControler.getWyxTypeList();
        int position = 0;
        for (QueryWyxArgs getWyxListArgs : types) {
            WyxContentListFrag frag;
            if(savedInstanceState==null){
                frag = WyxContentListFrag.newInstance(getWyxListArgs,mWyxControler);
            } else {
                String tag = "android:switcher:" + pager.getId() + ":" + (position++);
                frag = (WyxContentListFrag) getSupportFragmentManager().findFragmentByTag(tag);
                frag.recoverSavedInstance(getWyxListArgs, mWyxControler);
            }
            fragments.add(frag);
        }
        madapterPager = new TabPageIndicatorAdapter(getSupportFragmentManager(),fragments);
        pager.setAdapter(madapterPager);
        pager.setOffscreenPageLimit(4);
        mindicatorPager = (TabPageIndicator)findViewById(R.id.indicator);
        mindicatorPager.setViewPager(pager);
        mindicatorPager.setOnPageChangeListener(new OnPageChangeListener() {
            
            @Override
            public void onPageSelected(int pos) {
                mCurrentPos = pos;
                WyxContentListFrag fragment = (WyxContentListFrag) madapterPager.getItem(pos);
                if(fragment!=null){
                    if(!mWyxControler.isHaveCache(fragment.getWyxListArgs)){
                        fragment.load();
                    }
                }
//                FCLog.e("onPageSelected------"+pos+mWyxControler.isHaveCache(fragment.getWyxListArgs));
            }
            
            @Override
            public void onPageScrolled(int arg0, float arg1, int arg2) {
            }
            
            @Override
            public void onPageScrollStateChanged(int arg0) {
                
            }
        });
        pager.getViewTreeObserver().addOnGlobalLayoutListener(new OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                pager.getViewTreeObserver().removeGlobalOnLayoutListener(this);
                WyxContentListFrag fragment = fragments.get(0);
                fragment.load();
            }
        });
    }
    
    
    
    private void initTitleViews() {
        CommonTitleView titleView = (CommonTitleView) findViewById(R.id.title);
        titleView.getCenterTxtView().setText(I18NHelper.getText("qx.session_list.sessionname.micromarketing")/* 微营销 */);
        Intent it = getIntent();
        int type = 0;
        int sum = 0;
        if (it != null){
            type = it.getIntExtra(TYPE,0);
            sum = it.getIntExtra(SUM,0);
        }
        if (WYX_SESSION == type){
            titleView.addLeftAction(R.drawable.btn_title_back,new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    finish();
                }
            });
        }else {
            titleView.addLeftBackAction(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    finish();
                }
            });
        }
        titleView.addRightAction(R.string.work_search, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(WyxActivity.this, WyxSearchActivity.class);
                MainTabActivity.startActivityByAnim(intent);
            }
        });
    }

    @Override
    public void finish() {
        super.finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mWyxControler.clear();

    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    class TabPageIndicatorAdapter extends FragmentPagerAdapter {
        List<WyxContentListFrag> mFeedListFragments;
        public TabPageIndicatorAdapter(FragmentManager fm,List<WyxContentListFrag> feedListFragments) {
            super(fm);
            mFeedListFragments=feedListFragments;
        }
        @Override  
        public int getItemPosition(Object object) {  
            return POSITION_NONE;  
        } 
        
        @Override
        public Fragment getItem(int position) {
            return mFeedListFragments.get(position);
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return mFeedListFragments==null?"":mFeedListFragments.get(position).name;
        }

        @Override
        public int getCount() {
            return mFeedListFragments==null?0:mFeedListFragments.size();
        }
    }

    public static Intent getIntent(Context ctx,int sum,int type){
        Intent intent = new Intent(ctx,WyxActivity.class);
        intent.putExtra(SUM,sum);
        intent.putExtra(TYPE,type);
        return intent;
    }

//    @Override
//    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
//        super.onActivityResult(requestCode, resultCode, data);
//        Fragment fragment = madapterPager.getItem(mCurrentPos);
//        fragment.onActivityResult(requestCode,resultCode,data);
//    }
}
