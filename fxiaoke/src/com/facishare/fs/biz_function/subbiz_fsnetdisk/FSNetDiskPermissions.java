
package com.facishare.fs.biz_function.subbiz_fsnetdisk;

import com.fs.fsprobuf.FSNetDiskProtobuf.PermissionType;

public class FSNetDiskPermissions {

    /**
     * 高级
     */
    public static final int ADVANCED = 1;

    /**
     * 中级
     */
    public static final int SECONDARY = 2;

    /**
     * 基础
     */
    public static final int BASIC = 3;

    /**
     * 不可下载
     */
    public static final int LOW = 4;

    /**
     * 获得相应类型
     * 
     * @param permission
     * @return
     */
    public static final PermissionType getPermissionType(int permission) {
        if (permission == ADVANCED) {
            return PermissionType.Admin;
        } else if (permission == SECONDARY) {
            return PermissionType.Middle;
        } else if (permission == LOW) {
            return PermissionType.Low;
        }else {
            return PermissionType.Basic;
        }
    }

    public static final int fromPermissionType(PermissionType type) {
        if (type == PermissionType.Admin) {
            return ADVANCED;
        } else if (type == PermissionType.Middle) {
            return SECONDARY;
        }else if (type == PermissionType.Low) {
            return LOW;
        } else {
            return BASIC;
        }
    }

}
