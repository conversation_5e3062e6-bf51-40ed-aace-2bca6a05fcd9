/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.biz_function.subbiztrainhelper;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

import java.util.ArrayList;

/**
 * Created by guoyf on 2016/4/7.
 */
public class QueryAdminListResult {
    public static final int STATUS_SUCCESS = 1000;
    @JSONField(name="M1")
    public int status;//1000表示成功

    @JSONField(name="M2")
    public String message;

    @JSONField(name="M10")
    public ArrayList<User> users;

    @JSONCreator
    public QueryAdminListResult(
            @JSONField(name="M1") int status,
            @JSONField(name="M2") String message,
            @JSONField(name="M10") ArrayList<User> users) {
        this.status = status;
        this.message = message;
        this.users = users;
    }
}
