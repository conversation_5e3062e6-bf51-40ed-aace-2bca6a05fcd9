package com.facishare.fs.biz_function.subbiz_attendance_new;

import com.facishare.fs.i18n.I18NHelper;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.facishare.fslib.R;
import com.facishare.fs.biz_function.subbiz_attendance_new.adapter.AbsListBaseAdapter;
import com.facishare.fs.biz_function.subbiz_attendance_new.bean.CheckEffectInfoResult;
import com.facishare.fs.biz_function.subbiz_attendance_new.bean.HaveRule;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.pluginapi.contact.beans.AEmpSimpleEntity;
import com.facishare.fs.utils_fs.ToastUtils;
import com.fxiaoke.cmviews.sticky_listview.StickyListHeadersAdapter;
import com.fxiaoke.cmviews.sticky_listview.StickyListHeadersListView;
import com.lidroid.xutils.exception.DbException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 冲突人员的界面
 *
 * <AUTHOR>
 * @date 2016/6/29.
 */
public class ClashStaffDetailsFragment1 extends Fragment implements AdapterView.OnItemClickListener, View.OnClickListener {
    private StickyListHeadersListView lv_set_staff_clash;
    private LayoutInflater mInflater;
    private ClashStaffDetailsAdapter clashStaffDetailsAdapter;
    private String[] head = new String[]{I18NHelper.getText("wq.clash_staff_details_fragment.text.used_other_rule_emmployee")/* 已使用其他规则员工 */, I18NHelper.getText("wq.clash_staff_details_fragment.text.no_join_signin_emmployee")/* 不参与考勤员工 */};
    private LinearLayout ll_item_layout;
    private CheckBox cboSelectAll;

    private Boolean isSelectAll = false;
    private FrameLayout bottom_fragment;
    private TextView tv_no_join_check;
    private List<Integer> mListUnEffect = new ArrayList();//临时容器
    private List<Integer> mListEffect = new ArrayList();//临时容器
    private List<HaveRule> mListUnEffectHave = new ArrayList();//临时容器
    private HashMap<Integer, Boolean> clashStaffBeenMap;//保存选中状态
    private myCity city;
    public  List<myCity> mList;
    private List<AEmpSimpleEntity> byIds;
    private List<HaveRule> haveRules;
    private ImageView searchbar_del;
    private TextView searchbar_search;
    private EditText searchbar_content;
    private LinearLayout tv_edit_flag;
    private boolean isSearch = false;
    private CheckEffectInfoResult serializableExtra;
    private List<myCity> objects;

    public ClashStaffDetailsFragment1() {

    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        this.mInflater = inflater;
        View mView = inflater.inflate(R.layout.sign_clash_staff_details_fragment_1, container, false);
        clashStaffBeenMap = new HashMap<>();
        objects = new ArrayList<>();
        initData();
        if (null == byIds && null == haveRules) {
            mView = inflater.inflate(R.layout.function_empty_page_fragment, container, false);
            TextView tv_empty_page_content = (TextView) mView.findViewById(R.id.tv_empty_page_content);
            tv_empty_page_content.setText(I18NHelper.getText("wq.clash_staff_details_fragment.text.no_conflict_people")/* 暂无冲突人员 */);
        } else {

            initView(mView);
        }
        return mView;
    }


    private void initView(View mView) {
        ll_item_layout = (LinearLayout) mView.findViewById(R.id.ll_item_layout);

        bottom_fragment = (FrameLayout) mView.findViewById(R.id.bottom_fragment);

        tv_no_join_check = (TextView) mView.findViewById(R.id.tv_no_join_check);
        tv_edit_flag = (LinearLayout) mView.findViewById(R.id.tv_edit_flag);
        searchbar_content = (EditText) mView.findViewById(R.id.searchbar_content);
        searchbar_del = (ImageView) mView.findViewById(R.id.searchbar_del);
        searchbar_search = (TextView) mView.findViewById(R.id.searchbar_search);

        cboSelectAll = (CheckBox) mView.findViewById(R.id.cboSelectAll);
        lv_set_staff_clash = (StickyListHeadersListView) mView.findViewById(R.id.lv_set_staff_clash_1);
        lv_set_staff_clash.setPullLoadEnable(false);
        lv_set_staff_clash.stopLoadMore();
        lv_set_staff_clash.setPullRefreshEnable(false);
        lv_set_staff_clash.setPullOutHeadViewEnable(false);
        clashStaffDetailsAdapter = new ClashStaffDetailsAdapter();

        clashStaffDetailsAdapter.addDataList(mList);
        lv_set_staff_clash.setAdapter(clashStaffDetailsAdapter);
        lv_set_staff_clash.setOnItemClickListener(this);
        ll_item_layout.setOnClickListener(this);
        bottom_fragment.setOnClickListener(this);
        searchbar_search.setOnClickListener(this);
        tv_edit_flag.setOnClickListener(this);
        searchbar_del.setOnClickListener(this);
        bottom_fragment.setEnabled(false);
    }


    private void initData() {
        //初始化状态集合
        mList = new ArrayList();
        //  clashStaffBeenList = new ArrayList<>();
/****/

        serializableExtra = (CheckEffectInfoResult) getArguments().getSerializable("serializableExtra");
        if (null != serializableExtra) {
            if (serializableExtra.unEffectUIds != null && serializableExtra.unEffectUIds.size() != 0) {
                try {
                    byIds = FSContextManager.getCurUserContext().getContactDbHelper()
                            .getAEmpSimpleEntityDao().findByIds(serializableExtra.unEffectUIds);
                } catch (DbException e) {
                    e.printStackTrace();
                }
            }
            if (serializableExtra.haveRules != null && serializableExtra.haveRules.size() != 0) {
                //获取人员列表,强制指定不适用员工id列表
                haveRules = serializableExtra.haveRules;
            } else {
                ToastUtils.show(I18NHelper.getText("pay.common.common.no_data")/* 暂无数据 */);
            }
        }
        /****/
        if (haveRules != null && haveRules.size() != 0) {
            for (int i = 0; i < haveRules.size(); i++) {
                city = new myCity(haveRules.get(i), "a", 1);
                mList.add(city);
                clashStaffBeenMap.put(haveRules.get(i).userId, false);
            }
        }
        if (byIds != null && byIds.size() != 0) {
            for (int i = 0; i < byIds.size(); i++) {
                city = new myCity(byIds.get(i), "b", 2);
                mList.add(city);
                clashStaffBeenMap.put(byIds.get(i).employeeID, false);
            }
        }

    }


    //处理点击事件,注意由于StickyListHeadersListView继承自Xlistview所以默认是带有一个标题头的，所以每个条目要进行-1的操作
    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {

        if (tv_edit_flag.getVisibility() == View.GONE) {
            if (objects.get(position - 1).name instanceof HaveRule) {
                if (clashStaffBeenMap.get(((HaveRule) (objects.get(position - 1).name)).userId)) {
                    clashStaffBeenMap.put(((HaveRule) (objects.get(position - 1).name)).userId, false);
                } else {
                    clashStaffBeenMap.put(((HaveRule) (objects.get(position - 1).name)).userId, true);
                }
            } else if (objects.get(position - 1).name instanceof AEmpSimpleEntity) {
                if (clashStaffBeenMap.get(((AEmpSimpleEntity) (objects.get(position - 1).name)).employeeID)) {
                    clashStaffBeenMap.put(((AEmpSimpleEntity) (objects.get(position - 1).name)).employeeID, false);
                } else {
                    clashStaffBeenMap.put(((AEmpSimpleEntity) (objects.get(position - 1).name)).employeeID, true);
                }
            }
            for (int j = 0; j < objects.size(); j++) {
                if (objects.get(j).name instanceof HaveRule) {
                    if (clashStaffBeenMap.get(((HaveRule) (objects.get(j).name)).userId)) {
                        tv_no_join_check.setTextColor(getResources().getColor(R.color.orange));
                        bottom_fragment.setEnabled(true);
                        break;
                    } else {
                        tv_no_join_check.setTextColor(getResources().getColor(R.color.gray));
                        bottom_fragment.setEnabled(false);
                    }
                } else if (objects.get(j).name instanceof AEmpSimpleEntity) {
                    if (clashStaffBeenMap.get(((AEmpSimpleEntity) (objects.get(j).name)).employeeID)) {
                        tv_no_join_check.setTextColor(getResources().getColor(R.color.orange));
                        bottom_fragment.setEnabled(true);
                        break;
                    } else {
                        tv_no_join_check.setTextColor(getResources().getColor(R.color.gray));
                        bottom_fragment.setEnabled(false);
                    }
                }
            }


        } else {

            if (mList.get(position - 1).name instanceof HaveRule) {
                if (clashStaffBeenMap.get(((HaveRule) (mList.get(position - 1).name)).userId)) {
                    clashStaffBeenMap.put(((HaveRule) (mList.get(position - 1).name)).userId, false);
                } else {
                    clashStaffBeenMap.put(((HaveRule) (mList.get(position - 1).name)).userId, true);
                }
            } else if (mList.get(position - 1).name instanceof AEmpSimpleEntity) {
                if (clashStaffBeenMap.get(((AEmpSimpleEntity) (mList.get(position - 1).name)).employeeID)) {
                    clashStaffBeenMap.put(((AEmpSimpleEntity) (mList.get(position - 1).name)).employeeID, false);
                } else {
                    clashStaffBeenMap.put(((AEmpSimpleEntity) (mList.get(position - 1).name)).employeeID, true);
                }
            }
            for (int j = 0; j < mList.size(); j++) {

                if (mList.get(j).name instanceof HaveRule) {
                    if (clashStaffBeenMap.get(((HaveRule) (mList.get(j).name)).userId)) {
                        tv_no_join_check.setTextColor(getResources().getColor(R.color.orange));
                        bottom_fragment.setEnabled(true);
                        break;
                    } else {
                        tv_no_join_check.setTextColor(getResources().getColor(R.color.gray));
                        bottom_fragment.setEnabled(false);
                    }
                } else if (mList.get(j).name instanceof AEmpSimpleEntity) {
                    if (clashStaffBeenMap.get(((AEmpSimpleEntity) (mList.get(j).name)).employeeID)) {
                        tv_no_join_check.setTextColor(getResources().getColor(R.color.orange));
                        bottom_fragment.setEnabled(true);
                        break;
                    } else {
                        tv_no_join_check.setTextColor(getResources().getColor(R.color.gray));
                        bottom_fragment.setEnabled(false);
                    }
                }
            }
        }

        clashStaffDetailsAdapter.notifyDataSetChanged();


    }

    @Override
    public void onClick(View v) {
        int i1 = v.getId();
        if (i1 == R.id.tv_edit_flag) {//点击变换搜索框
            tv_edit_flag.setVisibility(View.GONE);
            searchbar_content.setVisibility(View.VISIBLE);
            searchbar_search.setVisibility(View.VISIBLE);
            //隐藏所有布局，点击搜索之后再变化
            lv_set_staff_clash.setVisibility(View.GONE);
            bottom_fragment.setVisibility(View.GONE);
            ll_item_layout.setVisibility(View.GONE);
            ////实时监控
            searchbar_content.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                    if (s.length() > 0) {
                        searchbar_del.setVisibility(View.VISIBLE);
                        searchbar_search.setText(I18NHelper.getText("crm.layout.layout_select_product.1825")/* 搜索 */);
                        isSearch = true;
                    } else {
                        searchbar_del.setVisibility(View.GONE);
                        searchbar_search.setText(I18NHelper.getText("commonfunc.dialog_fragment_fsmail_choose_attachment_menu.text.cancel")/* 取消 */);
                        isSearch = false;
                    }

                }

                @Override
                public void afterTextChanged(Editable s) {

                }
            });
            InputMethodManager imm = (InputMethodManager) getActivity().getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.toggleSoftInput(0, InputMethodManager.HIDE_NOT_ALWAYS);

        } else if (i1 == R.id.searchbar_search) {
            InputMethodManager imm;//搜索or取消，根据搜索框里面的字
            if (isSearch) {
                //搜索，获取输入的文字，进行匹配 byIds
                String serchKey = searchbar_content.getText().toString().trim();
                objects.clear();
                tv_no_join_check.setTextColor(getResources().getColor(R.color.gray));
                for (int i = 0; i < mList.size(); i++) {
                    //mList里面有两种可能，一个是AEmpSimpleEntity 一个是HaveRule
                    if (mList.get(i).name instanceof HaveRule) {
                        //包括，符合规则，一个新的列表
                        AEmpSimpleEntity byId = null;
                        try {
                            byId = FSContextManager.getCurUserContext().getContactDbHelper().getAEmpSimpleEntityDao()
                                    .findById(((HaveRule) mList.get(i).name).userId);
                        } catch (DbException e) {
                            e.printStackTrace();
                        }
                        if (byId.name.indexOf(serchKey) != -1) {
                            //    LogUtils.i("log", byIds.get(i).name);
                            objects.add(mList.get(i));
                            if (clashStaffBeenMap.get(byId.employeeID)) {
                                tv_no_join_check.setTextColor(getResources().getColor(R.color.orange));
                            }
                        }
                    } else if (mList.get(i).name instanceof AEmpSimpleEntity) {
                        if (((AEmpSimpleEntity) (mList.get(i).name)).name.indexOf(serchKey) != -1) {
                            //包括，符合规则，一个新的列表
                            // LogUtils.i("log", byIds.get(i).name);
                            objects.add(mList.get(i));
                            if (clashStaffBeenMap.get(((AEmpSimpleEntity) (mList.get(i).name)).employeeID)) {
                                tv_no_join_check.setTextColor(getResources().getColor(R.color.orange));
                            }
                        }
                    }


                }

                clashStaffDetailsAdapter.addDataList(objects);
                lv_set_staff_clash.setAdapter(clashStaffDetailsAdapter);
                lv_set_staff_clash.setVisibility(View.VISIBLE);
                if (objects.size() > 0) {
                    bottom_fragment.setVisibility(View.VISIBLE);
                    ll_item_layout.setVisibility(View.VISIBLE);
                } else {
                    bottom_fragment.setVisibility(View.GONE);
                    ll_item_layout.setVisibility(View.GONE);
                }
            } else {
                searchbar_search.setVisibility(View.GONE);
                tv_edit_flag.setVisibility(View.VISIBLE);
                searchbar_content.setVisibility(View.GONE);
                lv_set_staff_clash.setVisibility(View.VISIBLE);
                bottom_fragment.setVisibility(View.VISIBLE);
                ll_item_layout.setVisibility(View.VISIBLE);
                clashStaffDetailsAdapter.addDataList(mList);
                lv_set_staff_clash.setAdapter(clashStaffDetailsAdapter);
            }
            imm = (InputMethodManager) getActivity().getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.toggleSoftInput(0, InputMethodManager.HIDE_NOT_ALWAYS);

        } else if (i1 == R.id.searchbar_del) {
            searchbar_content.setText("");

        } else if (i1 == R.id.ll_item_layout) {
            clashStaffBeenMap.clear();
            if (!isSelectAll) {
                cboSelectAll.setChecked(true);
                isSelectAll = true;
                for (int i = 0; i < mList.size(); i++) {
                    if (mList.get(i).name instanceof HaveRule) {
                        clashStaffBeenMap.put(((HaveRule) (mList.get(i).name)).userId, true);
                    } else if (mList.get(i).name instanceof AEmpSimpleEntity) {
                        clashStaffBeenMap.put(((AEmpSimpleEntity) (mList.get(i).name)).employeeID, true);
                    }
                }
                bottom_fragment.setEnabled(true);
            } else {
                cboSelectAll.setChecked(false);
                isSelectAll = false;
                for (int i = 0; i < mList.size(); i++) {
                    if (mList.get(i).name instanceof HaveRule) {
                        clashStaffBeenMap.put(((HaveRule) (mList.get(i).name)).userId, false);
                    } else if (mList.get(i).name instanceof AEmpSimpleEntity) {
                        clashStaffBeenMap.put(((AEmpSimpleEntity) (mList.get(i).name)).employeeID, false);
                    }
                }
                bottom_fragment.setEnabled(false);
            }
            for (int i = 0; i < mList.size(); i++) {
                if (mList.get(i).name instanceof HaveRule) {
                    if (clashStaffBeenMap.get(((HaveRule) (mList.get(i).name)).userId)) {
                        tv_no_join_check.setTextColor(getResources().getColor(R.color.orange));
                        break;
                    } else {
                        tv_no_join_check.setTextColor(getResources().getColor(R.color.gray));
                    }
                } else if (mList.get(i).name instanceof AEmpSimpleEntity) {
                    if (clashStaffBeenMap.get(((AEmpSimpleEntity) (mList.get(i).name)).employeeID)) {
                        tv_no_join_check.setTextColor(getResources().getColor(R.color.orange));
                        break;
                    } else {
                        tv_no_join_check.setTextColor(getResources().getColor(R.color.gray));
                    }
                }

            }
            clashStaffDetailsAdapter.notifyDataSetChanged();

        } else if (i1 == R.id.bottom_fragment) {//点击下面的按钮，根据集合数据变化
            mListEffect.clear();
            mListUnEffect.clear();
            mListUnEffectHave.clear();
//                SaveRuleUsersArgs args = new SaveRuleUsersArgs();
            for (int j = 0; j < mList.size(); j++) {
                if (mList.get(j).name instanceof HaveRule) {
                    if (clashStaffBeenMap.get(((HaveRule) (mList.get(j).name)).userId)) {
                        mListEffect.add(((HaveRule) (mList.get(j).name)).userId);
                    } else {
                        //有规则的单独存，不要和不参与考勤的一起存，传回来保证数据正确
                        mListUnEffectHave.add(((HaveRule) (mList.get(j).name)));
                    }
                } else if (mList.get(j).name instanceof AEmpSimpleEntity) {
                    if (clashStaffBeenMap.get(((AEmpSimpleEntity) (mList.get(j).name)).employeeID)) {
                        mListEffect.add(((AEmpSimpleEntity) mList.get(j).name).employeeID);
                    } else {
                        mListUnEffect.add(((AEmpSimpleEntity) mList.get(j).name).employeeID);
                    }
                }
            }
            //过滤出选出的人员回传服务器
//                String rule_id = getArguments().getString(SetStaffActivity.KEY_RULEID);
//                StaffListBean staffListBean = (StaffListBean) getArguments().getSerializable("staffListBean");
//                if (!TextUtils.isEmpty(rule_id)) {
//                    args.ruleId = rule_id;
//                }
//                args.deptIds =staffListBean. myList;
//                args.userIds = staffListBean. peopleList;
//                args.adminIds = staffListBean. adminList;
//                args.secondAdminIds = staffListBean. adminlookList;
//                /***/
            if (serializableExtra.effectUIds != null) {

                mListEffect.addAll(serializableExtra.effectUIds);
            }
//                args.effectUIds = mListEffect;
//                args.unEffectUIds = mListUnEffect;
//                AttendanceWebApi.saveRuleUsers(args, new WebApiExecutionCallback<SaveRuleUsersResult>() {
//                    @Override
//                    public TypeReference<WebApiResponse<SaveRuleUsersResult>> getTypeReference() {
//                        return new TypeReference<WebApiResponse<SaveRuleUsersResult>>() {
//                        };
//                    }
//
//                    @Override
//                    public Class<SaveRuleUsersResult> getTypeReferenceFHE() {
//                        return SaveRuleUsersResult.class;
//                    }
//
//                    @Override
//                    public void completed(Date date, SaveRuleUsersResult saveRuleUsersResult) {
//                        ToastUtils.show("使用本规则");
//                    }
//                });
            Intent intent = new Intent();
            CheckEffectInfoResult checkEffectInfoResult = new CheckEffectInfoResult();
            checkEffectInfoResult.effectUIds = mListEffect;
            checkEffectInfoResult.unEffectUIds = mListUnEffect;//包括不参加
            checkEffectInfoResult.haveRules = mListUnEffectHave;//有其他规则
            checkEffectInfoResult.total = serializableExtra.total;
            checkEffectInfoResult.conflict = mListUnEffect.size() + mListUnEffectHave.size();
            intent.putExtra(SetStaffActivity.CLASH_STAFF, checkEffectInfoResult);//回传数据
            getActivity().setResult(1100, intent);
            getActivity().finish();

        }

    }

    class ClashStaffDetailsAdapter extends AbsListBaseAdapter<myCity> implements StickyListHeadersAdapter {


        public ClashStaffDetailsAdapter() {

        }


        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder = null;
            if (convertView == null) {
                holder = new ViewHolder();
                convertView = mInflater.inflate(R.layout.clash_staff_details_item, null);
                View layoutView = convertView.findViewById(R.id.itemlayout);
                LinearLayout.LayoutParams p = (LinearLayout.LayoutParams) layoutView.getLayoutParams();
                layoutView.setLayoutParams(p);
                holder.txtName = (TextView) convertView.findViewById(R.id.txtName);
                holder.txtInfo = (TextView) convertView.findViewById(R.id.txtInfo);

                holder.cboSelect = (CheckBox) convertView.findViewById(R.id.cboSelect);
                holder.imgBLine = convertView.findViewById(R.id.bottom_line);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }
            //处理复选框
            // holder.cboSelect.setChecked(clashStaffBeenList.get(position).getSelect());

            if (getDataList().get(position).name instanceof HaveRule) {
                AEmpSimpleEntity byId = null;
                try {
                    byId = FSContextManager.getCurUserContext().getContactDbHelper().getAEmpSimpleEntityDao()
                            .findById(((HaveRule) getDataList().get(position).name).userId);
                } catch (DbException e) {
                    e.printStackTrace();
                }
                holder.txtInfo.setText(((HaveRule) getDataList().get(position).name).ruleName);
                if (null != byId) {
                    holder.cboSelect.setChecked(clashStaffBeenMap.get(byId.employeeID));
                    holder.txtName.setText(byId.name);
                }
            } else if (getDataList().get(position).name instanceof AEmpSimpleEntity) {
                holder.txtInfo.setText(I18NHelper.getText("xt.sign_clash.des.not_attend_sign")/* 不参与考勤 */);//((AEmpSimpleEntity) getDataList().get(position).name).department
                holder.txtName.setText(((AEmpSimpleEntity) getDataList().get(position).name).name);
                holder.cboSelect.setChecked(clashStaffBeenMap.get(((AEmpSimpleEntity) getDataList().get(position).name).employeeID));
            }
//            if (position==getDataList().size()-2) {
//                holder.imgBLine.setVisibility(View.INVISIBLE);
//            }

            return convertView;


        }


        @Override
        public View getHeaderView(int position, View convertView, ViewGroup parent) {
            convertView = mInflater.inflate(R.layout.clash_staff_details_header_layout, null);
            TextView txtLetter = (TextView) convertView.findViewById(R.id.letter_index);
            myCity item = getItem(position);
            if ("a".equals(item.nameSpell)) {
                txtLetter.setText(head[0]);
            } else if ("b".equals(item.nameSpell)) {
                txtLetter.setText(head[1]);
            }
            return convertView;
        }

        @Override
        public long getHeaderId(int position) {
            if (tv_edit_flag.getVisibility() == View.GONE) {
                return objects.get(position).id;
            } else {
                return mList.get(position).id;
            }
        }


    }

    public final class ViewHolder {
        public TextView txtName;
        public TextView txtInfo;
        public CheckBox cboSelect;
        public View imgBLine;// 底部分割线
    }

    //填充对象
    public static class myCity {
        public int id;//分组用
        public Object name;
        public String nameSpell;

        public myCity(Object name, String nameSpell, int id) {
            super();
            this.name = name;
            this.nameSpell = nameSpell;
            this.id = id;
        }
    }

}
