package com.facishare.fs.biz_function.subbiz_attendance_new.adapter;

import com.facishare.fs.i18n.I18NHelper;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Paint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import com.facishare.fslib.R;
import com.facishare.fs.biz_function.subbiz_attendance_new.RuleTimeGroupSettingActivity;
import com.facishare.fs.biz_function.subbiz_attendance_new.RuleWorkTimeSettingActivity;
import com.facishare.fs.biz_function.subbiz_attendance_new.TimeSelectUtils;
import com.facishare.fs.biz_function.subbiz_attendance_new.bean.RuleDetail;
import com.facishare.fs.biz_function.subbiz_attendance_new.bean.TimeGroup;
import com.facishare.fs.biz_function.subbiz_attendance_new.bean.WeekDay;
import com.facishare.fs.biz_function.subbiz_attendance_new.util.FormatUtils;
import com.facishare.fs.common_utils.FSScreen;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Created by liky on 2016/6/27.
 */
public class WorkTimeSettingAdapter extends AbsListBaseAdapter<WeekDay> {
    private final String[] mWeekStrArr = {I18NHelper.getText("xt.attendance_new_worktime_setting_item.text.on_monday")/* 周一 */, I18NHelper.getText("xt.datepickerview.text.on_tuesday")/* 周二 */, I18NHelper.getText("xt.datepickerview.text.on_wednesday")/* 周三 */, I18NHelper.getText("xt.datepickerview.text.thursday")/* 周四 */, I18NHelper.getText("xt.datepickerview.text.friday")/* 周五 */, I18NHelper.getText("xt.datepickerview.text.on_saturday")/* 周六 */, I18NHelper.getText("xt.datepickerview.text.sunday")/* 周日 */};
    private Context mContext;
    private ListView mListView;
    private LayoutInflater mInflater;
    private List<TimeGroup> mGeneralTimeGroup;
    private long mGeneralRuningHour;
    private int mMode;//0-固定，1-弹性，2-排班

    private static class ViewHolder {
        CheckBox cb;
        TextView weekTxt;
        TextView centerTxt;
        View edit;
        View divider;
        LinearLayout root;
    }

    public WorkTimeSettingAdapter(Context ctx, ListView parent, List<WeekDay> weekdayList,
                                  List<TimeGroup> generalTimeGroup, long generalRuningHour,
                                  int mode) {//内部都进行了拷贝数据
        mContext = ctx;
        mInflater = LayoutInflater.from(ctx);
        mListView = parent;
        mMode = mode;

        setGeneralTimeGroup(generalTimeGroup);
        setGeneralRuningHour(generalRuningHour);
        updateWeekDayDataList(weekdayList, true);
    }

    public void setGeneralTimeGroup(List<TimeGroup> generalTimeGroup) {
        if (generalTimeGroup != null && generalTimeGroup.size() != 0) {
            mGeneralTimeGroup = new ArrayList<>(generalTimeGroup);
        } else {
            mGeneralTimeGroup = new ArrayList<>();
            TimeGroup tg = new TimeGroup();
            tg.inTimeStr = "09:00";
            tg.outTimeStr = "18:00";
            tg.isNextDay = 0;
            mGeneralTimeGroup.add(tg);
        }
    }

    public List<TimeGroup> getGeneralTimeGroup() {
        return mGeneralTimeGroup;
    }

    public void setGeneralRuningHour(long generalRuningHour) {
        mGeneralRuningHour = (generalRuningHour <= 0) ? (8 * 3600) * 1000 : generalRuningHour;
    }

    public long getGeneralRuningHour() {
        return mGeneralRuningHour;
    }

    public void updateWeekDayDataList(List<WeekDay> list, boolean bSetWithGeneralValue) {//拷贝数据
        if (getCount() != 7) {
            generateWeekDayList();
        }

        List<Boolean> needDefList = new ArrayList<>(Arrays.asList(new Boolean[]{true, true, true, true, true, true, true}));
        if (list != null) {
            for (WeekDay weekday : list) {
                if (weekday.dayNum >= 0 && weekday.dayNum <= 6) {
                    WeekDay newObj = new WeekDay();
                    newObj.isWorkDay = weekday.isWorkDay;
                    newObj.dayNum = weekday.dayNum;
                    newObj.isSpecial = weekday.isSpecial;
                    newObj.workTime = weekday.workTime;
                    newObj.timeGroups = (weekday.timeGroups != null) ? new ArrayList<>(weekday.timeGroups) : null;

                    if (weekday.dayNum == 0) {
                        replaceItem(newObj, 6);
                        needDefList.set(6, false);
                    } else {
                        replaceItem(newObj, weekday.dayNum - 1);
                        needDefList.set(weekday.dayNum - 1, false);
                    }
                }
            }
        }

        for (int i = 0; i < needDefList.size(); i++) {
            if (needDefList.get(i) && bSetWithGeneralValue) {
                setWeekDayWithGeneralValue(i);
                if (i == 5 || i == 6) {
                    setWeekDayFlag(i, 0);
                } else {
                    setWeekDayFlag(i, 1);
                }
            }
        }
    }

    public void setWeekDayWithGeneralValue(int index) {
        if (getCount() != 7) {
            generateWeekDayList();
        }

        WeekDay weekDay = getItem(index);
        if (weekDay == null) {
            return;
        }

        weekDay.workTime = mGeneralRuningHour;
        weekDay.timeGroups = new ArrayList<>(mGeneralTimeGroup);
        weekDay.isSpecial = 1;//TODO 修改了值，一定要设置isSpecial为1
    }

    public List<WeekDay> getWeekDayDataList() {
        if (getCount() != 7) {
            generateWeekDayList();
        }
        isSpecialSettingProcess();
        return new ArrayList<>(mDataList);
    }

    /**
     * 保证数据集的数量恒为7, 且数组index 0-6 分别对应周一到周日的数据
     */
    private void generateWeekDayList() {
        clearDataList();
        for (int i = 0; i < 7; i++) {//生成默认数据，且数据顺序为周一、二、三、四、五、六、日
            WeekDay weekday = new WeekDay();
            if (i < 6) {
                weekday.dayNum = i + 1;
            } else {
                weekday.dayNum = 0;
            }
            addItem(weekday);
        }
    }

    private void setWeekDayFlag(int index, int isWorkDay) {
        if (getCount() != 7) {
            generateWeekDayList();
        }

        WeekDay weekDay = getItem(index);
        if (weekDay == null) {
            return;
        }

        weekDay.isWorkDay = isWorkDay;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        View view = convertView;
        ViewHolder holder;
        if (view == null) {
            view = mInflater.inflate(R.layout.attendance_new_worktime_setting_item, parent, false);
            holder = new ViewHolder();
            holder.cb = (CheckBox) view.findViewById(R.id.cb);
            holder.weekTxt = (TextView) view.findViewById(R.id.week_txt);
            holder.centerTxt = (TextView) view.findViewById(R.id.center_txt);
            holder.edit = view.findViewById(R.id.edit);
            holder.divider = view.findViewById(R.id.divider);
            holder.root = (LinearLayout) view.findViewById(R.id.root);
            view.setTag(holder);
        } else {
            holder = (ViewHolder) view.getTag();
        }

        processUI(holder, position);
        return view;
    }

    @Override
    public int getItemViewType(int position) {
        return 0;
    }

    @Override
    public int getViewTypeCount() {
        return 1;
    }

    private void processUI(final ViewHolder holder, final int position) {
        if (position >= 0 && position <= 7) {
            holder.weekTxt.setText(mWeekStrArr[position]);
        } else {
            holder.weekTxt.setText("");
        }

        final WeekDay weekDay = getItem(position);

        holder.cb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {//回调先注册
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (weekDay != null) {
                    if (isChecked) {
                        weekDay.isWorkDay = 1;
                    } else {
                        weekDay.isWorkDay = 0;
                    }
                    weekDay.isSpecial = 1;//TODO 修改了值，一定要设置isSpecial为1
                    notifyDataSetChanged();
                }
            }
        });

        if (weekDay != null) {
            holder.cb.setChecked((weekDay.isWorkDay == 1) ? true : false);//setChecked会引发回调
        } else {
            holder.cb.setChecked(false);
        }

        holder.root.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (holder.cb.isChecked()) {
                    holder.cb.setChecked(false);
                } else {
                    holder.cb.setChecked(true);
                }
            }
        });

        holder.centerTxt.setText(getCenterString(weekDay, holder.centerTxt.getPaint()));
        holder.edit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {//跳向
                switch (mMode) {
                    case 0://固定
                        //跳转时间组设置页面
                        Intent intent = new Intent();
                        intent.setClass(mContext, RuleTimeGroupSettingActivity.class);
                        intent.putExtra(RuleTimeGroupSettingActivity.TIMEGROUP_SETTING_TITLE_KEY,
                                        I18NHelper.getFormatText("xt.attendance_new_advanced_setting_specialday_item.text.s_work01",mWeekStrArr[position])/* {0}工作时段 */);
                        intent.putExtra(RuleTimeGroupSettingActivity.TIMEGROUP_SETTING_LIST_KEY, (Serializable) weekDay.timeGroups);
                        intent.putExtra(RuleTimeGroupSettingActivity.TIMEGROUP_SETTING_EXTRADATA_INT_KEY, position);
                        RuleDetail ruleDetail = (RuleDetail) ((Activity) mContext).getIntent().getSerializableExtra("rule_detail_key");
                        if(ruleDetail!=null){
                            intent.putExtra("rule_detail_key", ruleDetail);
                        }
                        ((Activity) mContext).startActivityForResult(intent, RuleWorkTimeSettingActivity.WORKTIME_SETTING_SINGLE_TIMEGROUP_SETTING_REQ_CODE);
                        break;
                    case 1://弹性
                        TimeSelectUtils.getChooseTimeDialog(mContext, I18NHelper.getText("wq.rule_work_time_setting_activity.text.worktime_long")/* 选择工作时长 */, new String[]{I18NHelper.getText("xt.holiday_detail_header.des.hour")/* 小时 */, I18NHelper.getText("wq.enterprisereportadapter.text.minute")/* 分钟 */}, new Date(weekDay.workTime),
                                new TimeSelectUtils.DateDialogListener() {
                                    @Override
                                    public void onClickBack(Date date) {
                                        weekDay.workTime = date.getTime();//忽略时区，0000-00-00
                                        notifyDataSetChanged();
                                    }
                                }
                        ).show();
                        break;
                    default:
                        break;
                }
            }
        });

        if (position == getCount() - 1) {
            holder.divider.setVisibility(View.GONE);
        } else {
            holder.divider.setVisibility(View.VISIBLE);
        }
    }

    private String getCenterString(WeekDay weekDay, Paint paint) {
        if (weekDay == null) {
            return "";
        }

        switch (mMode) {
            case 0://固定
                if (weekDay.isWorkDay == 1) {
                    return FormatUtils.timeGroupList2String(weekDay.timeGroups, true, paint);
                } else {
                    return I18NHelper.getText("wq.rule_detail_special_list_manager.text.rest")/* 休息 */;
                }
            case 1://弹性
                if (weekDay.isWorkDay == 1) {
                    return FormatUtils.runingHour2String(new Date(weekDay.workTime));
                } else {
                    return I18NHelper.getText("wq.rule_detail_special_list_manager.text.rest")/* 休息 */;
                }
            default:
                return "";
        }
    }

    private boolean isTimeGroupListEqual(List<TimeGroup> list1, List<TimeGroup> list2) {
        Paint paint = new Paint();
        paint.setTextSize(FSScreen.sp2px(10));

        String listString1 = FormatUtils.timeGroupList2String(list1, false, paint);
        String listString2 = FormatUtils.timeGroupList2String(list2, false, paint);
        return listString1.equals(listString2);
    }

    private void isSpecialSettingProcess() {
        for (WeekDay weekDay : mDataList) {
            switch (mMode) {
                case 0://固定
                    if(weekDay.isWorkDay == 0) {
                        weekDay.isSpecial = 0;
                    } else {
                        if (!isTimeGroupListEqual(weekDay.timeGroups, mGeneralTimeGroup)) {
                            weekDay.isSpecial = 1;
                        } else {
                            weekDay.isSpecial = 0;
                        }
                    }
                    break;
                case 1://弹性
                    if(weekDay.isWorkDay == 0) {
                        weekDay.isSpecial = 0;
                    } else {
                        if (weekDay.workTime != mGeneralRuningHour) {
                            weekDay.isSpecial = 1;
                        } else {
                            weekDay.isSpecial = 0;
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    }

}
