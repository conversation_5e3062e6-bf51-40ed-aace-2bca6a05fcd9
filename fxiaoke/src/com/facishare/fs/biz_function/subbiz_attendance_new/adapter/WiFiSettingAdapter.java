package com.facishare.fs.biz_function.subbiz_attendance_new.adapter;

import com.facishare.fs.i18n.I18NHelper;
import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.facishare.fslib.R;
import com.facishare.fs.biz_function.subbiz_attendance_new.bean.WifiInfo;
import com.facishare.fs.biz_function.subbiz_attendance_new.dialog.AttendanceDialog;
import com.facishare.fs.biz_function.subbiz_attendance_new.view.DynamicList;

import java.util.List;

/**
 * Created by liky on 2016/6/27.
 */
public class WiFiSettingAdapter extends DynamicList.AbsDynamicListAdapter<WifiInfo> {

    private Context mContext;
    private LayoutInflater mInflater;
    private DynamicList mDynamicList;
    private RootItemClicked mRootItemClickedListerner;

    public interface RootItemClicked {
        void onClick(View v, WifiInfo wifiInfo);
    }

    private static class ViewHolder {
        LinearLayout root;
        TextView wifiNameTxt;
        TextView wifiMacTxt;
        ImageView wifiDelete;
        LinearLayout wifiZone;
        LinearLayout locationZone;
    }

    /**
     *
     * @param ctx
     * @param parent
     * @param wifiInfoList
     * @param needToClone  不论true还是false，外部对数据集操作尽可能直接使用adapter的封装方法处理，否则容易有问题，尤其needToClone=false时
     */
    public WiFiSettingAdapter(Context ctx, DynamicList parent, List<WifiInfo> wifiInfoList, boolean needToClone) {
        mContext = ctx;
        mInflater = LayoutInflater.from(ctx);
        mDynamicList = parent;

        if (needToClone) {
            addDataList(wifiInfoList);
        } else {
            attachDataSet(wifiInfoList);
        }
    }

    @Override
    public View getView(final int position, ViewGroup parent) {
        final View rootView = mInflater.inflate(R.layout.attendance_new_loaction_setting_item, parent, false);

        final ViewHolder holder = new ViewHolder();
        holder.root = (LinearLayout) rootView.findViewById(R.id.root);
        holder.wifiNameTxt = (TextView) rootView.findViewById(R.id.wifi_name_txt);
        holder.wifiMacTxt = (TextView) rootView.findViewById(R.id.wifi_mac_txt);
        holder.wifiDelete = (ImageView) rootView.findViewById(R.id.wifi_delete);
        holder.wifiZone = (LinearLayout) rootView.findViewById(R.id.wifi_zone);
        holder.locationZone = (LinearLayout) rootView.findViewById(R.id.location_zone);

        holder.wifiZone.setVisibility(View.VISIBLE);
        holder.locationZone.setVisibility(View.GONE);

        final WifiInfo wifiInfo = getItem(position);
        StringBuilder sb =new StringBuilder();
        if (wifiInfo !=null && !TextUtils.isEmpty(wifiInfo.bssId)) {
            sb.append(wifiInfo.bssId.trim());
            if(!TextUtils.isEmpty(wifiInfo.desc)) {
                sb.append("(");
                sb.append(wifiInfo.desc);
                sb.append(")");
            }
        }
        holder.wifiNameTxt.setText(sb.toString());
        holder.wifiMacTxt.setText(wifiInfo.macAddress);

        holder.root.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(mRootItemClickedListerner != null) {
                    mRootItemClickedListerner.onClick(holder.root, wifiInfo);
                }
            }
        });

        holder.wifiDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AttendanceDialog.showDialog(mContext, AttendanceDialog.FLAG_CONTENT_1, new AttendanceDialog.DialogClickListener() {
                    @Override
                    public void onClick(AttendanceDialog dialog, View view) {
                        int i = view.getId();
                        if (i == R.id.left_btn) {
                            dialog.cancel();

                        } else if (i == R.id.right_btn) {//删除条目
                            mDynamicList.removeViewFromList(rootView);
                            dialog.cancel();

                        } else {
                        }
                    }
                }, null, null, new String[]{I18NHelper.getText("commonfunc.dialog_fragment_fsmail_choose_attachment_menu.text.cancel")/* 取消 */, I18NHelper.getText("av.common.string.confirm")/* 确定 */}, null, I18NHelper.getText("wq.wifi_setting_adapter.text.isremove_wifi_loca")/* 确定删除此条WiFi地址吗？ */);
            }
        });
        return rootView;
    }

    public void setRootItemClickedListerner(RootItemClicked rootItemClickedListerner) {
        mRootItemClickedListerner = rootItemClickedListerner;
    }
}
