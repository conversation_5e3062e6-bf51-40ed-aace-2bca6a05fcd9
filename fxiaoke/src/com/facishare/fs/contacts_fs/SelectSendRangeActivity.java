package com.facishare.fs.contacts_fs;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.billy.cc.core.component.CC;
import com.billy.cc.core.component.CCResult;
import com.billy.cc.core.component.CCUtil;
import com.facishare.fs.BaseFragmentActivity;
import com.facishare.fs.Global;
import com.facishare.fs.biz_feed.utils.FeedSP;
import com.facishare.fs.biz_feed.utils.FeedSP.XData;
import com.facishare.fs.biz_personal_info.ContactsFindUilts;
import com.facishare.fs.biz_session_msg.utils.FeedBizUtils;
import com.facishare.fs.biz_session_msg.utils.MsgUtils;
import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.contacts_fs.beans.ISelectSendRang;
import com.facishare.fs.contacts_fs.customerservice.util.ISelectDepTypeListenerUtil;
import com.facishare.fs.contacts_fs.datactrl.ContactConfigDataUtils;
import com.facishare.fs.contacts_fs.datactrl.ContactDbOp;
import com.facishare.fs.contacts_fs.datactrl.ContactOperator;
import com.facishare.fs.contacts_fs.datactrl.ICacheEmployeeData;
import com.facishare.fs.contacts_fs.dep_level.fragment.SelectInDepLevelWithCrumbFragment;
import com.facishare.fs.contacts_fs.fragment.ContactSelectBarFrag;
import com.facishare.fs.contacts_fs.fragment.ContactSelectBarFrag.ShowType;
import com.facishare.fs.contacts_fs.fragment.ISelectEvent;
import com.facishare.fs.contacts_fs.fragment.SelectDepFragment;
import com.facishare.fs.contacts_fs.fragment.SelectEmpFragment;
import com.facishare.fs.contacts_fs.fragment.SelectEmpRoleFragment;
import com.facishare.fs.contacts_fs.fragment.SelectFreeFragment;
import com.facishare.fs.contacts_fs.fragment.SelectGroupFragment;
import com.facishare.fs.contacts_fs.fragment.SelectLastestFragment;
import com.facishare.fs.contacts_fs.fragment.SelectOutOppositePersonFragment;
import com.facishare.fs.contacts_fs.fragment.SelectOutPartnerFragment;
import com.facishare.fs.contacts_fs.fragment.SelectOutEnterpriseFragment;
import com.facishare.fs.contacts_fs.fragment.SelectOutTenantFragment;
import com.facishare.fs.contacts_fs.fragment.SelectStopEmpFragment;
import com.facishare.fs.contacts_fs.fragment.SelectUserGroupFragment;
import com.facishare.fs.contacts_fs.fragment.TotalSelectMapCtrl;
import com.facishare.fs.contacts_fs.fragment.TotalSelectMapCtrl.ISelectSummary;
import com.facishare.fs.contacts_fs.picker.DepartmentPicker;
import com.facishare.fs.contacts_fs.views.GroupSearchAct;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.pluginapi.ContactsHostManager;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.bi.BIConstant;
import com.facishare.fs.pluginapi.contact.beans.AEmpSimpleEntity;
import com.facishare.fs.pluginapi.contact.beans.AddTabData;
import com.facishare.fs.pluginapi.contact.beans.CSDataConfig;
import com.facishare.fs.pluginapi.contact.beans.CircleEntity;
import com.facishare.fs.pluginapi.contact.beans.CircleIndexLetter;
import com.facishare.fs.pluginapi.contact.beans.EmpIndexLetter;
import com.facishare.fs.pluginapi.contact.beans.OutOppositePersonDataBean;
import com.facishare.fs.pluginapi.contact.beans.OutOwner;
import com.facishare.fs.pluginapi.contact.beans.OutTenant;
import com.facishare.fs.pluginapi.contact.beans.RoleData;
import com.facishare.fs.pluginapi.contact.beans.SelectSendRangeConfig;
import com.facishare.fs.pluginapi.contact.beans.SendRangeData;
import com.facishare.fs.pluginapi.contact.beans.User;
import com.facishare.fs.pluginapi.contact.beans.UserGroupData;
import com.facishare.fs.pluginapi.select_contact.FSSelectOutOwnerEvent;
import com.facishare.fs.utils_fs.ToolUtils;
import com.facishare.fslib.R;
import com.fs.beans.beans.CommonSelectData;
import com.fxiaoke.cmviews.viewpager.ViewPagerCtrl;
import com.fxiaoke.dataimpl.contacts.ContactConfigProvider;
import com.fxiaoke.dataimpl.msg.MsgDataController;
import com.fxiaoke.fscommon.util.CommonDataContainer;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.fxlog.FCTimePoint;
import com.fxiaoke.lib.qixin.biz_ctrl.SessionCommonUtils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.database.DataSetObserver;
import android.os.Bundle;

import android.util.Pair;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.viewpager.widget.ViewPager.OnPageChangeListener;

import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.util.SparseArray;
import android.view.View;
import android.view.View.OnClickListener;

import de.greenrobot.event.EventBus;

/**
 * 选择发送范围页面
 */
public class SelectSendRangeActivity extends BaseFragmentActivity
        implements OnPageChangeListener, ISelectSummary, SelectGroupFragment.ISelectGroupDataSource, SelectEmpFragment.ISelectEmpDataSource, SelectDepFragment.ISelectDepDataSource {
    public static final int REQUEST_CODE_SEARCH = 0x01;
    public static final String IS_LOAD_LAST_DATA_KEY = "is_load_last_data_key";

    private ViewPagerCtrl mViewPagerCtrl;
    private SelectLastestFragment mLastestAtFragment;
    private SelectEmpFragment mEmpFragment;
    private SelectDepFragment mDepFragment;
    /**
     * 已停用员工
     */
    private SelectStopEmpFragment mStopEmpFragment;
    /**
     * 下游对接人列表
     */
    private SelectOutOppositePersonFragment mOppositePersonFragment;
    /**
     * 角色
     * */
    private SelectEmpRoleFragment mSelectEmpRoleFragment;
    /**
     * 用户组
     * */
    private SelectUserGroupFragment mSelectUserGroupFragment;
    /**
     * 外部人员
     * */
    private SelectOutEnterpriseFragment mSelectOutPersonFragment;

    /**
     * 外部企业
     * */
    private SelectOutEnterpriseFragment mSelectOutEnterpriseFragment;

    /**
     * 群对话
     */
    private SelectGroupFragment mGroupFragment;
    private List<SessionListRec> mGroupDatas;

    /**
     * 外部人员（按企业分组）
     */
    private SelectOutTenantFragment mOutTenantFragment;

    /**
     * 外部企业
     */
    private SelectOutPartnerFragment mOutPartnerFragment;

    /**
     * 顶层选人控制器
     */
    private TotalSelectMapCtrl mTotalSelectMapCtrl;

    private List<EmpIndexLetter> mEmpDatas;
    /**
     * 所有员工中分离出的非正常员工
     */
    private List<Integer> mEmpAbnormalDatas;
    private List<CircleIndexLetter> mDepDatas;

    /**
     * 混合数据
     */
    private List<CommonSelectData> mListCommonData;

    /**
     * 是回填的选人数据
     */
    private ArrayList<Integer> mBackFillEmpList;
    private ArrayList<Integer> mBackFillDepList;
    private ArrayList<Integer> mBackFillStopEmpList;
    private ArrayList<String> mBackFillGroupList;
    private Map<OutTenant, List<OutOwner>> mBackFillExternalEmpList;

//    OrgnizationOperator ops;

    private CSDataConfig mCSDataConfig;

    int myID;
    public static final String NO_SELECT_KEY_STRING = I18NHelper.getText("xt.selectsendrangeactivity.text.cannot_be_selected")/* 不能被选中 */;
    /*** 选人界面配置类----start-*/
    private SelectSendRangeConfig mSRangConfig;
    public static final String SELECT_RANG_CONFIG_KEY = "select_rang_config_key";

    /*** ------------end---------*/

    public static Intent getIntent(Context context, SelectSendRangeConfig selectSendRangeConfig) {
        Intent intent = new Intent(context, SelectSendRangeActivity.class);
        if (selectSendRangeConfig == null) return intent;
        if (selectSendRangeConfig.isHideDepLevel) {

        } else {
//            List lists = ContactsFindUilts.findCircleAll();
            ICacheEmployeeData cache = FSContextManager.getCurUserContext().getCacheEmployeeData();
            List<CircleEntity> lists = cache.getCirclesCache();
            if (lists != null && lists.size() > 0) {
                selectSendRangeConfig.isHideDepLevel = false;
                if (selectSendRangeConfig.empsMap != null && selectSendRangeConfig.empsMap.size() > 0) {
                    Set<Integer> keySet = selectSendRangeConfig.empsMap.keySet();
                    for (Integer id : keySet) {
                        ISelectDepTypeListenerUtil.getUtil().changeSelectedStatus(id, true);
                        ISelectDepTypeListenerUtil.getUtil().changeSelectedStatus(id, false);
                    }
                }
            } else {
                selectSendRangeConfig.isHideDepLevel = true;
            }
        }
        //兼容数量量过大闪退问题
        CommonDataContainer.getInstance().saveData(SELECT_RANG_CONFIG_KEY, selectSendRangeConfig);//新加配置文件
        return intent;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        FCTimePoint.start(FCLog.big_enterprise.getFunction() + " SelectSendRangeActivity");
        setContentView(R.layout.select_range_send);
        mTotalSelectMapCtrl = new TotalSelectMapCtrl(this);
        initIntent(savedInstanceState);
        initData();
        initView();
        initTitleEx();
        initObserver();


    }

    @Override
    protected void onResume() {
        super.onResume();
        FCTimePoint.end(FCLog.big_enterprise.getFunction() + " SelectSendRangeActivity");
    }

    void initIntent(Bundle savedInstanceState) {
        //新加配置文件
        mSRangConfig = (SelectSendRangeConfig) CommonDataContainer.getInstance().getAndRemoveSavedData(SELECT_RANG_CONFIG_KEY);
        if (mSRangConfig == null) {
            mSRangConfig = SelectSendRangeConfig.builder().build();
        }
        mCSDataConfig = mSRangConfig.csDataConfig;
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        CommonDataContainer.getInstance().saveData(SELECT_RANG_CONFIG_KEY, mSRangConfig);
    }

    /**
     * 过滤不存在的id
     */
    public static void filterSelectedList(List<Integer> selectedList) {
        if (selectedList == null) return;
        List<Integer> tempEmpList = new ArrayList<>();
        ICacheEmployeeData cache = FSContextManager.getCurUserContext().getCacheEmployeeData();
        SparseArray<AEmpSimpleEntity> allemps = cache.getEmployeeMapCache();
        for (Integer id : selectedList) {
            if (allemps.get(id) != null) {
                tempEmpList.add(id);
                ISelectDepTypeListenerUtil.getUtil().changeSelectedStatus(id, false);
            } else {
                ISelectDepTypeListenerUtil.getUtil().changeSelectedStatus(id, true);
            }
        }
        selectedList.clear();
        selectedList.addAll(tempEmpList);
    }

    private void initData() {
        myID = FSContextManager.getCurUserContext().getAccount().getEmployeeIntId();
        //picker清除数据
        DepartmentPicker.releasePicked();
        DepartmentPicker.showMeInPrivateMode = mSRangConfig.showMeInPrivateMode;

        if (mSRangConfig.isLastTab) {
            initLastestData();
        }
        //已停用员工Tab
        if (mSRangConfig.isShowStopEmpTob) {
            if (mSRangConfig.stopEmps != null) {
                mBackFillStopEmpList = (ArrayList<Integer>) mSRangConfig.mapToList(mSRangConfig.stopEmps);
            }
            if (mBackFillStopEmpList == null) {
                mBackFillStopEmpList = new ArrayList<>();
            }
            if (mEmpAbnormalDatas != null) {
                mBackFillStopEmpList.addAll(mEmpAbnormalDatas);
            }
            if (mSRangConfig.noSelf) {
                mBackFillStopEmpList.remove(Integer.valueOf(myID));
            }
            DepartmentPicker.pickStopEmployees(mBackFillStopEmpList, true);
        }
        if (mSRangConfig.outTenantMap != null && mSRangConfig.outTenantMap.size() > 0) {
            mBackFillExternalEmpList = new HashMap<>(mSRangConfig.outTenantMap);
            DepartmentPicker.setOutTenantMap(mBackFillExternalEmpList);
        } else {
            DepartmentPicker.setOutTenantMap(new HashMap<>());
        }


        //test code
//        List<OutTenant> OutTenantList = new ArrayList<>();
//        OutTenant  ot1 = new OutTenant();
//        ot1.outTenantId = "1";
//        ot1.outTenantName = "1name";
//        OutTenantList.add(ot1);
//
//        OutTenant  ot2 = new OutTenant();
//        ot2.outTenantId = "2";
//        ot2.outTenantName = "2name";
//        OutTenantList.add(ot2);
//
//        OutTenant  ot3 = new OutTenant();
//        ot3.outTenantId = "3";
//        ot3.outTenantName = "3name";
//        OutTenantList.add(ot3);
//
//        OutTenant  ot4 = new OutTenant();
//        ot4.outTenantId = "4";
//        ot4.outTenantName = "4name";
//        OutTenantList.add(ot4);
//
//        mSRangConfig.OutTenantList = OutTenantList;

        if (mSRangConfig.OutTenantList != null && mSRangConfig.OutTenantList.size() > 0) {

            DepartmentPicker.setOutTenantList(mSRangConfig.OutTenantList);
        } else {
            DepartmentPicker.setOutTenantList(new ArrayList<>());
        }


        pickCodeAll();
        setHideData();
        //picker备份数据
        DepartmentPicker.backup();
    }

    /**
     * pick最近tab中的‘全部’
     */
    private void pickCodeAll() {
        if (mSRangConfig != null && mSRangConfig.depsMap != null) {
            if (mBackFillDepList == null) {
                mBackFillDepList = new ArrayList<>();
            }
            if (mSRangConfig.depsMap.containsKey(Global.all)) {
                mBackFillDepList.add(Global.all);
            }
            if (mSRangConfig.depsMap.containsKey(BIConstant.ALL_CODE)) {
                mBackFillDepList.add(BIConstant.ALL_CODE);
            }
            if (mSRangConfig.depsMap.containsKey(BIConstant.ALL_CROSS_CODE)) {
                mBackFillDepList.add(BIConstant.ALL_CROSS_CODE);
            }
            pickDepData();
        }
    }

    ContactOperator depCo;

    public void loadDepData() {
        depCo = new ContactOperator();
        if (mDepFragment != null) {
            mDepFragment.setContactOperator(depCo);
        }
        depCo.addFilterDepIds(mSRangConfig.filterDepIds);
        depCo.addDesiDepIds(mSRangConfig.customDepIds);
        depCo.setIsInCustomMode(mSRangConfig.isInCustomMode);
//        mDepDatas = ops.getAllDepsWithOnlyIndexLetter();
        mDepDatas = depCo.getFilterOrderDepWithOnlyIndexLetter();
        mBackFillDepList = (ArrayList<Integer>) mSRangConfig.mapToList(mSRangConfig.depsMap);
        //回填部门,不存在部门的不回填
        filterDepData(mBackFillDepList);

    }

    private void pickDepData() {
        DepartmentPicker.pickDepartments(mBackFillDepList, true);
    }

    ContactOperator empCo;

    public void loadEmpData() {
        //picker回填数据处理，考虑过滤及指定id的情况
        if (mSRangConfig.isShowEmpTab) {
            empCo = new ContactOperator();
            if (mEmpFragment != null) {
                mEmpFragment.setContactOperator(empCo);
            }
            if (mSRangConfig.noSelf) {
                empCo.addFilterEmployeeIds(Collections.singletonList(myID));
            }
            empCo.addFilterEmployeeIds(mSRangConfig.filterEmpIds);
            empCo.addDesiEmployeeIds(mSRangConfig.customEmpIds);

            empCo.setFilterHasMail(mSRangConfig.showHasEmail);
            empCo.setFilterHasPhone(mSRangConfig.showHasPhone);
            empCo.setIsInCustomMode(mSRangConfig.isInCustomMode);

            mEmpDatas = empCo.getFilterOrderEmpWithOnlyIndexLetter();
            mBackFillEmpList = (ArrayList<Integer>) mSRangConfig.mapToList(mSRangConfig.empsMap);
            if (!mBackFillEmpList.isEmpty()) {

                List<User> userList = ContactsHostManager.getContacts().getUserByIds(mBackFillEmpList);
                mEmpAbnormalDatas = ContactHelper.filterAbnormalEmpIDList(userList);
                List<User> tempEmpList = new ArrayList<>();
                for(User user:userList){
                    if(user!= null && !mEmpAbnormalDatas.contains(user.getId())){
                        tempEmpList.add(user);
                    }
                }
                tempEmpList = ContactHelper.filterEmpUserList(tempEmpList, mSRangConfig.showHasEmail, mSRangConfig.showHasPhone);
                mBackFillEmpList.clear();
//                mEmpAbnormalDatas = ContactHelper.filterAbnormalEmpIDList(userList);
                userList.clear();
                for (int i = 0; i < tempEmpList.size(); i++) {
                    mBackFillEmpList.add(tempEmpList.get(i).getId());
                }
            }
            if (mSRangConfig.noSelf) {
                mBackFillEmpList.remove(Integer.valueOf(myID));
            }
            //回填员工,不存在员工的不回填
            filterSelectedList(mBackFillEmpList);//
        }
    }

    private void pickEmpData() {
        DepartmentPicker.pickEmployees(mBackFillEmpList, true);

    }


    public void loadGroupData() {
        if (mSRangConfig.isGrouptab) {
            mBackFillGroupList = (ArrayList<String>) mSRangConfig.mapToList(mSRangConfig.groupMap);
            mGroupDatas = SessionCommonUtils.getInnerGroupSessionList(context);
        }
    }

    private void pickGroupData() {
        if (mSRangConfig.isGrouptab) {
            mBackFillGroupList = (ArrayList<String>) mSRangConfig.mapToList(mSRangConfig.groupMap);
            if (mBackFillGroupList != null && mBackFillGroupList.size() > 0) {
                DepartmentPicker.pickGroups(mBackFillGroupList, true);
                if (mGroupDatas != null && mGroupDatas.size() > 0) {
                    for (int i = 0; i < mGroupDatas.size(); i++) {
                        String sessionId = mGroupDatas.get(i).getSessionId();
                        if (mBackFillGroupList.contains(sessionId)) {
                            String sessionName = mGroupDatas.get(i).getSessionName();
                            DepartmentPicker.togglePickGroup(sessionId, sessionName);
                        }
                    }
                }
            }
        }
    }


    //回填隔离数据
    void setHideData() {
        if (mSRangConfig != null && mSRangConfig.hideData != null) {
            if (mSRangConfig.hideData.getSelectEmp() != null) {
                DepartmentPicker.setHideEmps(mSRangConfig.mapToList(mSRangConfig.hideData.getSelectEmp()));
            }
            if (mSRangConfig.hideData.getSelectDep() != null) {
                DepartmentPicker.setHideDeps(mSRangConfig.mapToList(mSRangConfig.hideData.getSelectDep()));
            }
        }
    }

    void initView() {
        // viewpager
        mViewPagerCtrl = (ViewPagerCtrl) findViewById(R.id.pager);
        mViewPagerCtrl.init(this);
        mViewPagerCtrl.setOnPageChangeListener(this);
        if (mFragmentlists == null) {
            mFragmentlists = new ArrayList<>();
        }
        mFragmentlists.clear();
        int i = 0;
        if (mSRangConfig.isLastTab) {
            // 最近
            mLastestAtFragment = SelectLastestFragment.newInstance(this);
            mLastestAtFragment.setSelectEventLis(mTotalSelectMapCtrl);
            mLastestAtFragment.setData(mListCommonData, false);
            mLastestAtFragment.setSendRangeData(mSRangConfig.sendRangeData);
            mLastestAtFragment.setAtRangeData(mSRangConfig.atRangeData);
            mLastestAtFragment.setMydeps(getMyDeps());
            mLastestAtFragment.setMyOrgIdList(getMyOrgs());
            mLastestAtFragment.setEmployeeMaxCount(mSRangConfig.employeeMaxCount);
            mViewPagerCtrl.addTab(i++, I18NHelper.getText("fm.fsmail.FSMailContactsActivity.2")/* 最近 */, mLastestAtFragment);
            mFragmentlists.add(mLastestAtFragment);
        }

        if (mSRangConfig.isShowEmpTab) {
            //同事
//            mEmpFragment = SelectEmpFragment.getInstance(false, mSRangConfig.isHideDepLevel, mSRangConfig.employeeMaxCount);// 特殊需求，需要隐藏“按组织架构查看”
//            mEmpFragment.setSelectEventLis(mTotalSelectMapCtrl);
//            mEmpFragment.setContactOperator(empCo);
//            mEmpFragment.setDataSource(this);
//            mViewPagerCtrl.addTab(i++, TextUtils.isEmpty(mSRangConfig.innerTabTitle) ? I18NHelper.getText("xt"
//                    + ".selectuserupdateactivity.text.colleague")/* 同事 */ : mSRangConfig.innerTabTitle, mEmpFragment);

            boolean onlySelectEmp = false;
            ISelectEvent lis = mTotalSelectMapCtrl;
            ContactOperator contactOperator = empCo;
            SelectEmpFragment.ISelectEmpDataSource dataSource = this;
            int index = i++;
            String fragmentName = TextUtils.isEmpty(mSRangConfig.innerTabTitle) ?
                    I18NHelper.getText("xt.selectuserupdateactivity.text.colleague")/* 同事 */ :
                    mSRangConfig.innerTabTitle;
            Pair<SelectEmpFragment, SelectInDepLevelWithCrumbFragment> pair =
                    ContactConfigDataUtils.createSelectEmpFragmentForSendRange(this,
                            onlySelectEmp,  mSRangConfig.isHideDepLevel, mSRangConfig.employeeMaxCount, lis,
                            contactOperator, dataSource, mViewPagerCtrl, index, fragmentName);
            mEmpFragment = pair.first;
            mFragmentlists.add(mEmpFragment);
        }

        if (mSRangConfig.OutTenantList != null && mSRangConfig.OutTenantList.size() > 0) {
            // 外部企业
            mOutPartnerFragment = SelectOutPartnerFragment.newInstance(SelectOutTenantFragment.USAGE_SELECT_EMP, mSRangConfig.OutTenantList);
            String outTenantTabTitle = TextUtils.isEmpty(mSRangConfig.outTenantTabTitle) ?
                    I18NHelper.getText("fx.contacts_fs.title.partner_enterprise")/* 伙伴企业 */ : mSRangConfig.outTenantTabTitle;
            mViewPagerCtrl.addTab(i++, outTenantTabTitle, mOutPartnerFragment);
            mOutPartnerFragment.setSelectEventListener(mTotalSelectMapCtrl);
            mFragmentlists.add(mOutPartnerFragment);
        }

        if (mSRangConfig.outTenantMap != null && mSRangConfig.outTenantMap.size() > 0) {
            // 合作伙伴
            mOutTenantFragment = SelectOutTenantFragment.newInstance(SelectOutTenantFragment.USAGE_SELECT_EMP, mSRangConfig.outTenantMap,
                    mSRangConfig.employeeMaxCount);
            mOutTenantFragment.setSelectEventListener(mTotalSelectMapCtrl);
            String outOwnerTabTitle = TextUtils.isEmpty(mSRangConfig.outOwnerTabTitle) ?
                    I18NHelper.getText("crm.flowpropeller.changeHandler.externalContacts")/*外部联系人*/ : mSRangConfig.outOwnerTabTitle;
            mViewPagerCtrl.addTab(i++, outOwnerTabTitle, mOutTenantFragment);
            mFragmentlists.add(mOutTenantFragment);
        }

        if (mSRangConfig.isShowDepTab) {
            // 部门
//            mDepFragment = SelectDepFragment.newInstance(mSRangConfig.isHideDepLevel);
//            mDepFragment.setShowMyDepartments(mCSDataConfig == null || mCSDataConfig.mShowMyDep);
//            mDepFragment.setDataSource(this);
//            mDepFragment.setContactOperator(depCo);
//            mDepFragment.setSelectEventLis(mTotalSelectMapCtrl);
//            mViewPagerCtrl.addTab(i++, I18NHelper.getText("xt.addressbook.departments", "部门")/* 部门 */, mDepFragment);
            ISelectEvent lis = mTotalSelectMapCtrl;
            ContactOperator contactOperator = empCo;
            SelectDepFragment.ISelectDepDataSource dataSource = this;
            int index = i++;
            String fragmentName = I18NHelper.getText("xt.addressbook.departments", "部门")/* 部门 */ ;
            Pair<SelectDepFragment, SelectInDepLevelWithCrumbFragment> pair =
                    ContactConfigDataUtils.createSelectDepFragmentForSendRange(this, mSRangConfig.isHideDepLevel,
                            mCSDataConfig == null || mCSDataConfig.mShowMyDep, lis,contactOperator, dataSource,
                            mViewPagerCtrl, index, fragmentName);
            mDepFragment = pair.first;
            mFragmentlists.add(mDepFragment);
        }


        if (mSRangConfig.isGrouptab) {
            // 群会话
            mGroupFragment = SelectGroupFragment.newInstance(this);
            mGroupFragment.setSelectEventLis(mTotalSelectMapCtrl);
            mViewPagerCtrl.addTab(i++, I18NHelper.getText("qx.session.default.group_default_title")/* 群对话 */, mGroupFragment);
            mGroupFragment.setDataSource(this);
            mFragmentlists.add(mGroupFragment);
        }

        if (mSRangConfig.isShowStopEmpTob) {
            //已停用员工
            mStopEmpFragment = SelectStopEmpFragment
                    .getInstance()
                    .setConfig(mSRangConfig);
            mStopEmpFragment.setSelectEventLis(mTotalSelectMapCtrl);
            mViewPagerCtrl.addTab(i++, I18NHelper.getText("wq.projecttaskvo.text.terminated")/* 已停用 */, mStopEmpFragment);
            mFragmentlists.add(mStopEmpFragment);
        }

        //下游对接人列表
        if(mSRangConfig.isShowOutOppositePerson){
            mOppositePersonFragment = new SelectOutOppositePersonFragment().newInstance();
            mOppositePersonFragment.setOnlyChooseOne(mSRangConfig.mOnlyChooseOne);
            mOppositePersonFragment.setFilterUserIds(mSRangConfig.filterEmpIds);
            mOppositePersonFragment.setSelectOutOppositePersons(mSRangConfig.selectOutOppositePersions);
            mOppositePersonFragment.setISelectSummary(this);
            String outOppositePersonTabTitle=TextUtils.isEmpty(mSRangConfig.outOppositePersonTabTitle)
                    ?I18NHelper.getText("cml.crm.approval.external_employees")/*外部人员*/
                    :mSRangConfig.outOppositePersonTabTitle;
            mViewPagerCtrl.addTab(i++, outOppositePersonTabTitle, mOppositePersonFragment);
            mFragmentlists.add(mOppositePersonFragment);
        }

        if(mSRangConfig.isShowOutPerson){
            //外部人员
            mSelectOutPersonFragment = new SelectOutEnterpriseFragment().newInstance();
            mSelectOutPersonFragment.setFilterEmpIds(mSRangConfig.filterOutEmpIds);
            mSelectOutPersonFragment.setISelectSummary(this);
            mSelectOutPersonFragment.setOnlyChooseOne(mSRangConfig.mOnlyChooseOne);
            String userGroupTitle=TextUtils.isEmpty(mSRangConfig.outPersonTabTitle)
                    ?I18NHelper.getText("cml.crm.approval.external_employees")/*外部人员*/
                    :mSRangConfig.outPersonTabTitle;
            mViewPagerCtrl.addTab(i++, userGroupTitle, mSelectOutPersonFragment);
            mFragmentlists.add(mSelectOutPersonFragment);
        }
        if(mSRangConfig.isShowOutEnterprise){
            //下游企业
            mSelectOutEnterpriseFragment = new SelectOutEnterpriseFragment().newInstance();
            mSelectOutEnterpriseFragment.setSelectEnterprise(true);
            mSelectOutEnterpriseFragment.setOnlyChooseOne(mSRangConfig.mOnlyChooseOne);
            String userGroupTitle=TextUtils.isEmpty(mSRangConfig.outEnterpriseTabTitle)
                    ?I18NHelper.getText("fx.contacts_fs.title.out_enterprise.01")/*下游企业*/
                    :mSRangConfig.outEnterpriseTabTitle;
            mViewPagerCtrl.addTab(i++, userGroupTitle, mSelectOutEnterpriseFragment);
            mFragmentlists.add(mSelectOutEnterpriseFragment);
        }

        if(mSRangConfig.isShowUserGroupTab){
            //用户组
            mSelectUserGroupFragment = new SelectUserGroupFragment().newInstance()
                    .setOnlyChooseOne(mSRangConfig.mOnlyChooseOne)
                    .setISelectSummary(this)
                    .setSelectOutTeam(mSRangConfig.isSelectOutTeam);
            String userGroupTitle = TextUtils.isEmpty(mSRangConfig.userGroupTabTitle)
                    ? I18NHelper.getText("fx.contacts_fs.title.user_group_tab")/*用户组*/
                    : mSRangConfig.userGroupTabTitle;
            mViewPagerCtrl.addTab(i++, userGroupTitle, mSelectUserGroupFragment);
            mFragmentlists.add(mSelectUserGroupFragment);
        }

        if(mSRangConfig.isShowRoleTab) {
            //角色
            mSelectEmpRoleFragment = new SelectEmpRoleFragment().newInstance();
            mSelectEmpRoleFragment.setOnlyChooseOne(mSRangConfig.mOnlyChooseOne);

            mSelectEmpRoleFragment.setSelectOutTeam(mSRangConfig.isSelectOutTeam);
            String roleTitle=TextUtils.isEmpty(mSRangConfig.roleTabTitle)?I18NHelper.getText("crm.type.CoreObjType.2464")/*角色*/
                    :mSRangConfig.roleTabTitle;
            mViewPagerCtrl.addTab(i++, roleTitle, mSelectEmpRoleFragment);
            mFragmentlists.add(mSelectEmpRoleFragment);
        }

        //自定义tab
        i = freeTabInit(i);

        mViewPagerCtrl.commitTab();

        // 选人顶层控制器
        boolean onlyChooseOne = mSRangConfig.mOnlyChooseOne;
        mTotalSelectMapCtrl.setInitData(onlyChooseOne, FSContextManager.getCurUserContext().getAccount().getEmployeeIntId(), 0, null);
        mTotalSelectMapCtrl.setSendRangeData(mSRangConfig.sendRangeData);
        mTotalSelectMapCtrl.setAtRangeData(mSRangConfig.atRangeData);
        mTotalSelectMapCtrl.setISelectSummary(this);

        // 底部碎片
        if (!onlyChooseOne) {
            FragmentTransaction ft_bottom = getSupportFragmentManager().beginTransaction();
            ContactSelectBarFrag selectBarFrag = new ContactSelectBarFrag();
            selectBarFrag.setConfigData(mSRangConfig);
            selectBarFrag.setShowType(ShowType.Text);
            selectBarFrag.setConfirmClickListener(new OnClickListener() {

                public void onClick(View v) {
                    onClickOK(null);
                }
            });
            ft_bottom.add(R.id.bottom_fragment, selectBarFrag);
            ft_bottom.commitAllowingStateLoss();
        }
        if (i == 1) {
            if (mViewPagerCtrl != null) {
                mViewPagerCtrl.getTitleLayout().setVisibility(View.GONE);
            }
        }
        mViewPagerCtrl.setOffscreenPageLimit(mFragmentlists.size());
    }

    List<ISelectSendRang> mFragmentlists;

    private int freeTabInit(int i) {
        if (mSRangConfig.addTabDatas != null && mSRangConfig.addTabDatas.size() > 0) {
            for (int y = 0; y < mSRangConfig.addTabDatas.size(); y++) {
                AddTabData atd = (AddTabData) mSRangConfig.addTabDatas.get(y);
                List<CommonSelectData> commSd = new ArrayList<>();
                if (atd.tabEmpDatalist != null && atd.tabEmpDatalist.size() > 0) {
                    List<AEmpSimpleEntity> empList = ContactDbOp.findEmployeesByIds(atd.tabEmpDatalist);
                    String nameOrder = null;
                    if (atd.nameOrdermaps != null && atd.nameOrdermaps.size() > 0) {
                        nameOrder = atd.nameOrdermaps.get(AddTabData.EMP_TYPE_KEY);
                    }
                    for (AEmpSimpleEntity ae : empList) {
                        commSd.add(new CommonSelectData(ae.name, AddTabData.EMP_TYPE_KEY, nameOrder != null ? nameOrder : I18NHelper.getText("xt.selectuserupdateactivity.text.colleague")/* 同事 */, ae.employeeID, false, 0, 0));
                    }
                }
                if (atd.tabDepDatalist != null && atd.tabDepDatalist.size() > 0) {
                    String nameOrder = null;
                    if (atd.nameOrdermaps != null && atd.nameOrdermaps.size() > 0) {
                        nameOrder = atd.nameOrdermaps.get(AddTabData.DEP_TYPE_KEY);
                    }
                    for (Integer depId : atd.tabDepDatalist) {
                        CircleEntity depEntity = ContactDbOp.findCircleEntityById(depId);
                        if (depEntity == null) continue;
                        commSd.add(new CommonSelectData(depEntity.getI18NName(), AddTabData.DEP_TYPE_KEY, nameOrder != null ? nameOrder : I18NHelper.getText("xt.addressbook.departments", "部门")/* 部门 */, depEntity.circleID, false, 0, 0));
                    }
                }
                if (atd.tabFreeData != null && atd.tabFreeData.size() > 0) {
                    String nameOrder = null;
                    if (atd.nameOrdermaps != null && atd.nameOrdermaps.size() > 0) {
                        nameOrder = atd.nameOrdermaps.get(AddTabData.FREE_TYPE_KEY);
                    }
                    for (SendRangeData sendRd : atd.tabFreeData) {
                        CommonSelectData csd = new CommonSelectData(sendRd.getName(), AddTabData.FREE_TYPE_KEY,
                                nameOrder != null ? nameOrder : I18NHelper.getText("crm.layout.frag_filter_time_select.3544")/* 自定义 */, sendRd.getId(), false, 0, 0);
                        csd.sRData = sendRd;
                        csd.setDisplayName(sendRd.getRangeString());
                        commSd.add(csd);
                    }
                }

                SelectFreeFragment mfreeFragment = SelectFreeFragment.newInstance(this);
                mfreeFragment.setSelectEventLis(mTotalSelectMapCtrl);
                mfreeFragment.setData(commSd, false);
                mViewPagerCtrl.addTab(i++, atd.tabName != null ? atd.tabName : I18NHelper.getText("xt.selectsendrangeactivity.text.new_tab")/* 新tab */, mfreeFragment);
                mFragmentlists.add(mfreeFragment);
            }
        }
        return i;
    }

    /**
     * 根据id 判断是否应该排除员工
     * @param id
     * @return
     */
    private boolean isExcludeEmp(int id) {
        if (id <= 0) return true;
        if (mSRangConfig != null && mSRangConfig.filterEmpIds != null) {
            for (Object oid : mSRangConfig.filterEmpIds) {
                int filterId = 0;
                try {
                    filterId = Integer.valueOf(oid.toString());
                } catch (Exception e) {
                }
                if (filterId == id) {
                    return true;
                }
            }
        }
        return false;
    }



    private List<Integer> getMyDeps(){
      List<Integer> list = DepartmentPicker.getEmpDirectDepIDs(myID);
      List<Integer> retList = new ArrayList<>();
      for(Integer id: list){
          if(!isExcludeDep(id)){
              retList.add(id);
          }
      }

      return retList;
    }

    private List<CircleEntity> getMyOrgs(){
        List<CircleEntity> list = DepartmentPicker.getEmpOrgs(myID);
        List<CircleEntity> retList = new ArrayList<>();
        for(CircleEntity circleEntity: list){
            int id = circleEntity.circleID;
            if(!isExcludeDep(id)){
                retList.add(circleEntity);
            }
        }

        return retList;
    }

    /**
     * 根据id 判断是否应该排除部门
     * @return
     */
    private boolean isExcludeDeps(List<Integer> ids) {
        if(ids!=null){
            for (Integer oid : ids) {
                if(!isExcludeDep(oid)){
                    return false;
                }
            }
        }
        return true;
    }
    private boolean isExcludeDep(int id) {
        if (id <= 0) return true;
        if (mSRangConfig != null && mSRangConfig.filterDepIds != null) {
            for (Object oid : mSRangConfig.filterDepIds) {
                int filterId = 0;
                try {
                    filterId = Integer.valueOf(oid.toString());
                } catch (Exception e) {
                }
                if (filterId == id) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 初始化最近的数据
     *
     * @return
     */
    void initLastestData() {
        mListCommonData = new ArrayList<>();
        initSendRange(mListCommonData);
        if (mCSDataConfig.mOutSelectAllOptions != null) {
            //全部
            CommonSelectData crossData =
                    new CommonSelectData(BIConstant.ALL_CROSS_LABEL, CommonSelectData.NORMAL_TYPE,
                            I18NHelper.getText("xt.selectsendrangeactivity.text.commonly_used")/* 常用 */, BIConstant.ALL_CROSS_CODE, false, CommonSelectData.NORMAL_ITEM_ALL, 0);
            mListCommonData.add(crossData);
            DepartmentPicker.pickDepartment(BIConstant.ALL_CROSS_CODE, mCSDataConfig.mOutSelectAllOptions.selected);
        }
        if (mSRangConfig.isLoadLastData) {
            //排列顺序 3.5,6,2,1-------------
            if (mCSDataConfig.mShowAll) {
                //全部
                CommonSelectData data6 =
                        new CommonSelectData(BIConstant.ALL_LABLE
                                , CommonSelectData.NORMAL_TYPE, I18NHelper.getText("xt.selectsendrangeactivity.text.commonly_used")/* 常用 */, BIConstant.ALL_CODE, false, CommonSelectData.NORMAL_ITEM_ALL, 0);
                mListCommonData.add(data6);
            }
            if (mCSDataConfig.mShowMe && !isExcludeEmp(FSContextManager.getCurUserContext().getAccount().getEmployeeIntId())) {
                /* 我 */
                CommonSelectData data3 = new CommonSelectData(I18NHelper.getText("xt.item_meeting_question.text.myself")/* 我自己 */, 0, I18NHelper.getText("xt.selectsendrangeactivity.text.commonly_used")/* 常用 */,
                        FSContextManager.getCurUserContext().getAccount().getEmployeeIntId(), false,
                        CommonSelectData.NORMAL_ME, 0);
                mListCommonData.add(data3);
            }
            if (mCSDataConfig.mShowMyMainDepOwner) {
                //我的主属部门负责人
                int mainmanagerid = ContactsFindUilts.getDepManagerId(ContactsFindUilts.getMyId(null));
                if(!isExcludeEmp(mainmanagerid)){
                    CommonSelectData data5 = new CommonSelectData(I18NHelper.getText("xt.selectsendrangeactivity.text.head_of_my_main_department")/* 我的主属部门负责人 */, 0, I18NHelper.getText("xt.selectsendrangeactivity.text.commonly_used")/* 常用 */, ContactsFindUilts.getDepManagerId(ContactsFindUilts.getMyId(null)), false,
                            CommonSelectData.ME_DEP_MANAGER, 0);
                    if (mainmanagerid > 0 && ContactsFindUilts.isFindUserByid(mainmanagerid)) {
                        mListCommonData.add(data5);
                    }
                }
            }
            if (mCSDataConfig.mShowMyMainDep) {
                //我的主属部门
                int maindepid = ContactsFindUilts.getDepMainId(ContactsFindUilts.getMyId(null));
                if(!isExcludeDep(maindepid)) {
                    CommonSelectData data6 = new CommonSelectData(I18NHelper.getText("xt.selectsendrangeactivity.text.my_main_department")/* 我的主属部门 */, 0, I18NHelper.getText("xt.selectsendrangeactivity.text.commonly_used")/* 常用 */, ContactsFindUilts.getDepMainId(ContactsFindUilts.getMyId(null)), false,
                            CommonSelectData.ME_MAIN_DEP, 0);
                    if (maindepid > 0 && ContactsFindUilts.isFindCircleByid(maindepid)) {
                        mListCommonData.add(data6);
                    }
                }
            }
            if (mCSDataConfig.mShowMyDep) {
                /* 我的部门 */
                CommonSelectData data2 = new CommonSelectData(I18NHelper.getText("xt.selectsendrangeactivity.text.my_department")/* 我所在部门 */, 0, I18NHelper.getText("xt.selectsendrangeactivity.text.commonly_used")/* 常用 */, Global.MY_ALL_DEMP_ID, false,
                        CommonSelectData.NORMAL_MY_DEP, 0);
                List lists = DepartmentPicker.getEmpDirectDepIDs(myID);
                if (lists != null && lists.size() > 0 && !isExcludeDeps(lists)) {
                    mListCommonData.add(data2);
                }

            }
            if (mCSDataConfig.mShowMyMainOrgOwner) {
                //我的主属组织负责人
                int myOrgOwner = DepartmentPicker.getEmpMainOrgOwnerId(myID);
                if(!isExcludeEmp(myOrgOwner)) {
                    CommonSelectData myOrgOwnerData = new CommonSelectData(
                            I18NHelper.getText("xt.selectsendrangeactivity.text.my_org_owner")/* 我的主属组织负责人 */,
                            0,
                            I18NHelper.getText("xt.selectsendrangeactivity.text.commonly_used")/* 常用 */,
                            myOrgOwner,
                            false,
                            CommonSelectData.NORMAL_MY_ORG_OWNER,
                            0);
                    if (myOrgOwner > 0) {
                        mListCommonData.add(myOrgOwnerData);
                    }
                }
            }
            if (mCSDataConfig.mShowMyMainOrganization) {
                //我的主属组织
                int myOrg = DepartmentPicker.getEmpMainOrgId(myID);
                if(!isExcludeDep(myOrg)) {
                    CommonSelectData myOrgData = new CommonSelectData(
                            I18NHelper.getText("xt.selectsendrangeactivity.text.my_org")/* 我的主属组织 */,
                            0,
                            I18NHelper.getText("xt.selectsendrangeactivity.text.commonly_used")/* 常用 */,
                            myOrg,
                            false,
                            CommonSelectData.NORMAL_MY_MAIN_ORG,
                            0);
                    if (myOrg > 0) {
                        mListCommonData.add(myOrgData);
                    }
                }
            }
            if (mCSDataConfig.mShowMyOrganization) {
                //我的所在组织
                CommonSelectData data2 = new CommonSelectData(
                        I18NHelper.getText("xt.selectsendrangeactivity.text.my_orgs")/* 我所在组织 */,
                        0,
                        I18NHelper.getText("xt.selectsendrangeactivity.text.commonly_used")/* 常用 */,
                        Global.MY_ALL_ORG_ID,
                        false,
                        CommonSelectData.NORMAL_MY_ORGANIZATION,
                        0);
                List lists = DepartmentPicker.getEmpOrgIds(myID);

                if (lists != null && lists.size() > 0&& !isExcludeDeps(lists)) {
                    mListCommonData.add(data2);
                }
            }
            if (mCSDataConfig.mCustomTypeData != null) {
                int orgType = 1;
                for (Map.Entry<String, List<SendRangeData>> entry : mCSDataConfig.mCustomTypeData.entrySet()) {
                    if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                        for (SendRangeData srd : entry.getValue()) {
                            CommonSelectData csd = new CommonSelectData(srd.getName(),
                                    orgType + CommonSelectData.CUSTOM_TYPE_MASK, entry.getKey(),
                                    srd.getId(), false, 0, 0);
                            csd.sRData = srd;
                            csd.setDisplayName(ToolUtils.toRangeTextEx((HashMap<Integer, String>) srd.getSelectDep(),
                                    (HashMap<Integer, String>) srd.getSelectEmp(), null));

                            mListCommonData.add(csd);
                        }
                        orgType++;
                    }
                }
            }
            if (!ContactConfigProvider.getInstance().isHideCompanyInfo() && mCSDataConfig.mShowGlobal
                    && FSContextManager.getCurUserContext().getAccount().isAllCompanyVisible(SelectSendRangeActivity.this)) {
                /* 全公司 */
                if(!isExcludeDep(999999)) {
                    CommonSelectData data1 =
                            new CommonSelectData(FSContextManager.getCurUserContext().getAccount().getAllCompany()
                                    , 0, I18NHelper.getText("xt.selectsendrangeactivity.text.commonly_used")/* 常用 */, Global.all, false, CommonSelectData.NORMAL_ALL_DEP, 0);

                        mListCommonData.add(data1);
                }
            }
        }
        if (mCSDataConfig.mShowDepartment) {
            if(mCSDataConfig.depMap != null){
                for(Map.Entry<String, String> entry : mCSDataConfig.depMap.entrySet()){
                    int id = -1;
                    String idStr = entry.getKey();
                    try{
                        id = Integer.valueOf(idStr);
                    }catch (NumberFormatException e){
                        FCLog.e("SelectSendRangeActivity", Log.getStackTraceString(e));
                    }
                    String value = entry.getValue();
                    if (ContactsFindUilts.isFindCircleByid(id) && !isExcludeDep(id) ) {
                        mListCommonData
                                .add(new CommonSelectData(value, 2, I18NHelper.getText("xt.addressbook.departments", "部门")/* 部门 */, id, false, 0, 0));
                    }

                }
            }else{
                // 获取部门最近数据、最多10条
                LinkedList<XData> xDatas1 = FeedSP.getDefaultList();
                int count1 = xDatas1.size();
                count1 = count1 > (10 + 3) ? 13 : count1;
                for (int i = 3; i < count1; i++) {
                    XData xdata = xDatas1.get(i);
                    if (ContactsFindUilts.isFindCircleByid(xdata.id) && !isExcludeDep(xdata.id) ) {
                        mListCommonData
                                .add(new CommonSelectData(xdata.name, 2, I18NHelper.getText("xt.addressbook.departments", "部门")/* 部门 */, xdata.id, false, 0, 0));
                    }
                }
            }

        }
        if (mCSDataConfig.mShowColleague) {
            if(mCSDataConfig.empMap != null){
                for(Map.Entry<String, String> entry : mCSDataConfig.empMap.entrySet()){
                    int id = -1;
                    String idStr = entry.getKey();
                    try{
                        id = Integer.valueOf(idStr);
                    }catch (NumberFormatException e){
                        FCLog.e("SelectSendRangeActivity", Log.getStackTraceString(e));
                    }
                    String value = entry.getValue();
                    if (ContactsFindUilts.isFindUserByid(id) && !isExcludeEmp(id)) {
                        mListCommonData.add(new CommonSelectData(value, 1, I18NHelper.getText("xt.selectuserupdateactivity.text.colleague")/* 同事 */, id, false, 0, 0));
                    }
                }
            }else{
                // 获取同事最近数据、最多10条
                LinkedList<XData> xDatas2 = FeedSP.getEmployeeList();
                int count2 = xDatas2.size();
                count2 = count2 > 10 ? 10 : count2;
                for (int i = 0; i < count2; i++) {
                    XData xdata = xDatas2.get(i);
                    if (ContactsFindUilts.isFindUserByid(xdata.id) && !isExcludeEmp(xdata.id)) {
                        mListCommonData.add(new CommonSelectData(xdata.name, 1, I18NHelper.getText("xt.selectuserupdateactivity.text.colleague")/* 同事 */, xdata.id, false, 0, 0));
                    }
                }
            }

        }

        if (mCSDataConfig.mShowSession) {
            Set<String> groupSet = new HashSet<>();
            if(mCSDataConfig.groupMap != null){
                for(Map.Entry<String, String> entry : mCSDataConfig.groupMap.entrySet()){
                    String groupid = entry.getKey();
                    groupSet.add(groupid);
                }
            }else{
                //获取最近群组
                LinkedList<XData> xDatas3 = FeedSP.getShareGroupRangeJson();
                for (int i = 0; i < xDatas3.size(); i++) {
                    XData xdata = xDatas3.get(i);
                    groupSet.add(xdata.groupid);
                }
            }

            for(String groupid:groupSet){
                SessionListRec slr = MsgDataController.getInstace(context).getSessionBySessionID(groupid);
                if (slr != null) {
                    String name = MsgUtils.getSessionDisplayName(context, slr);
                    int count = slr.getParticipants().size();
                    CommonSelectData commonSelectData = new CommonSelectData(name, 9, I18NHelper.getText("xt.selectuserupdateactivity.text.group")/* 群组 */, count, false, 0, 0);
                    commonSelectData.groupid = slr.getSessionId();
                    mListCommonData.add(commonSelectData);
                }
            }
        }
    }

    void initSendRange(List<CommonSelectData> listCommonData) {
        if (mSRangConfig.sendRangeData != null) {
            CommonSelectData dataSendRange = new CommonSelectData(mSRangConfig.sendRangeData.getName(), 3,
                    I18NHelper.getText("xt.selectsendrangeactivity.text.fast")/* 快捷 */, mSRangConfig.sendRangeData.getId(), false, 0, CommonSelectData.QUICK_SEND_RANGE);
            dataSendRange.setDisplayName(getDisplayString(mSRangConfig.sendRangeData));
            listCommonData.add(dataSendRange);
        }
        if (mSRangConfig.atRangeData != null) {
            CommonSelectData dataAtRange = new CommonSelectData(mSRangConfig.atRangeData.getName(), 3, I18NHelper.getText("xt.selectsendrangeactivity.text.fast")/* 快捷 */,
                    mSRangConfig.atRangeData.getId(), false, 0, CommonSelectData.QUICK_AT_RANGE);
            dataAtRange.setDisplayName(getDisplayString(mSRangConfig.atRangeData));
            listCommonData.add(dataAtRange);
        }
    }

    public CharSequence getDisplayString(SendRangeData rangeData) {
        String name = rangeData.getName();
        String s = name + "（" + rangeData.getRangeString() + "）";
        SpannableString ss = new SpannableString(s);
        ss.setSpan(new AbsoluteSizeSpan(FSScreen.dip2px(14)), name.length(), s.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        ss.setSpan(new ForegroundColorSpan(0x999999),
                name.length(), s.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return ss;
    }

    protected void initTitleEx() {
        super.initTitleEx();
        refreshTitle(0);
    }

    /**
     * 刷新title
     */
    private void refreshTitle(int pageIndex) {
        mCommonTitleView.getLeftLayout().removeAllViews();
        mCommonTitleView.getRightLayout().removeAllViews();
        if (TextUtils.isEmpty(mSRangConfig.title)) {
            mCommonTitleView.setMiddleText(I18NHelper.getText("xt.selectsendrangeactivity.text.select_the_sending_range")/* 选择发送范围 */);
        } else {
            mCommonTitleView.setMiddleText(mSRangConfig.title);
        }
        mCommonTitleView.addLeftAction(R.string.return_before_new_normal, new OnClickListener() {

            @Override
            public void onClick(View v) {
                if (DepartmentPicker.haveSelected()) {
                    DepartmentPicker.restore();
                    setResult(RESULT_CANCELED);
                    finish();
                } else {
                    setResult(RESULT_CANCELED, getIntent());
                    finish();
                }
            }
        });
        if (mSRangConfig.showTitleResetButton) {
            mCommonTitleView.addRightAction(I18NHelper.getText("crm.layout.filter_view_lr_style_layout.1990")/* 重置 */, new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (DepartmentPicker.haveSelected()) {
                        DepartmentPicker.restore();
                    }
                    Intent intent = new Intent();
                    intent.putExtra("result_reset_emp_and_dep", true);
                    setResult(Activity.RESULT_OK, intent);
                    finish();
                }
            });
        }
        String title = mViewPagerCtrl.getTitlemap().get(pageIndex);
        String outOwnerTabTitle = TextUtils.isEmpty(mSRangConfig.outOwnerTabTitle) ?
                I18NHelper.getText("fx.contacts_fs.title.partner_emploee")/* 伙伴员工 */ : mSRangConfig.outOwnerTabTitle;
        String roleTitle=TextUtils.isEmpty(mSRangConfig.roleTabTitle)?I18NHelper.getText("crm.type.CoreObjType.2464")/*角色*/
                :mSRangConfig.roleTabTitle;
        String userGroupTitle=TextUtils.isEmpty(mSRangConfig.userGroupTabTitle)?
                I18NHelper.getText("fx.contacts_fs.title.user_group_tab")/*用户组*/
                :mSRangConfig.userGroupTabTitle;
        String outPersonTitle=TextUtils.isEmpty(mSRangConfig.outPersonTabTitle)?
                I18NHelper.getText("cml.crm.approval.external_employees")/*外部人员*/
                :mSRangConfig.outPersonTabTitle;
        String outEnterpriseTabTitle=TextUtils.isEmpty(mSRangConfig.outEnterpriseTabTitle)?
                I18NHelper.getText("qx.session.msg_des.outer_enterprise")/*外部企业*/
                :mSRangConfig.outEnterpriseTabTitle;
        String outOppositePersonTabTitle=TextUtils.isEmpty(mSRangConfig.outOppositePersonTabTitle)
                ?I18NHelper.getText("cml.crm.approval.external_employees")/*下游对接人*/
                :mSRangConfig.outOppositePersonTabTitle;
        if (I18NHelper.getText("fm.fsmail.FSMailContactsActivity.2")/* 最近 */.equals(title)) {//最近
        } else if (I18NHelper.getText("xt.selectuserupdateactivity.text.colleague")/* 同事 */.equals(title)) {//同事
            // 通讯录新搜索框
            mCommonTitleView.addRightAction(R.string.work_search, new OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivityForResult(ContactSearchAct.getMultiSelectIntent(context, ContactSelectBarFrag.ShowType.Text, 0,
                            mSRangConfig, empCo), REQUEST_CODE_SEARCH);
                }
            });
        } else if (I18NHelper.getText("xt.addressbook.departments","部门")/* 部门 */.equals(title)) {//部门
            // 通讯录新搜索框
            mCommonTitleView.addRightAction(R.string.work_search, new OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivityForResult(DepartmentSearchAct.getMultiSelectIntent(context, false,
                            mSRangConfig.isInCustomMode, mSRangConfig, 0), REQUEST_CODE_SEARCH);
                }
            });
        } else if (I18NHelper.getText("qx.session.default.group_default_title")/* 群对话 */.equals(title)) {//群
            // 通讯录新搜索框
            mCommonTitleView.addRightAction(R.string.work_search, new OnClickListener() {

                @Override
                public void onClick(View v) {
                    startActivityForResult(GroupSearchAct.getMultiSelectIntent(context), REQUEST_CODE_SEARCH);
                }
            });
        } else if (I18NHelper.getText("wq.projecttaskvo.text.terminated")/* 已停用 */.equals(title)) {
            //停用搜索
            mCommonTitleView.addRightAction(R.string.work_search, new OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivityForResult(StopEmpSearchAct
                            .getSelectIntent(context, mSRangConfig != null && mSRangConfig.mOnlyChooseOne,
                                    mStopEmpFragment.getAllStopEmpData(), false, false), REQUEST_CODE_SEARCH);
                }
            });

        } else if (TextUtils.equals(outOwnerTabTitle, title)) {
            mCommonTitleView.addRightAction(R.string.work_search, new OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivityForResult(SearchSelectOutUserAct
                            .getIntent(context, mSRangConfig), REQUEST_CODE_SEARCH);
                }
            });
        }else if(TextUtils.equals(roleTitle, title)){
            mCommonTitleView.addRightAction(R.string.work_search, new OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivityForResult(RoleSearchAct
                            .getIntent(context,mSelectEmpRoleFragment.getEmpRoleDataList()), REQUEST_CODE_SEARCH);
                }
            });
        }else if(TextUtils.equals(userGroupTitle, title)){
            mCommonTitleView.addRightAction(R.string.work_search, new OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivityForResult(UserGroupSearchAct
                            .getIntent(context,mSelectUserGroupFragment.getUserGroupDataList()), REQUEST_CODE_SEARCH);
                }
            });
        }else if(TextUtils.equals(outPersonTitle,title)){
            mCommonTitleView.addRightAction(R.string.work_search, new OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivityForResult(OutEnterpriseSearchAct
                            .getIntent(context,mSelectOutPersonFragment.getOutPersonList(),
                                    mSRangConfig.mOnlyChooseOne,false),
                            REQUEST_CODE_SEARCH);
                }
            });
        }else if(TextUtils.equals(outEnterpriseTabTitle,title)){
            mCommonTitleView.addRightAction(R.string.work_search, new OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivityForResult(OutEnterpriseSearchAct
                            .getIntent(context,mSelectOutEnterpriseFragment.getOutPersonList(),
                                    mSRangConfig.mOnlyChooseOne,true), REQUEST_CODE_SEARCH);
                }
            });
        }else if(TextUtils.equals(outOppositePersonTabTitle,title)){
            mCommonTitleView.addRightAction(R.string.work_search, new OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivityForResult(OutOppositePersonSearchAct
                            .getIntent(context,mSRangConfig.mOnlyChooseOne,mSRangConfig.filterEmpIds), REQUEST_CODE_SEARCH);
                }
            });
        }
//        else if(I18NHelper.getText("fx.contacts_fs.title.partner_enterprise").equals(title)){
//            //外部企业
//            mCommonTitleView.addRightAction(R.string.work_search, new OnClickListener() {
//                @Override
//                public void onClick(View v) {
//                    startActivityForResult(OutPartnerSearchAct.getSelectIntent(context, mSRangConfig), REQUEST_CODE_SEARCH);
//                }
//            });
//        }
    }

    @Override
    public void onBackPressed() {
        DepartmentPicker.restore();
        super.onBackPressed();
    }

    private void sendData() {
        Intent it = getIntent();
        setResult(RESULT_OK, it);

        //CC：更换合作伙伴外部负责人
        EventBus.getDefault().post(new FSSelectOutOwnerEvent(DepartmentPicker.getEmployeesMapPicked(),
                DepartmentPicker.getOutOwnerPicked()));
    }

    public void onClickOK(View view) {
        if (mSRangConfig.mConfirmChecker != null && !mSRangConfig.mConfirmChecker.checkConfirm()) {
            return;
        }
        sendData();
        String getNavigateCallId = CCUtil.getNavigateCallId(this);
        if (!TextUtils.isEmpty(getNavigateCallId)) {
            Map<Integer, String> employeeMapPicked = DepartmentPicker.getEmployeesMapPicked();
            Map<OutTenant, List<OutOwner>> outOwnerPicked = DepartmentPicker.getOutTenantMapPicked();
            Map<Integer, String> departmentMapPicked = DepartmentPicker.getDepartmentsMapPicked();
            Map<String, RoleData> roleMapPicked = DepartmentPicker.getRoleMapPicked();
            List<UserGroupData> userGroupList = DepartmentPicker.getUserGroupListPicked();
            List<OutTenant> outTenantList = DepartmentPicker.getOutTenantPicked();
            List<OutOppositePersonDataBean> outOppositePersonPicked = DepartmentPicker.getOutOppositePersonPicked();
            Map<String, Object> joResultMap = FeedBizUtils.makeAtDataForCCResult(employeeMapPicked, outOwnerPicked,
                    departmentMapPicked,roleMapPicked,userGroupList,outTenantList,outOppositePersonPicked);
            joResultMap.put("isSelectOutTeam",mSRangConfig.isSelectOutTeam);
            CC.sendCCResult(getNavigateCallId, CCResult.success(joResultMap));
            FCLog.i("SelectSendRangeActivity", "onClickOK ccId: " + getNavigateCallId + " return joResultMap: " + joResultMap);
        }
        finish();
    }

    public ArrayList<AEmpSimpleEntity> mapToList(Map<Integer, String> map) {
        ArrayList<AEmpSimpleEntity> ret = new ArrayList<>();
        for (Integer keyInteger : map.keySet()) {
            ret.add(FSContextManager.getCurUserContext().getCacheEmployeeData().getEmpShortEntityEXNew(keyInteger));
        }
        return ret;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ISelectDepTypeListenerUtil.getUtil().cleanAll();
        mViewPagerCtrl.closeSrc();
        mTotalSelectMapCtrl.closeSrc();
        releaseObserver();
        //还原成默认值
        DepartmentPicker.showMeInPrivateMode = true;
    }

    @Override
    public void onPageScrolled(int arg0, float arg1, int arg2) {
        // TODO Auto-generated method stub
    }

    @Override
    public void onPageScrollStateChanged(int arg0) {
        // TODO Auto-generated method stub
    }

    @Override
    public void onPageSelected(int arg0) {
        // TODO Auto-generated method stub
        if (mFragmentlists != null && mFragmentlists.size() > 0) {
            ISelectSendRang issr = mFragmentlists.get(arg0);
            if (issr != null) {
                issr.refreshView();
            }
        }
        refreshTitle(arg0);
    }

    @Override
    public void onSingleChoose() {
        onClickOK(null);
    }

    @Override
    public void finish() {
        if (mFragmentlists != null && mFragmentlists.size() > 0) {
            for (ISelectSendRang isr : mFragmentlists) {
                isr.clearSrc();
            }
        }


        super.finish();
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            onClickOK(null);
        } else {
            DepartmentPicker.notifyPickDataChange();
        }

    }

    /******************************************************/
    // 选中员工改变
    /**
     * 观察者
     */
    private DataSetObserver mObserver;

    /**
     * 初始化选人观察者
     */
    private void initObserver() {
        mObserver = new DataSetObserver() {
            @Override
            public void onChanged() {
                refreshList();
            }
        };
        DepartmentPicker.registerPickObserver(mObserver);
    }

    /**
     * 释放选人相关资源
     */
    private void releaseObserver() {
        DepartmentPicker.unregisterPickObserver(mObserver);
    }

    /**
     * 刷新列表
     */
    private void refreshList() {
        if (mFragmentlists != null && mFragmentlists.size() > 0) {
            try {
                mFragmentlists.get(mViewPagerCtrl.getCurIndex()).refreshView();
            } catch (ArrayIndexOutOfBoundsException e) {
                e.printStackTrace();
            } catch (NullPointerException e) {
                e.printStackTrace();
            }
        }
    }

    //过滤不存在部门
    void filterDepData(List<Integer> depList) {
        if (depList == null || depList.size() == 0) return;
        ICacheEmployeeData cache = FSContextManager.getCurUserContext().getCacheEmployeeData();
        Iterator<Integer> it = depList.iterator();
        while (it.hasNext()) {
            int id = it.next();
            if (id == Global.all || id == BIConstant.ALL_CODE || id == BIConstant.ALL_CROSS_CODE) continue;
            if (cache.getCircleEntityForId(id) == null) {
                it.remove();
            }
        }
    }

    public SelectSendRangeConfig getSRangConfig() {
        return mSRangConfig;
    }

    @Override
    public void loadGroupData(final SelectGroupFragment.onDataReadyListenser listenser) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                loadGroupData();
                SelectSendRangeActivity.this.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (listenser != null) {
                            listenser.onDataSucc(mGroupDatas);
                        }
                        pickGroupData();
                    }
                });

            }
        }).start();
    }

    @Override
    public void loadEmpData(final SelectEmpFragment.onEmpDataReadyListenser listenser) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                loadEmpData();
                SelectSendRangeActivity.this.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (listenser != null) {
                            listenser.onDataSucc(mEmpDatas, mSRangConfig.mOnlyChooseOne);
                        }
                        pickEmpData();
                        pickStopEmp();
                    }
                });
            }
        }).start();
    }

    void pickStopEmp() {
        DepartmentPicker.pickStopEmployees(mEmpAbnormalDatas, true);
    }

    @Override
    public void loadDepData(final SelectDepFragment.onDepDataReadyListenser listenser) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                loadDepData();
                SelectSendRangeActivity.this.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (listenser != null) {
                            listenser.onDataSucc(mDepDatas, mSRangConfig.mOnlyChooseOne);
                        }
                        pickDepData();
                    }
                });
            }
        }).start();
    }
}
