
package com.facishare.fs.contacts_fs.fragment;

import com.facishare.fs.contacts_fs.datactrl.ContactConfigDataUtils;
import com.facishare.fs.i18n.I18NHelper;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.facishare.fs.BaseFragmentActivity;
import com.facishare.fs.Global;
import com.facishare.fs.biz_feed.adapter.BaseShareAdapter;
import com.facishare.fs.contacts_fs.CircleActivity;
import com.facishare.fs.contacts_fs.ContactAction;
import com.facishare.fs.contacts_fs.ContactAction.onSetStarTagListner;
import com.facishare.fs.contacts_fs.datactrl.ICacheEmployeeData;
import com.facishare.fs.contacts_fs.dep_level.SeeInDepLevelActivity;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.pluginapi.contact.ContactConstants;
import com.facishare.fs.pluginapi.contact.beans.CircleEntity;
import com.facishare.fs.pluginapi.contact.beans.CircleIndexLetter;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fslib.R;
import com.fxiaoke.cmviews.SideBar;
import com.fxiaoke.dataimpl.contacts.ContactConfigProvider;
import com.fxiaoke.fxdblib.utils.DbToolsApi;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.fxlog.FCTimePoint;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.Animation.AnimationListener;
import android.view.animation.TranslateAnimation;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.SectionIndexer;
import android.widget.TextView;

/**
 * 通讯录部门页面 <br>
 */
public class DepartmentsFragment extends Fragment
{
    Context ctx;
    ListView mListView;
    DepListWidthStarAdapter mAdapter;
    /** 全部部门数据列表 */
    List<CircleIndexLetter> mData = new ArrayList<CircleIndexLetter>();
    View rootView;
    /** 索引条 */
    IndexBarCtrl mIndexBarCtrl;
    /** 星标控制器 */
    StarDataCtrler mStarDataCtrler = new StarDataCtrler();
    /** 星标部门 */
    List<CircleIndexLetter> mStarDepartments = new ArrayList<CircleIndexLetter>();
    /** 我的部门 */
    private List<CircleIndexLetter> mMyDepartments = new ArrayList<CircleIndexLetter>();
    /** 是否设置星标 */
    private boolean mIsStar;

//    public DepartmentsFragment(Context ctx) {
//        this.ctx = ctx;
//    }

    public static DepartmentsFragment newInstance(Context ctx) {
        DepartmentsFragment fragment = new DepartmentsFragment();
        Bundle data = new Bundle();
        fragment.setArguments(data);
        return fragment;
    }
    public DepartmentsFragment() {
        super();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        FCLog.w("enter onCreate");
        this.ctx = getActivity();

    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        this.ctx  = context;
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        lazyLoadData();

    }

    boolean isLoad =false;
    private void lazyLoadData(){
        if(getUserVisibleHint()){
            if(!isLoad){
                FCTimePoint.start(FCLog.big_enterprise.getFunction()+" Colleage lazyLoadData3");
                SideBar bar = (SideBar) rootView.findViewById(R.id.sideBar);
                mIndexBarCtrl = new IndexBarCtrl(getActivity(), bar);
                if (mData != null&&mData.size()>0) {
                    mIndexBarCtrl.initIndexBarWithCircleEntity(mListView, mData);
                }
                if(mData==null||mData.size()==0){
                    TextView empty_tv = (TextView) rootView.findViewById(R.id.empty_tv);
                    empty_tv.setText(I18NHelper.getText("xt.departmentsfragment.text.no_department")/* 没有部门 */);
                    empty_tv.setVisibility(View.VISIBLE);
                    mListView.setVisibility(View.GONE);
                    rootView.findViewById(R.id.empty_view).setVisibility(View.VISIBLE);
                }else{
                    rootView.findViewById(R.id.empty_view).setVisibility(View.GONE);
                    TextView empty_tv = (TextView) rootView.findViewById(R.id.empty_tv);
                    empty_tv.setVisibility(View.GONE);
                    mListView.setVisibility(View.VISIBLE);
                }
                FCTimePoint.end(FCLog.big_enterprise.getFunction()+" Colleage lazyLoadData3");
                isLoad = true;

            }else {
                rootView.findViewById(R.id.empty_view).setVisibility(View.GONE);
                TextView empty_tv = (TextView) rootView.findViewById(R.id.empty_tv);
                empty_tv.setVisibility(View.GONE);
                mListView.setVisibility(View.VISIBLE);
            }
        }

    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
            Bundle savedInstanceState) {
        if (rootView != null) {
            return rootView;
        }
        this.ctx = getActivity();
        rootView = inflater.inflate(R.layout.select_department_layout, container, false);
        mListView = (ListView) rootView.findViewById(R.id.selectable_list);
        mAdapter = new DepListWidthStarAdapter(ctx, mListView, mData);
        mAdapter.setDividerLeftMarginValue(BaseShareAdapter.s_dividerline_leftmargin_normal);
        if (mIsStar){
            changeStatus(ViewStatus.startag);
        }
//
        if(!ContactConfigDataUtils.checkEnableCustomOrganizationSettings() || ContactConfigProvider.getInstance().canShowDepartmentOrganization()){
            View seeIndepLevelView = inflater.inflate(R.layout.departments_entrance_layout, null);
            mListView.addHeaderView(seeIndepLevelView);
            seeIndepLevelView.setOnClickListener(new View.OnClickListener() {

                @Override
                public void onClick(View v) {
                    if (mClickShowDepLevelBack != null) {
                        mClickShowDepLevelBack.run();
                        return;
                    }
                    Intent intent = SeeInDepLevelActivity.getIntent(getActivity(), Global.all, false);
                    getActivity().startActivity(intent);

                }
            });
        }

        mListView.setAdapter(mAdapter);
//
//        mAdapter = new DepListWidthStarAdapter(ctx, mListView, mData);
//        mAdapter.setDividerLeftMarginValue(BaseShareAdapter.s_dividerline_leftmargin_normal);
//        updateData();
//        mListView.setAdapter(mAdapter);
//
//        SideBar bar = (SideBar) rootView.findViewById(R.id.sideBar);
//        mIndexBarCtrl = new IndexBarCtrl(getActivity(), bar);
//        if (mData.size() > 0) {
//            mIndexBarCtrl.initIndexBar(mListView, mData);
//        }

        mListView.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                // 这种写法已经过滤了listhead
                final CircleIndexLetter circle = (CircleIndexLetter) parent.getItemAtPosition(position);
                ICacheEmployeeData  empcache = FSContextManager.getCurUserContext().getCacheEmployeeData();
                final CircleEntity ce = empcache.getCircleEntityForId(circle.circleID);

                FCLog.w("onItemClick circle id " + circle.circleID);
                FCLog.w("onItemClick position " + position);
                if (!mIsStar) {
                    if (circle.circleID == -1) {
                        // 星标占位不响应点击
                    } else {

//                        Intent intent = CircleActivity.getIntent(
//                                ctx, String.valueOf(circle.circleID), ce.getI18NName());
//                        getActivity().startActivity(intent);
                        CircleActivity.start(getActivity(), String.valueOf(circle.circleID), ce.getI18NName());
                    }
                } else {
                    if (circle.circleID == -1) {
                        // 星标占位不响应点击
                    } else {
                        CheckBox cboSelect = (CheckBox) view.findViewById(R.id.cboSelect);
                        final boolean b = !cboSelect.isChecked();
                        getActivity().showDialog(BaseFragmentActivity.DIALOG_WAITING_BASE);
                        FCLog.i(FCLog.debug_star, "dep:" + ce.getI18NName() + " " + circle.circleID
                                + ":new status:" + b);

                        ContactAction.setDepartmentStarTag(circle.circleID, b,
                                new onSetStarTagListner() {

                                    @Override
                                    public void onSetResult(boolean isSuccess, boolean starTag, boolean isSelf) {
                                        getActivity().removeDialog(
                                                BaseFragmentActivity.DIALOG_WAITING_BASE);
                                        if (isSuccess) {
                                            FCLog.i(FCLog.debug_star, "dep:" + ce.getI18NName() + " "
                                                    + circle.circleID + ":new status:" + b
                                                    + " success");
                                            CircleEntity circleEntity = new CircleEntity();
                                            circleEntity.circleID=ce.circleID;
                                            circleEntity.setAsterisk(b);
                                            circleEntity.extraFilled =true;
                                            FSContextManager.getCurUserContext().getContactSynchronizer().setCircleStarAll(circleEntity);
                                            mAdapter.notifyDataSetChanged();
                                        } else {
                                            ToastUtils.show(I18NHelper.getText("wq.fs_net_disk_permission_fragment.text.setting_error")/* 设置失败 */);
                                        }
                                    }
                                });

                    }
                }
            }
        });

//        Handler H = new Handler();
//        H.postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                lazyLoadData();
//            }
//        }, 50);
        return rootView;
    }

    @Override
    public void onDestroyView() {

        super.onDestroyView();
    }

    public void clearSrc() {
        if (mIndexBarCtrl != null) {
            mIndexBarCtrl.clearSrc();
        }
    }

    public boolean isSearchMode() {
        return mAdapter.isSearchMode();
    }

    public void forceSearch() {
        // mSearchDataCtrler.search(null, 0);

    }

    public void showDisplayMode() {
        // mSearchDataCtrler.showDisplayMode();
    }

    public void refreshView() {
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
    }

    /**
     * 根据是否有星标， 更新头部数据
     * 
     * @param s
     */
    public void changeStatus(ViewStatus s) {
        if (s == ViewStatus.normal) {
            mIsStar = false;
            if (mAdapter!=null){
                mAdapter.setDividerLeftMarginValue(BaseShareAdapter.s_dividerline_leftmargin_normal);
                mAdapter.animateViewsHideStar();
            }
        } else {
            mIsStar = true;
            if (mAdapter!=null){
                mAdapter.setDividerLeftMarginValue(BaseShareAdapter.s_dividerline_leftmargin_edit);
                mAdapter.animateViewsShowStar();
            }
        }
    }


    /**
     * 设置数据
     * 
     * @param data
     */
    public void setData(List<CircleIndexLetter> data) {
        if (data == null) {
            mData = new ArrayList<CircleIndexLetter>();
        } else {
            FCLog.w("setData data size " + data.size());
            mData = data;
        }
        mMyDepartments=FSContextManager.getCurUserContext().getCacheEmployeeData().getOrderedCirclesOnlyIndexLetterThatEmpDirectIn(FSContextManager.getCurUserContext().getAccount().getEmployeeIntId());
        mMyDepartments=getNewInstance(mMyDepartments);
        if (mMyDepartments != null && mMyDepartments.size() > 0) {
            String myGroupName = I18NHelper.getText("av.common.view.me")/* 我 */;
            for (CircleIndexLetter dep : mMyDepartments) {
                dep.setIndexLetter(myGroupName);
                dep.setI18NIndexLetter(myGroupName);
            }
            mData.addAll(0, mMyDepartments);
        } else {
            mMyDepartments = new ArrayList<CircleIndexLetter>();
        }
        mStarDepartments.clear();
        if (mIndexBarCtrl != null) {
            mIndexBarCtrl.initIndexBar(mListView, mData);
        }
        updateData();
        if(mData==null||mData.size()==0){
            TextView empty_tv = (TextView) rootView.findViewById(R.id.empty_tv);
            empty_tv.setText(I18NHelper.getText("xt.departmentsfragment.text.no_department")/* 没有部门 */);
            empty_tv.setVisibility(View.VISIBLE);
            mListView.setVisibility(View.GONE);
            rootView.findViewById(R.id.empty_view).setVisibility(View.VISIBLE);
        }
    }
    List<CircleIndexLetter> getNewInstance(List<CircleIndexLetter> src){
        List<CircleIndexLetter> ret=new ArrayList<>();
        if (src!=null){
            for (CircleIndexLetter circleEntity:src
                 ) {
                CircleIndexLetter ce=new CircleIndexLetter();
                DbToolsApi.copyAttributeByAttr(circleEntity,ce);
                ret.add(ce);
            }
        }
        return ret;
    }
    /**
     * 业务处理数据
     */
    public void updateData() {
        // 星标数据
        addStarData();
        if (mAdapter != null) {
            mAdapter.updateData(mData);
            if (mIndexBarCtrl != null) {
                mIndexBarCtrl.initIndexBarWithCircleEntity(mListView, mData);
            }
        }
    }

    /**
     * 添加星标部门
     */
    private void addStarData() {
        FCLog.w("addStarData before data size " + mData.size());
        // 移除现有的星标部门，后面添加
        if (mStarDepartments.size() > 0) {
            for (int i = 0; i < mStarDepartments.size(); i++) {
                mData.remove(0);
            }
        }
//        if(mData!=null&&mData.size()>0) {
        mStarDepartments = mStarDataCtrler.getStarDep();
//        }
        if (mStarDepartments.size() == 0) {

            // mStarDepartments.add(mStarDataCtrler.makeDumyDep());
        } else {
            mData.addAll(0, mStarDepartments);
        }
        FCLog.w("addStarData after data size " + mData.size());
    }
    
    /**
     * 取得员工数量，除去星标
     * @return
     */
    public int getDepCount() {
        int count = 0;
        count = mData.size() - mStarDepartments.size() - mMyDepartments.size();
        return count;
    }
    Runnable mClickShowDepLevelBack = null;
    public void setClickShowDepLevelBack(Runnable runnable) {
        mClickShowDepLevelBack = runnable;
    }

    public enum ViewStatus {
        startag,
        normal,
    }

    class DepListWidthStarAdapter extends BaseShareAdapter implements
            SectionIndexer {
        Map<View, Object> itemViews = new HashMap<View, Object>();
        ViewStatus mCurViewStatus = ViewStatus.normal;
        TranslateAnimation animationMoveRightMiddle;
        TranslateAnimation animationMoveRightStar;
        TranslateAnimation animationMoveLeftMiddle;
        TranslateAnimation animationMoveLeftStar;
        DisplayMetrics dm;

        public class ALis implements AnimationListener {

            @Override
            public void onAnimationStart(Animation animation) {
                // TODO Auto-generated method stub
            }

            @Override
            public void onAnimationRepeat(Animation animation) {
                // TODO Auto-generated method stub

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                notifyDataSetChanged();
            }
        }

        ALis mALis = new ALis();

        public DepListWidthStarAdapter(Context context, ListView listView,
                List<CircleIndexLetter> mAdList) {
            super(context, listView, mAdList);
            dm = context.getResources().getDisplayMetrics();
            animationMoveRightMiddle = new TranslateAnimation((int) (-42.5 * dm.density), 0, 0, 0);
            animationMoveRightStar = new TranslateAnimation((int) (-42.5 * dm.density), 0, 0, 0);
            animationMoveLeftMiddle = new TranslateAnimation((int) (42.5 * dm.density), 0, 0, 0);
            animationMoveLeftStar = new TranslateAnimation((int) (42.5 * dm.density), 0, 0, 0);
            animationMoveRightMiddle.setAnimationListener(mALis);
            animationMoveLeftMiddle.setAnimationListener(mALis);
            animationMoveRightMiddle.setDuration(ContactAction.mAnimationDur);
            animationMoveRightStar.setDuration(ContactAction.mAnimationDur);
            animationMoveLeftMiddle.setDuration(ContactAction.mAnimationDur);
            animationMoveLeftStar.setDuration(ContactAction.mAnimationDur);

            this.defaultImageDrawable = R.drawable.user_head;
        }

        public BaseShareAdapter newInstance() {
            return new DepListWidthStarAdapter(context, mlistViewBase, mAdList);
        }

        public void animateViewsShowStar() {
            for (View key : itemViews.keySet()) {
                ViewHolder vh = (ViewHolder) itemViews.get(key);
                if (vh.cboSelect == null) {

                } else {
                    vh.middleForAnimationLayout.setAnimation(animationMoveRightMiddle);
                    vh.cboSelect.setAnimation(animationMoveRightStar);
                    vh.cboSelect.setVisibility(View.VISIBLE);
                }
            }
            animationMoveRightMiddle.startNow();
            animationMoveRightStar.startNow();

        }

        public void animateViewsHideStar() {
            for (View key : itemViews.keySet()) {
                ViewHolder vh = (ViewHolder) itemViews.get(key);
                if (vh.cboSelect == null) {

                } else {
                    vh.middleForAnimationLayout.setAnimation(animationMoveLeftMiddle);
                    vh.cboSelect.setAnimation(animationMoveLeftStar);
                    vh.cboSelect.setVisibility(View.GONE);
                }
            }
            animationMoveLeftMiddle.startNow();
            animationMoveLeftStar.startNow();

        }

        public View getView(int position, View convertView, ViewGroup parent) {
            convertView = initContentView(position, convertView, parent);
            ViewHolder viewHolder = (ViewHolder) convertView.getTag();
            CircleIndexLetter emp = (CircleIndexLetter) mAdList.get(position);
            viewHolder.curItemDepId = emp.circleID;
            itemViews.put(viewHolder.noIndexViewLayout, viewHolder);
            refreshView(viewHolder, position, emp);
            return convertView;
        }

        public View initContentView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder = null;
            if (convertView == null) {
                holder = new ViewHolder();
                convertView = LayoutInflater.from(context).inflate(
                        R.layout.departments_list_item, null);
                holder.txtDepartName = (TextView) convertView
                        .findViewById(R.id.txtDepartName);
                holder.txtCollCount = (TextView) convertView
                        .findViewById(R.id.txtColleaguesNumber);
                holder.imageRightArrow = (ImageView) convertView
                        .findViewById(R.id.customerViewRightArrow);
                holder.cboSelect = (CheckBox) convertView
                        .findViewById(R.id.cboSelect);
                holder.middleForAnimationLayout = convertView
                        .findViewById(R.id.middlelayout_foranimation);                    
                holder.letter_index = (TextView) convertView
                        .findViewById(R.id.txtSideBarIndex);
                holder.imageHeader = (ImageView) convertView
                        .findViewById(R.id.iv_per_user_head);
                holder.letter_index.setTag(
                        R.id.bottom_line, 
                        convertView.findViewById(R.id.bottom_line));
                holder.noIndexViewLayout = convertView.findViewById(R.id.emplayout);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }

            return convertView;
        }

        void refreshView(ViewHolder viewHolder, int position, final CircleIndexLetter circle) {
            int pre = position - 1;
            int next = position + 1;
            // Integer p = Integer.valueOf(position);

            showTitle(viewHolder.letter_index, circle.getIndexLetter(),
                    pre < 0 ? null : ((CircleIndexLetter) mAdList.get(pre)).getIndexLetter(),
                    next >= getCount() ? null : ((CircleIndexLetter) mAdList.get(next)).getIndexLetter(),
                    position);//TODO: need change

            if (viewHolder.cboSelect == null) {
                return;
            }

            FCTimePoint.start(FCLog.big_enterprise.getFunction()+" get emp count from db "+circle.circleID);

            ICacheEmployeeData  empcache = FSContextManager.getCurUserContext().getCacheEmployeeData();
            CircleEntity ce = empcache.getCircleEntityForId(circle.circleID);
            if (ce == null) {
                return;
            }
            if (!ContactConfigProvider.getInstance().isHideDepartmentStats()) {
                viewHolder.txtCollCount.setText(I18NHelper.getFormatText("crm.controler.LocalContactPicker.1310.v1"/* {0}人 */, ce.getMemberCount() + ""));
            } else {
                viewHolder.txtCollCount.setText("");
            }
            viewHolder.txtDepartName.setText(ce.getI18NName());
            // 头像
            String recordType = ce.recordType;
            if(TextUtils.equals(recordType, ContactConstants.DEP_ORGANIZATION)){
                viewHolder.imageHeader.setBackgroundResource(R.drawable.contact_orgnization_avatar);
            }else{
                viewHolder.imageHeader.setBackgroundResource(R.drawable.contact_group_avatar);
            }
            FCTimePoint.end(FCLog.big_enterprise.getFunction()+" get emp count from db "+circle.circleID);
            viewHolder.cboSelect.setTag(ce);
            if (!mIsStar) {
                viewHolder.cboSelect.setVisibility(View.GONE);
            } else {
                viewHolder.cboSelect.setVisibility(View.VISIBLE);
                viewHolder.cboSelect.setChecked(ce.isAsterisk());
            }
        }

        private class ViewHolder {
            CheckBox cboSelect;// 星标
            ImageView imageRightArrow;
            ImageView imageHeader;
            TextView letter_index;
            TextView txtDepartName;
            TextView txtCollCount;
            View noIndexViewLayout;
            View middleForAnimationLayout;
            int curItemDepId;
        }

        protected String getLetter(String s1) {
            String letter = " ";
            if (TextUtils.isEmpty(s1)) {
                return letter;
            }
            do {
                if (s1.equals(IndexBarCtrl.s_starString)) {
                    letter = s1;
                    break;
                }
                letter = (s1 == null || s1.length() == 0) ? null : s1.substring(0, 1).toUpperCase();
            } while (false);
            return letter;
        }

        @Override
        public String getName(int index) {

            return ((CircleIndexLetter) mAdList.get(index)).getIndexLetter();
        }

    }
}
