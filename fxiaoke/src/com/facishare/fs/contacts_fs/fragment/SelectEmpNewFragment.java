package com.facishare.fs.contacts_fs.fragment;

import com.facishare.fs.contacts_fs.picker.RelatedEmpPicker;
import com.facishare.fs.i18n.I18NHelper;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;

import com.facishare.fs.BaseActivity;
import com.facishare.fs.biz_session_msg.beans.SelectSessionOrEmpConstants;
import com.facishare.fs.contacts_fs.customerservice.util.ISelectDepTypeListenerUtil;
import com.facishare.fs.contacts_fs.datactrl.ContactOperator;
import com.facishare.fs.i18n.LanguageUtil;
import com.facishare.fs.pluginapi.contact.beans.EmpIndexLetter;
import com.facishare.fslib.R;
import com.facishare.fs.biz_feed.adapter.BaseShareAdapter;
import com.facishare.fs.contacts_fs.picker.DepartmentPicker;
import com.facishare.fs.contacts_fs.dep_level.SelectInDepLevelActivity;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.pluginapi.contact.beans.AEmpSimpleEntity;
import com.facishare.fs.ui.adapter.exp.UserManageAdapter;
import com.facishare.fs.utils_fs.AdapterUtils;
import com.fxiaoke.cmviews.SideBar;
import com.fxiaoke.dataimpl.msg.ObservableCenter;
import com.fxiaoke.fxdblib.utils.DbToolsApi;
import com.fxiaoke.fxlog.FCLog;

import android.app.Activity;
import android.content.Intent;
import android.database.DataSetObserver;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ListView;
import android.widget.RelativeLayout;

/**
 * Created by zengjj on 2016/2/25.
 */
public class SelectEmpNewFragment extends Fragment {

    private View rootView;
    /**
     * 同事列表
     */
    private ListView mEmpListView;
    private SideBar indexBar;

    private BaseActivity mActivity;

    private boolean mIsRequestSession = false;

    int mFeedId = -1;

    private int myID = -1;

    private boolean onlyChooseOne = false;

    /**
     * 查询同事时,是否包含自己,默认为false,包含自己
     */
    private boolean noself = true;

    /**
     * 不显示的员工id数组
     */
    private int[] mNoShowEmpArray = null;

    /**
     * 是否回填选人数据 新版要求不处理回填数据
     */
//    private ArrayList<Integer> mDefaultEmpsList;

    /**
     * 同事列表
     */
    private List<EmpIndexLetter> employees = null;
    /**
     * 同事列表适配器
     */
    private UserManageAdapter mShareRangeAdapter = null;

    /**
     * 星标控制器
     */
    private StarDataCtrler mStarDataCtrler = new StarDataCtrler();
    /**
     * 星标部门
     */
    private ArrayList<EmpIndexLetter> mStarEmployees = new ArrayList<EmpIndexLetter>();
    /**
     * 指定显示的人员id列表
     */
    ArrayList<Integer> mSpecifiedEmpList;
    /**
     * 增加相关团队人员列表
     */
    ArrayList<Integer> mRelatedTeamEmpList;
    private final static char[] indexCharArr = new char[]{
            '#', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N',
            'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
    };
    private final static char[] indexCharArrWithSpecified = new char[]{
            '相','#', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N',/* ignore i18n *///l-18nIgnore
            'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
    };
    /**
     * 是否显示按组织架构查看
     */
    boolean isShowChooseByCircle = true;
    ILaunchChatTickEventLis mTickEventCallBack = null;
    ICircleLayoutClickLis mClickChooseByCircle = null;
    public void setClickChooseByCircle(
            ICircleLayoutClickLis mClickChooseByCircle) {
        this.mClickChooseByCircle = mClickChooseByCircle;
    }

    public void refreshData() {
        Activity activity = getActivity();
        if (activity == null || activity.isFinishing()) {
            return;
        }
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                refreshList();
            }
        });
    }

    public interface ICircleLayoutClickLis{
       void onClick();
    }
    public SelectEmpNewFragment() {
        super();
    }

    public static SelectEmpNewFragment newInstance(boolean noSelf,
                                                   boolean onlyChooseOne, boolean isRequestSession, int myID,
                                                   ArrayList<Integer> defaultEmpsList, int[] filtEmpIds, int feedId) {
        return newInstance(noSelf,onlyChooseOne,isRequestSession,myID,defaultEmpsList,filtEmpIds,feedId,
                null,true);
    }

    public static SelectEmpNewFragment newInstance(boolean noSelf,
                                                   boolean onlyChooseOne, boolean isRequestSession, int myID,
                                                   ArrayList<Integer> selectedEmpList, int[] filtEmpIds, int feedId,
                                                   ArrayList<Integer> specifiedEmpList,boolean showChooseByCircle) {
        return newInstance(noSelf,onlyChooseOne,isRequestSession,myID,selectedEmpList,filtEmpIds,feedId,
                specifiedEmpList,showChooseByCircle,null);
    }
    public static SelectEmpNewFragment newInstance(boolean noSelf,
                                                   boolean onlyChooseOne, boolean isRequestSession, int myID,
                                                   ArrayList<Integer> selectedEmpList, int[] filtEmpIds, int feedId,
                                                   ArrayList<Integer> specifiedEmpList,boolean showChooseByCircle,
                                                   ILaunchChatTickEventLis lis){
        return newInstance(noSelf,onlyChooseOne,isRequestSession,myID,selectedEmpList,filtEmpIds,feedId,
                specifiedEmpList,showChooseByCircle,lis,null);
    }
    /**
     *q
     * @param noSelf 是否包含自己
     * @param onlyChooseOne 是否单选
     * @param isRequestSession 是否转发到群session
     * @param myID 当前登录账号id
     * @param selectedEmpList 默认选中的人员id列表
     * @param filtEmpIds 过滤掉不显示的id列表
     * @param feedId 关联的feedid
     * @param specifiedEmpList 指定要展示的人员列表，该值不为空时，不需要从本地再次读取数据用以展示 add by ywg 17.4.28
     * @param relatedTeamEmpList 展示在相关团队分组的人员列表，add by ywg 18.4.24
     * @return
     */
    public static SelectEmpNewFragment newInstance(boolean noSelf,
                                                   boolean onlyChooseOne, boolean isRequestSession, int myID,
                                                   ArrayList<Integer> selectedEmpList, int[] filtEmpIds, int feedId,
                                                   ArrayList<Integer> specifiedEmpList,boolean showChooseByCircle,
                                                   ILaunchChatTickEventLis lis,ArrayList<Integer> relatedTeamEmpList) {
        SelectEmpNewFragment fragment = new SelectEmpNewFragment();
        Bundle data = new Bundle();
        // 我自己是否显示
        data.putBoolean("noSelf", noSelf);
        // 默认多选
        data.putBoolean("onlyChooseOne", onlyChooseOne);
        data.putBoolean("isRequestSession", isRequestSession);
        data.putInt("myID", myID);
        data.putInt("feedId", feedId);
        data.putIntArray("filtEmpIds", filtEmpIds);
//        data.putIntegerArrayList("defaultEmpsList", selectedEmpList);//新版要求不支持回填数据功能
        if(specifiedEmpList!=null&&specifiedEmpList.size()>0){
            data.putIntegerArrayList("specifiedEmpList", specifiedEmpList);
        }
        data.putBoolean("isShowChooseByCircle", showChooseByCircle);
        if(relatedTeamEmpList!=null&&relatedTeamEmpList.size()>0){
            data.putIntegerArrayList("relatedTeamEmpList", relatedTeamEmpList);
        }
        fragment.setArguments(data);
        fragment.mTickEventCallBack = lis;
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.mActivity = (BaseActivity) getActivity();
        if (getArguments() != null) {

            //        mIsRequestSession = isRequestSession;
            //        mFeedId = feedId;
            //        this.myID = myID;
            //        // 默认多选
            //        this.onlyChooseOne = onlyChooseOne;
            //        // 我自己是否显示
            //        this.noself = noSelf;
            mFeedId = getArguments().getInt("feedId");
            mIsRequestSession = getArguments().getBoolean("isRequestSession");
            this.noself = getArguments().getBoolean("noSelf");
            this.onlyChooseOne = getArguments().getBoolean("onlyChooseOne");
            this.myID = getArguments().getInt("myID");
            this.mNoShowEmpArray = getArguments().getIntArray("filtEmpIds");
            //picker清除数据
            DepartmentPicker.releasePicked();
//            this.mDefaultEmpsList = getArguments().getIntegerArrayList("defaultEmpsList");
            this.mSpecifiedEmpList = getArguments().getIntegerArrayList("specifiedEmpList");
            this.mRelatedTeamEmpList = getArguments().getIntegerArrayList("relatedTeamEmpList");
            this.isShowChooseByCircle = getArguments().getBoolean("isShowChooseByCircle");
        }
        handleBackPickConflict();
//        if (mDefaultEmpsList != null && mDefaultEmpsList.size() > 0) {
//            DepartmentPicker.pickEmployees(mDefaultEmpsList, true);
//        }
        //picker备份数据
        DepartmentPicker.backup();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        if (rootView != null) {
            return rootView;
        }
        rootView = inflater.inflate(R.layout.select_emp_new_fragment_layout, container, false);

        initView();

        ObservableCenter.getInstance().addObserver(mActivity);

        initObserver();

        return rootView;
    }

    private void initView() {
        //同事列表
        mEmpListView = (ListView) rootView.findViewById(R.id.lvColl);
        LayoutInflater li = mActivity.getLayoutInflater();
        if(isShowChooseByCircle){//需要显示头部按组织架构查看时，才添加对应view
            View headerView = li.inflate(R.layout.select_user_layout_listheader_new, null);
            mEmpListView.addHeaderView(headerView);
            initOtherSelect(headerView);
        }
        mEmpListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                // 修改为底层统一管理选中数据的方式
                //                FCLog.w("mEmpListView position" + position);
                //                int headViewCount = mEmpListView.getHeaderViewsCount();
                //                AEmpSimpleEntity emp = employees.get(position - headViewCount);
                //                FCLog.w("mEmpListView position" + position+" ,headViewCount: "+headViewCount);
                //                boolean isSelected = DepartmentPicker.isEmployeePicked(emp.employeeID);
                //                AEmpSimpleEntity emp = employees.get(position - headViewCount);
                //                // 反选
                //                DepartmentPicker.pickEmployee(emp.employeeID, !isSelected);
                //修复monkey检测出的问题
                // java.lang.ArrayIndexOutOfBoundsException: length=3060; index=-1
                //      at java.util.ArrayList.get(ArrayList.java:315)
                //      at com.facishare.fs.contacts_fs.fragment.SelectEmpNewFragment$1.onItemClick
                // (SelectEmpNewFragment.java:195)
                EmpIndexLetter emp = (EmpIndexLetter) parent.getItemAtPosition(position);
                if (emp != null) {
                    boolean isSelected = DepartmentPicker.isEmployeePicked(emp.employeeID);
                    ISelectDepTypeListenerUtil.getUtil().changeSelectedStatus(emp.employeeID);
                    // 反选
                    DepartmentPicker.pickEmployee(emp.employeeID, !isSelected);
                }
            }
        });
        mEmpListView.setOnTouchListener(new View.OnTouchListener() {

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                mActivity.hideInput();
                return false;
            }
        });
        indexBar = (SideBar) rootView.findViewById(R.id.sideBar);
        indexBar.setTextView(indexBar.getDialogText());

        getEmpListData();

        filterEmpId(employees);

        checkInitContactOperator();

        mShareRangeAdapter = new UserManageAdapter(mActivity, mEmpListView, employees);
        initIndexBar(employees, mShareRangeAdapter);
//        filterEmpId();
    }

    private void checkInitContactOperator() {
        if (co != null) {
            return;
        }
        co = new ContactOperator();
        co.setEmpDataSource(employees);
    }

    public ContactOperator getCo() {
        checkInitContactOperator();
        return co;
    }

    /**
     * 初始化同事列表
     *
     * @param data
     */
    ContactOperator co;
    private void initEmpList(List<EmpIndexLetter> data) {
        if (noself) {
            for (int i = 0; i < data.size(); i++) {
                EmpIndexLetter entity = data.get(i);
                if (entity.employeeID == myID) {
                    data.remove(i);
                    break;
                }
            }
        }
        // 把自己放到最前面
        //mShareRangeAdapter.setSelfFirst(true, myID);
    }
    Runnable clickShowDepLevelBack = null;//点击按组织架构查看的回调；

    public void setClickShowDepLevelBack(Runnable clickShowDepLevelBack) {
        this.clickShowDepLevelBack = clickShowDepLevelBack;
    }
    /**
     * 同事列表头部
     *
     * @param container
     */
    void initOtherSelect(View container) {

        RelativeLayout ll_select_bydep = (RelativeLayout) container
                .findViewById(R.id.ll_select_by_dep);
        // 按部门选择
        ll_select_bydep.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (clickShowDepLevelBack != null) {//外界设置了点击回调，就走外界的
                    clickShowDepLevelBack.run();
                    return;
                }
                Intent intent = SelectInDepLevelActivity.getIntentEX(mActivity,
                        0, true, false, true, 0, true,null,co, false,0);
                mActivity.startActivityForResultNoAnimation(intent,
                        SelectSessionOrEmpConstants.ACTIVITY_RST_REQ_CODE_SELECT_in_deplevel);
                if(mClickChooseByCircle!=null){
                    mClickChooseByCircle.onClick();
                }
            }
        });
    }

    private void initIndexBar(List<?> e, BaseShareAdapter adapter) {
        if (e != null && !e.isEmpty()) {
            indexBar.setVisibility(View.VISIBLE);
        }
        mEmpListView.setAdapter(adapter);
        indexBar.setListView(mEmpListView);
        boolean isZHLang = LanguageUtil.isCurrentChineseLang();//中文简体和繁体环境下，'相'是一样的，故可以作为一样的索引分类
        if (isZHLang && mRelatedTeamEmpList != null && mRelatedTeamEmpList.size() > 0) {
            indexBar.setCharIndex(indexCharArrWithSpecified);
        } else {
            indexBar.setCharIndex(indexCharArr);
        }
        indexBar.setCharCollection(AdapterUtils.convertChar(e));
    }

    /**
     * 获取显示的同事数据
     */
    private void getEmpListData() {
        checkInitEmployees();
        if (employees != null && !employees.isEmpty()) {
            addStarData();
            addReleatedTeamData();
            initEmpList(employees);
            return;
        }
    }

    private void checkInitEmployees() {
        if (employees != null) {
            return;
        }
        if (mSpecifiedEmpList != null && mSpecifiedEmpList.size() > 0) {
            employees = FSContextManager.getCurUserContext().getCacheEmployeeData().getOrderedEmpListWihtOnlyIndexLetterByArrayID(mSpecifiedEmpList);
            //            Collections.sort(employees, new ComparatorAEmpSimpleEntity());
        } else {
            // 设置过滤
            //            OrgnizationOperator.clearStaticConfigs();
            //            OrgnizationOperator ops = new OrgnizationOperator();
            employees = FSContextManager.getCurUserContext().getCacheEmployeeData().getOrderEmployeeCache();
        }
    }

    private void addReleatedTeamData() {
        if (mRelatedTeamEmpList != null && mRelatedTeamEmpList.size() > 0) {
            List<EmpIndexLetter> relatedTeamEmpList = FSContextManager.getCurUserContext()
                    .getCacheEmployeeData().getOrderedEmpListWihtOnlyIndexLetterByArrayID(mRelatedTeamEmpList);
            List<EmpIndexLetter> relatedTeamEmpCopyList = new ArrayList<>();
            for (EmpIndexLetter item : relatedTeamEmpList) {
                EmpIndexLetter target = new EmpIndexLetter();
                DbToolsApi.copyAttributeByAttr(item, target);
                target.setIndexLetter(I18NHelper.getText("xt.select_emp_new_fragment.text.x")/* 相 */);
                relatedTeamEmpCopyList.add(target);
            }
            employees.addAll(0, relatedTeamEmpCopyList);
        }
    }

    public class ComparatorAEmpSimpleEntity implements Comparator {

        public int compare(Object arg0, Object arg1) {
            AEmpSimpleEntity preEmp = (AEmpSimpleEntity) arg0;
            AEmpSimpleEntity nextEmp = (AEmpSimpleEntity) arg1;
            String preNameOrder = "";
            if(!TextUtils.isEmpty(preEmp.getNameOrder())){
                preNameOrder = preEmp.getNameOrder();
            }
            String nextNameOrder = "";
            if(!TextUtils.isEmpty(nextEmp.getNameOrder())){
                nextNameOrder = nextEmp.getNameOrder();
            }
            return preNameOrder.compareTo(nextNameOrder);
        }
    }
    /**
     * 处理回填数据包含过滤的情况，以过滤为准,新版要求不处理回填数据
     */
    private void handleBackPickConflict() {
//        if (mDefaultEmpsList != null && mDefaultEmpsList.size() > 0) {
//            if (noself) {
//                mDefaultEmpsList.remove(Integer.valueOf(myID));
//            }
//            if (mNoShowEmpArray != null && mNoShowEmpArray.length > 0) {
//                for (int i = 0; i < mNoShowEmpArray.length; i++) {
//                    mDefaultEmpsList.remove(Integer.valueOf(mNoShowEmpArray[i]));
//                }
//            }
//        }

    }

    /**
     * 添加星标部门
     */
    private void addStarData() {
        FCLog.w("addStarData before data size " + employees.size());
        // 移除现有的星标部门，后面添加
        if (mStarEmployees.size() > 0) {
            for (int i = 0; i < mStarEmployees.size(); i++) {
                employees.remove(0);
            }
        }
        mStarEmployees = (ArrayList<EmpIndexLetter>) mStarDataCtrler.getStarEmp();
        if (mStarEmployees.size() == 0) {

            // mStarDepartments.add(mStarDataCtrler.makeDumyDep());
        } else {
            employees.addAll(0, mStarEmployees);
        }
        FCLog.w("addStarData after data size " + employees.size());
    }

    /**
     * 观察者
     */
    private DataSetObserver mObserver;

    /**
     * 初始化选人观察者
     */
    private void initObserver() {
        mObserver = new DataSetObserver() {
            @Override
            public void onChanged() {
                refreshList();
            }
        };
        DepartmentPicker.registerPickObserver(mObserver);
        RelatedEmpPicker.registerPickObserver(mObserver);

    }

    /**
     * 刷新同事列表
     */
    private void refreshList() {
        if (mShareRangeAdapter != null) {
            mShareRangeAdapter.notifyDataSetChanged();
        }
    }

    /**
     * 释放观察者
     */
    private void releaseObserver() {
        DepartmentPicker.unregisterPickObserver(mObserver);
        RelatedEmpPicker.unregisterPickObserver(mObserver);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        ObservableCenter.getInstance().deleteObserver(mActivity);
        releaseObserver();
    }

    private void filterEmpId(List<EmpIndexLetter> e) {
        if (mNoShowEmpArray == null || e == null) {
            return;
        }
        Iterator<EmpIndexLetter> iterator = e.iterator();
        while (iterator.hasNext()) {
            EmpIndexLetter emp = iterator.next();
            for (int i = 0; i < mNoShowEmpArray.length; i++) {
                if (emp.employeeID == mNoShowEmpArray[i]) {
                    iterator.remove();
                }
            }
        }
    }

    private void filterEmpIdById(List<AEmpSimpleEntity> e, int id) {
        Iterator<AEmpSimpleEntity> iterator = e.iterator();
        while (iterator.hasNext()) {
            AEmpSimpleEntity emp = iterator.next();
            if (emp.employeeID == id) {
                iterator.remove();
                break;
            }
        }
    }

    /**
     * 新的过滤方法
     */
//    private void filterEmpId() {
//        if (mNoShowEmpArray == null || mNoShowEmpArray.length < 1) {
//            return;
//        }
//        List<Integer> list = new ArrayList<Integer>();
//        for (int i = 0; i < mNoShowEmpArray.length; i++) {
//            list.add(Integer.valueOf(mNoShowEmpArray[i]));
//        }
//        OrgnizationOperator.setFilterEmployeeIds(list);
//    }

    public void onVisible() {
        if (mTickEventCallBack != null) {
            mTickEventCallBack.onFragmentVisible();
        }
    }
}
