/*
 * Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.contacts_fs.fragment;


import android.app.Activity;
import android.database.DataSetObserver;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.HeaderViewListAdapter;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;

import com.facishare.fs.biz_function.webview.JsApiWebActivity;
import com.facishare.fs.biz_session_msg.subbiz.interconnectenterprise.utils.CrossDbUtils;
import com.facishare.fs.contacts_fs.AllSelectedListener;
import com.facishare.fs.contacts_fs.adapter.RelatedEmpAdapter;
import com.facishare.fs.contacts_fs.beans.FriendEnterpriseData;
import com.facishare.fs.contacts_fs.beans.FriendEnterpriseEmployeeData;
import com.facishare.fs.contacts_fs.beans.SelectSessionOrEmpArg;
import com.facishare.fs.contacts_fs.datactrl.ContactDbOp;
import com.facishare.fs.contacts_fs.picker.RelatedEmpPicker;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.db.dao.FriendEnterpriseEmployeeDao;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.pluginapi.contact.beans.RelatedEmp;
import com.facishare.fs.utils_fs.EmployeeKeyUtils;
import com.facishare.fslib.R;
import com.fxiaoke.fshttp.web.http.WebApiUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * A placeholder fragment containing a simple view.
 */
public class SelectRelatedEmpByEaFragment extends Fragment {



//    public static final String RelatedEmpGuide = "/fsh5/ic/5.4/index.html?embed=0#/icintroduce";
    public static final String RelatedEmpGuide = "/fsh5/fs-connect-app/5.6/faq.html#/icintroduce";

    public interface OnItemClickListener {
        public void onItemClick(int pos, RelatedEmp data);
    }

    public static class Arg implements Serializable {

        /**
         * 是否单选
         */
        public boolean singleSelect = false;
        /**
         * 企业帐号
         */
        public String enterprise;
        /**
         * 选择条样式
         */
        public int barType = SelectRelatedContactBarFrag.TYPE_NONE;
        /**
         * 是否显示复选框
         */
        public boolean showCheckBox = false;
        /**
         * 是否显示内部通讯类
         */
        public boolean showLocal = false;
        /**
         * 是否显示按互联企业选择
         */
        public boolean showRelateSelect = true;

        public boolean contactBarClick = true ;

        public boolean shouldShowLocal = true ;
        /**
         * 是否显示内部通讯类
         */
        public SelectSessionOrEmpArg.SelectMode selectMode = SelectSessionOrEmpArg.SelectMode.DEFAULT_MODE;
        /**
         * 选择的外部对接人（仅管理员使用，普通员工为null）
         */
        public List<FriendEnterpriseEmployeeData> employeeList = null;
        /**
         * 选择的互联企业（仅管理员使用，普通员工为null）
         */
        public List<FriendEnterpriseData> enterpriseList;

    }



    private Arg mArg;
    private OnItemClickListener mItemClickListener;

    private List<RelatedEmp> mDataList = new ArrayList<>();
    private ListView mListView;

    private View mEmptyContainer;
    private TextView mEmptyGuide;

    public SelectRelatedEmpByEaFragment() {
    }

    private DataSetObserver mDataObserver;

    /**
     * 获得intent，多选用
     *
     * @param arg
     * @return
     */
    public static final SelectRelatedEmpByEaFragment newInstance(Arg arg) {
        SelectRelatedEmpByEaFragment f = new SelectRelatedEmpByEaFragment();
        Bundle args = new Bundle();
        args.putSerializable("arg", arg);
        f.setArguments(args);
        return f;
    }

    /**
     * 全选
     */
    public void selectAll() {
        Map<Integer, String> map = new LinkedHashMap<>();
        for (RelatedEmp user : mDataList) {
            map.put(user.getId(), user.getEnterpriseAccount());
        }
        RelatedEmpPicker.pickRelatedEmpMap(map);
    }

    /**
     * 全不选
     */
    public void unselectAll() {
        Set<String> set = new LinkedHashSet<>();
        for (RelatedEmp user : mDataList) {
            set.add(user.getEnterpriseAccount() + "-" + user.getId());
        }
        RelatedEmpPicker.unpickRelatedEmpIdSet(set);
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        if (activity instanceof OnItemClickListener) {
            mItemClickListener = (OnItemClickListener) activity;
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        mItemClickListener = null;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            mArg = (Arg) getArguments().getSerializable("arg");
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        initData();
        View view = inflater.inflate(R.layout.select_related_emp_fragment, container, false);
        mListView = (ListView) view.findViewById(R.id.listView);

        mListView.setAdapter(new RelatedEmpAdapter(getActivity(), mDataList,
                mArg.enterprise, mArg.showCheckBox,mArg.enterpriseList, R.layout.select_related_emp_item));
        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                int headViewCount = mListView.getHeaderViewsCount();
                RelatedEmp relatedEmp = mDataList.get(position - headViewCount);
                if (mArg.showCheckBox) {
                    if (mArg.singleSelect){
                        RelatedEmpPicker.clear();
                        RelatedEmpPicker.pickRelatedEmp(relatedEmp);
                    }else {
                        boolean picked = RelatedEmpPicker.isRelatedEmpPicked(relatedEmp);
                        if (picked) {
                            RelatedEmpPicker.unpickRelatedEmp(relatedEmp);
                        } else {
                            RelatedEmpPicker.pickRelatedEmp(relatedEmp);
                        }
                        if (allSelectedListener != null) {
                            triggerAllSelectedStateChanged(!picked);
                        }
                    }

                } else {
                    if (mItemClickListener != null) {
                        mItemClickListener.onItemClick(position - headViewCount, relatedEmp);
                    }
                }

            }
        });
        if (allSelectedListener != null) {
            // 进入界面初始化“全选”状态
            triggerAllSelectedStateChanged(true);
        }

        mDataObserver = new DataSetObserver() {
            @Override
            public void onChanged() {
                ListAdapter adapter = mListView.getAdapter();
                if (adapter instanceof HeaderViewListAdapter) {
                    ListAdapter adapter2 = ((HeaderViewListAdapter) mListView.getAdapter()).getWrappedAdapter();
                    ((BaseAdapter) adapter2).notifyDataSetChanged();
                } else {
                    ((BaseAdapter) adapter).notifyDataSetChanged();
                }
            }
        };
        mEmptyGuide = (TextView) view.findViewById(R.id.select_related_empty_guide);
        mEmptyGuide.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JsApiWebActivity
                        .startNeedCookie(getActivity(), WebApiUtils.requestUrl + RelatedEmpGuide, I18NHelper.getText("qx.session_list.sessionname.cross_entry")/* 企业互联 */, true, true);
            }
        });
        if (mDataList.size() == 0) {
            boolean hasExternalEnterprise = !mArg.shouldShowLocal || CrossDbUtils.hasExternalEnterprise();
            if (!hasExternalEnterprise) {
                mEmptyContainer = view.findViewById(R.id.related_empty_no_related_enterprise);
            } else if (mArg.showCheckBox) {
                mEmptyContainer = view.findViewById(R.id.related_empty_no_employee);
                ((TextView) mEmptyContainer.findViewById(R.id.empty_text)).setText(I18NHelper.getText("qx.contacts_fs.SelectEmpInEnterpriseFragment.2")/* 你还没有可选的外部联系人 */);
            } else {
                mEmptyContainer = view.findViewById(R.id.related_empty_no_employee);
                ((TextView) mEmptyContainer.findViewById(R.id.empty_text)).setText(I18NHelper.getText("qx.contacts_fs.SelectEmpInEnterpriseFragment.1")/* 你还没有可显示的外部联系人 */);
            }
            mEmptyContainer.setVisibility(View.VISIBLE);
            //        mListView.setEmptyView(mEmptyContainer);
        }
        RelatedEmpPicker.registerPickObserver(mDataObserver);
        return view;
    }

    int emptyIconResId = -1;
    String emptyText;

    public void setEmptyViewStyle(int iconResId, String emptyText) {
        this.emptyIconResId = iconResId;
        this.emptyText = emptyText;
    }

    private void initData() {
        mDataList.clear();
//        FriendEnterpriseEmployeeDao dao = FSContextManager.getCurUserContext().getContactDbHelper().getFriendEnterpriseEmployeeDao();
        try {
            List<FriendEnterpriseEmployeeData> dataList = null;
            if (TextUtils.isEmpty(mArg.enterprise)) {
                // 没有传入企业号，代表查看全部
                if (mArg.employeeList != null && mArg.employeeList.size() != 0) {
                    dataList = mArg.employeeList;
                } else if (mArg.shouldShowLocal){
                    if (!mArg.showCheckBox) {
                        dataList = ContactDbOp.findAllExternalEmployee( );
                    } else {
                        dataList = ContactDbOp.findAllExternalEmployee();
                        // dataList = dao.findAllByType(mArg.outType == OutType.WeiXinBC_Type ? ContactType.WX : ContactType.FS);
                    }
                }
            } else {
                if (mArg.employeeList != null && mArg.employeeList.size() != 0) {
                    dataList = new ArrayList();
                    for (FriendEnterpriseEmployeeData f : mArg.employeeList) {
                        if (mArg.enterprise.equals(f.enterpriseAccount)) {
                            dataList.add(f);
                        }
                    }
                } else if (mArg.shouldShowLocal){

                    if (!mArg.showCheckBox) {
                        dataList = ContactDbOp.findExternalEmployeeByEnterpriseAccount(mArg.enterprise);
                    } else {
                        dataList = ContactDbOp.findExternalEmployeeByEnterpriseAccount(mArg.enterprise);
                        // dataList = dao.findListByEnterpriseAccount(mArg.enterprise,
                        //         mArg.outType == OutType.WeiXinBC_Type ? ContactType.WX : ContactType.FS);
                    }
                }
            }
            if (dataList != null){
                for (FriendEnterpriseEmployeeData d : dataList) {
                    RelatedEmp user = new RelatedEmp(d);
                    if (mArg.selectMode.mode == SelectSessionOrEmpArg.SelectMode.MODE_NORMAL) {
                        mDataList.add(user);
                    } else if (mArg.selectMode.mode == SelectSessionOrEmpArg.SelectMode.MODE_CLIP) {
                        if (!mArg.selectMode.clipSet.contains(EmployeeKeyUtils.keyOf(user))) {
                            mDataList.add(user);
                        }
                    } else if (mArg.selectMode.mode == SelectSessionOrEmpArg.SelectMode.MODE_WHITE_LIST) {
                        if (mArg.selectMode.clipIdSet.contains(String.valueOf(user.getEmpCardId()))) {
                            mDataList.add(user);
                        }
                    }
                }
            }
            sortCollection(mDataList);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void sortCollection(List<RelatedEmp> mDataList) {
        Collections.sort(mDataList, new Comparator<RelatedEmp>() {
            @Override
            public int compare(RelatedEmp lhs, RelatedEmp rhs) {
                int ret = 0;
                if (!TextUtils.isEmpty(lhs.getNameOrder()) && !TextUtils.isEmpty(rhs.getNameOrder())) {
                    //返回值都不为空
                    ret = lhs.getNameOrder().charAt(0) - rhs.getNameOrder().charAt(0);
                } else if (TextUtils.isEmpty(lhs.getNameOrder()) && TextUtils.isEmpty(rhs.getNameOrder())) {
                    //返回值都为空
                    ret = 0;
                } else if (TextUtils.isEmpty(lhs.getNameOrder())) {
                    //lhs返回值为空
                    ret = -1;
                } else {
                    //rhs返回值为空
                    ret = 1;
                }
                return ret;
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        RelatedEmpPicker.unregisterPickObserver(mDataObserver);
    }

    private AllSelectedListener allSelectedListener;

    public void setOnSelectStateChangeListener(AllSelectedListener listener) {
        allSelectedListener = listener;
    }

    private void triggerAllSelectedStateChanged(boolean toPick) {
        int allSeletedState = AllSelectedListener.ALL_SELECTED;
        if (mDataList.size() == 0) {
            allSeletedState = AllSelectedListener.SELETED_DISABLE;
        } else if (!toPick) {
            allSeletedState = AllSelectedListener.ALL_NOT_SELECTED;
        } else {
            for (RelatedEmp emp : mDataList) {
                if (!RelatedEmpPicker.isRelatedEmpPicked(emp)) {
                    allSeletedState = AllSelectedListener.ALL_NOT_SELECTED;
                    break;
                }
            }
        }
        allSelectedListener.onAllSelectedStateChanged(allSeletedState);
    }
}
