/*
 * Copyright (C) 2023 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.contacts_fs.fragment;

import java.io.Serializable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

import com.facishare.fs.contacts_fs.adapter.PublicEmployeesAdapter;
import com.facishare.fs.contacts_fs.beans.EmployeeKey;
import com.facishare.fs.contacts_fs.beans.FriendEnterpriseEmployeeData;
import com.facishare.fs.contacts_fs.public_emp.beans.PublicEmployeesItem;
import com.facishare.fs.contacts_fs.public_emp.beans.PublicEmployeesWithSimpleInfoResult;
import com.facishare.fs.contacts_fs.picker.RelatedEmpPicker;
import com.facishare.fs.contacts_fs.utils.PublicEmployeesMixedDataUtils;
import com.facishare.fs.contacts_fs.public_emp.PublicEmployeesWebApiUtils;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.pluginapi.contact.beans.RelatedEmp;
import com.facishare.fslib.R;
import com.fxiaoke.cmviews.view.NoContentView;
import com.fxiaoke.cmviews.xlistview.XListView;

import android.app.Activity;
import android.content.Context;
import android.database.DataSetObserver;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.HeaderViewListAdapter;
import android.widget.ListAdapter;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.StringDef;
import androidx.fragment.app.Fragment;

/**
 *  本fragment作为新的互联用户和上游员工复用的展示组件 （PublicEmployees 作为关键词区分已有的业务）
 */
public class PublicEmployeesMixedFragment extends Fragment implements XListView.IXListViewListener {
    private RelativeLayout relativeLayout_clientname_loading, relativeLayout_clientname_content;

    public Context context;
//    private View LinearLayout_no_data;
    private NoContentView mEmptyView;
    private XListView mListView;
    private PublicEmployeesAdapter mAdapter;
    private DataSetObserver mDataObserver;
	final int PAGE_SIZE = 20;//50 默认
	private int mCurrentPageNumber = 1;
	private long mDestOuterTenantId = 0;
	private long mTenantId = 0;//上游企业id
    private int mResultTotalDataCount = 0;//接口返回的全部人员的总数计数

    LayoutInflater mLayoutInflater;

    private boolean mShowCheckBox = true;
    private String mSourceSelectType = "PublicEmployees";//"ExtContacts",//区分外部联系人、互联用户PublicEmployees、上游员工UpstreamPublicEmployees，
    private long mLimitCount = 0;//1表示单选，大于2多选

    private Arg mArg;
    private SelectRelatedEmpFragment.OnItemClickListener mItemClickListener;

    @Override
    public void onAttach(Context activity) {
        super.onAttach(activity);
        if (activity instanceof SelectRelatedEmpFragment.OnItemClickListener) {
            mItemClickListener = (SelectRelatedEmpFragment.OnItemClickListener) activity;
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        mItemClickListener = null;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        RelatedEmpPicker.unregisterPickObserver(mDataObserver);
    }

	@Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        context = getActivity();
        if (getArguments() != null) {
            mArg = (PublicEmployeesMixedFragment.Arg) getArguments().getSerializable("arg");
        }
        if (mArg != null) {
            mShowCheckBox = mArg.showCheckBox;
            mSourceSelectType = mArg.sourceType;
            if (mArg.singleSelect) {
                mLimitCount = 1;
            }
        }
    }
    public static final PublicEmployeesMixedFragment newInstance(Arg arg) {
        PublicEmployeesMixedFragment f = new PublicEmployeesMixedFragment();
        Bundle args = new Bundle();
        args.putSerializable("arg", arg);
        f.setArguments(args);
        return f;
    }


    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View newsLayout = inflater.inflate(R.layout.public_employees_simple_info_fragment, container, false);
        mLayoutInflater = inflater;
        initView(newsLayout);
        return newsLayout;
    }

    private void initView(View view) {

        relativeLayout_clientname_loading = (RelativeLayout) view.findViewById(R.id.relativeLayout_list_loading);
        relativeLayout_clientname_loading.setVisibility(View.VISIBLE);

        relativeLayout_clientname_content = (RelativeLayout) view.findViewById(R.id.relativeLayout_person_at);
//        LinearLayout_no_data = (View) view.findViewById(R.id.LinearLayout_no_data);
        mEmptyView = (NoContentView)view.findViewById(R.id.empty_view);
        mEmptyView.setText(I18NHelper.getText("crm.opportunity.OpportunityPipeFrag.1086")/* 暂时还没有数据 */);

        mListView = (XListView) view.findViewById(R.id.listview_person_at);
        mListView.setPullLoadEnable(true);
        mListView.setPullRefreshEnable(true);
        mListView.setXListViewListener(this);
        //		mListView.setFastScrollEnabled(true);
        mListView.setDivider(null);

        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {

            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if(mListView.getItemAtPosition(position) instanceof PublicEmployeesItem){
                    int headViewCount = mListView.getHeaderViewsCount();
                    PublicEmployeesItem item = (PublicEmployeesItem) mListView.getItemAtPosition(position);
                    if (mArg.showCheckBox) {
//                        String fullId = "E."+item.getEnterpriseAccount()+"."+ item.getEmployeeId();
//                        int dataType = PublicEmployeesMixedDataUtils.getDataTypeBySelectType(mSourceSelectType);
                        PublicEmployeesMixedDataUtils.processOnItemClickBySelect(item, mSourceSelectType, mArg.singleSelect);
                    } else {
                        if (mItemClickListener != null) {
                            FriendEnterpriseEmployeeData friend = new FriendEnterpriseEmployeeData();
                            friend.enterpriseAccount = item.getEAForSessionParticipant();
                            friend.employeeId = item.getIdForSessionParticipant();
                            friend.employeeName = item.getEmployeeName();
                            RelatedEmp data = new RelatedEmp(friend);
                            mItemClickListener.onItemClick(position - headViewCount, data);
                        }
                    }
                }

                //				final FeedReplyEntity feedReplyEntity = (FeedReplyEntity) mListView.getItemAtPosition(position);
                //				if(feedReplyEntity!=null){
                //					FeedsUitls.showReplyDetail2(context,feedReplyEntity,mAdapter);
                //				}
            }
        });
        mDataObserver = new DataSetObserver() {
            @Override
            public void onChanged() {
                ListAdapter adapter = mListView.getAdapter();
                if (adapter != null) {
                    if (adapter instanceof HeaderViewListAdapter) {
                        ListAdapter adapter2 = ((HeaderViewListAdapter) mListView.getAdapter()).getWrappedAdapter();
                        ((BaseAdapter) adapter2).notifyDataSetChanged();
                    } else {
                        ((BaseAdapter) adapter).notifyDataSetChanged();
                    }
                }
            }
        };
        RelatedEmpPicker.registerPickObserver(mDataObserver);
        onRefresh();
    }
    boolean isLoadingRefresh   = false;
    @Override
    public void onRefresh() {
        if(isLoadingRefresh){
            return;
        }
        isLoadingRefresh  = true;
        mCurrentPageNumber = 1;
        if (mAdapter != null) {
            mAdapter.updateList(new ArrayList<>());
        }
        sendListRq();
    }
    boolean isLoadingMore   = false;
    @Override
    public void onLoadMore() {
        if(isLoadingMore){
            return;
        }
        isLoadingMore = true;
        sendMoreListRq();
    }

    private void sendListRq() {
        PublicEmployeesWebApiUtils.IQueryPublicEmployeesCallBack callBack = new PublicEmployeesWebApiUtils.IQueryPublicEmployeesCallBack() {
            @Override
            public void onSuccess(String searchText, PublicEmployeesWithSimpleInfoResult result) {
                mCurrentPageNumber += 1;
                boolean hasMoreData = false;
                if (result != null && result.getData() != null && result.getData().getDataList() != null) {
                    String currentEA = FSContextManager.getCurUserContext().getAccount().getEnterpriseAccount();
                    String currentUpStreamEA = FSContextManager.getCurUserContext().getAccount().getUpstreamEa();
                    boolean isCurrentLoginVisitor = FSContextManager.getCurUserContext().getAccount().isVisitorLogin();

                    List<PublicEmployeesItem> filteredList = null;
                    int filteredDataCount = 0;//记录被过滤掉的数据数目
                    if (mArg == null || mArg.clipSet == null || mArg.clipSet.size() <= 0) {
//                        filteredList = result.getData().getDataList();
                        filteredList = new ArrayList<>();
                        for (PublicEmployeesItem item : result.getData().getDataList()) {
                            if (PublicEmployeesMixedDataUtils.isValidData(item)) {
                                if (PublicEmployeesMixedDataUtils.isVisitorPublicEmployeesData(item)) {
                                    item.setUpstreamEA(isCurrentLoginVisitor ? currentUpStreamEA : currentEA);
                                }
                                filteredList.add(item);
                            } else {
                                filteredDataCount += 1;
                            }
                        }
                    } else {
                        filteredList = new ArrayList<>();
                        for (PublicEmployeesItem item : result.getData().getDataList()) {
                            if (PublicEmployeesMixedDataUtils.isValidData(item)
//                                    && !mArg.clipSet.contains(new EmployeeKey(item.getEnterpriseAccount(), item.getEmployeeId()))) {
                                    && !mArg.clipSet.contains(item.transToEmployeeKey())) {
                                if (PublicEmployeesMixedDataUtils.isVisitorPublicEmployeesData(item)) {
                                    item.setUpstreamEA(isCurrentLoginVisitor ? currentUpStreamEA : currentEA);
                                }
                                filteredList.add(item);
                            } else {
                                filteredDataCount += 1;
                            }
                        }
                    }
                    initData(filteredList);
                    hasMoreData = (mAdapter.getCount()+filteredDataCount) < result.getData().getTotal();
                    mResultTotalDataCount = result.getData().getTotal();
                }
                endPress(hasMoreData);
            }

            @Override
            public void onFailed(String showTipError) {
                endPress(false);
            }
        };
        if (mSourceSelectType.equals(SelectSourceType.PUBLIC_EMPLOYEE)) {
            PublicEmployeesWebApiUtils.queryPublicEmployeesWithSimpleInfo(mDestOuterTenantId, null, PAGE_SIZE,
                    mCurrentPageNumber, callBack);
        } else if (mSourceSelectType.equals(SelectSourceType.UPSTREAM_PUBLIC_EMPLOYEES)) {
            PublicEmployeesWebApiUtils.queryUpstreamPublicEmployees(mTenantId, null, PAGE_SIZE,
                    mCurrentPageNumber, callBack);
        }
    }

    //更多
    private void sendMoreListRq() {
        sendListRq();
    }

    private void initData(List<PublicEmployeesItem> cirList) {
        if (mAdapter == null) {
            mAdapter = new PublicEmployeesAdapter(context, cirList, mShowCheckBox, mSourceSelectType);
            mListView.setAdapter(mAdapter);
            mAdapter.notifyDataSetChanged();
        } else {
            mAdapter.addAllList(cirList);
        }
    }

    private void endPress(boolean hasMoreData) {
        isLoadingRefresh = false;
        isLoadingMore = false;
        relativeLayout_clientname_loading.setVisibility(View.GONE);
        relativeLayout_clientname_content.setVisibility(View.VISIBLE);
        Date date = new Date();
        if (!hasMoreData) {
            mListView.onLoadSuccessEx2(date);
        }
        mListView.onLoadSuccess(date);
        mListView.setEmptyView(mEmptyView);
        checkInitCrossHeader(mAdapter != null && mAdapter.getCount() > 0);
    }
    View mCrossHeaderView;
    private void checkInitCrossHeader(boolean showCrossEaEntry) {
	    if(!showCrossEaEntry){
            if(mCrossHeaderView!=null){
                mCrossHeaderView.setVisibility(View.GONE);
                return;
            }
	        return;
        }
	    if(mCrossHeaderView!=null){
            mCrossHeaderView.setVisibility(View.VISIBLE);
            return;
        }
	    if(mLayoutInflater == null){
            mLayoutInflater = LayoutInflater.from(getContext());
        }
        if (FSContextManager.getCurUserContext().getAccount().isVisitorLogin()) {
            return;//无租户账号不支持按企业查看或选择上游员工
        }
        mCrossHeaderView = mLayoutInflater.inflate(R.layout.select_user_layout_listheader_new, null);
        TextView tvDesc = (TextView) mCrossHeaderView.findViewById(R.id.textView_desc);
        tvDesc.setText(mShowCheckBox ? I18NHelper.getText("xt.selectrelatedempfragment.text.according_to_the_choice_of_connected_companies")/* 按互联企业选择 */ : I18NHelper.getText("xt.selectrelatedempfragment.text.view_by_connected_company")/* 按互联企业查看 */);
//        mCrossHeaderView.findViewById(R.id.view_divider).setVisibility(View.GONE);
        mListView.addHeaderView(mCrossHeaderView);
        mCrossHeaderView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Activity activity = getActivity();
                PublicEmployeesMixedDataUtils.selectCrossUsersByEa(activity, mSourceSelectType, !mArg.showCheckBox, new Runnable() {
                    @Override
                    public void run() {
                    }
                });
            }
        });
    }

    public int getTotalDataCount(){
        return mResultTotalDataCount;
    }

//    private AllSelectedListener allSelectedListener;
//
//    public void setOnSelectStateChangeListener(AllSelectedListener listener) {
//        allSelectedListener = listener;
//    }


    @StringDef({SelectSourceType.EXT_CONTACTS, SelectSourceType.PUBLIC_EMPLOYEE, SelectSourceType.UPSTREAM_PUBLIC_EMPLOYEES})
    @Retention(RetentionPolicy.SOURCE)
    public static @interface SelectSourceType {
        public static String INNER = "Inner";//内部人员
        public static String EXT_CONTACTS = "ExtContacts";//外部联系人
        public static String PUBLIC_EMPLOYEE = "PublicEmployees";//互联用户
        public static String UPSTREAM_PUBLIC_EMPLOYEES = "UpstreamPublicEmployees";//上游员工
    }

    public static class Arg implements Serializable {
        public transient Set<EmployeeKey> clipSet;//黑名单列表
        /**
         * 是否单选
         */
        public boolean singleSelect = false;
//        /**
//         * 企业帐号
//         */
//        public String enterprise;
        /**
         * 选择条样式
         */
        public int barType = SelectRelatedContactBarFrag.TYPE_IMG;//默认头像型
        /**
         * 是否显示复选框
         */
        public boolean showCheckBox = true;
//        /**
//         * 是否显示内部通讯类
//         */
//        public boolean showLocal = false;
        /**
         * 是否显示按互联企业选择
         */
        public boolean showSelectByEaEntry = true;

//        public boolean contactBarClick = true ;
//
//        public boolean shouldShowLocal = true ;
        public String sourceType = SelectSourceType.PUBLIC_EMPLOYEE ;//默认数据源类型

        public static Arg create4SelectEmp(@SelectSourceType String type, boolean singleSelect) {
            Arg arg = new Arg();
            arg.sourceType = type;
            arg.singleSelect = singleSelect;
            arg.showCheckBox = true;
            return arg;
        }

        public static Arg create4SelectEmp(@SelectSourceType String type, boolean singleSelect, boolean showSelectByEaEntry) {
            Arg arg = new Arg();
            arg.sourceType = type;
            arg.singleSelect = singleSelect;
            arg.showSelectByEaEntry = showSelectByEaEntry;
            arg.showCheckBox = true;
            return arg;
        }
        /**
         *  通讯录查看时，不选择，只查看
         * @param type
         * @return
         */
        public static Arg create4Contact(@SelectSourceType String type) {
            Arg arg = new Arg();
            arg.sourceType = type;
            arg.showCheckBox = false;
            return arg;
        }
    }
}
