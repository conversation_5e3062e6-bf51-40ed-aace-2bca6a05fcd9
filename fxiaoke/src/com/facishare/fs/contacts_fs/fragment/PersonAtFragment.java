package com.facishare.fs.contacts_fs.fragment;



import java.util.Date;

import android.content.Context;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.RelativeLayout;

import com.facishare.fslib.R;
import com.facishare.fs.biz_feed.adapter.HomeAdapter;
import com.facishare.fs.biz_feed.bean.FeedEntity;
import com.facishare.fs.biz_feed.bean.GetFeedsResponse;
import com.fxiaoke.cmviews.xlistview.XListView;
import com.facishare.fs.contacts_fs.PersonAtFrameActivity;
import com.facishare.fs.ui.FeedsUitls;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.facishare.fs.web_business_utils.api.FeedService;
import com.alibaba.fastjson.TypeReference;
import com.fs.beans.beans.EnumDef.FeedType;
import com.fs.beans.beans.EnumDef.WorkFeedFilterType;

public class PersonAtFragment extends Fragment  implements XListView.IXListViewListener,OnClickListener{
	private RelativeLayout relativeLayout_clientname_loading,relativeLayout_clientname_content;
	private XListView mListView;

	private Context context;

	private View LinearLayout_no_data;
	private HomeAdapter mAdapter;
	
	private Long lastUpdateTime;
	
	private int empId = 0;
	private GetFeedsResponse feedsResponse;
	@Override 
	public void onCreate(Bundle savedInstanceState) {
		// TODO Auto-generated method stub
		super.onCreate(savedInstanceState);
		Bundle bundle = getArguments();
		context = getActivity();
		empId = bundle.getInt(PersonAtFrameActivity.EMPID_KEY);
	}	
	@Override 
	public View onCreateView(LayoutInflater inflater, ViewGroup container,
			Bundle savedInstanceState) {
		View newsLayout = inflater.inflate(R.layout.person_at_fragment, container,false);
		initView(newsLayout);
		return newsLayout;
	}
	
	private void initView(View view)
	{

		relativeLayout_clientname_loading = (RelativeLayout)view.findViewById(R.id.relativeLayout_list_loading);
		relativeLayout_clientname_loading.setVisibility(View.VISIBLE);
		
		relativeLayout_clientname_content = (RelativeLayout)view.findViewById(R.id.relativeLayout_person_at);
		LinearLayout_no_data = (View)view.findViewById(R.id.LinearLayout_no_data);

		
		mListView = (XListView)view.findViewById(R.id.listview_person_at);
		mListView.setPullLoadEnable(true);
		mListView.setPullRefreshEnable(true);
		mListView.setXListViewListener(this);
//		mListView.setFastScrollEnabled(true);
		mListView.setDivider(null);
		
		
		mListView.setOnItemClickListener(new OnItemClickListener() {
			@Override 
			public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
				final FeedEntity feed = (FeedEntity) mListView.getAdapter().getItem((int) position);
				
				if (feed != null) {
					if (feed.isEncrypted) {
						FeedsUitls.decrypt(context,view,feed,mAdapter);
					}else {
						FeedsUitls.showDetailsInfo(context,feed,feedsResponse);
					}
				}

			}
		});
		
		onRefresh();
	}
	
	@Override 
	public void onRefresh() {
		// TODO Auto-generated method stub
		mListView.setPullLoadEnable(true);

		sendListRq();
		
		
	}

	@Override 
	public void onLoadMore() {
		// TODO Auto-generated method stub
		sendMoreListRq();
	}
	
	
    private void sendListRq()
    {

        new FeedService().getWorkFeeds(WorkFeedFilterType.AtMe, 10, null, null, null, FeedType.All, null,empId
                , new WebApiExecutionCallback<GetFeedsResponse>() {
            @Override 
            public TypeReference<WebApiResponse<GetFeedsResponse>> getTypeReference() {
                return new TypeReference<WebApiResponse<GetFeedsResponse>>() {
                };
            }

            @Override 
            public void completed(Date time, GetFeedsResponse response) {
				if (response != null){ 
					feedsResponse = response;
					initData(response);
					if (response.size() < 10) {
						mListView.onLoadSuccessEx2(time);
					}else {
 						mListView.onLoadSuccess(time);
					}
				}
				endPress();
            }

			@Override 
			public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
				endPress();
			}

		});
    }

    //更多
    private void sendMoreListRq()
    {
    	
    	if (mAdapter != null && mAdapter.getCount() > 0) {
    		lastUpdateTime = ((FeedEntity)mAdapter.getItem(mAdapter.getCount() - 1)).lastUpdateTime.getTime();
    		
		}else {
			lastUpdateTime = 0l;
		}
        new FeedService().getWorkFeeds(WorkFeedFilterType.AtMe, 10, lastUpdateTime, null, null, FeedType.All, null,empId
                , new WebApiExecutionCallback<GetFeedsResponse>() {
            @Override 
            public TypeReference<WebApiResponse<GetFeedsResponse>> getTypeReference() {
                return new TypeReference<WebApiResponse<GetFeedsResponse>>() {
                };
            }

            @Override 
            public void completed(Date time, GetFeedsResponse response) {
				if (response != null){ 
					
					if (feedsResponse == null) {
						feedsResponse = response;
						mAdapter.setGetFeedsResponse(feedsResponse);
					} else {
						feedsResponse.copyFrom(response);
					}

					mAdapter.notifyDataSetChanged();
					

					if (response.size() < 10) {
						mListView.onLoadSuccessEx2(time);
					}else {
						mListView.onLoadSuccess(time);
					}
				}
				endPress();
            }

			@Override 
			public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
				endPress();
			}

		});
    }
    
	private void initData(GetFeedsResponse response)
	{
		if (mAdapter == null) {
			mAdapter = new HomeAdapter(context, mListView, response);
			mListView.setAdapter(mAdapter);
		}else {
			mAdapter.setGetFeedsResponse(response);
			mAdapter.notifyDataSetChanged();
		}

	}
    
	@Override

    public void onDestroy() {

    	// TODO Auto-generated method stub

    	super.onDestroy();

    	if (mAdapter!=null) {

    		mAdapter.clear();

		}
    }

	private void endPress()
	{
		relativeLayout_clientname_loading.setVisibility(View.GONE);
		relativeLayout_clientname_content.setVisibility(View.VISIBLE);
		mListView.onLoadSuccess(new Date());
		
		if (mAdapter != null && mAdapter.getCount() > 0) {
			LinearLayout_no_data.setVisibility(View.GONE);
		}else {
			LinearLayout_no_data.setVisibility(View.VISIBLE);
		}
	}

	@Override 
	public void onClick(View v) {
		// TODO Auto-generated method stub
		
	}
}
