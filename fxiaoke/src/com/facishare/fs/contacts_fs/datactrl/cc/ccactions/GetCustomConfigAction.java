/*
 * Copyright (C) 2024 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.contacts_fs.datactrl.cc.ccactions;

import java.util.HashMap;

import com.alibaba.fastjson.JSONObject;
import com.billy.cc.core.component.CC;
import com.billy.cc.core.component.CCResult;
import com.facishare.fs.account_system.persistent.PersistentAccountDataBySP;
import com.facishare.fs.contacts_fs.datactrl.ContactConfigUpdateProxy;
import com.facishare.fs.contacts_fs.datactrl.cc.IContactCCAction;
import com.facishare.fs.contacts_fs.public_emp.PublicEmployeesEntryCtr;
import com.fxiaoke.dataimpl.contacts.ContactConfigProvider;

public class GetCustomConfigAction implements IContactCCAction {
    @Override
    public boolean onCall(CC cc) {
        HashMap<String,Object> successData = new HashMap<>();
        ContactConfigProvider contact = ContactConfigProvider.getInstance();

        //getOrganizationConfig接口提供的数据——同事tab展示形式是否支持按字母和按组织架构查看
        JSONObject personnelConfig = new JSONObject();
        personnelConfig.put("organization", contact.canShowPersonnelOrganization() ? "1" : "");
        personnelConfig.put("letter", contact.canShowPersonnelLetter() ? "1" : "");
        personnelConfig.put("defaultValue", contact.isDefaultShowPersonnelLetter() ? "letter" : "organization");
        successData.put("personnelConfig", personnelConfig);

        //getOrganizationConfig接口提供的数据——部门tab展示形式是否支持按字母和按组织架构查看
        JSONObject departmentConfig = new JSONObject();
        departmentConfig.put("organization", contact.canShowDepartmentOrganization() ? "1" : "");
        departmentConfig.put("letter", contact.canShowDepartmentLetter() ? "1" : "");
        departmentConfig.put("defaultValue", contact.isDefaultShowDepartmentLetter() ? "letter" : "organization");
        successData.put("departmentConfig", departmentConfig);


        //BatchGetEnterpriseConfig接口提供的数据——是否隐藏全公司，true表示隐藏
        boolean hide_company_info = contact.isHideCompanyInfo();
        successData.put("hide_company_info", hide_company_info);

        //BatchGetEnterpriseConfig接口提供的数据——是否隐藏通讯录配置
        boolean hide_contact_directory = contact.isHideContactDirectory();
        successData.put("hide_contact_directory", hide_contact_directory);

        //BatchGetEnterpriseConfig接口提供的数据——是否隐藏组织/部门人员统计
        boolean hide_department_stats = contact.isHideDepartmentStats();
        successData.put("hide_department_stats", hide_department_stats);

        //互联选人tab的数据
        PublicEmployeesEntryCtr employeesCtr = PublicEmployeesEntryCtr.getInstance();

        //erSelectorTabState接口提供的数据——是否可展示或选择互联用户tab
        boolean showPublicEmployeeTab = employeesCtr.isShowPublicEmployeeTab();
        successData.put("showPublicEmployeeTab", showPublicEmployeeTab);

        //erSelectorTabState接口提供的数据——是否可展示或选择上游员工tab
        boolean showUpstreamEmployeeTab = employeesCtr.isShowUpstreamEmployeeTab();
        successData.put("showUpstreamEmployeeTab", showUpstreamEmployeeTab);

        CC.sendCCResult(cc.getCallId(), CCResult.success(successData));
        return false;
    }
}
