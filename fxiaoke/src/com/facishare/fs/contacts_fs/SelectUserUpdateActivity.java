
package com.facishare.fs.contacts_fs;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.facishare.fs.BaseFragmentActivity;
import com.facishare.fs.biz_session_msg.SessionCreateRecommendListActivity;
import com.facishare.fs.biz_session_msg.SessionMsgActivity;
import com.facishare.fs.biz_session_msg.beans.QixinStatisticsEvent;
import com.facishare.fs.biz_session_msg.beans.SelectSessionOrEmpConstants;
import com.facishare.fs.biz_session_msg.constants.SessionConstants;
import com.facishare.fs.biz_session_msg.dialog.DialogButtonCallBak;
import com.facishare.fs.biz_session_msg.dialog.SessionDialogUtils;
import com.facishare.fs.biz_session_msg.utils.SessionCreateUtils;
import com.facishare.fs.contacts_fs.beans.DiscussionGroup;
import com.facishare.fs.contacts_fs.beans.SelectGroupFlags;
import com.facishare.fs.contacts_fs.customerservice.util.ISelectDepTypeListenerUtil;
import com.facishare.fs.contacts_fs.datactrl.ContactConfigDataUtils;
import com.facishare.fs.contacts_fs.datactrl.ContactOperator;
import com.facishare.fs.contacts_fs.dep_level.fragment.SeeInDepLevelWithCrumbFragment;
import com.facishare.fs.contacts_fs.dep_level.fragment.SelectInDepLevelWithCrumbFragment;
import com.facishare.fs.contacts_fs.fragment.ContactSelectBarFrag;
import com.facishare.fs.contacts_fs.fragment.ILaunchChatTickEventLis;
import com.facishare.fs.contacts_fs.fragment.SelectDepNewFragment;
import com.facishare.fs.contacts_fs.fragment.SelectEmpNewFragment;
import com.facishare.fs.contacts_fs.picker.DepartmentPicker;
import com.facishare.fs.contacts_fs.picker.RelatedEmpPicker;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.list.select_obj.picker.MultiObjectPicker;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.ISessionMsg;
import com.facishare.fs.pluginapi.contact.beans.AEmpSimpleEntity;
import com.facishare.fs.pluginapi.contact.beans.CircleEntity;
import com.facishare.fs.pluginapi.crm.CrmDiscussConfig;
import com.facishare.fs.pluginapi.crm.CrmDiscussType;
import com.facishare.fs.utils_fs.EmployeeUtils;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fslib.R;
import com.fxiaoke.cmviews.viewpager.ViewPagerCtrl;
import com.fxiaoke.dataimpl.contacts.intent_provider.SelectUserIP;
import com.fxiaoke.dataimpl.msg.ITaskListener;
import com.fxiaoke.dataimpl.msg.MsgDataController;
import com.fxiaoke.fscommon.util.CommonDataContainer;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.SessionTypeKey;
import com.fxiaoke.lib.qixin.biz_ctrl.CrmBizUtils;
import com.fxiaoke.lib.qixin.biz_ctrl.IGetLocalBusinessSessionLis;
import com.fxiaoke.lib.qixin.biz_ctrl.IGetNetBusinessSessionLis;
import com.fxiaoke.lib.qixin.biz_ctrl.constant.TrustSessionConstant;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Pair;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.viewpager.widget.ViewPager;
import android.text.TextUtils;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.FrameLayout;

/**
 * 创建讨论组 发起对话 会议选人
 * 
 * <AUTHOR>
 */
public class SelectUserUpdateActivity extends BaseFragmentActivity implements ViewPager.OnPageChangeListener {

    public static final String RESULTE_ANSWER_KEY="resultkey";

    /**
     * 调用入口
     */
    //发起对话跳转
    public static final int ACTIVITY_CREATE_SESSION_SELECT=1;
    //添加会议人员
    public static final int ACTIVITY_MEETING_EMP_SELECT=2;
    private int mComeId=1;
    public static String ACTIVITY_START_ID="comeid";

    /**
     * 表示 转发时选择session
     */
    public static String IntentKey_isRequestSession = "isRequestSession";
    public static String IntentKey_Feedid = "select_user_feedid";

    int mFeedId = -1;

    /**
     * 是否允许单选,默认false多选
     */
    private boolean onlyChooseOne = false;
    public static final String ONLY_CHOOSEONE_KEY = "onlyChooseOne";
    /**
     * 查询同事时,是否包含自己,默认为true,不包含自己
     */
    private boolean noself = true;
    public static final String SHARE_NOSELF_KEY = "share_noself_key";
    /**
     * 不显示的员工id数组
     */
    private int[] mNoShowEmpArray = null;
    public static final String NO_ShOW_EMP_ARRAY = "no_show_emp_array";

    private int myID = -1;

    private List<Integer> listparticipantsIDs = new ArrayList<Integer>();

    private boolean mIsRequestSession = false;
    /**
     * 是否回填选人数据
     */
    private ArrayList<Integer> mDefaultEmpsList;
    public static final String IS_RECOVER_PICK_EMP_KEY = "is_recover_pick_emp_key";

    /**
     * 分享范围标题
     */
    private static final String SHARE_TITLE_KEY = "share_title_key";
    private String mTitleStr = null;

    /**
     * 显示哪些群组
     */
    private int mSelectGroupFlags = 0;
    /**
     * 显示哪些群组
     */
    public static final String SELECT_GROUP_FLAGS = "select_group_flags";



    private ViewPagerCtrl mViewPagerCtrl;
    private int INDEX_SHOW_EMP = 0;//同事显示在的分组索引
    //fragment
    private SelectEmpNewFragment mEmpNewFragment;
    private int INDEX_SHOW_DEP = 1;//群组显示在的分组索引
    private SelectDepNewFragment mDepNewFragment;
    private SelectEmpNewFragment mSpecialedEmpFragment;//新增tab
    private int INDEX_SHOW_SPECIFIED = 2;//新增tab显示在的分组索引
    private List<AEmpSimpleEntity> mEmpDatas;
    private List<CircleEntity> mDepDatas;

    public static String IntentKey_Crm_Config = "select_user_config_crm_data";//crm信息
    String mCrmId = null;//crm信息，目前暂为客户id
    CrmDiscussType mCrmType ;//crm类型

    private FrameLayout mFrameLayout;

    /**
     * 表示当前展示的内容，0表示人员，1表示部门
     */
    private int currentContent=0;

    /**
     * 新增tab里指定显示的人员id列表，有数据则显示新增tab，否则不显示
     */
    ArrayList<Integer> mSpecifiedEmpList;
    /**
     * 新增tab里，是否显示“按组织架构查看”
     */
    boolean isShowChooseByCircle = true;
	/**
     * 新增tab的name
     */
    String newTabName = "";
    public static String INTENT_EXTRA_IS_JUST_SELECT ="isJustForSelectSession";
    /**
     * 是否只为选中session会话，而不是选中后进入会话
     */
    boolean isJustForSelectSession = false;
    /**
     * 用这个方法
     * @param context
     * @param title 标题
     * @param noSelf 是否包含自己 true表示不包括
     * @param onlyChooseOne true只能选择一个人
     * @param isRequestSession
     * @param empsMap
     * @param filtEmpIds 要过滤的员工的ID数组
     * @param comeId  1表示从发起对话，2表示选人
     * @param isShowDepart  ture表示显示部门，false不显示
     * @return
     */
    public static Intent getIntent(Context context, String title, boolean noSelf, boolean onlyChooseOne,
                                   boolean isRequestSession, Map<Integer, String> empsMap, int[] filtEmpIds,int comeId,boolean isShowDepart) {
        return getIntent(context,title,noSelf,onlyChooseOne,isRequestSession,empsMap,filtEmpIds,-1,comeId,
                isShowDepart? SelectGroupFlags.SHOW_DEFAULT : SelectGroupFlags.SHOW_RECENT_GROUP);
    }
    /**
     * 选择企信成员入口
     *
     * @param context
     * @param title
     * @param noSelf 是否包含自己 true表示不包括
     * @param onlyChooseOne
     * @param isRequestSession 是否需要转发
     * @param empsMap
     * @param filtEmpIds
     * @return
     */
    public static Intent getIntent(Context context, String title, boolean noSelf,
                                   boolean onlyChooseOne, boolean isRequestSession, Map<Integer, String> empsMap, int[] filtEmpIds,int feedId,int comeId,int selectGroupFlags){
        Intent intent = new Intent(context, SelectUserUpdateActivity.class);
        if (!TextUtils.isEmpty(title)) {
            intent.putExtra(SHARE_TITLE_KEY, title);
        }
        intent.putExtra(SHARE_NOSELF_KEY, noSelf);
        intent.putExtra(ONLY_CHOOSEONE_KEY, onlyChooseOne);
        if(feedId > 0){
            intent.putExtra(IntentKey_Feedid, feedId);
        }
        intent.putExtra(IntentKey_isRequestSession, isRequestSession);
        if (filtEmpIds != null) {
            intent.putExtra(NO_ShOW_EMP_ARRAY, filtEmpIds);
        }
        if (empsMap != null && empsMap.size() > 0) {
            ArrayList<Integer> empList = new ArrayList<Integer>();
            empList.addAll(empsMap.keySet());
            intent.putIntegerArrayListExtra(IS_RECOVER_PICK_EMP_KEY, empList);
        }
        intent.putExtra(ACTIVITY_START_ID, comeId);
        intent.putExtra(SELECT_GROUP_FLAGS, selectGroupFlags);
        return intent;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.select_user_update_layout);
        //picker清除数据
        DepartmentPicker.releasePicked();
        parseIntent(getIntent());

        initTitle();

        initView();
    }

    ContactOperator co;
    private void parseIntent(Intent intent) {
        if (intent != null) {
            mTitleStr = intent.getStringExtra(SHARE_TITLE_KEY);

            mIsRequestSession = intent.getBooleanExtra(IntentKey_isRequestSession, false);
            mFeedId = intent.getIntExtra(IntentKey_Feedid, -1);

            CrmDiscussConfig config = (CrmDiscussConfig) intent.getSerializableExtra(IntentKey_Crm_Config);
            if(config!=null){
                mCrmId = config.getCrmId();
                mCrmType = config.getType();
            }

            myID = FSContextManager.getCurUserContext().getAccount().getEmployeeIntId();
            // 默认多选
            onlyChooseOne = intent.getBooleanExtra(ONLY_CHOOSEONE_KEY, false);
            // 我自己是否显示
            noself = intent.getBooleanExtra(SHARE_NOSELF_KEY, true);
            // 过滤的员工
            mNoShowEmpArray = intent.getIntArrayExtra(NO_ShOW_EMP_ARRAY);
            List<Integer> filterEmp=new ArrayList<>();
            if(noself){
                filterEmp.add(myID);
            }
            if(mNoShowEmpArray!=null&&mNoShowEmpArray.length>0){
                for(int eId:mNoShowEmpArray){
                    filterEmp.add(eId);
                }
            }
            if(filterEmp.size()>0){
                co=new ContactOperator();
                co.addFilterEmployeeIds(filterEmp);
            }

            //picker回填数据
            mDefaultEmpsList = fiterInvalidId(intent.getIntegerArrayListExtra(IS_RECOVER_PICK_EMP_KEY));

            mComeId=intent.getIntExtra(ACTIVITY_START_ID, 1);

            mSelectGroupFlags=intent.getIntExtra(SELECT_GROUP_FLAGS,0);

            //获取feed讨论参数
//            smt = (SessionMessageTemp)intent.getSerializableExtra(SessionMsgActivity.Intent_key_MSG);
            this.mSpecifiedEmpList = intent.getIntegerArrayListExtra(SelectUserIP.NEW_TAB_SPECIFIED_EMP_LIST);
            this.newTabName = intent.getStringExtra(SelectUserIP.NEW_TAB_NAME);
            this.isShowChooseByCircle = intent.getBooleanExtra(SelectUserIP.NEW_TAB_is_show_choose_by_circle,true);
            isJustForSelectSession = intent.getBooleanExtra(INTENT_EXTRA_IS_JUST_SELECT,false);
        }
    }

    /**
     * 过滤掉无效的人员id
     * @param integerArrayList
     * @return
     */
    private ArrayList<Integer> fiterInvalidId(ArrayList<Integer> integerArrayList) {
        ArrayList<Integer> integers = new ArrayList<>();
        if(integerArrayList!=null&&integerArrayList.size()>0){
            integers = new ArrayList<>();
            for(Integer itemId:integerArrayList){
                if(itemId > 0){
                    integers.add(itemId);
                }
            }
            if(integers.size()==0){
                integers = null;
            }
        }
        return integers;
    }

    /**
     * 初始化视图
     */
    private void initView(){

        mFrameLayout = (FrameLayout) findViewById(R.id.bottom_fragment);
        // 底部碎片
        FragmentTransaction ft_bottom = getSupportFragmentManager().beginTransaction();
        ContactSelectBarFrag selectBarFrag = new ContactSelectBarFrag();
        selectBarFrag.setShowType(ContactSelectBarFrag.ShowType.Img);
        selectBarFrag.setAutoHide(true);
        selectBarFrag.setConfirmClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                QixinStatisticsEvent.tick(QixinStatisticsEvent.LAUNCHCHAT_EMPLOYEE_OK);
                onClickConfirm();
            }
        });
        ft_bottom.add(R.id.bottom_fragment, selectBarFrag);
        ft_bottom.commitAllowingStateLoss();

        mViewPagerCtrl = (ViewPagerCtrl) findViewById(R.id.pager);
        mViewPagerCtrl.init(this);
        mViewPagerCtrl.setOnPageChangeListener(this);
        initViewPagerCtrlByFragmentList();
    }

    private void initViewPagerCtrlByFragmentList() {
        List<String> fragmentTitleList = new ArrayList<>();
        List<Fragment> contentFragmentList = new ArrayList<>();
        int firstShowFragmentIndex = 0;
        addEmpFragment(fragmentTitleList, contentFragmentList);
        addGroupFragment(fragmentTitleList, contentFragmentList);
        addSpecifiedEmpFragment(fragmentTitleList, contentFragmentList);
//        test
//        SelectEmpNewFragment testEmpNewFragment = SelectEmpNewFragment.newInstance(noself, onlyChooseOne, mIsRequestSession, myID,
//                mDefaultEmpsList, mNoShowEmpArray, mFeedId, null, true, null);
//        fragmentTitleList.add("同事test");
//        contentFragmentList.add(testEmpNewFragment);
        mViewPagerCtrl.addTabViewList(this, fragmentTitleList, contentFragmentList, firstShowFragmentIndex);
        mViewPagerCtrl.commitTab();
    }

    private void addEmpFragment(List<String> fragmentTitleList, List<Fragment> contentFragmentList) {
        INDEX_SHOW_EMP = fragmentTitleList.size();
        Pair<SelectEmpNewFragment, SelectInDepLevelWithCrumbFragment> pair = ContactConfigDataUtils
                .createSelectEmpNewFragment(mViewPagerCtrl, fragmentTitleList, contentFragmentList, noself, onlyChooseOne, mIsRequestSession, myID,
                        mDefaultEmpsList, mNoShowEmpArray, mFeedId, null/*, true*/);
        mEmpNewFragment = pair.first;
    }

    private void addGroupFragment(List<String> fragmentTitleList, List<Fragment> contentFragmentList) {
        Intent intent = getIntent();
        intent.putExtra(SelectDepNewFragment.Action_isSelectDisscution, true);
        intent.putExtra(IntentKey_isRequestSession, mIsRequestSession);
        ILaunchChatTickEventLis depLis = new ILaunchChatTickEventLis() {
            @Override
            public void onFragmentVisible() {
                QixinStatisticsEvent.tickPV(QixinStatisticsEvent.LAUNCHCHAT_DISCUSS_SHOW);
            }

            @Override
            public void onChooseCrmCustomer() {
                QixinStatisticsEvent.tickPV(QixinStatisticsEvent.LAUNCHCHAT_DISCUSS_ENTERCUSTOMERS);
            }

        };
        mDepNewFragment = SelectDepNewFragment.newInstance(intent, mComeId, mSelectGroupFlags, depLis, null);
        INDEX_SHOW_DEP = fragmentTitleList.size();
        fragmentTitleList.add(I18NHelper.getText("xt.selectuserupdateactivity.text.group")/* 群组 */);
        contentFragmentList.add(mDepNewFragment);
    }

    private void addSpecifiedEmpFragment(List<String> fragmentTitleList, List<Fragment> contentFragmentList) {
        if (mSpecifiedEmpList != null && mSpecifiedEmpList.size() > 0) {
            mSpecialedEmpFragment = SelectEmpNewFragment.newInstance(noself, onlyChooseOne, mIsRequestSession, myID,
                    mDefaultEmpsList, mNoShowEmpArray, mFeedId, mSpecifiedEmpList, false);
            INDEX_SHOW_SPECIFIED = fragmentTitleList.size();
            fragmentTitleList.add(newTabName);
            contentFragmentList.add(mSpecialedEmpFragment);
        }
    }

    /**
     * 初始化tittle
     */
    private void initTitle(){
        super.initTitleEx();
        mCommonTitleView.addLeftAction(I18NHelper.getText("commonfunc.dialog_fragment_fsmail_choose_attachment_menu.text.cancel")/* 取消 */, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
                finish();
            }
        });
        if (mTitleStr != null && mTitleStr.length() > 0) {
            mCommonTitleView.setTitle(mTitleStr);
        }else {
            mCommonTitleView.setTitle(I18NHelper.getText("xt.selectuserupdateactivity.text.choose_colleagues")/* 选择同事 */);
        }

        // 通讯录新搜索框
        mCommonTitleView.addRightAction(R.string.work_search, new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                if(mViewPagerCtrl.getCurIndex()==0 || mViewPagerCtrl.getCurIndex() == 2){
                    //跳转搜索员工界面
                    Intent intent = ContactSearchAct.getMultiSelectIntent(SelectUserUpdateActivity.this,
                            ContactSelectBarFrag.ShowType.Img, 0,QixinStatisticsEvent.LAUNCHCHAT_PREFIX,
                            null,co);
                    intent.putExtra(ContactSearchAct.AUTO_HIDE_BAR, true);
                    startActivityForResultNoAnimation(intent, SelectSessionOrEmpConstants.ACTIVITY_RST_REQ_CODE_SELECT_search);
                }else if(mViewPagerCtrl.getCurIndex()==1){
                    //跳转搜索群界面
                    CommonDataContainer.getInstance().saveData(DiscutionGroupSearchAct.listDataKey,mDepNewFragment.getmGroupDataList());

                    startActivityForResult(DiscutionGroupSearchAct.getIntent(SelectUserUpdateActivity.this, mSelectGroupFlags, QixinStatisticsEvent.LAUNCHCHAT_PREFIX),
                            SelectSessionOrEmpConstants.ACTIVITY_RST_REQ_CODE_GROUP_SEARCH);
                }
            }
        });
    }

    @Override
    public void onBackPressed() {
        QixinStatisticsEvent.tick(QixinStatisticsEvent.LAUNCHCHAT_CANCEL);
        DepartmentPicker.restore();
        setResult(RESULT_CANCELED);
        super.onBackPressed();
    }

    long mLastClickedTime = 0;
    /**
     * 点击确认、创建会话
     */
    public void onClickConfirm() {// TODO: 2017/3/9 可以控制下连续点击问题 ，也可提供阻塞式进度弹框
        long curClickedTime = System.currentTimeMillis();
        boolean isNeedRspClicked = true;
        if(curClickedTime - mLastClickedTime < 250){//屏蔽连点,以优化线上用户问题（建一个群，出来30几个群）
            isNeedRspClicked = false;
        }
        mLastClickedTime = curClickedTime;
        if(!isNeedRspClicked){
            return;
        }
        listparticipantsIDs.clear();
        listparticipantsIDs = DepartmentPicker.getEmployeesPicked();
        if (listparticipantsIDs.size() == 0) {
            ToastUtils.show(I18NHelper.getText("xt.selectuserupdateactivity.text.you_have_not_chosen_a_colleague,_please_choose_a_colleague")/* 您还没有选择同事，请选择同事 */);
            return;
        }
        if (mComeId == ACTIVITY_CREATE_SESSION_SELECT) {
            if (listparticipantsIDs.size() == 1) {
                SessionListRec tempSession = SessionCreateUtils.createSingleTempSession(listparticipantsIDs.get(0));
                processSessionResultEx(tempSession);
            } else {
                // TODO: 2017/12/1 还有可能是部门群，待优化
                SessionListRec tempSession = SessionCreateUtils.createGroupTempSession(SessionTypeKey.Session_Group_key,
                        SessionListRec.EnterpriseEnv.INNER.getEnterpriseType(), "",
                        "", EmployeeUtils.transId2ParticipantSLR(listparticipantsIDs),
                        mFeedId, mCrmId, mCrmType != null ? mCrmType.ordinal() : 0);
                processSessionResult(tempSession);
            }
        } else if (mComeId == ACTIVITY_MEETING_EMP_SELECT) {
            Intent it = new Intent();
            it.putExtra(RESULTE_ANSWER_KEY, (ArrayList) listparticipantsIDs);
            setResult(RESULT_OK, it);
            finish();
        }
    }

    /**
     * 检查是否已有群组推荐
     *
     * @param session
     */
    private void processSessionResult(SessionListRec session) {
        List<SessionListRec> recommendSessionList = SessionCreateRecommendListActivity.findRecommendSessionList(listparticipantsIDs);
        if (recommendSessionList.size() > 0) {
            Intent intent = SessionCreateRecommendListActivity.getIntent(context, session, recommendSessionList, true);
            startActivityForResult(intent, SelectSessionOrEmpConstants.REQUEST_SELECT_RECOMMEND_SESSION);
        } else {
            showDialog(DIALOG_WAITING_BASE);
            ContactAction.createInnerGroupSession(listparticipantsIDs, mFeedId, mCrmId, mCrmType, new ITaskListener() {
                @Override
                public void onSuccess(Object data) {
                    removeDialog(DIALOG_WAITING_BASE);
                    processSessionResultEx((SessionListRec) data);
                }

                @Override
                public void onProgress(Object data, int cur, int total) {
                    // TODO Auto-generated method stub
                }

                @Override
                public void onFailed(Object data) {
                    SelectUserUpdateActivity.this.removeDialog(DIALOG_WAITING_BASE);
                    MsgDataController.processFailed(context, data);
                }
            });
        }
    }

    private void processSessionResultEx(SessionListRec slr) {
        if (mIsRequestSession) {
            Intent it = new Intent();
            it.putExtra(SessionConstants.INTENT_KEY_SESSION_INFO, slr);
            setResultSafe(RESULT_OK, it);
        } else {
            SessionMsgActivity.startIntent(SelectUserUpdateActivity.this, slr);
        }
        close();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == SelectSessionOrEmpConstants.ACTIVITY_RST_REQ_CODE_SELECT_in_deplevel) {
            if (resultCode == RESULT_OK) {
                onClickConfirm();
            }
        } else if (requestCode == SelectSessionOrEmpConstants.ACTIVITY_RST_REQ_CODE_SELECT_search) {
            if (resultCode == RESULT_OK) {
                onClickConfirm();
            }
        } else if (requestCode == SelectSessionOrEmpConstants.ACTIVITY_RST_REQ_CODE_SELECT_user) {
            if (resultCode == RESULT_OK) {
//                if(mSelectSessionType!=null&&mSelectSessionType.equals(ReqType.TransmitNewMsg)){
//                    showRepostNewMsgConfirmDlg((SessionListRec)data.getSerializableExtra("sessioninfo"));
//
//                }else{
//
//                    showRepostConfirmDlg((SessionListRec)data.getSerializableExtra("sessioninfo"));
//                }
            }
        } else if (requestCode == SelectSessionOrEmpConstants.ACTIVITY_RST_REQ_CODE_GROUP_SEARCH) {
            if (resultCode == RESULT_OK) {
                DiscussionGroup dg = (DiscussionGroup) data.getSerializableExtra(DiscutionGroupSearchAct.RESULTDATA);
                mDepNewFragment.initListClick(dg);
            }
        } else if (requestCode == SelectSessionOrEmpConstants.ACTIVITY_RST_REQ_CODE_CHOOSE_CRM_CUSTOMER) {
            if (resultCode == RESULT_OK && data != null) {
                MultiObjectPicker picker = MultiObjectPicker.getPickerByIntent(data);
                if (picker != null) {
                    List<ObjectData> list = picker.getSelectedList();
                    if (!list.isEmpty()) {
                        if (mIsRequestSession) {
                            // 从转发等进来，把数据带回去
                            ObjectData objectData = list.get(0);
                            String customerId = objectData.getID();
                            SessionListRec cutomerSession = new SessionListRec();
                            cutomerSession.setSessionCategory(SessionTypeKey.Session_Business_Group_key);
                            cutomerSession.setSessionSubCategory("CrmCustomer-" + customerId);
                        cutomerSession.setSessionId(SessionListRec.makeTempSessionId(customerId));
                            cutomerSession.setSessionName(objectData.getName());
                            cutomerSession.setOrderingTime(System.currentTimeMillis());

                            Intent it = new Intent();
                            it.putExtra(SessionConstants.INTENT_KEY_SESSION_INFO, cutomerSession);
                            setResultSafe(RESULT_OK, it);
                            finish();
                        } else {
                            // 新建会话，直接新建并跳转
                            go2BusinessSession(SelectUserUpdateActivity.this,
                                    TrustSessionConstant.CUSTOMER_SESSION_ID_PREFIX, list.get(0).uniqueId(), "");
                        }
                    }
                }
            }
        } else if (requestCode == SelectSessionOrEmpConstants.ACTIVITY_RST_REQ_CODE_CHOOSE_CRM_OPPORTUNITY) {
            if (resultCode == RESULT_OK && data != null) {
                MultiObjectPicker picker = MultiObjectPicker.getPickerByIntent(data);
                if (picker != null && picker.getSelectedCount() == 1) {
                    ObjectData objectData = picker.getSelectedList().get(0);
                    String bizId = objectData.getID();
                    String objectName = objectData.getName();
                    if (mIsRequestSession) {
                        // 从转发等进来，把数据带回去
                        SessionListRec bizSession = new SessionListRec();
                        bizSession.setSessionCategory(SessionTypeKey.Session_Business_Group_key);
                        bizSession.setSessionSubCategory(TrustSessionConstant.OPPORTUNITY_SESSION_ID_PREFIX + "-" + bizId);
                        bizSession.setSessionId(SessionListRec.makeTempSessionId(bizId));
                        bizSession.setSessionName(objectName);
                        bizSession.setOrderingTime(System.currentTimeMillis());

                        Intent it = new Intent();
                        it.putExtra(SessionConstants.INTENT_KEY_SESSION_INFO, bizSession);
                        setResultSafe(RESULT_OK, it);
                        finish();
                    } else {
                        // 新建会话，直接新建并跳转
                        go2BusinessSession(SelectUserUpdateActivity.this, TrustSessionConstant.OPPORTUNITY_SESSION_ID_PREFIX, bizId, objectName);
                    }
                }
            }
        } else if (requestCode == SelectSessionOrEmpConstants.REQUEST_SELECT_RECOMMEND_SESSION) {
            if (resultCode == RESULT_OK) {
                SessionListRec chosenSession = (SessionListRec) data.getSerializableExtra(SessionCreateRecommendListActivity.RESULT_CHOSEN_SESSION);
                processSessionResultEx(chosenSession);
            }
        } else {
            DepartmentPicker.notifyPickDataChange();
        }
    }


    private void go2BusinessSession(final Context context, final String bizName, final String bizId, String objectName) {
        IGetLocalBusinessSessionLis callBack = new IGetLocalBusinessSessionLis() {

            @Override
            public void onSuccess(SessionListRec rec) {
                gotoSession(rec);
            }

            @Override
            public void onFailed(String error) {
                if (!TextUtils.isEmpty(error)) {
                    com.facishare.fs.common_utils.ToastUtils.show(error);
                }
            }

            @Override
            public void onGetNetSessionConfirm(String dialogTitle, String dialogContent) {
                SessionDialogUtils.showConfirmDialog(SelectUserUpdateActivity.this, dialogTitle, dialogContent, I18NHelper.getText("commonfunc.dialog_fragment_fsmail_choose_attachment_menu.text.cancel")/* 取消 */, I18NHelper.getText("av.common.string.confirm")/* 确定 */, new DialogButtonCallBak() {
                    @Override
                    public void onPositive(Object dialog) {
                        super.onPositive(dialog);

                        CrmBizUtils.createTrustSession(context, bizName, bizId, new IGetNetBusinessSessionLis() {
                            @Override
                            public void onSuccess(SessionListRec rec) {
                                gotoSession(rec);
                            }

                            @Override
                            public void onFailed(String error) {
                                if(!TextUtils.isEmpty(error)){
                                    com.facishare.fs.common_utils.ToastUtils.show(error);
                                }
                            }
                        });
                    }

                    @Override
                    public void onNegative(Object dialog) {
                        super.onNegative(dialog);
                    }
                });
            }

        };
        if (TrustSessionConstant.CUSTOMER_SESSION_ID_PREFIX.equals(bizName)) {
            CrmBizUtils.getLocalCustomerSession(context, bizId, callBack);
        } else if (TrustSessionConstant.OPPORTUNITY_SESSION_ID_PREFIX.equals(bizName)) {
            CrmBizUtils.getLocalOpportunitySession(context, bizId, callBack);
        }
    }

    private void gotoSession(SessionListRec rec) {
        ISessionMsg msg = HostInterfaceManager.getISessionMsg();
        if (msg != null) {
            msg.go2SendMsg(SelectUserUpdateActivity.this, rec, 0, null);
        }
        finish();
    }

    /**
     * 隐藏掉当前弹出的软键盘
     */
    public void hideInput() {
        InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        if (this.getCurrentFocus() != null) {
            inputMethodManager.hideSoftInputFromWindow(this.getCurrentFocus().getWindowToken(), 0);
        }
    }

    @Override
    public void onPageScrolled(int i, float v, int i1) {

    }

    @Override
    public void onPageSelected(int i) {
        if (INDEX_SHOW_EMP ==i ) {
            mFrameLayout.setVisibility(View.VISIBLE);
            mEmpNewFragment.onVisible();
        } else if (INDEX_SHOW_DEP == i ) {
            mFrameLayout.setVisibility(View.GONE);
            mDepNewFragment.onVisible();
        } else if (INDEX_SHOW_SPECIFIED == i) {
            mFrameLayout.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onPageScrollStateChanged(int i) {

    }

//    @Override
    //    public boolean onKeyDown(int keyCode, KeyEvent event) {
    //        //如果按下的是返回键，并且没有重复
    //        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
    //            close();
    //            return false;
    //        }
    //        return false;
    //    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ISelectDepTypeListenerUtil.getUtil().cleanAll();
        DepartmentPicker.restore();
        RelatedEmpPicker.clear();
    }

    @Override
    protected void onResume() {
        super.onResume();
        int showingFragmentIndex = mViewPagerCtrl.getCurIndex();
        if (INDEX_SHOW_EMP == showingFragmentIndex) {
            mEmpNewFragment.onVisible();
        } else if (INDEX_SHOW_DEP == showingFragmentIndex) {
            mDepNewFragment.onVisible();
        }
    }
}
