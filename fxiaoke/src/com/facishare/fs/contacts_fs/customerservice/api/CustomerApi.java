/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.contacts_fs.customerservice.api;

import com.facishare.fs.biz_session_msg.beans.CustomerCrmInfoVO;
import com.facishare.fs.contacts_fs.customerservice.mvp.beans.GetOuterServiceListResult;
import com.facishare.fs.contacts_fs.customerservice.mvp.beans.RedTipsResult;
import com.facishare.fs.contacts_fs.customerservice.mvp.beans.TipResult;
import com.facishare.fs.pluginapi.contact.beans.coustomer.CustomerResult;
import com.facishare.fs.pluginapi.contact.beans.coustomer.CustomerUpdate;
import com.facishare.fs.pluginapi.contact.beans.coustomer.CustomerUpdateResult;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiParameterList;
import com.fxiaoke.fshttp.web.http.WebApiUtils;

import java.util.List;

import static com.facishare.fs.contacts_fs.customerservice.api.CustomerApi.Action.RED_TIPS;
import static com.facishare.fs.contacts_fs.customerservice.api.CustomerApi.Action.SET_TIPS;

/**
 * Created by 钟光燕 on 2016/3/30.
 * ===================================================
 * <p/>
 * 客服服务号所需的网络接口
 * <p/>
 * ===================================================
 */
public class CustomerApi {

    protected final static String controller = "FHE/EM1AOPEN/Messenger";
    protected final static String outer_service_controller = "FHE/EM1AOPEN/OuterService";
    protected final static String redTipsController = "FHE/EM1AOPEN/OpenAppGuide";

    protected final static String wechat_outer_service_controller = "FHE/EM1AWECHATUNION/OuterService";

    public interface Action {
        String GET_CUSTOMERS = "GetServiceNumberList";
        String CHECK_UPDATE = "CheckAppUpdated" ;
        String GET_OUTER_SERVICE_LIST = "GetOuterServiceList";
        String RED_TIPS = "isFirstTimeEventOnlyQueryBatch" ;
        String SET_TIPS = "setFirstTimeEventBatch" ;
    }

    /**
     * 网络获取服务号列表接口
     * @param versionName
     * @param callback
     */
    public static void getCustomerList(String versionName, WebApiExecutionCallback<CustomerResult> callback) {
        WebApiUtils.postAsync(controller, Action.GET_CUSTOMERS,
        WebApiParameterList.create().with("M1", versionName),
        callback);
    }

    public static void getOuterServiceList(String versionName, WebApiExecutionCallback<GetOuterServiceListResult> callback) {
        WebApiUtils.postAsync(wechat_outer_service_controller, Action.GET_OUTER_SERVICE_LIST,
        WebApiParameterList.create().with("M1", versionName),
        callback);
    }

    public static void getCustomerCrmInfo(String appId, String wxOpenId, String versionName, WebApiExecutionCallback<CustomerCrmInfoVO> callback) {
        WebApiUtils.postAsync(wechat_outer_service_controller, "getOuterContact",
                WebApiParameterList.create().with("M1", versionName).with("M11", appId).with("M12", wxOpenId),
                callback);
    }

    public static void checkAppUpdate(String versionName, List<CustomerUpdate> modules, WebApiExecutionCallback<CustomerUpdateResult> callback) {
        WebApiUtils.postAsync(controller, Action.CHECK_UPDATE,
        WebApiParameterList.create().with("M1", versionName).with("M2", modules),
        callback);
    }

    public static void isFristRedTips(String versionName, List<String> tags,WebApiExecutionCallback<RedTipsResult> callback) {
        WebApiUtils.postAsync(redTipsController,RED_TIPS,
        WebApiParameterList.create().with("M1", versionName).with("M11", tags),
        callback);
    }

    public static void setFristRedTips(String versionName, List<String> tags,WebApiExecutionCallback<TipResult> callback) {
        WebApiUtils.postAsync(redTipsController,SET_TIPS,
        WebApiParameterList.create().with("M1", versionName).with("M11", tags),
        callback);
    }
}
