/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.contacts_fs.customerservice.util;

import android.app.Activity;
import android.content.Context;
import android.graphics.Rect;

import com.facishare.fs.App;
import com.lidroid.xutils.util.AlgorithmUtils;
import com.lidroid.xutils.util.FSNetUtils;
import com.nostra13.universalimageloader.utils.L;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * Created by 钟光燕 on 2016/3/17.
 * ===================================================
 * <p/>
 * code is m h l
 * <p/>
 * ===================================================
 */
public class CustomerUtil {


    public static boolean isNoNetWork() {
        String network  = FSNetUtils.getInstance().getNetTypeStr();
        return network == null || network.equals("no_speed");
    }
    public static long ONE_DAY = 24 * 60 * 60 * 1000 ;
//    public static long ONE_DAY = 1000 ;
    public static float  MAX_DENSITY = 1.7f;
    public static final String HELPER_MEETING_JUMP_FLAG = "fs://helper/meeting/send";
    public static String generate(String key){
        String ret="";
        try {
            ret= AlgorithmUtils.getMd5(key);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return ret;
    }

    public static String cropImg(String url,int width,int height){
        float ratio = 1.0f;
        float density = App.getFsApplication().getResources().getDisplayMetrics().density ;
        String crop = "";
        if (density > MAX_DENSITY){
            ratio = MAX_DENSITY/density ;
        }
        width = (int) (width * ratio + 0.5f);
        height = (int) (height * ratio + 0.5f);

        if (width != 0 && height != 0) {
            crop =url+ "&width=" + height +  "&height=" + width;
        }

//        URIBuilder uriBuilder = new URIBuilder(url) ;
//        List<NameValuePair> list = uriBuilder.getQueryParams() ;
//        for (NameValuePair pair : list){
//        }

        return crop;
    }


    public static int getStatusHeight(Context context){
        int statusHeight;
        Rect localRect = new Rect();
        ((Activity) context).getWindow().getDecorView().getWindowVisibleDisplayFrame(localRect);
        statusHeight = localRect.top;
        if (0 == statusHeight){
            Class<?> localClass;
            try {
                localClass = Class.forName("com.android.internal.R$dimen");
                Object localObject = localClass.newInstance();
                int i5 = Integer.parseInt(localClass.getField("status_bar_height").get(localObject).toString());
                statusHeight = context.getResources().getDimensionPixelSize(i5);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return statusHeight;
    }

    public static String getVersionName(){
        return App.versionName;
//        if (!version.contains(".")){
//            return version ;
//        }else {
//            return version.substring(0, version.indexOf(".", version.indexOf(".") + 1));
//        }
    }

}
