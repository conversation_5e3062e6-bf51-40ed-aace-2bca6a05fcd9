package com.facishare.fs.common_datactrl.draft.draft_fw;

import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CrmForm;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.OutDoorAttachUploader;
import com.facishare.fs.js.utils.OutdoorLog;
import com.facishare.fs.pluginapi.common_beans.Attach;
import com.fs.beans.beans.EnumDef;
import com.fxiaoke.fscommon.files.FileUtil;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fxlog.FCLog;

import java.io.File;
import java.io.FilenameFilter;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

/**
 * Created by zhujg on 2019/7/19.
 */

public class BaseAttach implements IAttach {
    private static final long serialVersionUID = 1673117381822854917L;
    public int retryCount = -1;//重新上传的次数
    public int saveFileCount = 0; //调用saveFiles接口的次数
    public LinkedList<Attach> upLoadFiles = new LinkedList<Attach>();
    protected String tag = "Feed.WQ";
    public int waterMark = 1;//打水印 0-客户端未打水印（兼容老接口）， 1-客户端已经打水印
    public String photoPath;
    public transient OutDoorAttachUploader uploader;
    public HashMap<String, String> mImageToMd5Map;
    public CrmForm crmForm;
    public int errorCode;
    public String errorMsg;

    @Override
    public LinkedList<Attach> getUpLoadFiles() {
        if(upLoadFiles==null){
            upLoadFiles = new LinkedList();
        }
        return upLoadFiles;
    }

    @Override
    public void updateAttachToService(String checkinId, List<Attach> attachList, WebApiExecutionCallback callback) {

    }

    @Override
    public void addRetryCount() {
        this.retryCount++;
    }

    @Override
    public int getRetryCount() {
        return retryCount;
    }

    @Override
    public String getTag() {
        return tag;
    }

    @Override
    public int getSaveCount() {
        return this.saveFileCount;
    }

    @Override
    public void addSaveCount() {
        this.saveFileCount++;
    }

    /**
     * 打点统计信息
     * saveFiles无论失败还是成功,都需要把上传的照片信息保存到埋点信息当中
     * @return
     */
    @Override
    public String createExData(){
        if(this.upLoadFiles != null){
            int imageCount = getUpLoadFiles().size();
            //统计所有照片的总共上传次数，在中间有失败重试的情况下与imageCount不一致
            int imageSendCount = 0;
            for (Attach attach : getUpLoadFiles()) {
                imageSendCount += attach.uploadCount;
            }
            return "{\"retryCount\":" + getRetryCount() + ",\"imageCount\":" + imageCount
                    + ",\"imageSendCount\":" + imageSendCount + ",\"saveFileCount\":"
                    + getSaveCount() + "}";
        }
        return "";
    }

    @Override
    public void setUploader(OutDoorAttachUploader uploader) {
        this.uploader = uploader;
    }

    @Override
    public OutDoorAttachUploader getUploader() {
        return uploader;
    }


    @Override
    public int getErrorCode() {
        return errorCode;
    }

    @Override
    public String getErrorMsg() {
        return errorMsg;
    }

    public int getWaterMark(){
        return this.waterMark;
    }

    public void addUpLoadImageFile(Attach upLoadFile) {
        if (upLoadFiles == null) {
            upLoadFiles = new LinkedList<Attach>();
        }
        if (upLoadFile != null) {
//            upLoadFile.tag=tag;
            upLoadFile.mIsSendByUnzipped = (waterMark==1);
            upLoadFiles.add(upLoadFile);
        }
    }

    public void removeImageFile() {
        if (upLoadFiles == null) {
            upLoadFiles = new LinkedList<Attach>();
        }
        if (upLoadFiles != null && !upLoadFiles.isEmpty()) {
            Iterator<Attach> iterator = upLoadFiles.iterator();
            for (; iterator.hasNext();) {
                Attach file = iterator.next();
                if (file.getFileType() == EnumDef.FeedAttachmentType.ImageFile.value) {
                    iterator.remove();
                }
            }
        }
    }

    public void putImageMd5(String localPath) {
        String md5 = FileUtil.jpgDigest(localPath, "md5");
        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,"照片路径:"+localPath+",MD5:"+md5);
        putImageMd5(localPath, md5);
    }

    public void putImageMd5(String localPath, String md5) {
        if (mImageToMd5Map == null) {
            mImageToMd5Map = new HashMap<>();
        }
        mImageToMd5Map.put(localPath, md5);
    }

    public void putImageMD5(HashMap<String, String> md5Map){
        if(md5Map==null){
            return;
        }
        if (mImageToMd5Map == null) {
            mImageToMd5Map = new HashMap<>();
        }
        mImageToMd5Map.putAll(md5Map);
    }

    /**
     *  获取客户手机相册中通过纷享拍照的照片的路径名称
     */
    public void printDCIMList(File file){
        try{
            File dir = file.getParentFile();
            if(dir.isDirectory()){
                File [] fs = dir.listFiles(new FilenameFilter(){
                    @Override
                    public boolean accept(File dir, String filename) {
                        if(filename!=null && filename.matches("\\d+\\.jpg")){
                            return true;
                        }
                        return false;
                    }
                });
                if(fs!=null){
                    for(File f : fs){
                        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT, "printDCIMList() path:"+f.getPath()+",canRead:"+f.canRead()+","+f.length());
                    }
                    FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT, "printDCIMList() 照片总数:"+fs.length);
                }
            }
        }catch(Exception e){
            FCLog.e(OutdoorLog.OUTDOOR_DEBUG_EVENT, e.getMessage());
        }
    }

    public boolean checkImageMd5(String imgPath) {
        if (mImageToMd5Map == null) {
            return true;
        }
        String md5 = mImageToMd5Map.get(imgPath);
        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,"checkImageMd5 imgPath:" + imgPath + ":md5:" + md5);
        if (md5 == null) {
            FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,"checkImageMd5() md5 = null return true");
            return true;
        }
        File file = new File(imgPath);
        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,"checkImageMd5() 文件是否存在["+file.exists()+"]"+imgPath);
        String fileMd5 = FileUtil.jpgDigest(imgPath, "md5");
        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,"checkImageMd5 jpgDigest fileMd5:" + fileMd5);
        if(fileMd5 == null){
            return true;
        }
        return md5.equalsIgnoreCase(fileMd5);
    }

    public boolean checkAllImageMd5() {
        if (upLoadFiles == null) {
            return true;
        }
        FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,"checkAllImageMd5 打印系统相册照片列表");
        boolean isPrinted = false;
        for (Attach attach : upLoadFiles) {
            if (attach.getFileType() == EnumDef.FeedAttachmentType.ImageFile.value
                    && attach.attachLocalState != Attach.AttachType.ATTACH_NETWORK_TYPE) {
                if(!isPrinted){
                    printDCIMList(new File(attach.originalPath));
                    isPrinted = true;
                }
                if (!checkImageMd5(attach.originalPath)) {
                    return false;
                }
            }
        }
        return true;
    }
}
