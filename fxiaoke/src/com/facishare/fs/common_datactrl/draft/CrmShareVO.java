package com.facishare.fs.common_datactrl.draft;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

import com.alibaba.fastjson.TypeReference;
import com.facishare.fs.biz_feed.subbiz_send.BaseFsSendActivity;
import com.facishare.fs.biz_feed.subbiz_send.XCRMSendShareActivity;
import com.facishare.fs.biz_feed.subbiz_send.feedsendapi.FeedSendTask;
import com.facishare.fs.biz_feed.subbiz_send.feedsendapi.IFeedSendTask;
import com.facishare.fs.web_business_utils.api.CrmFeedService;
import com.fs.beans.beans.EnumDef;
import com.fxiaoke.fshttp.web.ParamValue3;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fxlog.FCLog;

import android.content.Context;
import android.content.Intent;

public class CrmShareVO extends ShareVO{

	/**
	 * 
	 */
	private static final long serialVersionUID = 7115070772568002794L;
	
	public CrmShareVO() {
		super();
		tag="CrmShareVO";
	}
	
	public CrmShareVO(ShareVO shareVO) {
		super();
		this.content = shareVO.content;
		this.draftID = shareVO.draftID;
		this.draftType = shareVO.draftType;
		this.latestOpDate = shareVO.latestOpDate;
		this.draftState = shareVO.draftState;
		this.errorStr = shareVO.errorStr;
		tag="CrmShareVO";
	}

	/**
	 * 数据ID
	 */
	public String dataID;
	public HashMap<String, String> dataIDMap;
	
	/**
	 * 信息源与业务关系类型
	 */
	public int fbrType;	
	public HashMap<Integer, String> fbrTypeMap;


	public void setDataID(String dataID) {
		this.dataID = dataID;
		if (this.dataIDMap == null) {
			this.dataIDMap = new HashMap<String, String>(0);
		}
		dataIDMap.put(dataID, null);
	}


	public void setFbrType(int fbrType) {
		this.fbrType = fbrType;
		if (this.fbrTypeMap == null) {
			this.fbrTypeMap = new HashMap<Integer, String>(0);
		}
		fbrTypeMap.put(fbrType, null);
	}
	
//	@Override 
//	protected void setRange(Range range) {
//		super.setRange(range);
//		switch (range.rangeType) {
//		case RangeType.RANGE_CRM_FBR_TYPE:
//			setFbrTypeMap(range.rangeMap);
//			break;
//		case RangeType.RANGE_CRM_DATA_ID:
//			setDataIDMap(range.rangeMap);
//			break;
//		}
//	}
	public void setDataIDMap(HashMap<String, String> dataID2Map) {
		this.dataIDMap = dataID2Map;
		this.dataID = mapToString(dataID2Map);
	}
	public void setFbrTypeMap(HashMap<Integer, String> fbrTypeMap) {
		this.fbrTypeMap = fbrTypeMap;
		this.fbrType = mapToInt(fbrTypeMap);
	}
	@Override 
	public boolean validate() {
		if (content == null || content.length() == 0) {
			//附件,录音,图片其一
			if (!(containsFileType(EnumDef.FeedAttachmentType.ImageFile.value) 
					|| containsFileType(EnumDef.FeedAttachmentType.AudioFile.value) 
					|| containsFileType(EnumDef.FeedAttachmentType.AttachFile.value))) {
				return false;
			}
		}
		return true;
	}
	
    @Override
    public void clickDraft(Context context) {
        Intent intent = new Intent();
        intent.putExtra(BaseFsSendActivity.VO_KEY, this);
        intent.setClass(context, XCRMSendShareActivity.class);
        context.startActivity(intent);
    }
	
    @Override
    public void sendDraft(IFeedSendTask task, List<ParamValue3<Integer, String, Integer, String>> response) {
        CrmFeedService.SendShare(this.getContent(), this.fbrType, this.dataID, response,newCRMShareCallback(task));
    }
    
    private WebApiExecutionCallback<Integer> newCRMShareCallback(final IFeedSendTask task) {
        WebApiExecutionCallback<Integer> callback = new  WebApiExecutionCallback<Integer>() {
            
            @Override 
            public TypeReference<WebApiResponse<Integer>> getTypeReference() {
                return new TypeReference<WebApiResponse<Integer>>() {
                };
            }
            
            @Override 
            public void failed(WebApiFailureType failureType, int httpStatusCode,
                    String error) {
                FCLog.e(error);
                task.sendFailed(failureType,httpStatusCode,error);
            }
            
            @Override 
            public void completed(Date time, Integer response) {
                task.sendSuccess(time);
            }
        };
        
        return callback;
    }
    
}
