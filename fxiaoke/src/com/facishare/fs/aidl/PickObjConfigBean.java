package com.facishare.fs.aidl;

import android.os.Parcel;
import android.os.Parcelable;

import com.facishare.fs.metadata.list.select_obj.picker.PickObjConfig;

public class PickObjConfigBean implements Parcelable {
    private PickObjConfig pickObjConfig;

    public PickObjConfig getPickObjConfig() {
        return pickObjConfig;
    }

    public void setPickObjConfig(PickObjConfig config) {
        this.pickObjConfig = config;
    }

    public PickObjConfigBean(PickObjConfig config){
        pickObjConfig=config;
    }

    public PickObjConfigBean(Parcel in){
        pickObjConfig=(PickObjConfig)in.readSerializable();
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel out, int flags) {
        out.writeSerializable(pickObjConfig);
    }

    public static final Creator<PickObjConfigBean> CREATOR=new Creator<PickObjConfigBean>() {
        @Override
        public PickObjConfigBean createFromParcel(Parcel in) {
            return new PickObjConfigBean(in);
        }

        @Override
        public PickObjConfigBean[] newArray(int size) {
            return new PickObjConfigBean[size];
        }
    };
}
