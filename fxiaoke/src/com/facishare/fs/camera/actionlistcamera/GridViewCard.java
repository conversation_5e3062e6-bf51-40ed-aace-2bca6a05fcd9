package com.facishare.fs.camera.actionlistcamera;

import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.content.Context;
import android.view.ViewTreeObserver;
import android.widget.AdapterView;
import android.widget.GridView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.facishare.fs.App;
import com.facishare.fs.biz_feed.subbiz_send.datactrl.SendBaseGridViewCtrl;
import com.facishare.fs.biz_feed.subbiz_send.datactrl.SendBaseUtils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.CustomerAction;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.bean.DescribeFields;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.OutDoor2CacheManger;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.OutdoorRuleTools;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorcalendv4arrelated.itemview.AbsView;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.ICustomerGeoUpdate;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.OutDoorV2Utils;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.PhotoFragment;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.outdoorv2ActionList.LongOnClickDialog;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.outdoorv2.photo.PhotoUtils;
import com.facishare.fs.camera.FsCameraParam;
import com.facishare.fs.camera.utils.ImageUtil;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.js.utils.OutdoorLog;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.IPicService;
import com.facishare.fs.pluginapi.PicPreviewArg;
import com.facishare.fs.pluginapi.pic.bean.ImgData;
import com.facishare.fs.pluginapi.video.beans.ShortVideoPreviewConfig;
import com.facishare.fs.utils_fs.OutDoorHomeStyleSetUtils;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fs.web_business_utils.api.FileService;
import com.facishare.fslib.R;
import com.fxiaoke.fscommon.sandbox.SandboxContextManager;
import com.fxiaoke.fshttp.web.http.WebApiDownloadFileCallback;
import com.fxiaoke.fshttp.web.sandbox.ISandboxContext;
import com.fxiaoke.fxlog.FCLog;

import java.io.File;
import java.util.ArrayList;

/**
 * Created by sunhq on 2019/6/19.
 */

public class GridViewCard extends AbsView {

    public   Context mContext;
    private  View view;
    private GridView mImageGridview;
    SendBaseGridViewCtrl mSendGridViewCtrl;
    private TextView lable;
    PhotoFragment.ConfigData configData;
    protected int itemSpacing =30;
    public static final int columCount = 4;
    protected CustomerAction mCustomerAction;
    private DescribeFields describeFields;
    private  ArrayList<ImgData> mImgDataList = new ArrayList<>();
    private ArrayList<ImgData> mApiImgDataList = new ArrayList<>();

    LongOnClickDialog longOnClickDialog;

    int onGlobalLayoutTimes = 0;

    public GridViewCard(Context mContext, ArrayList<ImgData> mImgDataList, PhotoFragment.ConfigData configData, DescribeFields describeFields,CustomerAction mCustomerAction) {
        this.mContext = mContext;
        this.mImgDataList = mImgDataList;
        this.configData = configData;
        this.describeFields = describeFields;
        this.mCustomerAction = mCustomerAction;
        view  = LayoutInflater.from(mContext).inflate(R.layout.img_gridview_layout,null);
        findView(view);
        renderView();
    }

    private void renderView() {
        if(describeFields != null){
           String lableString = describeFields.label;
           //TextUtils.equals("???", I18NHelper.getText("CheckinsObj.field."+describeFields.api_name+".label"))?describeFields.label:I18NHelper.getText("CheckinsObj.field."+describeFields.api_name+".label");
            lable.setText(describeFields.required?"*"+lableString:lableString);
            for(ImgData imgdata:mImgDataList){
                FsCameraParam.CameraField imgDescr = null;
                if(imgdata.mObject!= null && imgdata.mObject instanceof FsCameraParam.CameraField){
                    imgDescr = (FsCameraParam.CameraField) imgdata.mObject;
                }
                if(imgDescr != null && TextUtils.equals(imgDescr.api_name,describeFields.api_name)){
                    mApiImgDataList.add(imgdata);
                }
            }
        }else {
            mApiImgDataList.addAll(mImgDataList);
        }

        mSendGridViewCtrl = new SendBaseGridViewCtrl(mContext,mImageGridview,mApiImgDataList,itemSpacing);
        mSendGridViewCtrl.setAlwaysShowAddImage(true);
        if(OutDoorHomeStyleSetUtils.getRuleHomeStyleResultBykey(OutDoorHomeStyleSetUtils.kkIsAICheckCopyPhoto_key) == 1){
            mSendGridViewCtrl.setShowAnil(true);
        }

        if(describeFields !=null ){
            mSendGridViewCtrl.setPhotoMax(describeFields.file_amount_limit);
        }else{
            mSendGridViewCtrl.setPhotoMax(mCustomerAction.maxDataNumber);
        }

        itemSpacing = ImageUtil.dp2px(mContext, 10);

        if(configData!=null &&configData.isComplete && mCustomerAction.isFreeAction == 0 ){
            mSendGridViewCtrl.setAllowShowAddImage(false);
        }
        if("face_recognition".equals(mCustomerAction.actionCode)){
            mSendGridViewCtrl.setAllowShowAddImage(false);
        }
        mImageGridview.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            public void onGlobalLayout() {
                if(onGlobalLayoutTimes>1){
                    return;
                }
                int widthfill = App.intScreenWidth;

                int width = /*mImageGridview.getWidth()*/widthfill - itemSpacing * 5;
                final int columnWidth = (width / columCount);
                //                if (mImageGridAdapter.getCount() > 0) {
                //                    mImageGridAdapter.setItemHeight(columnWidth);
                //                }

                int height = ((mSendGridViewCtrl.getAdapter().getCount() + columCount - 1) / columCount) * (columnWidth + itemSpacing) + itemSpacing;
//                FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,"javascript:height = "+ height);
//                FCLog.i(OutdoorLog.OUTDOOR_DEBUG_EVENT,"javascript:mImageGridview.getHeight() = "+ mImageGridview.getHeight());
                if (mImageGridview.getHeight() != height) {
                    LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.FILL_PARENT, height);
                    mImageGridview.setLayoutParams(lp);
                }
                onGlobalLayoutTimes++;
            }
        });

        mImageGridview.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if (position == mApiImgDataList.size()) {
//					if (OutDoorV2Constants.isVideo) {
//						openCamera(2);
//					}else {
                    openCamera(mCustomerAction.imageType);
//					}
                } else {
                    ImgData imgData = mApiImgDataList.get(position);
                    if (imgData != null && (!TextUtils.isEmpty(imgData.videoPath)||!TextUtils.isEmpty(imgData.videoServicePath))) {//点击可预览小视频
                        String fpath = OutDoor2CacheManger.getCheckTypeCacheDir()+imgData.localImgName;
                        OutDoorV2Utils.DownloadUPeaFile(mContext, fpath, imgData.videoServicePath, new ICustomerGeoUpdate() {
                            @Override
                            public void onResult(int result) {
                                if (result==1){
                                    imgData.videoPath = fpath;
                                }

                                ShortVideoPreviewConfig config = ShortVideoPreviewConfig.createFcpBeanPreviewConfig(imgData.videoServicePath, imgData.videoPath, imgData.path);
                                config.setCanDelete(!configData.isComplete|| mCustomerAction.isFreeAction == 1);
                                config.setRequestCode(SendBaseUtils.REQUEST_VIEDOCODE);
                                HostInterfaceManager.getVideoService().gotoPlayShortVideo(mContext, config);
                            }
                        });

                    }else {
                        int select = getImgInListPosition(imgData);
                        IPicService picService = HostInterfaceManager.getIPicService();
                        if (picService != null) {
                            picService.go2ViewForResult(mContext, new PicPreviewArg.Builder().setImgs(mImgDataList).
                                            setCurrentIndex(select).setShowDelBtn(!configData.isComplete|| mCustomerAction.isFreeAction == 1).setShowSkipToGroupLookBtn(true).setAutoFix(true)
                                            .setCanShare(OutdoorRuleTools.isCanShare()).build(),
                                    SendBaseUtils.REQUESTCODE_PREVIEW_PHOTO);
                        }
                    }


                }
            }
        });
    }

    private int getImgInListPosition(ImgData apiImgData){

        if(mImgDataList!= null){
            for(int i = 0;i<mImgDataList.size();i++){
                if(mImgDataList.get(i).middleImgName.equals(apiImgData.middleImgName)){
                    return i;
                }
            }
        }
        return 0;
    }
    @Override
    public View getView() {
        return view;
    }

    @Override
    public void findView(View view) {
        mImageGridview = view.findViewById(R.id.insert_pic_gridview);
        lable = view.findViewById(R.id.lable_api);
    }

    @Override
    public void updateData(Object o) {
        mImgDataList = (ArrayList<ImgData>) o;
        mSendGridViewCtrl.getAdapter().setListData(mImgDataList);
    }

    public void openCamera(int type){
        PhotoUtils.openCameraByPhotoActvity(mCustomerAction, ((Activity)mContext),type,describeFields);
    }
}
