package com.facishare.fs.camera.jointCamera;

import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.util.Base64;
import android.util.Log;


import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;

public class FileUtil {

    public static void copyFileUsingFileStreams(File source, File dest)
            throws IOException {
        InputStream input = null;
        OutputStream output = null;
        try {
            input = new FileInputStream(source);
            output = new FileOutputStream(dest);
            byte[] buf = new byte[1024];
            int bytesRead;
            while ((bytesRead = input.read(buf)) != -1) {
                output.write(buf, 0, bytesRead);
            }
        } finally {
            if (input != null) {
                input.close();
            }
            if (output != null) {
                output.close();
            }
        }
    }

    public static boolean isFileExist(String filename) {
        File file = new File(filename);
        return file.exists();
    }

    public static void mkdirAndClear(String dir) {
        makeDir(dir);
        deleteContent(dir);
    }

    /**
     * 重命名文件夹并在后台删除
     */
    public static void deleteDirAsync(String dir) {
        File oldDirPath = new File(dir);
        final File newDirPath = new File(dir + "_del");
        boolean res = oldDirPath.renameTo(newDirPath);
        if (res) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    deleteFile(newDirPath);
                    Log.i("FileUtil", "Dir deleted asynchronously: " + newDirPath.getAbsolutePath());
                }
            }).start();
        } else {
            deleteDir(dir);
            Log.i("FileUtil", "Dir deleted synchronously: " + dir);
        }
    }

    public static void deleteDir(String dir) {
        deleteFile(new File(dir));
    }

    public static void deleteContent(String dir) {
        File file = new File(dir);
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            for (File f : files) {
                deleteFile(f);
            }
        }
    }

    public static String makeDir(String dir) {
        File file = new File(dir);
        if (!file.exists()) {
            if (!file.mkdirs()) {
                throw new RuntimeException("file dir create failed:" + dir);
            }
        }
        return dir;
    }

    public static void writeFileContent(String filename, String content) throws IOException {
        FileWriter writer = new FileWriter(filename);
        writer.write(content);
        writer.close();
    }

    public static String readAssetFileUtf8String(AssetManager assetManager, String filename) throws IOException {
        byte[] bytes = readAssetFileContent(assetManager, filename);
        return new String(bytes, Charset.forName("UTF-8"));
    }

    public static byte[] readAssetFileContent(AssetManager assetManager, String filename) throws IOException {
        Log.i("FileUtil", " try to read asset file :" + filename);
        InputStream is = assetManager.open(filename);
        int size = is.available();
        byte[] buffer = new byte[size];
        int realSize = is.read(buffer);
        if (realSize != size) {
            throw new IOException("realSize is not equal to size: " + realSize + " : " + size);
        }
        is.close();
        return buffer;
    }

    private static void deleteFile(File file) {
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            for (int i = 0; i < files.length; i++) {
                File f = files[i];
                deleteFile(f);
            }
            file.delete(); // 如要保留文件夹，只删除文件，请注释这行
        } else if (file.exists()) {
            file.delete();
        }
    }

    public static boolean deleteFile(String filepath) {
        boolean result = false;
        File file = new File(filepath);
        if (file.exists()) {
            result = file.delete();
        }
        return result;
    }

    public static boolean createFile(String filepath) throws IOException {
        boolean result = false;
        File file = new File(filepath);
        if (!file.exists()) {
            result = file.createNewFile();
        }
        return result;
    }

    public static String readJsonFile(String filepath) throws IOException {
        FileInputStream fis = new FileInputStream(filepath);
        int size = fis.available();
        byte[] buffer = new byte[size];
        int realSize = fis.read(buffer);
        fis.close();
        if (realSize != size) {
            throw new IOException("readJsonFile realSize is not equal to size: " + realSize + " : " + size);
        }
        return new String(buffer);
    }

    /**
     * @param dirPath 文件夹路径
     * @param suffix  后缀，不带'.'
     * @return 返回列表包含文件名（不含路径）
     */
    public static List<String> listFilenameWithSuffix(String dirPath, String suffix) {
        List<String> filenames = new ArrayList<>();
        File dir = new File(dirPath);
        if (dir.exists()) {
            String[] files = dir.list();
            suffix = '.' + suffix;
            for (String filename : files) {
                if (filename.endsWith(suffix)) {
                    filenames.add(filename);
                }
            }
        }
        return filenames;
    }

    /**
     * 读取图片以Base64返回
     * @param filepath 图片路径
     */
    public static String readImageToBase64(String filepath) throws IOException {
        InputStream is = null;
        try {
            is = new FileInputStream(filepath);
        } catch (FileNotFoundException e) {
            throw new IOException("Read image failed: " + filepath, e);
        }
        byte[] content = Util.inputStreamToByte(is);
        String contentBase64 = Base64.encodeToString(content, Base64.DEFAULT);
        is.close();
        return contentBase64;
    }

    public static String bitmapToBase64(Bitmap bitmap) {
        String result = null;
        ByteArrayOutputStream baos = null;
        try {
            if (bitmap != null) {
                baos = new ByteArrayOutputStream();
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, baos);
                baos.flush();
                baos.close();
                byte[] bitmapBytes = baos.toByteArray();
                result = Base64.encodeToString(bitmapBytes, Base64.DEFAULT);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (baos != null) {
                    baos.flush();
                    baos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    public static String checkSum(String imageBase64, String checkSumAlgo) throws NoSuchAlgorithmException {
        // 1.Files.readAllBytes(file.toPath())
        // 2.Base64.decode(imageBase64,Base64.DEFAULT)
        MessageDigest messageDigest = null;
        try {
            messageDigest = MessageDigest.getInstance(checkSumAlgo);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            throw new NoSuchAlgorithmException("checkSum image failed: ", e);
        }
        messageDigest.update(Base64.decode(imageBase64, Base64.DEFAULT));
        byte[] digestBytes = messageDigest.digest();
        StringBuffer sb = new StringBuffer();
        for (byte b : digestBytes) {
            sb.append(Integer.toString((b & 0xff) + 0x100, 16).substring(1));
        }
        return sb.toString();
    }

}
