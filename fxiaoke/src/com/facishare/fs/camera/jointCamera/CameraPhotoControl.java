package com.facishare.fs.camera.jointCamera;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.ImageFormat;
import android.graphics.SurfaceTexture;
import android.hardware.camera2.CameraAccessException;
import android.hardware.camera2.CameraCaptureSession;
import android.hardware.camera2.CameraCharacteristics;
import android.hardware.camera2.CameraDevice;
import android.hardware.camera2.CameraManager;
import android.hardware.camera2.CameraMetadata;
import android.hardware.camera2.CaptureRequest;
import android.hardware.camera2.params.StreamConfigurationMap;
import android.media.ImageReader;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.util.Log;
import android.util.Size;
import android.view.Surface;
import android.view.TextureView;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;

import java.util.Arrays;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicBoolean;

@RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
class CameraPhotoControl implements IHandlerConst {

    private static final String TAG = "CameraPhotoControl";
    private static final String CAMERA_FRONT_ID = "1";
    private static final String CAMERA_BACK_ID = "0";
    /**
     * 记录camera的状态
     */
    protected static final int STATUS_INIT = 0;
    protected static final int STATUS_SURFACE_READY = 1;
    protected static final int STATUS_SURFACE_OPENED = 2;
    protected static final int STATUS_SURFACE_RELEASED = 5;

    protected Handler mBackgroundHandler;
    protected HandlerThread mBackgroundThread;

    protected Handler uiHandler;
    /**
     * @var 预览照相机的View
     */
    protected TextureView mTextureView;

    protected int surfaceWidth;
    protected int surfaceHeight;

    protected volatile int status = STATUS_INIT;
    protected volatile String mCameraId;
    protected volatile CameraDevice camera;

    protected AtomicBoolean isSetup = new AtomicBoolean(false);
    protected volatile Size previewSize;

    protected volatile int sensorOrientation;
    protected ImageReader mImageReader;
    protected PhotoImageAvailableListener imageAvailableListener;

    protected volatile CameraCaptureSession mPreviewSession;
    protected CaptureRequest mPreviewRequest;      // 预览请求,
    protected volatile CameraPhotoSensor sensor;
    protected volatile CaptureRequest.Builder captureBuilder;

    public CameraPhotoControl(@NonNull final TextureView mTextureView, @NonNull Handler handler,
                              final PhotoSaveOption photoSaveOption) {
        startBackgroundThread();
        uiHandler = handler;
        this.mTextureView = mTextureView;
        Log.i(TAG, "CameraPhotoControl construct");
        mBackgroundHandler.post(new Runnable() {
            @Override
            public void run() {
                Log.i(TAG, "mBackgroundHandler");
                FileUtil.makeDir(photoSaveOption.getCurrentPath());
                imageAvailableListener = new PhotoImageAvailableListener(
                        mBackgroundHandler, uiHandler, photoSaveOption, mTextureView.getContext());
                Log.i(TAG, "Camera init Step 2 Prepare OnImageAvailableListener Listener");
                setSurfaceTextureLister();
                if (mTextureView.isAvailable()) {
                    Log.i(TAG, "Camera init Step 4.1 TextureSurface is already avaiable");
                    surfaceWidth = mTextureView.getWidth();
                    surfaceHeight = mTextureView.getHeight();
                    status = STATUS_SURFACE_READY;
                    setup();
                } else {
                    Log.i(TAG, "Camera init Step 4.2 mTextureView is not available, wait to be avilable");
                }
            }
        });
    }

    /**
     * 供复写
     */
    public CameraPhotoControl() {}

    public boolean turnTorchAndStart(boolean isToTurnOn) {
        if (captureBuilder == null) {
            return false;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            captureBuilder.set(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON);
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            captureBuilder.set(CaptureRequest.FLASH_MODE, isToTurnOn ?
                    CameraMetadata.FLASH_MODE_TORCH : CameraMetadata.FLASH_MODE_OFF);
        }
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                mPreviewSession.setRepeatingRequest(captureBuilder.build(),
                        null, mBackgroundHandler);
            }
        } catch (CameraAccessException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    // 镜头切换开关,默认后置镜头;
    public boolean switchCameraLens() {
        if (mCameraId.equals(CAMERA_BACK_ID)) {
            mCameraId = CAMERA_FRONT_ID;
            CameraPhotoControl.this.camera.close();
            reopenCamera();
            return true;
        } else if (mCameraId.equals(CAMERA_FRONT_ID)) {
            mCameraId = CAMERA_BACK_ID;
            CameraPhotoControl.this.camera.close();
            reopenCamera();
            return false;
        }
        return false;
    }

    public void reopenCamera() {
        if (mTextureView.isAvailable()) {
            openCamera();
        } else {
            setSurfaceTextureLister();
        }
    }

    public void takePhoto() {
        imageAvailableListener.takePhoto();
    }

    protected void setup() {
        if (status == STATUS_SURFACE_READY && !isSetup.getAndSet(true)) {
            Log.i(TAG, "Camera init Step 5 setup begin");
            if (getCameraAndSize()) {
                Log.i(TAG, "Camera init Step 5.2 setup begin");
                if (previewSize == null) {
                    Log.e(TAG, "previewSize is null");
                    return;
                }

                Log.i(TAG, "previewSize choosen:" + previewSize.getHeight() + "*"
                        + previewSize.getWidth() + ";surface size:" + surfaceWidth + "*" + surfaceHeight);
                PhotoCropSize cropSize = new PhotoCropSize(new Size(previewSize.getHeight(), previewSize.getWidth()),
                        new Size(surfaceWidth, surfaceHeight), sensorOrientation);

                imageAvailableListener.setCropSize(cropSize);

                Message msg = uiHandler.obtainMessage();
                msg.what = MESSAGE_DECIDE_PREVIEW_SIZE;
                msg.obj = cropSize;
                uiHandler.sendMessage(msg);
                initSensors();
                openCamera();

            }
        }
    }

    protected void initSensors() {
        sensor = new CameraPhotoSensor(mTextureView.getContext(), new PhotoSensorAdapter() {
            @Override
            public void onOrientationSensorResult(boolean isSuccess) {
                imageAvailableListener.setOrientationSensorCorrect(isSuccess);
            }

            @Override
            public void onLightSensorResult(int status) {
                imageAvailableListener.setLightSensorStatus(status);
            }
        });
        sensor.start();
    }

    protected void openCamera() {
        CameraManager manager = (CameraManager) mTextureView.getContext().getSystemService(Context.CAMERA_SERVICE);
        // 检查权限
        try {
            if (ActivityCompat.checkSelfPermission(mTextureView.getContext(),
                    Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
                throw new RuntimeException("Camera Permission check failed");
            }
            CameraDevice.StateCallback stateCallback = new CameraDevice.StateCallback() {
                @Override
                public void onOpened(CameraDevice camera) {
                    Log.i(TAG, "Camera init Step 6.1 opencamera begin");
                    CameraPhotoControl.this.camera = camera;
                    status = STATUS_SURFACE_OPENED;
                    startPreview();
                }

                @Override
                public void onDisconnected(@NonNull CameraDevice camera) {
                    Log.i(TAG, "Camera onDisconnected");
                }

                @Override
                public void onError(@NonNull CameraDevice camera, int error) {
                    Log.i(TAG, "Camera onError : " + error);
                }
            };
            Log.i(TAG, "Camera init Step 6 opencamera begin");
            manager.openCamera(mCameraId, stateCallback, mBackgroundHandler);

        } catch (CameraAccessException e) {
            Log.e(TAG, "OpenCamera Error", e);
        }
    }

    protected void startPreview() {
        Log.i(TAG, "Camera init Step 7 startPreview and take photo");
        mImageReader = ImageReader.newInstance(previewSize.getWidth(), previewSize.getHeight(),
                ImageFormat.YUV_420_888, /*maxImages*/5);
        mImageReader.setOnImageAvailableListener(   // 设置监听和后台线程处理器
                imageAvailableListener, mBackgroundHandler);

        SurfaceTexture texture = mTextureView.getSurfaceTexture();
        assert texture != null;
        texture.setDefaultBufferSize(previewSize.getWidth(), previewSize.getHeight());  // 设置宽度和高度
        Surface surface = new Surface(texture);  // 用获取输出surface
        try {
            final CaptureRequest.Builder mPreviewRequestBuilder
                    = camera.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW);
            mPreviewRequestBuilder.addTarget(surface);  // 请求捕获的目标surface
            mPreviewRequestBuilder.addTarget(mImageReader.getSurface());
            Log.i(TAG, "Camera init Step 7.1 startPreview createCaptureSession 2 surfaces, wait onConfigured");
            camera.createCaptureSession(Arrays.asList(surface, mImageReader.getSurface()),
                    new CameraCaptureSession.StateCallback() {
                        // 一个会话的创建需要比较长的时间，当创建成功后就会执行onConfigured回调
                        @Override
                        public void onConfigured(@NonNull CameraCaptureSession cameraCaptureSession) {
                            Log.i(TAG, "Camera init Step 7.2 createCaptureSession onConfigured");
                            // 相机关闭时, 直接返回
                            if (null == camera) {
                                return;
                            }

                            // 会话可行时, 将构建的会话赋给mCaptureSession
                            mPreviewSession = cameraCaptureSession;
                            try {
                                // 自动对焦
                                // CONTROL_AF_MODE_CONTINUOUS_PICTURE
                                // 在该模式中，AF算法连续地修改镜头位置以尝试提供恒定对焦的图像流。
                                // 聚焦行为应适合静止图像采集; 通常这意味着尽可能快地聚焦。
                                mPreviewRequestBuilder.set(CaptureRequest.CONTROL_AF_MODE,
                                        CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE);
                                // 自动闪光：与ON一样，除了相机设备还控制相机的闪光灯组件，在低光照条件下启动它

                                // mPreviewRequestBuilder.set(CaptureRequest.CONTROL_AE_MODE,
                                //     CaptureRequest.CONTROL_AE_MODE_ON_AUTO_FLASH);
                                // 设置预览帧率为最高，似乎fpsRanges[fpsRanges.length-1]
                                // 一般就是手机相机能支持的最大帧率，一般也就是[30,30]
                                // 至少在mi 8和华为p30 pro上是这样
                                // mPreviewRequestBuilder.set(
                                //      CaptureRequest.CONTROL_AE_TARGET_FPS_RANGE,fpsRanges[fpsRanges.length-1]);

                                // 构建上述的请求
                                // (CaptureRequest mPreviewRequest是请求捕获参数的集合，包括连续捕获的频率等)
                                mPreviewRequest = mPreviewRequestBuilder.build();
                                // 重复进行上面构建的请求, 以便显示预览
                                mPreviewSession.setRepeatingRequest(mPreviewRequest,
                                        null, mBackgroundHandler);
                                uiHandler.sendEmptyMessage(MESSAGE_PREVIEW_PREPARED);
                                captureBuilder = mPreviewRequestBuilder;
                            } catch (CameraAccessException e) {
                                e.printStackTrace();
                            }
                            Log.i(TAG, "Camera init Step 7.3 createCaptureSession onConfigured finished");
                        }

                        @Override
                        public void onConfigureFailed(
                                @NonNull CameraCaptureSession cameraCaptureSession) {
                            Log.e(TAG, "CaptureSession onConfigureFailed");
                        }
                    }, mBackgroundHandler
            );
        } catch (CameraAccessException e) {
            Log.e(TAG, e.getMessage(), e);
        }
    }


    public void release() {
        Log.i(TAG, "CameraPhotoControl release");
        boolean isFinished = false;
        int retried = 0;
        sensor.stop();
        imageAvailableListener.setSkippingCapture(true);
        if (camera != null) {
            camera.close();
            camera = null;
        }
        do {
            retried++;
            isFinished = mBackgroundThread.quitSafely();
        } while (retried < 3 && !isFinished);
        if (!isFinished) {
            isFinished = mBackgroundThread.quit();
            if (!isFinished) {
                Log.e(TAG, "mBackgroundThread does not successfully finished");
            }
        }
        mBackgroundThread = null;
        Log.i(TAG, "CameraPhotoControl finshed");
    }

    protected void setSurfaceTextureLister() {
        Log.i(TAG, "Camera init Step 3 set OnImageAvailableListener and wait onSurfaceTextureAvailable ");
        mTextureView.setSurfaceTextureListener(new TextureView.SurfaceTextureListener() {
            @Override
            public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height) {
                Log.i(TAG, "Camera init Step 4.3  onSurfaceTextureAvailable");
                if (status == STATUS_SURFACE_RELEASED) {
                    return;
                }
                surfaceWidth = width;
                surfaceHeight = height;
                status = STATUS_SURFACE_READY;
                setup();
            }

            @Override
            public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) {

            }

            @Override
            public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) {
                return false;
            }

            @Override
            public void onSurfaceTextureUpdated(SurfaceTexture surface) {

            }
        });
    }

    protected boolean getCameraAndSize() {
        // 获取摄像头的管理者CameraManager
        CameraManager manager = (CameraManager) mTextureView.getContext().
                getSystemService(Context.CAMERA_SERVICE);
        try {
            // 遍历所有摄像头
            for (String cameraId : manager.getCameraIdList()) {
                CameraCharacteristics map = manager.getCameraCharacteristics(cameraId);
                // 1=full, 2=legacy, 3=超越full的存在, 0=limited
                Log.i(TAG, "camera=" + cameraId
                        + ", hardware_level=" + map.get(CameraCharacteristics.INFO_SUPPORTED_HARDWARE_LEVEL));
                // 默认打开后置摄像头
                if (map.get(CameraCharacteristics.LENS_FACING) ==
                        CameraCharacteristics.LENS_FACING_BACK) {
                    mCameraId = cameraId;
                    StreamConfigurationMap config = map.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);
                    if (config == null) {
                        return false;
                    }
//                    previewSize = choosePreviewSize(config.getOutputSizes(SurfaceTexture.class));
                    // 切换前置镜头后，解决预览变形问题
                    previewSize = getOptimalPreviewSize(config.getOutputSizes(SurfaceTexture.class),
                            surfaceWidth, surfaceHeight);
                    sensorOrientation = map.get(CameraCharacteristics.SENSOR_ORIENTATION);
                    // 选择一个后置摄像头
                    Log.i(TAG, "Camera init Step 5.1 cameraId choosen :"
                            + mCameraId + " orientation:" + sensorOrientation);
                    return true;
                }
            }


        } catch (CameraAccessException e) {
            Log.e(TAG, "CameraCharacteristics failed", e);
            return false;
        }
        Log.e(TAG, "CameraCharacteristics LENS_FACING_BACK failed");
        return false;
    }

    protected void startBackgroundThread() {
        mBackgroundThread = new HandlerThread("CameraPhotoBackground");
        mBackgroundThread.start();
        Log.i(TAG, "Camera init Step 1 start new Background Thread");
        mBackgroundHandler = new Handler(mBackgroundThread.getLooper());
    }

    protected Size choosePreviewSize(Size[] sizes) {
        int MIN_WIDTH = 800;

        int minWidth = MIN_WIDTH;
        int minHeight = MIN_WIDTH * surfaceHeight / surfaceWidth;

        Log.i(TAG, "choosePreviewSize from minSize: " + minWidth + "*" + minHeight
                + " origin:" + surfaceWidth + "*" + surfaceHeight);
        SortedMap<Integer, Size> candi = new TreeMap<>();
        for (Size sz : sizes) { // 此处大小按照逆序排列
            /**
             float width = (float) (sz.getWidth());
             float height = (float) (sz.getHeight());
             float ratio = width / height;
             float rvRatio = (float) surfaceHeight / surfaceWidth;
             */
            int width = sz.getHeight(); // 照相机的宽和高是横过来算的
            int height = sz.getWidth();
            if (width > minWidth && height > minHeight) {
                // Log.i(TAG, "preivew size choosen: " + width + "*" + height);
                candi.put(width * height, sz);
            }
        }
        Size sizeChoosen;
        if (candi.size() > 0) {
            sizeChoosen = candi.get(candi.firstKey());
        } else {
            sizeChoosen = sizes[0];
            Log.i(TAG, "choosePreviewSize no bigger than min: sizes.size=" + sizes.length
                    + ", first=" + sizeChoosen.getHeight() + "*" + sizeChoosen.getWidth());
        }
        Log.i(TAG, "choosePreviewSize finish: " + sizeChoosen.getHeight() + "*" + sizeChoosen.getWidth());
        return sizeChoosen;
    }

    // 切换前置镜头后，解决预览变形问题
    protected Size getOptimalPreviewSize(Size[] sizes, int w, int h) {
        final double aspectTolerance = 0.1;
        double targetRatio = (double) w / h;
        if (sizes == null) {
            return null;
        }
        Size optimalSize = null;
        double minDiff = Double.MAX_VALUE;

        int targetHeight = h;

        // Try to find an size match aspect ratio and size
        for (Size size : sizes) {
            double ratio = (double) size.getWidth() / size.getHeight();
            if (Math.abs(ratio - targetRatio) > aspectTolerance) {
                continue;
            }
            if (Math.abs(size.getHeight() - targetHeight) < minDiff) {
                optimalSize = size;
                minDiff = Math.abs(size.getHeight() - targetHeight);
            }
        }

        // Cannot find the one match the aspect ratio, ignore the requirement
        if (optimalSize == null) {
            minDiff = Double.MAX_VALUE;
            for (Size size : sizes) {
                if (Math.abs(size.getHeight() - targetHeight) < minDiff) {
                    optimalSize = size;
                    minDiff = Math.abs(size.getHeight() - targetHeight);
                }
            }
        }
        return optimalSize;
    }
}