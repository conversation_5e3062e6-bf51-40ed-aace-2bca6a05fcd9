package com.facishare.fs.biz_personal_info.manage.bean;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Created by changl on 2016/6/22.
 */
public class AGetInviteSwitchResponse implements Serializable {

    /**
     * 开关1:开，2：关
     */
    @JSONField(name="a")
    public String inviteSwitch;

    public boolean isOn;

    public AGetInviteSwitchResponse(){}

    @JSONCreator
    public AGetInviteSwitchResponse(@JSONField(name="a") String inviteSwitch) {
        this.inviteSwitch = inviteSwitch;
        if ("1".equals(this.inviteSwitch)){
            isOn = true;
        } else {
            isOn = false;
        }
    }
}
