package com.facishare.fs.biz_personal_info.datactrl;

import java.util.List;

import android.content.Context;
import android.content.Intent;

/**
 * <AUTHOR>
 * at 2018/8/29 16:19
 */
public interface EmpAndDepChooseController {
    Intent onAddClick(Context context, List<Integer> curEmpList, List<Integer> curDepList);

    void onRemove(Context context, List<Integer> removeEmpList, List<Integer> removeDepartmentList, List<Integer> restEmpList, List<Integer> restDepartmentList, EmpAndDepRemoveCallback callback);

    /**
     * 需要业务方自己合并列表，进行操作。注意lastEmpList和lastDepList修改不会对列表生效
     *
     * @param lastEmpList 增加前的员工列表
     * @param lastDepList 增加前的部门列表
     * @param callback
     */
    void onActivityResult(Context context, Intent data, List<Integer> lastEmpList, List<Integer> lastDepList, EmpAndDepAddCallback callback);

    Intent onFinish(List<Integer> finalEmpList, List<Integer> finalDepList);
}
