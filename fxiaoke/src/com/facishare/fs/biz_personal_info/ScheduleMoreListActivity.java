package com.facishare.fs.biz_personal_info;

import com.facishare.fs.i18n.I18NHelper;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.*;

import com.facishare.fs.BaseActivity;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.ui.FeedsUitls;
import com.facishare.fs.utils_fs.ImageLoaderUtil;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fslib.R;
import com.fs.beans.beans.EnumDef;
import com.fxiaoke.fscommon.util.FsUrlUtils;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.fxiaoke.lib.qixin.biz_ctrl.beans.ScheduleVo;
import com.fxiaoke.lib.qixin.consts.ScheduleBizTypes;
import com.nostra13.universalimageloader.core.ImageLoader;

import java.io.Serializable;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

public class ScheduleMoreListActivity extends BaseActivity {
    private static final String EXTRA_SCHEDULE_LIST = "schedule_list";
    private static final String EXTRA_QUERY_EMPS = "query_emps";
    private ListView mListView;
    private List<ScheduleVo> mDataList = new ArrayList<>();
    private Set<Integer> mQueryEmps = new TreeSet<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.schedule_more_list_act);
        initTitle();
        initIntent();
        initView();
    }

    private void initTitle() {
        initTitleCommon();
        mCommonTitleView.setMiddleText(I18NHelper.getText("xt.session_msg_schedule_item_layout.text.day_detail")/* 日程详情 */);
        mCommonTitleView.addLeftAction(I18NHelper.getText("crm.layout.activity_out_profile_person_info.8232")/* 返回 */, R.string.return_before_new_small, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }


    private void initView() {
        mListView = findViewById(R.id.listView);
        mListView.setAdapter(new MyAdapter());
        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                ScheduleVo scheduleVo = mDataList.get(position);
                if (!TextUtils.isEmpty(scheduleVo.androidUrl)) {
                    FsUrlUtils.gotoAction(ScheduleMoreListActivity.this, scheduleVo.androidUrl);
                    return;
                }
                int bizType = scheduleVo.bizType;
                if (bizType == ScheduleBizTypes.FEED) {
                    if (TextUtils.isEmpty(scheduleVo.content)) {
                        //没summary代表没权限
                        ToastUtils.show(I18NHelper.getText("xt.fs.ScheduleMoreListActivity.4")/* 该日程只向你开放了“参会时间” */);
                    } else {
                        FeedsUitls.showFeedDetail(context, Integer.parseInt(scheduleVo.bizDataId), EnumDef.FeedType.Schedule.value, scheduleVo.id);
                    }
                } else {
                    ToastUtils.show(I18NHelper.getFormatText("xt.fs.ScheduleTimeHelperActivity.9", bizType + "")/* 不正确的日程类型：{0} */);
                }
            }
        });
    }

    private void initIntent() {
        mDataList.clear();
        mDataList.addAll((List<ScheduleVo>) getIntent().getSerializableExtra(EXTRA_SCHEDULE_LIST));
        mQueryEmps.clear();
        mQueryEmps.addAll((Collection<Integer>) getIntent().getSerializableExtra(EXTRA_QUERY_EMPS));
    }

    public static final Intent getIntent(Context context, List<ScheduleVo> list, Collection<Integer> queryEmployees) {
        Intent intent = new Intent(context, ScheduleMoreListActivity.class);
        intent.putExtra(EXTRA_SCHEDULE_LIST, (ArrayList) list);
        intent.putExtra(EXTRA_QUERY_EMPS, (Serializable) queryEmployees);
        return intent;
    }

    private static final DateFormat HOUR_FORMAT = new SimpleDateFormat("HH:mm");

    private class MyAdapter extends BaseAdapter {

        @Override
        public int getCount() {
            return mDataList.size();
        }

        @Override
        public Object getItem(int position) {
            return mDataList.get(position);
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            if (convertView == null) {
                convertView = LayoutInflater.from(ScheduleMoreListActivity.this).inflate(R.layout.schedule_more_list_item, parent, false);
            }
            ViewHolder holder = (ViewHolder) convertView.getTag();
            if (holder == null) {
                holder = new ViewHolder();
                holder.viewPoint = convertView.findViewById(R.id.view_point);
                holder.ivHead1 = convertView.findViewById(R.id.imageView_head1);
                holder.ivHead2 = convertView.findViewById(R.id.imageView_head2);
                holder.tvTime = convertView.findViewById(R.id.textView_time);
                holder.tvContent = convertView.findViewById(R.id.textView_content);
            }
            ScheduleVo s = mDataList.get(position);
            holder.viewPoint.setBackgroundResource(s.participateIn ? R.drawable.schedule_point_shape_mine : R.drawable.schedule_point_shape_colleague);

            Pair<String, String> heads = ScheduleUtils.getTwoHeadsFromMap(s.participantMap, mQueryEmps, FSContextManager.getCurUserContext().getAccount().getEmployeeIntId());

            ImageLoader.getInstance().displayImage(WebApiUtils.getDownloadUrlForImg(heads.first, WebApiUtils.ImageType.IMG_100x100),
                    holder.ivHead1,
                    ImageLoaderUtil.getUserHeadImgDisplayImageOptionsForRoundedDefault(context));
            if (heads.second == null) {
                holder.ivHead2.setVisibility(View.GONE);
            } else {
                holder.ivHead2.setVisibility(View.VISIBLE);
                ImageLoader.getInstance().displayImage(WebApiUtils.getDownloadUrlForImg(heads.second, WebApiUtils.ImageType.IMG_100x100),
                        holder.ivHead2,
                        ImageLoaderUtil.getUserHeadImgDisplayImageOptionsForRoundedDefault(context));
            }

            if (s.end == 0) {
                holder.tvTime.setText(HOUR_FORMAT.format(new Date(s.start)));
            }else {
                holder.tvTime.setText(HOUR_FORMAT.format(new Date(s.start)) + "~" + HOUR_FORMAT.format(new Date(s.end)));
            }
            holder.tvContent.setText(s.content);
            return convertView;
        }
    }

    private static class ViewHolder {
        private View viewPoint;
        private ImageView ivHead1;
        private ImageView ivHead2;
        private TextView tvTime;
        private TextView tvContent;
    }
}
