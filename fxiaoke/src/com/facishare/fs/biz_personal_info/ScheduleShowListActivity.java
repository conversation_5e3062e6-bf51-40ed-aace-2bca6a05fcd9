package com.facishare.fs.biz_personal_info;

import com.billy.cc.core.component.CC;
import com.billy.cc.core.component.CCResult;
import com.billy.cc.core.component.IComponentCallback;
import com.facishare.fs.biz_feed.newfeed.feedenum.FeedEnum;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.datactrl.AbsCalendarCtrl;
import com.facishare.fs.biz_session_msg.SessionMsgActivity;
import com.facishare.fs.biz_session_msg.beans.QixinStatisticsEvent;
import com.facishare.fs.biz_session_msg.utils.FeedBizUtils;
import com.facishare.fs.biz_session_msg.utils.MsgCreateUtils;
import com.facishare.fs.biz_session_msg.utils.SessionInfoUtils;
import com.facishare.fs.biz_session_msg.views.TitlePopWindow;
import com.facishare.fs.contacts_fs.FeedShareRangeHandler;
import com.facishare.fs.contacts_fs.SelectSendRangeActivity;
import com.facishare.fs.contacts_fs.picker.DepartmentPicker;
import com.facishare.fs.i18n.I18NHelper;

import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.alibaba.fastjson.JSON;
import com.facishare.fs.BaseActivity;
import com.facishare.fs.biz_function.subbiz_outdoorsignin.FsTickUtils;
import com.facishare.fs.biz_session_msg.dialog.FunctionTipDialog;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.pluginapi.HostInterface;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.contact.beans.CSDataConfig;
import com.facishare.fs.pluginapi.contact.beans.SelectSendRangeConfig;
import com.facishare.fs.ui.setting.ScheduleSyncSettingActivity;
import com.facishare.fs.ui.setting.TestQixinActivity;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fslib.R;
import com.facishare.fs.Shell;
//import com.facishare.fs.biz_feed.bean.ScheduleInfoEntity;
import com.facishare.fs.biz_feed.subbiz_send.XSendScheduleActivity;
import com.facishare.fs.biz_feed.utils.FeedSP;
import com.facishare.fs.biz_personal_info.datactrl.ScheduleCalendarCtrl;
import com.facishare.fs.ui.FeedsUitls;
import com.facishare.fs.utils_fs.Accounts;
import com.fs.beans.beans.EnumDef;
import com.fs.beans.beans.GetCalendarViewResult;
import com.fs.beans.beans.ScheduleShortInfo;
import com.fxiaoke.cmviews.xlistview.XListView;
import com.fxiaoke.fscommon.util.FsUrlUtils;
import com.fxiaoke.fscommon_res.view.calendar.base.CalendarLayout;
import com.fxiaoke.fscommon_res.view.calendar.base.MonthView;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.SessionMessageTemp;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.lib.qixin.biz_ctrl.SessionCommonUtils;
import com.fxiaoke.stat_engine.StatEngine;

import java.util.*;

import androidx.fragment.app.FragmentActivity;
import de.greenrobot.event.core.MainSubscriber;


/**
 * Created by wangyp on 2015/11/24.
 */
public class ScheduleShowListActivity extends BaseActivity implements XListView.IXListViewListener, AdapterView.OnItemClickListener {

    private final static String CALENDAR_MODE = "schedule_list_calendar_mode";
    private static final int REQUESTCODE_SELECT_COLLEAGUE = 1;

    public static final String PLATFORM_FEED = "XT.FEED";
    public static final String PLATFORM_VISIT = "CRM.VISIT";

    CalendarLayout mCalendarLayout;
    XListView mlistView;
    MyCalendarListAdapter myCalendarListAdapter;
    View no_content_LinearLayout;
    String mDateFormat;

    private TitlePopWindow mRightPopWindow;
    private TextView mConfirmButton;

    public static final String KEY_MODE = "key_select_mode";
    public static final String KEY_OUT_TIME = "out_time_key";
    public static final String KEY_SESSION_ID = "key_session_id";

    private static final int MODE_LOOK_UP = 0;
    private static final int MODE_SELECT_TO_SHARE = 1;

    private int mMode;
    private long mOutTime;
    private String mSessionId;

    private ScheduleShortInfo mSelectSchedule;

    public static Intent getIntentForShare(Context context, String sessionId) {
        Intent intent = new Intent(context, ScheduleShowListActivity.class);
        intent.putExtra(KEY_MODE, MODE_SELECT_TO_SHARE);
        intent.putExtra(KEY_SESSION_ID, sessionId);
        return intent;
    }

    ScheduleCalendarCtrl mScheduleCalendarCtrl = null;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.schedule_show_list_act);
        initData();
        initTitle();
        mCalendarLayout = findViewById(R.id.calendar);
        mlistView = (XListView) findViewById(R.id.calendar_show_list_view);
        myCalendarListAdapter = new MyCalendarListAdapter(context, null);
        myCalendarListAdapter.setSelectMode(mMode == MODE_SELECT_TO_SHARE);
        mlistView.setAdapter(myCalendarListAdapter);
        mlistView.setPullLoadEnable(true);
        mlistView.setXListViewListener(this);
        mlistView.hideFooter();
        mlistView.postDelayed(new Runnable() {
            @Override
            public void run() {
                GetCalendarViewResult re = readCurrentCalendarSchedule();
                refreshList(re);
            }
        },10);
        mlistView.setOnItemClickListener(this);

        initNoContentView();

        mMainRefListData = new MainSubscriber<RefListData>() {

            @Override
            public void onEventMainThread(RefListData event) {
                //刷新数据 需要刷新 当前选定日期的日程列表
                mScheduleCalendarCtrl.refresh();
            }
        };

        mMainRefListData.register();
        int mode = FeedSP.getType(CALENDAR_MODE,MonthView.CalendarMode.MONTH.ordinal());
        mScheduleCalendarCtrl = new ScheduleCalendarCtrl(this, mode);

        showDialog();

        show();

        mlistView.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                int height = mCalendarLayout.getHeight();
                for (int i=0; i<mCalendarLayout.getChildCount(); ++i) {
                    View childView = mCalendarLayout.getChildAt(i);
                    if (childView == mlistView) {
                        break;
                    }
                    if (childView instanceof MonthView && ((MonthView) childView).isEnableCollapse() && ((MonthView) childView).getCalendarMode() == MonthView.CalendarMode.MONTH) {
                        height -= ((MonthView) childView).getMinHeight();
                    } else {
                        height -= childView.getHeight();
                    }
                }
                LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) mlistView.getLayoutParams();
                params.height = height;
                mlistView.setLayoutParams(params);

                mlistView.getViewTreeObserver().removeGlobalOnLayoutListener(this);
            }
        });
    }

    private void initData() {
        Intent intent = getIntent();
        mMode = intent.getIntExtra(KEY_MODE, MODE_LOOK_UP);
        mOutTime = intent.getLongExtra(KEY_OUT_TIME, 0);
        mSessionId = intent.getStringExtra(KEY_SESSION_ID);

        // 设置视图日期时间，默认为当前
        mDateFormat = ScheduleUtils.formatDateSchedule(mOutTime > 0 ? mOutTime : new Date().getTime());
    }

    private void initTitle() {
        initTitleCommon();
        mCommonTitleView.setMiddleText(I18NHelper.getText("xt.work_home_head.des.my_schedule")/* 我的日程 */);
        if (!isInMenu()) {
            mCommonTitleView.addLeftBackAction(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    finish();
                }
            });
        }

        if (mMode == MODE_LOOK_UP) {
            mCommonTitleView.addRightAction(R.string.more_icon, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    initPopMenuIfNeed();
                    mRightPopWindow.show(mCommonTitleView, null);
                }
            });
            mCommonTitleView.addRightAction(R.string.fs_create_add, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Calendar date = mScheduleCalendarCtrl.getSelectCalendar();
                    StatEngine.tick("Calendar_Add");
                    long beginTime = 0;
                    if (date != null) {
                        date.set(Calendar.HOUR_OF_DAY, 9);
                        date.set(Calendar.MINUTE, 0);
                        beginTime = date.getTimeInMillis();
                    }
                    startSendSchedulePage(context, beginTime);
                    setNeedUpdateSchedulePageOnceOnResume();
                }
            });
        } else if (mMode == MODE_SELECT_TO_SHARE) {
            mConfirmButton = mCommonTitleView.addRightAction(I18NHelper.getText("av.common.string.confirm")/* 确定 */, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mSelectSchedule == null) {
                        return;
                    }
                    checkPermissionAndShareSchedule(mSelectSchedule);
                }
            });
            mConfirmButton.setTextColor(getResources().getColor(R.color.primaryTitleColorDisable));
        }
    }

    boolean needUpdateSchedulePageOnceOnResume = false;//标记是否需要在界面重新渲染时更新下日历的日程数据
    private void setNeedUpdateSchedulePageOnceOnResume() {
        needUpdateSchedulePageOnceOnResume = true;
    }
    private void resetNeedUpdateSchedulePageOnceOnResume() {
        needUpdateSchedulePageOnceOnResume = false;
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (needUpdateSchedulePageOnceOnResume && mScheduleCalendarCtrl != null && mScheduleCalendarCtrl.getSelectCalendar() != null) {
            try {
                mScheduleCalendarCtrl.getDataByDate(mScheduleCalendarCtrl.getSelectCalendar(), AbsCalendarCtrl.QUERY_TYPE_SHOW_MONTH);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        resetNeedUpdateSchedulePageOnceOnResume();
    }

    public static Calendar getDefaultCalendar(){
        Calendar cal = Calendar.getInstance();
        if (cal.get(Calendar.MINUTE) < 30) {
            cal.set(Calendar.MINUTE, 30);
        } else {
            cal.set(Calendar.MINUTE, 0);
            cal.add(Calendar.HOUR_OF_DAY, 1);
        }
        cal.set(Calendar.SECOND,0);
        return cal;
    }

    private void startSendSchedulePage(Context context, long beginTime) {
        XSendScheduleActivity.start(context, beginTime);
    }

    /**
     * 企信右上角菜单
     */
    private void initPopMenuIfNeed() {
        if (mRightPopWindow == null) {
            mRightPopWindow = new TitlePopWindow(this, TitlePopWindow.DisplayConfig.create(TitlePopWindow.DisplayConfig.Orientation.RIGHT).build());
            TitlePopWindow.ItemData data1 = new TitlePopWindow.ItemData();
            data1.name = I18NHelper.getText("xt.fs.ScheduleShareSettingsActivity.8")/* 共享设置 */;
            TitlePopWindow.ItemData data2 = new TitlePopWindow.ItemData();
            data2.name = I18NHelper.getText("xt.fs.ScheduleShowListActivity.2")/* 查看同事 */;
            TitlePopWindow.ItemData data3 = new TitlePopWindow.ItemData();
            data3.name = I18NHelper.getText("xt.fs.ScheduleShowListActivity.schedule_conflict_settings_entry","冲突设置")/* 冲突设置 */;

            List<TitlePopWindow.ItemData> data = new ArrayList<TitlePopWindow.ItemData>();
            data.add(data1);
            data.add(data2);
            boolean canSet = HostInterfaceManager.getCloudCtrlManager().getBooleanConfig("can_set_schedule_conflict", true);
            if (canSet) {//日程冲突提醒设置页面
                data.add(data3);
            }

            mRightPopWindow.setData(data, new TitlePopWindow.OnItemClickLis() {

                @Override
                public void onItemClick(int pos) {
                    switch (pos) {
                        case 0:
                            Intent intent = new Intent(ScheduleShowListActivity.this, ScheduleShareSettingsActivity.class);
                            startActivity(intent);
                            break;
                        case 1:
                            SelectSendRangeConfig selectSendRangeConfig = new SelectSendRangeConfig.Builder().setTitle(I18NHelper.getText("xt.selectuserupdateactivity.text.choose_colleagues")/* 选择同事 */)
                                    .setNoSelf(false).setLastTab(true).setEmpsMap(null).setShowDepTab(true).setGrouptab(true)
                                    .setConfirmChecker(ScheduleUtils.getSelectSendRangeConfigChecker())
                                    .setCsDataConfig(CSDataConfig.builder().setShowMyDep(false).setShowMyMainDepOwner(false).setShowMyMainDep(false).setShowGlobal(false).build())
                                    .build();
                            Intent it = SelectSendRangeActivity.getIntent(ScheduleShowListActivity.this, selectSendRangeConfig);
                            startActivityForResult(it, REQUESTCODE_SELECT_COLLEAGUE);
                            break;
                        case 2:
                            FsUrlUtils.ActionConfig actionConfig = new FsUrlUtils.ActionConfig(ScheduleShowListActivity.this,
                                    "ava://object_detail/pages/feed/schedule_conflict_remind/index");
                            actionConfig.setEventTypeCallBack(new IComponentCallback() {
                                @Override
                                public void onResult(CC cc, CCResult result) {
                                    if (result != null && !result.isSuccess()) {
                                        FCLog.e("UtilOpenAction", "onResult failed ccId:" + cc.getCallId() + " with error:" + result
                                                + " and url： " + actionConfig.getActionUrl());
                                    }
                                }
                            });
                            FsUrlUtils.gotoAction(actionConfig);
                            break;
                        default:
                            break;
                    }
                }
            });
        }
    }

    private ImageView imgTip;
    private TextView textTip;

    private void initNoContentView() {
        no_content_LinearLayout = (View) findViewById(R.id.no_content_LinearLayout);
        imgTip = (ImageView) no_content_LinearLayout.findViewById(R.id.no_content_img);
        textTip = (TextView) no_content_LinearLayout.findViewById(R.id.no_content_text);
        imgTip.setImageResource(R.drawable.attendance_emptypage_icon);
        textTip.setText(I18NHelper.getText("xt.scheduleshowlistactivity.text.no_schedule_yet")/* 还没有日程 */);
        no_content_LinearLayout.setVisibility(View.GONE);
    }

    public void endPross() {
        stopRefresh();
    }

    //请求失败
    public void endFailed(){
        imgTip.setImageResource(R.drawable.my_empty_failed);
        no_content_LinearLayout.setVisibility(View.VISIBLE);
        textTip.setText(I18NHelper.getText("xt.scheduleshowlistactivity.text.failed_to_get_data")/* 获取数据失败 */);
        myCalendarListAdapter.updateData(null);
        this.mlistView.setVisibility(View.GONE);
        stopRefresh();
    }

    public void stopRefresh() {
        this.stopRefresh(true);
    }

    public void stopRefresh(boolean isSuccess) {
        if(this.mlistView != null) {
            this.mlistView.stopRefresh();
        }

    }
    @Override
    public void onRefresh() {
        mScheduleCalendarCtrl.refresh();
    }

    @Override
    public void onLoadMore() {

    }

    /**
     * 保存今天天的日程记录
     */
    public void saveCurrentCalendarSchedule(GetCalendarViewResult response,Calendar calendar){
        Calendar today = Calendar.getInstance();
        boolean istoday = today.get(Calendar.YEAR) == calendar.get(Calendar.YEAR)
                &&today.get(Calendar.MONTH) == calendar.get(Calendar.MONTH)
                &&today.get(Calendar.DAY_OF_MONTH) == calendar.get(Calendar.DAY_OF_MONTH);

        if(istoday){
            String json = JSON.toJSONString(response);
            Accounts.saveCache("schedule_calendar_key",json);
        }

    }

    private GetCalendarViewResult readCurrentCalendarSchedule(){
            String json = Accounts.getCache("schedule_calendar_key");
            if(json!=null){
                GetCalendarViewResult result = JSON.parseObject(json,GetCalendarViewResult.class);
                Calendar today = Calendar.getInstance();
                Calendar searchTime = Calendar.getInstance();
                searchTime.setTimeInMillis(result.searchDateTime);
                boolean istoday = today.get(Calendar.YEAR) == searchTime.get(Calendar.YEAR)
                        &&today.get(Calendar.MONTH) == searchTime.get(Calendar.MONTH)
                        &&today.get(Calendar.DAY_OF_MONTH) == searchTime.get(Calendar.DAY_OF_MONTH);
                if(istoday){
                    return result;
                }else{
                    Accounts.saveCache("schedule_calendar_key",null);
                }
            }
        return null;
    }

    public void refreshList(GetCalendarViewResult response){
        if (response != null && response.schedules != null && response.schedules.size() > 0) {
            myCalendarListAdapter.updateData(response.schedules);
            no_content_LinearLayout.setVisibility(View.GONE);
            mlistView.setVisibility(View.VISIBLE);
        }else{
            myCalendarListAdapter.updateData(null);
            mlistView.setVisibility(View.GONE);
            textTip.setText(I18NHelper.getText("xt.scheduleshowlistactivity.text.no_schedule_yet")/* 还没有日程 */);
            no_content_LinearLayout.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mMainRefListData.unregister();
        FeedSP.saveType(CALENDAR_MODE,mScheduleCalendarCtrl.getCalendarMode());
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    MainSubscriber<RefListData> mMainRefListData;


    public static class RefListData {
        boolean isRef;
    }
    AlertDialog alertDialog;
    private void showDialog()
    {
        if (FeedSP.getBooleanType("schedule_guide_map")) {
            return;
        }
        alertDialog = new AlertDialog.Builder(context).create();
        alertDialog.getWindow().setBackgroundDrawable(new ColorDrawable());
        alertDialog.show();
        View view = LinearLayout.inflate(context,R.layout.schedule_show_dialog,null);
        alertDialog.setContentView(view);
        alertDialog.setCanceledOnTouchOutside(true);
        view.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                alertDialog.dismiss();
                return false;
            }

        });
        FeedSP.saveBooleanType("schedule_guide_map",true);

    }

    private void show(){
        if(!FeedSP.getBooleanType("is_frist_open_schedule_sync")){
            FeedSP.saveBooleanType("is_frist_open_schedule_sync",true);    new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    String title = I18NHelper.getText("xt.scheduleshowlistactivity.text.schedule_sync_to_phone_calendar")/* 日程同步到手机日历 */;
                    String[] contents = {I18NHelper.getText("xt.scheduleshowlistactivity.text.you_can_view_the_last")/* 您可以在：我的-设置-隐私-日程同步设置中，打开手机日历同步，即可在手机系统日历查看最近15天日程 */};
                    int[] contentTextColors = {0xff666666};
                    String confirmText = I18NHelper.getText("xt.scheduleshowlistactivity.text.ignore")/* 忽略 */;
                    String set = I18NHelper.getText("xt.scheduleshowlistactivity.text.set_now")/* 立即设置 */;
                    FunctionTipDialog.showDialog(context, FunctionTipDialog.DialogStyle.IMAGE_TOP,
                            R.drawable.group_bot_first_tip_top_img, title, contents,
                            contentTextColors,set, confirmText,  new FunctionTipDialog.DialogClickListener() {
                                @Override
                                public void onClick(FunctionTipDialog dialog, View view) {
//                                FeedSP.saveBooleanType("isTempDialog",true);
                                    startActivity(new Intent(ScheduleShowListActivity.this, ScheduleSyncSettingActivity.class));
                                    dialog.cancel();
                                    FsTickUtils.tickXT(FsTickUtils.schedule_sync_gotosetting);
                                }
                            }, new FunctionTipDialog.DialogClickListener() {
                                @Override
                                public void onClick(FunctionTipDialog dialog, View view) {
                                    dialog.cancel();
                                    FsTickUtils.tickXT(FsTickUtils.schedule_sync_ignore);
                                }
                            });
                }
            },500);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUESTCODE_SELECT_COLLEAGUE && resultCode == RESULT_OK) {
            Map<Integer, String> employeeMap = DepartmentPicker.getEmployeesMapPicked();
            Map<Integer, String> departmentMap = DepartmentPicker.getDepartmentsMapPicked();
            Map<String, Boolean> groupMap = DepartmentPicker.getGroupMapPicked();

            Map<Integer, String> employeePicked = ScheduleUtils.getMergedPickedEmployee(employeeMap, departmentMap, groupMap, ScheduleUtils.getScheduleLookUpEmployeeMaxCount()).first;
            FeedSP.saveShareRangemEmployee(employeePicked);

            Intent queryIntent = ScheduleTimeHelperActivity.getQueryIntent(this, I18NHelper.getText("xt.fs.ScheduleShowListActivity.2")/* 查看同事 */, employeePicked, System.currentTimeMillis());
            startActivity(queryIntent);
        }
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        ScheduleShortInfo scheduleInfo = (ScheduleShortInfo) parent.getItemAtPosition(position);
        if (scheduleInfo == null) {
            return;
        }
        setNeedUpdateSchedulePageOnceOnResume();
        if (mMode == MODE_SELECT_TO_SHARE) {
            onItemSelect(scheduleInfo);
            return;
        }
        if (!TextUtils.isEmpty(scheduleInfo.androidUrl)) {
            FsUrlUtils.gotoAction(ScheduleShowListActivity.this, scheduleInfo.androidUrl);
            return;
        }else {
            FCLog.i("ScheduleShowListActivity","scheduleInfo.androidUrl is null");
        }
        if (PLATFORM_FEED.equalsIgnoreCase(scheduleInfo.platform)) {
            FeedsUitls.showDetailsInfoOfSchedule(ScheduleShowListActivity.this, Integer.parseInt(scheduleInfo.dataId), scheduleInfo.id);
            StatEngine.tick("Calendar_Event_Schedule");
        } else if (PLATFORM_VISIT.equalsIgnoreCase(scheduleInfo.platform)) {
            Shell.go2VisitDetail(ScheduleShowListActivity.this, scheduleInfo.dataId);
            StatEngine.tick("Calendar_Event_Visit");
        }
    }

    private void onItemSelect(ScheduleShortInfo scheduleInfo) {
        if (!PLATFORM_FEED.equalsIgnoreCase(scheduleInfo.platform)) {
            return;
        }

        if (mSelectSchedule == null || !TextUtils.equals(mSelectSchedule.id, scheduleInfo.id)) {
            mSelectSchedule = scheduleInfo;
            mConfirmButton.setTextColor(getResources().getColor(R.color.primaryTitleColor));
        } else {
            mSelectSchedule = null;
            mConfirmButton.setTextColor(getResources().getColor(R.color.primaryTitleColorDisable));
        }
        myCalendarListAdapter.setSelectedItem(mSelectSchedule);
        myCalendarListAdapter.notifyDataSetChanged();
    }

    private void checkPermissionAndShareSchedule(ScheduleShortInfo scheduleInfo) {
        int feedId = Integer.parseInt(scheduleInfo.dataId);
        int feedType = EnumDef.FeedType.Schedule.value;
        int senderId = scheduleInfo.userId;

        FeedShareRangeHandler feedShareRangeHandler = new FeedShareRangeHandler(context, feedId, feedType, senderId);
        feedShareRangeHandler.setFeedShareRangeCallback(new FeedShareRangeHandler.FeedShareRangeCallback() {
            @Override
            public void onConfirmShareRange(List<Integer> outsideEmployeeIds) {
                QixinStatisticsEvent.tick(QixinStatisticsEvent.SCHEDULE_SHARE_SENDCONFIRM);
                if (MsgCreateUtils.needCreateTempMessageByServer(feedType)) {
                    SessionListRec session = SessionCommonUtils.getSessionInAllSource(mSessionId);
                    String bizArgStr = null;
                    MsgCreateUtils.createTempMessageByServer(session, feedId, feedType, bizArgStr,scheduleInfo.id, new MsgCreateUtils.ICreateMsgCallBack() {
                        @Override
                        public void onSuccess(SessionMessageTemp smt) {
                            SessionMsgActivity.requestDiscussionDirect(context, smt, session, false);
                        }

                        @Override
                        public void onFailed(String error) {
                            if (!TextUtils.isEmpty(error)) {
                                ToastUtils.show(error);
                            }
                        }
                    });
                    return;
                }
                SessionMessageTemp smt = MsgCreateUtils.createTempMessage(feedId, feedType, senderId, scheduleInfo.summary, "", scheduleInfo.beginTime);
                Intent intent = new Intent();
                intent.putExtra(SessionMsgActivity.Intent_key_MSG, smt);
                setResult(RESULT_OK, intent);
                finish();
            }
        });
        SessionListRec session = SessionCommonUtils.getSessionInAllSource(mSessionId);
        feedShareRangeHandler.checkFeedShareRange(SessionInfoUtils.getSessionParticipantIds(session), session.getSessionId());
    }
}
