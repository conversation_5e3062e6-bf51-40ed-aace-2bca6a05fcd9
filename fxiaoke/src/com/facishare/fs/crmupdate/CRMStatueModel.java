package com.facishare.fs.crmupdate;

import android.app.Activity;
import android.content.Context;

import com.facishare.fs.account_system.beans.CheckCrmStatusResult;
import com.facishare.fs.biz_function.function_home.utils_home.FunctionUtils;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.pluginapi.Account;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiParameterList;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.fxlog.FCLog;

import java.util.Date;

/**
 * Created by 钟光燕 on 2016/6/2.
 * ===================================================
 * <p>
 * CRM model，获取CRM状态，网络和缓存
 * <p>
 * 把最新的网络状态缓存起来
 * <p>
 * ===================================================
 */
public class CRMStatueModel {

    public static final String TAG = CRMStatueModel.class.getSimpleName();
    public static final String CONTROLLER = "FHE/EM1AOPEN/Messenger";
    public static final String ACTION = "QueryCrmAvailability";
    private Context mContext ;
    private Account mAccount ;


    public CRMStatueModel() {
        mAccount = FSContextManager.getCurUserContext().getAccount();
    }

    public void getNetCrmStatue(final CRMStatueCallback callback) {
        WebApiParameterList param = WebApiParameterList.createWith("M1", FunctionUtils.getAppVersion());
        WebApiUtils.postAsync(CONTROLLER, ACTION, param, new WebApiExecutionCallback<CheckCrmStatusResult>() {

            @Override
            public TypeReference<WebApiResponse<CheckCrmStatusResult>> getTypeReference() {
                return new TypeReference<WebApiResponse<CheckCrmStatusResult>>() {
                };
            }
            public Class<CheckCrmStatusResult> getTypeReferenceFHE() {
                return CheckCrmStatusResult.class;
            }

            @Override
            public void completed(Date time, CheckCrmStatusResult response) {
                FCLog.i(TAG, " checkCrmStatus completed");

                if (response == null){
                    FCLog.e(TAG, " checkCrmStatus completed,response invalid");
                    return;
                }
                if (!"1".equals(response.code)) {
                    FCLog.e(TAG, " checkCrmStatus completed,response invalid");
                    return;
                }
                CRMSPUtil.putBoolean(CRMSPUtil.CRM_UPDATE_COMPLETE,false);
                cacheCrmStatue(callback,response.availability);
            }

            @Override
            public void failed(WebApiFailureType failureType, int httpStatusCode, String error, int errorCode, int enterpriseID) {
                FCLog.e(TAG, " checkCrmStatus failed，error=" + error + "httpStatusCode=" + httpStatusCode + "failureType==" + failureType.toString());
            }

        });
    }

    public void cacheCrmStatue(CRMStatueCallback callback,int availability) {
        mAccount.updateCrmStatus(availability, FunctionUtils.getAppVersion());
        if (callback != null){
             callback.callBackStatue(availability);
        }
    }
    public void getCacheStatue(CRMStatueCallback callback) {
        if (callback != null){
            callback.callBackStatue(mAccount.getCrmStatus(FunctionUtils.getAppVersion()));
        }
    }
}
