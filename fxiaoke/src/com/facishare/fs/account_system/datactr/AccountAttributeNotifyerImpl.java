package com.facishare.fs.account_system.datactr;

import java.util.List;

import android.app.Activity;
import android.widget.ImageView;

import com.facishare.fs.biz_session_msg.views.AccountChangedPopProvider;
import com.fxiaoke.dataimpl.msg.SingletonObjectHolder;

/** 
 *  
 * <AUTHOR> clw 
 */
public class AccountAttributeNotifyerImpl implements IAccountAttributeNotifyer{
    private Activity mContext;
    AccountChangedPopProvider mPopProvider = null;
    private static enum NotifyType{MarketVersion,TrialVersion}
    
    public AccountAttributeNotifyerImpl(Activity context){
    	mContext = context;
    }

	/**
	 * 通知改变成营销版
	 * @param mode
	 */
	@Override
	public void notifyToMarketingVersion(GetAccountEditionMode mode) {
		notifyCorrespondVersion(NotifyType.MarketVersion,mode);
	}

	/**
	 * 通知改变成工作版
	 * @param mode
	 */
	@Override
	public void notifyToWorkVersion(GetAccountEditionMode mode) {	
	    List<Object> lisObjects = SingletonObjectHolder.getInstance().findObjects(
	    		IAccountAttributeChangedListener.class);
	    IAccountAttributeChangedListener lis = null;
        for (Object ob : lisObjects) {
        	lis = (IAccountAttributeChangedListener) ob;
        	lis.onChangedToWorkVersion(mode);
        }			
	}

	/**
	 * 通知改变成通知版
	 * @param mode
	 */
	@Override
	public void notifyToTrailVersion(GetAccountEditionMode mode) {
		notifyCorrespondVersion(NotifyType.TrialVersion,mode);
	}
	
	private void notifyCorrespondVersion(final NotifyType notifyType,GetAccountEditionMode mode){
		if (mode == GetAccountEditionMode.PASSIVE_GET_MODE) {
			List<Object> lisObjects = SingletonObjectHolder.getInstance()
					.findObjects(IAccountAttributeChangedListener.class);
			IAccountAttributeChangedListener lis = null;
			for (Object ob : lisObjects) {
				lis = (IAccountAttributeChangedListener) ob;
				if (notifyType == NotifyType.TrialVersion) {
					lis.onChangedToTrialVersion(mode);
				} else if (notifyType == NotifyType.MarketVersion) {
					lis.onChangedToMarketingVersion(mode);
				}
			}
//			mContext.runOnUiThread(new Runnable() {
//				public void run() {
//					if (mPopProvider != null) {
//						mPopProvider.dismiss(mPopProvider.isShowing());
//					}
//					mPopProvider = new AccountChangedPopProvider(mContext);
//
//					if(notifyType == NotifyType.MarketVersion){
//						mPopProvider.showToMarketingVersionPop(new ImageView(mContext));
//					}else if(notifyType == NotifyType.TrialVersion){
//						mPopProvider.showToTrialVersionPop(new ImageView(mContext));
//					}
//
//				}
//			});

		}else if(mode == GetAccountEditionMode.ACTIVE_GET_MODE){
			List<Object> lisObjects = SingletonObjectHolder.getInstance()
					.findObjects(IAccountAttributeChangedListener.class);
			IAccountAttributeChangedListener lis = null;
			for (Object ob : lisObjects) {
				lis = (IAccountAttributeChangedListener) ob;
				if (notifyType == NotifyType.TrialVersion) {
					lis.onChangedToTrialVersion(mode);
				} else if (notifyType == NotifyType.MarketVersion) {
					lis.onChangedToMarketingVersion(mode);
				}
			}
		}
		
	}
}
