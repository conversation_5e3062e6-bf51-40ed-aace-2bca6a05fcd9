package com.facishare.fs.account_system.datactr;

import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.facishare.fs.App;
import com.facishare.fs.account_system.beans.EmployeeEditionData;
import com.facishare.fs.account_system.beans.GetCurrentEmployeeEditionResult;
import com.facishare.fs.common_utils.time.NetworkTime;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.pluginapi.Account;
import com.facishare.fs.pluginapi.AccountManager;
import com.facishare.fs.pluginapi.account.bean.AEmployeeEditionData;
import com.facishare.fs.pluginapi.account.bean.EmployeeEditionType;
import com.facishare.fs.utils_fs.SettingsSP;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiParameterList;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.dataimpl.msg.SingletonObjectHolder;
import com.fxiaoke.fxlog.FCLog;

/** 
 *  
 * <AUTHOR> clw 
 */
public class EmployeeEditioinUtils {	
	private final static String TAG = EmployeeEditioinUtils.class.getSimpleName();
	private final static int PRODUCT_TYPE_WORK = 0;
	private final static int PRODUCT_TYPE_MARKET = 1;
    private final static int ONE_HOUR_IN_MILLS = 1000*60*60;
	private final static int INTERVAL_TIME_IN_HOUR_OF_REFRESH_DATA = 24;
	
	public final static int DEFAULT_PRODUCT_ROLE = -1;
	public final static int DEFAULT_TRIVAL_VERSION_EXPIRE_TIME = -1;
	public final static boolean DEFAULT_HAS_FREE_PACKAGE = true;
	public final static boolean DEFAULT_IS_TRIAL_VERSION = false;
	
	
	
	public static boolean needReGetEditionDate(){
		AEmployeeEditionData editionData = getEmployeeEditionData();
        Account account = FSContextManager.getCurUserContext().getAccount();
        return editionData == null
				|| editionData.businessAccount==null||!editionData.businessAccount.equals(account.getEnterpriseAccount())
				|| editionData.employeeID==null||!editionData.employeeID.equals(account.getEmployeeId())
				|| getCurrentVersionType() == EmployeeEditionType.UNKNOW_VERSION
				|| (System.currentTimeMillis() - SettingsSP.getLastObtainEditionDataTime()
						> INTERVAL_TIME_IN_HOUR_OF_REFRESH_DATA*ONE_HOUR_IN_MILLS );
	}
	
	public static void getEmployeeEdition(GetAccountEditionMode getAccountEditionMode){
		AccountAttributeController.getEmployeeEdition(getAccountEditionMode);
        //此处保留更新时间逻辑，以避免请求不通引起频繁更新问题
//		long netWorkTime = NetworkTime.getInstance(App.getInstance()).getCurrentNetworkTime();
//		ContactsSettingsSP.putLastObtainEditionDataTime(netWorkTime);
	}
	
	public static void saveEmployeeEditionData(String businessAccount,String employeeID,int productRole,
			long trialVersionExpireTime,boolean hasFreePackage,boolean isTrialVersion)
	{
		AEmployeeEditionData employeeEditionData = new AEmployeeEditionData();
	
		employeeEditionData.businessAccount = businessAccount;
		employeeEditionData.employeeID = employeeID;
		employeeEditionData.productRole = productRole;
		employeeEditionData.trialVersionExpireTime = trialVersionExpireTime;
		employeeEditionData.hasFreePackage = hasFreePackage;
		employeeEditionData.isTrialVersion = isTrialVersion;
        //这里在首次登录后，更新版本信息，保证首次登录后不重复出现升级提示框，对已有时间差的影响很小
//		long netWorkTime = NetworkTime.getInstance(App.getInstance()).getCurrentNetworkTime();
//        ContactsSettingsSP.putLastObtainEditionDataTime(netWorkTime, businessAccount, employeeID);
        AccountManager.getAccount().updateEmployeeEditionData(JSON.toJSONString(employeeEditionData));
	 }
	    
	public static AEmployeeEditionData getEmployeeEditionData() {
		return AccountManager.getAccount().getEmployeeEditionData();
	}
	
	public static EmployeeEditionType getCurrentVersionType() {
		return AccountManager.getAccount().getCurrentVersionType();
	}

	public static boolean hasFreePackageOfCurrentVersion() {
		AEmployeeEditionData emplyeeEditionData = getEmployeeEditionData();
		if(emplyeeEditionData != null){
			return emplyeeEditionData.hasFreePackage;
		}else{
			return DEFAULT_HAS_FREE_PACKAGE;
		}	
	}
	
	public static boolean isTrialVersionOfCurrentVesion() {
		AEmployeeEditionData emplyeeEditionData = getEmployeeEditionData();
		if(emplyeeEditionData != null){
			return emplyeeEditionData.isTrialVersion;
		}else{
			return DEFAULT_IS_TRIAL_VERSION;
		}	
	}	
	
	public static boolean isVersionChanged(EmployeeEditionData  employeeEditionData){		
		return employeeEditionData != null 
				&& getCurrentVersionType().ordinal() != employeeEditionData.versionType
				|| (getCurrentVersionType().ordinal() == PRODUCT_TYPE_WORK
					&& employeeEditionData.versionType == PRODUCT_TYPE_WORK 
					&& isTrialVersionOfCurrentVesion() != isTrialVersion(employeeEditionData));		
	}
	
	public static boolean isWorkVersion(EmployeeEditionData  employeeEditionData){
		return employeeEditionData != null && employeeEditionData.versionType == PRODUCT_TYPE_WORK && employeeEditionData.trialVersionExpireTime <=  NetworkTime.getInstance(App.getInstance().getApplicationContext()).getCurrentNetworkTime();
	}
	
	public static boolean isTrialVersion(EmployeeEditionData  employeeEditionData){
		return employeeEditionData != null && employeeEditionData.versionType == PRODUCT_TYPE_WORK && employeeEditionData.trialVersionExpireTime > NetworkTime.getInstance(App.getInstance().getApplicationContext()).getCurrentNetworkTime();
	}
	
	public static boolean isMarketVersion(EmployeeEditionData  employeeEditionData){
		return employeeEditionData != null && employeeEditionData.versionType == PRODUCT_TYPE_MARKET;
	}	
	
	
	public static class AccountAttributeController {
		private final static String TAG = AccountAttributeController.class.getSimpleName();
		private final static String controller = "FHE/EM1AUA/UserCenterAPI";
		
		public static void getEmployeeEdition(final GetAccountEditionMode getAccountEditionMode){
			
			if (FSContextManager.getCurUserContext().getAccount().getEmployeeIntId() == 0) {
				return;
			}
			WebApiParameterList param = WebApiParameterList.createWith("M1", FSContextManager.getCurUserContext().getAccount().getEmployeeIntId());
	        WebApiUtils.postAsync(controller,"GetCurrentEmployeeEdition",param,new WebApiExecutionCallback<GetCurrentEmployeeEditionResult>() {
				
				@Override
				public TypeReference<WebApiResponse<GetCurrentEmployeeEditionResult>> getTypeReference() {			
	                return new TypeReference<WebApiResponse<GetCurrentEmployeeEditionResult>>() {
	                };
	            }
	            public Class<GetCurrentEmployeeEditionResult> getTypeReferenceFHE(){
	                return GetCurrentEmployeeEditionResult.class;
	            }            
	            
	 			@Override
	 			public void completed(Date time, GetCurrentEmployeeEditionResult response) {
	 				
	 				FCLog.i(TAG," GetCurrentEmployeeEdition completed");
	 				
	 				if(!isValidRespondDate(response)){
	 					FCLog.e(TAG," GetCurrentEmployeeEdition completed,response invalid");
						return;
	 				}				
					
					if(!EmployeeEditioinUtils.isVersionChanged(response.employeeEditionData)){
						FCLog.d(TAG," GetCurrentEmployeeEdition completed,Version has not Changed");
						return;
					}
					
					saveAccountInfos(response.employeeEditionData);				
					notifyVersionChanged(response.employeeEditionData,getAccountEditionMode);			   
	 			}
	            
	            @Override
	            public void failed(WebApiFailureType failureType, int httpStatusCode,String error, int errorCode,int enterpriseID
	                    ) {
	    			FCLog.e(TAG," GetCurrentEmployeeEdition failed，error="+error+"httpStatusCode="+httpStatusCode+"failureType=="+failureType.toString());
	            }
	 
			});
		}
		
		private static boolean isValidRespondDate(GetCurrentEmployeeEditionResult response){		
			return response != null && response.employeeEditionData != null ;
		}
		
		private static void saveAccountInfos(EmployeeEditionData  employeeEditionData){
            Account account = FSContextManager.getCurUserContext().getAccount();
            EmployeeEditioinUtils.saveEmployeeEditionData(account.getEnterpriseAccount(),
                    account.getEmployeeId(),
					employeeEditionData.versionType, employeeEditionData.trialVersionExpireTime,
					employeeEditionData.freePackage != null && !employeeEditionData.freePackage.isEmpty(), 
					EmployeeEditioinUtils.isTrialVersion(employeeEditionData));
		}
		
		private static void notifyVersionChanged(EmployeeEditionData employeeEditionData,GetAccountEditionMode getEditionMode){
			
			List<Object> lisObjects = SingletonObjectHolder.getInstance()
					.findObjects(AccountAttributeNotifyerImpl.class);
			AccountAttributeNotifyerImpl lis = null;
			for (Object ob : lisObjects) {
				lis = (AccountAttributeNotifyerImpl) ob;
			}
			
			if(lis == null){
				return;
			}	
			
			if(EmployeeEditioinUtils.isWorkVersion(employeeEditionData)){
				lis.notifyToWorkVersion(getEditionMode);
			}else if (EmployeeEditioinUtils.isTrialVersion(employeeEditionData)) {		
				lis.notifyToTrailVersion(getEditionMode);
			} else if(EmployeeEditioinUtils.isMarketVersion(employeeEditionData)) {
				lis.notifyToMarketingVersion(getEditionMode);
			}
		}
	}
	
}
