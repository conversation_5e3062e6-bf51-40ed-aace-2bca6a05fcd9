package com.facishare.fs.account_system;

import com.facishare.fs.i18n.I18NHelper;
import java.util.ArrayList;
import java.util.List;

import com.facishare.fs.BaseActivity;
import com.facishare.fs.account_system.xlogin.BaseNoIdentityActivity;
import com.facishare.fslib.R;
import com.facishare.fs.account_system.beans.IndustryVo;
import com.facishare.fs.account_system.beans.RoleVo;
import com.facishare.fs.utils_fs.task.ITaskProcessListener;
import com.facishare.fs.pluginapi.AccountManager;
import com.fxiaoke.fscommon_res.adapter.AbsListBaseAdapter;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ListView;
import android.widget.TextView;

/**
 * Created by yangwg on 2017/7/8.
 * 选择行业界面
 */

public class ExperienceChooseIndustryActivity extends BaseNoIdentityActivity {

    List<IndustryVo> mIndustries;
    boolean mHasCookie;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.experience_choose_industry_act);
        mIndustries = (List<IndustryVo>) getIntent().getSerializableExtra("IndustryVos");
        mHasCookie = getIntent().getBooleanExtra("hasCookie", false);
        initView();
    }
    public static void startExperienceIndustriesAct(Context context,ArrayList<IndustryVo> list, boolean hasCookie){
        Intent intent = new Intent(context, ExperienceChooseIndustryActivity.class);
        if (list != null) {
            intent.putExtra("IndustryVos", list);
            intent.putExtra("hasCookie", hasCookie);
        }
        context.startActivity(intent);
    }

    private void initView() {
        initTitleCommon();
        mCommonTitleView.setMiddleText(I18NHelper.getText("account.choose_industry.default.title")/* 选择行业 */);
        mCommonTitleView.addLeftAction(R.string.btn_title_back,
                new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        close();
                    }
                });
        ListView listView = (ListView) findViewById(R.id.experience_industry_list_view);
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                IndustryVo vo = (IndustryVo) parent.getItemAtPosition(position);
                if (vo != null) {
                    List<RoleVo> roleVos = vo.getRoleVoList();
                    if (roleVos == null || roleVos.size() == 1) {
                        String industryCode = vo.getIndustryCode();
                        String role = "";
                        if (roleVos != null) {
                            role = roleVos.get(0).getRoleCode();

                        }
                        //只有一个行业，且只有或者没有行业角色时，要直接登陆
                        ITaskProcessListener lis = new ITaskProcessListener() {
                            @Override
                            public void onStart() {
                                showBaseLoadingDialog();
                            }

                            @Override
                            public void onSuccess(Object object) {
                                hideDalog();
                                ExperienceLoginUtils.proceessLoginByInitialData(context,object,AccountManager.isLogin(context));
                            }

                            @Override
                            public void onFailed(String errorInfo, Object object) {
                                hideDalog();
                            }
                        };
                        ExperienceLoginUtils.regGetEAccount(context, industryCode, role, mHasCookie, lis);
                        return;
                    }
                    ArrayList<RoleVo> roleVoList = new ArrayList<RoleVo>();
                    roleVoList.addAll(roleVos);
                    ExperienceChooseRoleActivity.startExperienceChooseRoleAct(context, vo, roleVoList, mHasCookie);
                }
            }
        });
        IndustriesAdatper adatper = new IndustriesAdatper(this,mIndustries);
        listView.setAdapter(adatper);
    }

    private void hideDalog() {
        hideBaseLoadingDialog();
    }

    class IndustriesAdatper extends AbsListBaseAdapter<IndustryVo> {
        Context mCtx;

        public IndustriesAdatper(Context ctx, List<IndustryVo> datas) {
            this.mCtx = ctx;
            updateData(datas);
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            View view = convertView;
            ViewHolder holder;
            if (view == null) {
                view = LayoutInflater.from(mCtx).inflate(R.layout.experience_choose_industry_view_item, parent, false);
                holder = new ViewHolder();
                holder.divider = view.findViewById(R.id.industry_divider);
                holder.industryTitle = (TextView) view.findViewById(R.id.industry_name);
                view.setTag(holder);
            } else {
                holder = (ViewHolder) view.getTag();
            }

            refreshItem(holder, position);
            return view;
        }

        private void refreshItem(final ViewHolder holder, final int position) {
            final IndustryVo item = getItem(position);
            if (item != null) {
                holder.industryTitle.setText(item.getIndustryName());
            } else {
                holder.industryTitle.setText("");
            }

            if (getCount() - 1 == position) {
                holder.divider.setVisibility(View.GONE);
            } else {
                holder.divider.setVisibility(View.VISIBLE);
            }
        }

        private class ViewHolder {
            TextView industryTitle;
            View divider;
        }
    }


}
