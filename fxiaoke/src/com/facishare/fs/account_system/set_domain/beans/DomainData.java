/*
 * Copyright (C) 2023 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.account_system.set_domain.beans;

import java.io.Serializable;

import com.facishare.fs.biz_feed.newfeed.cmpt.Text;
import com.facishare.fs.i18n.I18NHelper;
import com.fxiaoke.fxdblib.beans.BatchOfChildrenItem;

import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public class DomainData implements Serializable {
    public String name;
    public String name18NKey;//目前仅预制域名会有多语key
    public String host;
//    public int currentIndex;//改由唯一id替换
    public boolean canEdit = true;// 是否可以被更改或删除，当前host属于预制的域名类型时，是不可以更改的，废弃新增的标识预制类型的字段isPreSetItem
    public boolean isSelected = false;//当前是否已被选中？
    public boolean isEnableUrl = false;//当前host是否经过检查是有效的url？
    public int sortIndex = 0;//排序值
    public String id;//唯一id值，目前用UUID值来区分
    @NonNull
    @Override
    public String toString() {
        return "Domain["
                + "" + name + ","
                + "canEdit=" + canEdit + ","
                + "isSelected=" + isSelected + ","
                + "isEnableUrl=" + isEnableUrl + ","
                + "" + host + ","
                + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int nameCode = 0;
        if (name != null) {
            nameCode = name.hashCode();
        }
        result = (int) (prime * result + nameCode);
        return result;
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj instanceof DomainData) {
            DomainData other = (DomainData) obj;
            if (other.id != null) {
                return TextUtils.equals(other.id, id);
            } else  if (other.name != null) {
                return TextUtils.equals(other.name, name);
            } else {
                if (other.host != null) {
                    return TextUtils.equals(other.host, host);
                }
            }
        }
        return super.equals(obj);
    }

    public String getName() {
        if (!TextUtils.isEmpty(name18NKey)) {
            return I18NHelper.getText(name18NKey, name);
        }
        return name;
    }
}
