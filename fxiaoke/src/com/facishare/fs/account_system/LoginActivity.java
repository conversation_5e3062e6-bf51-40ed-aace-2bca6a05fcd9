package com.facishare.fs.account_system;

import java.io.Serializable;
import java.util.List;

import com.facishare.fs.App;
import com.facishare.fs.BaseActivity;
import com.facishare.fs.LoginDataForPopupWindow;
import com.facishare.fs.account_system.beans.AccountStatisticsEvent;
import com.facishare.fs.account_system.beans.CloseActivityEvent;
import com.facishare.fs.account_system.beans.EnterpriseSummaryVo;
import com.facishare.fs.account_system.beans.QXAccountExperience;
import com.facishare.fs.account_system.datactr.LoginSwitchDomainHandler;
import com.facishare.fs.account_system.dialog.DebugSelectorDialog;
import com.facishare.fs.account_system.login.data.LoginMethodType;
import com.facishare.fs.account_system.login.ui.util.LoginViewUtils;
import com.facishare.fs.account_system.persistent.PersistentAccountDataBySP;
import com.facishare.fs.account_system.quick_login.AccountInfo;
import com.facishare.fs.account_system.quick_login.DebugSwitchAccountDialog;
import com.facishare.fs.account_system.quick_login.QuickDebugLogin;
import com.facishare.fs.account_system.set_domain.SetDomainActivity;
import com.facishare.fs.account_system.set_domain.SetDomainLogUtils;
import com.facishare.fs.account_system.set_domain.beans.DomainData;
import com.facishare.fs.account_system.set_domain.event.DomainDataChangeEvent;
import com.facishare.fs.account_system.set_domain.persistent.DomainDataProvider;
import com.facishare.fs.account_system.webpai.AccountSecurityWebApiUtils;
import com.facishare.fs.account_system.webpai.CloudLoginService;
import com.facishare.fs.account_system.xlogin.BaseLoginOperationActivity;
import com.facishare.fs.account_system.xlogin.NewLoginAct2;
import com.facishare.fs.account_system.xlogin.PrivacyTipViewLayout;
import com.facishare.fs.account_system.xlogin.SSOLoginProcessor;
import com.facishare.fs.beans.UserInitialDataJsonResult;
import com.facishare.fs.biz_session_msg.views.ScrollCtrlScrollView;
import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.common_utils.permission.GrantedExecuter;
import com.facishare.fs.dialogs.ComDialog;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.account.bean.LoginUserInfo;
import com.facishare.fs.privacy.PrivacyUtil;
import com.facishare.fs.privacy.WarnDialogUtil;
import com.facishare.fs.utils_fs.AppStateHelper;
import com.facishare.fs.utils_fs.AppStateListener;
import com.facishare.fs.utils_fs.FSPreference;
import com.facishare.fs.utils_fs.FsUtils;
import com.facishare.fs.utils_fs.NetUtils;
import com.facishare.fs.utils_fs.ToastUtils;
import com.facishare.fslib.R;
import com.fs.fsprobuf.AccountSystemProtobuf.GetUserInitialDataResult;
import com.fxiaoke.dataimpl.msg.MsgLogDefine;
import com.fxiaoke.fscommon.util.AccountInfoUtils;
import com.fxiaoke.fscommon.util.CommonDataContainer;
import com.fxiaoke.fscommon_res.permission.PermissionExecuter;
import com.fxiaoke.fscommon_res.utils.BrandColorRenderUtils;
import com.fxiaoke.fshttp.web.http.WebApiDownloadFileCallback;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.stat_engine.StatEngine;
import com.fxiaoke.stat_engine.events.session.UeEventSession;
import com.lidroid.xutils.net.FSNetObserver;

import android.Manifest;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.BitmapDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.View.OnFocusChangeListener;
import android.view.View.OnTouchListener;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import de.greenrobot.event.core.MainSubscriber;
import de.greenrobot.event.core.PublisherEvent;

/**
 * 企业账户登录（旧版）
 */
public class LoginActivity extends BaseLoginOperationActivity
        implements android.content.DialogInterface.OnKeyListener, OnFocusChangeListener,
        OnTouchListener {
    private Context context;
    private boolean verCode = false;// 标记验证码是否显示
    private String businessAccount = "";
    private String personalAccount = "";
    private String pwd = "";
    private String verificationcode = "";
    private EditText et_business_account, et_personal_account, et_password, et_verification_code;
    private ImageView iv_business_account_clear, iv_business_account_down, iv_personal_account_clear,
            iv_personal_account_down,iv_personal_pwd_clear;
    private Button bnLogin;

    private ImageView  im_verification_code;
    private TextView tv_reset_pwd;
    private View tv_veri_tb_layout;

    private TextView textView_line_code;
    public static final String resultEnp = "resultEnp";
    public static final String resultPerson = "resultPerson";
    public static final String resultPwd = "resultPwd";

    private MainSubscriber<CloseActivityEvent> mCloseActivityEvent;
    private ScrollCtrlScrollView ScrollView_login;

    public int typeid = 0;// 收到短信邀请注册=4、有纷享帐号注册=5
    private int typeHeight = 0;

    private boolean isShowVer = false;

    Button debugBtn = null;

    //  下拉显示常用帐号的popupwindow，分别显示企业帐号和个人帐号
    LoginPopupWindow mBusinessPopupWindow;
    LoginPopupWindow mPersonalPopupWindow;
    //  记录旧版登录下拉框中所以的数据，并随时保持数据点击和删除事件的同步的管理数据类
    LoginDataForPopupWindow mLoginPopupData;
    boolean mNeedClearPassword = false;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.login_new);
        if(savedInstanceState == null){//恢复页面的时候不能去做清除cookie，重置为登出状态等操作
            LoginUitls.resetToLogoutState();
        }
        disableSwipeBack();
        mCloseActivityEvent = new MainSubscriber<CloseActivityEvent>() {
            @Override
            public void onEventMainThread(final CloseActivityEvent event) {
                FCLog.d("CloseActivityEvent","LoginActivity event: "+event);
                if (event != null && LoginActivity.class.getSimpleName().equals(event.mNeedCloseName)) {
                    if (!LoginActivity.this.isFinishing()) {
                        finish();
                    }
                }
            }
        };
        mCloseActivityEvent.register();
        registerDomainDataChangeEvent();

        context = this;
        initView();

        init();
        setInputData(getIntent());
        showInputEx();
        debugBtn = (Button) findViewById(R.id.button_debug);
        if (FsUtils.isInformal()) {
            debugBtn.setVisibility(View.VISIBLE);
            String string = LoginUitls.getSwitchHostBaseRequestUrl(context);// 此处只需要获取切换环境保存的地址，没有的话，就用初始配置的url，比如api.fqixin或者ceshi112
            if (TextUtils.isEmpty(string)) {
                string = WebApiUtils.requestUrl;
            }
            debugBtn.setText(string);
            debugBtn.setOnClickListener(new OnClickListener() {

                @Override
                public void onClick(View v) {
//                    showDebug();
//                    startSwitchDomain(v);
                    showDomainList();
                }
            });
            debugBtn.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    showQuickLoginDialog();
                    return false;
                }
            });
        }
        if (savedInstanceState != null && savedInstanceState.getBoolean("hasInitLoginSwitchDomainHandler")) {
            initLoginSwitchDomainHandler(true);
        }
        // 找回密码
        tv_reset_pwd.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                //旧版 忘记密码
                StatEngine.tick("ms_login_3");
                StatEngine.tick(AccountStatisticsEvent.MS_USER_EA_LOGIN_FORGET_PW);
                startResetPwd();
            }
        });
        tv_reset_pwd.setVisibility(View.GONE);//6.2起的新需求 不再显示忘记密码入口
        // 登录
        bnLogin.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                AccountStatisticsEvent.loginTick(AccountStatisticsEvent.ENTLOGIN_LOGIN);
                processClickLoginBtn();
            }
        });

        FSPreference.getInstance().putBoolean(FSPreference.PrefID.PREF_RESTART_APP, false);
        if(getIntent().getBooleanExtra(QXAccountExperience.QX_Login_View, false)) {
            long intentTime = getIntent().getLongExtra(QXAccountExperience.INTENT_KEY_SUFFIX_VALUE,0);
            String cachedKey = QXAccountExperience.CACHE_UESESSION_KEY_PREFIX+intentTime;
            Object ueEventSession = CommonDataContainer.getInstance().getSavedData(cachedKey);
            if(ueEventSession instanceof UeEventSession ){
                ((UeEventSession)ueEventSession).endTick();
            }
            CommonDataContainer.getInstance().removeSavedData(cachedKey);
        }
        initPublicKey();
        checkToShowKickOutErrorDialog();
        LoginViewUtils.startNetWorkSecurityCheck(this);
        mNetObserver = LoginViewUtils.createFSNetObserverForSecurityCheck(mNetObserverAtAppBackground);
        mAppStateListener = LoginViewUtils.createAppStateListenerForSecurityCheck(mAppStateListenerRunnable);
    }
    FSNetObserver mNetObserver;
    AppStateListener mAppStateListener;
    boolean isLastNetChangedBackground = false;
    //我们的应用在后台时，收到的网络变化监听（需要在应用回到前台时提示用户信息）
    Runnable mNetObserverAtAppBackground = new Runnable() {
        @Override
        public void run() {
            isLastNetChangedBackground = true;
        }
    };
    //我们的应用前后台切换的监听，目前用来补偿提示网络切换的变化
    Runnable mAppStateListenerRunnable = new Runnable() {
        @Override
        public void run() {
            if (AppStateHelper.isAppRunTop()) {
                if (isLastNetChangedBackground) {
                    LoginViewUtils.showSecurityWarringSwitchNet();
                }
                isLastNetChangedBackground = false;
            }

        }
    };
    private void processClickLoginBtn() {
        if (emptyValidate()) {
            hideInput();
            if(!NetUtils.checkNet(context)) {
                ToastUtils.netErrShow();
                FCLog.i(MsgLogDefine.debug_account_security, "FHELogin net invalid" );
                return;
            }
            showDialog(BaseActivity.DIALOG_WAITING_BASE);
            reqEnterpriseAccountLogin();
        }
    }
    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putBoolean("hasInitLoginSwitchDomainHandler", mLoginSwitchDomainHandler != null);
    }
    @Override
    protected void initTitleEx() {
        super.initTitleEx();
        mCommonTitleView.setBackgroundResource(R.drawable.light_actionbar_top_bg);
        mCommonTitleView.setMiddleText(DomainDataProvider.getInstance().getSelectedDomainItem(this).getName());
        mCommonTitleView.getCenterTxtView().setTextSize(16);//据产品更改 【移动端默认登录地址体验优化】https://www.tapd.cn/********/prong/stories/view/11********001313926
//        mCommonTitleView.setMiddleText(I18NHelper.getText("account.login_by_account.default.title")/* 企业帐号登录 */);
        mCommonTitleView.setTextColor(Color.parseColor("#3c394b"));
        mCommonTitleView.addLeftAction(I18NHelper.getText("account.login_by_account.default.left_title")/* 返回  */, R.drawable.light_actionbar_top_bg, 0, new
                OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        AccountStatisticsEvent.loginTick(AccountStatisticsEvent.ENTLOGIN_BACK);
                        Intent intent = getIntent();
                        int currentItem = 0;
                        if (intent != null) {// 获取关闭前viewpager所在的位置，如果没有的话，再返回时，更改为第一页
                            currentItem = intent.getIntExtra("savedCurrentItem", 0);
                        }
                        final int resumeShowItem = currentItem;
                        backToGuide(resumeShowItem);
                    }
                });
        mCommonTitleView.setGroupTextColor(mCommonTitleView.getLeftLayout(), Color.parseColor("#f09835"));

        mCommonTitleView.setBackgroundColor(0xfff2f3f5);
    }

    // 对用户帐户,个人帐号、密码进行空值校验
    private boolean emptyValidate() {
        businessAccount = et_business_account.getText().toString().trim();
        personalAccount = et_personal_account.getText().toString().trim();
        pwd = et_password.getText().toString().trim();
        inputPwd = et_password.getText().toString().trim();
        if (/*isRsa && */pwd.length() > 0) {
            if (pwd != null && pwd.length() > 20) {
                ToastUtils.showToast(I18NHelper.getText("xt.login_logs_list_item.text.wrong_password")/* 密码错误 */);
                et_password.requestFocus();
                return false;
            }
            pwd = LoginUitls.getEncodePassword(this,pwd,mPublicKey);
        }

        if (businessAccount.equals("")) {
            ToastUtils.showToast(I18NHelper.getText("account.login_by_account.guide.ea_must")/* 企业帐号是必填项 */);
            et_business_account.requestFocus();
            return false;
        }

        if (personalAccount.equals("")) {
            ToastUtils.showToast(I18NHelper.getText("account.login_by_account.guide.account_must")/* 个人帐号是必填项 */);
            et_personal_account.requestFocus();
            return false;
        }

        if (pwd.equals("")) {
            ToastUtils.showToast(I18NHelper.getText("account.login_by_account.guide.pwd_must")/* 个人密码是必填项 */);
            et_password.requestFocus();
            return false;
        }

        if (verCode) {
            verificationcode = et_verification_code.getText().toString().trim();
            if (verificationcode.equals("")) {
                ToastUtils.showToast(I18NHelper.getText("account.login_by_account.guide.auth_code_must")/* 验证码是必填项 */);
                return false;
            }
        }
        if (mPrivacyTipViewLayout != null && !mPrivacyTipViewLayout.isAgreePrivacy()) {
            showPrivacyTipDialog();
            hideInput();
            return false;
        }
        return true;
    }

    /**
     * 显示图像验证码控件
     */
    private void getCodeImgByNewMethod() {
        WebApiDownloadFileCallback callback = new WebApiDownloadFileCallback() {
            @Override
            public void completed(byte[] content, String type) {
                dismiss();
                if (content != null) {
                    BitmapFactory.Options options = new BitmapFactory.Options();
                    options.inPreferredConfig = Bitmap.Config.RGB_565;
                    Bitmap bm = BitmapFactory.decodeByteArray(content, 0, content.length, options);
                    im_verification_code.setBackgroundDrawable(new BitmapDrawable(bm));
                } else {
                    ToastUtils.showToast(I18NHelper.getText("account.login_by_account.result.get_code_failed")/* 获取验证码失败，请核实帐号信息是否正确! */);
                }
            }
        };
        AccountSecurityWebApiUtils.reqFHEGetImageCode(businessAccount, personalAccount,callback);
    }

    private void init() {
        List<CommonlyUsedAccountData> list = mLoginPopupData.getPersionList();

        // 回填企业帐号和用户名(只回填正式)
        if (list != null && list.size() != 0) {
            CommonlyUsedAccountData data = list.get(0);
            et_business_account.setText(mLoginPopupData.getCurrentBusiness());// 企业帐号
            et_personal_account.setText(data.getPersonalAaccount());// 个人帐号
            mNeedClearPassword = true;
        }
        if (WebApiUtils.Debug) {
            et_business_account.setText(WebApiUtils.devtestBusinessAccount);
            et_business_account.setEnabled(false);
        }
        if (TextUtils.isEmpty(et_business_account.getText().toString())) {
            et_business_account.requestFocus();
        } else {
            if (TextUtils.isEmpty(et_personal_account.getText().toString())) {
                et_personal_account.requestFocus();
            } else {
                et_password.requestFocus();
            }
        }
    }

    public void initView() {
        initTitleEx();
        //编辑框删除按钮
        iv_business_account_clear = (ImageView) findViewById(R.id.iv_business_account_clear);
        iv_business_account_clear.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                mBusinessPopupWindow.setSelectItem(-1);
                et_business_account.setText("");
            }
        });
        iv_personal_account_clear = (ImageView) findViewById(R.id.iv_personal_account_clear);
        iv_personal_account_clear.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                mPersonalPopupWindow.setSelectItem(-1);
                et_personal_account.setText("");
            }
        });
        // 企业团队帐号
        et_business_account = (EditText) findViewById(R.id.et_business_account);
        et_business_account.setOnFocusChangeListener(this);
        et_business_account.setOnTouchListener(this);
        et_business_account.addTextChangedListener(textWatcherb);
        //企业团队下拉选项
        iv_business_account_down = (ImageView) findViewById(R.id.iv_business_account_down);
        iv_business_account_down.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                setPopupMaxHeight();
                //是否已经显示下拉框
                Object flag = iv_business_account_down.getTag();
                if (flag != null && (boolean) flag) {
                    //flag为true,表示显示了下拉框，此时改为隐藏
                    iv_business_account_down.setImageResource(R.drawable.arrows_down);
                    iv_business_account_down.setTag(false);
                    mBusinessPopupWindow.dismiss();
                } else {
                    //false,表示没有显示下拉框，此时改为显示
                    iv_business_account_down.setImageResource(R.drawable.arrows_up);
                    iv_business_account_down.setTag(true);
                    hideInput();
                    mBusinessPopupWindow.show(findViewById(R.id.iv_business_account_line));
                }
            }
        });
//        TextView passwordDes = findViewById(R.id.password_des);
//        if (I18NHelper.getText("xt.login_new.text.password").equals("密        码")) {//中文：密        码
//            passwordDes.setText("密\u3000\u3000码");
//        }
//        if (I18NHelper.getText("xt.login_new.text.password").equals("密        碼")) {//繁体：密        碼
//            passwordDes.setText("密\u3000\u3000碼");
//        }
        // 用户名
        et_personal_account = (EditText) findViewById(R.id.et_personal_account);
        et_personal_account.setOnFocusChangeListener(this);
        et_personal_account.setOnTouchListener(this);
        et_personal_account.addTextChangedListener(textWatcherb);
        //用户名下拉框
        iv_personal_account_down = (ImageView) findViewById(R.id.iv_personal_account_down);
        iv_personal_account_down.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                setPopupMaxHeight();
                //是否已经显示下拉框，
                Object flag = iv_personal_account_down.getTag();
                if (flag != null && (boolean) flag) {
                    //flag为true,表示显示了下拉框，此时改为隐藏
                    iv_personal_account_down.setImageResource(R.drawable.arrows_down);
                    iv_personal_account_down.setTag(false);
                    mPersonalPopupWindow.dismiss();
                } else {
                    //false,表示没有显示下拉框，此时改为显示
                    iv_personal_account_down.setImageResource(R.drawable.arrows_up);
                    iv_personal_account_down.setTag(true);
                    hideInput();
                    mPersonalPopupWindow.show(findViewById(R.id.iv_personal_account_line));
                }

            }
        });
        // 密码
        et_password = (EditText) findViewById(R.id.et_password);
        et_password.setTypeface(Typeface.DEFAULT);
        et_password.addTextChangedListener(textWatcher);
        et_password.setOnFocusChangeListener(this);
        et_password.setOnTouchListener(this);
        //有输入密码时，才禁止使用截屏等有安全风险的操作
        FsUtils.forbidPasswordViewSecure(this, et_password);
        // 验证码
        et_verification_code = (EditText) findViewById(R.id.et_verification_code);
        et_verification_code.setOnFocusChangeListener(this);
        im_verification_code = (ImageView) findViewById(R.id.image_verification_code);
        im_verification_code.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                getCodeImgByNewMethod();
            }
        });

        bnLogin = (Button) findViewById(R.id.bn_login);
        BrandColorRenderUtils.changeViewBackgroundDrawable(bnLogin, bnLogin.getBackground(), "#F4A243");
        // 忘记帐号或密码
        tv_reset_pwd = (TextView) findViewById(R.id.tv_reset_pwd);
        // 验证码
        tv_veri_tb_layout = findViewById(R.id.tv_veri_tb_layout);

        textView_line_code = (TextView) findViewById(R.id.textView_line_code);

        iv_personal_pwd_clear = (ImageView) findViewById(R.id.iv_personal_pwd_clear);
        iv_personal_pwd_clear.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                // TODO Auto-generated method stub
                et_password.setText("");
            }
        });
        ScrollView_login = (ScrollCtrlScrollView) findViewById(R.id.ScrollView_login);
        //初始化下拉视图数据
        initPopupWindow();
        /* 改由和ios统一的方式，未登录时的第一个页面里，显示增加关于的入口
        View v_outLayout = findViewById(R.id.rl_login_new);
        v_outLayout.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                showHideActivity();
            }
        });*/

        initPrivacyView();
    }
    private PrivacyTipViewLayout mPrivacyTipViewLayout;
    private void initPrivacyView(){
        mPrivacyTipViewLayout = findViewById(R.id.privacy_layout);
//      TextView  privacyText = findViewById(R.id.privacy_text);
//
//        PrivacyUtil.setLoginPrivacyText(privacyText,LoginActivity.this);

        //        privacyText.setText(PrivacyUtil.getLoginPrivacyText());

    }

    private void showPrivacyTipDialog() {
//        ToastUtils.showToast("请勾选同意隐私协议");
        WarnDialogUtil.showLoginAgreeDialog(this, new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPrivacyTipViewLayout != null) {
                    mPrivacyTipViewLayout.setAgreePrivacy(true);
                }
                processClickLoginBtn();
            }
        }, null);
//        mPrivacyTipViewLayout.setAgreePrivacy(true);//对话框同意后要设置勾选
//        processClickLoginBtn();
    }

    //给下拉框设置最大高度，需在界面绘制完成后执行，只执行一次
    boolean hasSetPopupMaxHeight = false;

    private void setPopupMaxHeight() {
        if (hasSetPopupMaxHeight) {
            return;
        }
        hasSetPopupMaxHeight = true;
        int[] top = new int[2];
        et_password.getLocationOnScreen(top);
        int[] bottom = new int[2];
        tv_reset_pwd.getLocationOnScreen(bottom);//获取在整个屏幕内的绝对坐标
        mBusinessPopupWindow.setMaxHeight(bottom[1] - top[1]);
        mPersonalPopupWindow.setMaxHeight(bottom[1] - top[1]);
    }

    private void initPopupWindow() {
        mBusinessPopupWindow = new LoginPopupWindow(this);
        mPersonalPopupWindow = new LoginPopupWindow(this);
        mLoginPopupData = new LoginDataForPopupWindow();
        mBusinessPopupWindow.initData(mLoginPopupData.getBusinessList(), new LoginPopupWindow.OnItemClickLis() {
            @Override
            public void onItemClick(int pos) {
                //先做判断，防止空指针和越界
                if (mLoginPopupData.getBusinessList().size() > pos) {
                    //获取当前被点击的企业帐号，并同步到数据控制类中和下方个人帐号中
                    String currentBusiness = mLoginPopupData.getBusinessList().get(pos);
                    et_business_account.setText(currentBusiness);
                    et_business_account.setSelection(currentBusiness.length());
                    mLoginPopupData.notifyData(currentBusiness);
                    mPersonalPopupWindow.updata(mLoginPopupData.getPersionNameList());
                    //将企业帐号和默认的个人帐号回显到文本框中
                    CommonlyUsedAccountData user = mLoginPopupData.getPersionList().get(0);
                    et_personal_account.setText(user.getPersonalAaccount());
//                    et_password.setText(user.getPasswordString());//不让回填密码，但先保存密码
                    mBusinessPopupWindow.dismiss();
                    mNeedClearPassword = true;
                    mBusinessPopupWindow.setSelectItem(pos);
                    updataPopupWindowData();
                }
            }

            @Override
            public void onItemDeleteClick(final int pos) {
                StatEngine.tick(AccountStatisticsEvent.MS_USER_EA_LOGIN_REMOVE_ACCOUNT);
                ComDialog.showConfirmDialog(LoginActivity.this, I18NHelper.getText("account.login_by_account.guide.delete_log_confirm")/* 确定删除此条帐号记录吗？
删除后不影响此帐号正常登录使用 */, true, new
                        OnClickListener
                                () {
                            @Override
                            public void onClick(View v) {
                                //登录-下拉框，X掉帐号二次确认-确定删除此条帐号记录？ 确定的次数
                                StatEngine.tick("ms_login_10");
                                StatEngine.tick(AccountStatisticsEvent.MS_USER_EA_LOGIN_REMOVE_ACCOUNT_CONFIRM);
                                //先做判断，防止空指针和越界
                                if (mLoginPopupData.getBusinessList().size() > pos) {
                                    mLoginPopupData.removeBusiness(pos);
                                    updataPopupWindowData();
                                }
                                if (pos == mBusinessPopupWindow.getSelectItem()) {
                                    //如果当前选中的条目被删除了，那么默认显示列表中的第一条
                                    List<String> businessList = mLoginPopupData.getBusinessList();
                                    if (businessList.size() != 0) {
                                        //如果企业列表还剩其他企业，那么显示其他企业
                                        String currentBusiness = businessList.get(0);
                                        mLoginPopupData.setCurrentBusiness(currentBusiness);
                                        et_business_account.setText(currentBusiness);
                                        CommonlyUsedAccountData data = mLoginPopupData.getPersionList().get(0);
                                        et_personal_account.setText(data.getPersonalAaccount());
//                                        et_password.setText(data.getPasswordString());//不让回填密码，但先保存密码
                                    } else {
                                        //如果企业列表没有企业，则清空数据
                                        mLoginPopupData.setCurrentBusiness("");
                                        et_business_account.setText("");
                                        et_personal_account.setText("");
                                        et_password.setText("");
                                    }
                                }
                                mBusinessPopupWindow.dismiss();
                            }
                        });
            }
        });
        mBusinessPopupWindow.setOnDismiss(new LoginPopupWindow.OnDismissLis() {
            @Override
            public void onDismiss() {
                iv_business_account_down.setImageResource(R.drawable.arrows_down);
                iv_business_account_down.setTag(false);
            }
        });
        //选中最后一次登录的企业，即当前企业
        mBusinessPopupWindow
                .setSelectItem(mLoginPopupData.getBusinessList().indexOf(mLoginPopupData.getCurrentBusiness()));

        mPersonalPopupWindow.initData(mLoginPopupData.getPersionNameList(), new LoginPopupWindow
                .OnItemClickLis() {
            @Override
            public void onItemClick(int pos) {
                //先做判断，防止空指针和越界
                if (mLoginPopupData.getPersionList().size() > pos) {
                    CommonlyUsedAccountData user = mLoginPopupData.getPersionList().get(pos);
                    et_business_account.setText(user.getBusinessAaccount());
                    et_personal_account.setText(user.getPersonalAaccount());
                    et_personal_account.setSelection(user.getPersonalAaccount().length());
//                    et_password.setText(user.getPasswordString());//不让回填密码，但先保存密码
                    mPersonalPopupWindow.dismiss();
                    mNeedClearPassword = true;
                    mPersonalPopupWindow.setSelectItem(pos);
                }
            }

            @Override
            public void onItemDeleteClick(final int pos) {
                ComDialog.showConfirmDialog(LoginActivity.this, I18NHelper.getText("account.login_by_account.guide.delete_log_confirm")/* 确定删除此条帐号记录吗？
删除后不影响此帐号正常登录使用 */, true, new
                        OnClickListener
                                () {
                            @Override
                            public void onClick(View v) {
                                //登录-下拉框，X掉帐号二次确认-确定删除此条帐号记录？ 确定的次数
                                StatEngine.tick("ms_login_10");
                                //先记录当前被删除帐号所属的企业
                                String currentBusiness = mLoginPopupData.getCurrentBusiness();
                                //先做判断，防止空指针和越界
                                if (mLoginPopupData.getPersionList().size() > pos) {
                                    //先记录帐号个数，再进行删除
                                    int size = mLoginPopupData.getPersionList().size();
                                    mLoginPopupData.removePersion(pos);
                                    updataPopupWindowData();
                                    if (pos == mPersonalPopupWindow.getSelectItem()) {
                                        //如果删除的是选中的条目，那么需要重新设置编辑框中的数据
                                        if (size > 1) {
                                            //如果删除后，改企业还有其他帐号，那么依然显示此企业下的帐号
                                            mLoginPopupData.notifyData(currentBusiness);
                                        }
                                        List<CommonlyUsedAccountData> data = mLoginPopupData.getPersionList();
                                        if (data != null && data.size() != 0) {
                                            //如果被删除帐号的企业下还有其他帐号，那么显示其他帐号
                                            CommonlyUsedAccountData currentData = data.get(0);
                                            et_business_account.setText(currentData.getBusinessAaccount());
                                            et_personal_account.setText(currentData.getPersonalAaccount());
//                                            et_password.setText(currentData.getPasswordString());//不让回填密码，但先保存密码
                                        } else {
                                            //如果企业列表没有企业，则清空数据
                                            mLoginPopupData.setCurrentBusiness("");
                                            et_business_account.setText("");
                                            et_personal_account.setText("");
                                            et_password.setText("");
                                        }
                                    }
                                }
                                mPersonalPopupWindow.dismiss();
                            }
                        });

            }
        });
        mPersonalPopupWindow.setOnDismiss(new LoginPopupWindow.OnDismissLis() {
            @Override
            public void onDismiss() {
                iv_personal_account_down.setImageResource(R.drawable.arrows_down);
                iv_personal_account_down.setTag(false);
            }
        });

        updataPopupWindowData();
    }

    private void updataPopupWindowData() {
        //没有数据时不显示下拉
        if (mLoginPopupData.getBusinessList().size() == 0) {
            iv_business_account_down.setVisibility(View.GONE);
            iv_personal_account_down.setVisibility(View.GONE);
        } else {
            iv_business_account_down.setVisibility(View.VISIBLE);
            iv_personal_account_down.setVisibility(View.VISIBLE);
        }
        mBusinessPopupWindow.updata(mLoginPopupData.getBusinessList());
        mPersonalPopupWindow.updata(mLoginPopupData.getPersionNameList());
    }

    Handler handler = new Handler();

    protected void backToGuide(final int resumeShowItem) {
        hideInput();
        handler.postDelayed(new Runnable() {

            @Override
            public void run() {
                Intent it = new Intent(LoginActivity.this, ShowNewGuideMapActivity.class);
                it.putExtra("savedCurrentItem", resumeShowItem);
                it.putExtra("isShowVer", isShowVer);
                setResult(1, it);
                finish();
            }
        }, 50);

    }

    @Override
    protected boolean shouldFinishOnExitAction() {
        return false;
    }


    @Override
    protected boolean isCanStartPasslockActivity() {
        return false;
    }

    TextWatcher textWatcherb = new TextWatcher() {

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // TODO Auto-generated method stub
            String keyString = s.toString();

            if (keyString != null && keyString.trim().length() > 0) {
                setScrollViewEx();
            }
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            // TODO Auto-generated method stub

        }

        @Override
        public void afterTextChanged(Editable s) {
            // 安全考虑，在编辑个人帐号时，清楚掉回填的密码
            if (mNeedClearPassword && et_personal_account.hasFocus()) {
                mNeedClearPassword = false;
                et_password.setText("");
            }
            if (!TextUtils.isEmpty(s.toString())) {
                if (et_business_account.hasFocus()) {
                    iv_business_account_clear.setVisibility(View.VISIBLE);
                } else if (et_personal_account.hasFocus()) {
                    iv_personal_account_clear.setVisibility(View.VISIBLE);
                }
            } else {
                iv_business_account_clear.setVisibility(View.GONE);
                iv_personal_account_clear.setVisibility(View.GONE);
            }
            checkLoginButton();
        }
    };

    TextWatcher textWatcher = new TextWatcher() {

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // TODO Auto-generated method stub
            String keyString = et_password.getText().toString();

            if (keyString != null && keyString.trim().length() > 0) {
                iv_personal_pwd_clear.setVisibility(View.VISIBLE);
            } else {
                iv_personal_pwd_clear.setVisibility(View.GONE);
            }
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            // TODO Auto-generated method stub

        }

        @Override
        public void afterTextChanged(Editable s) {
            checkLoginButton();
        }
    };

    /**
     * 如果必填项没填，则登录按钮不可点
     */
    private void checkLoginButton() {
        if (TextUtils.isEmpty(et_business_account.getText().toString().trim()) ||
                TextUtils.isEmpty(et_personal_account.getText().toString().trim()) ||
                TextUtils.isEmpty(et_password.getText().toString().trim())) {
            bnLogin.setEnabled(false);
        } else {
            bnLogin.setEnabled(true);
        }
    }
    UeEventSession mLoginUESession;
    /**
     * 登录校验新接口
     */
    protected void reqEnterpriseAccountLogin() {
        reqEnterpriseAccountLoginWithFullJson();
    }

    @Override
    protected void showImageCodeView() {
        showVer();
        getCodeImgByNewMethod();
    }

    private void reqEnterpriseAccountLoginWithFullJson() {
        mLoginUESession = QXAccountExperience.startTickSession(QXAccountExperience.QX_Login_Submit,
                QXAccountExperience.TYPE_LOGIN_ENTERPRISE_ACCOUNT);
        isShowedResultToast = false;
        FCLog.i(MsgLogDefine.debug_account_security, "reqEnterpriseAccountLoginWithFullJson with input ea: "+businessAccount
                +" ,personalAccount:"+personalAccount+" ,imgCode: "+verificationcode);
        EnterpriseSummaryVo en = new EnterpriseSummaryVo();
        en.setEnterpriseAccount(businessAccount);
        en.setLoginMethodType(LoginMethodType.EA_ACCOUNT_LOGIN.getType());//标记企业帐号登录方式
        CloudLoginService.reqEnterpriseAccountCloudLogin(businessAccount, personalAccount, mPublicKey, pwd, verificationcode,
                getEnterpriseCloudCallBack(en));

    }




    @Override
    protected void processData(GetUserInitialDataResult response){

        String unencryptedPwd =et_password.getText().toString().trim();//不让回填密码，但先保存密码
        boolean isSavePwd = /*image_switch.getChecked()*/true;

        LoginUserInfo userInfo = LoginUitls.translateUserInitDataToLoginUserInfo(response
                .getInitialData());
        typeid = LoginUitls.processLoginCompleted4OldLogin(context, businessAccount, personalAccount,
                unencryptedPwd, pwd, isSavePwd,
                userInfo,
                typeid);
        ShowNewGuideMapActivity.closeSelf();
    }

    // 显示验证码
    private void showVer() {
        isShowVer = true;
        verCode = true;
        tv_veri_tb_layout.setVisibility(View.VISIBLE);
        textView_line_code.setVisibility(View.VISIBLE);
    }


    @Override
    public void finish() {
        dismiss();
        super.finish();
    }

    @Override
    public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
            dismiss();
        }

        return false;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode,resultCode,data);
        if (requestCode == requestCodeLogin) {//忘记密码页面返回
            setInputData(data);
        }
    }

    public static void closeSelf() {
        PublisherEvent.post(new CloseActivityEvent(LoginActivity.class.getSimpleName()));
    }

    @Override
    protected void onDestroy() {
        LoginViewUtils.removeSecurityCheckFSNetObserver(mNetObserver);
        LoginViewUtils.removeSecurityCheckAppStateListener(mAppStateListener);
        if (mCloseActivityEvent != null) {
            mCloseActivityEvent.unregister();
        }
        mCloseActivityEvent = null;
        unRegisterDomainDataChangeEvent();
        if (mQuickLoginEvent != null) {
            mQuickLoginEvent.unregister();
        }
        mQuickLoginEvent = null;
        if (mLoginSwitchDomainHandler != null) {
            mLoginSwitchDomainHandler.release();
        }
        super.onDestroy();
    }

    private void setScrollView() {
        if (ScrollView_login != null) {
            ScrollView_login.smoothScrollTo(0, FSScreen.dip2px(typeHeight));
        }
    }

    private void setScrollViewEx() {
        typeHeight = 20;
        if (ScrollView_login != null) {
            ScrollView_login.smoothScrollTo(0, FSScreen.dip2px(typeHeight));
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        hideInput();
        return super.onTouchEvent(event);
    }

    @Override
    protected void onStart() {
        super.onStart();
        HostInterfaceManager.getHostInterface().clearPushNotify();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setInputData(intent);
        PersistentAccountDataBySP accountSp = new PersistentAccountDataBySP(App.getInstance());
        boolean isReset = accountSp.isResetVercodeState();
        if (isReset) {
            isShowVer = false;
            tv_veri_tb_layout.setVisibility(View.GONE);
            textView_line_code.setVisibility(View.GONE);
        }
    }
	
   /* //连击七次空白处进入隐藏网络诊断界面
    private long mLastKickTime;
    private int mContinueKickCount;
    private void showHideActivity(){
        if (mLastKickTime == 0) {
            mLastKickTime = System.currentTimeMillis();
            mContinueKickCount = 0;
            mContinueKickCount++;
        } else {
            long now = System.currentTimeMillis();
            if ((now - mLastKickTime) > 1500) {
                mLastKickTime = 0;
                mContinueKickCount = 0;
            } else {
                mLastKickTime = now;
                mContinueKickCount++;
                if (mContinueKickCount < 7) {
                    return;
                }
                mLastKickTime = 0;
                mContinueKickCount = 0;
                //进入隐藏网络诊断界面
                AboutActivity.startWithUnknownUser(this);
            }
        }
    }*/

    // 重置输入框数据,用于从找回密码跳转携带数据
    private void setInputData(Intent data) {
        if (data != null) {
            String sEnterprice = data.getStringExtra(resultEnp);
            String sPersonAccount = data.getStringExtra(resultPerson);
            if (!TextUtils.isEmpty(sEnterprice)) {
                et_business_account.setText(sEnterprice);
                et_personal_account.setText(sPersonAccount);
            }
        }
        //回填完后判定按钮是否可用
        checkLoginButton();
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        // TODO Auto-generated method stub
        if (hasFocus) {
            int i = v.getId();
            if (i == R.id.et_business_account) {
                if (TextUtils.isEmpty(et_business_account.getText().toString())) {
                    iv_business_account_clear.setVisibility(View.GONE);
                } else {
                    iv_business_account_clear.setVisibility(View.VISIBLE);
                }
                setScrollView();

            } else if (i == R.id.et_personal_account) {
                if (TextUtils.isEmpty(et_personal_account.getText().toString())) {
                    iv_personal_account_clear.setVisibility(View.GONE);
                } else {
                    iv_personal_account_clear.setVisibility(View.VISIBLE);
                }
                setScrollView();

            } else if (i == R.id.et_verification_code || i == R.id.et_password) {
                ScrollView_login.smoothScrollTo(0, App.intScreenHeight / 4);
                if (TextUtils.isEmpty(et_password.getText().toString())) {
                    iv_personal_pwd_clear.setVisibility(View.GONE);
                } else {
                    iv_personal_pwd_clear.setVisibility(View.VISIBLE);
                }
            }
        } else {
            int i = v.getId();
            if (i == R.id.et_business_account) {
                iv_business_account_clear.setVisibility(View.GONE);
            } else if (i == R.id.et_personal_account) {
                iv_personal_account_clear.setVisibility(View.GONE);
            } else if (i == R.id.et_password) {
                iv_personal_pwd_clear.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        int i = v.getId();
        if (i == R.id.et_business_account) {
            et_business_account.requestFocus();
            setScrollViewEx();


        } else if (i == R.id.et_personal_account) {
            et_personal_account.requestFocus();
            setScrollViewEx();

        } else if (i == R.id.et_password) {
            ScrollView_login.smoothScrollTo(0, App.intScreenHeight / 2);

        }
        return false;
    }

    // 显示键盘
    private void showInputEx() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                InputMethodManager imm =
                        (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.toggleSoftInput(0, InputMethodManager.HIDE_NOT_ALWAYS);
            }
        }, 20);

        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                showRemindText();
            }
        }, 30);
    }

    private void showRemindText() {
        isShowVer = getIntent().getBooleanExtra("isShowVer", false);

        if (isShowVer) {
            showImageCodeView();
        }

        TextView showTextView = (TextView) findViewById(R.id.textView_show_ts);
        TextView showTextView2 = (TextView) findViewById(R.id.textView_show_ts2);

        if (App.intScreenWidth >= 720) {
            showTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX,
                    (int) context.getResources().getDimension(R.dimen.dialog_textSize_ex));
            showTextView2.setTextSize(TypedValue.COMPLEX_UNIT_PX,
                    (int) context.getResources().getDimension(R.dimen.dialog_textSize_ex));
        } else {
            showTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX,
                    (int) context.getResources().getDimension(R.dimen.dialog_title_textSize));
            showTextView2.setTextSize(TypedValue.COMPLEX_UNIT_PX,
                    (int) context.getResources().getDimension(R.dimen.dialog_title_textSize));
        }
        typeid = getIntent().getIntExtra("type", 0);

        switch (typeid) {// 收到短信邀请注册=4、有纷享帐号注册=5
            case 4:
                findViewById(R.id.LinearLayout_show_ts).setVisibility(View.VISIBLE);
                showTextView2.setVisibility(View.VISIBLE);
                showTextView.setVisibility(View.VISIBLE);

                showTextView.setText(I18NHelper.getText("xt.login_new.text.look_invite")/* 1.请查看邀请短信或邮件 */);
                showTextView2.setText(I18NHelper.getText("xt.login_new.text.select_accountnum")/* 2.找到帐号信息并对应填写到下面的区域 */);
                typeHeight = 80;
                break;
            case 5:
                findViewById(R.id.LinearLayout_show_ts).setVisibility(View.VISIBLE);
                showTextView2.setVisibility(View.GONE);
                showTextView.setText(I18NHelper.getText("account.login_by_account.guide.input_to_appointed_area")/* 请根据管理员给你的帐号信息并对应填写到下面的区域 */);
                typeHeight = 80;
                break;
            case 0:
                findViewById(R.id.LinearLayout_show_ts).setVisibility(View.GONE);
                typeHeight = 0;
                break;
            default:
                break;
        }
    }
    LoginSwitchDomainHandler mLoginSwitchDomainHandler = null;
    private void initLoginSwitchDomainHandler(boolean needCheckSwitchedDomain) {
        if (mLoginSwitchDomainHandler == null) {
            mLoginSwitchDomainHandler = new LoginSwitchDomainHandler(this, debugBtn, needCheckSwitchedDomain);
        }
    }
    private void startSwitchDomain(View v) {
        initLoginSwitchDomainHandler(false);
        mLoginSwitchDomainHandler.startSwitchDomain(v);
    }
    private void showDomainList() {
        SetDomainActivity.start(this,-1 /*REQUEST_CODE_SET_DOMAIN*/);
    }

    private MainSubscriber<DomainDataChangeEvent> mDomainDataChangeEventListener;

    private void registerDomainDataChangeEvent() {
        mDomainDataChangeEventListener = new MainSubscriber<DomainDataChangeEvent>() {
            @Override
            public void onEventMainThread(final DomainDataChangeEvent event) {
                processDomainDataChangeEvent(event);
            }
        };
        mDomainDataChangeEventListener.register();
    }

    private void unRegisterDomainDataChangeEvent() {
        if (mDomainDataChangeEventListener != null) {
            mDomainDataChangeEventListener.unregister();
            mDomainDataChangeEventListener = null;
        }
    }

    private void processDomainDataChangeEvent(DomainDataChangeEvent event) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                updateViewAndRequestUrl(DomainDataProvider.getInstance().getSelectedDomainItem(LoginActivity.this));
            }
        });
    }

    private void updateViewAndRequestUrl(DomainData selectedDomainItem) {
        if (selectedDomainItem == null) {
            mCommonTitleView.setMiddleText(I18NHelper.getText("xt.guide_new_map.text.log_in")/* 登录 */);
            LoginViewUtils.changedLoginUrl(this, WebApiUtils.ONLINE_URL);
            initPublicKey();
            SetDomainLogUtils.warnLog("updateViewAndRequestUrl to null");
            return;
        }
        mCommonTitleView.setTitle(selectedDomainItem.getName());
        LoginViewUtils.changedLoginUrl(this, selectedDomainItem.host);
        initPublicKey();
        SetDomainLogUtils.debugLog("updateViewAndRequestUrl to " + selectedDomainItem);
    }
//    private void showDebug(){
//        Dialog dialog = new DebugSelectorDialog(this);
//        dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
//            @Override
//            public void onDismiss(DialogInterface dialog) {
//                if (FsUtils.isInformal()) {
//                    String string = LoginUitls.getSwitchHostBaseRequestUrl(context);
//                    if (TextUtils.isEmpty(string)) {
//                        string = WebApiUtils.requestUrl;
//                    }
//                    String lastText = debugBtn.getText().toString();
//                    debugBtn.setText(string);
//                    if (!TextUtils.equals(lastText, string)) {
//                        initPublicKey();
//                    }
//                }
//            }
//        });
//        dialog.show();
//    }


    public static class CommonlyUsedAccountData implements Serializable, Comparable {
        private static final long serialVersionUID = 2L;

        String businessAaccount = "";

        String personalAaccount = "";

        String phoneAreaCode = "";

        String passwordString = "";

        long lastLoginTime = 0;

        boolean noClear = false;

        /**
         * @return 获取企业帐号
         */
        public String getBusinessAaccount() {
            return businessAaccount;
        }

        /**
         * @return 获取个人帐号
         */
        public String getPersonalAaccount() {
            return personalAaccount;
        }

        /**
         * @return 获取手机账号区号
         */
        public String getPhoneAreaCode() {
            return phoneAreaCode;
        }

        public String getPasswordString() {
            return passwordString;
        }

        public void setBusinessAaccount(String businessAaccount) {
            this.businessAaccount = businessAaccount;
        }

        public void setPhoneAreaCode(String phoneAreaCode) {
            this.phoneAreaCode = phoneAreaCode;
        }

        public void setPasswordString(String passwordString) {
            this.passwordString = passwordString;
        }

        public void setPersonalAaccount(String personalAaccount) {
            this.personalAaccount = personalAaccount;
        }

        public void setLastLoginTime(long time) {
            lastLoginTime = time;
        }

        public long getLastLoginTime() {
            return lastLoginTime;
        }

        public void setNoClear(boolean flag) {
            noClear = flag;
        }

        public boolean getNoClear() {
            return noClear;
        }

        /**
         * @param businessAaccount 企业帐号
         * @param personalAaccount 个人帐号
         */
        public CommonlyUsedAccountData(String businessAaccount, String personalAaccount,
                                       String passwordString, long lastLoginTime) {
            this.businessAaccount = businessAaccount;
            this.personalAaccount = personalAaccount;
            // this.passwordString = passwordString;
            this.lastLoginTime = lastLoginTime;
        }

        /**
         * @param businessAaccount 企业帐号
         * @param personalAaccount 个人帐号
         * @param phoneAreaCode 手机号区号
         */
        public CommonlyUsedAccountData(String businessAaccount, String personalAaccount,
                                       String passwordString, String phoneAreaCode, long lastLoginTime) {
            this.businessAaccount = businessAaccount;
            this.personalAaccount = personalAaccount;
            this.phoneAreaCode = phoneAreaCode;
            this.lastLoginTime = lastLoginTime;
        }

        public CommonlyUsedAccountData() {
            super();
        }

        //按照最后登录的时间进行排序，越晚越靠前
        @Override
        public int compareTo(Object another) {
            if (another instanceof CommonlyUsedAccountData) {
                CommonlyUsedAccountData data = (CommonlyUsedAccountData) another;
                return (int) (data.getLastLoginTime() - lastLoginTime);
            }
            return 0;
        }
    }

    private void startResetPwd() {
        Intent intent = new Intent(context, ResetPasswordActivity.class);
        intent.putExtra("areacode", "");
        intent.putExtra("mobile", "");
        intent.putExtra("isOld", "old");
        startActivityForResult(intent, requestCodeLogin);
    }

    @Override
    protected void processDataWithFullJson(UserInitialDataJsonResult response) {
        if(response.getUserInitialData()!=null){

            LoginUserInfo userInfo = createUserInfoByResponse(response);
            LoginUitls.processLoginCompleted4OldLogin(context, userInfo.enterpriseAccount,
                    userInfo.account,pwd, pwd, false, userInfo, 0);
            String mobileNationCode =userInfo.mobileNationCode;
            PersistentAccountDataBySP.saveAccountInfo(context, userInfo, mobileNationCode, true, pwd);

        }
        NewLoginAct2.closeSelf();
        ShowNewGuideMapActivity.closeSelf();
        closeSelf();
    }

    @Override
    protected void onResume() {
        super.onResume();
        AccountStatisticsEvent.loginPVTick(AccountStatisticsEvent.ENTLOGIN_VIEW);

        //检查下是否需要sso登录
        SSOLoginProcessor.check2StartSSOLogin(this);

        if (AccountInfoUtils.isAgreeAppPrivacy(this)) {
            initQuickLoginEventForDebug();
        }

        if (mLoginSwitchDomainHandler != null && mLoginSwitchDomainHandler.checkCustomSetDomainData()) {
            initPublicKey();
        } else {
            if (FsUtils.isInformal() && debugBtn != null) {
                String string = LoginUitls.getSwitchHostBaseRequestUrl(context);
                if (TextUtils.isEmpty(string)) {
                    string = WebApiUtils.requestUrl;
                }
                debugBtn.setText(string);
            }
        }
    }


    /**---------------------------------- 快速登录相关代码 start-----------------------------------*/

    private MainSubscriber<AccountInfo> mQuickLoginEvent;
    private void initQuickLoginEventForDebug() {
        if (FsUtils.isInformal() && mQuickLoginEvent == null) {
            mQuickLoginEvent = new MainSubscriber<AccountInfo>() {
                @Override
                public void onEventMainThread(AccountInfo info) {
                    if (info == null) {
                        return;
                    }
                    QuickDebugLogin.getInstance().removeStickyEvent();
                    autoLogin(info);
                }
            };
            mQuickLoginEvent.registerSticky();
        }
    }

    private void autoLogin(AccountInfo account) {
        showDialog(BaseActivity.DIALOG_WAITING_BASE);
        String url = account.getUrl();
        LoginUitls.saveBaseRequestUrl(this,url);
        resetBaseRequestUrl();
        inputPwd = account.getPassword();
        QuickDebugLogin.getInstance().doPublickKeyInit(new QuickDebugLogin.OnGetPulickKeyListener() {
            @Override
            public void onSucceed(String publicKey, int passwordLevel) {
                mPublicKey = publicKey;
                mPasswordLevel = passwordLevel;
                businessAccount = account.geteAccount();
                personalAccount = account.getPersonalAccount();
                pwd = LoginUitls.getEncodePassword(LoginActivity.this,account.getPassword(),mPublicKey);
                reqEnterpriseAccountLogin();
            }

            @Override
            public void onError() {

            }
        });
    }

    //保存用户输入的密码明文
    private String inputPwd;

    @Override
    protected void doSaveAccountIfNeed() {
        if (FsUtils.isInformal()) {
            AccountInfo account = new AccountInfo(businessAccount,personalAccount,
                    inputPwd, LoginUitls.getSwitchHostBaseRequestUrl(this));
            QuickDebugLogin.getInstance().save(account);
        }
    }

    private void showQuickLoginDialog() {
        if (!PermissionExecuter.hasPermission(LoginActivity.this, Manifest.permission
                .WRITE_EXTERNAL_STORAGE)) {
            new PermissionExecuter().requestPermissions(LoginActivity.this, Manifest.permission.WRITE_EXTERNAL_STORAGE,
                    new GrantedExecuter() {

                        @Override
                        public void exe() {
                            showQuickLoginDialog();
                        }
                    });
            return;
        }
        DebugSwitchAccountDialog dialog = new DebugSwitchAccountDialog(this);
        dialog.setFromLogin(true);
        dialog.show();
    }

    @Override
    protected int getLoginType() {
        return LoginMethodType.EA_ACCOUNT_LOGIN.getType();
    }
    /**---------------------------------- 快速登录相关代码 end-------------------------------------*/
}
