<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:padding="10dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:orientation="vertical">
        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            i18n:fstext="mail.update.common.first_binding_163_mail_tips"
            android:textSize="16sp"
            android:textColor="@color/black"
            android:lineSpacingExtra="5dp"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">
        <com.facishare.fs.sizectrlviews.SizeControlButton
            android:id="@+id/btn_one"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:padding="5dp"
            android:background="@color/white"
            i18n:fstext="mail.update.common.confirmed"
            android:textColor="@color/color_blue_text"
            android:textSize="16sp"
            android:layout_marginRight="10dp"
                android:layout_marginEnd="10dp" />

        <com.facishare.fs.sizectrlviews.SizeControlButton
            android:id="@+id/btn_two"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:padding="5dp"
            android:background="@color/white"
            i18n:fstext="mail.update.common.goto_163_mail"
            android:textColor="@color/color_blue_text"
            android:textSize="16sp"/>
    </LinearLayout>

</LinearLayout>