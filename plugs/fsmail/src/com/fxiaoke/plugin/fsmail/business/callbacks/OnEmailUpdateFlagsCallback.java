package com.fxiaoke.plugin.fsmail.business.callbacks;

import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.plugin.fsmail.business.results.EmailUpdateFlagsResult;

/**
 * Created by wubb on 2016/8/10.
 */
public interface OnEmailUpdateFlagsCallback {
    void onSuccess(EmailUpdateFlagsResult result);
    void onFailed(WebApiFailureType failureType, int httpStatusCode, String error, int errorCode,
                  int enterpriseID);
}
