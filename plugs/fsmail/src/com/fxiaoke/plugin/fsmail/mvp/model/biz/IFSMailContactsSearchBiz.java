package com.fxiaoke.plugin.fsmail.mvp.model.biz;

import com.fxiaoke.plugin.fsmail.enums.ContactsType;
import com.fxiaoke.plugin.fsmail.mvp.model.bean.FSMailContactsBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by wubb on 2017/7/20.
 */

public interface IFSMailContactsSearchBiz {
    List<FSMailContactsBean> searchData(String keywords);
    void updateData(ArrayList<FSMailContactsBean> selectedContactsList);
    void refreshData(List<FSMailContactsBean> data);
    List<FSMailContactsBean> getContactsData();
    void updateSelectedContacts(List<FSMailContactsBean> selectedContacts);
}
