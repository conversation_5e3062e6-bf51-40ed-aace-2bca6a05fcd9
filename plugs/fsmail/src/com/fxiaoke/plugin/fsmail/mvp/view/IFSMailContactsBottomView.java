package com.fxiaoke.plugin.fsmail.mvp.view;

import com.fxiaoke.plugin.fsmail.mvp.model.bean.FSMailContactsBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by wubb on 2017/7/26.
 */

public interface IFSMailContactsBottomView {
    ArrayList<FSMailContactsBean> getSelectedContactsList();
    void clearSelectedContacts();
    void toggleContactsItem(FSMailContactsBean bean);
    void addSelectedContacts(List<FSMailContactsBean> contactsList);
    void updateView(ArrayList<FSMailContactsBean> selectedContactsList);
    void setOkButtonClickListener(Runnable runnable);
}
