package com.fxiaoke.plugin.fsmail.mvp.model.biz;

import android.text.TextUtils;

import com.fxiaoke.plugin.fsmail.enums.ContactsType;
import com.fxiaoke.plugin.fsmail.mvp.model.bean.FSMailContactsBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by wubb on 2017/7/20.
 */

public class FSMailContactsSearchBiz implements IFSMailContactsSearchBiz {
    IFSMailContactsRecentBiz mRecentBiz;
    IFSMailContactsColleagueBiz mColleagueBiz;
    IFSMailContactsCRMBiz mCrmBiz;

    private List<FSMailContactsBean> mTotalContactsList=new ArrayList<>();
    private List<FSMailContactsBean> mContactsList=new ArrayList<>();

    public FSMailContactsSearchBiz(){
        mRecentBiz=new FSMailContactsRecentBiz();
        mColleagueBiz=new FSMailContactsColleagueBiz();
        mCrmBiz=new FSMailContactsCRMBiz();
    }

    @Override
    public List<FSMailContactsBean> getContactsData() {
        return mContactsList;
    }

    @Override
    public List<FSMailContactsBean> searchData(String keywords){
        List<FSMailContactsBean> contactsList=new ArrayList<>();
        if(TextUtils.isEmpty(keywords))
            return contactsList;

        for(FSMailContactsBean bean : mTotalContactsList){
            if(!TextUtils.isEmpty(bean.nickname) && bean.nickname.indexOf(keywords)>=0){
                contactsList.add(bean);
                continue;
            }
            if(!TextUtils.isEmpty(bean.email) && bean.email.indexOf(keywords)>=0){
                contactsList.add(bean);
            }
        }
        return contactsList;
    }

    @Override
    public void updateData(ArrayList<FSMailContactsBean> selectedContactsList) {
        if(mTotalContactsList.isEmpty()){
            mTotalContactsList.addAll(mRecentBiz.getRecentContactsData(selectedContactsList));
            mTotalContactsList.addAll(mColleagueBiz.getColleagueContactsData(selectedContactsList));
            mTotalContactsList.addAll(mCrmBiz.getCRMContactsData(selectedContactsList));
        }
    }

    @Override
    public void refreshData(List<FSMailContactsBean> data) {
        mContactsList.clear();
        mContactsList.addAll(data);
    }

    @Override
    public void updateSelectedContacts(List<FSMailContactsBean> selectedContacts) {
        for(FSMailContactsBean bean : mContactsList){
            bean.selected=false;
            for(FSMailContactsBean item : selectedContacts){
                if(bean.uuid.equalsIgnoreCase(item.uuid))
                    bean.selected=true;
            }
        }
    }
}
