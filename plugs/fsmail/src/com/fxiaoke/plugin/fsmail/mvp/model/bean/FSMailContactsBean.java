package com.fxiaoke.plugin.fsmail.mvp.model.bean;

import android.graphics.Bitmap;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by wubb on 2017/7/19.
 */

public class FSMailContactsBean implements Parcelable {
    public String uuid;
    public String nickname; //用户昵称
    public String email; //用户邮箱
    public transient Bitmap userHead; //用户头像
    public transient boolean selected; //是否被选中
    public int type;//账号类型，1、常用联系人，2、往来联系人3、企业通讯录4、系统绑定账号,5为crm联系人，6为crm线索,7为客户||||
    public int userId;////用户id，如果为0表示没有查到
    public String company;//crm联系人企业名称
    public String crmId;//crm联系人或线索id

    public FSMailContactsBean(){

    }

    protected FSMailContactsBean(Parcel in) {
        uuid=in.readString();
        nickname=in.readString();
        email = in.readString();
        type=in.readInt();
        userId=in.readInt();
        company=in.readString();
        crmId=in.readString();
    }

    public static final Creator<FSMailContactsBean> CREATOR = new Creator<FSMailContactsBean>() {
        @Override
        public FSMailContactsBean createFromParcel(Parcel in) {
            return new FSMailContactsBean(in);
        }

        @Override
        public FSMailContactsBean[] newArray(int size) {
            return new FSMailContactsBean[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(uuid);
        dest.writeString(nickname);
        dest.writeString(email);
        dest.writeInt(type);
        dest.writeInt(userId);
        dest.writeString(company);
        dest.writeString(crmId);
    }
}
