package com.fxiaoke.plugin.fsmail.fragments;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import android.view.View;

import com.facishare.fs.dialogs.LoadingProDialog;

/**
 * Created by wubb on 2016/8/29.
 */
public class BaseV4Fragment extends Fragment {
    public Runnable showViewListener,hideViewListener;
    boolean mIsViewVisible;
    public void hideView()
    {
        mIsViewVisible=false;
        View view=getView();
        if(view!=null){
            view.setVisibility(View.GONE);
        }
        if(hideViewListener!=null)
            hideViewListener.run();
    }

    public void showView()
    {
        mIsViewVisible=true;
        if(showViewListener!=null)
            showViewListener.run();
        View view=getView();
        if(view!=null){
            view.setVisibility(View.VISIBLE);
        }
    }

    public boolean isViewVisible(){
        return mIsViewVisible;
    }

    LoadingProDialog mLoadingProDialog;

    //add by wubb
    private void initLoadingProDialog(){
        mLoadingProDialog= LoadingProDialog.creatLoadingPro(getActivity());
        mLoadingProDialog.hideLoadingTextView();
        mLoadingProDialog.setCancelable(false);
    }

    //add by wubb
    public void showBaseLoadingDialog(){
        if(mLoadingProDialog==null) return;
        if(mLoadingProDialog.isShowing()) return;
        try {
            mLoadingProDialog.show();
        }
        catch (Exception e){
            e.printStackTrace();
        }
    }

    //add by wubb
    public void hideBaseLoadingDialog(){
        if(mLoadingProDialog==null) return;
        try {
            mLoadingProDialog.dismiss();
        }
        catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initLoadingProDialog();
    }
}
