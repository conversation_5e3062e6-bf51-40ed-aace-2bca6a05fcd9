package com.fxiaoke.plugin.fsmail.fragments;


import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ListView;

import com.fxiaoke.plugin.fsmail.R;
import com.fxiaoke.plugin.fsmail.adapters.FSMailContactsAdapter;
import com.fxiaoke.plugin.fsmail.mvp.model.bean.FSMailContactsBean;
import com.fxiaoke.plugin.fsmail.mvp.presenter.FSMailContactsColleaguePresenter;
import com.fxiaoke.plugin.fsmail.mvp.view.IFSMailContactsColleagueView;

import java.util.ArrayList;
import java.util.List;

public class FSMailContactsColleagueFragment extends BaseV4Fragment implements IFSMailContactsColleagueView {
    private static final String KEY_SELECTED_CONTACTS="key_selected_contacts";
    private FSMailContactsColleaguePresenter mPresenter;
    private FSMailContactsAdapter mAdapter;
    private ListView lv_contacts;
    private List<FSMailContactsBean> mContactsList;
    private LinearLayout ll_no_contacts_tips;
    private FSMailContactsBottomFragment mBottomFragment;
    @Override
    public void setBottomFragment(FSMailContactsBottomFragment bottomFragment){
        mBottomFragment=bottomFragment;
    }

    @Override
    public void updateSelectedContacts(List<FSMailContactsBean> selectedContacts) {
        mPresenter.updateSelectedContacts(selectedContacts);
    }

    @Override
    public void notifyDataSetChanged() {
        mAdapter.notifyDataSetChanged();
    }

    public static FSMailContactsColleagueFragment newInstance(ArrayList<FSMailContactsBean> selectedContactsList){
        FSMailContactsColleagueFragment fragment=new FSMailContactsColleagueFragment();
        Bundle bundle=new Bundle();
        bundle.putParcelableArrayList(KEY_SELECTED_CONTACTS,selectedContactsList);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_fsmail_contacts_colleague, container, false);
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mPresenter=new FSMailContactsColleaguePresenter(this);
        initView(view);
    }

    private void initView(View view){
        lv_contacts=(ListView) view.findViewById(R.id.lv_contacts);
        ll_no_contacts_tips=(LinearLayout)view.findViewById(R.id.ll_no_contacts_tips);

        ArrayList<FSMailContactsBean> selectedContactsList=new ArrayList<>();
        if(getArguments()!=null){
            selectedContactsList=getArguments().getParcelableArrayList(KEY_SELECTED_CONTACTS);
        }
        mContactsList=mPresenter.getColleagueContactsData(selectedContactsList);
        mAdapter=new FSMailContactsAdapter(getActivity(),lv_contacts,mBottomFragment,mContactsList);
        lv_contacts.setAdapter(mAdapter);

        refreshView();
    }

    private void refreshView() {
        if(mContactsList.size()==0){
            ll_no_contacts_tips.setVisibility(View.VISIBLE);
            lv_contacts.setVisibility(View.GONE);
        }
        else{
            ll_no_contacts_tips.setVisibility(View.GONE);
            lv_contacts.setVisibility(View.VISIBLE);
        }
    }
}
