package com.fxiaoke.plugin.fsmail.activities;

import com.facishare.fs.i18n.I18NHelper;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.fragment.app.FragmentTransaction;
import android.view.View;

import com.fxiaoke.cmviews.viewpager.ViewPagerCtrl;
import com.fxiaoke.plugin.fsmail.BaseActivity;
import com.fxiaoke.plugin.fsmail.R;
import com.fxiaoke.plugin.fsmail.fragments.FSMailContactsBottomFragment;
import com.fxiaoke.plugin.fsmail.fragments.FSMailContactsCRMFragment;
import com.fxiaoke.plugin.fsmail.fragments.FSMailContactsColleagueFragment;
import com.fxiaoke.plugin.fsmail.fragments.FSMailContactsRecentFragment;
import com.fxiaoke.plugin.fsmail.mvp.model.bean.FSMailContactsBean;

import java.util.ArrayList;

public class FSMailContactsActivity extends BaseActivity {
    public static final String SELECTED_CONTACTS="selected_contacts";
    private static final String KEY_SELECTED_CONTACTS="key_selected_contacts";
    public static void startActivityForResult(Activity activity,int requestCode,ArrayList<FSMailContactsBean> contactsList){
        Intent intent=new Intent(activity,FSMailContactsActivity.class);
        intent.putParcelableArrayListExtra(KEY_SELECTED_CONTACTS,contactsList);
        activity.startActivityForResult(intent,requestCode);
    }

    private ViewPagerCtrl vpc_fsmail_contacts;

    private FSMailContactsRecentFragment recentFragment;
    private FSMailContactsColleagueFragment colleagueFragment;
    private FSMailContactsCRMFragment crmFragment;
    private FSMailContactsBottomFragment bottomFragment;

    private static final int SEARCH_FSMAIL_CONTACTS_REQUEST_CODE=1001;

    private ArrayList<FSMailContactsBean> mSelectedContactsList=new ArrayList<>();
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_fsmail_contacts);
        initIntent();
        initView();
    }

    @Override
    protected void initTitleEx() {
        super.initTitleEx();
        mCommonTitleView.addLeftAction(I18NHelper.getText("crm.layout.activity_out_profile_person_info.8232")/* 返回 */, R.string.btn_title_back, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        mCommonTitleView.setMiddleText(I18NHelper.getText("fm.fsmail.FSMailContactsActivity.1")/* 添加联系人 */);
//        int width=FSScreen.dip2px(this,35);
//        SVGImageView ivSearch=new ImageView(this);
//        ivSearch.setImageResource(R.drawable.ic_search);
        mCommonTitleView.addRightAction(R.string.crm_work_search, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                FSMailContactsSearchActivity.startActivityForResult(FSMailContactsActivity.this,bottomFragment.getSelectedContactsList(),SEARCH_FSMAIL_CONTACTS_REQUEST_CODE);
            }
        });
    }

    private void initIntent(){
        mSelectedContactsList=getIntent().getParcelableArrayListExtra(KEY_SELECTED_CONTACTS);
    }

    private void initView(){
        initTitleEx();
        vpc_fsmail_contacts=(ViewPagerCtrl)findViewById(R.id.vpc_fsmail_contacts);
        initBottomFragment();
        initFragments();
    }

    private void initFragments(){
        recentFragment= FSMailContactsRecentFragment.newInstance(mSelectedContactsList);
        recentFragment.setBottomFragment(bottomFragment);

        colleagueFragment= FSMailContactsColleagueFragment.newInstance(mSelectedContactsList);
        colleagueFragment.setBottomFragment(bottomFragment);

        crmFragment= FSMailContactsCRMFragment.newInstance(mSelectedContactsList);
        crmFragment.setBottomFragment(bottomFragment);

        initTab();
    }

    private void initBottomFragment(){
        bottomFragment=FSMailContactsBottomFragment.newInstance(mSelectedContactsList,true);
        FragmentTransaction ft=getSupportFragmentManager().beginTransaction();
        ft.replace(R.id.fl_bottom,bottomFragment);
        ft.commit();
    }

    private void initTab() {
        vpc_fsmail_contacts.init(this);
        vpc_fsmail_contacts.addTab(0, I18NHelper.getText("fm.fsmail.FSMailContactsActivity.2")/* 最近 */, recentFragment);
        vpc_fsmail_contacts.addTab(1, I18NHelper.getText("xt.selectuserupdateactivity.text.colleague")/* 同事 */, colleagueFragment);
        vpc_fsmail_contacts.addTab(2, getString(R.string.crm), crmFragment);
        vpc_fsmail_contacts.commitTab();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(data==null) return;
        if(requestCode==SEARCH_FSMAIL_CONTACTS_REQUEST_CODE || requestCode==FSMailContactsBottomFragment.EDIT_SELECTED_CONTACTS_LIST_REQUEST_CODE){
            ArrayList<FSMailContactsBean> contactsList=data.getParcelableArrayListExtra(SELECTED_CONTACTS);
            if(contactsList==null) return;
            bottomFragment.clearSelectedContacts();
            bottomFragment.addSelectedContacts(contactsList);

            recentFragment.updateSelectedContacts(contactsList);
            colleagueFragment.updateSelectedContacts(contactsList);
            crmFragment.updateSelectedContacts(contactsList);
        }
    }
}
