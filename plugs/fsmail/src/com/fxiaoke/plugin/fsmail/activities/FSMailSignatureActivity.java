package com.fxiaoke.plugin.fsmail.activities;

import com.facishare.fs.i18n.I18NHelper;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.facishare.fs.common_utils.ToastUtils;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.plugin.fsmail.R;
import com.fxiaoke.plugin.fsmail.business.FSMailBusiness;
import com.fxiaoke.plugin.fsmail.business.FSMailBusinessHelper;
import com.fxiaoke.plugin.fsmail.business.callbacks.OnEmailUpdateCallback;
import com.fxiaoke.plugin.fsmail.business.results.EmailUpdateResult;
import com.fxiaoke.plugin.fsmail.models.FSMailAccountModel;
import com.fxiaoke.plugin.fsmail.models.FSMailActivityEventBusModel;
import com.fxiaoke.plugin.fsmail.network.NetUtils;
import com.fxiaoke.plugin.fsmail.utils.ActivityHelper;
import com.fxiaoke.plugin.fsmail.utils.DialogFragmentHelper;

import de.greenrobot.event.EventBus;

public class FSMailSignatureActivity extends FSMailBaseActivity {
    static final String KEY_SIGNATURE="key_signature";
    public static void startActivity(Context context,String signature){
        Intent intent=new Intent(context,FSMailSignatureActivity.class);
        intent.putExtra(KEY_SIGNATURE,signature);
        ActivityHelper.startActivity(context,intent);
    }

    EditText et_signature;
    TextView tv_cur_len,tv_max_len;
    final int MAX_CHR_COUNT=300;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_fsmail_signature);
        initView();
    }

    @Override
    protected void initTitleEx() {
        super.initTitleEx();

        mCommonTitleView.addLeftAction(I18NHelper.getText("crm.layout.activity_out_profile_person_info.8232")/* 返回 */, R.string.btn_title_back, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        mCommonTitleView.setMiddleText(I18NHelper.getText("mail.common.common.signature")/* 个性签名 */);

        mCommonTitleView.addRightAction(I18NHelper.getText("crm.layout.layout_scan_addobj_bottom_layout.1835")/* 保存 */, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveSignature();
            }
        });
    }

    private void saveSignature(){
        if(!NetUtils.checkNet()){
            ToastUtils.show(I18NHelper.getText("crm.auth.UDFAuthSyncRunnable.1531")/* 当前网络不可用，请检查设置 */);
            return;
        }
        final String signature=et_signature.getText().toString();
        if(TextUtils.isEmpty(signature))
        {
            ToastUtils.show(I18NHelper.getText("fm.fsmail.FSMailSignatureActivity.3")/* 签名不能为空 */);
            return;
        }
        if(signature.length()>MAX_CHR_COUNT){
            ToastUtils.show(I18NHelper.getText("mail.write.common.signature_length_limit")/* 签名的长度不能超过300个字符 */);
            return;
        }
        FSMailBusiness.EmailAccountInfo emailAccountInfo=new FSMailBusiness.EmailAccountInfo();
        emailAccountInfo.signature=signature;
        FSMailBusiness.emailUpdate(emailAccountInfo, new OnEmailUpdateCallback() {
            @Override
            public void onSuccess(EmailUpdateResult result) {
                if(result==null)
                {
                    ToastUtils.show(I18NHelper.getText("fm.fsmail.FSMailWriteEmailActivity.4")/* 服务器错误 */);
                    return;
                }
                if(result.errorCode==0){
                    if(result.data==1){
                        FSMailAccountModel model= FSMailBusinessHelper.readFSMailAccount();
                        if(model!=null){
                            model.signature=signature;
                            FSMailBusinessHelper.saveFSMailAccount(model);
                        }
                        ToastUtils.show(I18NHelper.getText("fm.fsmail.FSMailSignatureActivity.4")/* 签名修改成功 */);
                        EventBus.getDefault().post(new FSMailSettingsActivity.RefreshEmailInfoEvent());
                        finish();
                    }
                    else{
                        ToastUtils.show(I18NHelper.getText("fm.fsmail.FSMailSignatureActivity.1")/* 签名修改失败 */);
                    }
                }
                else if(result.errorCode== FSMailBusiness.ACCOUNT_OR_PWD_ERROR){
                    DialogFragmentHelper.showUpdateEmailPwdV4DialogFragment(FSMailSignatureActivity.this);
                }
                else if(result.errorCode== FSMailBusiness.ACCOUNT_NOT_BIND_ERROR){
                    FSMailBindingActivity.startActivity(FSMailSignatureActivity.this);
                    finish();
                    EventBus.getDefault().post(new FSMailActivityEventBusModel(FSMailActivityEventBusModel.FINISH));
                }
                else{
                    ToastUtils.show(result.errorMessage);
                }
            }

            @Override
            public void onFailed(WebApiFailureType failureType, int httpStatusCode, String error, int errorCode,
                                 int enterpriseID) {
                ToastUtils.show(WebApiFailureType.getToastShowText(failureType,error));
            }
        });
    }

    private void initView(){
        initTitleEx();
        et_signature=(EditText)findViewById(R.id.et_signature);
        tv_cur_len=(TextView)findViewById(R.id.tv_cur_len);
        tv_max_len=(TextView)findViewById(R.id.tv_max_len);

        if(getIntent().hasExtra(KEY_SIGNATURE))
        {
            String signature=getIntent().getStringExtra(KEY_SIGNATURE);
            et_signature.setText(signature);
            et_signature.setSelection(et_signature.length());
            tv_cur_len.setText(signature.length()+"");
        }
        else{
            tv_cur_len.setText("0");
        }

        tv_max_len.setText(MAX_CHR_COUNT+"");

        et_signature.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                tv_cur_len.setText(s.length()+"");
                tv_cur_len.setTextColor(s.length()>MAX_CHR_COUNT? getResources().getColor(R.color.red):getResources().getColor(R.color.black));
            }
        });

    }
}
