package com.facishare.fs.anticheat;

import android.content.Context;

import com.facishare.fs.common_utils.cheatrisk.CheatRisk;
import com.fxiaoke.fxlog.DebugEvent;

/**
 * Created by zhujg on 2018/9/29.
 */

public interface IAntiCheat {

    public static DebugEvent ANTI_CHEAT_EVENT = new DebugEvent("anti_cheat");

    public static String VERSION = "202009241700";

    public String getCheatLog(Context cxt);

    public void exeAntiCheat(Context cxt,CheatRisk.ICheatRisk ICheatRiskCallback);

    public int anti(Context cxt);

    public void lookThrowable(Context cxt,String str);

    public String getVersion(Context cxt);

}
