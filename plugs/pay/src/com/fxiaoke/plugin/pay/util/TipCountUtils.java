/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.pay.util;

/**
 * Created by wangkw on 2016/7/20.
 */
public class TipCountUtils {

    public interface Event{
        String ATTEND_RED_ENV_TRANS_OUT = "attend_red_env_trans_out";
        String ATTEND_RED_ENV_INTRODUCE = "attend_red_env_introduce";
        String QR_COLLECTION = "qr_collection";
    }

    public static boolean needShow(String event){
        return needShow(event, 1);
    }

    public static boolean needShow(String event, int limit){
        return PaySP.getInt(event, 0) < limit;
    }

    public static void addCount(String event){
        int count = PaySP.getInt(event, 0);
        PaySP.putInt(event, count + 1);
    }

    public static void clearCount(String event){
        PaySP.putInt(event, 0);
    }

}
