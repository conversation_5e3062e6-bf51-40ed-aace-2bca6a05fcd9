/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.pay.enterprise.presenter;

import com.facishare.fs.i18n.I18NHelper;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.afollestad.materialdialogs.MaterialDialog;
import com.facishare.fs.pluginapi.pay.StatId4Pay;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.lib.pay.bean.result.CommonResult;
import com.fxiaoke.lib.pay.error.ErrorDispatcher;
import com.fxiaoke.lib.pay.http.HttpCallBack;
import com.fxiaoke.lib.pay.stat_event.StatEventManager;
import com.fxiaoke.lib.pay.utils.MD5Util;
import com.fxiaoke.plugin.pay.activity.wallet.SetPayPassWordActivity;
import com.fxiaoke.plugin.pay.beans.EWalletAccountInfo;
import com.fxiaoke.plugin.pay.beans.ItKey;
import com.fxiaoke.plugin.pay.beans.arg.AliPayEABaseArg;
import com.fxiaoke.plugin.pay.beans.arg.enterprise.WxEAChargeArg;
import com.fxiaoke.plugin.pay.beans.arg.wallet.CheckTransAmountArg;
import com.fxiaoke.plugin.pay.beans.result.EAWalletResult;
import com.fxiaoke.plugin.pay.beans.result.QueryEAWalletHasPwdResult;
import com.fxiaoke.plugin.pay.beans.vo.EWalletAccount;
import com.fxiaoke.plugin.pay.beans.vo.PayWay;
import com.fxiaoke.plugin.pay.common_view.PassWordCallBack;
import com.fxiaoke.plugin.pay.common_view.PayWindow;
import com.fxiaoke.plugin.pay.constants.WalletType;
import com.fxiaoke.plugin.pay.enterprise.EWalletModel;
import com.fxiaoke.plugin.pay.enterprise.TransResultActivity;
import com.fxiaoke.plugin.pay.enterprise.view.BaseViewInterface;
import com.fxiaoke.plugin.pay.enterprise.view.EWalletTransViewInterface;
import com.fxiaoke.plugin.pay.error.PwdErrHandler;
import com.fxiaoke.plugin.pay.error.RiskErrHandler;
import com.fxiaoke.plugin.pay.error.ToastDelegate;
import com.fxiaoke.plugin.pay.event.EWalletRefreshEvent;
import com.fxiaoke.plugin.pay.pay.AliPay;
import com.fxiaoke.plugin.pay.pay.EnterprisePayCheck;
import com.fxiaoke.plugin.pay.pay.IPayCallback;
import com.fxiaoke.plugin.pay.pay.IPayCheck;
import com.fxiaoke.plugin.pay.pay.WxPay;
import com.fxiaoke.plugin.pay.util.MoneyUtils;
import com.fxiaoke.plugin.pay.util.PayDialogUtils;
import com.fxiaoke.plugin.pay.util.PaySP;
import com.fxiaoke.plugin.pay.util.TipCountUtils;
import com.fxiaoke.stat_engine.StatEngine;

import android.app.Activity;
import android.content.Intent;
import androidx.annotation.NonNull;
import android.text.TextUtils;

import de.greenrobot.event.EventBus;

/**
 * 考勤红包页面转入的Presenter
 * Created by wangkw on 2016/6/27.
 */
public class AttendRedEnvTransPresenter implements EWalletOptInterface{

    private final AliPay aliPay;
    private final WxPay wxPay;
    private final EWalletModel eWalletModel = new EWalletModel();
    private final ErrorDispatcher errorDispatcher = ErrorDispatcher.init(new ToastDelegate()).addHandler(new RiskErrHandler());
    private final Activity activity;
    private final BaseViewInterface baseView;
    private final EWalletTransViewInterface transView;

    private final String thisWalletId;
    private final String subEA;
    private PayWay payWay;
    private PayWay[] payWays;
    private long amount;

    private PayWindow mPwdWindow;

    private long amountLimit;

    private final boolean isIn;
    private static final String TIP_EVENT = PaySP.getKeyWithUserAccount(TipCountUtils.Event.ATTEND_RED_ENV_TRANS_OUT);

    public static final int REQUEST_SET_PWD = 1;



    public AttendRedEnvTransPresenter(@NonNull Activity activity,
                                      @NonNull BaseViewInterface baseView,
                                      @NonNull EWalletTransViewInterface transView,
                                      @NonNull EWalletAccountInfo accountInfo,
                                      boolean isIn) {
        this.activity = activity;
        this.baseView = baseView;
        this.transView = transView;
        this.thisWalletId = accountInfo.walletId;
        this.subEA = accountInfo.subEA;
        this.isIn = isIn;
        initPayWindow();
        errorDispatcher.addHandler(new PwdErrHandler(WalletType.EA_WALLET, mPwdWindow));

        IPayCheck ePayCheck = new EnterprisePayCheck();

        wxPay = new WxPay(activity, baseView);
        wxPay.init(ePayCheck, new IPayCallback() {
            @Override
            public void onPayFailed(boolean end, CommonResult result) {
                onFailed(result);
            }

            @Override
            public void onPaySucceed(long amount, String orderNo) {
                StatEngine.tick(StatId4Pay.PAY_EPAY_ATTENDANCELM_TRANSIN_TAP,
                        StatId4Pay.Key.SUCCEED, StatId4Pay.Key.WECHAT);
                go2TransResult(payWay, amount);
            }

            @Override
            public void onPayWaiting(String message) {
                showConfirmDlg(message);
            }
        });

        aliPay = new AliPay(activity, baseView);
        aliPay.init(ePayCheck, new IPayCallback() {
            @Override
            public void onPayFailed(boolean end, CommonResult result) {
                onFailed(result);
            }

            @Override
            public void onPaySucceed(long amount, String orderNo) {
                StatEngine.tick(StatId4Pay.PAY_EPAY_ATTENDANCELM_TRANSIN_TAP,
                        StatId4Pay.Key.SUCCEED, StatId4Pay.Key.ALIPAY);
                go2TransResult(payWay, amount);
            }

            @Override
            public void onPayWaiting(String message) {
                showConfirmDlg(message);
            }
        });
    }
    private volatile boolean hasPwd;
    @Override
    public void initQuery() {
        baseView.showLoading();

        eWalletModel.queryEAWalletHasPwd(new HttpCallBack<QueryEAWalletHasPwdResult>() {
            @Override
            public void success(Date date, QueryEAWalletHasPwdResult queryEAWalletHasPwdResult) {
                hasPwd = queryEAWalletHasPwdResult.hasPwd == 1;
                FCLog.v(TAG, "queryEAWalletHasPwd, hasPwd:" + hasPwd);

                queryEAWallet();
            }

            @Override
            public void fail(CommonResult commonResult) {
                hasPwd = false;
                FCLog.v(TAG, "queryEAWalletHasPwd failed, hasPwd:" + hasPwd);

                queryEAWallet();
            }
        }, false);
    }

    private void queryEAWallet() {
        eWalletModel.queryEAWallet(new HttpCallBack<EAWalletResult>() {
            @Override
            public void success(Date time, EAWalletResult response) {
                if (!response.isFromCache()){
                    EWalletModel.setEWalletResult(response);
                    EWalletModel.setHasPwd(hasPwd);

                    baseView.hideLoading();
                }
                bindView();
            }

            @Override
            public void fail(CommonResult result) {
                onFailed(result);
            }
        });
    }

    @Override
    public long getMaxLimit() {
        return 0;
    }
    @Override
    public long getAbleBalance() {
        return 0;
    }

    @Override
    public void queryPayWay() {
        baseView.showLoading();
        eWalletModel.queryEAWallet(new HttpCallBack<EAWalletResult>() {
            @Override
            public void success(Date time, EAWalletResult response) {
                EWalletModel.setEWalletResult(response);
                baseView.hideLoading();
                bindView();
            }

            @Override
            public void fail(CommonResult result) {
                onFailed(result);
            }
        });
    }

    @Override
    public void addPayWay(PayWay payWay) {

    }

    @Override
    public void showPayWaySelect() {

    }

    @Override
    public long isAmountOutOfLimit(long amount) {
        if (payWay.getIntValue() == PayWay.ALIPAY
                || payWay.getIntValue() == PayWay.WECHAT){
            return 0;   //支付宝没有限额
        } else {
            if (amount > amountLimit){
                return amount - amountLimit;
            } else {
                return 0;
            }
        }
    }


    @Override
    public PayWay getPayWay() {
        return payWay;
    }

    public List<PayWay> getPayWayList(){
        return Arrays.asList(payWays);
    }

    private void bindView(){
        EWalletAccount defaultAccount = EWalletModel.findAccountFromCache(
                EWalletAccount.Type.DEFAULT);
        EWalletAccount attendAccount = EWalletModel.findAccountFromCache(
                EWalletAccount.Type.ATTENDANCE_RED_ENVELOPE);
        if (defaultAccount != null && attendAccount != null){
            if (payWays != null && payWays.length > 0){
                if (payWays[0].getIntValue() == PayWay.BALANCE){
                    bindBalancePayWay(payWays[0], defaultAccount);
                } else if (payWays.length > 1){
                    bindBalancePayWay(payWays[1], defaultAccount);
                }
            }
            bindPayWay(payWays);
            amountLimit = isIn ? defaultAccount.getAbleBalanceWithCent() : attendAccount.getAbleBalanceWithCent();
            updateInputHint();
        }

    }

    /**
     * 设置余额账户，如果是转入需要显示余额，转出则不需要
     */
    private void bindBalancePayWay(PayWay payWay, EWalletAccount defaultAccount){
        payWay.setWalletAccount(defaultAccount);
        if (isIn){
            long ableBalance = defaultAccount.getAbleBalanceWithCent();
            payWay.setDesc(I18NHelper.getFormatText("pay.enterprise.presenter.available_balance_tips",MoneyUtils.getCNYFromCent(ableBalance))/* 可用余额：{0} */);
            if (ableBalance == 0){
                payWay.setAble(false);
            }
        }
    }

    private void updateInputHint(){
        if (payWay.getIntValue() == PayWay.BALANCE){
            String hint = I18NHelper.getFormatText("pay.enterprise.presenter.most_of_the_time_format_tips",(isIn ? I18NHelper.getText("pay.enterprise.presenter.transfer_in")/* 转入 */ : I18NHelper.getText("pay.wallet.common.transfer_to")/* 转出 */) , MoneyUtils.getCNYFromCent(amountLimit))/* 本次最多可{0}{1} */;
            transView.setInputHint(hint);
        } else if (payWay.getIntValue() == PayWay.ALIPAY
                || payWay.getIntValue() == PayWay.WECHAT){
            transView.setInputHint(I18NHelper.getText("pay.common.common.input_money")/* 请输入金额 */);
        }
    }

    private void autoSelect(){
        if (payWays != null && payWays.length > 0){
            if (payWays.length == 1){ //只有1个则选第0个
                onSelect(0);
            } else if (payWays[0].isAble()){ //第0个可用选第0个
                onSelect(0);
            } else {                        //第0个不可用选第1个
                onSelect(1);
            }
        }
    }

    @Override
    public void bindPayWay(PayWay... payWays){
        if (payWays != null && payWays.length > 0) {
            this.payWays = payWays;
            autoSelect();

            setPwdWindowSubTitle();
        }
    }

    public void onSelect(int index){
        if (payWays != null && payWays.length > 0) {
            if (payWays.length == 1){
                payWay = payWays[0];
                payWays[0].setSelected(true);
            } else {
                payWay = payWays[index];
                payWays[0].setSelected(index == 0);
                payWays[1].setSelected(index == 1);
                if (payWays.length > 2){
                    payWays[2].setSelected(index == 2);
                }
            }
            transView.bindPayWay(payWays);
            updateInputHint();
            transView.updateNextBtnState();
        }
    }

    @Override
    public void onNext(long moneyInput) {
        if (payWay.getIntValue() == PayWay.BALANCE){
            balancePay(moneyInput);
        } else if (isIn){
            if (payWay.getIntValue() == PayWay.ALIPAY){
                StatEngine.tick(StatId4Pay.PAY_EPAY_ATTENDANCELM_TRANSIN_TAP, StatId4Pay.Key.NEXTSTEP,
                        StatId4Pay.Key.ALIPAY);
            } else if (payWay.getIntValue() == PayWay.WECHAT){
                StatEngine.tick(StatId4Pay.PAY_EPAY_ATTENDANCELM_TRANSIN_TAP, StatId4Pay.Key.NEXTSTEP,
                        StatId4Pay.Key.WECHAT);
            }
            baseView.showLoading();
            checkTransInAmount(moneyInput);
        }
    }

    private void balancePay(long moneyInput){
        if (payWay.getWalletAccount() == null){
            baseView.showToast(I18NHelper.getText("pay.enterprise.presenter.try_again_later")/* 请稍后再试 */);
            return;
        }
        if (needSetPwd(payWay.getWalletAccount())){
            return;
        }
        if (isIn){
            StatEngine.tick(StatId4Pay.PAY_EPAY_ATTENDANCELM_TRANSIN_TAP, StatId4Pay.Key.NEXTSTEP, StatId4Pay.Key.BALANCE);
            showPayWindow(moneyInput);
        } else {
            if (TipCountUtils.needShow(TIP_EVENT)){
                TipCountUtils.addCount(TIP_EVENT);
//                showFirstTransOutTipDlg();
            } else {
                StatEngine.tick(StatId4Pay.PAY_EPAY_ATTENDANCELM_TRANSOUT_TAP,
                        StatId4Pay.Key.NEXTSTEP, StatId4Pay.Key.BALANCE);
                showPayWindow(moneyInput);
            }
        }
    }


    private void checkTransInAmount(final long amount) {
        final int payType = PayType.RECHARGE;
        CheckTransAmountArg arg = new CheckTransAmountArg(amount, null, payWay.getIntValue(), payType);
        eWalletModel.checkTransAmount(arg, new HttpCallBack<CommonResult>() {
            @Override
            public void success(Date time, CommonResult response) {
                if (payWay.getIntValue() == PayWay.ALIPAY){
                    aliPayTransIn(amount);
                } else if (payWay.getIntValue() == PayWay.WECHAT){
                    wxPayTransIn(amount);
                }
            }

            @Override
            public void fail(CommonResult result) {
                onFailed(result);
            }
        });
    }

    private void wxPayTransIn(long amount) {
        WxEAChargeArg arg = new WxEAChargeArg();
        arg.setAmount(amount);
        arg.setWalletId(AttendRedEnvTransPresenter.this.thisWalletId);
        eWalletModel.wxEACharge(arg, wxPay.new Delegate(amount));
    }

    private void aliPayTransIn(final long amount){
        AliPayEABaseArg arg = new AliPayEABaseArg();
        arg.setAmount(amount);
        arg.setWalletId(thisWalletId);
        eWalletModel.aliPayEACharge(arg, aliPay.new Delegate(amount));
    }


    /**
     * 校验是否需要设置密码
     * @return true 代表需要设置密码 false 代表不需要
     */
    private boolean needSetPwd(EWalletAccount walletInfo){
        if (!walletInfo.isHasPwd()){
            showSetPwdDialog();
            return true;
        } else {
            return false;
        }
    }

    protected void showSetPwdDialog() {
        new MaterialDialog.Builder(activity)
                .title(I18NHelper.getText("pay.enterprise.presenter.setting_pay_pwd")/* 请设置支付密码 */)
                .content(I18NHelper.getText("pay.enterprise.presenter.no_setting_pay_pwd_tips")/* 未设置支付密码无法从余额转入 */)
                .negativeText(I18NHelper.getText("pay.enterprise.presenter.abort_operation")/* 放弃操作 */)
                .positiveText(I18NHelper.getText("pay.enterprise.presenter.setting_pwd")/* 设置密码 */)
                .callback(new MaterialDialog.ButtonCallback() {
                    @Override
                    public void onPositive(MaterialDialog dialog) {
                        StatEngine.tick(StatId4Pay.PAY_EPAY_SETPASSWD_SOURCE, StatId4Pay.Key.FROMATTENDANCELMTRANSIN);
                        Intent it = new Intent(activity, SetPayPassWordActivity.class);
                        it.putExtra(WalletType.WALLET_TYPE, WalletType.EA_WALLET);
                        baseView.go2Activity(it, REQUEST_SET_PWD);
                    }

                    @Override
                    public void onNegative(MaterialDialog dialog) {

                    }
                })
                .build()
                .show();
    }

    private void showFirstTransOutTipDlg(){
        PayDialogUtils.createConfirmDlg(activity, I18NHelper.getText("pay.enterprise.presenter.transfer_out_tips")/* 转出操作可能影响到考勤红包的发放 */, I18NHelper.getText("xt.account_change_to_market_popup.text.i_know")/* 我知道了 */, null).show();
    }

    private void showPayWindow(long moneyInput){
        mPwdWindow.setMoney(MoneyUtils.getCNYFromCent(moneyInput));
        mPwdWindow.show();
        amount = moneyInput;
    }

    private void initPayWindow() {
        mPwdWindow = new PayWindow(activity);
        mPwdWindow.setTitle(I18NHelper.getText("pay.enterprise.presenter.input_enterprise_pay_pwd")/* 请输入企业支付密码 */);
        mPwdWindow.setSubTitle(isIn ? I18NHelper.getText("pay.enterprise.presenter.transfer_in_atendance_wallet")/* 转入到考勤红包 */ : I18NHelper.getText("pay.enterprise.presenter.transfer_out_to_card")/* 转出到银行卡 */);
        mPwdWindow.setInputCompletedCallBack(new PassWordCallBack() {
            @Override
            public void onInputCompleted(int payType, String cardNo, String mPwd) {
                baseView.showLoading();
                eaTransfer(MD5Util.md5String(mPwd));
            }
        });
    }

    private void setPwdWindowSubTitle() {
        String outSubTitle = I18NHelper.getText("pay.enterprise.presenter.transfer_out_to_card")/* 转出到银行卡 */;
        String inSubTitle = "";
        if(isIn) {
            inSubTitle = I18NHelper.getFormatText("pay.enterprise.presenter.transfer_in_to_tips",findAccountNameFromCache())/* 转入到{0} */;
        } else if(!isIn && payWay != null && payWay.getIntValue() == PayWay.BALANCE) {
            //转出到企业余额
            outSubTitle = I18NHelper.getText("pay.enterprise.presenter.transfer_out_to_enterprise_balance")/* 转出到企业余额 */;
        }
        mPwdWindow.setSubTitle(isIn ? inSubTitle : outSubTitle);
    }

    private void eaTransfer(String pwdMd5){
        String transferWalletId = payWay.getWalletAccount().getWalletId();
        String fromWalletId = isIn ? transferWalletId : thisWalletId;
        String toWalletId = isIn ? thisWalletId : transferWalletId;

        eWalletModel.eaTransfer(amount, fromWalletId, toWalletId, pwdMd5, new HttpCallBack<CommonResult>() {
            @Override
            public void success(Date time, CommonResult response) {
                go2TransResult(payWay, amount);
            }

            @Override
            public void fail(CommonResult result) {
                onFailed(result);
            }
        });
    }

    /**
     * 跳转转账结果
     */
    private void go2TransResult(PayWay payWay, long amount){
        if(isIn) {
            StatEventManager.getInstance().endTickEvent(StatEventManager.ACTION_EPAY_LM_IN);
        }
        baseView.hideLoading();
        Intent intent = new Intent(activity, TransResultActivity.class);
        intent.putExtra(ItKey.TITLE, isIn ? I18NHelper.getText("pay.enterprise.presenter.transfer_in_result")/* 转入结果 */ : I18NHelper.getText("pay.enterprise.presenter.transfer_out_result")/* 转出结果 */);
        intent.putExtra(ItKey.RESULT, isIn ? I18NHelper.getText("pay.enterprise.presenter.transfer_in_success")/* 转入成功 */ : I18NHelper.getText("pay.enterprise.presenter.transfer_out_success")/* 转出成功 */);
        String desc = isIn ? I18NHelper.getFormatText("pay.enterprise.presenter.successful_transfer_in_tips",MoneyUtils.getCNYFromCent(amount),findAccountNameFromCache())/* 成功转入{0}到{1} */
                : I18NHelper.getFormatText("pay.enterprise.presenter.successful_transfer_out_tips",MoneyUtils.getCNYFromCent(amount),payWay.getPayWayString())/* 成功转出{0}到{1} */;
        intent.putExtra(ItKey.DESCRIPTION, desc);
        EventBus.getDefault().post(new EWalletRefreshEvent());
        tickSucceed(payWay);
        baseView.go2Activity(intent, 0);
        baseView.finishActivity(Activity.RESULT_OK, null);
    }

    private String findAccountNameFromCache() {
        String accountName = EWalletModel.findAccountNameFromCache(thisWalletId);
        if(TextUtils.isEmpty(accountName)) {
            return I18NHelper.getText("pay.enterprise.presenter.account")/* 账户 */;
        }
        return accountName;
    }

    private void tickSucceed(PayWay payWay){
        String key = null;
        switch (payWay.getIntValue()){
            case PayWay.ALIPAY:
                key = StatId4Pay.Key.ALIPAY;
                break;
            case PayWay.BALANCE:
                key = StatId4Pay.Key.BALANCE;
                break;
            case PayWay.CARD:
                key = StatId4Pay.Key.CARD;
                break;
        }

        if (isIn){
            StatEngine.tick(StatId4Pay.PAY_EPAY_ATTENDANCELM_TRANSIN_TAP, StatId4Pay.Key.SUCCEED, key);
        } else {
            StatEngine.tick(StatId4Pay.PAY_EPAY_ATTENDANCELM_TRANSOUT_TAP, StatId4Pay.Key.SUCCEED, key);
        }
    }

    private void showConfirmDlg(final String content){
        baseView.hideLoading();
        PayDialogUtils.createConfirmDlg(activity, content, I18NHelper.getText("xt.account_change_to_market_popup.text.i_know")/* 我知道了 */, null).show();
    }


    @Override
    public void onFailed(CommonResult result) {
        baseView.hideLoading();
        errorDispatcher.dispatchError(activity, result);
    }
}
