/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.pay.enterprise;

import com.facishare.fs.i18n.I18NHelper;
import com.fxiaoke.lib.pay.stat_event.StatEventManager;
import com.fxiaoke.plugin.pay.BaseActivity;
import com.fxiaoke.plugin.pay.R;
import com.fxiaoke.plugin.pay.beans.ItKey;
import com.fxiaoke.plugin.pay.beans.vo.PayWay;
import com.fxiaoke.plugin.pay.enterprise.presenter.EWalletOptInterface;
import com.fxiaoke.plugin.pay.enterprise.presenter.EWalletRechargePresenter;
import com.fxiaoke.plugin.pay.enterprise.view.EWalletTransView;
import com.fxiaoke.plugin.pay.enterprise.view.PayWayHolder;
import com.fxiaoke.lib.pay.gray.PayGrayAuthorityUtil;
import com.fxiaoke.lib.pay.gray.OperTypeTools;
import com.fxiaoke.lib.pay.gray.SystemIdUtils;
import com.fxiaoke.plugin.pay.util.PayDialogUtils;

import android.os.Bundle;
import android.view.View;

/**
 * Created by wangkw on 2016/6/16.
 */
public class EWalletRechargeActivity extends BaseActivity{

    EWalletTransView transView;

    private long moneyEnter;
    private String walletId;

    private EWalletOptInterface rechargePresenter;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.ewallet_transaction_act);
        initData();
        initView();
        initOther();
    }

    private void initData() {
        walletId = getIntent().getStringExtra(ItKey.WALLET_ID);
    }

    private void initView() {
        transView = new EWalletTransView(this, this, PayWayHolder.TYPE_NONE, new EWalletTransView.TransListener() {
            @Override
            public void onPayWayClick(int index) {
                rechargePresenter.onSelect(index);
            }

            @Override
            public void onNext(long amount) {
                nextButtonAction(amount);
            }

            @Override
            public boolean isPayWayReady() {
                return  rechargePresenter!= null && rechargePresenter.getPayWay() != null;
            }

            @Override
            public long isAmountOutLimit(long amount) {
                return rechargePresenter.isAmountOutOfLimit(amount);
            }

            @Override
            public void withdrawAll() {

            }
        });

        initTitle(I18NHelper.getText("pay.wallet.common.charge")/* 充值 */);
        transView.bindText(I18NHelper.getText("pay.wallet.common.charge")/* 充值 */);
        View bottomTip = findViewById(R.id.ll_bottom_tip);
        bottomTip.setVisibility(View.VISIBLE);
    }

    private void initOther() {
        rechargePresenter = new EWalletRechargePresenter(this, this, transView, walletId);
//        rechargePresenter.bindPayWay(PayWay.getAliPayInstance(), PayWay.getWxPayInstance());//默认先允许显示
        grayPay();
    }

    private void grayPay() {
        PayGrayAuthorityUtil.updateGrayAuthority(new PayGrayAuthorityUtil.UpdateListener() {
            @Override
            public void onGrayUpdate() {
                boolean isShowWexinPay = OperTypeTools.getOperTypeVisiable(SystemIdUtils.WeXinPay);
                boolean isShowAliPay = OperTypeTools.getOperTypeVisiable(SystemIdUtils.AIiPay);
                if(isShowWexinPay && isShowAliPay) {
                    rechargePresenter.bindPayWay(PayWay.getAliPayInstance(),
                            PayWay.getWxPayInstance());
                } else if(isShowWexinPay) {
                    rechargePresenter.bindPayWay(PayWay.getWxPayInstance());
                } else if(isShowAliPay) {
                    rechargePresenter.bindPayWay(PayWay.getAliPayInstance());
                } else {
                    //均不能显示
                    PayDialogUtils.createNoPayWayErrDlg(EWalletRechargeActivity.this).show();
                }
            }
        });
    }

    /**
     * 下一步按钮的响应
     *
     */
    private void nextButtonAction(long amount) {
        moneyEnter = amount;
        rechargePresenter.onNext(moneyEnter);
    }

}
