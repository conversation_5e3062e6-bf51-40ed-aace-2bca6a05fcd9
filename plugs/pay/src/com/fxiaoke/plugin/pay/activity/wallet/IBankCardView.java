package com.fxiaoke.plugin.pay.activity.wallet;

import com.fxiaoke.lib.pay.bean.result.CommonResult;
import com.fxiaoke.plugin.pay.activity.BaseView;
import com.fxiaoke.plugin.pay.beans.result.BankCardListResult;
import com.fxiaoke.plugin.pay.beans.result.QueryBankByCardNoResult;


/**
 * Created by lif on 2016/1/7.
 */
public interface IBankCardView extends BaseView {
    void initData(BankCardListResult result);
    void queryFailed(CommonResult result);
}
