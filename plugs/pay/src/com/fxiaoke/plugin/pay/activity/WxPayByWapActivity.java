package com.fxiaoke.plugin.pay.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.facishare.fs.js.views.JsApiWebViewClient;
import com.facishare.fs.js.views.JsApiWebViewFragmentEx;
import com.facishare.fs.js.views.X5JsApiWebViewClient;
import com.fxiaoke.plugin.pay.BaseActivity;
import com.fxiaoke.plugin.pay.R;
import com.fxiaoke.plugin.pay.event.WxPayByWapEvent;

import de.greenrobot.event.EventBus;

public class WxPayByWapActivity extends BaseActivity {
    private static final String TAG="WxPayByWapActivity";
    private static final String KEY_URL="key_url";
    public static void startActivity(Context context, String url){
        Intent intent=new Intent(context,WxPayByWapActivity.class);
        intent.putExtra(KEY_URL,url);
        context.startActivity(intent);
    }
    private JsApiWebViewFragmentEx mJsApiWebViewFragment;
    private String mUrl="";
    private boolean mCanFinishMe=false;
    @Override
    public void onBackPressed() {
        if(mJsApiWebViewFragment.canGoBack()){
            mJsApiWebViewFragment.goBack();
            return;
        }
        super.onBackPressed();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_wx_pay_by_wap);
        mUrl=getIntent().getStringExtra(KEY_URL);
        initTitleCommon();
        initJsApiWebViewFragment();
    }

    private void onJsApiWebViewFragmentLoaded(){
        //必须同时重载setJsApiWebViewClient()和setX5JsApiWebViewClient()，这样才能在内核切换后运行正常
        mJsApiWebViewFragment.setJsApiWebViewClient(new JsApiWebViewClient(){
            @Override
            public boolean shouldOverrideUrlLoading(String url) {
                mCanFinishMe=true;
                return super.shouldOverrideUrlLoading(url);
            }
        });

        mJsApiWebViewFragment.setX5JsApiWebViewClient(new X5JsApiWebViewClient(){
            @Override
            public boolean shouldOverrideUrlLoading(String url) {
                mCanFinishMe=true;
                return super.shouldOverrideUrlLoading(url);
            }
        });

        //如果要使用JSAPI，就需要调用一下这个函数，如果不需要使用JSAPI，就可以不用调这个函数
        mJsApiWebViewFragment.initJsApi(mCommonTitleView);
        mJsApiWebViewFragment.setEnablePullDown(false);
        mJsApiWebViewFragment.loadUrl(mUrl);
    }

    private void initJsApiWebViewFragment(){
        mJsApiWebViewFragment=new JsApiWebViewFragmentEx();
        mJsApiWebViewFragment.setBusinessType(this.getClass().getName());
        mJsApiWebViewFragment.setFragmentInitFinishedListener(new Runnable(){
            @Override
            public void run() {
                onJsApiWebViewFragmentLoaded();
            }
        });
        FragmentManager fm=getSupportFragmentManager();
        FragmentTransaction ft= fm.beginTransaction();
        ft.replace(R.id.fl_webview,mJsApiWebViewFragment);
        ft.commit();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(mJsApiWebViewFragment!=null){
            mJsApiWebViewFragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if(mCanFinishMe){
            EventBus.getDefault().post(new WxPayByWapEvent());
            finish();
        }
    }
}
