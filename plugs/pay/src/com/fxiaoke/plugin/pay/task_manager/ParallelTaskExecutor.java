package com.fxiaoke.plugin.pay.task_manager;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by fup on 2017/7/6.
 * 并行执行task
 */

public class ParallelTaskExecutor {
    private ExecutorService executorService;

    public ParallelTaskExecutor(int capacity) {
        if(capacity <= 0) {
            throw new IllegalArgumentException("the capacity is illegal");
        }
        executorService = Executors.newFixedThreadPool(capacity);
    }

    public void exeTask(SyncTask task) {
        executorService.submit(task);
    }
}
