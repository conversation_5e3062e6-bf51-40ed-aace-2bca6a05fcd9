/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.pay.model;

import java.io.Serializable;

import android.content.res.Resources;

/**
 * 标题栏属性
 * Created by wangkw on 2016/3/15.
 */
public class TitleProperty implements Serializable {


    private String titleText;

    private int backResId = 0;

    private Integer bgColor; //背景颜色，优先级比bgResId高

    private Integer titleTextColor; //文本颜色，优先级比titleTextColorResId高

    private int bgColorResId;   //背景颜色资源id

    private int titleTextColorResId; //背景颜色资源id

    public TitleProperty(String titleText) {
        this.titleText = titleText;
    }

    public TitleProperty setBackResId(int backResId) {
        this.backResId = backResId;
        return this;
    }

    public TitleProperty setBgColor(int bgColor) {
        this.bgColor = bgColor;
        return this;
    }

    public TitleProperty setTitleTextColor(int titleTextColor) {
        this.titleTextColor = titleTextColor;
        return this;
    }

    public TitleProperty setBgColorResId(int bgResId) {
        this.bgColorResId = bgResId;
        return this;
    }

    public TitleProperty setTitleTextColorResId(int titleTextColorResId) {
        this.titleTextColorResId = titleTextColorResId;
        return this;
    }

    public TitleProperty parse(Resources res){
        if (bgColor == null && bgColorResId != 0){
            bgColor = res.getColor(bgColorResId);
        }
        if (titleTextColor == null && titleTextColorResId != 0){
            titleTextColor = res.getColor(titleTextColorResId);
        }
        return this;
    }


    public String getTitleText() {
        return titleText;
    }

    public int getBackResId() {
        return backResId;
    }

    public int getBgColor() {
        return bgColor;
    }

    public int getTitleTextColor() {
        return titleTextColor;
    }
}
