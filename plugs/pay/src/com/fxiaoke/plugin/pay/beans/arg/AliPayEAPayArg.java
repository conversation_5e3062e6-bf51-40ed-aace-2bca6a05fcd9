/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.pay.beans.arg;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * Created by wangkw on 2016/6/21.
 */
public class AliPayEAPayArg extends AliPayEABaseArg {

    //收款的企业号，为空默认为纷享
    String toEA;
    //支付密码 md5

    String password;
    //商户号

    String merchantCode;
    //商品ID

    String goodsId;
    //商户订单号

    String merchantOrderNo;

    //商品名称

    String subject;
    //商品描述

    String body;

    //签名

    String sign;

    //收款操作人的纷享ID
    String toUserId;

    String receiverSubEA;

    @JSONField(name = "toEA")
    public String getToEA() {
        return toEA;
    }

    @JSONField(name = "toEA")
    public void setToEA(String toEA) {
        this.toEA = toEA;
    }

    @JSONField(name = "password")
    public String getPassword() {
        return password;
    }

    @JSONField(name = "password")
    public void setPassword(String password) {
        this.password = password;
    }

    @JSONField(name = "merchantCode")
    public String getMerchantCode() {
        return merchantCode;
    }

    @JSONField(name = "merchantCode")
    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    @JSONField(name = "goodsId")
    public String getGoodsId() {
        return goodsId;
    }

    @JSONField(name = "goodsId")
    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    @JSONField(name = "merchantOrderNo")
    public String getMerchantOrderNo() {
        return merchantOrderNo;
    }

    @JSONField(name = "merchantOrderNo")
    public void setMerchantOrderNo(String merchantOrderNo) {
        this.merchantOrderNo = merchantOrderNo;
    }

    @JSONField(name = "sign")
    public String getSign() {
        return sign;
    }

    @JSONField(name = "sign")
    public void setSign(String sign) {
        this.sign = sign;
    }

    @JSONField(name = "subject")
    public String getSubject() {
        return subject;
    }

    @JSONField(name = "subject")
    public void setSubject(String subject) {
        this.subject = subject;
    }

    @JSONField(name = "body")
    public String getBody() {
        return body;
    }

    @JSONField(name = "body")
    public void setBody(String body) {
        this.body = body;
    }

    @JSONField(name = "toUserId")
    public String getToUserId() {
        return toUserId;
    }

    @JSONField(name = "toUserId")
    public void setToUserId(String toUserId) {
        this.toUserId = toUserId;
    }
    public String getReceiverSubEA() {
        return receiverSubEA;
    }

    public void setReceiverSubEA(String receiverSubEA) {
        this.receiverSubEA = receiverSubEA;
    }
}
