package com.fxiaoke.plugin.pay.beans.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.fxiaoke.lib.pay.bean.result.CommonResult;

/**
 * Created by wangtao on 2015/12/30.
 */
public class BindCardResult  extends CommonResult {

    /**
     * 银行卡唯一绑定ID （对应customId）
     */
    @JSONField (serialize = false)
    private String cardInfoId;

    /**
     * 银行卡号
     */
    @JSONField (serialize = false)
    private String cardNo;

    /**
     * 银行卡开户名
     */
    @JSONField (serialize = false)
    private String cardName;

    /**
     * 银行预留手机号
     */
    @JSONField (serialize = false)
    private String phone;

    /**
     * 身份证号码
     */
    @JSONField (serialize = false)
    private String idCardNo;


    /**
     * 银行名称
     */
    @JSONField (serialize = false)
    private String bankName;

    /**
     * 银行编码
     */
    @JSONField (serialize = false)
    private String bankCode;

    /**
     * 卡类型
     */
    @JSONField (serialize = false)
    private int cardType;

    @JSONField (serialize = false)
    private int chargeStatus;

    @J<PERSON>NField (serialize = false)
    private int withdrawStatus;

    public BindCardResult() {
    }

    public BindCardResult(int errorCode, String errorMessage) {
        super(errorCode, errorMessage);
    }


    public int getChargeStatus() {
        return chargeStatus;
    }

    public int getWithdrawStatus() {
        return withdrawStatus;
    }

    public String getCardInfoId() {
        return cardInfoId;
    }

    public void setCardInfoId(String cardInfoId) {
        this.cardInfoId = cardInfoId;
    }


    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public int getCardType() {
        return cardType;
    }

    public void setCardType(int cardType) {
        this.cardType = cardType;
    }


    @Override
    public String toString() {
        return "BindCardResult{" +
                "cardInfoId='" + cardInfoId + '\'' +
                ", cardNo='" + cardNo + '\'' +
                ", cardName='" + cardName + '\'' +
                ", phone='" + phone + '\'' +
                ", idCardNo='" + idCardNo + '\'' +
                ", bankName='" + bankName + '\'' +
                ", bankCode='" + bankCode + '\'' +
                ", cardType=" + cardType +
                '}';
    }
}
