package com.fxiaoke.plugin.pay.common_view;

import android.content.Context;
import android.content.DialogInterface;
import android.view.WindowManager;

import com.afollestad.materialdialogs.MaterialDialog;

/**
 * Created by fup on 2016/5/23.
 */
public class DialogBuildWrap {
    private ButtonCallbackWrap mButtonCallback;
    private Context mContext;
    private MaterialDialog.Builder mBuild;
    public DialogBuildWrap(Context context) {
        mBuild = new MaterialDialog.Builder(context);
        mContext = context;
        mButtonCallback = new ButtonCallbackWrap();
        mBuild.callback(new MaterialDialog.ButtonCallback() {
            @Override
            public void onAny(MaterialDialog dialog) {
                mButtonCallback.onAny(dialog);
                super.onAny(dialog);
            }

            @Override
            public void onNegative(MaterialDialog dialog) {
                mButtonCallback.onNegative(dialog);
                super.onNegative(dialog);
            }

            @Override
            public void onNeutral(MaterialDialog dialog) {
                mButtonCallback.onNeutral(dialog);
                super.onNeutral(dialog);
            }

            @Override
            public void onPositive(MaterialDialog dialog) {
                mButtonCallback.onPositive(dialog);
                super.onPositive(dialog);
            }
        });
    }

    public MaterialDialog.Builder getBuilder(){
        return mBuild;
    }

    public void setOnAnyButtonOnClick(ButtonCallbackWrap.ButtonOnClick onClick) {
        mButtonCallback.setOnAnyButtonOnClick(onClick);
    }

    public void setOnPositiveButtonOnClick(ButtonCallbackWrap.ButtonOnClick onClick) {
        mButtonCallback.setOnPositiveButtonOnClick(onClick);
    }

    public void setOnNegativeButtonOnClick(ButtonCallbackWrap.ButtonOnClick onClick) {
        mButtonCallback.setOnNegativeButtonOnClick(onClick);
    }

    public void setOnNeutralButtonOnClick(ButtonCallbackWrap.ButtonOnClick onClick) {
        mButtonCallback.setOnNeutralButtonOnClick(onClick);
    }


    public static class ButtonCallbackWrap extends MaterialDialog.ButtonCallback {
        private ButtonOnClick mOnAnyButtonOnClick = null;
        private ButtonOnClick mOnPositiveButtonOnClick = null;
        private ButtonOnClick mOnNegativeButtonOnClick = null;
        private ButtonOnClick mOnNeutralButtonOnClick = null;
        public interface ButtonOnClick{
            void onClick(MaterialDialog dialog);
        }

        public void setOnAnyButtonOnClick(ButtonOnClick onClick) {
            mOnAnyButtonOnClick = onClick;
        }

        public void setOnPositiveButtonOnClick(ButtonOnClick onClick) {
            mOnPositiveButtonOnClick = onClick;
        }

        public void setOnNegativeButtonOnClick(ButtonOnClick onClick) {
            mOnNegativeButtonOnClick = onClick;
        }

        public void setOnNeutralButtonOnClick(ButtonOnClick onClick) {
            mOnNeutralButtonOnClick = onClick;
        }

        public void onAny(MaterialDialog dialog) {
            if(mOnAnyButtonOnClick != null) {
                mOnAnyButtonOnClick.onClick(dialog);
            }
        }

        public void onPositive(MaterialDialog dialog) {
            if(mOnPositiveButtonOnClick != null) {
                mOnPositiveButtonOnClick.onClick(dialog);
            }
        }

        public void onNegative(MaterialDialog dialog) {
            if(mOnNegativeButtonOnClick != null) {
                mOnNegativeButtonOnClick.onClick(dialog);
            }
        }

        public void onNeutral(MaterialDialog dialog) {
            if(mOnNeutralButtonOnClick != null) {
                mOnNeutralButtonOnClick.onClick(dialog);
            }
        }
    }
}
