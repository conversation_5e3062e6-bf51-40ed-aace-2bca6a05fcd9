package com.fxiaoke.plugin.pay.common_view;

import android.app.Dialog;
import android.app.DialogFragment;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageButton;
import android.widget.TextView;

import com.afollestad.materialdialogs.MaterialDialog;
import com.afollestad.materialdialogs.Theme;
import com.fxiaoke.plugin.pay.R;
import com.lidroid.xutils.util.SystemActionsUtils;

/**
 * Created by fup on 2016/7/7.
 */
public class CardHolderInfoDialog extends DialogFragment implements View.OnClickListener{
    private ImageButton mImageBtn;
    private TextView mPhone;
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        View view;
        view = LayoutInflater.from(getActivity()).inflate(R.layout.dialog_card_holder_info, null);

        MaterialDialog dialog = new MaterialDialog.Builder(getActivity())
                .customView(view, false)
                .build();
        mImageBtn = (ImageButton) view.findViewById(R.id.ib_close);
        mPhone = (TextView) view.findViewById(R.id.telephone);
        mImageBtn.setOnClickListener(this);
        mPhone.setOnClickListener(this);
        return dialog;
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.ib_close) {
            dismissAllowingStateLoss();

        } else if (i == R.id.telephone) {
            SystemActionsUtils.delPhone(getContext(), "4001869000");

        }
    }

}
