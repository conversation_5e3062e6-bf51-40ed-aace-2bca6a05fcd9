<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.fxiaoke.plugin.pay"
    android:versionCode="1"
    android:versionName="1.0">



    <application>

        <activity android:exported="false"
            android:name=".activity.wallet.WalletActivity"
            android:label="@string/app_name"
            android:launchMode="standard"
            android:screenOrientation="portrait">

        </activity>
        <activity android:exported="false"
            android:name=".activity.bankcard.BankCardDetailActivity"
            android:configChanges="orientation|keyboardHidden"
            android:launchMode="standard"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustUnspecified|stateHidden" />
        <activity android:exported="false"
            android:name=".activity.bankcard.BankCardActivity"
            android:configChanges="orientation|keyboardHidden"
            android:launchMode="standard"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustUnspecified|stateHidden" />
        <activity android:exported="false"
            android:name=".activity.bankcard.BankCardAddInfoActivity"
            android:configChanges="orientation|keyboardHidden"
            android:launchMode="standard"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustUnspecified|stateHidden" />
        <activity android:exported="false"
            android:name=".activity.bankcard.BankCardAddActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity android:exported="false"
            android:name=".activity.bankcard.SupportBankListActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity android:exported="false"
            android:name=".activity.bankcard.PhoneNumberVerifyActivity"
            android:configChanges="orientation|keyboardHidden"
            android:launchMode="standard"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustUnspecified|stateHidden" />
        <activity android:exported="false"
            android:name=".activity.wallet.ForgetPassWordActivity"
            android:configChanges="orientation|keyboardHidden"
            android:launchMode="standard"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustUnspecified|stateHidden" />
        <activity android:exported="false"
            android:name=".activity.wallet.PassWordManagerActivity"
            android:configChanges="orientation|keyboardHidden"
            android:launchMode="standard"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustUnspecified|stateHidden" />
        <activity android:exported="false"
            android:name=".activity.wallet.VerifyOldPwdActivity"
            android:configChanges="orientation|keyboardHidden"
            android:launchMode="standard"
            android:screenOrientation="portrait" />
        <activity android:exported="false"
            android:name=".activity.wallet.SetPayPassWordActivity"
            android:configChanges="orientation|keyboardHidden"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustUnspecified|stateHidden" />
        <activity android:exported="false"
            android:name=".activity.wallet.WalletChargeActivity"
            android:configChanges="orientation"
            android:launchMode="standard"
            android:screenOrientation="portrait" />

         <activity android:exported="false"  android:name=".activity.wallet.ChooseAccountTypeActivity"
            android:configChanges="orientation"
            android:launchMode="standard"
            android:screenOrientation="portrait" />
        <activity android:exported="false"
            android:name=".activity.UserProtocolActivity"
            android:configChanges="orientation|keyboardHidden"
            android:screenOrientation="portrait" />
        <activity android:exported="false"
            android:name=".activity.PaymentsBalanceActivity"
            android:configChanges="orientation|keyboardHidden"
            android:screenOrientation="portrait"  >
        </activity>

        <activity android:exported="false"
            android:name=".activity.OpeningPayActivity"
            android:configChanges="orientation|keyboardHidden"
            android:theme="@style/CustomAppTheme.Translucent"
              >
        </activity>

        <activity android:exported="false"
            android:name=".activity.PaymentDetailInfoActivity"
            android:configChanges="orientation|keyboardHidden"
            android:screenOrientation="portrait" />
        <activity android:exported="false"
                android:name=".activity.QRPayActivity"
                android:configChanges="orientation|keyboardHidden"
                android:screenOrientation="portrait" />

        <activity android:exported="false"
                android:name=".enterprise.EWalletActivity"
                android:configChanges="orientation"
                android:screenOrientation="portrait" >
            <intent-filter>
                <action android:name="fs.intent.action.fs_fspay_epay_wallet"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>

        <activity android:exported="false"
                android:name=".enterprise.EWalletAccountActivity"
                android:configChanges="orientation"
                android:screenOrientation="portrait" >
                <intent-filter>
                    <action android:name="fs.intent.action.fs_fspay_epay_attendancelm_pay"/>
                    <action android:name="fs.intent.action.fs_fspay_epay_attendancelm_notenough"/>
                    <action android:name="fs.intent.action.fs_fspay_epay_subaccount_index"/>
                    <category android:name="android.intent.category.DEFAULT"/>
                </intent-filter>
        </activity>

        <activity android:exported="false"
                android:name=".enterprise.EWalletHistoryActivity"
                android:configChanges="orientation"
                android:screenOrientation="portrait" />

        <activity android:exported="false"
                android:name=".qr.collection.CollectionHistoryActivity"
                android:configChanges="orientation"
                android:screenOrientation="portrait" >
            <intent-filter>
                <action android:name="fs.intent.action.fs_fspay_epay_qr_collection_record"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>

        <activity android:exported="false"
                android:name=".qr.collection.QrCollectionActivity"
                android:configChanges="orientation"
                android:screenOrientation="portrait"
                android:launchMode="singleTask">
            <intent-filter>
                <action android:name="fs.intent.action.fs_fspay_epay_qr_newCollection"/>
                <action android:name="fs.intent.action.fs_fspay_epay_qr_collection"/>
                <action android:name="fs.intent.action.fs_fspay_epay_qr_collection_default"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>

         <activity android:exported="false"  android:name=".activity.AwardLuckyMoneyActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation"
            android:launchMode="standard" />

         <activity android:exported="false"  android:name=".activity.LuckyMoneyInfoActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation"
            android:launchMode="standard" />

         <activity android:exported="false"  android:name=".activity.MyLuckyMoneyInfoActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation"
            android:launchMode="standard" >
            <intent-filter>
                <action android:name="fs.intent.action.fs_fspay_luckymoney_myluckymoney"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>

        <activity android:exported="false"
                android:name=".enterprise.TransDetailActivity"
                android:configChanges="orientation"
                android:screenOrientation="portrait">
                <intent-filter>
                    <action android:name="fs.intent.action.fs_fspay_epay_withdraw_apply"/>
                    <action android:name="fs.intent.action.fs_fspay_epay_withdraw_finish"/>
                    <action android:name="fs.intent.action.fs_fspay_epay_withdraw_refund"/>
                    <action android:name="fs.intent.action.fs_fspay_epay_qr_collection_admin"/>
                    <action android:name="fs.intent.action.fs_fspay_epay_qr_collection_seller"/>
                    <action android:name="fs.intent.action.fs_fspay.wallet"/>
                    <category android:name="android.intent.category.DEFAULT"/>
                </intent-filter>
        </activity>


        <activity android:exported="false"
                android:name=".enterprise.TransResultActivity"
                android:configChanges="orientation"
                android:screenOrientation="portrait" />

        <activity android:exported="false"
                android:name=".enterprise.EWalletRechargeActivity"
                android:configChanges="orientation"
                android:screenOrientation="portrait" />

        <activity android:exported="false"
            android:name=".enterprise.EWalletBankCardAddActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />

        <activity android:exported="false"
            android:name=".enterprise.EWalletPrivateBankCardAddActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />

        <activity android:exported="false"
                android:name=".enterprise.EWalletBankCardManageActivity"
                android:configChanges="orientation"
                android:screenOrientation="portrait"/>


        <activity android:exported="false"
                android:name=".enterprise.EWalletWithdrawActivity"
                android:configChanges="orientation"
                android:screenOrientation="portrait" />

        <activity android:exported="false"
                android:name=".enterprise.AttendRedEnvTransInActivity"
                android:configChanges="orientation"
                android:screenOrientation="portrait" />

        <activity android:exported="false"
                android:name=".enterprise.AttendRedEnvTransOutActivity"
                android:configChanges="orientation"
                android:screenOrientation="portrait" />

        <activity android:exported="false"
            android:name=".enterprise.TransOutWithCardActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />

        <activity android:exported="false"
                android:name=".enterprise.EPayActivity"
                android:configChanges="orientation"
                android:screenOrientation="portrait" />

<!--        <activity android:exported="false" -->
<!--                android:name="com.alipay.sdk.app.H5PayActivity"-->
<!--                android:configChanges="orientation|keyboardHidden|navigation"-->
<!--                 -->
<!--                android:screenOrientation="portrait"-->
<!--                android:windowSoftInputMode="adjustResize|stateHidden" >-->
<!--        </activity>-->

         <activity android:exported="false"  android:name=".activity.WxPayByWapActivity"
            android:configChanges="orientation|keyboardHidden"
            android:theme="@style/CustomAppTheme.Translucent">
        </activity>

        <meta-data android:name="TINKER_ID"
            android:value="${TINKER_ID}"/>

    </application>
    <uses-permission android:name="android.permission.INTERNET" />
</manifest>
