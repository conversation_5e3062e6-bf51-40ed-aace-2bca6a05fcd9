<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main_layout"
    android:orientation="horizontal" android:layout_width="match_parent"
    android:layout_height="64dp">
    
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="12dp"
        android:orientation="vertical"
            android:layout_marginStart="12dp">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/lucky_money_type_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:maxWidth="@dimen/lucky_money_list_max_width"
                android:textColor="#333333"
                android:textSize="16sp"
                />
            <ImageView
                android:id="@+id/ping"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ping_icon"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="5dp"
                android:visibility="gone"
                    android:layout_marginStart="5dp" />
        </LinearLayout>
        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/date"
            android:layout_marginTop="5dp"
            android:textColor="#c3c3c3"
            android:textSize="12sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="12dp"
        android:orientation="vertical"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="12dp">
        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/money"
            android:textColor="#333333"
            android:textSize="16sp"
            android:layout_gravity="end"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/status"
            android:textColor="#c3c3c3"
            android:textSize="12sp"
            android:layout_marginTop="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>
    <View
        android:id="@+id/spit_line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#e3e3e3"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="12dp"
            android:layout_marginStart="12dp" />

</RelativeLayout>