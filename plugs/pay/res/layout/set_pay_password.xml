<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    xmlns:passwordview="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_height" />

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/tv_action_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="88dp"
        i18n:fstext="pay.wallet.common.input_payment_pwd"
        android:textSize="@dimen/l_text_size" />


    <com.fxiaoke.plugin.pay.common_view.PassWordView
        android:id="@+id/password_frame"
        android:layout_width="match_parent"
        android:layout_height="51dp"
        android:layout_marginLeft="30dp"
        android:layout_marginRight="30dp"
        android:layout_marginTop="30dp"
        android:cursorVisible="false"
        passwordview:pwd_background="@color/white"
        passwordview:pwd_border_color="@color/split_line"
            android:layout_marginStart="30dp"
            android:layout_marginEnd="30dp" />

    <com.facishare.fs.sizectrlviews.SizeControlButton
        android:id="@+id/btn_complete"
        style="@style/button_gray_style"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginTop="36dp"
        android:enabled="false"
        i18n:fstext="pay.common.common.next"
            android:layout_marginEnd="15dp"
            android:layout_marginStart="15dp" />
</LinearLayout>

