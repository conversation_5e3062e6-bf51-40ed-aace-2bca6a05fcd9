<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:orientation="vertical">

    <View
            android:id="@+id/section_divider"
            android:layout_width="match_parent"
            android:layout_height="12dp"/>

    <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/section_time"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:textColor="@color/sub_text"
            android:paddingLeft="15dp"
            android:gravity="center_vertical"
            android:textSize="13sp"
            android:background="@color/white"
            i18n:fstext="pay.common.common.time"
            android:paddingStart="15dp" />

    <View
            android:id="@+id/section_time_divider"
            style="@style/pay_split_line_style"/>

    <include
            android:layout_width="match_parent"
            android:layout_height="72dp"
            layout="@layout/item_transaction"/>


</LinearLayout>