<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="pay_split_line_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1px</item>
        <item name="android:background">@color/split_line</item>
    </style>
    <style name="dash_line_style">
        <item name="android:layerType">software</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">2dp</item>
        <item name="android:background">@drawable/dash_line</item>
    </style>
    <style name="dash_line_style_white">
        <item name="android:layerType">software</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">@drawable/dash_line_white</item>
    </style>
    <style name="title_view_line_style">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">1px</item>
        <item name="android:background">@color/bg_cover</item>
    </style>
    <style name="edit_text_line_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1px</item>
        <item name="android:baseline">@color/base_blue_color</item>
    </style>
    <style name="vertical_split_line_style">
        <item name="android:layout_width">1px</item>
        <item name="android:layout_height">30dp</item>
        <item name="android:background">@color/white_split_line_with_transparency</item>
    </style>
    <style name="button_blue_style">
        <item name="android:gravity">center</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:background">@drawable/btn_blue_bg_selector</item>
        <item name="android:textColor">#ffffff</item>
        <item name="android:textSize">@dimen/x_text_size</item>
    </style>
    <style name="button_gray_style">
        <item name="android:gravity">center</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:background">@drawable/btn_gray_bg_selector</item>
        <item name="android:textColor">@color/gray_btn_text</item>
        <item name="android:textSize">@dimen/x_text_size</item>
    </style>

    <style name="button_yellow_gray_style">
        <item name="android:gravity">center</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:background">@drawable/btn_yellow_selector</item>
        <item name="android:textColor">@drawable/btn_text_selector</item>
        <item name="android:textSize">@dimen/x_text_size</item>
    </style>
    <style name="text_blue_style">
        <item name="android:textColor">@color/blue_text</item>
        <item name="android:textSize">@dimen/l_text_size</item>
    </style>
    <style name="bank_card_style">
        <item name="android:background">@drawable/btn_blue_bg_selector</item>
        <item name="android:textColor">#ffffff</item>
        <item name="android:textSize">@dimen/x_text_size</item>
    </style>
    <style name="AnimationFade">
        <item name="android:windowEnterAnimation">@anim/dialog_enter</item>
        <item name="android:windowExitAnimation">@anim/dialog_exit</item>
    </style>
    <style name="Dialog" parent="android:style/Theme.Dialog">
        <item name="android:background">#********</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
    </style>



    <style name="CustomProgressDialog" parent="@style/CustomDialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
    </style>



    <style name="BankSelectDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
    </style>
    <style name="BankCardAddItemStyle">
        <item name="android:textSize">17sp</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textColor">#999999</item>
        <item name="android:layout_width">110dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center_vertical|left</item>
    </style>
    <style name="BankCardAddItemLayoutStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">54dp</item>
        <item name="android:layout_marginLeft">15dp</item>
        <item name="android:background">#ffffff</item>

    </style>
    <style name="BankCardAddEditItemStyle">
        <item name="android:textSize">17sp</item>
        <item name="android:layout_width">204dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_marginLeft">110dp</item>
        <item name="android:textColor">#333333</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:background">@null</item>
    </style>
    <style name="BankCardAddIconStyle">
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_alignParentRight">true</item>
        <item name="android:layout_marginRight">12dp</item>
    </style>
    <style name="BankCardAddItemLineStyle">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">1px</item>
        <item name="android:layout_marginLeft">15dp</item>
        <item name="android:background">#eeeeee</item>
    </style>
    <style name="PaymentsItemHeadStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#999999</item>
        <item name="android:textSize">15sp</item>
    </style>
    <style name="PaymentsItemContentStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#444444</item>
        <item name="android:textSize">13sp</item>
        <item name="android:layout_marginRight">10dp</item>
        <item name="android:layout_alignParentRight">true</item>
    </style>
    <style name="BankSelectItemHeadTextStyle">
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textSize">17sp</item>
        <item name="android:textColor">#999999</item>
        <item name="android:layout_marginLeft">24dp</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>
    <style name="BankSelectItemArrow">
        <item name="android:layout_width">7dp</item>
        <item name="android:layout_height">12dp</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_alignParentRight">true</item>
        <item name="android:layout_marginRight">24dp</item>
        <item name="android:layout_marginLeft">10dp</item>
    </style>

    <style name="BottomTipText">
        <item name="android:layout_height">40dp</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/blue_text</item>
        <item name="android:textSize">13sp</item>
        <item name="android:singleLine">true</item>
    </style>
    <declare-styleable name="ContentWithSpaceEditText">
        <attr name="epaysdk_type" format="enum">
            <enum name="phone" value="0" />
            <enum name="card" value="1" />
            <enum name="IDCard" value="2" />
        </attr>
        <attr name="maxLength" format="integer"/>
    </declare-styleable>
    <declare-styleable name="AutofitTextView">
        <!-- Minimum size of the text. -->
        <attr name="minTextSize" format="dimension" />
        <!-- Amount of precision used to calculate the correct text size to fit within its
        bounds. Lower precision is more precise and takes more time. -->
        <attr name="precision" format="float" />
        <!-- Defines whether to automatically resize text to fit to the view's bounds. -->
        <attr name="sizeToFit" format="boolean" />
    </declare-styleable>
</resources>