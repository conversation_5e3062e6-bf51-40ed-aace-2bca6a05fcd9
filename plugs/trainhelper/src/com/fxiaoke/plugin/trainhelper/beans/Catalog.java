package com.fxiaoke.plugin.trainhelper.beans;

import com.facishare.fs.i18n.I18NHelper;
import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * Created by guoyf on 2016/4/22.
 */
public class Catalog {

    public static final Catalog ALL = new Catalog("all",I18NHelper.getText("th.beans.view.total_courses")/* 全部课程 */);
    public static final Catalog PUBLIC = new Catalog("publicCourse",I18NHelper.getText("th.beans.view.fs_open_course")/* 纷享公开课 */);
    public static final Catalog LEARN = new Catalog("learnUse",I18NHelper.getText("th.beans.view.enjoy_fxiaoke")/* 会用纷享 */);
    public static final Catalog DEFAULT = new Catalog("default",I18NHelper.getText("wq.projectsearchadapter.text.uncategorized")/* 未分类 */);
    @JSONField(name="M1")
    public String labelId;
    @JSONField(name="M2")
    public String labelName;

    @JSONCreator
    public Catalog(
            @JSONField(name="M1") String labelId,
            @JSONField(name="M2") String labelName) {
        this.labelId = labelId;
        this.labelName = labelName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Catalog catalog = (Catalog) o;

        return labelId != null ? labelId.equals(catalog.labelId) : catalog.labelId == null;

    }

    @Override
    public int hashCode() {
        return labelId != null ? labelId.hashCode() : 0;
    }
}
