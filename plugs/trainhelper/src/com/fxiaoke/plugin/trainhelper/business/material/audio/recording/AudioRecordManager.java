package com.fxiaoke.plugin.trainhelper.business.material.audio.recording;

import com.facishare.fs.i18n.I18NHelper;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.ref.SoftReference;
import java.util.Vector;

import com.fxiaoke.plugin.trainhelper.business.material.audio.AudioUploadContract;
import com.fxiaoke.plugin.trainhelper.business.material.audio.encoding.MP3Encoder;
import com.fxiaoke.plugin.trainhelper.threadmanager.DiskIOThreadPool;
import com.fxiaoke.plugin.trainhelper.utils.AudioRecordUtil;
import com.fxiaoke.plugin.trainhelper.utils.ToastUtils;

import android.content.Context;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.text.TextUtils;

/**
 * Created by jiangh on 2017/8/25.
 */

public class AudioRecordManager {
    public static int sampleRateInHz = 44100;
    private static int RESULT_CODE_SUCCESS = 0; // 录音成功
    private static int RESULT_CODE_ERROR = 1; // 录音失败
    private static AudioRecordManager instance;
    public static String FILE_SUFFIX = ".mp3";
    private AudioRecord mAudioRecorder;
    private DataOutputStream mDataOutputStream;
    private Thread recordThread;
    private  int bufferSize=0;
    private long totalByteOffset = 0;
    private boolean isRecording = false;
    private volatile boolean isMergingFiles = false; // 正在合并临时文件为最终输出文件
    private String mFinishOutputDir = ""; // 最终录音完成的输出文件夹
    private String mOutputFileFullPath = ""; // 最终录音完成的完整输出路径
    private String mFilePathPrefix = ""; // 录音临时文件缓存前缀
    private String mCurTempFilePath = ""; // 当前录音临时文件路径
    private Vector<String> mRecordTempFiles = new Vector<String>(); // 录音临时文件集合
    private SoftReference<AudioUploadContract.OnThAudioRecordListerner> mListener;

    private AudioRecordManager(){
        bufferSize = 10*AudioRecord.getMinBufferSize(sampleRateInHz, AudioFormat.CHANNEL_IN_MONO, AudioFormat
                .ENCODING_PCM_16BIT);
        mAudioRecorder = new AudioRecord(MediaRecorder.AudioSource.MIC, sampleRateInHz, AudioFormat.CHANNEL_IN_MONO,
                AudioFormat.ENCODING_PCM_16BIT, bufferSize);
    }

    public static synchronized AudioRecordManager getInstance(){
        if(instance == null){
            synchronized(AudioRecordManager.class){
                instance = new AudioRecordManager();
            }
        }
        return instance;
    }

    public int getBufferSize(){
        if(bufferSize == 0){
            bufferSize = 10*AudioRecord.getMinBufferSize(sampleRateInHz, AudioFormat.CHANNEL_IN_MONO, AudioFormat
                    .ENCODING_PCM_16BIT);
        }
        return bufferSize;
    }

    // 每个字节表示多少秒
    public float getSecondPerByte(){
        int bytePerSecond = sampleRateInHz * 2;
        return 1.0f/(bytePerSecond*1.0f);
    }

    public void startRecord(Context context) throws Exception
    {
        if(mAudioRecorder == null) return;
        if(isMergingFiles){
            ToastUtils.show(I18NHelper.getText("th.material.audio.outputing_audio_file")/* 正在输出录音文件 */);
            return;
        }

        if(mDataOutputStream == null){
            mFilePathPrefix = AudioRecordUtil.getNewAudioTempFilePathPrefix(context);
            if(TextUtils.isEmpty(mFilePathPrefix)){
                ToastUtils.show(I18NHelper.getText("th.material.audio.generat_audio_file_failed")/* 音频文件创建失败 */);
                return;
            }

            //        int segmentIndex = mRecordTempFiles.size();
            //        mCurTempFilePath = mFilePathPrefix + "_" + segmentIndex + ".raw.temp";
            mCurTempFilePath = mFilePathPrefix + ".raw";

            File recordFile = new File(mCurTempFilePath);
            if(!recordFile.exists()){
                try {
                    recordFile.createNewFile();
                } catch (IOException e) {
                    e.printStackTrace();
                    ToastUtils.show(I18NHelper.getText("th.material.audio.generat_audio_file_failed")/* 音频文件创建失败 */);
                    return;
                }
            }
            mDataOutputStream = new DataOutputStream(new FileOutputStream(recordFile, true));
        }

        startRecodingThread();
    }

    public void stopRecord() throws Exception {
        if(mAudioRecorder == null) return;
        pauseRecord(true);
        //mediaRecorder.reset();
        isRecording = false;
        transTempFilesToOutputAsycn();
    }

    public void pauseRecord(boolean stop)throws Exception {
        if(mAudioRecorder == null) return;
        if(isMergingFiles){
            ToastUtils.show(I18NHelper.getText("th.material.audio.outputing_audio_file")/* 正在输出录音文件 */);
            return;
        }
        if(isRecording){
            try {
                destroyRecodingThread();
                if (mAudioRecorder!= null) {
                    if (mAudioRecorder.getState() == AudioRecord.STATE_INITIALIZED) {
                        mAudioRecorder.stop();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (mDataOutputStream != null) {
            mDataOutputStream.flush();
            if(stop){
                mDataOutputStream.close();
                mDataOutputStream = null;
                totalByteOffset = 0;
            }
        }
        //mRecordTempFiles.add(mCurTempFilePath);
        isRecording = false;
    }

    public void resumeRecord(Context context) throws Exception {
        if(mAudioRecorder == null) return;
        if(isMergingFiles){
            ToastUtils.show(I18NHelper.getText("th.material.audio.outputing_audio_file")/* 正在输出录音文件 */);
            return;
        }
        if(!isRecording){
            startRecord(context);
        }
        isRecording = true;
    }

    public boolean isRecording(){
        return isRecording;
    }

    public String getLastFilePath(){
        return mOutputFileFullPath + FILE_SUFFIX;
    }

    public void release(){
//        if(mediaRecorder != null){
//            if(isRecording){
//                mediaRecorder.stop();
//            }
//            mediaRecorder.release();
//            mediaRecorder = null;
//            isRecording = false;
//        }

        if (mAudioRecorder != null) {
            if(isRecording){
                try {
                    pauseRecord(true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            destroyRecodingThread();
            mAudioRecorder.release();
            mAudioRecorder = null;
            isRecording = false;
        }
        mListener = null;
        instance = null;
    }

    /**
     * 启动录音线程
     */
    private void startRecodingThread() {
        destroyRecodingThread();
        isRecording = true;
        recordThread = new Thread(recordRunnable);
        recordThread.start();
    }

    /**
     * 销毁线程方法
     */
    private void destroyRecodingThread() {
        try {
            isRecording = false;
            if (null != recordThread && Thread.State.RUNNABLE == recordThread.getState()) {
                try {
                    Thread.sleep(500);
                    recordThread.interrupt();
                } catch (Exception e) {
                    recordThread = null;
                }
            }
            recordThread = null;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            recordThread = null;
        }
    }

    /**
     * 录音线程
     */
    Runnable recordRunnable = new Runnable() {
        @Override
        public void run() {
            if(mAudioRecorder == null) return;
            try {
                //android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_URGENT_AUDIO);
                int bytesRecord;
                //int bufferSize = 320;
                //byte[] cachedBuffer = new byte[sampleRateInHz];
                byte[] tempBuffer = new byte[bufferSize];
                if (mAudioRecorder.getState() != AudioRecord.STATE_INITIALIZED) {
                    stopRecord();
                    return;
                }
                mAudioRecorder.startRecording();
                //writeToFileHead();
                while (isRecording) {
                    if (null != mAudioRecorder) {
                        bytesRecord = mAudioRecorder.read(tempBuffer, 0, bufferSize);
                        if (bytesRecord == AudioRecord.ERROR_INVALID_OPERATION || bytesRecord == AudioRecord.ERROR_BAD_VALUE) {
                            continue;
                        }
                        if (bytesRecord != 0 && bytesRecord != -1) {
                            totalByteOffset += bytesRecord;
                            // 投递每一个buffer出去，以便绘制波形图
                            if(mListener != null && mListener.get() != null){
                                mListener.get().onAudioBufferCatched(tempBuffer, bytesRecord, totalByteOffset);
                            }

                            //在此可以对录制音频的数据进行二次处理 比如变声，压缩，降噪，增益等操作
                            //我们这里直接将pcm音频原数据写入文件 这里可以直接发送至服务器 对方采用AudioTrack进行播放原数据
                            if(mDataOutputStream != null){
                                mDataOutputStream.write(tempBuffer, 0, bytesRecord);
                            }
                        } else {
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    };


    public void setAudioFinishOutputDir(String dirPath){
        mFinishOutputDir = dirPath;
    }

    public void setAudioRecordListerner(AudioUploadContract.OnThAudioRecordListerner listerner){
        if(listerner != null){
            mListener = new SoftReference<AudioUploadContract.OnThAudioRecordListerner>(listerner);
        }else{
            mListener = null;
        }
    }

    private void transTempFilesToOutputAsycn(){
        if(isMergingFiles){
            return;
        }
        isMergingFiles = true;
        DiskIOThreadPool.getExecutor().execute(new Runnable() {
            @Override
            public void run() {
                doTransTempFiles();
            }
        });
    }

    private synchronized void doTransTempFiles(){
        if(mListener != null && mListener.get() != null){
            mListener.get().onAudioRecordProcessing();
        }

//        FileOutputStream fileOutputStream = null;
//        FileInputStream fileInputStream = null;
        boolean gotException = false;
        try{
            mOutputFileFullPath = AudioRecordUtil.getOutputFullPathByPrefix(mFinishOutputDir, mFilePathPrefix);
            if(TextUtils.isEmpty(mOutputFileFullPath)){
                finishMerge(RESULT_CODE_ERROR);
                return;
            }
            File recordAudioFile = new File(getLastFilePath());
            if(!recordAudioFile.exists()){
                recordAudioFile.createNewFile();
            }
            //fileOutputStream = new FileOutputStream(recordAudioFile);

            // 开始合并
//            for (int i = 0; i < mRecordTempFiles.size(); i++) {
//                File tempfile = new File(mRecordTempFiles.get(i));
//                fileInputStream = new FileInputStream(tempfile);
//                byte[] myByte = new byte[fileInputStream.available()];
//                // file length
//                int length = myByte.length;
//
//                if (i >= 0) {// first audio file if (i == 0)
//                    while (fileInputStream.read(myByte) != -1) {
//                        fileOutputStream.write(myByte, 0, length);
//                    }
//                } else {// delete file head information
//                    while (fileInputStream.read(myByte) != -1) {
//
//                        fileOutputStream.write(myByte, 6, length - 6);
//                    }
//                }
//                fileOutputStream.flush();
//                fileInputStream.close();
//            }
//
//            fileOutputStream.close();

            // convert .raw PCM file data to wav/mp3 format
            MP3Encoder.convertPCM2MP3(mCurTempFilePath, getLastFilePath(), 1, sampleRateInHz);
//            AudioRecordUtil.convertPCMAudio2Other(recordAudioFile.getAbsolutePath(), getLastFilePath(), sampleRateInHz,
//                    bufferSize, AudioRecordUtil.AUDIO_FMT_WAV);

        }catch(Exception e){
            e.printStackTrace();
            gotException = true;
        }finally {
//            try {
//            if(fileOutputStream != null){
//                fileOutputStream.close();
//            }
//            } catch (IOException e) {
//                e.printStackTrace();
//                gotException = true;
//            }
            finishMerge(gotException ? RESULT_CODE_ERROR : RESULT_CODE_SUCCESS);
        }

    }

    private synchronized void finishMerge(int resultCode){
        isMergingFiles = false;
//        if(mRecordTempFiles != null){
//            mRecordTempFiles.clear();
//        }
        if(mListener != null && mListener.get() != null){
            if(resultCode == RESULT_CODE_SUCCESS){
                mListener.get().onAudioRecordFinish(getLastFilePath());
            }else if(resultCode == RESULT_CODE_ERROR){
                mListener.get().onAudioRecordFail();
            }
        }
    }

}
