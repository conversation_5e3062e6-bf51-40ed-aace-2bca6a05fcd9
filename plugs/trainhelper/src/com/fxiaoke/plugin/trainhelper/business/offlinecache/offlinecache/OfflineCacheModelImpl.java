package com.fxiaoke.plugin.trainhelper.business.offlinecache.offlinecache;

import static com.fxiaoke.plugin.trainhelper.business.offlinecache.offlinecache.OfflineCacheContract.*;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.plugin.trainhelper.beans.CoursewareVo;
import com.fxiaoke.plugin.trainhelper.business.offlinecache.DownloadInfoSimple;
import com.fxiaoke.plugin.trainhelper.business.offlinecache.OfflineCacheDownloadManager;
import com.fxiaoke.plugin.trainhelper.business.offlinecache.base.OfflineCacheBaseContract;
import com.fxiaoke.plugin.trainhelper.business.offlinecache.base.OfflineCacheBaseModel;
import com.fxiaoke.plugin.trainhelper.threadmanager.DiskIOThreadPool;
import com.fxiaoke.plugin.trainhelper.utils.TrainHelperUtil;
import com.fxiaoke.plugin.trainhelper.utils.VideoCacheUtils;
import com.lzy.okserver.download.DownloadInfo;
import com.lzy.okserver.download.DownloadManager;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import androidx.collection.ArrayMap;

/**
 * Created by jiangh on 2017/6/12.
 */

public class OfflineCacheModelImpl extends OfflineCacheBaseModel implements IOfflineCacheModel {
    private static final String TAG = "OfflineCacheModelImpl";
    IOfflineCachePresenter mPresenter;
    private Handler mUIHandler;

    /**
     * 已经分类完成的视频缓存----新
     */
    private ArrayMap<Integer, ArrayList<CoursewareVo>> mClassifiedDownloadedVideos = new ArrayMap<Integer, ArrayList<CoursewareVo>>();

    /**
     * 由于 DownloadManager 返回的下载速度为平均下载速度，所以此时需要记录下每个任务上一次下载的大小，然后用这一次
     * 的大小减去上一次的大小，除以时间就是网速的大小
     */
    private HashMap<String, Long> lastDownloadSize = new HashMap<>();
    private long lastTimeMilliSec = 0;

    public OfflineCacheModelImpl(){
        initUIHandler();
    }

    @Override
    public void setPresenter(OfflineCacheBaseContract.IOfflinePresenter presenter) {
        super.setPresenter(presenter);
        if(presenter != null){
            if(presenter instanceof IOfflineCachePresenter){
                mPresenter = (IOfflineCachePresenter)presenter;
            }
        }else{
            mPresenter = null;
        }
    }

    @Override
    public Object getData() {
        return mClassifiedDownloadedVideos;
    }

    private void initUIHandler(){
        mUIHandler = new Handler(Looper.getMainLooper());
    }

    @Override
    public void readCacheInfoFromLocalAsync(Context context, final OfflineCacheBaseContract.IDiskInfoHandleCallBack
            callBack) {
        final File cacheFile = context.getCacheDir();
        DiskIOThreadPool.getSingleThreadQueueExecutor().execute(new Runnable() {
            @Override
            public void run() {
                //需要提前将老版本的正在下载信息清除掉
                VideoCacheUtils.removeAllDownloadingFile(cacheFile);
                mDownloadingVideos = VideoCacheUtils.readDownloadingFilesNew(cacheFile, sourceType);
                mDownloadedVideos = VideoCacheUtils.getLocalCachedVideosNew(sourceType);
                ensureDownloadFinishedVideoHandled();
                mDownloadState.downloadingVideoSize = mDownloadingVideos != null ? mDownloadingVideos.size() : 0;
                mDownloadState.downloadedVideoSize = mDownloadedVideos != null ? mDownloadedVideos.size() : 0;
                classifyDownloadVideos();
                if(callBack != null){
                    callBack.onSuccess(mDownloadState);
                }
            }
        });
    }

    @Override
    public void startAutoRefreshCachingStatus(Runnable runnable) {
        if(runnable == null){
            super.startAutoRefreshCachingStatus(new Runnable() {
                @Override
                public void run() {
                    updateAfterDataChangedHappened();
                }
            });
        }else{
            super.startAutoRefreshCachingStatus(runnable);
        }

        lastTimeMilliSec = System.currentTimeMillis();
    }

    @Override
    public boolean hasDownloadingTask() {
        return mDownloadState.cachingTaskNum > 0;
    }

    @Override
    public ArrayList<CoursewareVo> getCoursewareVoListAt(int position) {
        position--;
        Iterator iterator = mClassifiedDownloadedVideos.keySet().iterator();
        int key = 0;
        while (position >= 0) {
            key = (int) iterator.next();
            position--;
        }
        ArrayList<CoursewareVo> coursewareVoList = mClassifiedDownloadedVideos.get(key);
        return coursewareVoList;
    }

    @Override
    public void setAllCourseVideosChecked(boolean checked) {
        if(mDownloadedVideos != null){
            for (int i = 0; i < mDownloadedVideos.size(); i++) {
                mDownloadedVideos.get(i).isCheck = checked;
            }
        }
    }

    @Override
    public void onSelectedDownloadingItemChecked(int index) {
        if(mClassifiedDownloadedVideos == null){
            return;
        }
        final ArrayList<CoursewareVo> coursewareVoList = getCoursewareVoListAt(index);
        if(coursewareVoList != null && coursewareVoList.size() > 0){
            if(mPresenter != null && coursewareVoList.get(0) != null){
                mPresenter.onListItemCheckBoxClick(coursewareVoList.get(0));
            }
        }
    }

    @Override
    public void deleteCheckedDownloadedVideosAsync(final OfflineCacheBaseContract.IDiskInfoHandleCallBack callBack) {

        ArrayMap<Integer, ArrayList<CoursewareVo>> deleteTargetMap = new ArrayMap<Integer, ArrayList<CoursewareVo>>();
        Iterator iterator = mClassifiedDownloadedVideos.keySet().iterator();
        while (iterator.hasNext()) {
            int courseId = (int) iterator.next();
            final ArrayList<CoursewareVo> coursewareVoArrayList = mClassifiedDownloadedVideos.get(courseId);
            if (coursewareVoArrayList == null || coursewareVoArrayList.size() == 0) {
                continue;
            }
            final CoursewareVo dataBean = coursewareVoArrayList.get(0);
            if (dataBean.isCheck) {
                deleteTargetMap.put(dataBean.courseId, coursewareVoArrayList);
                //删除缓存列表中的数据
                iterator.remove();
            }
        }

        if(deleteTargetMap.size() == 0){
            if(callBack != null){
                callBack.onSuccess(0);
            }
        }

        Object[] keySet = deleteTargetMap.keySet().toArray();
        final int len = keySet.length;
        for(int i =0; i < len; i++){
            final int index  = i;
            final ArrayList<CoursewareVo> targetCVArrayList = deleteTargetMap.get(keySet[i]);
            if (targetCVArrayList.size() == 1) {
                DiskIOThreadPool.getExecutor().execute(new Runnable() {
                    @Override
                    public void run() {
                        //删除在 SD 卡上缓存的文件
                        VideoCacheUtils.deleteDownloadedVideoNew(targetCVArrayList.get(0));
                        if(index == len -1){
                            if(callBack != null){
                                callBack.onSuccess(len);
                            }
                        }
                    }
                });
            } else {
                //删除整个课程的所有课时
                DiskIOThreadPool.getExecutor().execute(new Runnable() {
                    @Override
                    public void run() {
                        for (CoursewareVo deleteCourse : targetCVArrayList) {
                            VideoCacheUtils.deleteDownloadedVideoNew(deleteCourse);
                        }
                        if(index == len -1){
                            if(callBack != null){
                                callBack.onSuccess(len);
                            }
                        }
                    }
                });
            }
        }

    }

    @Override
    public void deleteAllDownloadingVideosAsync(final OfflineCacheBaseContract.IDiskInfoHandleCallBack callBack) {
        if(mPresenter == null || mPresenter.getViewContext() == null) {
            if(callBack != null){
                callBack.onFailed("context is null");
            }
            return;
        }
        OfflineCacheDownloadManager.getInstance().pauseAllDownloadingTask(sourceType);
        final List<DownloadInfo> downloadInfoList = OfflineCacheDownloadManager.getInstance().getAllTask(false,
                sourceType);
        // 执行异步删除文件操作
        final File cacheFile = mPresenter.getViewContext().getCacheDir();
        DiskIOThreadPool.getExecutor().execute(new Runnable() {
            @Override
            public void run() {
                //删除cache目录下正在下载的文件信息
                boolean result = VideoCacheUtils.removeAllDownloadingFileNew(cacheFile, sourceType);
                FCLog.e(TAG, "file delete successful? : " + result);
            }
        });

        final int len = downloadInfoList.size();
        if(len == 0){
            if(callBack != null){
                callBack.onSuccess(0);
            }
            return;
        }
        for (int i = 0 ; i < len ; i++) {
            final int index = i;
            DownloadInfo downloadInfo = downloadInfoList.get(i);
            //删除在 SD 卡上缓存的文件
            ArrayList<CoursewareVo> downloadingInfos = mDownloadingVideos.get(TrainHelperUtil.getFileNameFromUrl(downloadInfo.getUrl()) + ".json");
            CoursewareVo info = null;
            if (downloadingInfos != null) {
                for (CoursewareVo temp : downloadingInfos) {
                    if (downloadInfo.getTaskKey().equals(TrainHelperUtil.getFileTaskKey(temp))) {
                        info = temp;
                        break;
                    }
                }
            }
            if (info == null) {
                if(index == len -1){
                    if(callBack != null){
                        callBack.onSuccess(len);
                    }
                }
                continue;
            }

            final CoursewareVo finalInfo = info;
            DiskIOThreadPool.getExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    VideoCacheUtils.deleteDownloadedVideoNew(finalInfo);
                    if(index == len -1){
                        if(callBack != null){
                            callBack.onSuccess(len);
                        }
                    }
                }
            });
        }
    }

    @Override
    public void updateAfterDataChangedHappened() {
        if(mPresenter == null || mPresenter.getViewContext() == null){
            return;
        }
        readCacheInfoFromLocalAsync(mPresenter.getViewContext(),
                new OfflineCacheBaseContract.IDiskInfoHandleCallBack() {
                    @Override
                    public void onSuccess(Object result) {
                        updateCachingStatus();
                        updateFolderStateAndName();
                        updateStorageSizePb();
                    }

                    @Override
                    public void onFailed(String errCode) {

                    }
                });

    }

    @Override
    public void handleTaskOnProgress(DownloadInfo downloadInfo) {
        // 伙伴学堂和内训课程的下载任务要分开展示
        final DownloadInfoSimple downloadInfoSimple = getSimpleDownloadInfo(downloadInfo);
        if(checkEaAndTrainType(downloadInfoSimple)){
            return;
        }
        updateAfterDataChangedHappened();
    }

    @Override
    public void handleTaskOnFinish(DownloadInfo downloadInfo) {
        if(mPresenter == null || mPresenter.getViewContext() == null){
            return;
        }
        final DownloadInfoSimple downloadInfoSimple = getSimpleDownloadInfoIgnoreSourceType(downloadInfo);
        if(downloadInfoSimple == null){
            return;
        }
        FCLog.i(TAG, "----onFinish-----" + downloadInfoSimple.fileName);
        super.handleTaskOnFinish(downloadInfoSimple, new OfflineCacheBaseContract.IDiskInfoHandleCallBack() {
            @Override
            public void onSuccess(Object result) {
                updateAfterDataChangedHappened();
            }

            @Override
            public void onFailed(String errCode) {
                FCLog.i(TAG, "----handleTaskOnFinish failed-----errCode: " + errCode);
            }
        });

    }

    private void updateCachingStatus(){
        ArrayList<DownloadInfo> infos = OfflineCacheDownloadManager.getInstance().getAllTask(false, sourceType);
        if(infos == null || mDownloadedVideos == null){
            return;
        }
        int mCachingNum = infos.size();
//        ArrayList<CoursewareVo> localCachedVideos = VideoCacheUtils.getLocalCachedVideosNew();
//        for (CoursewareVo dataBean : mDownloadedVideos) {
//            for (CoursewareVo newBean : localCachedVideos) {
//                if (newBean.id == dataBean.id) {
//                    newBean.isCheck = dataBean.isCheck;
//                    break;
//                }
//            }
//        }
//
//        mDownloadedVideos.clear();
//        mDownloadedVideos.addAll(localCachedVideos);
//        classifyDownloadVideos();
        if(mPresenter != null){
            mPresenter.updateCachingStatus(mCachingNum, mDownloadedVideos.size());
//            if(mDownloadedVideos.size() > 0){
//                mPresenter.notifyDataSetChanged();
//            }
        }

        updateCachingFolderView();
    }

    private void updateCachingFolderView(){
        if(mPresenter == null){
            return;
        }
        mDownloadState.downloadedVideoSize = mDownloadedVideos.size();

        long totalNetWorkSpeed = 0;
        long totalCachedSize = 0;
        long totalVideoFileSize = 0;
        ArrayList<DownloadInfo> allTask = OfflineCacheDownloadManager.getInstance().getAllTask(false, sourceType);
        HashMap<String, ArrayList<CoursewareVo>> map = mDownloadingVideos;

        int cachingTaskNum = allTask.size();

        if (cachingTaskNum == 0 || map.size() == 0) {
            if(mPresenter != null){
                mPresenter.updateCachingFolderView(mDownloadState);
            }
            return;
        } else {
            //有正在缓存的视频
            float pastTime = (System.currentTimeMillis() - lastTimeMilliSec) / 1000f;
            for (DownloadInfo downloadInfo : allTask) {
                long cachedLength = downloadInfo.getDownloadLength();
                totalCachedSize += cachedLength;
                long totalLength = downloadInfo.getTotalLength();
                totalVideoFileSize += totalLength;
                if (lastDownloadSize.get(downloadInfo.getTaskKey()) == null || pastTime <= 0) {
                    totalNetWorkSpeed += downloadInfo.getNetworkSpeed();
                } else {
                    totalNetWorkSpeed += ((downloadInfo.getDownloadLength() - lastDownloadSize.get(downloadInfo.getTaskKey())) / pastTime);
                }
                lastDownloadSize.put(downloadInfo.getTaskKey(), downloadInfo.getDownloadLength());
                lastTimeMilliSec = System.currentTimeMillis();
            }
            mDownloadState.mCachedTotalSize += totalCachedSize;//占用空间加上正在缓存中的视频已缓存的文件大小；
        }
        mDownloadState.cachingTaskNum = cachingTaskNum;
        mDownloadState.downloadingVideoSize = map.size();
        mDownloadState.totalNetWorkSpeed = totalNetWorkSpeed;
        mDownloadState.totalCachedSize = totalCachedSize;
        mDownloadState.totalVideoFileSize = totalVideoFileSize;
        if(mPresenter != null){
            mPresenter.updateCachingFolderView(mDownloadState);
        }
    }


    /**
     * 更新文件夹的状态和名字
     */
    private void updateFolderStateAndName() {
        ArrayList<DownloadInfo> infos = OfflineCacheDownloadManager.getInstance().getAllTask(false, sourceType);
        if(infos == null || mDownloadingVideos == null){
            return;
        }
        if (infos.size() == 0 || mDownloadingVideos.size() == 0) {
            return;
        }
        boolean isAllCancel = true;
        //可能需要判断新老版本下载的状态
        for (DownloadInfo downloadInfo : infos) {
            if (downloadInfo.getState() != DownloadManager.PAUSE
                    && downloadInfo.getState() != DownloadManager.ERROR
                    && downloadInfo.getState() != DownloadManager.NONE) {
                isAllCancel = false;
                break;
            }
        }

        for (int i=0; i<infos.size(); i++) {
            ArrayList<CoursewareVo> downloadingInfos = mDownloadingVideos.get(
                    TrainHelperUtil.getFileNameFromUrl(infos.get(i).getUrl()) + ".json");
            CoursewareVo info = null;
            if (downloadingInfos != null) {
                for (CoursewareVo temp : downloadingInfos) {
                    if (infos.get(i).getTaskKey().equals(TrainHelperUtil.getFileTaskKey(temp))) {
                        info = temp;
                        break;
                    }
                }
            }
            if (info == null) {
                continue;
            }

            if(mPresenter != null){
                mPresenter.updateFolderStateAndName(isAllCancel, infos.size() > 1 ? info.courseName : info.name);
            }
        }
    }


    /**
     * 将已经下载好的视频进行分类
     */
    private void classifyDownloadVideos() {
        /**
         * adapter 绑定的数据，必须在UI线程更新, see:
         * FATAL EXCEPTION: main
         * Process: com.facishare.fs, PID: 14294
         * java.lang.IllegalStateException:
         * The content of the adapter has changed but ListView did not receive a notification.
         * Make sure the content of your adapter is not modified from a background thread,
         * but only from the UI thread.
         * Make sure your adapter calls notifyDataSetChanged() when its content changes
         * */
        mUIHandler.post(new Runnable() {
            @Override
            public void run() {
                ArrayMap<Integer, ArrayList<CoursewareVo>> oldMapData =
                        new ArrayMap<Integer, ArrayList<CoursewareVo>>(mClassifiedDownloadedVideos);
                mClassifiedDownloadedVideos.clear();
                for (CoursewareVo coursewareVo : mDownloadedVideos) {
                    ArrayList<CoursewareVo> coursewareVoArrayList = mClassifiedDownloadedVideos.get(coursewareVo.courseId);
                    if (coursewareVoArrayList == null) {
                        coursewareVoArrayList = new ArrayList<>();
                        coursewareVoArrayList.add(coursewareVo);

                        if(oldMapData.get(coursewareVo.courseId) != null
                                && oldMapData.get(coursewareVo.courseId).size() > 0){
                            coursewareVoArrayList.get(0).isCheck = oldMapData.get(coursewareVo.courseId).get(0).isCheck;
                        }

                        mClassifiedDownloadedVideos.put(coursewareVo.courseId, coursewareVoArrayList);
                    } else {
                        coursewareVoArrayList.add(coursewareVo);
                    }
                }
                if(mPresenter != null){
                    mPresenter.notifyDataSetChanged();
                }
            }
        });

    }
}
