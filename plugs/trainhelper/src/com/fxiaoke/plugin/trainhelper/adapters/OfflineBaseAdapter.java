package com.fxiaoke.plugin.trainhelper.adapters;

import com.fxiaoke.plugin.trainhelper.business.offlinecache.base.OfflineCacheBaseContract;

import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;

/**
 * Created by ji<PERSON><PERSON> on 2017/6/15.
 */

public class OfflineBaseAdapter extends BaseAdapter implements OfflineCacheBaseContract.IBaseListAdaper {
    @Override
    public void setData(Object data) {

    }

    @Override
    public void release() {

    }

    @Override
    public int getCount() {
        return 0;
    }

    @Override
    public Object getItem(int position) {
        return null;
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        return null;
    }
}
