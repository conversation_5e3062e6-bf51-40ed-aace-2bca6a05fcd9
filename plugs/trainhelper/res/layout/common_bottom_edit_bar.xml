<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="wrap_content">
    <View
            android:background="#dedede"
            android:layout_width="match_parent"
            android:layout_height="1px"
    />
    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="68dp"
            android:background="#fff"
            android:gravity="center"
            android:orientation="horizontal"
    >

        <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/tv_cache_select_all"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="20dp"
                android:layout_weight="1"
                android:background="@drawable/trainhelper_cache_selectall_selector"
                android:gravity="center"
                i18n:fstext="th.base.view.select_all"
                android:textColor="#f09835"
                android:textSize="17sp"
                android:layout_marginEnd="20dp"
                android:layout_marginStart="12dp" />

        <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/tv_cache_delete"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_marginRight="12dp"
                android:layout_weight="1"
                android:background="@drawable/trainhelper_cache_video_begin_selector"
                android:gravity="center"
                i18n:fstext="xt.work_reply_inc_footer.text.remove"
                android:textColor="#ffffff"
                android:textSize="17sp"
                android:layout_marginEnd="12dp" />
    </LinearLayout>

</LinearLayout>