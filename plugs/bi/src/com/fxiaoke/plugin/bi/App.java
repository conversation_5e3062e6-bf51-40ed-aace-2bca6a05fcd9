/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.bi;

import com.alibaba.android.bindingx.plugin.weex.BindingX;
import com.facishare.fs.js.ava.AvaJsapiActionHandlerManager;
import com.facishare.fs.js.weex.component.WXJsApiWeb;
import com.fxiaoke.fscommon.base.FSApplicationLike;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.plugin.bi.jshandlers.BiOpportunityList;
import com.fxiaoke.plugin.bi.jshandlers.BiSendFeed;
import com.fxiaoke.plugin.bi.jshandlers.BiShowActionSheet;
import com.fxiaoke.plugin.bi.jshandlers.BiShowFeedDetail;
import com.fxiaoke.plugin.bi.jshandlers.BiShowFeedList;
import com.fxiaoke.plugin.bi.jshandlers.BiTransmitData;
import com.fxiaoke.plugin.bi.weex.BiSelectModule;
import com.taobao.weex.WXSDKEngine;
import com.taobao.weex.common.WXException;

import android.app.Application;
import android.content.Intent;

/**
 * Created by xiangd on 2016/11/29.
 */
public class App extends FSApplicationLike {

    public App(Application application, int tinkerFlags, boolean tinkerLoadVerifyFlag,
               long applicationStartElapsedTime, long applicationStartMillisTime, Intent tinkerResultIntent) {
        super(application, tinkerFlags, tinkerLoadVerifyFlag, applicationStartElapsedTime, applicationStartMillisTime, tinkerResultIntent);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        try {
            WXSDKEngine.registerModule("BiSelectModule",BiSelectModule.class);
            WXSDKEngine.registerComponent("fsweb", WXJsApiWeb.class);
            initAva();
            try {
                BindingX.register();
                FCLog.i("BindingX","BindingX moudle init success");
            } catch (WXException e){
                e.printStackTrace();
                FCLog.i("BindingX","BindingX moudle init failed begin");
                FCLog.i("BindingX",e.getMessage());
                FCLog.i("BindingX","BindingX moudle init failed end");
            }
        } catch (WXException e) {
            e.printStackTrace();
        }
    }

    private void initAva(){
        AvaJsapiActionHandlerManager.registerHandlerMap(BiShowActionSheet.ACTION_SHOW_ACTION_SHEET,
                new BiShowActionSheet());
        AvaJsapiActionHandlerManager.registerHandlerMap(BiTransmitData.ACTION_Transmit,
                new BiTransmitData());
        AvaJsapiActionHandlerManager.registerHandlerMap(BiSendFeed.ACTION_sendFeed,
                new BiSendFeed());
        AvaJsapiActionHandlerManager.registerHandlerMap(BiOpportunityList.ACTION_OPPORTUNITY_LIST,
                new BiOpportunityList());
        AvaJsapiActionHandlerManager.registerHandlerMap(BiShowFeedDetail.ACTION_SHOW_FEED_DETAIL,
                new BiShowFeedDetail());
        AvaJsapiActionHandlerManager.registerHandlerMap(BiShowFeedList.ACTION_SHOW_FEED_LIST,
                new BiShowFeedList());
    }

}
