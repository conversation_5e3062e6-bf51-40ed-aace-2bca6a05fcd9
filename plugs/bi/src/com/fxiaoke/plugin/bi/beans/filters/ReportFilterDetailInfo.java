/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.bi.beans.filters;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fxiaoke.plugin.bi.beans.filters.CommonDataBean.DataTypeEnum;
import com.fxiaoke.plugin.bi.type.DateDbFieldNameEnum;

/**
 * 报表详情页，数据实体
 * <p/>
 * Created by xiangd on 2016/12/21.
 */
public class ReportFilterDetailInfo implements Serializable {

    @JSONField(name = "tabs")
    public List<CommonDataBean> tabs;

    @JSONField(name = "conf")
    public FilterConfigInfo conf;

    @JSONField(name = "mode")
    public String mode;//default 旧组件（不传就是 default）  slider 新的侧滑组件

    @JSONField(serialize = false, deserialize = false)
    public CommonDataBean getCommonDataBean(DataTypeEnum dataTypeEnum) {
        if (tabs == null || tabs.isEmpty() || dataTypeEnum == null) {
            return null;
        }

        int size = tabs.size();
        for (int position = 0; position < size; position++) {
            CommonDataBean dataBean = tabs.get(position);
            if (dataBean.type == dataTypeEnum.getType()) {
                dataBean.setPosition(position);
                return dataBean;
            }
        }
        return null;
    }

    /**
     * 获取数据范围列表数据
     */
    @JSONField(serialize = false, deserialize = false)
    public DataScopeListInfo getDataScopeListBean() {
        CommonDataBean dataBean = getCommonDataBean(DataTypeEnum.DataScopeList);
        DataScopeListInfo dataScopeListInfo = null;
        if (dataBean != null) {
            dataScopeListInfo = (DataScopeListInfo) dataBean.getResultData();
        }
        return dataScopeListInfo;
    }

    /**
     * 获取表头筛选列表数据
     */
    @JSONField(serialize = false, deserialize = false)
    public DataScopeListInfo getTableFilterBean() {
        CommonDataBean dataBean = getCommonDataBean(DataTypeEnum.TableHeadFilter);
        if (dataBean != null) {
            DataScopeListInfo info = new DataScopeListInfo();
            info.filterList=new ArrayList<>();
            info.filterList.add((DataScopeListInfo.DataFilterBean) dataBean.getResultData());
            return info;
        }
        return null;
    }

    /**
     * 获取ActionBar列表
     */
    @JSONField(serialize = false, deserialize = false)
    public List<CommonDataBean> getTabsList() {
        List<CommonDataBean> list = new ArrayList<>();
        if (tabs != null && !tabs.isEmpty()) {
            for(CommonDataBean bean : tabs) {
                bean.initResultDataIfNeed();
                list.add(bean);
            }
        }
        return list;
    }

    @JSONField(serialize = false, deserialize = false)
    public Object getConfigJO(){
        if (conf == null)
            return null;
        return JSON.toJSON(conf);
    }
}
