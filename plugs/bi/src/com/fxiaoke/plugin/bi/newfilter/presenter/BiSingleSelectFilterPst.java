package com.fxiaoke.plugin.bi.newfilter.presenter;

import java.util.List;

import com.facishare.fs.metadata.list.newfilter.mvp.BaseFilterMView;
import com.facishare.fs.metadata.list.newfilter.mvp.IBaseFilterModel;
import com.facishare.fs.metadata.list.newfilter.mvp.mviews.DeletableOptionFilterMView;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.plugin.bi.data_scope.fields.SelectField;
import com.fxiaoke.plugin.bi.fragment.editor.SingleSelectEnumFrag;
import com.fxiaoke.plugin.bi.newfilter.models.BiSelectFilterModel;
import com.fxiaoke.plugin.bi.type.FieldTypeEnum;
import com.fxiaoke.plugin.bi.ui.DataEditActivity;

import android.app.Activity;
import android.content.Intent;

/**
 * Created by zhouz on 12/26/20.
 */
public class BiSingleSelectFilterPst extends BiAbsSelectFilterPst {
    @Override
    protected void onSelectOption(MultiContext context,
                             DeletableOptionFilterMView<BiSelectFilterModel> modelView,
                             BiSelectFilterModel model) {
        SelectField field = model.getDataScopeInfo();
        startActivityForResult(modelView, DataEditActivity.getSingleSelectEnumIntent(context.getContext(),
                field.getSelectableEnumItemList()), REQUEST_CODE);
    }

    @Override
    public void onActivityResult(MultiContext context,
                                 BaseFilterMView<BiSelectFilterModel> modelView,
                                 BiSelectFilterModel model, int requestCode, int resultCode,
                                 Intent data) {
        super.onActivityResult(context, modelView, model, requestCode, resultCode, data);
        if (resultCode != Activity.RESULT_OK) {
            return;
        }
        SelectField field = model.getDataScopeInfo();
        List<String> dataList =
                (List<String>) data.getSerializableExtra(SingleSelectEnumFrag.RESULT_CHECKED_ID_LIST);
        field.setResult(dataList);
        refreshContentAndSelectedView(modelView);
    }

    @Override
    public boolean accept(IBaseFilterModel model) {
        if(!super.accept(model)){
            return false;
        }
        SelectField field = ((BiSelectFilterModel)model).getDataScopeInfo();
        return field.getFieldTypeEnum() == FieldTypeEnum.SingleSelectEnum
                || field.getFieldTypeEnum() == FieldTypeEnum.RefEnum;
    }
}
