/*
 * Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.bi.data;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.facishare.fs.metadata.ILoadingView;
import com.fxiaoke.plugin.bi.beans.filters.DataScopeListInfo;
import com.fxiaoke.plugin.bi.beans.filters.ReportFilterDetailInfo;
import com.fxiaoke.plugin.bi.data_scope.DataScopeArg;

import android.app.Activity;
import android.util.Pair;
import androidx.annotation.NonNull;
import android.text.TextUtils;
import io.reactivex.Single;
import io.reactivex.SingleEmitter;
import io.reactivex.SingleObserver;
import io.reactivex.SingleOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * 详情筛选器数据封装
 * Created by zhouz on 2018/5/31.
 */
public class RptFilterInfoWrapper {
    /**
     * （详情页）
     * 报表详情页数据对象
     */
    private ReportFilterDetailInfo mReportFilterDetailInfo;
    /**
     * （详情页）
     * 上次筛选数据JSON串
     */
    private String mLastFilterJson;

    public ReportFilterDetailInfo getReportFilterDetailInfo() {
        return mReportFilterDetailInfo;
    }

    public void setReportFilterDetailInfo(ReportFilterDetailInfo mReportFilterDetailInfo) {

        this.mReportFilterDetailInfo = mReportFilterDetailInfo;
        this.mLastFilterJson = getReportDetailFilterJson();
    }

    public DataScopeArg getDataScoeArg(){
        if (mReportFilterDetailInfo != null) {
            DataScopeListInfo dataScopeListBean = mReportFilterDetailInfo.getDataScopeListBean();
            if (dataScopeListBean != null) {
                DataScopeArg arg = new DataScopeArg();
                arg.data=dataScopeListBean;
                arg.conf=mReportFilterDetailInfo.conf;
                return arg;
            }
        }
        return null;
    }

    public DataScopeArg getTableFilterArg(){
        if (mReportFilterDetailInfo != null) {
            DataScopeListInfo dataScopeListBean = mReportFilterDetailInfo.getTableFilterBean();
            if (dataScopeListBean != null) {
                DataScopeArg arg = new DataScopeArg();
                arg.data=dataScopeListBean;
                arg.conf=mReportFilterDetailInfo.conf;
                return arg;
            }
        }
        return null;
    }

    /**
     * （详情页）
     * 获取数据范围筛选器列表
     */
    @NonNull
    public List<DataScopeListInfo.DataFilterBean> getDataScopeFilterList() {
        List<DataScopeListInfo.DataFilterBean> dataFilterBeanList = new ArrayList<>();
        if (mReportFilterDetailInfo != null) {
            DataScopeListInfo dataScopeListBean = mReportFilterDetailInfo.getDataScopeListBean();
            if (dataScopeListBean != null && dataScopeListBean.getFilterList() != null) {
                dataFilterBeanList.addAll(dataScopeListBean.getFilterList());
            }
        }
        return dataFilterBeanList;
    }

    public interface OnFilterChangedCallback {

        void onChanged(boolean isChange, String filterJson);
    }

    /**
     * （详情页）
     * 检测筛选器数据是否发生改变
     */
    public void checkFilterJsonChanged(Activity context, OnFilterChangedCallback callback) {
        Single.create(new SingleOnSubscribe<Pair<Boolean, String>>() {
            @Override
            public void subscribe(@io.reactivex.annotations.NonNull SingleEmitter<Pair<Boolean, String>> emitter)
                    throws Exception {
                String newFilterJson = getReportDetailFilterJson();
                boolean isChanged = !TextUtils.equals(mLastFilterJson, newFilterJson);
                mLastFilterJson=newFilterJson;
                emitter.onSuccess(new Pair<>(isChanged, newFilterJson));
            }
        }).subscribeOn(Schedulers.computation())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SingleObserver<Pair<Boolean, String>>() {
                    @Override
                    public void onSubscribe(@io.reactivex.annotations.NonNull Disposable d) {
                        ILoadingView.ContextImplProxy.showLoading(context);
                    }

                    @Override
                    public void onSuccess(@io.reactivex.annotations.NonNull Pair<Boolean, String> rst) {
                        ILoadingView.ContextImplProxy.dismissLoading(context);
                        if (callback != null) {
                            callback.onChanged(rst.first, rst.second);
                        }
                    }

                    @Override
                    public void onError(@io.reactivex.annotations.NonNull Throwable e) {
                        ILoadingView.ContextImplProxy.dismissLoading(context);
                    }
                });
    }

    /**
     * （详情页）
     *  返回详情页筛选器数据json
     */
    public String getReportDetailFilterJson() {
        if (mReportFilterDetailInfo != null) {
            return JSON.toJSONString(mReportFilterDetailInfo);
        }
        return null;
    }
}
