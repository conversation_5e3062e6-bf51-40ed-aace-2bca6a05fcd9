<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/layout_show_date"
        android:layout_width="match_parent"
        android:layout_height="@dimen/content_item_height"
        android:layout_marginTop="12dp"
        android:background="@color/white"
        android:paddingLeft="@dimen/content_paddingLeft"
        android:paddingRight="@dimen/content_paddingRight"
            android:paddingEnd="@dimen/content_paddingRight"
            android:paddingStart="@dimen/content_paddingLeft">

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/tv_showDate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="12dp"
            android:ellipsize="end"
            android:gravity="start"
            android:singleLine="true"
            android:text="2015-11-25"
            android:textColor="@color/tv_title_one_color"
            android:textSize="16dp"
                android:layout_marginEnd="12dp"
                android:layout_alignParentStart="true" />

        <ImageView
            android:id="@+id/img_start_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:background="@drawable/list_item_nav_arrow"
                android:layout_alignParentEnd="true" />

    </RelativeLayout>


</LinearLayout>


