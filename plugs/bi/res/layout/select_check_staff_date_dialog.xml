<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:gravity="center"
              android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="#fafafa"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.facishare.fs.sizectrlviews.SizeControlButton
            android:id="@+id/btn_cancel"
            android:layout_width="60dp"
            android:layout_height="30dp"
            android:background="@null"
            i18n:fstext="commonfunc.dialog_fragment_fsmail_choose_attachment_menu.text.cancel"
            android:textColor="#5196f0"/>

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/txtSetTypeText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            i18n:fstext="crm.layout.item_date_choice_model1.1952"
            android:textColor="#333333"
            android:textSize="16sp"/>

        <com.facishare.fs.sizectrlviews.SizeControlButton
            android:id="@+id/btn_ok"
            android:layout_width="60dp"
            android:layout_height="30dp"
            android:background="@null"
            i18n:fstext="av.common.string.confirm"
            android:textColor="#5196f0"/>
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/datePicker"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="0dp">

            <com.fxiaoke.cmviews.wheels.WheelWhiteStyleView
                android:id="@+id/year"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="120dp"
                android:visibility="gone"/>

            <com.fxiaoke.cmviews.wheels.WheelWhiteStyleView
                android:id="@+id/month"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="120dp"
                android:visibility="gone"/>

            <com.fxiaoke.cmviews.wheels.WheelWhiteStyleView
                android:id="@+id/day"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="120dp"
                android:visibility="gone"/>

            <com.fxiaoke.cmviews.wheels.WheelWhiteStyleView
                android:id="@+id/hour"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:visibility="gone"/>

        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_centerVertical="true">

            <View
                android:layout_width="wrap_content"
                android:layout_height="1px"
                android:layout_alignParentTop="true"
                android:background="#cccccc"/>

            <View
                android:layout_width="wrap_content"
                android:layout_height="1px"
                android:layout_alignParentBottom="true"
                android:background="#cccccc"/>
        </RelativeLayout>
    </RelativeLayout>
</LinearLayout>