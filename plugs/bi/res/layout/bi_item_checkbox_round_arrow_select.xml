<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:background="@drawable/bg_common_item_selector"
              android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:descendantFocusability="blocksDescendants"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/content_paddingLeft"
        android:paddingRight="@dimen/content_paddingRight"
            android:paddingEnd="@dimen/content_paddingRight"
            android:paddingStart="@dimen/content_paddingLeft">

        <ImageView
            android:id="@+id/img_item_selected"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_centerVertical="true"
            android:background="@drawable/checkbox_round_selector"
            android:visibility="visible"/>

        <ImageView
            android:id="@+id/img_next_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:background="@drawable/list_item_nav_arrow"
            android:visibility="invisible"
                android:layout_alignParentEnd="true" />

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/tv_item_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="14dp"
            android:layout_marginRight="5dp"
            android:layout_toLeftOf="@id/img_next_arrow"
            android:layout_toRightOf="@id/img_item_selected"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:singleLine="true"
            i18n:fstext="bi.layout.item_checkbox_multi_layout_search.2149"
            android:textColor="#333333"
            android:textSize="14dp"
                android:layout_toEndOf="@id/img_item_selected"
                android:layout_toStartOf="@id/img_next_arrow"
                android:layout_marginEnd="5dp"
                android:layout_marginStart="14dp" />

    </RelativeLayout>

    <View
        android:id="@+id/img_bottom_line"
        style="@style/style_divider_line_no_margin"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="48dp"
            android:layout_marginStart="48dp" />


</LinearLayout>