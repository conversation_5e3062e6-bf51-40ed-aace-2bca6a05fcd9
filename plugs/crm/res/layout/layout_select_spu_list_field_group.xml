<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/root_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

    <LinearLayout
            android:id="@+id/spu_top_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

        <TextView
                android:id="@+id/tv_spu_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="#3b4047"
                android:textSize="15dp" />

        <ImageView
                android:id="@+id/btn_top_add_product"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginRight="12dp"
                android:paddingTop="10dp"
                android:paddingRight="10dp"
                android:paddingBottom="10dp"
                android:scaleType="centerInside"
                android:src="@drawable/btn_add_product"
                android:layout_marginEnd="12dp"
                android:paddingEnd="10dp" />
    </LinearLayout>

    <LinearLayout
            android:id="@+id/ll_meta_info_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

        <com.fxiaoke.cmviews.view.DynamicViewStub
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="12dp"
                app:id="@+id/left_extra_stub"
                android:layout_marginEnd="12dp" />

        <com.fxiaoke.cmviews.view.DynamicViewStub
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="12dp"
                android:layout_weight="1"
                app:id="@+id/left_field_stub"
                android:layout_marginEnd="12dp" />

        <com.fxiaoke.cmviews.view.DynamicViewStub
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="12dp"
                app:id="@+id/right_field_stub"
                android:layout_marginEnd="12dp" />

        <com.fxiaoke.cmviews.view.DynamicViewStub
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="12dp"
                app:id="@+id/right_extra_stub"
                android:layout_marginEnd="12dp" />
    </LinearLayout>

    <!--加减按钮-->
    <ImageView
            android:id="@+id/btn_bottom_add_product"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginRight="12dp"
            android:paddingTop="10dp"
            android:paddingRight="10dp"
            android:paddingBottom="10dp"
            android:scaleType="centerInside"
            android:src="@drawable/btn_add_product"
            android:layout_marginEnd="12dp"
            android:paddingEnd="10dp" />
</LinearLayout>