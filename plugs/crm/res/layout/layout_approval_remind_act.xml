<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_default"
        android:orientation="vertical">

    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="48dp" />

    <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

        <com.fxiaoke.cmviews.viewpager.ViewPagerCtrl
                android:id="@+id/layout_pager"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
    </RelativeLayout>

    <FrameLayout
            android:id="@+id/select_obj_frag_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white" />
</LinearLayout>