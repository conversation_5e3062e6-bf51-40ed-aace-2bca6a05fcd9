<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/tag_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="24dp"
        android:textColor="@color/font_gray_1"
        android:textSize="@dimen/text_size_m"
        i18n:fstext="crm.layout.classify_holiday_list_title_layout.8043"
            android:layout_marginEnd="24dp"
            android:layout_marginStart="12dp" />

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/content_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15dp"
        android:layout_marginTop="15dp"
        android:layout_marginRight="12dp"
        android:layout_weight="1"
        android:background="@color/transparence"
        android:textColor="@color/font_black_1"
        android:textColorHint="@color/font_gray_3"
        android:textSize="@dimen/text_size_l"
        i18n:fshint="crm.layout.layout_crm_data_time.1894"
            android:layout_marginEnd="12dp" />

    <ImageView
        android:id="@+id/arrow_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="fitCenter"
        android:layout_marginRight="12dp"
        android:src="@drawable/contact_list_arrow"
            android:layout_marginEnd="12dp" />
</LinearLayout>
