<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:app="http://schemas.android.com/apk/res-auto"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#ffffff">
    <fragment
            android:id="@id/bottom_action_bar_frag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:name="com.fxiaoke.plugin.crm.common_view.BottomActionBarFrag"
            android:layout_alignParentBottom="true"/>
    <com.fxiaoke.fscommon_res.view.calendar.FsCalendarLayout
            android:id="@+id/calendar"
            android:layout_below="@id/title"
            android:layout_above="@id/bottom_action_bar_frag"
            app:multiChoose="false"
            app:enableCollapse="true"
            app:collapseMode="middle"
            app:calendarMode="week"
            app:itemHeight="45dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
        <LinearLayout
                android:id="@+id/list_frag"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
    </com.fxiaoke.fscommon_res.view.calendar.FsCalendarLayout>
    <LinearLayout
            android:id="@+id/ll_list_view_visit_frag"
            android:layout_below="@id/title"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
</RelativeLayout>
