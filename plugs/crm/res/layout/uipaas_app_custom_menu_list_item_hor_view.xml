<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2022 Facishare Technology Co., Ltd. All Rights Reserved.
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/LinearLayout_h_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

    <RelativeLayout
            android:id="@+id/app_custom_menu_list_item_container"
            android:layout_width="match_parent"
            android:layout_height="@dimen/app_custom_menu_list_item_height"
            android:gravity="center_vertical">

        <com.caverock.androidsvg.SVGImageView
                android:id="@+id/image_view_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="12dp"
                android:src="@drawable/my_list_tx_icon" />

        <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="12dp"
                android:layout_marginLeft="12dp"
                android:layout_toEndOf="@+id/image_view_icon"
                android:layout_toRightOf="@+id/image_view_icon"
                android:gravity="center">

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                    android:id="@+id/textView_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text=""
                    android:textColor="#333333"
                    android:textSize="16dp" />

            <ImageView
                    android:id="@+id/my_RemindIcon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toEndOf="@+id/textView_title"
                    android:layout_toRightOf="@id/textView_title"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_notify_remind"
                    android:visibility="gone"
                     />
        </RelativeLayout>

        <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/textView_count_tx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:layout_toLeftOf="@+id/imageView_arrow_m"
                android:textColor="#999999"
                android:textSize="16dp"
                android:visibility="gone"
                android:layout_marginEnd="10dp"
                android:layout_toStartOf="@+id/imageView_arrow_m" />

        <TextView
                android:id="@+id/textView_notify"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="6dp"
                android:layout_toLeftOf="@id/imageView_arrow_m"
                android:gravity="center"
                android:paddingLeft="6dp"
                android:paddingTop="2dp"
                android:paddingRight="6dp"
                android:paddingBottom="2dp"
                android:textColor="#ffffff"
                android:textSize="10sp"
                android:visibility="gone"
                android:paddingEnd="6dp"
                android:paddingStart="6dp"
                android:layout_marginEnd="6dp"
                android:layout_toStartOf="@id/imageView_arrow_m" />

        <TextView
                android:id="@+id/textView_notify_white_bg_red_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="6dp"
                android:layout_toLeftOf="@id/imageView_arrow_m"
                android:gravity="center"
                android:paddingLeft="6dp"
                android:paddingTop="2dp"
                android:paddingRight="6dp"
                android:paddingBottom="2dp"
                android:textColor="#F45A22"
                android:textSize="10sp"
                android:visibility="gone"
                android:layout_toStartOf="@id/imageView_arrow_m"
                android:paddingEnd="6dp"
                android:layout_marginEnd="6dp"
                android:paddingStart="6dp" />

        <ImageView
                android:id="@+id/imageView_arrow_m"
                android:layout_width="10dp"
                android:layout_height="15dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="24dp"
                android:src="@drawable/crm_timeline_jumparrow"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="24dp" />
    </RelativeLayout>

    <View
            android:id="@+id/TextView_line_my"
            style="@style/list_item_divider_line_bg"
             />
</LinearLayout>