<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    android:background="@color/bg_default"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layout_alignParentRight="true"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:paddingTop="9.5dp"
        android:paddingBottom="9.5dp"
        android:textColor="#91959E"
        android:textSize="12dp"
        android:id="@+id/high_sea_name"
            android:layout_alignParentEnd="true"
            android:paddingStart="12dp"
            android:paddingEnd="12dp" />
    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        i18n:fstext="crm.layout.high_seah_list_center_bar.1988"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:paddingTop="9.5dp"
        android:paddingBottom="9.5dp"
        android:textColor="#3487e2"
        android:textSize="12dp"
        android:visibility="gone"
        android:id="@+id/rule_btn"
            android:paddingStart="12dp"
            android:layout_alignParentEnd="true"
            android:paddingEnd="12dp" />
    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        i18n:fstext="crm.layout.high_seah_list_center_bar.1987"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:paddingTop="9.5dp"
        android:paddingBottom="9.5dp"
        android:visibility="gone"
        android:textColor="#3487e2"
        android:textSize="12dp"
        android:id="@+id/record_btn"
            android:layout_alignParentEnd="true"
            android:paddingStart="12dp"
            android:paddingEnd="12dp" />
</LinearLayout>