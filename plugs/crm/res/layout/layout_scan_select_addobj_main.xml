<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:background="@color/crm_add_bg"
              android:orientation="vertical">

    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
        android:id="@+id/title"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/title_height"/>

    <ListView
        android:id="@+id/itemlist"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:dividerHeight="0dp"
        android:visibility="visible"></ListView>

    <LinearLayout
        android:id="@+id/empty_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:id="@+id/no_content_img"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="40dp"
            android:src="@drawable/fc_no_content_icon"/>

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/no_content_text"
            android:layout_width="170dp"
            android:layout_height="wrap_content"
            i18n:fstext="crm.layout.layout_scan_select_addobj_main.1831"
            android:textColor="#bdbdbd"
            android:textSize="14dp"
            />

        <com.facishare.fs.sizectrlviews.SizeControlButton
            android:id="@+id/no_content_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp"
            android:layout_marginTop="64dp"
            android:background="@drawable/btn_no_content_bg"
            android:gravity="center"
            i18n:fstext="common.xlist_fragment.guide.click_for_retry"
            android:textColor="@color/font_white_1"
            android:textSize="17dp"
            android:visibility="gone"
                android:layout_marginStart="12dp"
                android:layout_marginEnd="12dp" />
    </LinearLayout>
</LinearLayout>