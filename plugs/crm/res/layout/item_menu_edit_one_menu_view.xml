<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground">

    <com.caverock.androidsvg.SVGImageView
            android:id="@+id/menuIconView"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="25dp"/>
    <ImageView
            android:id="@+id/operIconView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@+id/menuIconView"
            android:layout_alignTop="@+id/menuIconView"
            android:scaleType="center"
            android:visibility="gone"
            android:layout_marginTop="-5dp"
            android:layout_marginLeft="-5dp"
            android:layout_toEndOf="@+id/menuIconView"
            android:layout_marginStart="-5dp" />

    <TextView
            android:id="@+id/txtCountView"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:layout_toRightOf="@+id/menuIconView"
            android:layout_marginLeft="-12dp"
            android:layout_marginTop="13dp"
            android:paddingLeft="4dp"
            android:minWidth="18dp"
            android:paddingRight="4dp"
            android:gravity="center"
            android:textColor="#FFFFFF"
            android:visibility="gone"
            android:textSize="10sp"
            android:paddingStart="4dp"
            android:layout_toEndOf="@+id/menuIconView"
            android:layout_marginStart="-12dp"
            android:paddingEnd="4dp" />

    <ImageView
            android:id="@+id/deleteIconView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="8dp"
            android:visibility="gone"
            android:layout_marginRight="8dp"
            android:scaleType="centerInside"
            android:layout_marginEnd="8dp"
            android:layout_alignParentEnd="true" />
    <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/menuTitleView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/menuIconView"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="25dp"
            android:layout_marginTop="6dp"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:textColor="#181C25"
            android:textSize="14dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp" />
</RelativeLayout>