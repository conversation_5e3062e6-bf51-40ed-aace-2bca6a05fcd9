<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
  -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

    <com.fxiaoke.cmviews.view.NoContentView
            android:id="@+id/empty_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"/>

    <LinearLayout
            android:id="@+id/display_view"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#f2f2f2">

        <TextView
                android:id="@+id/opportunity_amount_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="10sp"
                android:textColor="#181c25"
                android:layout_marginTop="10dp"
                android:layout_marginLeft="6dp"
                android:layout_marginStart="6dp" />

        <com.facishare.fs.metadata.list.flowstage.stageviews.ScrollStageView
                android:id="@+id/scroll_stage_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="8dp"
                android:layout_marginLeft="6dp"
                android:layout_marginRight="6dp"
                android:scrollbars="none"
                android:layout_marginEnd="6dp"
                android:layout_marginStart="6dp" />

        <androidx.viewpager.widget.ViewPager
                android:id="@+id/view_pager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
    </LinearLayout>

</RelativeLayout>
