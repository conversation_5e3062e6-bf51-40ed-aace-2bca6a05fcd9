<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/addContactToLocalLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical"
        android:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingBottom="16dp"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"
            android:paddingTop="16dp"
                android:paddingStart="12dp"
                android:paddingEnd="12dp">

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                i18n:fstext="crm.layout.item_auto_add_to_team_view.1985"
                    android:textColor="@color/model_title_right_title_color"
                    android:textSize="@dimen/text_size_m"/>

            <CheckBox
                android:id="@+id/cb_auto_add_to_team"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:background="@drawable/cbo_push_selector"
                android:button="@null"
                android:checked="true"
                    android:layout_marginStart="12dp" />
        </LinearLayout>

    </LinearLayout>
</LinearLayout>
