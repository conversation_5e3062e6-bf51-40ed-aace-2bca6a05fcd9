/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.StockCheckNoteObj.modelviews.table;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import com.facishare.fs.metadata.list.modelviews.ListContentAdapter;
import com.facishare.fs.metadata.modify.MetaModifyConfig;
import com.facishare.fs.metadata.modify.MetaModifyContext;
import com.facishare.fs.metadata.modify.modelviews.table.TableItemMView;
import com.facishare.fs.metadata.modify.modelviews.table.TableListItemArg;
import com.facishare.fs.modelviews.ModelView;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.plugin.crm.StockCheckNoteObj.StockCheckNoteObj;
import com.fxiaoke.plugin.crm.deliverynote.modelviews.table.BaseTableListAdapter;
import com.fxiaoke.plugin.crm.goodsreceivednote.GoodsReceivedNoteObj;

import android.text.TextUtils;

public class ModifyDetailFragTableListAdapter extends BaseTableListAdapter {
    public ModifyDetailFragTableListAdapter(MultiContext context, int scene) {
        super(context, scene);
        setAllowDelete(canShowAdd());//如果是显示添加按钮，就可以支持长按删除功能，否则不支持
    }

    @Override
    public ModelView createModelView(MultiContext context, int position, TableListItemArg listItemArg) {
        return new MyTableItemMVGroup(context);
    }

    public boolean canShowAdd(){
        MetaModifyConfig mConfig = MetaModifyContext.get(getMultiContext()).getModifyConfig();
        if(mConfig==null) return true;
        String lifeStatus = mConfig.getObjectData().getLifeStatus();
        return mConfig == null
                || !mConfig.isEditType()
                || TextUtils.equals(lifeStatus, GoodsReceivedNoteObj.Status.INEFFECTIVE);
    }

    @Override
    public boolean showAdd(TableListItemArg itemArg) {
        return canShowAdd();
    }

    public static class MyTableItemMVGroup extends TableItemMView {
        public MyTableItemMVGroup(MultiContext context) {
            super(context);
        }

        @Override
        protected ListContentAdapter<TableListItemArg> createContentAdapter() {
            return new ListContentAdapter<TableListItemArg>(){
                @Override
                public Set<String> leftFieldRenderBlackList(
                        TableListItemArg listItemArg) {
                    return new HashSet<>(Arrays.asList(StockCheckNoteObj.StockCheckNoteProductObj.NAME,
                            StockCheckNoteObj.StockCheckNoteProductObj.STOCK_ID,
                            StockCheckNoteObj.StockCheckNoteProductObj.STOCK_CHECK_NOTE_ID
                    ));
                }
            };
        }
    }
}
