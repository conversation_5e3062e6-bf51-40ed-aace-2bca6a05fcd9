/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.selectobject;

import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.jetbrains.annotations.NotNull;

import com.afollestad.materialdialogs.DialogFragmentWrapper;
import com.afollestad.materialdialogs.MaterialDialog;
import com.billy.cc.core.component.CCResult;
import com.facishare.fs.common_utils.JsonHelper;
import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.config.ISPOperator;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.beans.CrmObjectInfo;
import com.facishare.fs.metadata.beans.FilterScene;
import com.facishare.fs.metadata.beans.FindCrmObjectListResult;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.ObjectDataKeys;
import com.facishare.fs.metadata.beans.ObjectDescribe;
import com.facishare.fs.metadata.beans.Operator;
import com.facishare.fs.metadata.beans.fields.FilterInfo;
import com.facishare.fs.metadata.data.cache.selectCrmObj.SelectCrmObjectListCacheHelper;
import com.facishare.fs.metadata.data.source.MetaDataRepository;
import com.facishare.fs.metadata.list.beans.search_query.SearchQueryInfo;
import com.facishare.fs.metadata.list.select_obj.MetaDataSelectObjAct;
import com.facishare.fs.metadata.list.select_obj.picker.IOffLineAddOfSelectObjHook;
import com.facishare.fs.metadata.list.select_obj.picker.MultiObjectPicker;
import com.facishare.fs.metadata.list.select_obj.picker.PickMode;
import com.facishare.fs.metadata.list.select_obj.picker.PickObjConfig;
import com.facishare.fs.metadata.modify.backfill.BackFillInfo;
import com.facishare.fs.metadata.modify.backfill.BackFillInfos;
import com.facishare.fs.metadata.navigator.INavigator;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.facishare.fs.pluginapi.crm.beans.CrmObjWrapper;
import com.facishare.fs.pluginapi.crm.beans.CustomerInfo;
import com.facishare.fs.pluginapi.crm.beans.SelectCrmMultiObjInfo;
import com.facishare.fs.pluginapi.crm.beans.SelectCrmObjectMode;
import com.facishare.fs.pluginapi.crm.biz_api.ICustomer;
import com.facishare.fs.pluginapi.crm.biz_api.ISelectCrmObject;
import com.facishare.fs.pluginapi.crm.config.SelectCrmObjUtils;
import com.facishare.fs.pluginapi.crm.config.SelectMultiObjConfig;
import com.facishare.fs.pluginapi.crm.config.SelectVisitCustomerConfig;
import com.facishare.fs.pluginapi.crm.old_beans.AShortFCustomer;
import com.facishare.fs.pluginapi.crm.old_beans.SelectCustomer;
import com.facishare.fs.pluginapi.crm.type.CoreObjType;
import com.facishare.fs.pluginapi.crm.type.ICrmBizApiName;
import com.fxiaoke.cmviews.view.NoContentView;
import com.fxiaoke.cmviews.view.TopNoticeView;
import com.fxiaoke.fscommon.util.CommonDataContainer;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.fxlog.module.CrmLog;
import com.fxiaoke.plugin.crm.BaseActivity;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.common.MetaFieldKeys;
import com.fxiaoke.plugin.crm.selectcustomer.activity.SelectNearCustomerAct;
import com.fxiaoke.plugin.crm.selectobject.adapter.SelectCrmObjAdapter;
import com.fxiaoke.plugin.crm.selectobject.beans.SelectCrmObjBean;
import com.fxiaoke.plugin.crm.selectobject.mvp.presenter.SelectCrmObjPresenter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ListView;
import io.reactivex.Single;
import io.reactivex.SingleObserver;
import io.reactivex.SingleSource;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by lixn on 2016/12/13.
 * 混合的选对象列表页-对接feed和企信
 */

public class SelectCrmObjAct extends BaseActivity {
    private static final int MIN_CLICK_DELAY_TIME = 1000;
    private long lastClickTime = 0;
    public static final String KEY_SHOW_TOP_TIPS = "_key_show_top_tips";
    public static final String OVER_COUNT_PROMPT = "crm.selectobject.SelectCrmObjAct.885"/* 最多只能选%d条数据 */;
    public static final int REQUEST_CODE_SELECT_USER_DEFINED_OBJECT = 5757;
    public static final int REQUEST_CODE_SELECT_OBJECT = 5758;
    public static final int REQUEST_CODE_SELECT_CUSTOMER = 5759;//选择拜访类型客户对象
    private static final String USER_DEFINED_DATA = "user_defined_data";
    private static final String MAIN_OBJ_LIST = "main_obj_list";//从外勤选择的主对象
    private static final String CONFIG = "config";
    private static final String SELECTING_TYPE = "selecting_type";
    private static final String CUSTOMER_TYPE = "customerType";
    private static final String SELECT_VISIT_CUSTOMER_CONFIG = "SelectVisitCustomerConfig";
    public static final String ONLY_CHOOSE_ONE = "onlyChooseOne";
    public static final String ONLY_CHOOSE_ONE_TYPE_OBJ = "onlyChooseOneTypeObj";
    public static final String OBJ_API_NAME = "objApiName";

    private TopNoticeView mTopNoticeView;
    private ListView mListView;
    private SelectCrmObjAdapter mAdapter;

    private SelectMultiObjConfig mConfig;
    private HashMap<String, ArrayList<ObjectData>> mUserDefinedData;  // 自定义对象
    private HashMap<String, ObjectDescribe> mUserDefinedObjDescribe;   // 自定义对象类型描述
    private List<SelectCrmObjBean> mObjList;
    private String mSelectingApiName = ""; // 所选对象的API Name（针对自定义对象）
    private int mObjCount; // 已选对象数量
    private String mOverCountPrompt;
    private int mCustomerType = CrmObjWrapper.CustomerType.COMMON.value;//默认通用的模板
    private SelectVisitCustomerConfig mSelectVisitCustomerConfig;
    private List<CustomerInfo> mCustomerList;//选择的拜访类型的客户对象列表
    public Map<String, Object> mCustomerObjectDescribe;
    private List<SelectCrmMultiObjInfo> mSelectMainObjListFromOutdoor;
    private boolean mOnlyChooseOne;//是否单选
    private boolean mOnlyChooseOneTypeObj;//是否只选择一种类型对象
    private Map<String, SearchQueryInfo> mSearchQueryInfoMap;//多个对象的列表的查询条件
    private SelectCrmObjContract.Presenter mPresenter;
    private boolean mOffLineList;
    private boolean mOffLineAdd;
    private boolean disableAdd;
    private Class<IOffLineAddOfSelectObjHook> mOffLineHook;
    private String mObjApiName;//获取指定对象下的crm对象列表
    private Map<String, Object> mFormObjectData;//对象新建编辑表单数据
    public static Intent getIntent(Context context, CrmObjWrapper objWrapper){
        Intent intent = new Intent(context, SelectCrmObjAct.class);
        if (objWrapper!= null) {
            CommonDataContainer.getInstance().saveData(ISelectCrmObject.KEY_SELECTED_MULTI_CRM_OBJ, objWrapper);
        }
        return intent;
    }

    @Override
    protected void onCreate(Bundle arg0) {
        super.onCreate(arg0);
        setContentView(R.layout.activity_select_crm_obj);
        initData(arg0);
        initView();
    }

    private void initData(Bundle arg) {
        CrmObjWrapper wrapper = (CrmObjWrapper) CommonDataContainer.getInstance().getSavedData(ISelectCrmObject
                .KEY_SELECTED_MULTI_CRM_OBJ);
        CommonDataContainer.getInstance().removeSavedData(ISelectCrmObject.KEY_SELECTED_MULTI_CRM_OBJ);
        if (wrapper != null) {//优先从统一的数据类型中取得
            mConfig = wrapper.getConfig();
            mOnlyChooseOne = wrapper.isOnlyChooseOne();
            mOnlyChooseOneTypeObj = wrapper.isOnlyChooseOneTypeObj();
            mSearchQueryInfoMap = wrapper.getSearchQueryInfoMap();
            mOffLineList = wrapper.isOfflineList();
            mOffLineAdd = wrapper.isOfflineAdd();
            disableAdd = wrapper.isDisableAdd();
            mOffLineHook = wrapper.getOffLineAddHookClz();
            mObjApiName = wrapper.getObjApiName();
            mFormObjectData = wrapper.getFormObjectData();
            assembleObjectData(wrapper.getUserDefinedData());
            assembleObjectDescribe(wrapper.getUserDefinedObjDescribe());
            assembleCustomerData(wrapper);
            assembleSelectMainObjFromOutdoor(wrapper);
        }else {
            DialogFragmentWrapper.showBasicWithOpsNoCancel(this, I18NHelper
                            .getText("crm.selectCrmObj.paramsEmptyTips")/* 终端提示: 缺少必要参数,请重新打开页面尝试*/,
                    new MaterialDialog.ButtonCallback() {
                        @Override
                        public void onPositive(MaterialDialog dialog) {
                            super.onPositive(dialog);
                            finish();
                        }
                    });
            return;
        }

        if (mUserDefinedData == null) {
            mUserDefinedData = new HashMap<>();
        }
        if (mUserDefinedObjDescribe == null) {
            mUserDefinedObjDescribe = new LinkedHashMap<>();
        }
        if (mCustomerList == null) {
            mCustomerList = new ArrayList<>();
        }
        if (mConfig != null && !TextUtils.isEmpty(mConfig.mMaxCountPrompt)) {
            mOverCountPrompt = mConfig.mMaxCountPrompt;
        } else if (mConfig != null && mConfig.mMaxCount != Integer.MAX_VALUE) {
            mOverCountPrompt = I18NHelper.getFormatText(OVER_COUNT_PROMPT, String.valueOf(mConfig.mMaxCount));
        } else {
            mOverCountPrompt = "";
        }
        mPresenter = new SelectCrmObjPresenter(this);
        if(TextUtils.isEmpty(mObjApiName)) {
            getCrmObjectList();
        }else {
            getCrmObjectListWithObjApiName(mObjApiName, mFormObjectData);
        }
        updateSelectingObjCount();
    }

    private void initView() {
        initTitleEx();
        NoContentView emptyView = (NoContentView) findViewById(R.id.empty_view);
        mTopNoticeView = (TopNoticeView) findViewById(R.id.top_notice);
        mListView = (ListView) findViewById(R.id.list_view);
        mAdapter = new SelectCrmObjAdapter(this);
        mListView.setAdapter(mAdapter);
        mListView.setEmptyView(emptyView);
        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                long currentTime = Calendar.getInstance().getTimeInMillis();
                if (currentTime - lastClickTime > MIN_CLICK_DELAY_TIME) {
                    lastClickTime = currentTime;
                    Object obj = parent.getItemAtPosition(position);
                    if (obj instanceof SelectCrmObjBean) {
                        SelectCrmObjBean bean = (SelectCrmObjBean) obj;
                        if (bean.isEnabled()) {
                            go2SelectObject(bean);
                        }
                    }
                }
            }
        });
        if (!TextUtils.isEmpty(mOverCountPrompt) && getSPOperator().getBoolean(KEY_SHOW_TOP_TIPS, true)) {
            mTopNoticeView.setVisibility(View.VISIBLE);
            mTopNoticeView.setTip(mOverCountPrompt);
            if (mTopNoticeView.getDismissIcon() != null) {
                mTopNoticeView.getDismissIcon().setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mTopNoticeView.dismiss();
                        getSPOperator().save(KEY_SHOW_TOP_TIPS, false);
                    }
                });
            }
        } else {
            mTopNoticeView.setVisibility(View.GONE);
        }
    }

    @Override
    protected void initTitleEx() {
        super.initTitleEx();
        initTitleCommon();
        mCommonTitleView.setTitle(I18NHelper.getText("crm.selectobject.SelectCrmObjAct.884")/* CRM业务模块 */);
        mCommonTitleView.addLeftBackAction(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        mCommonTitleView
                .addRightAction(I18NHelper.getText("av.common.string.confirm")/* 确定 */, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        setResult();
                    }
                });
    }

    private ISPOperator getSPOperator() {
        return FSContextManager.getCurUserContext().getSPOperator("SelectCrmObj");
    }

    private void assembleObjectData(LinkedHashMap<String, ArrayList<Map>> parsedData) {
        if (parsedData == null) {
            return;
        }
        for (HashMap.Entry<String, ArrayList<Map>> entry : parsedData.entrySet()) {
            ArrayList<Map> entryData = entry.getValue();
            ArrayList<ObjectData> objectDataList = new ArrayList<>();
            for (Map map : entryData) {
                ObjectData od = new ObjectData(map);
                objectDataList.add(od);
            }
            if (mUserDefinedData == null) {
                mUserDefinedData = new HashMap<>();
            }
            mUserDefinedData.put(entry.getKey(), objectDataList);
        }
    }

    private void assembleObjectDescribe(LinkedHashMap<String, String> parsedDescribe) {
        if (parsedDescribe == null) {
            return;
        }
        if (mUserDefinedObjDescribe == null) {
            mUserDefinedObjDescribe = new LinkedHashMap<>();
        }
        for (HashMap.Entry<String, String> entry : parsedDescribe.entrySet()) {
            String entryData = entry.getValue();
            try {
                mUserDefinedObjDescribe.put(entry.getKey(), JsonHelper.fromJsonString(entryData, ObjectDescribe.class));
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 处理选择客户对象参数
     *
     * @param wrapper
     */
    private void assembleCustomerData(CrmObjWrapper wrapper) {
        mCustomerType = wrapper.getCustomerObjType().value;
        mSelectVisitCustomerConfig = wrapper.getSelectVisitCustomerConfig();
        getCustomerListFromConfig(mSelectVisitCustomerConfig);
    }

    /**
     * 处理从外勤页面选择的主对象
     *
     * @param wrapper
     */
    private void assembleSelectMainObjFromOutdoor(CrmObjWrapper wrapper) {
        mSelectMainObjListFromOutdoor = wrapper.getSelectedMainObjFromOutdoor();
    }

    /**
     * 从 SelectVisitCustomerConfig 获取 已选的客户列表
     *
     * @param selectVisitCustomerConfig
     */
    private void getCustomerListFromConfig(SelectVisitCustomerConfig selectVisitCustomerConfig) {
        if (selectVisitCustomerConfig != null) {
            mCustomerObjectDescribe = selectVisitCustomerConfig.objectDescribe;
            if (selectVisitCustomerConfig.mRecoverList != null &&
                    selectVisitCustomerConfig.mRecoverList.size() > 0) {
                if (mCustomerList == null) {
                    mCustomerList = new ArrayList<>();
                } else {
                    mCustomerList.clear();
                }
                mCustomerList.addAll(selectVisitCustomerConfig.mRecoverList);
            }
        }
    }

    private void go2SelectObject(final SelectCrmObjBean bean) {
        final CoreObjType objType = CoreObjType.valueOfApiName(bean.getObjApiName());
        if (objType == CoreObjType.Customer && isVisitCustomerType()) {//针对选拜访类型客户对象
            startActivityForResult(SelectNearCustomerAct.getIntent(this, getSelectVisitCustomerConfig()),
                    REQUEST_CODE_SELECT_CUSTOMER);
        } else {
            mSelectingApiName = bean.getObjApiName();
            int entrance = (mConfig != null && mConfig.mEntrance == SelectMultiObjConfig.Entrance.OUTDOOR)
                    ? INavigator.OUTDOOR : INavigator.DEFAULT;
            mPresenter.selectUserDefinedObject(getPickObjConfig(bean), entrance);
        }
    }


    /**
     * 配置 选择客户对象前的config
     *
     * @return
     */
    private SelectVisitCustomerConfig getSelectVisitCustomerConfig() {
        SearchQueryInfo targetSearchQueryInfo = null;
        if(mSearchQueryInfoMap != null && !mSearchQueryInfoMap.isEmpty() && mSearchQueryInfoMap.containsKey(ICrmBizApiName.ACCOUNT_API_NAME)){
            targetSearchQueryInfo = mSearchQueryInfoMap.get(ICrmBizApiName.ACCOUNT_API_NAME);
        }
        SelectVisitCustomerConfig config = new SelectVisitCustomerConfig.Builder()
                .onlyChooseOne(mOnlyChooseOne)//用 CrmObjWrapper 中 onlyChooseOne的覆盖
                .geography(mSelectVisitCustomerConfig != null ? mSelectVisitCustomerConfig.mGeography : null)
                .recoverList(mCustomerList)
                .setParams(targetSearchQueryInfo)
                .setOfflineList(mOffLineList)
                .setOfflineAdd(mOffLineAdd)
                .setOffLineAddHookClz(mOffLineHook)
                .setDisableAdd(disableAdd)
                .build();

        return config;
    }

    /**
     * 获取选择自定义对象的config
     *
     * @param bean 点击的对象
     *
     * @return
     */
    private PickObjConfig getPickObjConfig(SelectCrmObjBean bean) {
        SearchQueryInfo.Builder searchQueryInfoBuilder = new SearchQueryInfo.Builder();
        List<ObjectData> backData = mUserDefinedData.get(mSelectingApiName);
        BackFillInfos backFillInfos = null;
        // 外勤入口--特殊情况处理，目前没有用到也没法测试，暂时保留特殊逻辑
        if (mConfig != null && mConfig.mEntrance != null
                && mConfig.mEntrance == SelectMultiObjConfig.Entrance.OUTDOOR
                && mConfig.mSelectedCustomer != null && mConfig.mSelectedCustomer.size() > 0) {  // 外勤入口
            CustomerInfo customerInfo = mConfig.mSelectedCustomer.get(0);
            if (customerInfo != null) {
                //新建回填客户信息
                BackFillInfo backFillInfo =
                        new BackFillInfo(MetaFieldKeys.Customer.CUSTOMER_ID, customerInfo.customerID,
                                customerInfo.name, false);
                BackFillInfos.Builder backFillInfosBuilder = new BackFillInfos.Builder();
                backFillInfos = backFillInfosBuilder.add(backFillInfo).build();
            }
            //关联客户的查询条件
            for (CustomerInfo info : mConfig.mSelectedCustomer) {
                FilterInfo filterInfo =
                        new FilterInfo(MetaFieldKeys.Customer.CUSTOMER_ID, Operator.EQ, customerInfo.customerID);
                searchQueryInfoBuilder.filter(filterInfo);
            }
        }
        SearchQueryInfo targetSearchQueryInfo = searchQueryInfoBuilder.build();
        if(mSearchQueryInfoMap != null && !mSearchQueryInfoMap.isEmpty() && mSearchQueryInfoMap.containsKey(bean.getObjApiName())){
            SearchQueryInfo searchQueryInfo = mSearchQueryInfoMap.get(bean.getObjApiName());
            targetSearchQueryInfo.setOrderInfos(searchQueryInfo.getOrderInfos());
            targetSearchQueryInfo.setFilterInfos(searchQueryInfo.getFilterInfos());
            targetSearchQueryInfo.setWheres(searchQueryInfo.getWheres());
        }
        int recoverCount = backData != null ? backData.size() : 0;
        PickObjConfig.Builder builder = new PickObjConfig.Builder()
                .title(bean.getObjCaption())
                .apiName(bean.getObjApiName())
                .selectedObjectName(bean.getObjCaption())
                .pickMode(mOnlyChooseOne ? PickMode.SINGLE : PickMode.MULTI)
                .scene(PickObjConfig.SCENE_NORMAL)
                .setExtFilterScenes(getExtFilterScenes(bean.getObjApiName(), pickMultiSelectAssociateObjData()))
                .initDatas(backData)
                .searchQueryParams(targetSearchQueryInfo)
                .backFillInfos(backFillInfos)
                .setIncludeAssociated(true)
                .setOfflineList(mOffLineList)
                .setOfflineAdd(mOffLineAdd)
                .disableAdd(disableAdd)
                .setOffLineAddHookClz(mOffLineHook);
        if (mConfig != null) {
            builder.setMaxCount(mConfig.mMaxCount - mObjCount + recoverCount)
                    .setMaxPrompt(mOverCountPrompt);
        }
        return builder.build();
    }

    /**
     * 获取场景对应的筛选条件
     * 场景：已选XX的XX
     *
     * @param associateObjData 已选的对象列表
     * @param apiName          已选择的对象 apiName
     * @param targetApiName    要选择的对象 apiName
     *
     * @return
     */
    private List<FilterInfo> processPickedOjbectFilterInfos(Map<String, List<ObjectData>> associateObjData,
                                                            String apiName,
                                                            String targetApiName) {
        List<FilterInfo> filterInfoList = null;
        if (!TextUtils.isEmpty(apiName)
                && associateObjData != null
                && associateObjData.get(apiName) != null
                && !associateObjData.get(apiName).isEmpty()) {
            List<ObjectData> objectDataList = associateObjData.get(apiName);
            List<String> filterValue = null;
            for (ObjectData objectData : objectDataList) {
                if (objectData == null || TextUtils.isEmpty(objectData.getID())) {
                    continue;
                }
                if (filterValue == null) {
                    filterValue = new ArrayList<>();
                }
                filterValue.add(objectData.getID());
            }
            String fieldName = SelectCrmObjUtils.multiSelectFilterFieldName(apiName);
            //高级外勤需要特殊处理已选客户场景筛选字段 field_name, 外勤客户id为：customer_id
            if(TextUtils.equals(targetApiName, ICrmBizApiName.CHECK_IN_OBJ)){
                if(TextUtils.equals(fieldName, "account_id")){
                    fieldName = "customer_id";
                }
            }
            FilterInfo filterInfo =
                    new FilterInfo(fieldName, Operator.IN, filterValue);
            if (filterInfoList == null) {
                filterInfoList = new ArrayList<>();
            }
            filterInfoList.add(filterInfo);
        }
        return filterInfoList;
    }

    /**
     * 增加扩展的筛选场景项
     */
    private List<FilterScene> getExtFilterScenes(String targetApiName, Map<String, List<ObjectData>> associateObjData) {
        if (associateObjData == null) {
            return null;
        }
        List<FilterScene> fakeSceneList = null;
        for (String apiName : SelectCrmObjUtils.multiSelectAssociateObjTypeApiNames()) {
            if (!SelectCrmObjUtils.isMultiSelectFakeScene(targetApiName, apiName)) {
                continue;
            }
            List<ObjectData> objectDataList = associateObjData.get(apiName);
            if (objectDataList == null || objectDataList.isEmpty()) {
                continue;
            }
            FilterScene fakeScene = new FilterScene();
            fakeScene.label = I18NHelper.getFormatText("crm.select_obj.presenter.scene",
                    CoreObjType.valueOfApiName(apiName).description,
                    CoreObjType.valueOfApiName(targetApiName).description);
            fakeScene.filterObjType = apiName;
            fakeScene.is_default = true;
            //设置场景对应的筛选
            List<FilterInfo> filterInfoList = processPickedOjbectFilterInfos(associateObjData,
                    apiName, targetApiName);
            fakeScene.filters = filterInfoList;
            //标记为假场景，非 server 下发的
            fakeScene.isFaked = true;
            if (fakeSceneList == null) {
                fakeSceneList = new ArrayList<>();
            }
            fakeSceneList.add(fakeScene);
        }
        return fakeSceneList;
    }

    /**
     * 获取已选对象数据
     *
     * @return
     */
    private Map<String, List<ObjectData>> pickMultiSelectAssociateObjData() {
        Map<String, List<ObjectData>> associateObjData = null;

        //添加当前列表中已选的data
        if (mUserDefinedData != null && !mUserDefinedData.isEmpty()) {
            for (String apiName : SelectCrmObjUtils.multiSelectAssociateObjTypeApiNames()) {
                if (mUserDefinedData.get(apiName) != null) {
                    if (associateObjData == null) {
                        associateObjData = new HashMap<>();
                    }
                    associateObjData.put(apiName, mUserDefinedData.get(apiName));
                }
            }
        }

        //添加外部选择的主对象data
        if (mSelectMainObjListFromOutdoor != null && !mSelectMainObjListFromOutdoor.isEmpty()) {
            for (SelectCrmMultiObjInfo objInfo : mSelectMainObjListFromOutdoor) {
                if (objInfo == null || !SelectCrmObjUtils.multiSelectAssociateObjTypeApiNames()
                        .contains(objInfo.getObjApiName())) {
                    continue;
                }
                if (associateObjData == null) {
                    associateObjData = new HashMap<>();
                }
                if (associateObjData.get(objInfo.getObjApiName()) == null) {
                    List<ObjectData> objectDataList = new ArrayList<>();
                    associateObjData.put(objInfo.getObjApiName(), objectDataList);
                }
                ObjectData objectData = new ObjectData();
                objectData.setId(objInfo.getObjId());
                associateObjData.get(objInfo.getObjApiName()).add(objectData);
            }
        }
        return associateObjData;
    }

    /**
     * 获取 CRM 对象列表
     */
    @SuppressLint("CheckResult")
    private void getCrmObjectList() {
        showLoading();
        Single<FindCrmObjectListResult> single = MetaDataRepository.getInstance(this)
                .findCrmObjectList(false, true, false)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io());
        if (mOffLineList) {
            boolean hasCrmObjectListCache = SelectCrmObjectListCacheHelper.hasCrmObjectListCache();
            if (hasCrmObjectListCache) {
                single = single.timeout(SelectCrmObjectListCacheHelper.getReadTimeOut(),
                        TimeUnit.MILLISECONDS)
                        .onErrorResumeNext(
                                new Function<Throwable, SingleSource<?
                                        extends FindCrmObjectListResult>>() {
                                    @Override
                                    public SingleSource<? extends FindCrmObjectListResult> apply(
                                            Throwable throwable) throws Exception {
                                        return SelectCrmObjectListCacheHelper.getCrmObjectListCache();
                                    }
                                });
            }
        }
        single.observeOn(AndroidSchedulers.mainThread()).subscribe(getResultSingleObserver());
    }

    /**
     * 获取 CRM 对象列表
     */
    @SuppressLint("CheckResult")
    private void getCrmObjectListWithObjApiName(String objApiName,
                                                Map<String, Object> formObjectData) {
        showLoading();
        MetaDataRepository.getInstance(this)
                .findNewCrmObjectList(objApiName, formObjectData, false, true, false)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(getResultSingleObserver());
    }

    private SingleObserver<FindCrmObjectListResult> getResultSingleObserver(){
        return new SingleObserver<FindCrmObjectListResult>() {
            @Override
            public void onSubscribe(@NotNull Disposable d) {

            }

            @Override
            public void onSuccess(
                    @NotNull FindCrmObjectListResult result) {
                dismissLoading();
                if (result == null) {
                    return;
                }
                mObjList = transferData(result.getObjectList());
                refreshList();
            }

            @Override
            public void onError(@NotNull Throwable throwable) {
                dismissLoading();
                DialogFragmentWrapper.showBasicWithTwoOpsNoCancel(mContext, throwable.getMessage(),
                        I18NHelper.getText(
                                "commonfunc.dialog_fragment_fsmail_choose_attachment_menu"
                                        + ".text.cancel")/* 取消 */,
                        I18NHelper.getText("crm.layout.item_smart_h5_card.1914")/* 重新加载 */,
                        new MaterialDialog.ButtonCallback() {
                            @Override
                            public void onPositive(MaterialDialog dialog) {
                                getCrmObjectList();
                            }

                            @Override
                            public void onNegative(MaterialDialog dialog) {
                                mContext.finish();
                            }
                        });
            }
        };
    }


    private void refreshList() {
        if (mObjList == null) {
            return;
        }
        for (int i = 0; i < mObjList.size(); i++) {
            SelectCrmObjBean bean = mObjList.get(i);
            if (TextUtils.equals(CoreObjType.Customer.apiName, bean.getObjApiName()) && isVisitCustomerType()) {
                bean.setObjNum(mCustomerList != null ? mCustomerList.size() : 0);
            } else {
                List<ObjectData> data = mUserDefinedData.get(bean.getObjApiName());
                bean.setObjNum(data != null ? data.size() : 0);
            }
        }
        mAdapter.updateDataList(mObjList);
    }

    private List<SelectCrmObjBean> transferData(List<CrmObjectInfo> infoList) {
        List<SelectCrmObjBean> resultList = null;
        List<String> designatedTypes = (mConfig != null ? mConfig.mDesignatedTypes : null);
        List<String> filteredTypes = (mConfig != null ? mConfig.mFilteredTypes : null);
        boolean hasFilters = filteredTypes != null && !filteredTypes.isEmpty();
        if (infoList != null && !infoList.isEmpty()) {
            for (CrmObjectInfo info : infoList) {
                if (info == null || TextUtils.isEmpty(info.getApiName())) {
                    continue;
                }
                if (hasFilters) {
                    if (filteredTypes.contains(info.getApiName())) {
                        continue;

                    }
                }

                if (resultList == null) {
                    resultList = new ArrayList<>();
                }

                //白名单过滤
                if (designatedTypes == null || designatedTypes.size() == 0 || designatedTypes.contains(info.getApiName())) {
                    //没有白名单或者白名单存在则添加
                    resultList.add(assembleObjBean(info));
                }
            }
        }
        //对白名单数据排序，白名单只有一条数据，不用排序
        if(designatedTypes != null && designatedTypes.size() > 1 && resultList != null && resultList.size() > 1){
            resultList = sortDesignatedTypesObj(resultList, designatedTypes);
        }
        return resultList;
    }

    /**
     * 排序白名单数据，按照调用方传进来的顺序
     * @param resultList
     * @param designatedTypes
     * @return
     */
    private List<SelectCrmObjBean> sortDesignatedTypesObj(List<SelectCrmObjBean> resultList, List<String> designatedTypes){
        List<SelectCrmObjBean> sortList = new ArrayList<>();
        for (String designatedType: designatedTypes) {
            for (SelectCrmObjBean selectCrmObjBean: resultList) {
                if(TextUtils.equals(designatedType, selectCrmObjBean.getObjApiName())){
                    sortList.add(selectCrmObjBean);
                    break;
                }
            }
        }
        return sortList;
    }

    private SelectCrmObjBean assembleObjBean(CrmObjectInfo info) {
        SelectCrmObjBean bean = new SelectCrmObjBean();
        bean.setObjCaption(info.getDisplayName());
        bean.setObjNum(0);
        String apiName = info.getApiName();
        bean.setObjApiName(apiName);
        if (mConfig != null && mConfig.mDisabledTypes != null && mConfig.mDisabledTypes.contains(apiName)) {
            bean.setEnabled(false);
        } else {
            bean.setEnabled(true);
        }
        return bean;
    }

    private LinkedHashMap<String, ArrayList<Map>> getSortUserDefinedData() {
        LinkedHashMap<String, ArrayList<Map>> data = new LinkedHashMap<>();
        if (mObjList == null) {
            return data;
        }
        for (SelectCrmObjBean bean : mObjList) {
            String apiName = bean.getObjApiName();
            if (mUserDefinedData.containsKey(apiName) && mUserDefinedData.get(apiName) != null
                    && mUserDefinedData.get(apiName).size() > 0) {
                ArrayList<Map> list = new ArrayList<>();
                ArrayList<ObjectData> datas = mUserDefinedData.get(apiName);
                for (int i = 0; i < datas.size(); i++) {
                    ObjectData od = datas.get(i);
                    list.add(od.getMap());
                }
                data.put(apiName, list);
            }
        }
        return data;
    }

    private LinkedHashMap<String, String> serializeDecribe() {
        LinkedHashMap<String, String> describeMap = new LinkedHashMap<>();
        if (mUserDefinedObjDescribe != null && mUserDefinedObjDescribe.size() > 0) {
            for (String apiName : mUserDefinedObjDescribe.keySet()) {
                try {
                    describeMap.put(apiName, JsonHelper.toJsonString(mUserDefinedObjDescribe.get(apiName)));
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return describeMap;
    }

    private CrmObjWrapper getAssembledWrapper() {
        CrmObjWrapper wrapper = new CrmObjWrapper();
        wrapper.setUserDefinedData(getSortUserDefinedData());
        wrapper.setUserDefinedObjDescribe(serializeDecribe());
        //设置已选择的拜访类型的客户对象
        if (isVisitCustomerType()) {
            wrapper.setSelectVisitCustomerConfig(getReturnSelectVisitCustomerConfig());
        }
        return wrapper;
    }

    private void sendCcResult(){
        if (mObjList != null) {
            Map<String, Object> ccRst = new HashMap<>();
            List<Map<String, Object>> datas = new ArrayList<>();
            for (SelectCrmObjBean bean : mObjList) {
                String apiName = bean.getObjApiName();
                if (mUserDefinedData.containsKey(apiName) && mUserDefinedData.get(apiName) != null
                        && mUserDefinedData.get(apiName).size() > 0) {
                    ObjectDescribe describe = mUserDefinedObjDescribe.get(apiName);
                    ArrayList<Map> list = new ArrayList<>();
                    ArrayList<ObjectData> objdatas = mUserDefinedData.get(apiName);
                    for (int i = 0; i < objdatas.size(); i++) {
                        ObjectData od = objdatas.get(i);
                        Map<String, Object> dataMap = new HashMap<>();
                        dataMap.put(ObjectDataKeys.ID, od.getID());
                        dataMap.put(ObjectDataKeys.NAME, od.getName());
                        list.add(dataMap);
                    }
                    Map<String, Object> objMap = new HashMap<>();
                    objMap.put("dataList", list);
                    objMap.put("apiName", apiName);
                    String displayName = describe!=null?describe.getDisplayName():null;
                    if (TextUtils.isEmpty(displayName)){
                        displayName=bean.getObjCaption();
                    }
                    objMap.put("objectDisplayName", displayName);
                    datas.add(objMap);
                }
            }
            ccRst.put("selectedData", datas);
            MetaDataUtils.sendCcResultSafe(this, CCResult.success(ccRst));
        }
    }

    /**
     * 是否拜访客户类型
     *
     * @return
     */
    private boolean isVisitCustomerType() {
        return mCustomerType == CrmObjWrapper.CustomerType.VISIT.value;
    }

    /**
     * 返回已选择的客户对象集合
     *
     * @return SelectVisitCustomerConfig(包含客户对象集合)
     */
    private SelectVisitCustomerConfig getReturnSelectVisitCustomerConfig() {
        SelectVisitCustomerConfig config = null;
        if (mCustomerList != null && mCustomerList.size() > 0) {
            config = new SelectVisitCustomerConfig.Builder()
                    .recoverList(mCustomerList)
                    .setObjectDescribe(mCustomerObjectDescribe)
                    .build();
        }
        return config;
    }

    /**
     * 检查对象数量
     *
     * @param num
     *
     * @return
     */
    private boolean isOverLimit(int num) {
        return mConfig != null && num > mConfig.mMaxCount;
    }

    /**
     * 更新已选对象数量
     */
    private void updateSelectingObjCount() {
        for (Map.Entry<String, ArrayList<ObjectData>> entry : mUserDefinedData.entrySet()) {
            if (entry.getValue() != null) {
                mObjCount += entry.getValue().size();
            }
        }
        //客户类型加入选择数量计算
        if (mCustomerList != null) {
            mObjCount += mCustomerList.size();
        }
    }

    /**
     * 处理回调选择客户列表
     */
    private void dealCallBackCustomerListInfo(List<CustomerInfo> dataList) {
        if (dataList != null && dataList.size() >= 0) {
            int objCount = mObjCount + dataList.size();
            if (mCustomerList != null && mCustomerList.size() > 0) {
                objCount -= mCustomerList.size();
            }
            if (isOverLimit(objCount)) {
                ToastUtils.show(mOverCountPrompt);
                return;
            } else {
                mObjCount = objCount;
                mCustomerList.clear();
                mCustomerList.addAll(dataList);
            }
        }
        handleSelectedResult();
    }

    /**
     * 将SelectCustomer 转型成 List<CustomerObjectData>
     *
     * @param selectCustomer
     */
    private List<CustomerInfo> getSelectCustomerList(SelectCustomer selectCustomer) {
        List<CustomerInfo> list;
        if (selectCustomer != null) {
            if (selectCustomer.data == null || selectCustomer.data.isEmpty()) {
                return Collections.EMPTY_LIST;
            }
            list = new ArrayList<>();
            for (AShortFCustomer item : selectCustomer.data.values()) {
                if (item == null) {
                    continue;//过滤空数据
                }
                if (selectCustomer.data.size() == 1) {
                    FCLog.i(TAG, "onActivityResult()->getSelectCustomerList():" + item.toString());
                }
                CustomerInfo customerInfo = new CustomerInfo();
                customerInfo.customerID = item.customerID;
                customerInfo.name = item.name;
                customerInfo.address = item.address;
                customerInfo.distance = String.valueOf(item.distance);
                customerInfo.locationID = item.locationID;
                customerInfo.extraData = item.extraData;
                list.add(customerInfo);
            }
            return list;
        } else {
            return Collections.EMPTY_LIST;
        }
    }

    private void setResult() {
        Intent intent = new Intent();
        //添加返回数据类型
        intent.putExtra(ISelectCrmObject.KEY_OUTDOOR_SELECTED_CRM_OBJ_MODE, SelectCrmObjectMode.MULTI_TYPE);
        CommonDataContainer.getInstance().saveData(ISelectCrmObject.KEY_SELECTED_MULTI_CRM_OBJ, getAssembledWrapper());
        sendCcResult();
        setResult(RESULT_OK, intent);
        finish();
    }

    /**
     * 处理选择对象后逻辑
     */
    private void handleSelectedResult() {
        //如果是单一类型的对象或者单选模式直接返回
        if (mOnlyChooseOneTypeObj || mOnlyChooseOne) {
            setResult();
        } else {
            refreshList();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data == null) {
            CrmLog.d(TAG, "onActivityResult data == null =resultCode " + resultCode);
            return;
        }
        if (resultCode != RESULT_OK) {
            CrmLog.d(TAG, "onActivityResult requestCode != RESULT_OK >> resultCode " + resultCode);
            return;
        }
        mPresenter.onActivityResult(requestCode, resultCode, data);
        if (mOnlyChooseOneTypeObj || mOnlyChooseOne) {
            // 如果是只选择一种类型对象（单一类型）或者单选，由于对象类型分三种类型，返回前，先清空上次存储的数据，保证返回的数据只存在一种，
            // 这个操作跟 setResult() 中 填充数据类型有关系；
            // 这里统一清空，分类判断繁琐，因此不做分类判断；
            mUserDefinedData.clear();
            mUserDefinedObjDescribe.clear();
            mCustomerList.clear();
        }
        if (requestCode == REQUEST_CODE_SELECT_USER_DEFINED_OBJECT) {
            MultiObjectPicker picker = MultiObjectPicker.getPickerByIntent(data);
            if (picker != null) {
                ArrayList<ObjectData> dataList = picker.getSelectedList();
                ObjectDescribe objectDescribe =
                        (ObjectDescribe) data.getSerializableExtra(MetaDataSelectObjAct.OBJECT_DESCRIBE);
                if (dataList != null && dataList.size() >= 0) {
                    int objCount = mObjCount + dataList.size();
                    if (mUserDefinedData.get(mSelectingApiName) != null
                            && mUserDefinedData.get(mSelectingApiName).size() > 0) {
                        objCount -= mUserDefinedData.get(mSelectingApiName).size();
                    }
                    if (isOverLimit(objCount)) {
                        ToastUtils.show(mOverCountPrompt);
                        return;
                    } else {
                        mObjCount = objCount;
                        mUserDefinedData.put(mSelectingApiName, dataList);
                        if (objectDescribe != null) {
                            mUserDefinedObjDescribe.put(mSelectingApiName, objectDescribe);
                        }
                    }
                    handleSelectedResult();
                }
            }
        } else if (requestCode == REQUEST_CODE_SELECT_CUSTOMER) {
            if (mSelectVisitCustomerConfig != null) {
                //                    SelectCustomer selectCustomer = (SelectCustomer) data.getSerializableExtra
                // (SelectCustomerAct
                //                            .KEY_SELECT_CUSTOMER_FOR_OUTDOOR);
                SelectCustomer selectCustomer = (SelectCustomer) CommonDataContainer.getInstance()
                        .getAndRemoveSavedData(ICustomer.KEY_SELECT_VISIT_CUSTOMER);
                ObjectDescribe objectDescribe =
                        (ObjectDescribe) CommonDataContainer.getInstance()
                                .getAndRemoveSavedData(ICustomer.KEY_OBJECT_DESCRIBE);
                mCustomerObjectDescribe =
                        objectDescribe != null ? objectDescribe.getInnerMap() : null;
                dealCallBackCustomerListInfo(getSelectCustomerList(selectCustomer));
            }
        }
    }

}
