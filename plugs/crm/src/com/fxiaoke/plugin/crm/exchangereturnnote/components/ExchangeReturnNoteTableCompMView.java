package com.fxiaoke.plugin.crm.exchangereturnnote.components;

import android.content.Intent;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.beans.MultiLayout;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.modify.MetaModifyContext;
import com.facishare.fs.metadata.modify.master_detail.MultiEditArgData;
import com.facishare.fs.metadata.modify.master_detail.MultiEditConfig;
import com.facishare.fs.metadata.modify.master_detail.MultiEditResultData;
import com.facishare.fs.metadata.modify.modelviews.componts.beans.TableComMViewArg;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.plugin.crm.deliverynote.activity.BaseStockMultiFormEditAct;
import com.fxiaoke.plugin.crm.exchangegoodsnote.components.ExchangeGoodsNoteModifyDetailFragTableCompMView;
import com.fxiaoke.plugin.crm.exchangereturnnote.ExchangeReturnNoteProductInObj;
import com.fxiaoke.plugin.crm.exchangereturnnote.actions.ExchangeReturnNoteDetailLookupAction;
import com.fxiaoke.plugin.crm.exchangereturnnote.activity.ExchangeReturnNoteMultiFormEditAct;
import com.fxiaoke.plugin.crm.exchangereturnnote.modelviews.table.ExchangeReturnNoteProductTableListAdapter;
import com.fxiaoke.plugin.crm.outbounddeliverynote.OutboundDeliveryNoteObj;
import com.fxiaoke.plugin.crm.outbounddeliverynote.modelviews.table.OutboundDeliveryNoteModifyDetailFragTableListAdapter;
import com.fxiaoke.plugin.crm.requisitionnote.RequisitionNoteObj;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class ExchangeReturnNoteTableCompMView extends ExchangeGoodsNoteModifyDetailFragTableCompMView {
    protected String mSalesOrderId=null;
    public ExchangeReturnNoteTableCompMView(MultiContext context) {
        super(context);
    }

    @Override
    protected String getQuantifyField() {
        return ExchangeReturnNoteProductInObj.AUXILIARY_QUANTITY;
    }

    @Override
    protected String getAmountField() {
        return ExchangeReturnNoteProductInObj.QUANTITY;
    }

    @Override
    protected Intent getMultiFormActIntent(MultiEditConfig config, boolean isFromCopy) {
        updateConfig(config);
        BaseStockMultiFormEditAct.isInEditMode= MetaModifyContext.get(getMultiContext()).getModifyConfig().isEditType();
        if(BaseStockMultiFormEditAct.isInEditMode || config.isEditType) {
            for(MultiEditArgData data : config.multiEditArgDatas) {
                String stock_id = data.objectData.getString(RequisitionNoteObj.RequisitionNoteProductObj.STOCK_ID);
                String stock_id__r = data.objectData.getString(RequisitionNoteObj.RequisitionNoteProductObj.STOCK_ID+"__r");
                data.objectData.put(RequisitionNoteObj.RequisitionNoteProductObj.STOCK_ID+"_2",stock_id);
                data.objectData.put(RequisitionNoteObj.RequisitionNoteProductObj.STOCK_ID+"__r_2",stock_id__r);
            }
        }
        return ExchangeReturnNoteMultiFormEditAct.getIntent(getContext(),config, isFromCopy);
    }

    @Override
    protected String getNoContentButtonText() {
        return I18NHelper.getText("exchangereturnnoteobj.field.string.add_return_product2");//添加退货产品
    }

    @Override
    protected OutboundDeliveryNoteModifyDetailFragTableListAdapter newTableListAdapter(MultiContext context, int scene) {
        return new ExchangeReturnNoteProductTableListAdapter(context,scene,false);
    }

    public void updateSalesOrderId(String salesOrderId){
        mSalesOrderId=salesOrderId;
    }

    public void clearSwapInProducts(){
        updateProductList(new ArrayList<>(), ExchangeReturnNoteDetailLookupAction.RECORD_TYPE_DEFAULT,true);
        updateProductList(new ArrayList<>(), ExchangeReturnNoteDetailLookupAction.RECORD_TYPE_SWAP_IN,true);
    }

    public void clearSwapOutProducts(){
        updateProductList(new ArrayList<>(), ExchangeReturnNoteDetailLookupAction.RECORD_TYPE_SWAP_OUT,true);
    }

    public void updateProductList(List<ObjectData> list){
        updateProductList(list,getRecordType(),false);
    }

    @Override
    public void updateView(TableComMViewArg arg) {
        Iterator<MultiLayout> iterator = arg.getMultiLayout().iterator();
        while (iterator.hasNext()){
            MultiLayout layout=iterator.next();
            if(layout.isNotMatch()){
                iterator.remove();
            }
        }
        super.updateView(arg);
    }

    public void updateProductList(List<ObjectData> list, String recordType, boolean cover){
        if(list==null) return;
        ArrayList<MultiEditResultData> dataList=new ArrayList<>();
        for(ObjectData objectData : list){
            objectData.setRecordType(recordType);
            MultiEditResultData multiEditResultData=new MultiEditResultData(objectData,null);
            dataList.add(multiEditResultData);
        }
        updateDataOfRecordType(recordType, dataList, cover);
    }
}
