/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.selectsku.skuattribute;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.facishare.fs.common_utils.function.BiConsumer;
import com.facishare.fs.metadata.beans.Layout;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.ObjectDescribe;
import com.facishare.fs.metadata.beans.fields.Field;
import com.facishare.fs.metadata.beans.fields.FilterInfo;
import com.facishare.fs.metadata.config.MetaDataConfig;
import com.facishare.fs.metadata.list.ListSource;
import com.facishare.fs.metadata.list.beans.ListItemArg;
import com.facishare.fs.metadata.list.modelviews.ListContentAdapter;
import com.facishare.fs.metadata.list.select_obj.picker.MultiObjectPicker;
import com.facishare.fs.metadata.list.select_obj.picker.PickObjConfig;
import com.facishare.fs.modelviews.adapter.basic.BaseMViewListAdapter;
import com.facishare.fs.pickerutils.MOPCounter;
import com.fxiaoke.fscommon.http.WebApiExecutionCallbackWrapper;
import com.fxiaoke.plugin.crm.lib.bean.ProductEnumDetailInfo;
import com.fxiaoke.plugin.crm.onsale.selectdetail.SelectOnSaleDetailObjFrag;
import com.fxiaoke.plugin.crm.onsale.selectdetail.classify.ClassifyWrapper;
import com.fxiaoke.plugin.crm.order.action.SelectEntrance;
import com.fxiaoke.plugin.crm.order.selectproduct.config.PickProductConfig;
import com.fxiaoke.plugin.crm.order.utils.MDOrderProductService;
import com.fxiaoke.plugin.crm.selectsku.SKUConstant;
import com.fxiaoke.plugin.crm.selectsku.SKUUtils;
import com.fxiaoke.plugin.crm.selectsku.beans.AttributesResult;

import android.os.Bundle;
import android.text.TextUtils;
import io.reactivex.functions.Function;

/**
 * 选择产品属性值Frag
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2020/9/15
 */
public class SelectSKUAttributeFrag extends SelectOnSaleDetailObjFrag {

    private SelectSKUAttributePicker mSelectSKUPicker;
    //参数1：当前属性值字段，参数2：上一次的属性值字段的apiName
    private BiConsumer<List<Field>, List<String>> mAttributeFilterFieldsConsumer;

    public static SelectSKUAttributeFrag newInstance(PickObjConfig config, MOPCounter mopCounter,
                                                     PickProductConfig selectProductConfig,
                                                     boolean searchType) {
        SelectSKUAttributeFrag frag = new SelectSKUAttributeFrag();
        Bundle args = createArgs(config, mopCounter, selectProductConfig, searchType);
        frag.setArguments(args);
        return frag;
    }

    @Override
    protected void initData(Bundle bundle) {
        super.initData(bundle);
        initSelectSKUPicker();
        if (mAdapter instanceof SelectSKUAttributeAdapter) {
            SelectSKUAttributeListenerImpl listener =
                    new SelectSKUAttributeListenerImpl(getMultiContext(), mSelectProductConfig,
                            mSelectSKUPicker);
            SelectSKUAttributeAdapter adapter = (SelectSKUAttributeAdapter) mAdapter;
            adapter.setOnSelectSKUAttributeListener(listener);
            adapter.setPickerProxy(listener);
        }
    }

    private void initSelectSKUPicker() {
        if (mObjectPicker instanceof SelectSKUAttributePicker) {
            mSelectSKUPicker = (SelectSKUAttributePicker) mObjectPicker;
        } else {
            mSelectSKUPicker = new SelectSKUAttributePicker(mConfig, mSelectProductConfig);
        }
    }

    @Override
    protected BaseMViewListAdapter initMetaDataListAdapter(boolean isPickType,
                                                           MultiObjectPicker objectPicker) {
        SelectSKUAttributeAdapter adapter =
                new SelectSKUAttributeAdapter(mMultiContext, mSelectProductConfig);
        adapter.setListSource(ListSource.SelectList);
        adapter.setContentAdapterProvider(new Function<String, ListContentAdapter<ListItemArg>>() {
            @Override
            public ListContentAdapter<ListItemArg> apply(String s) throws Exception {
                ListContentAdapter<ListItemArg> contentAdapter =
                        MetaDataConfig.getOptions().getMetaBizImplFactories()
                                .getListAdapterFactory(mConfig.getApiName())
                                .getSelectObjectListContentAdapter();
                contentAdapter.setListSource(ListSource.SelectList);
                return contentAdapter;
            }
        });
        adapter.updatePickType(isPickType);
        adapter.setObjectPicker(objectPicker);
        adapter.setDataList(mObjDataListManager.getInfos());
        return adapter;
    }

    @Override
    protected void onPickedDataSetChanged() {
        super.onPickedDataSetChanged();
        List<ListItemArg> showList = new ArrayList<>();
        List<ListItemArg> skuList = mObjDataListManager.getInfos();
        if (skuList != null && !skuList.isEmpty()) {
            for (ListItemArg skuData : skuList) {
                if (skuData == null || skuData.objectData == null) {
                    continue;
                }
                String skuId = skuData.objectData.getID();
                showList.add(skuData);
                List<ObjectData> attribute =
                        skuData.objectData.getMetaDataList("attribute", ObjectData.class);
                boolean hasAttribute = attribute != null && !attribute.isEmpty();
                if (!hasAttribute) {
                    continue;
                }
                List<ObjectData> selectedSkuList = mSelectSKUPicker.getSelectedList(skuId);
                if (selectedSkuList == null || selectedSkuList.isEmpty()) {
                    continue;
                }
                Layout layout = skuData.layout;
                ObjectDescribe describe = skuData.objectDescribe;
                for (int i = 0, size = selectedSkuList.size(); i < size; i++) {
                    ObjectData selectedSku = selectedSkuList.get(i);
                    if (selectedSku == null) {
                        continue;
                    }
                    selectedSku.put(SKUConstant.KEY_SHOW_GRAY_DIVIDER, i == (size - 1));
                    showList.add(new ListItemArg(selectedSku, layout, describe));
                }
            }
        }
        mAdapter.updateDataList(showList);
    }

    public void updateSelectedProductAfterRemoved() {
        if (mObjectPicker != null) {
            mObjectPicker
                    .pickBatch(SKUUtils.getRemovedDatasFromSelectedList(), false);//移除掉在已选页面移除的数据
        }
    }

    @Override
    protected void onClassifySelected(ProductEnumDetailInfo detailInfo) {
        List<String> lastAttributeFields = removeAttributeFilter();
        getAttributeAndValueByCategoryId(detailInfo, lastAttributeFields);
        super.onClassifySelected(detailInfo);
    }

    @Override
    protected List<FilterInfo> getAllFilters() {
        List<FilterInfo> filterInfos = super.getAllFilters();
        if (filterInfos != null && !filterInfos.isEmpty()) {
            for (FilterInfo filterInfo : filterInfos) {
                if (filterInfo == null || TextUtils.isEmpty(filterInfo.fieldName)) {
                    continue;
                }
                String fieldName = filterInfo.fieldName;
                if (fieldName.startsWith("product_id.attribute")) {
                    filterInfo.isMasterField = true;
                }
            }
        }
        return filterInfos;
    }

    private List<String> removeAttributeFilter() {
        if (mFilterInfos == null || mFilterInfos.isEmpty()) {
            return null;
        }
        List<String> removedFields = new ArrayList<>();
        Iterator<FilterInfo> it = mFilterInfos.iterator();
        while (it.hasNext()) {
            FilterInfo filterInfo = it.next();
            if (filterInfo == null || TextUtils.isEmpty(filterInfo.fieldName)) {
                continue;
            }
            String fieldName = filterInfo.fieldName;
            if (fieldName.startsWith("attribute") || fieldName.startsWith("product_id.attribute")) {
                removedFields.add(fieldName);
                it.remove();
            }
        }
        return removedFields;
    }

    private void getAttributeAndValueByCategoryId(ProductEnumDetailInfo detailInfo,
                                                  List<String> lastAttributeFields) {
        if (mAttributeFilterFieldsConsumer == null) {
            return;
        }
        String classifyCode = detailInfo == null ? null : detailInfo.mItemcode;
        String classifyId = detailInfo == null ? null : detailInfo.id;
        if (TextUtils.isEmpty(classifyId)
                || TextUtils.equals(classifyCode, ClassifyWrapper.PROMOTION_CLASSIFY)) {
            mAttributeFilterFieldsConsumer.accept(null, lastAttributeFields);
            return;
        }
        MDOrderProductService.getAttributeAndValueByCategoryId(classifyId,
                new WebApiExecutionCallbackWrapper<AttributesResult>(AttributesResult.class,
                        mActivity) {
                    @Override
                    public void succeed(AttributesResult result) {
                        boolean selectPriceBookProduct = mSelectProductConfig != null
                                && mSelectProductConfig.getSelectEntrance()
                                == SelectEntrance.PriceBookProduct;
                        List<Field> fields = result == null ? null
                                : result.toFilterFields(selectPriceBookProduct);
                        mAttributeFilterFieldsConsumer.accept(fields, lastAttributeFields);
                    }

                    @Override
                    public void failed(String error) {
                        super.failed(error);
                        mAttributeFilterFieldsConsumer.accept(null, lastAttributeFields);
                    }
                });
    }

    public void setAttributeFilterFieldsConsumer(BiConsumer<List<Field>, List<String>> consumer) {
        this.mAttributeFilterFieldsConsumer = consumer;
    }
}
