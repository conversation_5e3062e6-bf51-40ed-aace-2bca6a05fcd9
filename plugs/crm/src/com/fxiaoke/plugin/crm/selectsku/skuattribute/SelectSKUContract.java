/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.selectsku.skuattribute;

import java.util.List;

import com.facishare.fs.metadata.beans.fields.Field;
import com.fxiaoke.plugin.crm.onsale.selectdetail.SelectOnSaleDetailObjContract;

/**
 * 类名
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2020/9/15
 */
public interface SelectSKUContract {

    interface View extends SelectOnSaleDetailObjContract.View<Presenter> {

    }

    interface Presenter extends SelectOnSaleDetailObjContract.Presenter {

        void updateAttributeFilterFields(List<Field> currentAttributeFields,
                                         List<String> lastAttributeFieldApiNames);
    }
}
