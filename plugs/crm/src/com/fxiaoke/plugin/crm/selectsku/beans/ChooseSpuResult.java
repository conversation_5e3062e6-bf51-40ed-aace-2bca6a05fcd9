/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.selectsku.beans;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 类名
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2018/11/23
 */
public class ChooseSpuResult implements Serializable {

    private boolean result;
    private boolean success;
    private String msg;

    @JSONField(name = "result")
    public boolean isResult() {
        return result;
    }

    @JSONField(name = "result")
    public void setResult(boolean result) {
        this.result = result;
    }

    @JSONField(name = "success")
    public boolean isSuccess() {
        return success;
    }

    @JSONField(name = "success")
    public void setSuccess(boolean success) {
        this.success = success;
    }

    @JSONField(name = "msg")
    public String getMsg() {
        return msg;
    }

    @JSONField(name = "msg")
    public void setMsg(String msg) {
        this.msg = msg;
    }
}
