/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.selectsku.beans;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.fs.metadata.beans.MetaDataParser;
import com.facishare.fs.metadata.beans.ObjectData;

/**
 * 规格列表实体
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2018/11/9
 */
public class SpecificationResult implements Serializable {

    private static final long serialVersionUID = 8243708136880503393L;

    private List<Map<String, Object>> dataList;

    @JSONField(name = "dataList")
    public List<Map<String, Object>> getDataList() {
        return dataList;
    }

    @JSONField(name = "dataList")
    public void setDataList(List<Map<String, Object>> dataList) {
        this.dataList = dataList;
    }

    @JSONField(serialize = false, deserialize = false)
    public List<ObjectData> getSpecDataList() {
        List<ObjectData> objectDataList = null;
        try {
            objectDataList = MetaDataParser.toMetaDatas(dataList, ObjectData.class);
        } catch (Exception ignore) {
        }
        return objectDataList;
    }
}
