/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.commonlist.beans;

import com.fxiaoke.plugin.crm.R;

import android.view.View;

/**
 * Created by l<PERSON><PERSON> on 2017/7/5.
 * 头部按钮描述
 */

public class TitleButtonDesc {
    public int mBGResID = com.fxiaoke.fscommon_res.R.drawable.btn_actionbar_top_bg;
    public int mIconResID;
    public View.OnClickListener mLis;

    public TitleButtonDesc(int mBGResID, int mIconResID, View.OnClickListener mLis) {
        this.mBGResID = mBGResID;
        this.mIconResID = mIconResID;
        this.mLis = mLis;
    }

    public TitleButtonDesc(int mIconResID, View.OnClickListener mLis) {
        this.mIconResID = mIconResID;
        this.mLis = mLis;
    }

    /**
     * 默认新建按钮
     * @param lis
     * @return
     */
    public static TitleButtonDesc getDefaultAddButton(View.OnClickListener lis) {
        TitleButtonDesc desc = new TitleButtonDesc(R.drawable.barbuttonicon_add, lis);
        return desc;
    }

    /**
     * 默认更多按钮
     * @param lis
     * @return
     */
    public static TitleButtonDesc getDefaultMoreButton(View.OnClickListener lis) {
        TitleButtonDesc desc = new TitleButtonDesc(R.drawable.title_more, lis);
        return desc;
    }

    /**
     * 自定义按钮
     * @param iconResID
     * @param lis
     * @return
     */
    public static TitleButtonDesc getCustomButton(int iconResID, View.OnClickListener lis) {
        TitleButtonDesc desc = new TitleButtonDesc(iconResID, lis);
        return desc;
    }

}
