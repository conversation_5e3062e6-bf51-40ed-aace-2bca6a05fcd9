/*
 * Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.flowpropeller.contract;

import com.facishare.fs.flowpropeller.beans.TaskDetailInfo;
import com.facishare.fs.metadata.BasePresenter;
import com.facishare.fs.metadata.BaseView;

/**
 * Author:  wangrz
 * Date:    2018/11/9 16:15
 * Remarks: 任务详情
 */
public interface BaseFlowTaskDetailContract {
    interface View extends BaseView<BaseFlowTaskDetailContract.Presenter> {
        void showLoading();

        void dismissLoading();

        void updateView(TaskDetailInfo taskDetailInfo);

        boolean isUiSafety();



    }

    interface Presenter extends BasePresenter {
        //通过 taskId 获取任务详情
        void getTaskDetailByTaskId(String taskId);
        //通过 activityId 获取任务详情
        void getTaskDetailByActivityId(String workflowInstanceId, String activityId);
        //更换处理人
        void changeTaskHandler(String actionName, TaskDetailInfo taskDetailInfo);
    }
}
