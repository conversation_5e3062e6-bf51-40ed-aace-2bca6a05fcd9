package com.fxiaoke.plugin.crm.purchaseorder.utils

import java.lang.NumberFormatException

class PurchaseOrderUtils {
    companion object {
        fun getDoubleValue(str:String) : Double{
            var value:Double=0.00
            try {
                value=str.replace(",","").replace("%","").toDouble()
            } catch (e: NumberFormatException){
                e.printStackTrace()
            }
            return value
        }
    }
}