package com.fxiaoke.plugin.crm.purchaseorder.action

import com.facishare.fs.metadata.actions.ISelectDetailLookupContext
import com.facishare.fs.metadata.beans.formfields.ObjectReferenceFormField
import com.facishare.fs.modelviews.MultiContext
import com.facishare.fs.pluginapi.crm.controller.product.beans.OrderProductPriceType
import com.facishare.fs.pluginapi.crm.type.ICrmBizApiName
import com.fxiaoke.plugin.crm.deliverynote.utils.StockUtils
import com.fxiaoke.plugin.crm.goodsreceivednote.action.GoodsReceivedNoteDetailLookupAction
import com.fxiaoke.plugin.crm.goodsreceivednote.activity.GoodsReceivedNoteSelectProductAct.Companion.getIntent
import com.fxiaoke.plugin.crm.order.selectproduct.config.PickProductConfig
import com.fxiaoke.plugin.crm.purchaseorder.PurchaseOrderObj

class PurchaseOrderDetailLookupAction(context:MultiContext) : GoodsReceivedNoteDetailLookupAction(context) {
    override fun start(target: ISelectDetailLookupContext) {
        mTarget = target
        if (mTarget == null) {
            return
        }
        var field: ObjectReferenceFormField? = getObjectReferenceIdFormField(ICrmBizApiName.PRODUCT_API_NAME)
        if (field == null) {
            super.start(target)
        } else {
            mPickedField = field
            showChooseActionWithScanQRCodeDialog()
        }
    }

    override fun enableScanCode(): Boolean {
        return StockUtils.getScanCodeType()=="1"
    }

    override fun getWarehouseId(): String {
        return ""
    }

    override fun getQuantifyField(): String {
        return PurchaseOrderObj.PurchaseOrderProductObj.PURCHASE_AMOUNT
    }

    override fun onManualAdd() {
        go2MetaDataSelectProductAct()
    }

    override fun go2MetaDataSelectProductAct() {
        val pickObjConfig = createSelectedProductConfig(mTarget, mPickedField, false)
        val selectProductConfig = PickProductConfig.Builder().needInputLayout(false).build()
        startActivityForResult(getIntent(activity, pickObjConfig, selectProductConfig), SELECT_PRODUCT_CODE)
    }
}