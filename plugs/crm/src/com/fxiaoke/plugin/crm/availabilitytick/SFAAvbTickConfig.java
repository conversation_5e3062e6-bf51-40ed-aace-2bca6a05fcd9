/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.availabilitytick;

import com.facishare.fs.pluginapi.crm.availabilitytick.CrmAvbTickConfig;
import com.facishare.fs.pluginapi.crm.type.CoreObjType;
import com.fxiaoke.plugin.crm.ServiceObjectType;

/**
 * Created by liuyu on 2016/12/15.
 * SFA可用性埋点配置
 */
public class SFAAvbTickConfig extends CrmAvbTickConfig {

    public static String getServiceObjectTypeName(ServiceObjectType objectType) {
        String result = "Nodefine";
        if (objectType != null) {
            switch (objectType) {
                case SalesClue:
                    result = "Saleclue";
                    break;
                case Customer:
                    result = "Customer";
                    break;
                case Contact:
                    result = "Contact";
                    break;
                case Product:
                    result = "Product";
                    break;
                case Payment:
                    result = "Payment";
                    break;
                case Refund:
                    result = "Refund";
                    break;
                case SaleAction:
                    result = "Saleaction";
                    break;
                case Opportunity:
                    result = "Opportunity";
                    break;
                case Bill:
                    result = "Bill";
                    break;
                case Trade:
                    result = "Trade";
                    break;
                case Order:
                    result = "Order";
                    break;
                case ReturnOrder:
                    result = "Returnorder";
                    break;
                case Visit:
                    result = "Visit";
                    break;
                case VisitAction:
                    result = "Visitaction";
                    break;
                case InventoryAction:
                    result = "Inventoryaction";
                    break;
                case Contract:
                    result = "Contract";
                    break;
                case SalesCluePool:
                    result = "Salecluepool";
                    break;
                case HighSeas:
                    result = "Highsea";
                    break;
                case Competitor:
                    result = "Competitor";
                    break;
                case MarketingEvent:
                    result = "Marketingevent";
                    break;
                case Inventory:
                    result = "Inventory";
                    break;
                case CustomerLocation:
                    result = "Multiaddress";
                    break;
                case Invoice:
                    result = "Invoice";
                    break;
                case CrmInfo:
                    result = "Crminfo";
                    break;
            }
        }
        return result;
    }

    /**
     * 手动新建
     *
     * @param objectType
     *
     * @return
     */
    public static String keyForManualAdd(ServiceObjectType objectType) {
        return sGlobalKey + "Manual_Add_" + getServiceObjectTypeName(objectType);
    }

    /**
     * 扫名片新建
     *
     * @param objectType
     *
     * @return
     */
    public static String keyForBcrAdd(ServiceObjectType objectType) {
        return sGlobalKey + "Bcr_Add_" + getServiceObjectTypeName(objectType);
    }

    /**
     * 更换负责人
     *
     * @param objectType
     *
     * @return
     */
    public static String keyForChangeOwner(ServiceObjectType objectType) {
        return sGlobalKey + getServiceObjectTypeName(objectType) + "_Changeowner" ;
    }

    /**
     * 对象详情发送销售记录
     *
     * @param objectType
     *
     * @return
     */
    public static String keyForSendSalesRecord(CoreObjType objectType) {
        return sGlobalKey + getServiceObjectTypeName(ServiceObjectType.valueOfCore(objectType)) + "_Sendsalesrecord";
    }

    /**
     * 快捷发送销售记录
     *
     * @return
     */
    public static String keyForQuickSendSalesRecord() {
        return sGlobalKey + "Quick_Sendsalesrecord";
    }

    /**
     * 列表
     *
     * @param objectType
     *
     * @return
     */
    public static String keyForList(ServiceObjectType objectType) {
        return sGlobalKey + "Getlist_" + getServiceObjectTypeName(objectType);
    }

    /**
     * 详情
     *
     * @param objectType
     *
     * @return
     */
    public static String keyForDetail(ServiceObjectType objectType) {
        return sGlobalKey + "Getdetail_" + getServiceObjectTypeName(objectType);
    }

    /**
     * 销售线索-“转换线索”
     *
     * @return
     */
    public static String keyForSaleclueTransfer() {
        return sGlobalKey + "Saleclue_Transfer";
    }

    /**
     * 销售线索-“转换合作伙伴”
     *
     * @return
     */
    public static String keyForSaleclueTransferPartner() {
        return sGlobalKey + "Saleclue_Transfer_Partner";
    }

    /**
     * 客户-“添加联合跟进人”
     *
     * @return
     */
    public static String keyForCustomerAddpartner() {
        return sGlobalKey + "Customer_Addpartner";
    }

    /**
     * 商机-“阶段变更”
     *
     * @return
     */
    public static String keyForOpportunityChangestage() {
        return sGlobalKey + "Opportunity_Changestage";
    }

    /**
     * 商机-“填写阶段反馈”
     *
     * @return
     */
    public static String keyForOpportunityFeedback() {
        return sGlobalKey + "Opportunity_Feedback";
    }

    /**
     * 商机-“完成销售流程”
     *
     * @return
     */
    public static String keyForOpportunityCompletesalestage() {
        return sGlobalKey + "Opportunity_Completesalestage";
    }

    /**
     * 拜访-“设置协访”
     *
     * @return
     */
    public static String keyForVisitAssistvisit() {
        return sGlobalKey + "Visit_Assistvisit";
    }

    /**
     * 客户“确认”
     *
     * @return
     */
    public static String keyForCustomerApprovalconfirm() {
        return sGlobalKey + "Customer_Approvalconfirm";
    }

    /**
     * 客户“驳回”
     *
     * @return
     */
    public static String keyForCustomerApprovalreject() {
        return sGlobalKey + "Customer_Approvalreject";
    }

    /**
     * 销售订单“确认”
     *
     * @return
     */
    public static String keyForOrderApprovalconfirm() {
        return sGlobalKey + "Order_Approvalconfirm";
    }

    /**
     * 销售订单“驳回”
     *
     * @return
     */
    public static String keyForOrderApprovalreject() {
        return sGlobalKey + "Order_Approvalreject";
    }

    /**
     * 扫名片
     *
     * @return
     */
    public static String keyForBcr() {
        return sGlobalKey + "Bcr";
    }

    /**
     * 根据销售订单ID获取可发货仓库信息埋点
     * add by wubb
     * @return
     */
    public static String keyForGetWarehouseBySalesOrderId(){
        return sGlobalKey + "DeliveryNoteObj_GetWarehouseBySalesOrderId";
    }

    /**
     * 根据销售订单ID和仓库ID获取可发货的产品列表埋点
     * add by wubb
     * @return
     */
    public static String keyForGetCanDeliverProducts(){
        return sGlobalKey + "DeliveryNoteObj_GetCanDeliverProducts";
    }

    /**
     * 确认收货埋点
     * add by wubb
     * @return
     */
    public static String keyForConfirmReceive(){
        return sGlobalKey + "DeliveryNoteObj_ConfirmReceive";
    }

    /**
     * 获取仓库列表
     * add by wubb
     * @return
     */
    public static String keyForGetWarehouseList(){
        return sGlobalKey + "WarehouseObj_QueryList";
    }

    /**
     * 调拨单确认入库埋点
     * add by wubb
     * @return
     */
    public static String keyForRequisitionNoteIsConfirmed(){
        return sGlobalKey + "RequisitionNoteObj_IsConfirmed";
    }

    /**
     * 根据产品id列表查询产品详情埋点
     * add by wubb
     * @return
     */
    public static String keyForProductQueryProductByIds(){
        return sGlobalKey + "ProductObj_QueryProductByIds";
    }

    /**
     * 生成盘盈入库单和盘亏出库单埋点
     * add by wubb
     * @return
     */
    public static String keyForGenerateStockCheckNote(){
        return sGlobalKey + "StockCheckNoteObj_GenerateNote";
    }

    /**
     * 根据库存id列表拉取库存埋点
     * add by wubb
     * @return
     */
    public static String keyForQueryByIds(){
        return sGlobalKey + "StockObj_QueryByIds";
    }

    /**
     * 是否显示实时可用库存
     * add by wubb
     * @return
     */
    public static String keyForShowRealtimeAvailableStock(){
        return sGlobalKey + "StockObj_is_show_real_time_available_stock";
    }

    /**
     * 获取实时可用库存
     * add by wubb
     * @return
     */
    public static String keyForGetRealtimeAvailableStock(){
        return sGlobalKey + "StockObj_get_real_time_available_stock";
    }

    /**
     * 获取产品条形码关联的产品
     * add by wubb
     * @return
     */
    public static String keyForGetRelatedListByBarCode(){
        return sGlobalKey + "ProductObj_get_related_list_by_bar_code";
    }

    /**
     * 根据序列号name，获取库存信息
     * add by wubb
     * @return
     */
    public static String keyForGetInfoBySerialNumberName(){
        return sGlobalKey + "ProductObj_get_info_by_serial_number_name";
    }

    /**
     * 根据批次name，获取库存信息
     * add by wubb
     * @return
     */
    public static String keyForGetInfoByBatchName(){
        return sGlobalKey + "ProductObj_get_info_by_batch_name";
    }

    /**
     * 查询企业库存模块扫码配置
     * add by wubb
     * @return
     */
    public static String keyForQueryScanCodeConfig(){
        return sGlobalKey + "ProductObj_query_scan_code_config";
    }

    /**
     * 查询库存启用开关
     * add by wubb
     * @return
     */
    public static String keyForQueryStockSwitch(){
        return sGlobalKey + "query_stock_switch";
    }

    /**
     * 库存配置查询通用接口
     * add by wubb
     * @return
     */
    public static String keyForGetConfigValues(){
        return sGlobalKey + "get_config_values";
    }
}
