/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.remind.concrete;

import java.io.Serializable;
import java.util.List;

import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.pickerutils.MOPController;
import com.facishare.fs.pickerutils.MOPCounter;
import com.facishare.fs.sizectrlviews.SizeControlTextView;
import com.fxiaoke.cmviews.custom_fragment.FsFragment;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.remind.beans.ActionButtonBean;
import com.fxiaoke.plugin.crm.remind.picker.CrmToDoListMultiPicker;

import android.app.Activity;
import android.database.DataSetObserver;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

/**
 * Author:  wangrz
 * Date:    2019/10/15 15:58
 * Remarks: 选择对象底部碎片
 */
public class RemindSelectObjectBarFrag extends FsFragment {
    private final static String OBJECT_NAME = "objectName";
    private final static String ACTION_BUTTONS = "action_buttons";
    private LinearLayout mButtonsContainer;
    private TextView mContentText;
    private String objectName;
    //操作button
    private List<ActionButtonBean> mActionButtonList;

    private MOPCounter mMOPCounter;
    private CrmToDoListMultiPicker mPicker;
    /** 观察者*/
    private DataSetObserver mPickerObserver;
    /** 点击底部按钮*/
    private IBarClick mOnClickListener;

    private IRegisterCallback registerCallback;

    public static RemindSelectObjectBarFrag getInstance(String objectName, List<ActionButtonBean> buttonList, MOPCounter mopCounter) {
        RemindSelectObjectBarFrag frag = new RemindSelectObjectBarFrag();
        Bundle args = new Bundle();
        args.putString(OBJECT_NAME, objectName);
        args.putSerializable(ACTION_BUTTONS, (Serializable) buttonList);
        args.putSerializable(MOPController.KEY_COUNTER, mopCounter);
        frag.setArguments(args);
        return frag;
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        mActivity = activity;
        if (mActivity instanceof IBarClick) {
            mOnClickListener = (IBarClick) mActivity;
        }
        if (mActivity instanceof IRegisterCallback) {
            registerCallback = (IRegisterCallback) mActivity;
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        initData(savedInstanceState);
        View view = inflater.inflate(getLayoutResId(), null);
        initView(view);
        //设置 buttons
        setActionButtons();
        //更新视图
        updateView();
        return view;
    }

    protected int getLayoutResId() {
        return R.layout.layout_remind_select_object_bar;
    }

    protected void initView(View view) {
        mButtonsContainer = view.findViewById(R.id.layout_buttons_container);
        mContentText = (TextView) view.findViewById(R.id.textView_selectrange_show);
    }

    protected void initData(Bundle savedInstanceState) {
        if (getArguments() != null) {
            objectName = getArguments().getString(OBJECT_NAME);
            mActionButtonList = (List<ActionButtonBean>) getArguments().getSerializable(ACTION_BUTTONS);
            mMOPCounter = (MOPCounter) getArguments().getSerializable(MOPController.KEY_COUNTER);
        }
        mPicker = MOPController.obtainPickerByCounter(mMOPCounter, CrmToDoListMultiPicker.class);
        //初始化数据观察者
        mPickerObserver = new DataSetObserver() {
            @Override
            public void onChanged() {
                //数据改变时，更新视图
                updateView();
            }
        };
        //注册观察者
        registerObserver(mPickerObserver);
    }

    private void setActionButtons(){
        if(mActionButtonList == null || mActionButtonList.isEmpty()){
            return;
        }

        for(int i = 0; i < mActionButtonList.size(); ++i) {
            mButtonsContainer.setVisibility(View.VISIBLE);
            if(i <= 2){
                final ActionButtonBean actionButtonBean = mActionButtonList.get(i);
                SizeControlTextView textView = new SizeControlTextView(getContext());
                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(FSScreen.dp2px(63), FSScreen.dp2px(30));
                layoutParams.leftMargin = FSScreen.dp2px(12);
                textView.setLayoutParams(layoutParams);
                textView.setGravity(Gravity.CENTER);
                textView.setSingleLine();
                textView.setEllipsize(TextUtils.TruncateAt.END);
                textView.setTextSize(12);
                textView.setTextColor(actionButtonBean.getTextColorId());
                textView.setBackgroundResource(actionButtonBean.getBackgroundId());
                textView.setText(actionButtonBean.getLabel());
                textView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if(mOnClickListener != null){
                            mOnClickListener.onBarButtonClick(actionButtonBean);
                        }
                    }
                });
                mButtonsContainer.addView(textView);
            }
        }
    }
    /**
     * 更新界面
     */
    public void updateView() {
        if (isUiSafety()) {
            showLayout();
        }
    }

    public void showLayout() {
        String contentStr = getContentText();
        mContentText.setText(contentStr == null ? "" : contentStr);
    }

    protected String getContentText(){
        return mPicker.getPickedStr(objectName);
    }

    private void registerObserver(DataSetObserver dataSetObserver) {
        if (dataSetObserver == null) return;
        if (registerCallback == null) {
            if (mPicker != null)
                mPicker.registerPickObserver(dataSetObserver);
        } else {
            registerCallback.registerPickObserver(dataSetObserver);
        }
    }

    private void unRegisterObserver(DataSetObserver dataSetObserver) {
        if (dataSetObserver == null) return;
        if (registerCallback == null) {
            if (mPicker != null)
                mPicker.unregisterPickObserver(dataSetObserver);
        } else {
            registerCallback.unregisterPickObserver(dataSetObserver);
        }
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        unRegisterObserver(mPickerObserver);
    }

    public interface IRegisterCallback {
        void registerPickObserver(DataSetObserver dataSetObserver);
        void unregisterPickObserver(DataSetObserver dataSetObserver);
    }

    public interface IBarClick {
        /**
         * 按钮点击操作
         * @param buttonBean
         */
        void onBarButtonClick(ActionButtonBean buttonBean);
    }
}
