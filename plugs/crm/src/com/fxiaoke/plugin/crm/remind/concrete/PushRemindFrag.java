/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.remind.concrete;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.workflow.beans.FindReadNoticesResult;
import com.facishare.fs.workflow.beans.ReadNoticeObject;
import com.facishare.fs.workflow.beans.RefObjectData;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiParameterList;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.fxiaoke.plugin.crm.CrmConfig;
import com.fxiaoke.plugin.crm.ServiceObjectType;
import com.fxiaoke.plugin.crm.commonlist.BaseObjListAdapter;
import com.fxiaoke.plugin.crm.commonlist.ObjListViewController;
import com.fxiaoke.plugin.crm.remind.CrmRemindKeyType;
import com.fxiaoke.plugin.crm.custom_field.beans.UserDefineFieldDataInfo;
import com.fxiaoke.plugin.crm.custom_field.beans.UserDefinedFieldInfo;
import com.fxiaoke.plugin.crm.remind.BaseRemindFragment;
import com.fxiaoke.plugin.crm.remind.PushRemindDetailAct;
import com.fxiaoke.plugin.crm.remind.api.RemindService;
import com.fxiaoke.plugin.crm.remind.beans.ImportJobState;
import com.fxiaoke.plugin.crm.remind.beans.JobListItem;
import com.fxiaoke.plugin.crm.remind.beans.JobListResult;
import com.fxiaoke.plugin.crm.remind.beans.PushRemindItemData;
import com.fxiaoke.plugin.crm.utils.CrmUtils;
import com.fxiaoke.fscommon.http.WebApiExecutionCallbackWrapper;
import com.fxiaoke.stat_engine.events.StatEvent;
import com.fxiaoke.stat_engine.events.session.UeEventSession;
import com.fxiaoke.stat_engine.statuscode.ErrorType;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;

/**
 * 作者 : wangying
 * 实现的主要功能 : 所有的推送消息都走这个, 审批推送/导入助手/工作流消息
 * 创建日期 : 2016年12月15日 18:54
 */
public class PushRemindFrag extends BaseRemindFragment<PushRemindItemData>{

    public static PushRemindFrag getInstance(int index, CrmRemindKeyType type){
        PushRemindFrag frag = new PushRemindFrag();
        frag.setArguments(getBundle(index,type));
        return frag;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = super.onCreateView(inflater, container, savedInstanceState);
        if (CrmRemindKeyType.WorkFlowPush == mRemindType) {
            setNoInfosStr(I18NHelper.getText("crm.concrete.PushRemindFrag.952")/* 暂无工作流消息 */);
        } else if (CrmRemindKeyType.ApprovalPush == mRemindType) {
            setNoInfosStr(I18NHelper.getText("crm.concrete.PushRemindFrag.953")/* 暂无审批消息 */);
        }
        return view;
    }

    @Override
    protected BaseAdapter getAdapter() {
        return new BaseObjListAdapter<PushRemindItemData>(mActivity, ServiceObjectType.PushRemind) {
            @Override
            public ObjListViewController getController() {
                return CrmConfig.viewControllerProvider.getObjListViewController();
            }
        };
    }

    @Override
    protected boolean change2ReadState(PushRemindItemData pushRemindItemData) {
        return true;
    }

    @Override
    protected void go2DetailAct(PushRemindItemData pushRemindItemData) {
        startActivity(PushRemindDetailAct.getIntent(mActivity,mRemindType,pushRemindItemData));
    }

    @Override
    public void pullToRefresh() {
        if(mRemindType == CrmRemindKeyType.ImportAndExportMessages){
            jobListRefresh();
        }else {
            noticeListRefresh();
        }
    }

    @Override
    public void pullToLoadMore() {
        if(mRemindType == CrmRemindKeyType.ImportAndExportMessages){
            jobListLoadMore();
        }else {
            noticeListLoadMore();
        }
    }

    private void jobListRefresh(){
        RemindService.queryJobList(1, PAGE_SIZE, new WebApiExecutionCallbackWrapper<JobListResult>(JobListResult.class,getActivity()) {
            @Override
            public void failed(String error) {
                ToastUtils.show(error);
                getDataFinish(true, false);
            }

            @Override
            public void succeed(JobListResult response) {
                ((BaseObjListAdapter)mAdapter).updateDataList(covert(response,1));
                if(response != null){
                    mHasMore = response.getTotalCount() > PAGE_SIZE;
                }
                getDataFinish(true, true);
            }
        });
    }

    private void jobListLoadMore(){
        final int pageNum = getLastItem() == null ? 1 : getLastItem().pageNum + 1;
        RemindService.queryJobList(pageNum, PAGE_SIZE, new
                WebApiExecutionCallbackWrapper<JobListResult>(JobListResult.class,getActivity()) {
            @Override
            public void failed(String error) {
                ToastUtils.show(error);
                getDataFinish(false, false);
            }

            @Override
            public void succeed(JobListResult response) {
                ((BaseObjListAdapter)mAdapter).addDataList(covert(response,pageNum));
                if(response != null){
                    mHasMore = response.getTotalCount() > PAGE_SIZE * pageNum;
                }
                getDataFinish(false, true);
            }
        });
    }

    private void noticeListRefresh(){
        final UeEventSession eventSession = StatEvent.ueEventSession("CRM_Avb_Approve_Push");
        if (mRemindType == CrmRemindKeyType.ApprovalPush) {
            eventSession.startTick();
        }
        findReadNotices("", 1, PAGE_SIZE,
                new WebApiExecutionCallbackWrapper<FindReadNoticesResult>(FindReadNoticesResult.class,getActivity()) {
                    @Override
                    public void failed(String error) {
                        if (mRemindType == CrmRemindKeyType.ApprovalPush) {
                            eventSession.errorTick(ErrorType.newInstance(getHttpStatusCode(), getFailureType()
                                    .getDetailFailDesc()));
                        }
                        ToastUtils.show(error);
                        getDataFinish(true, false);
                    }

                    @Override
                    public void succeed(FindReadNoticesResult response) {
                        if (mRemindType == CrmRemindKeyType.ApprovalPush) {
                            eventSession.endTick();
                        }
                        ((BaseObjListAdapter)mAdapter).updateDataList(covert(response,1));
                        if(response != null && response.getPageInfoObject() != null){
                            mHasMore = response.getPageInfoObject().getTotalCount() > PAGE_SIZE;
                        }
                        getDataFinish(true, true);
                    }
                });
    }

    private void noticeListLoadMore(){
        final int pageNum = getLastItem() == null ? 1 : getLastItem().pageNum + 1;
        findReadNotices("", pageNum, PAGE_SIZE,
                new WebApiExecutionCallbackWrapper<FindReadNoticesResult>(FindReadNoticesResult.class,getActivity()) {
                    @Override
                    public void failed(String error) {
                        ToastUtils.show(error);
                        getDataFinish(false, false);
                    }

                    @Override
                    public void succeed(FindReadNoticesResult response) {
                        ((BaseObjListAdapter)mAdapter).addDataList(covert(response,pageNum));
                        if(response != null && response.getPageInfoObject() != null){
                            mHasMore = response.getPageInfoObject().getTotalCount() > PAGE_SIZE * pageNum;
                        }
                        getDataFinish(false, true);
                    }
                });
    }

    private List<PushRemindItemData> covert(JobListResult response,int pageNum){
        List<PushRemindItemData> datas = new ArrayList<>();
        String titleFail = "crm.concrete.PushRemindFrag.titleFail"/* 系统异常,{0}{1}操作异常终止 */;
        String titleError = "crm.concrete.PushRemindFrag.titleError"/* 系统异常,{0}无法{1} */;
        String titleNormal = "crm.concrete.PushRemindFrag.titleNormal"/* {0}{1}操作{2} */;

        String contentFail = "crm.concrete.PushRemindFrag.contentFail"/* {0}于{1}进行了{2}{3},异常终止。 */;
        String contentError = "crm.concrete.PushRemindFrag.contentError"/* {0}于{1}进行了{2}{3},无法{4}。 */;
        String contentNormal = "crm.concrete.PushRemindFrag.contentNormal"/* {0}于{1}进行了{2}{3},操作{4}。 */;

        String contentDetailFail = "crm.concrete.PushRemindFrag.contentDetailFail"/* {0}于{1}进行了{2}{3},{4}过程出现异常终止,已经{5}约{6}条数据,请重新尝试。*/;
        String contentDetailError = "crm.concrete.PushRemindFrag.contentDetailError"/* {0}于{1}进行了{2}{3},系统发生异常,无法对数据进行{4},异常码为{5}。 */;
        String contentDetailNormal = "crm.concrete.PushRemindFrag.contentDetailNormal"/* {0}于{1}进行了{2}{3},操作{4},详情如下。 */;
        String contentDetailNoErrorResult = "crm.concrete.PushRemindFrag.contentNormal"/* {0}于{1}进行了{2}{3},操作{4}。 */;
        if(response != null && response.getItems() != null){
            for (JobListItem jItem : response.getItems()) {
                PushRemindItemData itemData = new PushRemindItemData();
                itemData.crmRemindKeyType = mRemindType;
                String typeName = jItem.getApiFullName();
                //操作方式1:导入 2:导出(注意:老数据是空的，都是导入)
                String jobTypeName;
                if(jItem.getJobType() == 2){
                    jobTypeName = I18NHelper.getText("crm.concrete.PushRemindFrag.Export"/*导出*/);
                }else {
                    jobTypeName = I18NHelper.getText("crm.concrete.PushRemindFrag.Import"/*导入*/);
                }
                itemData.jobTypeName = jobTypeName;
                ImportJobState state = ImportJobState.valueOf(jItem.getStatus());
                if(state == ImportJobState.FAIL){
                    itemData.title = I18NHelper.getFormatText(titleFail,typeName, jobTypeName);
                }else if(state == ImportJobState.ERROR){
                    itemData.title = I18NHelper.getFormatText(titleError,typeName, jobTypeName);
                }else {
                    itemData.title = I18NHelper.getFormatText(titleNormal,typeName, jobTypeName, state.desc);
                }

                String showCreator = jItem.getCreaterId();
                if(!TextUtils.isEmpty(jItem.getCreaterName())){
                    showCreator = jItem.getCreaterName();
                }
                String startTime = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.CHINA).format(jItem.getStartTime());

                if(state == ImportJobState.FAIL){
                    itemData.content = I18NHelper.getFormatText(contentFail,showCreator,startTime,typeName,jobTypeName);
                }else if(state == ImportJobState.ERROR){
                    itemData.content = I18NHelper.getFormatText(contentError,showCreator,startTime,typeName,jobTypeName,jobTypeName);
                }else {
                    itemData.content = I18NHelper.getFormatText(contentNormal,showCreator,startTime,typeName,jobTypeName,state.desc);
                }

                if(jItem.getResult() != null){
                    if(state == ImportJobState.FAIL){
                        itemData.detailContent = I18NHelper.getFormatText(contentDetailFail,showCreator,startTime,
                                typeName,jobTypeName, jobTypeName, jobTypeName,String.valueOf(jItem
                                        .getResult().getSucCount()));
                    }else if(state == ImportJobState.ERROR){
                        itemData.detailContent = I18NHelper.getFormatText(contentDetailError,showCreator,startTime,typeName,jobTypeName,jobTypeName, jItem.getResult().getCode());
                    }else {
                        itemData.detailContent = I18NHelper.getFormatText(contentDetailNormal,showCreator,startTime,typeName,jobTypeName,state.desc);
                    }
                }else {
                    if(state == ImportJobState.FAIL || state == ImportJobState.ERROR){
                        itemData.detailContent = I18NHelper.getFormatText(contentDetailNoErrorResult,showCreator,startTime,typeName,jobTypeName,state.desc);
                    }else {
                        itemData.detailContent = I18NHelper.getFormatText(contentDetailNormal,showCreator,startTime,typeName,jobTypeName,state.desc);
                    }
                }

                itemData.time = jItem.getEndTime();
                itemData.pageNum = pageNum;
                //构建自定义字段
                if(state == ImportJobState.FAIL){

                }else if(state == ImportJobState.ERROR){

                }else {
                    List<UserDefinedFieldInfo> infos = new ArrayList<>();
                    List<UserDefineFieldDataInfo> infoDatas = new ArrayList<>();
                    infos.add(CrmUtils.getSimpleFieldInfo(ServiceObjectType.PushRemind,"1", I18NHelper.getFormatText("crm.concrete.PushRemindFrag.fieldTypeName"/* {0}对象 */, jobTypeName), CrmUtils.SystemFieldType.TEXT));
                    infos.add(CrmUtils.getSimpleFieldInfo(ServiceObjectType.PushRemind,"2", I18NHelper.getFormatText("crm.concrete.PushRemindFrag.filedTotalCount"/* {0}数据量 */, jobTypeName), CrmUtils.SystemFieldType.TEXT));
                    infos.add(CrmUtils.getSimpleFieldInfo(ServiceObjectType.PushRemind,"3", I18NHelper.getFormatText("crm.concrete.PushRemindFrag.filedFailCount"/* {0}失败量 */, jobTypeName), CrmUtils.SystemFieldType.TEXT));
                    //6.7 add:操作方式1:导入 2:导出(注意:老数据是空的，都是导入),导入这块原来展示，保留逻辑，导出不展示这两个字段
                    if(jItem.getJobType() != 2) {
                        infos.add(CrmUtils.getSimpleFieldInfo(ServiceObjectType.PushRemind, "4",
                                I18NHelper.getText("crm.concrete.PushRemindFrag.948")/* 数据重复时操作方式 */,
                                CrmUtils.SystemFieldType.TEXT));
                        infos.add(CrmUtils.getSimpleFieldInfo(ServiceObjectType.PushRemind, "5",
                                I18NHelper.getText("crm.concrete.PushRemindFrag.951")/* 新数据操作方式 */,
                                CrmUtils.SystemFieldType.TEXT));
                    }
                    if(jItem.getResult() != null){
                        infoDatas.add(CrmUtils.getSimpleDataInfo("1", typeName));
                        infoDatas.add(CrmUtils.getSimpleDataInfo("2", jItem.getResult().getTotalCount()+""));
                        infoDatas.add(CrmUtils.getSimpleDataInfo("3", jItem.getResult().getFailCount()+""));
                        if(jItem.getJobType() != 2) {
                            infoDatas.add(CrmUtils.getSimpleDataInfo("4", jItem.getRepeatMode()));
                            infoDatas.add(CrmUtils.getSimpleDataInfo("5", jItem.getNewDataMode()));
                        }
                    }
                    itemData.fieldInfos = infos;
                    itemData.dataInfos = infoDatas;
                }
                datas.add(itemData);
            }
        }
        return datas;
    }

    private List<PushRemindItemData> covert(FindReadNoticesResult response,int pageNum){
        List<PushRemindItemData> datas = new ArrayList<>();
        if(response != null && response.getReadNoticeObjects() != null){
            for (ReadNoticeObject jItem : response.getReadNoticeObjects()) {
                PushRemindItemData itemData = new PushRemindItemData();
                itemData.crmRemindKeyType = mRemindType;
                itemData.readNoticeObject = jItem;
                itemData.title = jItem.getMessageTitle();
                itemData.content = jItem.getMessageContent();
                itemData.detailContent = jItem.getMessageContent();
                itemData.time = jItem.getTimeStamp();
                itemData.pageNum = pageNum;
                //构建自定义字段
                List<UserDefinedFieldInfo> infos = new ArrayList<>();
                List<UserDefineFieldDataInfo> infoDatas = new ArrayList<>();
                if(jItem.getRefObjectDatas() != null){
                    for (int i = 0; i < jItem.getRefObjectDatas().size(); i++) {
                        RefObjectData item = jItem.getRefObjectDatas().get(i);
                        ServiceObjectType type = parseApiName(item.getObjType()); //自定义对象传unknown
                        infos.add(CrmUtils.getSimpleFieldInfoByObjectType(item.getObjType(), type.extendObjType.mainKeyName,
                                type));
                        infoDatas.add(CrmUtils.getSimpleDataInfo(item.getObjType(),item.getObjId(),item.getObjName()));
                    }
                }
                itemData.fieldInfos = infos;
                itemData.dataInfos = infoDatas;

                datas.add(itemData);
            }
        }
        return datas;
    }

    private ServiceObjectType parseApiName(String apiName){
        for (ServiceObjectType serviceObjectType : ServiceObjectType.values()) {
            if(TextUtils.equals(serviceObjectType.coreObjType.apiName,apiName)){
                return serviceObjectType;
            }
        }
        return ServiceObjectType.UnKnow;
    }

    /**
     * 查询审批消息或工作流消息
     */
    private void findReadNotices(String entityId, int pageNum, int pageSize,
                                 WebApiExecutionCallback<FindReadNoticesResult> callback) {
        WebApiParameterList params = WebApiParameterList.create()
                .with("M1", "CRM")
                .with("M2", entityId)
                .with("M3", mRemindType.getKey())
                .with("M4", pageNum)
                .with("M5", pageSize);
        WebApiUtils.postAsync("FHE/EM1HPAASMESSAGE", "businessNoticeFcpController/findReadNotices", params, callback);
    }

}
