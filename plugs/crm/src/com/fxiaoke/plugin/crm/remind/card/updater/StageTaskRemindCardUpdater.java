package com.fxiaoke.plugin.crm.remind.card.updater;

import android.graphics.Color;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.list.beans.ListItemArg;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.fxiaoke.plugin.crm.R;

/**
 * 阶段推进器待办卡片信息更新者
 */
public class StageTaskRemindCardUpdater extends BaseRemindCardUpdater {

    public StageTaskRemindCardUpdater(boolean todo) {
        super(todo);
    }

    @Override
    protected void updateActionView(TextView actionView, ListItemArg arg) {
        actionView.setVisibility(View.GONE);
    }

    @Override
    protected void updateStatusView(TextView statusView, ListItemArg arg) {
        if (todo) {
            String todoStr = I18NHelper.getText("meta.layout.layout_bpm_my_todo_task.2924")/* 待处理 */;
            statusView.setText(todoStr);
            statusView.setTextColor(Color.parseColor("#FF8000"));
            statusView.setBackgroundResource(R.drawable.shape_remind_list_status_todo);
        } else {
            String state = arg.objectData.getString("state");
            if (TextUtils.equals(state, "pass")) {
                statusView.setText(I18NHelper.getText("crm.layout.adapter_outdoor_record_item.8193")/* 已完成 */);
                statusView.setTextColor(Color.parseColor("#30c777"));
                statusView.setBackgroundResource(R.drawable.shape_remind_list_status_pass);
                statusView.setVisibility(View.VISIBLE);
            } else if (TextUtils.equals(state, "cancel")) {
                statusView.setText(I18NHelper.getText("xt.x_feed_detail_activity.text.cancled")/* 已取消 */);
                statusView.setTextColor(Color.parseColor("#FF522A"));
                statusView.setBackgroundResource(R.drawable.shape_remind_list_status_timed_out);
                statusView.setVisibility(View.VISIBLE);
            } else if (TextUtils.equals(state, "error")) {
                statusView.setText(I18NHelper.getText("meta.beans.InstanceState.3073")/* 异常 */);
                statusView.setTextColor(Color.parseColor("#FF522A"));
                statusView.setBackgroundResource(R.drawable.shape_remind_list_status_timed_out);
                statusView.setVisibility(View.VISIBLE);
            } else {
                statusView.setVisibility(View.GONE);
            }
        }
    }
}
