package com.fxiaoke.plugin.crm.remind.beans;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.fs.pluginapi.crm.fieldauthority.FieldAuthUtils;

import java.io.Serializable;

/**
 * Created by xudd on 2015/10/19.
 */
public class RemindRecordInfo implements Serializable{
    /**	*  提醒ID	*/
    @JSONField(name="M1")
    public String remindRecordID;
    /**	*  提醒类型: 1-线索超时 2-线索被收回 3-客户将被收回 4-客户报备已通过 5-客户报备未通过 6-客户被收回 7-客户负责人变更 8-新增相关客户 9-相关客户被取消 10-客户被延期 11-权限变更 12-客户被作废 13-客户被合并 14-联系人作废 15-成交被确认 16-成交被驳回 17-回款被确认 18-回款被驳回 19-客户新增服务记录 20-退款被确认 21-退款被驳回	*/
    @JSONField(name="M2")
    public int type;
    /**	*  提醒标题	*/
    @JSONField(name="M3")
    public String title;
    public String mShowTitle;
    /**	*  提醒内容	*/
    @JSONField(name="M4")
    public String content;
    public String mShowContent;
    /**	*  提醒内容2	*/
    @JSONField(name="M5")
    public String content2;
    public String mShowContent2;
    /**	*  数据ID	*/
    @JSONField(name="M6")
    public String dataID;
    /**	*  创建时间	*/
    @JSONField(name="M7")
    public long createTime;
    /**	*  是否未读	*/
    @JSONField(name="M8")
    public boolean isUnRead;
    @JSONField(name="M26")
    public boolean useLink;
    @JSONField(name="M27")
    public String fsLink;

    /**
     * 默认不展开状态
     */
    private boolean mIsExpand = false;

    public RemindRecordInfo() {}

    @JSONCreator
    public RemindRecordInfo(@JSONField(name="M1") String remindRecordID,
                            @JSONField(name="M2") int type,
                            @JSONField(name="M3") String title,
                            @JSONField(name="M4") String content,
                            @JSONField(name="M5") String content2,
                            @JSONField(name="M6") String dataID,
                            @JSONField(name="M7") long createTime,
                            @JSONField(name="M8") boolean isUnRead,
                            @JSONField(name="M26") boolean useLink,
                            @JSONField(name="M27") String fsLink) {
        this.remindRecordID = remindRecordID;
        this.type = type;
        this.title = title;
        this.mShowTitle = FieldAuthUtils.getShowStr(title);
        this.content = content;
        this.mShowContent = FieldAuthUtils.getShowStr(content);
        this.content2 = content2;
        this.mShowContent2 = FieldAuthUtils.getShowStr(content2);
        this.dataID = dataID;
        this.createTime = createTime;
        this.isUnRead = isUnRead;
        this.useLink = useLink;
        this.fsLink = fsLink;
    }

    public boolean isExpand() {
        return mIsExpand;
    }

    public void setExpand(boolean mIsExpand) {
        this.mIsExpand = mIsExpand;
    }

}
