/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.remind.approval.frag;

import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.beans.FilterScene;
import com.facishare.fs.metadata.beans.GetDataListResult;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.ObjectDescribe;
import com.facishare.fs.metadata.beans.Operator;
import com.facishare.fs.metadata.beans.fields.Field;
import com.facishare.fs.metadata.beans.fields.FilterInfo;
import com.facishare.fs.metadata.beans.fields.IObjFieldInfo;
import com.facishare.fs.metadata.beans.fields.Option;
import com.facishare.fs.metadata.beans.fields.SelectOneField;
import com.facishare.fs.metadata.list.MetaDataListFrag;
import com.facishare.fs.metadata.list.adapter.MetaDataListAdapter;
import com.facishare.fs.metadata.list.beans.ListItemArg;
import com.facishare.fs.metadata.list.modelviews.ListContentAdapter;
import com.facishare.fs.workflow.enums.ApproveInstanceStateEnum;
import com.facishare.fs.workflow.utils.ApproveBizHelper;
import com.facishare.fs.workflow.utils.JumpDetailUtil;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.remind.approval.ApprovalRemindType;
import com.fxiaoke.plugin.crm.remind.approval.filter.ApprovalRemindFilterBar;
import com.fxiaoke.plugin.crm.remind.approval.filter.ApprovalRemindFilterItem;
import com.fxiaoke.plugin.crm.remind.approval.filter.ApprovalRemindFilterType;
import com.fxiaoke.plugin.crm.remind.approval.frag.view.ApprovalRemindContentAdapter;
import com.fxiaoke.plugin.crm.remind.approval.processor.ApprovalFilterResult;
import com.fxiaoke.plugin.crm.remind.approval.processor.FilterProcessorConsumer;
import com.fxiaoke.plugin.crm.remind.approval.processor.FilterProcessorManager;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.functions.Function;

/**
 * 审批流程实例列表
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2020/12/1
 */
public class ApprovalInstanceListFrag extends MetaDataListFrag {

    private boolean mHasFilters;
    private ApprovalRemindFilterBar mApprovalRemindFilterBar;
    private FilterProcessorManager mFilterProcessorManager;

    public static ApprovalInstanceListFrag getInstance(String objectDescribeId) {
        ApprovalInstanceListFrag frag = new ApprovalInstanceListFrag();
        frag.setArguments(createArgs(objectDescribeId));
        return frag;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = super.onCreateView(inflater, container, savedInstanceState);
        initApprovalRemindFilterBar(view);
        return view;
    }

    @Override
    protected int getLayoutResource() {
        return R.layout.layout_approval_remind_frag;
    }

    private void initApprovalRemindFilterBar(View view) {
        mApprovalRemindFilterBar = view.findViewById(R.id.approval_remind_filter_bar);
        List<ApprovalRemindFilterItem> filterItems = new ArrayList<>();
        filterItems.add(ApprovalRemindFilterItem
                .obtain(ApprovalRemindFilterType.ASSOCIATED_OBJECT));
        filterItems.add(ApprovalRemindFilterItem.obtain(ApprovalRemindFilterType.INSTANCE_STATUS));
        filterItems.add(ApprovalRemindFilterItem.obtain(ApprovalRemindFilterType.BEGIN_TIME));
        mApprovalRemindFilterBar.updateFilterItems(filterItems);
        mApprovalRemindFilterBar.setFilterTypeConsumer(
                new com.facishare.fs.common_utils.function.Consumer<ApprovalRemindFilterType>() {
                    @Override
                    public void accept(ApprovalRemindFilterType filterType) {
                        mFilterProcessorManager.start(filterType);
                    }
                });
    }

    @Override
    protected void initData(Bundle onSavedInstanceData) {
        super.initData(onSavedInstanceData);
        setCanPull(true);//支持下拉
        mFilterProcessorManager = new FilterProcessorManager(mMultiContext,
                new FilterProcessorConsumer() {
                    @Override
                    public ApprovalRemindType getApprovalRemindType() {
                        return ApprovalRemindType.APPROVAL_INSTANCE;
                    }

                    @Override
                    public List<IObjFieldInfo> getStatusOptions() {
                        return ApprovalInstanceListFrag.this.getStatusOptions();
                    }

                    @Override
                    public void handleFilterResult(ApprovalFilterResult result) {
                        mHasFilters = result.hasFilters;
                        updateByFilter(result.whatApiName, result.filterInfos);
                        String label = result.filterResult.getFilterResultLabel();
                        int color = Color.parseColor("#ff8000");
                        ApprovalRemindFilterType filterType = result.filterResult.filterType;
                        if (TextUtils.isEmpty(label)) {
                            label = filterType.desc;
                            color = Color.parseColor("#545861");
                        }
                        ApprovalRemindFilterItem item =
                                ApprovalRemindFilterItem.obtain(result.filterResult.filterType)
                                        .setLabel(label).setLabelColor(color);
                        mApprovalRemindFilterBar.updateFilterItem(item);
                    }
                });
        if (mOrder != null) {
            mOrder.fieldName = "start_time";//流程发起时间排序
            mOrder.isAsc = false;
        }
    }

    @Override
    protected void refreshHeaderView(GetDataListResult result) {
        super.refreshHeaderView(result);
        View view = getTotalView().getView();
        view.setVisibility(mHasFilters ? View.VISIBLE : View.GONE);
    }

    @Override
    protected MetaDataListAdapter createAdapter() {
        MetaDataListAdapter adapter = super.createAdapter();
        adapter.setContentAdapterProvider(new Function<String, ListContentAdapter<ListItemArg>>() {
            @Override
            public ListContentAdapter<ListItemArg> apply(String s) throws Exception {
                return new ApprovalRemindContentAdapter(ApprovalRemindType.APPROVAL_INSTANCE);
            }
        });
        return adapter;
    }

    @Override
    protected void onListItemClick(ListItemArg arg) {
        if (arg == null || arg.objectData == null) {
            return;
        }
        ObjectData objectData = arg.objectData;
        String objApiName = objectData.getString("object_api_name");
        String objDataId = objectData.getString("object_data_id");
        String instanceId = objectData.getID();
        String statusStr = objectData.getString("state");
        ApproveInstanceStateEnum state = ApproveInstanceStateEnum.getState(statusStr);
        if (state == ApproveInstanceStateEnum.PASS ||
                state == ApproveInstanceStateEnum.REJECT ||
                state == ApproveInstanceStateEnum.CANCEL ||
                state == ApproveInstanceStateEnum.ERROR) {
            ApproveBizHelper
                    .tick(objApiName, objDataId, ApproveBizHelper.ApproveOperation.IntoApprDetail);
            String objectName = objectData.getString("object_data_id__r");
            JumpDetailUtil.go2ApprovalDetailPage(getContext(), objApiName, objDataId, instanceId,
                    objectName);
        } else {
            JumpDetailUtil.dealJump(getContext(), objApiName, objDataId, instanceId);
        }
    }

    private void updateByFilter(String whatApiName, List<FilterInfo> filters) {
        List<FilterInfo> filterInfos = new ArrayList<>();
        if (!TextUtils.isEmpty(whatApiName)) {//关联对象
            filterInfos.add(new FilterInfo("object_api_name", Operator.EQ, whatApiName));
        }
        if (filters != null && !filters.isEmpty()) {
            filterInfos.addAll(filters);
        }
        setFilters(filterInfos).startRefresh();
    }

    private List<IObjFieldInfo> getStatusOptions() {
        ObjectDescribe describe = getObjectDescribe();
        Field field = describe == null ? null : describe.getFields().get("state");
        if (field == null) {
            return null;
        }
        SelectOneField selectOneField = field.to(SelectOneField.class);
        List<Option> options = selectOneField.getOptionsOfMetaData();
        if (options == null || options.isEmpty()) {
            return null;
        }
        return new ArrayList<>(options);
    }

    public void updateByTemplateId(String templateId) {
        FilterScene filterScene = new FilterScene();
        filterScene.apiName = "InCharge";
        filterScene.id = templateId;
        setFilterScene(filterScene);
        if (getXListView() != null) {
            getXListView().setPullRefreshEnable(true);
            getXListView().setPullOutHeadViewEnable(true);
        }
        startRefresh();
    }

    @Override
    protected String getTotalString(String total) {
        return I18NHelper.getFormatText("crm.ApprovalRemindFrag.filter_result", total)/* 查询结果：共{0}条符合筛选条件的审批 */;
    }
}