/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.business_query;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.pluginapi.crm.ICcCRMActions;
import com.facishare.fs.pluginapi.crm.type.CoreObjType;
import com.fxiaoke.plugin.crm.BaseActivity;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.bizevent.BizAction;
import com.fxiaoke.plugin.crm.bizevent.BizHelper;
import com.fxiaoke.plugin.crm.bizevent.BizSubModule;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.fragment.app.FragmentTransaction;
import android.view.View;

/**
 * 工商查询详情页面
 * Created by lixn on 2016/11/14.
 */
public class BusinessInfoAct extends BaseActivity {

    public static final String TAG_FRAG_INFO = "frag_info";
    public static final String TITLE = I18NHelper.getText("crm.business_query.BusinessInfoAct.1674")/* 公司信息 */;
    public static final String ACTION_FILL_BACK = I18NHelper.getText("crm.business_query.BusinessInfoAct.1675")/* 回填 */;
    private String mQueryNumber;
    private boolean mNeedBackFill;

    public static Intent getIntent(Context context, String queryNumber, boolean needBackFill) {
        Intent intent = new Intent(context, BusinessInfoAct.class);
        intent.putExtra(ICcCRMActions.ParamKeysBusinessInfo.companyNo, queryNumber);
        intent.putExtra(BusinessSearchAct.KEY_NEED_BACK_FILL, needBackFill);
        return intent;
    }

    @Override
    protected void onCreate(Bundle arg0) {
        super.onCreate(arg0);
        setContentView(R.layout.activity_business_info);
        initData(arg0);
        initView();
        BusinessInfoFrag frag = BusinessInfoFrag.getInstance(mQueryNumber);
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        if (ft != null) {
            ft.replace(R.id.container, frag, TAG_FRAG_INFO).commitAllowingStateLoss();
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(ICcCRMActions.ParamKeysBusinessInfo.companyNo, mQueryNumber);
        outState.putBoolean(BusinessSearchAct.KEY_NEED_BACK_FILL, mNeedBackFill);
    }

    private void initData(Bundle savedInstance) {
        if (savedInstance == null) {
            if (getIntent() != null) {
                mQueryNumber = getIntent().getStringExtra(ICcCRMActions.ParamKeysBusinessInfo.companyNo);
                mNeedBackFill = getIntent().getBooleanExtra(BusinessSearchAct.KEY_NEED_BACK_FILL, false);
            }
        } else {
            mQueryNumber = savedInstance.getString(ICcCRMActions.ParamKeysBusinessInfo.companyNo);
            mNeedBackFill = savedInstance.getBoolean(BusinessSearchAct.KEY_NEED_BACK_FILL, false);
        }
    }

    private void initView() {
        initTitleCommon();
        mCommonTitleView.setTitle(TITLE);
        mCommonTitleView.addLeftBackAction(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        if (mNeedBackFill) {
            mCommonTitleView.addRightAction(ACTION_FILL_BACK, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    fillBack();
                }
            });
        }
    }

    /**
     * 回填数据
     */
    private void fillBack() {
        BizHelper.commonClBizTick(CoreObjType.BusinessQuery, BizSubModule.Detail, BizAction.WriteBack);
        Intent intent = new Intent();
        intent.putExtra(ICcCRMActions.ParamKeysBusinessInfo.companyNo, mQueryNumber);
        setResult(RESULT_OK, intent);
        finish();
    }

}
