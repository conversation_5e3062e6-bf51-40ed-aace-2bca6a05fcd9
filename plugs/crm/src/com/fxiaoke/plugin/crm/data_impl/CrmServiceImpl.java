/*
 * Copyright (C) 2021 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.data_impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.facishare.fs.pluginapi.crmservice.ICrmService;
import com.facishare.fs.pluginapi.crmservice.GetRightResult;
import com.fxiaoke.fscommon.http.WebApiExecutionCallbackWrapper;
import com.fxiaoke.fscommon.sandbox.SandboxUtils;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiParameterList;
import com.fxiaoke.fshttp.web.http.WebApiUtils;

import android.content.Context;

/**
 * author: wangrz
 * date: 2021/11/3 13:25
 * description:
 */
public class CrmServiceImpl implements ICrmService {
    /**
     * 对象 分版&权限 校验接口
     * 接口逻辑：1、首先判断分版是否支持 2、然后判断权限
     * 6.3.0 增加
     *
     * @param apiNames    要检验的对象
     * @param actionCodes 要校验的权限标识
     * @param callback
     */
    @Override
    public void checkFunctionRight(List<String> apiNames, List<String> actionCodes,
                                   WebApiExecutionCallback<GetRightResult> callback) {
        WebApiParameterList params = WebApiParameterList.create()
                .with("api_names", apiNames)
                .with("action_codes", actionCodes);
        WebApiUtils
                .postAsync("FHE/EM1ANCRM/API/v1/object", "version_privilege/service/check", params,
                        callback);
    }

    @Override
    public void checkObjectRight(Context context, String apiName, String actionCode,
                                 com.facishare.fs.pluginapi.crmservice.GetObjectRightCallback callback) {
        if (callback == null) {
            return;
        }
        List<String> apiNames = new ArrayList<>(1);
        apiNames.add(apiName);
        List<String> actionCodes = new ArrayList<>(1);
        actionCodes.add(actionCode);
        checkFunctionRight(apiNames, actionCodes,
                new WebApiExecutionCallbackWrapper<GetRightResult>(GetRightResult.class,
                        SandboxUtils.getActivityByContext(context)) {
                    @Override
                    public void succeed(GetRightResult getRightResult) {
                        if (getRightResult != null) {
                            callback.onSuccess(getActionRightFromMap(apiName, actionCode,
                                    getRightResult.datas));
                        } else {
                            callback.onSuccess(false);
                        }
                    }

                    @Override
                    public void failed(String error) {
                        super.failed(error);
                        callback.onFail(error);
                    }
                });
    }

    /**
     * 从map中获得对象和相应action的权限
     *
     * @param apiName
     * @return
     */
    @Override
    public boolean getActionRightFromMap(String apiName, String action,
                                                Map<String, Object> map) {
        boolean actionRight = false;
        if (map == null) {
            return false;
        }
        Object obj = map.get(apiName);
        if (obj instanceof Map) {
            Map<String, Object> objMap = (Map<String, Object>) obj;
            Object actionObj = objMap.get(action);
            if (actionObj instanceof Boolean) {
                actionRight = (Boolean) actionObj;
            }
        }
        return actionRight;
    }
}
