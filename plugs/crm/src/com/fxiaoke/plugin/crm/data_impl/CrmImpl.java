/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.data_impl;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.facishare.fs.pluginapi.ICrm;
import com.facishare.fs.pluginapi.crm.beans.CrmObjWrapper;
import com.facishare.fs.pluginapi.crm.beans.FilterComparisonSelectConfig;
import com.facishare.fs.metadata.list.select_obj.picker.ISelectObjInfo;
import com.facishare.fs.pluginapi.crm.biz_api.IAddCrmObject;
import com.facishare.fs.pluginapi.crm.biz_api.IContact;
import com.facishare.fs.pluginapi.crm.biz_api.ICrmCascade;
import com.facishare.fs.pluginapi.crm.biz_api.ICrmData;
import com.facishare.fs.pluginapi.crm.biz_api.ICrmMail;
import com.facishare.fs.pluginapi.crm.biz_api.ICrmObjField;
import com.facishare.fs.pluginapi.crmservice.GetObjectRightCallback;
import com.facishare.fs.pluginapi.crmservice.GetRightResult;
import com.facishare.fs.pluginapi.crmservice.ICrmService;
import com.facishare.fs.pluginapi.crm.biz_api.ICustomer;
import com.facishare.fs.pluginapi.crm.biz_api.IMetaData;
import com.facishare.fs.pluginapi.crm.biz_api.ISelectCrmFilter;
import com.facishare.fs.pluginapi.crm.biz_api.ISelectCrmObject;
import com.facishare.fs.pluginapi.crm.biz_api.IViewCrmObject;
import com.facishare.fs.pluginapi.crm.config.DateSelectConfig;
import com.facishare.fs.pluginapi.crm.config.OutdoorSelectCrmObjConfig;
import com.facishare.fs.pluginapi.crm.config.SelectVisitCustomerConfig;
import com.facishare.fs.pluginapi.crm.controller.objfield.SelectObjFieldConfig;
import com.facishare.fs.pluginapi.crm.func_api.IDateRangeSelect;
import com.facishare.fs.pluginapi.crm.func_api.ILocalContact;
import com.facishare.fs.pluginapi.crm.func_api.IMP;
import com.facishare.fs.pluginapi.crm.type.CoreObjType;
import com.facishare.fs.pluginapi.crm.type.DateFormatFlagEnum;
import com.facishare.fs.pluginapi.fsmail.models.FSMailModel;
import com.facishare.fs.pluginapi.location.PluginFsLocationResult;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Created by liuyu on 2015/10/16.
 */
public class CrmImpl implements ICrm {

    private ICustomer mICustomerProxy;
    private IContact mIContactProxy;

    private IMP mIMPProxy;
    private ICrmData mICrmDataProxy;

    private ISelectCrmObject mISelectCrmObject;
    private IViewCrmObject mIViewCrmObject;

    private ILocalContact mILocalContact;

    private ICrmMail mICrmMailProxy;

    private ISelectCrmFilter mISelectCrmFilterProxy;

    private IAddCrmObject mIAddCrmObject;

    private IDateRangeSelect mDateRangeSelect;

    private IMetaData mIMetaDataProxy;

    private ICrmObjField mCrmObjFieldProxy;

    private ICrmCascade mCrmCascadeProxy;
    private ICrmService mICrmService;

    public CrmImpl() {
        mICustomerProxy = new CustomerImpl();
        mIContactProxy = new ContactImpl();

        mIMPProxy = new MPImpl();
        mICrmDataProxy = new CrmDataImpl();
        mISelectCrmObject = new SelectCrmObjectImpl();
        mIViewCrmObject = new ViewCrmObjectImpl();

        mILocalContact = new LocalContactImpl();

        mICrmMailProxy=new CrmMailImpl();

        mIAddCrmObject = new AddCrmObjectImpl();

        mDateRangeSelect = new DateRangeSelectImpl();

        mISelectCrmFilterProxy = new SelectCrmFilterImpl();

        mIMetaDataProxy = new MetaDataImpl();

        mCrmObjFieldProxy = new CrmObjFieldImpl();
        mCrmCascadeProxy = new CrmCascadeImpl();
        mICrmService = new CrmServiceImpl();
    }


    @Override
    public void go2MP(Activity context, int requestCode) {
        mIMPProxy.go2MP(context, requestCode);
    }

    @Override
    public void go2MPSpecialOne(Activity context, boolean hasCrmRights) {
        mIMPProxy.go2MPSpecialOne(context, hasCrmRights);
    }

    @Override
    public void go2NearCustomerActivity(Activity context, PluginFsLocationResult customerAddress, int scope) {
        mICustomerProxy.go2NearCustomerActivity(context, customerAddress, scope);
    }

    @Override
    public void showCrmData(Activity context, int empId, int reUserId, long startTime, long endTime) {
        mICrmDataProxy.showCrmData(context, empId, reUserId, startTime, endTime);
    }

    @Override
    public void selectCrmData(Activity context, int requestCode) {
        mICrmDataProxy.selectCrmData(context, requestCode);
    }

    @Override
    public void selectCrmData(Activity context, long startTime, long endTime, int requestCode) {
        mICrmDataProxy.selectCrmData(context, startTime, endTime, requestCode);
    }

    @Override
    public void go2ViewCrmObject(@NonNull Activity context, @NonNull String apiName, @NonNull String dataID,
                                 @Nullable Bundle extraData) {
        mIViewCrmObject.go2ViewCrmObject(context, apiName, dataID, extraData);
    }

    @Override
    public void go2ViewCrmObject(@NonNull Activity context, @NonNull String apiName, @NonNull String dataID) {
        mIViewCrmObject.go2ViewCrmObject(context, apiName, dataID);
    }

    @Override
    public void go2ViewCrmObject(Activity context, CoreObjType type, String id) {
        mIViewCrmObject.go2ViewCrmObject(context, type, id);
    }

    @Override
    public void go2ViewCrmObjectList(Activity context, String apiName) {
        mIViewCrmObject.go2ViewCrmObjectList(context, apiName);
    }

    @Override
    public void go2ViewCrmObjectList(Activity context, String apiName, @Nullable Bundle extraData) {
        mIViewCrmObject.go2ViewCrmObjectList(context, apiName, extraData);
    }

    @Override
    public void go2SalesGroupAct(Activity context, String apiName, String id) {
        mIViewCrmObject.go2SalesGroupAct(context, apiName, id);
    }

    @Override
    public void go2SalesGroupAct(Activity context, String apiName, String id, @Nullable Bundle extraData) {
        mIViewCrmObject.go2SalesGroupAct(context, apiName, id, extraData);
    }

    @Override
    public void go2SelectMultiCrmObj(Activity context, CrmObjWrapper objWrapper, int requestCode) {
        mISelectCrmObject.go2SelectMultiCrmObj(context, objWrapper, requestCode);
    }

    @Override
    public void go2SelectCrmObject(Activity context, OutdoorSelectCrmObjConfig config,
                                   int requestCode) {
        mISelectCrmObject.go2SelectCrmObject(context, config, requestCode);
    }

    @Override
    public LinkedHashMap<String, List<ISelectObjInfo>> getSelectedCrmObjectList(Intent intent) {
        return mISelectCrmObject.getSelectedCrmObjectList(intent);
    }

    @Override
    public void selectLocalContact(Activity activity, String title, int requestCode) {
        mILocalContact.selectLocalContact(activity, title, requestCode);
    }

    @Override
    public void searchLocalContact(Activity activity, String title, int requestCode) {
        mILocalContact.searchLocalContact(activity, title, requestCode);
    }

    @Override
    public void go2SelectVisitCustomer(Activity activity, SelectVisitCustomerConfig config, int requestCode){
        mICustomerProxy.go2SelectVisitCustomer(activity,config,requestCode);
    }
    @Override
    public void go2NoCrmActivity(Activity activity) {
        mICustomerProxy.go2NoCrmActivity(activity);
    }

    @Override
    public void go2MailTemplateListActivity(Activity context, FSMailModel mailModel) {
        mICrmMailProxy.go2MailTemplateListActivity(context, mailModel);
    }

    @Override
    public void go2AddCrmObject(Activity context, String apiName, boolean jumpDetail, Bundle extraData) {
        mIAddCrmObject.go2AddCrmObject(context, apiName, jumpDetail, extraData);
    }

    @Override
    public void go2AddCrmObjectForResult(Activity context, String apiName, Bundle extraData, int requestCode) {
        mIAddCrmObject.go2AddCrmObjectForResult(context, apiName, extraData, requestCode);
    }

    @Override
    public void gotEditCrmObject(Activity context, String apiName, String dataId, int requestCode) {
        mIAddCrmObject.gotEditCrmObject(context, apiName, dataId, requestCode);
    }

    @Override
    public void go2SelectDateRange(Activity activity, int selectDateRangeId, int requestCode) {
        mDateRangeSelect.go2SelectDateRange(activity, selectDateRangeId, requestCode);
    }

    @Override
    public void go2SelectDateRange(Activity activity, int selectDateRangeId, int dateGroupEnumFlags,
                                   DateFormatFlagEnum dateRegEnum, int requestCode) {
        mDateRangeSelect.go2SelectDateRange(activity, selectDateRangeId, dateGroupEnumFlags, dateRegEnum, requestCode);
    }

    @Override
    public void go2SelectDateRange(Activity activity, DateSelectConfig selectConfig, int requestCode) {
        mDateRangeSelect.go2SelectDateRange(activity, selectConfig, requestCode);
    }

    @Override
    public void go2SelectCrmFilterComparisonAct(Activity context, FilterComparisonSelectConfig config, int requestCode) {
        mISelectCrmFilterProxy.go2SelectCrmFilterComparisonAct(context, config, requestCode );
    }

    @Override
    public void go2SelectCrmFilterTimeAct(Activity context, String type, String timeValue, DateSelectConfig dateSelectConfig ,
                                          boolean showResetBtn, int requestCode) {
        mISelectCrmFilterProxy.go2SelectCrmFilterTimeAct(context, type, timeValue, dateSelectConfig, showResetBtn,
                requestCode);
    }

    @Override
    public void go2GlobalVarListAct(Activity context, String varType, String selectedVarApiName, int requestCode) {
        mIMetaDataProxy.go2GlobalVarListAct(context, varType, selectedVarApiName, requestCode);
    }

    @Override
    public void go2SelectObjFieldActivity(Activity context, SelectObjFieldConfig config, int requestCode){
        mCrmObjFieldProxy.go2SelectObjFieldActivity(context,config,requestCode);
    }

    @Override
    public void go2CityCascadeActivity(Activity context, boolean isCityCascade, boolean isSelectedByLevel,
                                       List<String> selectedIds, int requestCode) {
        mCrmCascadeProxy.go2CityCascadeActivity(context,isCityCascade, isSelectedByLevel, selectedIds, requestCode);
    }

    @Override
    public void go2CityCascadeActivity(Activity context, boolean isCityCascade, boolean isSelectedByLevel,
                                       List<String> selectedIds, int areaNode, int requestCode) {
        mCrmCascadeProxy.go2CityCascadeActivity(context,isCityCascade, isSelectedByLevel, selectedIds, areaNode,
                requestCode);
    }

    @Override
    public void checkFunctionRight(List<String> apiNames, List<String> actionCodes,
                                   WebApiExecutionCallback<GetRightResult> callback) {
        mICrmService.checkFunctionRight(apiNames, actionCodes, callback);
    }

    @Override
    public void checkObjectRight(Context context, String apiName, String actionCode,
                                 GetObjectRightCallback callback) {
        mICrmService.checkObjectRight(context, apiName, actionCode, callback);
    }

    @Override
    public boolean getActionRightFromMap(String apiName, String action, Map<String, Object> map) {
        return mICrmService.getActionRightFromMap(apiName, action, map);
    }
}

