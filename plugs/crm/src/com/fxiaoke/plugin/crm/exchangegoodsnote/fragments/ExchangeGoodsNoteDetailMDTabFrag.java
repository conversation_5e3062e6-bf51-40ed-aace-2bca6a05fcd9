/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.exchangegoodsnote.fragments;

import android.os.Bundle;

import com.facishare.fs.metadata.detail.fragment.DetailMDTabFrag;
import com.facishare.fs.metadata.detail.fragment.DetailTabFragArg;
import com.facishare.fs.metadata.modify.Scene;
import com.facishare.fs.metadata.modify.modelviews.table.TableListAdapter;
import com.fxiaoke.plugin.crm.exchangegoodsnote.modelviews.table.ExchangeGoodsNoteProductTableListAdapter;

/**
 * Created by wubb on 2019/7/29.
 */

public class ExchangeGoodsNoteDetailMDTabFrag extends DetailMDTabFrag {
    public static DetailMDTabFrag newInstance(DetailTabFragArg detailTabFragArg) {
        ExchangeGoodsNoteDetailMDTabFrag frag = new ExchangeGoodsNoteDetailMDTabFrag();
        if (detailTabFragArg == null) {
            return frag;
        }
        if (!(detailTabFragArg instanceof DetailMDFragArg)) {
            throw new IllegalArgumentException("arg must be instanceof DetailMDFragArg");
        }

        Bundle data = new Bundle();
        data.putSerializable(KEY_FRAG_ARG, detailTabFragArg);
        frag.setArguments(data);
        return frag;
    }

    @Override
    protected TableListAdapter createListAdapter() {
        return new ExchangeGoodsNoteProductTableListAdapter(mMultiContext, Scene.INFO,true);
    }
}
