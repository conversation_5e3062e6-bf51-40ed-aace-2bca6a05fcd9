/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.outbounddeliverynote.presenter;

import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.modify.MetaModifyConfig;
import com.facishare.fs.metadata.modify.contract.MetaDataAddOrEditContract;
import com.facishare.fs.metadata.modify.master_detail.IModifyDetailFrag;
import com.facishare.fs.metadata.modify.master_detail.IModifyMasterFrag;
import com.facishare.fs.metadata.modify.master_detail.MetaDataModifyDetailFrag;
import com.facishare.fs.metadata.modify.master_detail.MetaDataModifyMasterFrag;
import com.fxiaoke.plugin.crm.deliverynote.presenter.BaseStockAddOrEditPresenter;
import com.fxiaoke.plugin.crm.outbounddeliverynote.fragment.OutboundDeliveryNoteModifyDetailFrag;
import com.fxiaoke.plugin.crm.outbounddeliverynote.fragment.OutboundDeliveryNoteModifyMasterFrag;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class OutboundDeliveryNoteAddOrEditPresenter extends BaseStockAddOrEditPresenter {
    public OutboundDeliveryNoteAddOrEditPresenter(MetaDataAddOrEditContract.View view, MetaModifyConfig config) {
        super(view, config);
    }

    @Override
    protected IModifyMasterFrag createMasterFrag(MetaDataModifyMasterFrag.ModifyMasterFragArg arg) {
        return OutboundDeliveryNoteModifyMasterFrag.newInstance(arg);
    }

    @Override
    protected IModifyDetailFrag createDetailFrag(MetaDataModifyDetailFrag.ModifyDetailFragArg fragArg) {
        return OutboundDeliveryNoteModifyDetailFrag.newInstance(fragArg);
    }

    @Override
    protected void tempChangeMasterData(ObjectData masterObjData,Map<String, List<ObjectData>> detailData) {
        if (!mConfig.isEditType()){
            removeIdField(detailData);
        }
    }
}
