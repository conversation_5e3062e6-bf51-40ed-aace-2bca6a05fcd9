/*
 * Copyright (C) 2022 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.uipaas.utils;

import com.fxiaoke.fxlog.FCLog;

public class AppCustomLogUtils {
    public static final String TAG = "AppCustomHome";

    public static void debugLog(String msg) {
        if (FCLog.isDebugMode() || FCLog.isBuildDebug()) {
            FCLog.d(TAG, msg);
        }
    }

    public static void infoLog(String msg) {
        FCLog.i(TAG, msg);
    }

    public static void errorLog(String msg) {
        FCLog.i(TAG, msg);
    }

    public static void warnLog(String msg) {
        FCLog.w(TAG, msg);
    }
}
