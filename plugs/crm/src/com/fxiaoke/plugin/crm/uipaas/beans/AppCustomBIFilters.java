/*
 * Copyright (C) 2024 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.uipaas.beans;

import java.io.Serializable;

public class AppCustomBIFilters  implements Serializable {
    private String filterType;
    private String filterData;
    private long updateTime;
    public AppCustomBIFilters() {
        super();
    }

    public AppCustomBIFilters(String filterType, String filterData) {
        this.filterType = filterType;
        this.filterData = filterData;
    }

    public String getFilterType() {
        return filterType;
    }

    public void setFilterType(String filterType) {
        this.filterType = filterType;
    }

    public String getFilterData() {
        return filterData;
    }

    public void setFilterData(String filterData) {
        this.filterData = filterData;
    }
    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }
}