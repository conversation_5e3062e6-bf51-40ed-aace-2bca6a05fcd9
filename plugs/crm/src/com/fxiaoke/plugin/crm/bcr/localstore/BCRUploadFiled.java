/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.bcr.localstore;


import com.facishare.fs.common_utils.DateTimeUtils;
import com.facishare.fs.pluginapi.AccountManager;
import com.fxiaoke.location.api.FsLocationResult;
import com.fxiaoke.location.impl.FsMultiLocationManager;
import com.fxiaoke.plugin.crm.App;
import com.fxiaoke.plugin.crm.bcr.scanner.BCRResultNewEntity;
import com.lidroid.xutils.util.DeviceInfoUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import android.text.TextUtils;

/**
 * <AUTHOR>
 * @date 2019/4/269:40
 * @description
 */
public class BCRUploadFiled {

    private Map<String, Object> mMap;
    //图片名称
    private static final String NPATH = "nPath";
    //扫码eid
    private static final String EID = "eid";
    //扫码uid
    private static final String UID = "uid";
    //扫码日期
    private static final String OPERATEDATE = "operateDate";
    //扫码地点-纬度
    private static final String LATITUDE = "latitude";
    //扫码地点-经度
    private static final String LONGITUDE = "longitude";
    //扫码地点-文本地址
    private static final String ADDRESS = "address";
    //扫码手机品牌
    private static final String DEVICETYPE = "deviceType";

    //姓名
    private static final String NAME = "name";
    //姓名拼音
    private static final String SPELLNAME = "spellName";
    //部门
    private static final String DEPARTMENT = "department";
    //职务
    private static final String POST = "post";
    //公司名字
    private static final String COMPANYNAME = "companyName";
    //公司拼音名字
    private static final String COMPANYSPELLNAME = "companySpellName";
    //公司地址
    private static final String COMPANYADDRESS = "companyAddress";
    //公司网址
    private static final String COMPANYWEBSITE = "companyWebSite";
    //备注
    private static final String REMARK = "remark";
    //性别
    private static final String GENDER = "gender";
    //生日
    private static final String BIRTHDAY = "birthday";
    //兴趣
    private static final String INTEREST = "interest";
    private static final String TEL = "tel";
    private static final String MOBILEPHONE = "mobilePhone";
    private static final String EMAIL = "email";
    private static final String FAX = "fax";
    private static final String SINAWEIBO = "sinaWeibo";
    private static final String TENCENTWEIBO = "tencentWeibo";
    private static final String WECHAT = "weChat";
    private static final String QQ = "qq";
    /**
     * 名片本地路径
     */
    private static final String MP_PATH = "mpPath";

    public BCRUploadFiled() {
        mMap = new HashMap<>();
    }

    public BCRUploadFiled(Map<String, Object> mMap) {
        this.mMap = mMap;
        if (mMap == null) {
            mMap = new HashMap<>();
        }
    }

    public void setnPath(String nPath) {
        mMap.put(NPATH, nPath);
    }

    public Map<String, Object> getMap() {
        return mMap;
    }

    public void setmMap(Map<String, Object> mMap) {
        this.mMap = mMap;
    }

    private void createCommonField() {
        this.mMap.put(EID, AccountManager.getAccount().getEnterpriseAccount());
        this.mMap.put(UID, AccountManager.getAccount().getEmployeeId());
        this.mMap.put(OPERATEDATE, DateTimeUtils.getSystemTime(App.getInstance(), System.currentTimeMillis()));
        this.mMap.put(DEVICETYPE, DeviceInfoUtils.getDeviceModel());
        FsLocationResult lastLocation = FsMultiLocationManager.getInstance().getLastLocation();
        if (lastLocation != null) {
            this.mMap.put(LATITUDE, lastLocation.getLatitude());
            this.mMap.put(LONGITUDE, lastLocation.getLongitude());
            this.mMap.put(ADDRESS, lastLocation.getAddress());
        }
    }

    public void transfermToLocalField(BCRResultNewEntity resultEntity) {
        createCommonField();
        if (resultEntity != null && resultEntity.isLegal()) {
            this.mMap.put(NAME, resultEntity.getName());
            this.mMap.put(POST, resultEntity.getTitle());
            this.mMap.put(COMPANYNAME, resultEntity.getOrganization());
            this.mMap.put(COMPANYADDRESS, toArray(resultEntity.getAddress()));
            this.mMap.put(COMPANYWEBSITE, resultEntity.getUrl());
            this.mMap.put(FAX, toArray(resultEntity.getWorkFax()));
            this.mMap.put(EMAIL, toArray(resultEntity.getEmail()));
            this.mMap.put(TEL, getTelList(resultEntity));
            this.mMap.put(MOBILEPHONE, getMobileList(resultEntity));
        }
    }

    private List<String> getTelList(BCRResultNewEntity resultEntity){
        List<String> result = new ArrayList<>();
        if(!TextUtils.isEmpty(resultEntity.getHomePhone())) {
            result.add(resultEntity.getHomePhone());
        }
        if(!TextUtils.isEmpty(resultEntity.getWorkPhone())) {
            result.add(resultEntity.getWorkPhone());
        }
        return result;
    }
    private List<String> getMobileList(BCRResultNewEntity resultEntity){
        List<String> result = new ArrayList<>();
        if(!TextUtils.isEmpty(resultEntity.getTelephone())) {
            result.add(resultEntity.getTelephone());
        }
        if(!TextUtils.isEmpty(resultEntity.getMobile())) {
            result.add(resultEntity.getMobile());
        }
        return result;
    }
    private List<String> toArray(String source) {
        List<String> result = new ArrayList<>();
        if (source != null) {
            result.add(source);
        }
        return result;
    }
}
