package com.fxiaoke.plugin.crm.local_contact;

import com.facishare.fs.i18n.I18NHelper;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.facishare.fs.common_utils.permission.GrantedExecuter;
import com.facishare.fs.pluginapi.contact.beans.LocalContactEntity;
import com.fxiaoke.cmviews.search_view.FSFragSearchView;
import com.fxiaoke.cmviews.search_view.SearchListener;
import com.fxiaoke.fscommon_res.permission.PermissionExecuter;
import com.fxiaoke.fxlog.module.CrmLog;
import com.fxiaoke.plugin.crm.BaseActivity;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.local_contact.controler.LocalContactCtrl;
import com.fxiaoke.plugin.crm.local_contact.controler.LocalContactCtrl.GetLocalContactListner;
import com.fxiaoke.plugin.crm.local_contact.controler.LocalContactPicker;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import android.text.TextUtils;
import android.view.View;
import android.view.View.OnClickListener;

/**
 * 搜索本地通讯录数据
 * 返回List<LocalContactEntity>，在onActResult获取
 *
 * <AUTHOR>
 */
public class SearchLocalContactAct extends BaseActivity {

    public static final String TAG = SearchLocalContactAct.class.getSimpleName().toString();

    /**
     * 搜索本地联系人
     */
    public static final int KEY_REQUEST_SEARCH = 10930;
    public static final String KEY_RESULT_DATA = "result_data";

    private static final String KEY_TITLE = "title";
    private String mTitle;
    /**
     * 搜索控件
     */
    private FSFragSearchView mSearchView;


    /**
     * 全部本地联系人碎片
     */
    private SelectLocalContactFrag mFrag;
    /**
     * 全部本地联系人数据
     */
    private List<LocalContactEntity> mDatas;

    private FragmentManager mManager;
    private SelectLCBarFrag mSelectBarFrag;

    private PermissionExecuter permissionExecuter= new PermissionExecuter(); //权限管理

    /**
     * 入口
     *
     * @param context
     * @param title
     * @return
     */
    public static Intent getIntent(Context context,
                                   String title) {
        Intent intent = new Intent(context, SearchLocalContactAct.class);
        if (!TextUtils.isEmpty(title)) {
            intent.putExtra(KEY_TITLE, title);
        }
        return intent;
    }

    /**
     * 搜索回调
     */
    private SearchListener mSearchListener = new SearchListener() {
        @Override
        public void onSearchContent(final CharSequence s) {
            List<LocalContactEntity> searchResult = null;
            searchResult = searchLC(s);
            mFrag.updateData(searchResult);
            if (searchResult == null || searchResult.size() < 1) {
                //隐藏
                mSearchView.hideFragment(mManager);
            } else {
                //显示
                mSearchView.showFragment(mManager);
            }
        }

        @Override
        public void onCancelClick() {
            setResult(RESULT_CANCELED);
            finish();
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_search_local_contact);
        parseIntent(getIntent());
        initTitleEx();
        initView();
        bindView();
        loadData();
    }

    private void parseIntent(Intent intent) {
        if (intent != null) {
            mTitle = intent.getStringExtra(KEY_TITLE);
        }
        if (TextUtils.isEmpty(mTitle)) {
            mTitle = I18NHelper.getText("crm.local_contact.SelectLocalContactAct.1308")/* 搜索本地联系人 */;
        }
        mManager = getSupportFragmentManager();
    }

    protected void initTitleEx() {
        super.initTitleEx();
        mCommonTitleView.setMiddleText(mTitle);
        mCommonTitleView.addLeftAction(R.string.return_before_new_normal, new OnClickListener() {

            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }

    private void initView() {
        //搜索控件
        mSearchView = (FSFragSearchView) findViewById(R.id.search_layout);
        mSearchView.setEditHint(I18NHelper.getText("mail.common.common.search_contacts")/* 搜索联系人 */);
        mSearchView.setNoContentStr(I18NHelper.getText("mail.common.common.search_contacts")/* 搜索联系人 */);
        mSearchView.setListener(mSearchListener);
        //搜索结果碎片
        mFrag = SelectLocalContactFrag.getInstance(false, false);
        mSearchView.setFragment(mFrag, mManager);
        //底部碎片
        FragmentTransaction ft_bottom = mManager.beginTransaction();
        mSelectBarFrag = SelectLCBarFrag.newInstance(false,SelectLCBarFrag.ShowType.Text);
        mSelectBarFrag.setConfirmClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                onClickConfirm();
            }
        });
        ft_bottom.add(R.id.bottom_fragment, mSelectBarFrag);
        ft_bottom.commitAllowingStateLoss();
    }

    private void bindView() {

    }

    /**
     * 加载数据
     * 这里比较复杂，需要异步读取
     */
    private void loadData() {
        if (!PermissionExecuter.hasAllPermissions(this,
                new String[] {Manifest.permission.READ_PHONE_STATE, Manifest.permission.READ_CONTACTS})) {
            permissionExecuter.requestPermissions(this,
                    new String[] {Manifest.permission.READ_PHONE_STATE, Manifest.permission.READ_CONTACTS},
                    new GrantedExecuter() {
                        @Override
                        public void exe() {
                            loadData();
                        }
                    });
            return;
        }
        inited=true;
        showLoading();
        LocalContactCtrl.waitForDatas(mContext, new GetLocalContactListner() {

            @Override
            public void dataReady(boolean isReady) {
                mDatas = LocalContactCtrl.getPhoneContactInfoList();
                CrmLog.d(TAG, "dataReady mDatas " + mDatas.size());
                filtNoName();
                dismissLoading();
            }
        });
    }

    boolean inited;
    @Override
    protected void onResume() {
        super.onResume();
        if(!inited){
            loadData();
        }
    }


    /**
     * 过滤没有名字的
     */
    private void filtNoName() {
        Iterator<LocalContactEntity> it;
        LocalContactEntity localContactEntity;
        if (mDatas != null && mDatas.size() > 0) {
            it = mDatas.iterator();
            while (it.hasNext()) {
                localContactEntity = it.next();
                if (TextUtils.isEmpty(localContactEntity.getName())) {
                    it.remove();
                }
            }
        }
    }

    /**
     * 搜索联系人
     * @return
     */
    private List<LocalContactEntity> searchLC(CharSequence str) {
        if (mDatas == null || mDatas.size() < 1 || TextUtils.isEmpty(str)) {
            return null;
        }
        List<LocalContactEntity> resultList = new ArrayList<LocalContactEntity>();
        LocalContactEntity data = null;
        for (int i = 0; i < mDatas.size(); i++) {
            data = mDatas.get(i);
            if (!TextUtils.isEmpty(data.getName()) && data.getName().contains(str)) {
                resultList.add(data);
            }
        }
        return resultList;
    }

    /**
     * 确认
     */
    private void onClickConfirm() {
        List<LocalContactEntity> selectList = LocalContactPicker.getSelectedPersonList();
        if (selectList != null && selectList.size() > 0) {
            handleResultData(selectList);
        } else {

        }
    }

    /**
     * 处理结果数据
     */
    private void handleResultData(final List<LocalContactEntity> selectList) {
        showLoading();
        LocalContactCtrl.getContactDetailInfo(mContext, selectList, new LocalContactCtrl.GetDetailLocalContactListner() {
            @Override
            public void dataReady(final List<LocalContactEntity> dataList) {
                dismissLoading();
                Intent intent = new Intent();
                intent.putExtra(KEY_RESULT_DATA, (Serializable) LocalContactPicker.getSelectedPersonList());
                setResult(RESULT_OK, intent);
                finish();
            }
        });
    }

    @Override
    public void onBackPressed() {
        setResult(RESULT_CANCELED);
        super.onBackPressed();
    }

    @Override
    protected String getLoadingContentStr() {
        return null;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

    }

}
