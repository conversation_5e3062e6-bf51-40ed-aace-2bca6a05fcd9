/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.quote.modify.historyquote;

import java.util.Collection;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.config.MetaDataConfig;
import com.facishare.fs.metadata.list.beans.ListItemArg;
import com.facishare.fs.metadata.list.modelviews.ListContentAdapter;
import com.facishare.fs.metadata.list.modelviews.ListItemContentMView;
import com.facishare.fs.metadata.list.modelviews.ListItemFieldArg;
import com.facishare.fs.metadata.list.modelviews.field.controller.ListFormFieldMViewCtrl;
import com.facishare.fs.modelviews.MultiContext;
import com.facishare.fs.modelviews.controller.ModelViewController;
import com.facishare.fs.modelviews.presenter.BaseModelViewPresenter;
import com.fxiaoke.cmviews.BaseListAdapter;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.onsale.bom.view.BomExpandPicker;
import com.fxiaoke.plugin.crm.onsale.bom.view.IBomExpandPicker;
import com.fxiaoke.plugin.crm.order.adapter.SkuListContentAdapter;
import com.fxiaoke.plugin.crm.order.presenter.QuantityFieldMViewPresenter;
import com.fxiaoke.plugin.crm.order.utils.MDOrderProductUtils;
import com.fxiaoke.plugin.crm.quote.modify.historyquote.bean.ListItemArgWrapper;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;

/**
 * 选择历史报价适配器
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2019/12/16
 */
public class SelectHistoryQuoteAdapter extends BaseListAdapter<ListItemArgWrapper, SelectHistoryQuoteAdapter.Holder> {

    private MultiContext mMultiContext;
    private LayoutInflater mInflater;
    private OnConfirmClickListener mListener;
    private IBomExpandPicker mBomExpandPicker = new BomExpandPicker();

    public SelectHistoryQuoteAdapter(@NonNull MultiContext multiContext) {
        super(multiContext.getContext());
        this.mMultiContext = multiContext;
        mInflater = LayoutInflater.from(multiContext.getContext());
    }

    @Override
    protected View createConvertView(Context context, int position, ListItemArgWrapper wrapper) {
        return mInflater.inflate(R.layout.item_select_history_quote_layout, null);
    }

    @Override
    protected void updateView(Holder holder, int position, ListItemArgWrapper wrapper) {
        holder.mHistoryQuoteText.setText(wrapper.getTypeText());
        updateDataContainerView(holder.mDataContainer, wrapper);
        holder.mNoDataText.setVisibility(wrapper.hasData ? View.GONE : View.VISIBLE);
        holder.mNoDataText
                .setText(I18NHelper.getText("crm.order.SelectHistoryQuoteAdapter.no_history_quote")/* 暂无历史报价 */);
        holder.mConfirmText.setVisibility(wrapper.hasData ? View.VISIBLE : View.GONE);
        holder.mConfirmText
                .setText(I18NHelper.getText("crm.order.SelectHistoryQuoteAdapter.user_this_price")/* 使用该价格 */);
        holder.mConfirmText.setTag(R.id.tv_confirm, wrapper);
        holder.mConfirmText.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mListener != null) {
                    ListItemArgWrapper itemArgWrapper = (ListItemArgWrapper) v.getTag(R.id.tv_confirm);
                    mListener.onClickConfirm(itemArgWrapper);
                }
            }
        });
    }

    @Override
    protected Holder createHolder(View convertView, int position, ListItemArgWrapper wrapper) {
        Holder holder = new Holder();
        holder.mHistoryQuoteText = convertView.findViewById(R.id.tv_history_quote_info);
        holder.mDataContainer = convertView.findViewById(R.id.data_container);
        holder.mNoDataText = convertView.findViewById(R.id.tv_no_data);
        holder.mConfirmText = convertView.findViewById(R.id.tv_confirm);
        return holder;
    }

    private void updateDataContainerView(ViewGroup containerView, ListItemArgWrapper wrapper) {
        containerView.removeAllViews();
        if (wrapper.hasData) {//有数据才进行UI渲染
            HistoryQuoteItemContentView contentMView = new HistoryQuoteItemContentView(mMultiContext);
            HistoryQuoteItemContainerView itemView = new HistoryQuoteItemContainerView(mMultiContext, contentMView);
            itemView.init();
            itemView.setBomExpandPicker(mBomExpandPicker);
            itemView.updateView(wrapper.arg);
            containerView.addView(itemView.getView());
        }
        containerView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (wrapper.arg == null) {
                    return;
                }
                String apiName = wrapper.arg.objectDescribe == null ? null : wrapper.arg.objectDescribe.getApiName();
                String dataId = wrapper.arg.objectData == null ? null : wrapper.arg.objectData.getID();
                if (TextUtils.isEmpty(apiName) || TextUtils.isEmpty(dataId)) {
                    return;
                }
                Context context = v.getContext();
                context.startActivity(
                        MetaDataConfig.getOptions().getNavigator().getDetailIntent(context, apiName, dataId));
            }
        });
    }

    public void setClickListener(OnConfirmClickListener listener) {
        this.mListener = listener;
    }

    static class Holder {
        TextView mHistoryQuoteText;
        ViewGroup mDataContainer;
        TextView mNoDataText;
        TextView mConfirmText;
    }

    /**
     * 历史报价ItemContentView
     */
    static class HistoryQuoteItemContentView extends ListItemContentMView<ListItemArg> {

        HistoryQuoteItemContentView(@NonNull MultiContext multiContext) {
            super(multiContext);
        }

        @Override
        protected void beforeUpdate(ListItemArg listItemArg) {
            super.beforeUpdate(listItemArg);
            MDOrderProductUtils.updateNameFieldLabel(listItemArg);
        }

        @Override
        protected boolean showRightArrowView() {
            return true;
        }

        @Override
        public ListContentAdapter<ListItemArg> getContentAdapter() {
            return new ListContentAdapter<ListItemArg>() {

                @Override
                public ListItemFieldArg getLeftTitleFieldArg(ListItemArg listItemArg,
                                                             Context context) {
                    return new SkuListContentAdapter().getLeftTitleFieldArg(listItemArg,context);
                }

                @NonNull
                @Override
                public ModelViewController<ListItemFieldArg, Void> createLeftFieldMViewController() {
                    return new ListFormFieldMViewCtrl() {
                        @NonNull
                        @Override
                        protected Collection<BaseModelViewPresenter<ListItemFieldArg, Void>> priorityPresenters() {
                            Collection<BaseModelViewPresenter<ListItemFieldArg, Void>> l = super.priorityPresenters();
                            l.add(new QuantityFieldMViewPresenter());
                            return l;
                        }
                    };
                }
            };
        }
    }

    public interface OnConfirmClickListener {

        void onClickConfirm(ListItemArgWrapper wrapper);
    }
}
