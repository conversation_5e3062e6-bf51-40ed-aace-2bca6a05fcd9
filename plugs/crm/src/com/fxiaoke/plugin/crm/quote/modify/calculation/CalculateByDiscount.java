/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.quote.modify.calculation;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import com.facishare.fs.metadata.beans.MetaDataParser;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.ObjectDescribe;
import com.fxiaoke.plugin.crm.order.utils.MDOrderProductUtils;

import android.text.TextUtils;
import androidx.annotation.NonNull;

/**
 * 通过折扣试算
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2019/7/30
 */
public class CalculateByDiscount extends AbsTrialCalculate {

    public CalculateByDiscount(@NonNull ITrialCalculateContext calculateContext) {
        super(calculateContext);
    }

    @Override
    public void trialCalculation(String input) {
        if (TextUtils.isEmpty(input)) {
            return;
        }
        input = input.replace("%", "");
        if (TextUtils.isEmpty(input)) {
            return;
        }
        List<ObjectData> dataList = mCalculateContext.getAllDataList();
        if (dataList == null || dataList.isEmpty()) {
            return;
        }
        double discount = MetaDataParser.parseDouble(input, 0);//输入的折扣
        ObjectDescribe describe = mCalculateContext.getObjectDescribe();
        int extraDiscountDecimalPlaces = MDOrderProductUtils.getPercentileFieldDecimalPlaces("extra_discount", describe);
        BigDecimal bd = new BigDecimal(discount).setScale(extraDiscountDecimalPlaces, RoundingMode.HALF_UP);
        double extraDiscount = bd.doubleValue();//额外折扣

        for (ObjectData objectData : dataList) {
            if (objectData == null) {
                continue;
            }
            //子产品明细不支持填入额外折扣
            objectData.put(EXTRA_DISCOUNT, extraDiscount);
        }
        mCalculateContext
                .onTrialCalculateComplete(discount, TrialCalculateType.BY_DISCOUNT, dataList);
    }
}
