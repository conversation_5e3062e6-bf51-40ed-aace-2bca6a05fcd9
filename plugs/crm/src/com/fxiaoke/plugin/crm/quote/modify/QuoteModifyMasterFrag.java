/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.quote.modify;

import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.modify.modelviews.field.LookUpMView;
import com.facishare.fs.pluginapi.crm.type.ICrmBizApiName;
import com.fxiaoke.plugin.crm.onsale.modify.OnSaleObjectModifyMasterFrag;
import com.fxiaoke.plugin.crm.quote.util.QuoteConstant;
import com.fxiaoke.plugin.crm.quote.util.QuoteUtils;

import android.os.Bundle;

/**
 * 报价单新建编辑页MasterFragment
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2018/1/22
 */
public class QuoteModifyMasterFrag
        extends OnSaleObjectModifyMasterFrag<QuoteModifyContract.Presenter>
        implements QuoteModifyContract.MasterView {

    private LookUpMView mOpportunityModel;

    public static QuoteModifyMasterFrag newInstance(ModifyMasterFragArg arg) {
        QuoteModifyMasterFrag frag = new QuoteModifyMasterFrag();
        Bundle bundle = new Bundle();
        bundle.putSerializable(KEY_FRAG_ARG, arg);
        frag.setArguments(bundle);
        return frag;
    }

    @Override
    public void updateModelViews() {
        super.updateModelViews();
        mOpportunityModel = QuoteUtils.getFieldModel(mAddOrEditMViewGroup,
                QuoteConstant.OPPORTUNITY_ID, LookUpMView.class);
    }

    @Override
    protected void clearPriceBookAndDetailDataWhenChangeCustomer(ObjectData customerData) {
        super.clearPriceBookAndDetailDataWhenChangeCustomer(customerData);
        updateLookUpMView(null, mOpportunityModel);
    }

    @Override
    protected String getDetailObjectApiName() {
        return ICrmBizApiName.QUOTE_LINES_API_NAME;
    }
}
