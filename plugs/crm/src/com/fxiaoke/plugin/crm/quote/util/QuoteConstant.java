/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.quote.util;

/**
 * 报价单相关常量
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2018/1/22
 */
public class QuoteConstant {
    //报价单新建编辑页相关字段常量
    public static final String QUOTE_ID = "quote_id";//报价单编码
    public static final String OPPORTUNITY_ID = "opportunity_id";//商机
    public static final String PRICE_BOOK = "price_book_id";//价目表
    public static final String PARTNER_ID = "partner_id";//合作伙伴
    public static final String OPTIONS = "options";

    /**
     * 报价单列表页状态
     */
    public static class LifeStatus {
        public static final String NORMAL = "normal";//正常
        public static final String INEFFECTIVE = "ineffective";//未生效
        public static final String UNDER_REVIEW = "under_review";//审核中
        public static final String IN_CHANGE = "in_change";//变更中
        public static final String INVALID = "invalid";//作废
    }

    //报价单明细新建编辑页相关字段常量
    public static final String MD_QUOTE = "quote_id";//报价单
    public static final String QUOTE_LINES_PRODUCT = "product_id";//产品名称
    public static final String QUOTE_LINES_PRODUCT_R = "product_id__r";//产品名称
    public static final String QUOTE_LINES_PRODUCT_RO = "product_id__ro";//产品
    public static final String QUOTE_LINES_PB_PRODUCT = "price_book_product_id";//价目表产品
    public static final String QUOTE_LINES_SPECS = "quote_lines_specs";//规格
    public static final String QUOTE_LINES_UNIT = "quote_lines_unit";//单位
    public static final String DISCOUNT = "discount";//折扣
    public static final String SALES_PRICE = "sales_price";//报价
    public static final String PRICE = "price";//价格
    public static final String TOTAL_AMOUNT = "total_amount";//小计
    public static final String QUANTITY = "quantity";//数量
    public static final String PRICE_BOOK_ID = "price_book_id";//价目表id

    //从价目表明细中取产品相关信息
    public static final String KEY_PRODUCT_NAME = "product_id__r";//产品名称
    public static final String KEY_PRODUCT_ID = "product_id";//产品id
    public static final String KEY_PRODUCT_SPEC = "specification_value_set";//价目表产品规格
    public static final String KEY_PRODUCT_PRICE = "pricebook_price";//价目表产品价格

    //是否是特殊添加的字段，如果是特殊添加的字段，渲染完后需要隐藏
    public static final String FIELD_IS_SPECIAL_FIELD_ADDED = "field_is_special_field_added";

}
