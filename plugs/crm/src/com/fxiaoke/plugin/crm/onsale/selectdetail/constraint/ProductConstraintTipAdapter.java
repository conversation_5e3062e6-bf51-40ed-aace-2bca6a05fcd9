/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.onsale.selectdetail.constraint;

import com.facishare.fs.i18n.I18NHelper;
import com.fxiaoke.cmviews.BaseListAdapter;
import com.fxiaoke.plugin.crm.R;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import androidx.annotation.NonNull;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

/**
 * 类名
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2020/9/9
 */
public class ProductConstraintTipAdapter
        extends BaseListAdapter<ProductConstraintTipBean, ProductConstraintTipAdapter.Holder> {

    private LayoutInflater mInflater;

    public ProductConstraintTipAdapter(@NonNull Context context) {
        super(context);
        mInflater = LayoutInflater.from(context);
    }

    @Override
    protected View createConvertView(Context context, int position, ProductConstraintTipBean bean) {
        return mInflater.inflate(R.layout.layout_product_constraint_tip_item_view, null);
    }

    @Override
    protected void updateView(Holder holder, int position, ProductConstraintTipBean bean) {
        int flag = Spanned.SPAN_INCLUSIVE_EXCLUSIVE;
        SpannableStringBuilder leftText = new SpannableStringBuilder();
        int length = leftText.length();
        leftText.append(
                I18NHelper.getText("crm.ProductConstraintTipAdapter.constraint_title_tip")/*您选择产品*/)
                .append(" ")
                .setSpan(new ForegroundColorSpan(Color.parseColor("#545861")), 0, length, flag);
        SpannableStringBuilder productText = new SpannableStringBuilder();
        productText.append(bean.getProductName())
                .setSpan(new ForegroundColorSpan(Color.parseColor("#181C25")), 0,
                        productText.length(), flag);
        productText.setSpan(new StyleSpan(Typeface.BOLD), 0, productText.length(), flag);
        SpannableStringBuilder text =
                new SpannableStringBuilder().append(leftText).append(productText);
        holder.mSelectedProductName.setText(text);
        holder.mRequiredView.updateConstraints(true, bean.getRequiredList());
        holder.mNotRequiredView.updateConstraints(false, bean.getNotRequiredList());
    }

    @Override
    protected Holder createHolder(View convertView, int position, ProductConstraintTipBean bean) {
        Holder holder = new Holder();
        holder.mSelectedProductName = convertView.findViewById(R.id.tv_selected_product_name);
        holder.mRequiredView = convertView.findViewById(R.id.required_view);
        holder.mNotRequiredView = convertView.findViewById(R.id.not_required_view);
        return holder;
    }

    static class Holder {
        private TextView mSelectedProductName;
        private ProductConstraintView mRequiredView;
        private ProductConstraintView mNotRequiredView;
    }
}
