package com.fxiaoke.plugin.crm.onsale.promotion.bean;

import com.facishare.fs.i18n.I18NHelper;

/**
 * 促销类型
 */
public enum PromotionType {
    //打折
    Discount(1, I18NHelper.getText("crm.PromotionType.discount")/* 打折 */),
    //减免
    Relief(2, I18NHelper.getText("crm.PromotionType.reduction")/* 减免 */),
    //一口价
    OnePrice(3, I18NHelper.getText("crm.PromotionType.one_price")/* 一口价 */),
    //买赠
    BuyGifts(4, I18NHelper.getText("crm.PromotionType.present")/* 买赠 */),
    //满折
    FullFold(11, I18NHelper.getText("crm.PromotionType.full_fold")/* 满折 */),
    //满减
    FullReduction(12, I18NHelper.getText("crm.PromotionType.full_reduction")/* 满减 */),
    //满赠
    FullGift(13, I18NHelper.getText("crm.PromotionType.full_gift")/* 满赠 */),
    //打折(组合)
    DiscountCombination(21, I18NHelper.getText("crm.PromotionType.discount_combination")/* 打折(组合)*/),
    //减免(组合)
    ReliefCombination(22, I18NHelper.getText("crm.PromotionType.reduction_combination")/* 减免(组合) */),
    //一口价(组合)
    PriceCombination(23, I18NHelper.getText("crm.PromotionType.one_price_combination")/* 一口价(组合) */),
    //买赠(组合)
    BuyGiftCombination(24, I18NHelper.getText("crm.PromotionType.buy_gift_combination")/* 买赠(组合) */);

    public final int key;
    public final String desc;

    PromotionType(int key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static PromotionType getTypeByKey(int key) {
        for (PromotionType type : values()) {
            if (type.key == key) {
                return type;
            }
        }
        return null;
    }

}
