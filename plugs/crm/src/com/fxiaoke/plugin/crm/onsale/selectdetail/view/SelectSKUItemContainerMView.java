/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.onsale.selectdetail.view;

import com.facishare.fs.metadata.list.beans.ListItemArg;
import com.facishare.fs.metadata.list.modelviews.ListItemContainerMView;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.selectsku.SKUConstant;

import android.content.Context;
import androidx.annotation.NonNull;
import android.view.View;

/**
 * 类名
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2020/9/3
 */
public class SelectSKUItemContainerMView extends ListItemContainerMView<ListItemArg> {

    private View mGrayDivider;
    private SelectSKUItemContentMView mSKUItemView;

    public SelectSKUItemContainerMView(@NonNull MultiContext multiContext,
                                       SelectSKUItemContentMView updateView) {
        super(multiContext, updateView);
        this.mSKUItemView = updateView;
    }

    @Override
    protected View onCreateView(Context context) {
        View root = super.onCreateView(context);
        mGrayDivider = root.findViewById(R.id.gray_divider);
        return root;
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.layout_select_sku_list_item_container;
    }

    @Override
    public void updateView(ListItemArg listItemArg) {
        super.updateView(listItemArg);
        boolean showGrayDivider =
                listItemArg.objectData.getBoolean(SKUConstant.KEY_SHOW_GRAY_DIVIDER);
        mGrayDivider.setVisibility(showGrayDivider ? View.VISIBLE : View.GONE);
        mDivider.setVisibility(showGrayDivider ? View.GONE : View.VISIBLE);
    }
}
