/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.onsale.promotion.pop.promotionlist;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.beans.ObjectData;
import com.fxiaoke.plugin.crm.R;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.Nullable;

/**
 * 促销赠品View
 */
public class PromotionGiftView extends LinearLayout {

    private TextView mGiftName;
    private TextView mGiftNum;

    public PromotionGiftView(Context context) {
        this(context, null);
    }

    public PromotionGiftView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PromotionGiftView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        View view = LayoutInflater.from(context)
                .inflate(R.layout.layout_promotion_gift_info, this, true);
        mGiftName = view.findViewById(R.id.tv_gift_name);
        mGiftNum = view.findViewById(R.id.tv_gift_num);
    }

    public void updateGiftInfo(ObjectData promotionGift, boolean isPromotionGift) {
        if (isPromotionGift) {
            double giftProductNum = promotionGift.getDouble("gift_product_num");
            String giftProductName = promotionGift.getString("gift_product_id__r", "");
            int giftType = promotionGift.getInt("gift_type");
            mGiftName.setText((giftType == 2) ? I18NHelper.getText("crm.PromotionGiftView.product_self")/* 本品 */ : giftProductName);
            mGiftNum.setText("x " + giftProductNum);
        } else {
            double quantity = promotionGift.getDouble("quantity");
            String productName = promotionGift.getString("product_id__r");
            mGiftName.setText(productName);
            mGiftNum.setText("x " + quantity);
        }
    }
}
