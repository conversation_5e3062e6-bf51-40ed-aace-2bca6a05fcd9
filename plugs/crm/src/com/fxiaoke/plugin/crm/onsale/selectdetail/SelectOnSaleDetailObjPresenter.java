/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.onsale.selectdetail;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.jetbrains.annotations.NotNull;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.billy.cc.core.component.CC;
import com.facishare.fs.common_utils.JsonHelper;
import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.BaseActivity;
import com.facishare.fs.metadata.ILoadingView;
import com.facishare.fs.metadata.beans.GetDataListResult;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.RefObjEachResult;
import com.facishare.fs.metadata.data.cache.selectCrmObj.SelectCrmObjectListCacheHelper;
import com.facishare.fs.metadata.list.select_obj.IOffLineListener;
import com.facishare.fs.metadata.list.select_obj.MetaDataSelectObjUtil;
import com.facishare.fs.metadata.list.select_obj.picker.PickMode;
import com.facishare.fs.metadata.list.select_obj.picker.PickObjConfig;
import com.facishare.fs.metadata.list.select_obj.presenter.MetaDataSelectObjPresenter;
import com.facishare.fs.pluginapi.ICcComponentNames;
import com.facishare.fs.pluginapi.crm.ICcCRMActions;
import com.facishare.fs.pluginapi.crm.beans.CustomerObjectData;
import com.facishare.fs.pluginapi.crm.type.ICrmBizApiName;
import com.fxiaoke.fscommon.http.WebApiExecutionCallbackWrapper;
import com.fxiaoke.fscommon.sandbox.SandboxUtils;
import com.fxiaoke.fscommon_res.view.IconButton;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.lib.bean.GetAllProductCategoryResult;
import com.fxiaoke.plugin.crm.lib.bean.ProductEnumDetailInfo;
import com.fxiaoke.plugin.crm.onsale.selectdetail.classify.ClassifyWrapper;
import com.fxiaoke.plugin.crm.order.action.SelectEntrance;
import com.fxiaoke.plugin.crm.order.beans.AvailablePriceBookResult;
import com.fxiaoke.plugin.crm.order.selectproduct.config.PickProductConfig;
import com.fxiaoke.plugin.crm.order.selectproduct.view.SelectPriceBookPop;
import com.fxiaoke.plugin.crm.order.utils.MDOrderProductService;
import com.fxiaoke.plugin.crm.product.api.ProductService;
import com.fxiaoke.plugin.crm.selectsku.SKUConstant;

import android.text.TextUtils;
import android.view.View;
import io.reactivex.Single;
import io.reactivex.SingleEmitter;
import io.reactivex.SingleObserver;
import io.reactivex.SingleOnSubscribe;
import io.reactivex.SingleSource;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;

/**
 * 选择售中对象的从对象的Presenter
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2020/6/2
 */
public abstract class SelectOnSaleDetailObjPresenter<V extends SelectOnSaleDetailObjContract.View>
        extends MetaDataSelectObjPresenter
        implements SelectOnSaleDetailObjContract.Presenter {

    private PickProductConfig mPickProductConfig;
    protected V mSelectObjView;

    //选择价目表
    private IconButton mSelectPriceBookButton;
    private SelectPriceBookPop mSelectPriceBookPop;
    private String mSelectedPriceBookId;//已选的价目表id
    //分类
    private View mClassifyButton;

    protected SelectEntrance mEntrance;
    private boolean mOffLine;
    public SelectOnSaleDetailObjPresenter(BaseActivity activity, PickObjConfig config,
                                          PickProductConfig pickProductConfig,
                                          V view) {
        super(activity, config, view);
        this.mPickProductConfig = pickProductConfig;
        this.mSelectObjView = view;
        this.mEntrance = pickProductConfig.getSelectEntrance();
        if (isEnablePriceBookBtn()) {
            initSelectPriceBookPop();
        }
    }

    @Override
    public void start() {
        super.start();
        requestClassifyData();
    }

    @Override
    protected void addOperationActions() {
        if (isEnablePriceBookBtn()) {
            addSelectPriceBookButton();
        }
        addClassifyButton();
        super.addOperationActions();
    }

    private void addClassifyButton() {
        boolean expand = ClassifyWrapper.getClassifyMode();
        int resId = expand ? R.drawable.product_classify_check : R.drawable.product_classify_uncheck;
        mClassifyButton = mHorizontalListActionBar.addLeftView(
                I18NHelper.getText("xt.project_new_fast_task.text.classify")/* 分类 */,
                COLOR_NORMAL, resId, new View.OnClickListener() {
                    @Override
                    public void onClick(final View view) {
                        boolean show = mSelectObjView.onClickClassifyBtn();
                        ClassifyWrapper.saveClassifyMode(show);
                        mHorizontalListActionBar.updateLeftView(mClassifyButton, COLOR_NORMAL, show
                                ? R.drawable.product_classify_check
                                : R.drawable.product_classify_uncheck);
                    }
                });
    }

    /**
     * 请求分类数据
     */
    private void requestClassifyData() {
        Single<GetAllProductCategoryResult> single = getAllProductCategoryResultSingle();
        //判断是否使用离线缓存数据数据
        if (mActivity instanceof IOffLineListener && ((IOffLineListener) mActivity)
                .isUseCacheDataList()) {
            //判断是否数据缓存
            boolean hasDataCache =
                    SelectCrmObjectListCacheHelper.hasProductCategoryCache();
            if (hasDataCache) {
                single = single.timeout(SelectCrmObjectListCacheHelper.getReadTimeOut(),
                        TimeUnit.MILLISECONDS)
                        .onErrorResumeNext(
                                new Function<Throwable, SingleSource<?
                                        extends GetAllProductCategoryResult>>() {
                                    @Override
                                    public SingleSource<? extends GetAllProductCategoryResult> apply(
                                            Throwable throwable) throws Exception {
                                        mOffLine = true;
                                        return getProductCategoryResultCache();
                                    }
                                })
                        .observeOn(AndroidSchedulers.mainThread())
                        .doOnSuccess(new Consumer<GetAllProductCategoryResult>() {
                            @Override
                            public void accept(GetAllProductCategoryResult result)
                                    throws Exception {
                                if (mActivity instanceof IOffLineListener) {
                                    ((IOffLineListener) mActivity).setOffLine(mOffLine);
                                }
                            }
                        });
            }
        }
        single.observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(Schedulers.io())
                .subscribe(new SingleObserver<GetAllProductCategoryResult>() {
                    @Override
                    public void onSubscribe(@NotNull Disposable d) {
                        ILoadingView.ContextImplProxy.showLoading(mActivity);
                    }

                    @Override
                    public void onSuccess(@NotNull GetAllProductCategoryResult result) {
                        ILoadingView.ContextImplProxy.dismissLoading(mActivity);
                        List<ProductEnumDetailInfo> classifyList =
                                result == null ? null : result.productCategoryInfo;
                        mSelectObjView.updateClassifyData(classifyList);
                    }

                    @Override
                    public void onError(@NotNull Throwable e) {
                        ILoadingView.ContextImplProxy.dismissLoading(mActivity);
                        mSelectObjView.updateClassifyData(null);
                    }
                });

    }

    private Single<GetAllProductCategoryResult> getProductCategoryResultCache() {
        return SelectCrmObjectListCacheHelper
                .getProductCategoryResultCache(ICrmBizApiName.PRODUCT_API_NAME,
                        SelectCrmObjectListCacheHelper.SP_PRODUCT_CATEGORY_OFFLINE_CACHE).map(
                        new Function<Object,
                                GetAllProductCategoryResult>() {
                            @Override
                            public GetAllProductCategoryResult apply(
                                    @NotNull Object result)
                                    throws Exception {
                                GetAllProductCategoryResult categoryResult =
                                        null;
                                try {
                                    String json = JsonHelper.toJsonString(result);
                                    categoryResult =
                                            JSONObject.parseObject(json,
                                                    GetAllProductCategoryResult.class);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                return categoryResult != null ?
                                        categoryResult :
                                        new GetAllProductCategoryResult();
                            }
                        });
    }

    private Single<GetAllProductCategoryResult> getAllProductCategoryResultSingle() {
        return Single.create(new SingleOnSubscribe<GetAllProductCategoryResult>() {

            @Override
            public void subscribe(
                    @NotNull SingleEmitter<GetAllProductCategoryResult> emitter)
                    throws Exception {
                ProductService
                        .GetAllProductCategory(
                                new WebApiExecutionCallbackWrapper<GetAllProductCategoryResult>(
                                        GetAllProductCategoryResult.class,
                                        SandboxUtils.getActivityByContext(mActivity)) {
                                    @Override
                                    public void failed(String error) {
                                        emitter.onError(new Throwable(error));
                                    }

                                    @Override
                                    public void succeed(
                                            GetAllProductCategoryResult response) {
                                        emitter.onSuccess(response);
                                    }
                                });
            }
        });
    }

    /**
     * 添加选择价目表按钮
     */
    private void addSelectPriceBookButton() {
        String title;
        String priceBookName = mConfig.getMasterObjectData().getString("price_book_id__r");
        if (!TextUtils.isEmpty(priceBookName)) {
            title = priceBookName;
        } else {
            title = I18NHelper.getText("crm.order.SelectPriceBookPop.title_text")/* 请选择价目表 */;
        }
        mSelectPriceBookButton = mHorizontalListActionBar.addLeftView(title, COLOR_NORMAL,
                com.facishare.fs.metadata.R.drawable.titlebar_down_black,
                new View.OnClickListener() {
                    @Override
                    public void onClick(final View view) {
                        mSelectPriceBookPop.show();
                    }
                });
        mSelectPriceBookButton.setPadding(50, 0, 50, 0);
    }

    private void initSelectPriceBookPop() {
        mSelectPriceBookPop = new SelectPriceBookPop(mActivity,
                new SelectPriceBookPop.ISelectPriceBookProcessor() {
                    @Override
                    public void onPriceBookSelected(ObjectData selectedPriceBook) {
                        mSelectedPriceBookId = selectedPriceBook.getID();
                        updateSelectPriceBookPop();
                        SelectOnSaleDetailObjPresenter.this
                                .onPriceBookSelected(selectedPriceBook, true);
                    }

                    @Override
                    public int getPriceBookProductCount(ObjectData priceBook) {
                        return mSelectObjView.getPriceBookProductCount(priceBook);
                    }

                    @Override
                    public boolean isPicked(ObjectData priceBook) {
                        return TextUtils.equals(mSelectedPriceBookId, priceBook.getID());
                    }

                    @Override
                    public void onShow() {
                        updateSelectPriceBookButton(null, true);
                    }

                    @Override
                    public void onDismiss() {
                        updateSelectPriceBookButton(null, false);
                    }
                });
    }

    @Override
    protected Single getTemplateSource() {
        if (isEnablePriceBookBtn()) {
            return getAvailablePriceBookList()
                    .flatMap(new Function<AvailablePriceBookResult, SingleSource<Object>>() {
                        @Override
                        public SingleSource<Object> apply(AvailablePriceBookResult result)
                                throws Exception {
                            List<ObjectData> priceBooks = result == null ? null :
                                    result.getPriceBookList();
                            if (priceBooks == null || priceBooks.isEmpty()) {
                                return getEmptySingle();
                            }
                            return SelectOnSaleDetailObjPresenter.super.getTemplateSource();
                        }
                    });
        }
        return super.getTemplateSource();
    }

    private Single<Object> getEmptySingle() {
        return Single.create(new SingleOnSubscribe<Object>() {
            @Override
            public void subscribe(SingleEmitter<Object> emitter) throws Exception {
                emitter.onSuccess(new Object());
            }
        }).observeOn(AndroidSchedulers.mainThread())
                .doOnSuccess(new Consumer<Object>() {
                    @Override
                    public void accept(Object o) throws Exception {
                        baseView.dismissLoading();
                        baseView.updateTitle(mConfig.getTitle(), mConfig.getApiName());
                        mSelectObjView.updateNoAvailablePriceBookView();
                    }
                });
    }

    /**
     * 获取当前客户适用的价目表列表
     */
    private Single<AvailablePriceBookResult> getAvailablePriceBookList() {
        return Single.create(new SingleOnSubscribe<AvailablePriceBookResult>() {
            @Override
            public void subscribe(final SingleEmitter<AvailablePriceBookResult> e)
                    throws Exception {
                ObjectData masterData = mConfig.getMasterObjectData();
                String customerId = null;
                String partnerId = null;
                if (masterData != null) {
                    customerId = masterData.getString("account_id");
                    partnerId = masterData.getString("partner_id");
                }
                boolean isCreateScene =
                        mPickProductConfig != null && mPickProductConfig.isCreateScene();
                MDOrderProductService
                        .getAvailablePriceBookList(customerId, partnerId, !isCreateScene, masterData,
                                new WebApiExecutionCallbackWrapper<AvailablePriceBookResult>
                                        (AvailablePriceBookResult.class, mActivity) {
                                    @Override
                                    public void succeed(AvailablePriceBookResult result) {
                                        if (result == null) {
                                            this.failed("result is null");
                                        } else {
                                            if (!e.isDisposed()) {
                                                e.onSuccess(result);
                                            }
                                        }
                                    }

                                    @Override
                                    public void failed(String error) {
                                        super.failed(error);
                                        ToastUtils.show(error);
                                        if (!e.isDisposed()) {
                                            e.onError(new Throwable(error));
                                        }
                                    }
                                });
            }
        }).observeOn(AndroidSchedulers.mainThread())
                .doOnSuccess(new Consumer<AvailablePriceBookResult>() {
                    @Override
                    public void accept(AvailablePriceBookResult result) throws Exception {
                        List<ObjectData> priceBookList =
                                result == null ? null : result.getPriceBookList();
                        mSelectPriceBookPop.updatePriceBookList(priceBookList);
                        if (priceBookList == null || priceBookList.isEmpty()) {
                            return;
                        }
                        String priceBookId =
                                mConfig.getMasterObjectData().getString("price_book_id");
                        //如果没有价目表id，则默认取第一条价目表数据
                        if (TextUtils.isEmpty(priceBookId)) {
                            ObjectData firstData = priceBookList.get(0);
                            priceBookId = firstData == null ? null : firstData.getID();
                            onPriceBookSelected(firstData, false);
                        }
                        mSelectedPriceBookId = priceBookId;
                        updateSelectPriceBookPop();
                    }
                });
    }

    /**
     * 当选择了价目表回调
     *
     * @param selectedPriceBook 选择的价目表
     * @param refresh           是否刷新页面
     */
    private void onPriceBookSelected(ObjectData selectedPriceBook, boolean refresh) {
        String name = selectedPriceBook == null ?
                I18NHelper.getText("crm.order.SelectPriceBookPop.title_text")/* 请选择价目表 */
                : selectedPriceBook.getName();
        updateSelectPriceBookButton(name, false);
        mSelectObjView.onPriceBookSelected(selectedPriceBook, refresh);
    }

    @Override
    public void updateSelectPriceBookPop() {
        if (mSelectPriceBookPop != null) {
            mSelectPriceBookPop.refresh();
        }
    }

    @Override
    public void checkProductConstraint(List<ObjectData> selectedProducts,
                                       com.facishare.fs.common_utils.function.Consumer<Boolean> consumer) {
        //未开启cpq、不支持配置、单选模式下，不需要调用校验接口
        boolean isOpenCpq = mPickProductConfig != null && mPickProductConfig.isOpenCpq();
        boolean supportConfigSubProducts =
                mPickProductConfig != null && mPickProductConfig.supportConfigSubProducts();
        if (!isOpenCpq || !supportConfigSubProducts || PickMode.SINGLE == mConfig.getMode()) {
            consumer.accept(true);
            return;
        }
        List<String> skuIds = toSKUIds(selectedProducts);
        if (skuIds == null || skuIds.isEmpty()) {
            consumer.accept(true);
            return;
        }
        List<String> selectedSKUIds =
                mPickProductConfig == null ? null : mPickProductConfig.getSelectedSKUIds();
        if (selectedSKUIds != null && !selectedSKUIds.isEmpty()) {
            skuIds.addAll(selectedSKUIds);
        }
        ILoadingView.ContextImplProxy.showLoading(mActivity);
        MDOrderProductService.checkProductConstraint(skuIds,
                new WebApiExecutionCallbackWrapper<JSONObject>(JSONObject.class, mActivity) {
                    @Override
                    public void succeed(JSONObject response) {
                        ILoadingView.ContextImplProxy.dismissLoading(mActivity);
                        boolean success = true;
                        if (response != null && !response.isEmpty()) {
                            JSONObject errorMsg = response.getJSONObject("errorMessage");
                            success = errorMsg == null || errorMsg.isEmpty();
                        }
                        consumer.accept(success);
                        if (!success) {//有依赖互斥条件，弹窗提示
                            CC.obtainBuilder(ICcComponentNames.KEY_CC_CRM)
                                    .setActionName(ICcCRMActions.Action_ShowConstraintAlert)
                                    .setContext(mActivity)
                                    .addParam("showConstraintAlert", response)
                                    .build()
                                    .call();
                        }
                    }

                    @Override
                    public void failed(String error) {
                        ILoadingView.ContextImplProxy.dismissLoading(mActivity);
                        ToastUtils.show(error);
                        consumer.accept(false);
                    }
                });
    }

    private void updateSelectPriceBookButton(String title, boolean up) {
        int drawableResId = up ? com.facishare.fs.metadata.R.drawable.titlebar_up_black
                : com.facishare.fs.metadata.R.drawable.titlebar_down_black;
        if (TextUtils.isEmpty(title)) {
            mHorizontalListActionBar.updateLeftView(mSelectPriceBookButton, COLOR_NORMAL,
                    drawableResId);
        } else {
            mHorizontalListActionBar.updateLeftView(mSelectPriceBookButton, title, COLOR_NORMAL,
                    drawableResId);
        }
    }

    /**
     * 是否添加选择价目表操作
     */
    protected boolean isEnablePriceBookBtn() {
        boolean supportChangePriceBook =
                mPickProductConfig != null && mPickProductConfig.supportChangePriceBook();
        if (!supportChangePriceBook) {
            return false;
        }
        if (SKUConstant.IS_PRICE_BOOK_OPT && mEntrance == SelectEntrance.PriceBookProduct) {
            ObjectData masterData = mConfig.getMasterObjectData();
            String priceBookId = masterData == null ? null : masterData.getString("price_book_id");
            return TextUtils.isEmpty(priceBookId);
        }
        return false;
    }

    /**
     * 从给定的sku列表数据中获取产品包id
     */
    private List<String> toSKUIds(List<ObjectData> skuList) {
        if (skuList == null || skuList.isEmpty()) {
            return null;
        }
        List<String> productPkgIds = new ArrayList<>();
        for (ObjectData sku : skuList) {
            if (sku == null) {
                continue;
            }
            String key = mEntrance == SelectEntrance.Product ? "_id" : "product_id";
            String productId = sku.getString(key);
            if (!TextUtils.isEmpty(productId)) {
                productPkgIds.add(productId);
            }
        }
        return productPkgIds;
    }
}
