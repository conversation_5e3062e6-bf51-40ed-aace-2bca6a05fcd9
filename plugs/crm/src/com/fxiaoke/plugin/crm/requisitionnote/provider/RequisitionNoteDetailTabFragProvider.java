/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.requisitionnote.provider;

import com.facishare.fs.metadata.beans.components.GroupComponent;
import com.facishare.fs.metadata.detail.fragment.DetailTabFragArg;
import com.facishare.fs.metadata.detail.fragment.DetailTabFragProvider;
import com.fxiaoke.plugin.crm.requisitionnote.fragment.RequisitionNoteDetailMDTabFrag;

import androidx.fragment.app.Fragment;
import android.text.TextUtils;

/**
 * Created by wubb on 2018/5/15.
 */

public class RequisitionNoteDetailTabFragProvider extends DetailTabFragProvider {
    private final String RequisitionNoteProductObjGroupComApiName = "RequisitionNoteProductObj_md_group_component";
    private final int RequisitionNoteDetailMDTabFragType = getCustomFragType(1);

    @Override
    protected int getFragType(GroupComponent component) {
        if (TextUtils.equals(component.getApiName(), RequisitionNoteProductObjGroupComApiName)) {
            return RequisitionNoteDetailMDTabFragType;
        }
        return super.getFragType(component);
    }

    @Override
    protected Fragment createTabFragment(int fragType, DetailTabFragArg fragArg) {
        if (RequisitionNoteDetailMDTabFragType == fragType) {
            return RequisitionNoteDetailMDTabFrag.newInstance(fragArg);
        }
        return super.createTabFragment(fragType, fragArg);
    }
}
