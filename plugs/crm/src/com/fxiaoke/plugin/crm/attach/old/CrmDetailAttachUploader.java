package com.fxiaoke.plugin.crm.attach.old;

import com.facishare.fs.i18n.I18NHelper;
import android.content.Context;
import android.os.Handler;
import android.util.SparseArray;

import com.facishare.fs.pluginapi.fileserver.IFileServer;
import com.facishare.fs.pluginapi.fileserver.IFileUploader;
import com.facishare.fs.pluginapi.fileserver.upload.FileUploadAbstractVo;
import com.facishare.fs.pluginapi.fileserver.upload.FileUploadProgressCallback;
import com.facishare.fs.pluginapi.fileserver.upload.FileUploadStateCallback;
import com.facishare.fs.pluginapi.fileserver.upload.FileUploadStates;
import com.facishare.fs.pluginapi.fileserver.upload.FileUploadTaskInfo;
import com.facishare.fs.pluginapi.fileserver.upload.IFileUploaderBusinessCallback;
import com.fs.fsprobuf.ServerProtobuf;
import com.fxiaoke.fscommon.sandbox.SandboxContextManager;
import com.fxiaoke.fscommon.sandbox.SandboxUtils;
import com.fxiaoke.fshttp.web.sandbox.ISandboxContext;
import com.fxiaoke.fxlog.module.CrmLog;
import com.fxiaoke.plugin.crm.attach.beans.CrmDetailUploadVo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * Created by xudd on 2015/9/16.
 */
public class CrmDetailAttachUploader implements IFileUploader {

    public static final String LOADER_ID = "CRM_Attach";

    public static final String TAG = "CrmDetailAttachUploader :: ";

    private String mLoaderId;

    private int mCurMaxId = 0;

    private int mFileLength;

    private Queue<Integer> mFileUploadTaskQueue = new LinkedBlockingQueue<Integer>();

    /**
     * key:任务ID，value:任务信息
     */
    private HashMap<String, SparseArray<FileUploadTaskInfo>> mTaskInfoMap = new LinkedHashMap<>();

    /**
     * 任务回调
     */
    private IFileUploaderBusinessCallback mFileUploaderBusinessCallback;

    /**
     * 进度回调
     */
    private List<FileUploadProgressCallback> mProgressCallbackList = new ArrayList<FileUploadProgressCallback>();

    /**
     * 状态回调的list
     */
    private List<FileUploadStateCallback> mStateCallbackList = new ArrayList<FileUploadStateCallback>();

    /**
     * CRM-防止重复回调
     */
    private SparseArray<Boolean> mPreventList = new SparseArray<Boolean>();

    private Handler mHandler = new Handler();

    private IFileServer mFileServer;

    private Context mContext;

    private ISandboxContext mSandboxContext;

    @Override
    public ISandboxContext getSandboxContext() {
        return mSandboxContext;
    }

    public CrmDetailAttachUploader(IFileServer fileServer) {
        mFileServer = fileServer;
    }

    public void destroy() {
        Iterator<SparseArray<FileUploadTaskInfo>> it = mTaskInfoMap.values().iterator();
        while(it.hasNext()){
            SparseArray<FileUploadTaskInfo> sparseArray = it.next();
            if(sparseArray != null && sparseArray.size() > 0){
                for(int index = 0; index < sparseArray.size(); index++){
                    FileUploadTaskInfo task = sparseArray.get(index);
                    task.state = FileUploadStates.CANCELED;
                    callStateCallback(task);
                }
                sparseArray.clear();
            }
        }

        mTaskInfoMap.clear();
        mPreventList.clear();
    }

    private synchronized int genNewId() {
        mCurMaxId++;
        return mCurMaxId;
    }

    @Override
    public void setLoaderId(String loaderId) {
        mLoaderId = loaderId;
    }

    @Override
    public String getLoaderId() {
        return mLoaderId;
    }

    @Override
    public int addTask(Context context, String name, String path) {
        return addTask(context, name, path, new CrmDetailUploadVo("",0),ServerProtobuf.EnterpriseEnv.INNER);
    }

    //add by wubb
    @Override
    public int addTask(Context context, String name, String path,ServerProtobuf.EnterpriseEnv enterpriseEnv){
        return addTask(context, name, path, new CrmDetailUploadVo("",0),enterpriseEnv);
    }

    @Override
    public int addTask(Context context, String name, String path, FileUploadAbstractVo fileUploadAbstractVo, long timeOut) {
        return 0;
    }

    //add by wubb
    @Override
    public int addTask(Context context, String name, String path, FileUploadAbstractVo vo,long timeOut,ServerProtobuf.EnterpriseEnv enterpriseEnv){
        return 0;
    }


    @Override
    public int addTask(Context context, String name, String path, FileUploadAbstractVo vo) {
        return addTask(context, name, path, vo,ServerProtobuf.EnterpriseEnv.INNER);
    }

    //add by wubb
    @Override
    public int addTask(Context context, String name, String path, FileUploadAbstractVo vo,ServerProtobuf.EnterpriseEnv enterpriseEnv){
        mSandboxContext = SandboxContextManager.getInstance().getContext(SandboxUtils.getActivityByContext(context));
        if (mContext == null) {
            mContext = context.getApplicationContext();
        }

        FileUploadTaskInfo task = new FileUploadTaskInfo();
        int id = genNewId();
        task.id = id;
        task.name = name;
        task.path = path;
        task.state = FileUploadStates.WAITING;
        task.vo = vo;
        task.enterpriseEnv = enterpriseEnv;
        String dataId = ((CrmDetailUploadVo)vo).mDataId;
        if(mTaskInfoMap.get(dataId) == null){
            SparseArray<FileUploadTaskInfo> sparseArray = new SparseArray<>();
            mTaskInfoMap.put(dataId, sparseArray);
        }
        mTaskInfoMap.get(dataId).put(task.id, task);

        retryTask(context, id);
        return id;
    }

    @Override
    public void cancelTask(int taskId) {
        CrmLog.d(TAG, TAG + "cancelTask:" + taskId);
        FileUploadTaskInfo task = getTask(taskId);
        if (task != null) {
            task.state = FileUploadStates.CANCELED;
            callStateCallback(task);
            removeTask(taskId);
        }
    }

    @Override
    public void retryTask(Context context, int taskId) {
        CrmLog.d(TAG, TAG + "retryTask,TaskId:" + taskId);

        FileUploadTaskInfo task = getTask(taskId);
        if(task != null){
            if (task.state == FileUploadStates.FAILED) {
                task.state = FileUploadStates.WAITING;
                callStateCallback(task);
            }

            mFileUploadTaskQueue.offer(task.id);
        }
        if (mFileUploadTaskQueue.size() == 1) {
            int nextTaskId = mFileUploadTaskQueue.element();
            if (nextTaskId != 0) {
                processLineTask(nextTaskId);
            }
        }
    }

    private FileUploadTaskInfo getTask(int taskId){
        Iterator<SparseArray<FileUploadTaskInfo>> it = mTaskInfoMap.values().iterator();
        while(it.hasNext()){
            SparseArray<FileUploadTaskInfo> sparseArray = it.next();
            if(sparseArray != null && sparseArray.size() > 0){
                FileUploadTaskInfo task = sparseArray.get(taskId, null);
                if(task != null){
                    return task;
                }
            }
        }
        return null;
    }

    private void removeTask(int taskId){
        Iterator<SparseArray<FileUploadTaskInfo>> it = mTaskInfoMap.values().iterator();
        while(it.hasNext()){
            SparseArray<FileUploadTaskInfo> sparseArray = it.next();
            if(sparseArray != null){
                sparseArray.remove(taskId);
            }
        }
    }

    private void executerNextTask() {
        if (mFileUploadTaskQueue.size() > 0) {
            mFileUploadTaskQueue.poll();

            if (mFileUploadTaskQueue.size() > 0) {
                int nextTaskId = mFileUploadTaskQueue.element();
                if (nextTaskId != 0) {
                    processLineTask(nextTaskId);
                }
            }
        }
    }

    private void processLineTask(int taskId) {
        FileUploadTaskInfo taskInfo = getTask(taskId);
        if (taskInfo == null) {
            executerNextTask();
        } else {
            String path = taskInfo.path;
            mFileServer.uploadTempFileASync(mContext, taskId, path, this,0,taskInfo.enterpriseEnv);
        }
    }

    @Override
    public List<FileUploadTaskInfo> getUploadTaskList() {
        List<FileUploadTaskInfo> list = new ArrayList<FileUploadTaskInfo>();
        Iterator<String> it = mTaskInfoMap.keySet().iterator();
        while(it.hasNext()) {
            String dataId = it.next();
            for (int index = 0; index < mTaskInfoMap.get(dataId).size(); index++) {
                FileUploadTaskInfo task = mTaskInfoMap.get(dataId).valueAt(index);
                list.add(task);
            }
        }
        return list;
    }

    public List<FileUploadTaskInfo> getUploadingTaskList(String dataId) {
        List<FileUploadTaskInfo> list = new ArrayList<FileUploadTaskInfo>();
        Iterator<String> it = mTaskInfoMap.keySet().iterator();
        while(it.hasNext()) {
            if(dataId.equals(it.next())){
                for (int index = 0; index < mTaskInfoMap.get(dataId).size(); index++) {
                    FileUploadTaskInfo task = mTaskInfoMap.get(dataId).valueAt(index);
                    list.add(task);
                }
            }
        }
        return list;
    }

    @Override
    public FileUploadTaskInfo getUploadTaskById(int taskId) {
        return getTask(taskId);
    }

    //MARK- IBusinessCallback
    @Override
    public void setBusinessCallback(IFileUploaderBusinessCallback callBack) {
        mFileUploaderBusinessCallback = callBack;
    }

    @Override
    public IFileUploaderBusinessCallback getBusinessCallback() {
        return mFileUploaderBusinessCallback;
    }

    // MARK- FileSaveProgressCallback
    @Override
    public void addProgressCallback(FileUploadProgressCallback callBack) {
        mProgressCallbackList.add(callBack);
    }

    @Override
    public void removeProgressCallback(FileUploadProgressCallback callBack) {
        mProgressCallbackList.remove(callBack);
    }

    @Override
    public List<FileUploadProgressCallback> getFileSaveProgressCallbackList() {
        List<FileUploadProgressCallback> callBackList = new ArrayList<FileUploadProgressCallback>();
        for (FileUploadProgressCallback callBack : mProgressCallbackList) {
            if (callBack != null) {
                callBackList.add(callBack);
            }
        }

        return callBackList;
    }

    // FileSaveStateCallback
    @Override
    public synchronized void addStateCallback(FileUploadStateCallback callBack) {
        mStateCallbackList.add(callBack);
    }

    @Override
    public synchronized void removeStateCallback(FileUploadStateCallback callback) {
        mStateCallbackList.remove(callback);
    }

    @Override
    public List<FileUploadStateCallback> getFileSaveStateCallbackList() {
        List<FileUploadStateCallback> callBackList = new ArrayList<FileUploadStateCallback>();
        for (FileUploadStateCallback callBack : mStateCallbackList) {
            if (callBack != null) {
                callBackList.add(callBack);
            }
        }

        return callBackList;
    }

    /**
     * 调用状态回调接口
     *
     * @param taskInfo
     * @param taskInfo
     */
    private void callStateCallback(FileUploadTaskInfo taskInfo) {
        if (mStateCallbackList.size() == 0) {
            return;
        }
        CrmLog.d(TAG, TAG + "callStateCallback: id:" + taskInfo.id + " state:" + taskInfo.state);
        final FileUploadTaskInfo localTaskInfo = taskInfo;
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                for (FileUploadStateCallback callBack : mStateCallbackList) {
                    if (callBack != null) {
                        callBack.onStateChanged(localTaskInfo, localTaskInfo.state);
                    }
                }
            }
        });
    }

    @Override
    public void onTempFileUploadSuccess(int taskId, final String storagePath) {
        if (mPreventList.get(taskId) != null) {
            //同一个taskId重复回调success, 规避
            CrmLog.d(TAG, TAG + "onTempFileUploadSuccess, duplicate taskId, ignore");
            cancelTask(taskId);
            executerNextTask();
            return;
        }
        mPreventList.put(taskId, true);

        final FileUploadTaskInfo taskInfo = getTask(taskId);
        if (taskInfo == null) {
            //产生异常taskId, 规避
            CrmLog.d(TAG, TAG + "onTempFileUploadSuccess, abnormal taskId, ignore");
            cancelTask(taskId);
            executerNextTask();
            return;
        }
        if (storagePath != null) {
            CrmLog.d(TAG, TAG + "临时路径：" + storagePath);

            if (mFileUploaderBusinessCallback != null) {
                mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        mFileUploaderBusinessCallback.onTempFileUploader(taskInfo, storagePath, mFileLength);
                    }
                });

            }
        } else {
            taskInfo.state = FileUploadStates.FAILED;
            callStateCallback(taskInfo);
            executerNextTask();
        }
    }



    @Override
    public void onTempFileUploadFailed(int taskId, Object data) {
        CrmLog.e(TAG, TAG + "upload failed,TaskId:" + taskId);

        FileUploadTaskInfo taskInfo = getTask(taskId);
        taskInfo.state = FileUploadStates.FAILED;
        callStateCallback(taskInfo);
        executerNextTask();
    }


    @Override
    public void onTempFileUploadProgress(int taskId, int cur, int total) {
        CrmLog.d(TAG, TAG + String.format("upload progress,TaskId:%d,cur:%d,total:%d", taskId, cur, total));

        FileUploadTaskInfo taskInfo = null;
        if (cur == 0 && total == 0) {
            taskInfo = getTask(taskId);
            if (taskInfo != null && taskInfo.state == FileUploadStates.WAITING) {
                CrmLog.d(TAG, TAG + "start progress,taskInfo state:" + taskInfo.state);

                taskInfo.state = FileUploadStates.UPLOADING;
                callStateCallback(taskInfo);
            }
            return;
        }

        taskInfo = getTask(taskId);
        if (taskInfo == null) {
            return;
        }

        CrmLog.d(TAG, TAG + "upload progress,taskInfo state:" + taskInfo.state);

        mFileLength = total;
        // 在这里处理下，不显示到100%
        if (cur == total) {
            cur = total - 1;
        }

        final int localTaskId = taskId;
        final int localCur = cur;
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                for (FileUploadProgressCallback callBack : mProgressCallbackList) {
                    if (callBack != null) {
                        callBack.onProgressChanged(localTaskId, localCur, mFileLength);
                    }
                }
            }
        });
    }

    @Override
    public void setTaskResult(int taskId, boolean success) {
        CrmLog.d(TAG, TAG + "setTaskResult,taskId:" + taskId);
        FileUploadTaskInfo task = getTask(taskId);
        if (task == null) {
            return;
        } else {
            if (success) {
                task.state = FileUploadStates.UPLOADED;
                removeTask(taskId);
            } else {
                task.state = FileUploadStates.FAILED;
                if(mPreventList.get(taskId)){
                    mPreventList.put(taskId, null);
                }
            }

            callStateCallback(task);
        }
        executerNextTask();
    }
}
