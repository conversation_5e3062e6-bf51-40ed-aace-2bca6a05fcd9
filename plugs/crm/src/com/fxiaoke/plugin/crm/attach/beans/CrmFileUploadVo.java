/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.attach.beans;

import com.facishare.fs.pluginapi.fileserver.upload.FileUploadAbstractVo;

/**
 * Created by xudd on 2016/3/24.
 */
public class CrmFileUploadVo extends FileUploadAbstractVo{

    public boolean mIsNetDiskFile;
    public long mSize;
    //标记该task属于哪个附件字段
    public int mIndex;
    public String mTmpPath;

    public CrmFileUploadVo(boolean isNetDiskFile, long size, int index){
        mIsNetDiskFile = isNetDiskFile;
        mSize = size;
        mIndex = index;
    }
}
