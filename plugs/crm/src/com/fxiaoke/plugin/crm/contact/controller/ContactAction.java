/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.contact.controller;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.plugin.crm.bcr.scanner.Mp2Activity;
import com.fxiaoke.plugin.crm.local_contact.SelectLocalContactAct;

import android.content.Intent;

/**
 * 联系人更多操作
 * Created by liuyu on 2015/8/24.
 */
public class ContactAction {

    /**
     * 拍名片
     *
     * @param context
     */
    public static void go2MP(MultiContext context) {
        Mp2Activity.startMPPage2(context);
    }

    /**
     * 本地联系人转为联系人
     *
     * @param multiContext
     */
    public static void go2SelectLC(MultiContext multiContext) {
        Intent intent = SelectLocalContactAct.getIntent(multiContext.getContext(), I18NHelper.getText("crm.local_contact"
                + ".SelectLocalContactAct.1309")/* 选择本地联系人 */);
        multiContext.startActivityForResult(intent, SelectLocalContactAct.KEY_REQUEST_SLC);
    }
}
