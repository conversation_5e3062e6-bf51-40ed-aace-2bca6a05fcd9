package com.fxiaoke.plugin.crm.contact.views;

import android.content.Context;
import androidx.annotation.NonNull;
import android.view.Gravity;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup.LayoutParams;
import android.widget.ImageView;
import android.widget.ImageView.ScaleType;
import android.widget.LinearLayout;

import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.metadata.list.ListSource;
import com.facishare.fs.metadata.list.beans.ListItemArg;
import com.facishare.fs.metadata.list.modelviews.ListItemContentMView;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.contact.ContactUtils;

/**
 * Created by xiangd on 2018/11/7.
 */
public class ContactListItemContentMView extends ListItemContentMView {

    private ImageView mPhoneImgView;

    public ContactListItemContentMView(@NonNull MultiContext multiContext) {
        super(multiContext);
    }

    @Override
    protected View onCreateView(Context context) {
        View rootView = super.onCreateView(context);
        // 选对象列表页和相关卡片不显示打电话操作
        if (ListSource.SelectList != getContentAdapter().getListSource() && ListSource.RelatedCard != getContentAdapter().getListSource()) {
            initPhoneLayout(context);
        }
        return rootView;
    }

    private void initPhoneLayout(Context context) {
        // 打电话
        mPhoneImgView = new ImageView(context);
        mPhoneImgView.setImageResource(R.drawable.fcrm_icon_phone);
        mPhoneImgView.setScaleType(ScaleType.CENTER_INSIDE);
        mRightExtraStub.setInflatedView(mPhoneImgView).inflate();
        LayoutParams params = mPhoneImgView.getLayoutParams();
        params.width = FSScreen.dip2px(60);
        params.height = LayoutParams.MATCH_PARENT;
        if (params instanceof LinearLayout.LayoutParams) {
            LinearLayout.LayoutParams params2 = (LinearLayout.LayoutParams) params;
            params2.gravity = Gravity.CENTER_VERTICAL;
        }
        mPhoneImgView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                final ListItemArg itemArg = (ListItemArg) view.getTag();
                if (itemArg == null) {
                    return;
                }

                ContactUtils.performPhoneClick(getMultiContext().getContext(), itemArg.objectDescribe, itemArg
                        .objectData);
            }
        });
    }



    @Override
    protected void onUpdate(ListItemArg listItemArg) {
        if (mPhoneImgView != null) {
            mPhoneImgView.setTag(listItemArg);
        }
    }
}
