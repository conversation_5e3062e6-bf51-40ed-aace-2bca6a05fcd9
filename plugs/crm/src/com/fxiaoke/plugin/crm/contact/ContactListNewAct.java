/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.contact;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.actions.OperationItem;
import com.facishare.fs.metadata.beans.FilterScene;
import com.facishare.fs.metadata.beans.NewBatchImportAddressBookResult;
import com.facishare.fs.metadata.beans.RecordType;
import com.facishare.fs.metadata.list.MetaDataListAct;
import com.facishare.fs.metadata.list.beans.search_query.OrderInfo;
import com.facishare.fs.metadata.list.contract.MetaDataListContract.Presenter;
import com.facishare.fs.metadata.utils.CallUtil;
import com.facishare.fs.pluginapi.contact.beans.LocalContactEntity;
import com.facishare.fs.pluginapi.crm.type.CoreObjType;
import com.facishare.fs.pluginapi.crmservice.CrmObjectRightService;
import com.facishare.fs.pluginapi.crmservice.GetObjectRightCallback;
import com.fxiaoke.fscommon.http.WebApiExecutionCallbackWrapper;
import com.fxiaoke.fscommon.sandbox.SandboxContextManager;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.basic_setting.event.ChangeOwnerOpsEvent;
import com.fxiaoke.plugin.crm.basic_setting.event.RecoverOpsEvent;
import com.fxiaoke.plugin.crm.bcr.scanner.MpParams;
import com.fxiaoke.plugin.crm.contact.MyContactsFragment.OnTimeoutLoadCallback;
import com.fxiaoke.plugin.crm.contact.event.ContactDeleteEvent;
import com.fxiaoke.plugin.crm.local_contact.SelectLocalContactAct;
import com.fxiaoke.plugin.crm.scanmp.ScanMPConstants;
import com.fxiaoke.plugin.crm.scanmp.activity.ScanAddCustomerContactTabAct;
import com.fxiaoke.plugin.crm.scanmp.activity.ScanSelectRecordTypeAct;
import com.fxiaoke.plugin.crm.scanmp.beans.ScanAddConfig;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager.LayoutParams;
import de.greenrobot.event.core.ISubscriber;
import de.greenrobot.event.core.MainSubscriber;

/**
 * 联系人优先按“A-Z顺序”显示优化规则：
 * 1. 每次进入联系人列表页，都会记录一次本地联系人加载时长localLoadTime。
 * 2. 首次进入联系人列表页，优先按“A-Z顺序”显示本地联系人。
 *    当本地联系人数据加载时长超过5s时，自动切换到默认的排序方式，从网络拉取数据显示。
 * 3. 后续每次进入列表页，都根据前次的本地联系人加载时间是否大于5s, 选择显示顺序：
 *    localLoadTime <  5s时，按“A-Z顺序”，当加载时长超过5s仍会自动切换为默认；
 *    localLoadTime >= 5s时，按默认的排序方式显示，从网络拉取数据显示。
 * Created by xiangd on 2018/10/30.
 */
public class ContactListNewAct extends MetaDataListAct {

    public static final String SORT_BY_AZ = "sort_by_az";
    public static OrderInfo ORDER_BY_AZ = new OrderInfo(I18NHelper.getText("crm.contact.ContactListActivity.1631")/* 我负责的联系人按A-Z排序 */,
            SORT_BY_AZ, true);
    /**
     * “我负责的”场景apiName
     */
    public static final String MY_CONTACT_SCENE_API_NAME = "InCharge";
    /**
     * 通过A-Z索引显示联系人的Fragment
     * 备注：此Fragment只有在“我负责的联系人按A-Z排序”，场景为“我负责的”时显示
     */
    private MyContactsFragment mAZContactFragment;

    private ScanAddConfig mAddConfig;
    /**
     * 首次加载是否优先按A-Z顺序显示本地联系人
     */
    private boolean mFirstLoadLocalContact;
    /**
     * 是否是PRM 模式 671 add
     * PRM 模式：不支持展示本地通讯录和A-Z排序
     */
    private boolean mIsPrmMode;
    private ContactListPresenter mContactListPresenter;

    @Override
    protected Presenter createPresenter() {
        mContactListPresenter=new ContactListPresenter(this, getTargetApiName(), this)
                .setPresenterInterface(new ContactListPresenter.PresenterInterface() {
                    @Override
                    public void changeListContent(OrderInfo selectedOrderInfo,
                                                  boolean isAZContactFragment) {
                        if (isAZContactFragment) {
                            mContactListPresenter.checkMyContactScene();
                            showAZContactFragment();
                        } else {
                            showContactListFrag();
                        }
                    }
                });
        mContactListPresenter.setPrmMode(mIsPrmMode);
        mContactListPresenter.setORDER_BY_AZ(ORDER_BY_AZ);
        mContactListPresenter.setFirstLoadLocalContact(mFirstLoadLocalContact);
        return mContactListPresenter;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setSoftInputMode(LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        super.initData(savedInstanceState);
        if (savedInstanceState != null) {
            CallUtil.restoreInstanceState(savedInstanceState);
        }
        mIsPrmMode = SandboxContextManager.getInstance().isUpEa(this);
        //是否进行加载展示本地联系人
        mFirstLoadLocalContact = !mIsPrmMode && ContactUtils.checkShowLocalContactFirst();
    }

    @Override
    protected void handleFragment() {
        super.handleFragment();
        if (mIsPrmMode) {
            //只展示非本地联系人
            showContactListFrag();
        } else {
            // 2个Fragment始终初始化，进行show和hide操作
            showAZContactFragment();
            if (!mFirstLoadLocalContact) {
                showContactListFrag();
            }
        }
    }

    @Override
    protected boolean isShowListFragment() {
        //联系人列表有自己的列表展示规则，设置初始化时候先隐藏 listFragment
        return false;
    }

    @Override
    protected Fragment getTargetListFragment() {
        //联系人有两种列表，需要区分
        return mContactListPresenter.getCurrentInfo() == ORDER_BY_AZ ? mAZContactFragment : mListFrag;
    }

    @Override
    protected void onResume() {
        super.onResume();
        //打电话后 发送销售记录
        CallUtil.go2SendSRAfterCall(getMultiContext());
    }

    @Override
    public void onSafeSaveInstanceState(@NonNull Bundle outState) {
        super.onSafeSaveInstanceState(outState);
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        CallUtil.onSaveInstanceState(outState);
    }

    /**
     * 从A-Z排序方式切换到默认排序
     */
    private void switchToDefaultOrder() {
        //排序条件
        List<OrderInfo>sConditions=mContactListPresenter.getSortCondition();
        FilterScene cScenes = mContactListPresenter.getCurrentScene();
        OrderInfo dInfo=mContactListPresenter.getDefaultOrder();

        if(sConditions!=null) {
            if(dInfo!=null){
                for(int i=0;i<sConditions.size();i++){
                    OrderInfo info=sConditions.get(i);
                    if(TextUtils.equals(dInfo.fieldName,info.fieldName)
                            &&dInfo.isAsc==info.isAsc){
                        mContactListPresenter.setCurrentInfo(info);
                        break;
                    }
                }
            }else{
                int azIndex = sConditions.indexOf(ORDER_BY_AZ);
                int sSize = sConditions.size() - 1;
                if (sSize > azIndex) {
                    mContactListPresenter.setCurrentInfo(sConditions.get(sSize));
                } else {
                    mContactListPresenter.setCurrentInfo(sConditions.get(azIndex - 1));
                }
            }
        }
    }

    @Override
    public void onUpdateSelectedScene(FilterScene filterScene) {
        if (filterScene != null) {
            // 场景不是我负责的，排序方式却是 A-Z 需切换到默认的
            if (!TextUtils.equals(filterScene.apiName, MY_CONTACT_SCENE_API_NAME)
                    && ORDER_BY_AZ == mContactListPresenter.getCurrentInfo()) {
                switchToDefaultOrder();
                showContactListFrag();
            }
        }
        super.onUpdateSelectedScene(filterScene);
    }

    private void showAZContactFragment() {
        mContactListPresenter.setIsFromLoadLocalContact(true);
        mScrollLinearLayout.getRefreshHeader().setVisibility(View.GONE);
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        if (mAZContactFragment == null) {
            mAZContactFragment = new MyContactsFragment();
            if (mFirstLoadLocalContact) {
                // 首次加载以A-Z顺序显示本地联系人时，加载时间过长需切到默认显示模式，从网络拉取
                mAZContactFragment.setOnTimeoutLoadCallback(new OnTimeoutLoadCallback() {
                    @Override
                    public void onTimeoutLoad() {
                        switchToDefaultOrder();
                        showContactListFrag();
                        // 场景数据成功拉取时，才发起请求
                        if (mBasePresenter.getCurrentScene() != null && mListFrag != null) {
                            mListFrag.showResetBtn(mPresenter.hasFilters());
                            mListFrag.sortRefresh(mContactListPresenter.getCurrentInfo());
                        }
                    }

                });
            }
            ft.add(R.id.container, mAZContactFragment);
        }
        setCurrentScrollContainer(mAZContactFragment);
        ft.show(mAZContactFragment).hide(mListFrag).commitAllowingStateLoss();
    }

    private void showContactListFrag() {
        mContactListPresenter.setIsFromLoadLocalContact(false);
        mScrollLinearLayout.getRefreshHeader().setVisibility(View.VISIBLE);
        if (mListFrag == null) {
            super.initListFrag();
        }
        if(mIsPrmMode){
            getSupportFragmentManager().beginTransaction()
                    .show(mListFrag).commitAllowingStateLoss();
        }else {
            mAZContactFragment.setOnTimeoutLoadCallback(null);
            getSupportFragmentManager().beginTransaction()
                    .show(mListFrag).hide(mAZContactFragment).commitAllowingStateLoss();
        }
        setCurrentScrollContainer(mListFrag);
    }

    private void startRefresh() {
        if (mContactListPresenter.getCurrentInfo() == ORDER_BY_AZ) {
            showAZContactFragment();
        } else {
            showContactListFrag();
            refreshList();
        }
    }

    /**
     * 批量上传联系人
     */
    private void batchImportAddressBook(List<Map<String, Object>> infos) {
        showLoading();
        ContactMetaService.batchImportAddressBook(infos,
                new WebApiExecutionCallbackWrapper<NewBatchImportAddressBookResult>(
                        NewBatchImportAddressBookResult.class,this) {
                    @Override
                    public void succeed(NewBatchImportAddressBookResult result) {
                        dismissLoading();
                        ToastUtils.show(I18NHelper.getText("crm.crm.ContactListActivity.1")/* 导入成功 */);
                        startRefresh();
                    }

                    @Override
                    public void failed(String error) {
                        super.failed(error);
                        dismissLoading();
                        ToastUtils.show(error);
                    }
                });
    }

    @Override
    protected List<ISubscriber> onGetEvents() {
        List<ISubscriber> subscribers = super.onGetEvents();
        subscribers.add(new MainSubscriber<ChangeOwnerOpsEvent>() {
            @Override
            public void onEventMainThread(ChangeOwnerOpsEvent changeOwnerOpsEvent) {
                startRefresh();
            }
        });
        subscribers.add(new MainSubscriber<RecoverOpsEvent>() {
            @Override
            public void onEventMainThread(RecoverOpsEvent recoverOpsEvent) {
                startRefresh();
            }
        });
        subscribers.add(new MainSubscriber<ContactDeleteEvent>() {
            @Override
            public void onEventMainThread(ContactDeleteEvent contactDeleteEvent) {
                startRefresh();
            }
        });
        return subscribers;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode != RESULT_OK) {
            return;
        }
        switch (requestCode) {
            case SelectLocalContactAct.KEY_REQUEST_SLC:
                if (data != null) {
                    List<LocalContactEntity> lctList = (ArrayList<LocalContactEntity>) data
                            .getSerializableExtra(SelectLocalContactAct.KEY_RESULT_DATA);
                    batchImportAddressBook(ContactUtils.getImportContactInfos(lctList));
                }
                break;
            case MpParams.KEY_REQUEST_MP:
                if (data != null) {
                    LocalContactEntity localContactEntity = (LocalContactEntity) data.getSerializableExtra(MpParams.KEY_LOCAL_CONTACTY);

                    mAddConfig = ScanAddConfig.builder()
                            .setLocalContactEntity(localContactEntity)
                            .build();
                    CrmObjectRightService.checkObjectRight(this, CoreObjType.Customer.apiName,
                            OperationItem.ACTION_CREATE, new GetObjectRightCallback() {
                                @Override
                                public void onSuccess(boolean hasCustomerRight) {
                                    boolean hasContactRight = mPresenter.hasOperationAction(OperationItem.ACTION_CREATE);
                                    mAddConfig.setAddCustomer(hasCustomerRight);
                                    mAddConfig.setAddContact(hasContactRight);
                                    List<CoreObjType> objTypes = new ArrayList<>();
                                    Map<CoreObjType, List<String>> recordTypes = new HashMap<>();
                                    if (hasCustomerRight) {
                                        objTypes.add(CoreObjType.Customer);
                                    }
                                    if (hasContactRight) {
                                        objTypes.add(CoreObjType.Contact);
                                        if(!TextUtils.isEmpty(mPresenter.getFixedRecordType())) {
                                            List<String> contactRecordTypes = new ArrayList<>();
                                            contactRecordTypes
                                                    .add(mPresenter.getFixedRecordType());
                                            recordTypes
                                                    .put(CoreObjType.Contact, contactRecordTypes);
                                        }
                                    }
                                    startActivityForResult(ScanSelectRecordTypeAct.getIntent(ContactListNewAct.this, objTypes, recordTypes),
                                            ScanMPConstants.REQUEST_TO_SELECTED_RECORD_TYPE);
                                }

                                @Override
                                public void onFail(String error) {
                                    ToastUtils.show(error);
                                }
                            });
                }
                break;
            case ScanMPConstants.REQUEST_TO_SELECTED_RECORD_TYPE:
                if (data != null) {
                    Map<CoreObjType, RecordType> objRecordTypes = (Map<CoreObjType, RecordType>) data.getSerializableExtra(ScanMPConstants.RECORD_TYPE);
                    if (objRecordTypes != null && !objRecordTypes.isEmpty()) {
                        for (Map.Entry<CoreObjType, RecordType> entry:objRecordTypes.entrySet()) {
                            if (entry.getValue() == null) {
                                continue;
                            }
                            CoreObjType coreObjType = entry.getKey();
                            if(coreObjType == CoreObjType.Customer) {
                                mAddConfig.setCustomerRecordType(entry.getValue().apiName);
                            } else if (coreObjType == CoreObjType.Contact) {
                                mAddConfig.setContactRecordType(entry.getValue().apiName);
                            }
                        }
                        startActivity(ScanAddCustomerContactTabAct.getIntent(ContactListNewAct.this, mAddConfig));
                    }
                }
                break;
            default:
                break;
        }
    }

}
