package com.fxiaoke.plugin.crm.contact;


import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.facishare.fs.pluginapi.crm.beans.ContactInfo;
import com.fxiaoke.plugin.crm.data_manager.disk_cache.protocol.IListDiskCache;
import com.fxiaoke.plugin.crm.lib.db.CrmDbHelper;
import com.fxiaoke.plugin.crm.lib.db.DbColumn;
import com.fxiaoke.plugin.crm.lib.db.dao.ContactInfoColumnEnum;
import com.fxiaoke.plugin.crm.sync.CSPageLog;
import com.lidroid.xutils.exception.DbException;

/**
 * Crm 联系人的数据库缓存
 * <b>创建时间</b> 2015/8/13
 *
 * <AUTHOR>
 */
public class ContactDbCache implements IListDiskCache<ContactInfo> {
    @Override
    public void addToLocal(ContactInfo contactInfo) {
        try {
            CrmDbHelper.getContactInfoDao().saveOrUpdate(contactInfo);
        } catch (DbException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void addCollectionToLocal(Collection<ContactInfo> t)     {
        try {
            List<ContactInfo> contactInfos = new ArrayList<>();
            contactInfos.addAll(t);
            CrmDbHelper.getContactInfoDao().saveOrUpdateAll(contactInfos);
        } catch (DbException e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean saveToLocal(List<ContactInfo> contactInfos) {
        try {
            clear();
            CrmDbHelper.getContactInfoDao().saveOrUpdateAll(contactInfos);
            return true;
        } catch (DbException e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public List<ContactInfo> getFromLocal() {
        try {
            //V6.2做了优化，查询少量字段
            List<ContactInfoColumnEnum> columns = new ArrayList<>();
            columns.add(ContactInfoColumnEnum.Name);
            columns.add(ContactInfoColumnEnum.Post);
            columns.add(ContactInfoColumnEnum.CustomerID);
            columns.add(ContactInfoColumnEnum.CustomerName);
            columns.add(ContactInfoColumnEnum.Tel);
            columns.add(ContactInfoColumnEnum.Mobile);
            columns.add(ContactInfoColumnEnum.NameOrder);
            columns.add(ContactInfoColumnEnum.NumInfo);
            return CrmDbHelper.getContactInfoDao().findAllLimitColumns(columns);
        } catch (DbException e) {
            e.printStackTrace();
            CSPageLog.e(e.toString());
        }
        return null;
    }

    @Override
    public void clear() {
        try {
            CrmDbHelper.getContactInfoDao().deleteAll();
        } catch (DbException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void removeFromLocal(ContactInfo contactInfo) {
        try {
            CrmDbHelper.getContactInfoDao().delete(contactInfo);
        } catch (DbException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void removeListFromLocal(List<ContactInfo> contactInfos) {
        try {
            CrmDbHelper.getContactInfoDao().deleteList(contactInfos);
        } catch (DbException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void removeFromLocalById(Object id) {
        if (id == null) return;
        try {
            CrmDbHelper.getContactInfoDao().deleteById(id.toString());
        } catch (DbException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void removeFromLocalByIds(List ids) {
        if (ids == null) return;
        List<String> idStrList = new ArrayList<>();
        for (Object id : ids) {
            idStrList.add(id.toString());
        }
        try {
            CrmDbHelper.getContactInfoDao().deleteListById(idStrList);
        } catch (DbException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void updateLocal(ContactInfo contactInfo) {
        try {
            if (contactInfo == null) return;
            CrmDbHelper.getContactInfoDao().update(contactInfo, DbColumn.ContactInfoColumn._ContactID + " = ?", new String[]{contactInfo.mContactID});
        } catch (DbException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void updateCollection(Collection<ContactInfo> collection) {
        try {
            List<ContactInfo> infos = new ArrayList<>();
            infos.addAll(collection);
            CrmDbHelper.getContactInfoDao().saveOrUpdateAll(infos);
        } catch (DbException e) {
            e.printStackTrace();
        }
    }
}
