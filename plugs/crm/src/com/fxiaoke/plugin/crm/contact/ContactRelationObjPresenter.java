/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.contact;

import androidx.fragment.app.FragmentActivity;

import com.facishare.fs.metadata.beans.NewBatchImportAddressBookResult;
import com.facishare.fs.metadata.detail.RelationObjListConfig;
import com.facishare.fs.metadata.detail.contract.RelationObjContract;
import com.facishare.fs.metadata.detail.presenter.AllRelationObjsPresenter;
import com.facishare.fs.metadata.list.webmenu.MetaWMController;
import com.fxiaoke.plugin.crm.bcr.scanner.ScanAddAction;
import com.fxiaoke.plugin.crm.common.CommonMetaWMController;
import com.fxiaoke.plugin.crm.contact.actions.AddressBookImportAction;
import com.fxiaoke.plugin.crm.contact.utils.ContactRalateUtil;

/**
 * <AUTHOR>
 * @date 2019/9/19 9:53
 * @description
 */
public class ContactRelationObjPresenter extends AllRelationObjsPresenter {

    private CommonMetaWMController mWmOpsController;
    private ScanAddAction mScanAction;
    private AddressBookImportAction mImportAction;
    public ContactRelationObjPresenter(FragmentActivity activity, RelationObjContract.View view, RelationObjListConfig config) {
        super(activity,view, config);
    }

    @Override
    public MetaWMController getOpsController() {
        if (mWmOpsController == null) {
            mWmOpsController = new CommonMetaWMController(super.getOpsController()) {
                @Override
                public void onScanCard() {
                    mScanAction = new ScanAddAction(mView.getMultiContext());
                    if (mAddRelateAction != null) {
                        mAddRelateAction.setScanner(mScanAction);
                    }
                    mAddRelateAction.start(mView);
                }

                @Override
                public void onSelectLocalContact() {
                    //全部相关页面，通讯录导入
                    mImportAction = new AddressBookImportAction(mView.getMultiContext());
                    mImportAction.setOnAddressBookImportListener(new AddressBookImportAction.OnAddressBookImportListener() {
                        @Override
                        public void onSucceed(NewBatchImportAddressBookResult result) {
                            mView.refreshList();
                        }

                        @Override
                        public void onError(String error) {

                        }
                    });
                    mImportAction.setRelatedObjInfo(ContactRalateUtil.getRelatedObjectInfo(mView
                            .getAssociatedFieldApiName(), mView.getObjectData()));
                    mImportAction.start(mView);
                }
            };
        }
        //添加扫名片
        mWmOpsController.addScanMpMenuItem();
        //添加通讯录导入
        mWmOpsController.addLocalContactImportAction();
        return mWmOpsController;
    }

    interface OnGetPartnerRecordTypeListener{
        void callBack(String recordType);
    }
}
