/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.utils;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.provider.ContactsContract;
import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.afollestad.materialdialogs.DialogFragmentWrapper;
import com.afollestad.materialdialogs.MaterialDialog;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.ILoadingView;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.pluginapi.GetUserArgs;
import com.facishare.fs.pluginapi.GetUserCallback;
import com.facishare.fs.pluginapi.contact.beans.LocalContactEntity;
import com.facishare.fs.pluginapi.contact.beans.User;
import com.facishare.fs.pluginapi.crm.beans.ContactInfo;
import com.facishare.fs.pluginapi.crm.fieldauthority.FieldAuthUtils;
import com.fxiaoke.plugin.crm.Shell;
import com.fxiaoke.plugin.crm.contact.ContactUtils;
import com.fxiaoke.plugin.crm.contact.consts.ContactConstants;
import com.lidroid.xutils.util.SystemActionsUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by liuyu on 2015/9/7.
 * 用户交流操作类，
 * 封装了如：发企信、打电话、发短信、导出到手机等基本操作
 */
public class ConnectUserAction {

    /**
     * 保存到手机
     * 优先保存手机、其次座机
     *
     * @param context
     * @param user
     */
    public static void saveToLocal(Context context, User user) {
        String mobile = isMobileEnabled(user) ? user.getMobile() : user.getBizPhone();
        addContacts(context, TextUtils.isEmpty(mobile) ? null : new String[]{mobile},
                user.getName(),
                user.getEmail(),
                FSContextManager.getCurUserContext().getContactsDataDelegate().getDepNames(user.getOrg()) + " " +
                        user.getPosition(),
                Shell.getBusinessAccount());
    }

    /**
     * 保存联系人到本地
     */
    public static void saveToLocal(Context context, ContactInfo contactInfo) {
        if (contactInfo == null) {
            return;
        }

        addContacts(context, ContactUtils.getMobilOrPhone(contactInfo),
                getShowStr(contactInfo.mName),
                getShowStr(contactInfo.mEmail),
                (getShowStr(contactInfo.mDepartment) + " " + getShowStr(contactInfo.mPost)).trim(),
                getShowStr(contactInfo.mCompany));
    }

    public static void saveToLocal(Context context, ObjectData objectData) {
        if (objectData == null) {
            return;
        }

        List<String> mobileOrPhoneList = ContactUtils.getMobileOrPhoneList(context, objectData);
        addContacts(context,
                mobileOrPhoneList.toArray(new String[mobileOrPhoneList.size()]),
                getShowStr(objectData.getName()),
                getShowStr(objectData.getString(ContactConstants.API_EMAIL)),
                (getShowStr(objectData.getString(ContactConstants.API_DEPARTMENT))
                        + " " + getShowStr(objectData.getString(ContactConstants.API_JOB_TITLE))).trim(),
                getShowStr(objectData.getString(ContactConstants.API_COMPANY)));
    }

    private static String getShowStr(String fieldName) {
        return (fieldName != null && FieldAuthUtils.isHasShowRight(fieldName)) ? fieldName : "";
    }

    /**
     * 保存本地通讯录对象到本地
     *
     * @param localCE
     *
     * @return
     */
    public static void saveToLocal2(Context context, LocalContactEntity localCE) {
        if (localCE != null) {
            //处理电话
            String homeTel = localCE.getHomePhone();
            String workTel = localCE.getWorKPhone();
            List<String> tel = new ArrayList<>();
            if (!TextUtils.isEmpty(localCE.getMobile())) {
                tel.add(localCE.getMobile());
            }
            if (!TextUtils.isEmpty(localCE.getCompanyPhone())) {
                tel.add(localCE.getCompanyPhone());
            }
            if (!TextUtils.isEmpty(workTel)) {
                tel.add(workTel);
            }
            if (!TextUtils.isEmpty(homeTel)) {
                tel.add(homeTel);
            }
            if (!TextUtils.isEmpty(localCE.getCallbackPhone())) {
                tel.add(localCE.getCallbackPhone());
            }
            if (!TextUtils.isEmpty(localCE.getCompanyMainPhone())) {
                tel.add(localCE.getCompanyMainPhone());
            }
            if (!TextUtils.isEmpty(localCE.getCarPhone())) {
                tel.add(localCE.getCarPhone());
            }
            String[] phones = tel.toArray(new String[tel.size()]);
            addContacts(context, phones, localCE.getName(), localCE.getEmail(), localCE.getPost(), localCE.getCompany());
        }
    }

    public static void addContacts(Context context, String[] phones, String name, String email, String title,
                                   String company) {
        context.startActivity(getAddContactToLocalIntent(phones, name, email, title, company));
    }

    @NonNull
    public static Intent getAddContactToLocalIntent(String[] phones, String name, String email,
                                                    String title, String company) {
        Intent intent = new Intent(Intent.ACTION_INSERT,
                Uri.withAppendedPath(Uri.parse("content://com.android.contacts"), "contacts"));
        if (phones != null) {
            if (phones.length > 0) {
                intent.putExtra(ContactsContract.Intents.Insert.PHONE, phones[0]);
            }
            if (phones.length > 1) {
                intent.putExtra(ContactsContract.Intents.Insert.SECONDARY_PHONE, phones[1]);
            }
            if (phones.length > 2) {
                intent.putExtra(ContactsContract.Intents.Insert.TERTIARY_PHONE, phones[2]);
            }
        }
        if (name != null) {
            intent.putExtra(ContactsContract.Intents.Insert.NAME, name);
        }
        if (email != null) {
            intent.putExtra(ContactsContract.Intents.Insert.EMAIL, email);
        }
        if (title != null) {
            intent.putExtra(ContactsContract.Intents.Insert.JOB_TITLE,
                    title.replaceAll("null", ""));
        }
        if (!TextUtils.isEmpty(company)) {
            intent.putExtra(ContactsContract.Intents.Insert.COMPANY, company);
        }
        return intent;
    }

    /**
     * 发企信 打电话 发短信 保存到手机
     * 过滤掉对本人的操作
     *
     * @param context
     * @param userId
     */
    public static void showUserOps(final FragmentActivity context, final int userId) {
        if (String.valueOf(userId).equalsIgnoreCase(Shell.getEmployeeID())) {
            return;
        }
        final User user = Shell.getUserById(userId);
        if (user == null) {
            return;
        }
        if (TextUtils.isEmpty(user.getMobile()) && TextUtils.isEmpty(user.getBizPhone())) {
            if (context instanceof ILoadingView) {
                ((ILoadingView) context).showLoading();
            }
            FSContextManager.getCurUserContext().getContactCache().getUserWithMobileOrTel(new GetUserArgs.Builder().setId(userId).build(), new GetUserCallback() {
                @Override
                public void onUserGot(User newUser) {
                    if (context instanceof ILoadingView) {
                        ((ILoadingView) context).dismissLoading();
                    }
                    showOpsDialog(context, userId, newUser);
                }
            });
        } else {
            showOpsDialog(context, userId, user);
        }
    }

    /**
     * 展示打电话对话框
     * @param context
     * @param userId
     * @param user
     */
    private static void showOpsDialog(final FragmentActivity context, final int userId, final User user) {
        String title = I18NHelper.getFormatText("crm.utils.FxCrmUtils.v1.4384"/* 联系 {0}   */ , user.getName());
        String[] items;
        if (isMobileEnabled(user)) {
            items = new String[] {
                    I18NHelper.getText("xt.bc_person_detail_layout.text.sending_a_letter")/* 发企信 */,
                    I18NHelper.getFormatText("crm.commondetail.BaseBottomBarMoreOpsWMController.v1.1643",user.getMobile())/* 打电话 {0} */,
                    I18NHelper.getFormatText("account.easy_login.oper.send_sms_with_phone",user.getMobile())/* 发短信 {0} */,
                    I18NHelper.getText("crm.utils.FxCrmUtils.4383")/* 保存到手机 */
            };
            DialogFragmentWrapper.showListWithTitle(context, title, items, new MaterialDialog.ListCallback() {
                @Override
                public void onSelection(MaterialDialog dialog, View itemView, int which, CharSequence text) {
                    switch (which) {
                        case 0:
                            Shell.sendQixin(context, userId);
                            break;
                        case 1:
                            String number = user.getMobile();
                            SystemActionsUtils.delPhone(context, number);
                            break;
                        case 2:
                            SystemActionsUtils.sendSMS(context, user.getMobile());
                            break;
                        case 3:
                            saveToLocal(context, user);
                            break;
                        default:
                            break;
                    }
                }
            });
        } else if (isBizPhoneEnabled(user)) {
            items = new String[] {
                    I18NHelper.getText("xt.bc_person_detail_layout.text.sending_a_letter")/* 发企信 */,
                    I18NHelper.getFormatText("crm.commondetail.BaseBottomBarMoreOpsWMController.v1.1643",user.getMobile())/* 打电话 {0} */,
                    I18NHelper.getText("crm.utils.FxCrmUtils.4383")/* 保存到手机 */
            };
            DialogFragmentWrapper.showListWithTitle(context, title, items, new MaterialDialog.ListCallback() {
                @Override
                public void onSelection(MaterialDialog dialog, View itemView, int which, CharSequence text) {
                    if (which == 0) {
                        Shell.sendQixin(context, userId);
                    } else {
                        switch (which) {
                            case 0:
                                Shell.sendQixin(context, userId);
                                break;
                            case 1:
                                String number = user.getBizPhone();
                                SystemActionsUtils.delPhone(context, number);
                                break;
                            case 2:
                                saveToLocal(context, user);
                                break;
                            default:
                                break;
                        }
                    }
                }
            });
        } else {
            items = new String[] {I18NHelper.getText("xt.bc_person_detail_layout.text.sending_a_letter")/* 发企信 */
            };
            DialogFragmentWrapper.showListWithTitle(context, title, items, new MaterialDialog.ListCallback() {
                @Override
                public void onSelection(MaterialDialog dialog, View itemView, int which, CharSequence text) {
                    Shell.sendQixin(context, userId);
                }
            });
        }
    }

    /**
     * 手机号是否可用
     *
     * @param user
     *
     * @return
     */
    private static boolean isMobileEnabled(User user) {
        return user != null && !TextUtils.isEmpty(user.getMobile());
    }

    /**
     * 座机号是否可用
     *
     * @param user
     *
     * @return
     */
    private static boolean isBizPhoneEnabled(User user) {
        return user != null && !TextUtils.isEmpty(user.getBizPhone());
    }

    /**
     * 显示部门和职位
     *
     * @param view
     * @param dep
     * @param post
     */
    public static void setPostOrdep(TextView view, String dep, String post) {
        StringBuilder depStr = new StringBuilder();
        dep = FSContextManager.getCurUserContext().getContactsDataDelegate().getDepNames(dep);
        if (!TextUtils.isEmpty(post) && post.trim().length() > 0) {
            depStr.append(post);
        }
        if (!TextUtils.isEmpty(dep) && dep.trim().length() > 0) {
            if (depStr.length() > 0) {
                depStr.append(" - ");
            }
            depStr.append(dep);
        }
        if(TextUtils.isEmpty(depStr)){
            view.setText("--");
        }else {
            view.setText(depStr.toString());
        }
    }

}
