/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.promotion.detail;

import java.util.List;
import java.util.ListIterator;
import java.util.Map;

import com.facishare.fs.metadata.actions.OperationItem;
import com.facishare.fs.metadata.beans.ButtonOption;
import com.facishare.fs.metadata.beans.Layout;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.ObjectDescribe;
import com.facishare.fs.metadata.beans.components.GroupComponent;
import com.facishare.fs.metadata.data.source.MetaDataSource;
import com.facishare.fs.metadata.detail.contract.MetaDataDetailContract;
import com.facishare.fs.metadata.detail.presenter.MetaDataDetailPresenter;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.plugin.crm.hookutils.PromotionDetailDataHookUtil;

import android.text.TextUtils;

/**
 * Created by ji<PERSON><PERSON> on 2018/1/10.
 */

public class PromotionDetailPresenter extends MetaDataDetailPresenter {
    private static final String TAG = "PromotionDetailPresenter";

    public PromotionDetailPresenter(String apiName, String dataId, MetaDataDetailContract.View view) {
        super(apiName, dataId, view);
    }

    @Override
    protected void preHandleDetailData() {
        // 终端屏蔽编辑、复制按钮，不支持终端编辑促销对象
        removeEditCloneAction(getLayout() != null ? getLayout().getButtonMetadatas() : null);
        FCLog.d(TAG, "preHandleDetailData");
        try {
            GroupComponent promotionRuleGroupComponent =
                    PromotionDetailDataHookUtil.findPromotionRuleGroupComponentFromMasterLayout(getLayout());
            if(promotionRuleGroupComponent == null){
                return;
            }

            // 在主对象布局中删除促销规则的数据
            // 遍历时顺便找到详细信息中的促销规则节点，以便后续插入促销阶梯文本数据
            final Map<String, Object> layoutDetailInfoComponent = PromotionDetailDataHookUtil.removePromotionRuleTabLayout(


                    getLayout());
            if(layoutDetailInfoComponent == null){
                return;
            }

            PromotionDetailDataHookUtil.fetchMDFragData(getContext(),getLayout(),
                    promotionRuleGroupComponent, getObjectDescribe(), getObjectData(), new MetaDataSource.GetRelatedListCallBack() {
                        @Override
                        public void onDataLoaded(List<ObjectData> objectDataList, ObjectDescribe objectDescribe,
                                                 Layout layout, int totalNum) {
                            FCLog.d(TAG, "onDataLoaded");
                            if(objectDataList == null
                                    || objectDataList.isEmpty()
                                    || layout == null
                                    || layout.getComponents() == null
                                    || layout.getComponents().isEmpty()){
                                FCLog.d(TAG, "onDataLoaded, null data, return!");
                                return;
                            }

                            try{
                                // 1.拼接阶梯数据的文本:
                                String promotionLevelList =
                                        PromotionDetailDataHookUtil.getPromotionRuleLevelDescribeStr(
                                                objectDataList);

                                // 2.把促销阶梯文本插入到主对象详细信息中的促销规则节点中:
                                PromotionDetailDataHookUtil.insertPromotionLevelStr2MasterDetailInfo(
                                        promotionLevelList,
                                        layoutDetailInfoComponent,
                                        getObjectDescribe(),
                                        getObjectData());

                                // 3.刷新UI
                                mView.updateTabAndPager(getLayout(), getObjectDescribe(), getObjectData()
                                );
                            }catch (Exception e){
                                e.printStackTrace();
                            }
                        }

                        @Override
                        public void onDataNotAvailable(String error) {
                            FCLog.d(TAG, "onDataNotAvailable");
                        }
                    });

            FCLog.d(TAG, "end of preHandleDetailData");
        }catch(Exception e){
            e.printStackTrace();
        }
    }// end of preHandleDetailData()

    private void removeEditCloneAction(List<ButtonOption> buttonOptions){
        if(buttonOptions != null && !buttonOptions.isEmpty()){
            ListIterator<ButtonOption> listIterator = buttonOptions.listIterator();
            if(listIterator != null){
                while (listIterator.hasNext()){
                    ButtonOption buttonOption = listIterator.next();
                    if(TextUtils.equals(buttonOption.action, OperationItem.ACTION_EDIT)
                            || TextUtils.equals(buttonOption.action, OperationItem.ACTION_CLONE)){
                        listIterator.remove();
                    }
                }
            }
        }
    }
}
