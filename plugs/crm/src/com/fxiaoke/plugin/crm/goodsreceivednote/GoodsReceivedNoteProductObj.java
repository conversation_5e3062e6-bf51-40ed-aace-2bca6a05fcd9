/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.goodsreceivednote;

/**
 * Created by wubb on 2018/1/29.
 * 服务端定义的入库产品对象
 */

public class GoodsReceivedNoteProductObj {
    public static final String NAME="name";//入库产品ID
    public static final String GOODS_RECEIVED_NOTE_ID = "goods_received_note_id";//入库单编号
    public static final String PRODUCT_ID = "product_id";//产品名称
    public static final String IS_GIVE_AWAY = "is_give_away";//是否赠品
    public static final String SPECS="specs";//产品规格
    public static final String UNIT="unit";//基准单位
    public static final String BATCH_SN="batch_sn";//批次和序列号管理  6.5新增
    public static final String SERIAL_NUMBER_ID="serial_number_id";//序列号 6.5新增
    public static final String BATCH_ID="batch_id";//批次编号  6.5新增
    public static final String MANUFACTURE_DATE="manufacture_date";//生产日期  6.5新增
    public static final String EXPIRY_DATE="expiry_date";//有效日期  6.5新增
    public static final String WARNING_DAYS_IN_ADVANCE="warning_days_in_advance";//临到期预警天数  6.5新增
    public static final String GOODS_RECEIVED_AMOUNT = "goods_received_amount";//入库数量
    public static final String AUXILIARY_RECEIVED_QUANTITY = "auxiliary_received_quantity";//入库数量 6.8新增，适配多单位
    public static final String PURCHASE_ORDER_PRODUCT_AMOUNT="purchase_order_product_amount";//采购订单产品数量  6.6新建
    public static final String HAS_RECEIVED_AMOUNT="has_received_amount";//已入库数量  6.6新建
    public static final String PURCHASE_ORDER_PRODUCT_ID="purchase_order_product_id";//采购订单产品ID  6.6新建
    public static final String REMARK = "remark";//备注
    public static final String LIFE_STATUS = "life_status";//生命状态
    public static final String CONVERSION_RATIO="conversion_ratio";//转换比
    public static final String BASE_UNIT_COUNT="base_unit_count";//基准单位数量
    public static final String STATE_UNIT_COUNT="stat_unit_count";//统计单位数量
    public static final String WHETHER_MULTI_UNIT_ENABLED="whether_multi_unit_enabled";//是否开启多单位
    public static final String ACCEPTANCE_WAREHOUSE_ID="acceptance_warehouse_id";//入库仓库 7.0新增
    public static final String COST_PRICE = "cost_price";//成本单价 7.4新增
    public static final String COST_SUM = "cost_sum";//成本小计 7.4新增
}