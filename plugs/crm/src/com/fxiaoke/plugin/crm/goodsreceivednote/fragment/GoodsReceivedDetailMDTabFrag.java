/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.goodsreceivednote.fragment;

import android.os.Bundle;

import com.facishare.fs.metadata.detail.fragment.DetailMDTabFrag;
import com.facishare.fs.metadata.detail.fragment.DetailTabFragArg;
import com.facishare.fs.metadata.modify.Scene;
import com.facishare.fs.metadata.modify.modelviews.table.TableListAdapter;
import com.fxiaoke.plugin.crm.goodsreceivednote.adapter.GoodsReceivedTableListAdapter;

/**
 * Created by fup on 2018/2/2.
 */

public class GoodsReceivedDetailMDTabFrag extends DetailMDTabFrag {
    public static DetailMDTabFrag newInstance(DetailTabFragArg detailTabFragArg) {
        GoodsReceivedDetailMDTabFrag frag = new GoodsReceivedDetailMDTabFrag();
        if (detailTabFragArg == null) {
            return frag;
        }
        if (!(detailTabFragArg instanceof DetailMDTabFrag.DetailMDFragArg)) {
            throw new IllegalArgumentException("arg must be instanceof DetailMDFragArg");
        }

        Bundle data = new Bundle();
        data.putSerializable(KEY_FRAG_ARG, detailTabFragArg);
        frag.setArguments(data);
        return frag;
    }

    @Override
    protected TableListAdapter createListAdapter() {
        return new GoodsReceivedTableListAdapter(mMultiContext, Scene.INFO);
    }
}
