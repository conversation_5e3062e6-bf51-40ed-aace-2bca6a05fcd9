/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.goodsreceivednote.provider;

import com.facishare.fs.metadata.beans.components.GroupComponent;
import com.facishare.fs.metadata.detail.fragment.DetailTabFragArg;
import com.facishare.fs.metadata.detail.fragment.DetailTabFragProvider;
import com.fxiaoke.plugin.crm.goodsreceivednote.fragment.GoodsReceivedDetailMDTabFrag;

import androidx.fragment.app.Fragment;
import android.text.TextUtils;

/**
 * Created by fup on 2018/2/2.
 */

public class GoodsReceivedDetailTabFragProvider extends DetailTabFragProvider {
    private final String GoodsReceivedNoteProductObjGroupComApiName = "GoodsReceivedNoteProductObj_md_group_component";
    private final int GoodsReceivedDetailMDTabFragType = getCustomFragType(1);

    /**不能把所有md frag类型都覆盖成自定义的*/
    @Override
    protected int getFragType(GroupComponent component) {
        if (TextUtils.equals(GoodsReceivedNoteProductObjGroupComApiName, component.getApiName())) {
            return GoodsReceivedDetailMDTabFragType;
        }
        return super.getFragType(component);
    }

    @Override
    protected Fragment createTabFragment(int fragType, DetailTabFragArg fragArg) {
        if (GoodsReceivedDetailMDTabFragType == fragType) {
            return GoodsReceivedDetailMDTabFrag.newInstance(fragArg);
        }
        return super.createTabFragment(fragType, fragArg);
    }
}
