/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.custom_field.framework;

import java.util.List;

import com.fxiaoke.plugin.crm.contract.BasePresenter;
import com.fxiaoke.plugin.crm.contract.BaseView;
import com.fxiaoke.plugin.crm.custom_field.beans.UserDefineFieldDataInfo;
import com.fxiaoke.plugin.crm.custom_field.beans.UserDefinedFieldInfo;

/**
 * <AUTHOR>
 *         <b>DATE</b> 2016/5/11.
 */
public interface BaseUserDefinedInfoContract {
    interface View<T extends Presenter> extends BaseView<T> {
        void updateDefinedViews(List<UserDefinedFieldInfo> fieldInfos, List<UserDefineFieldDataInfo>
                dataInfos);
    }

    interface Presenter extends BasePresenter {
    }
}
