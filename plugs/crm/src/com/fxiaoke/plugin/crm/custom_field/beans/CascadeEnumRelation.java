/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.custom_field.beans;

import java.io.Serializable;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * 多级级联关系描述
 * add by liuyu
 */
public class CascadeEnumRelation implements Serializable {

    public CascadeEnumRelation() {

    }

    /**
     * 当前选项ID
     */
    private String itemID;

    /**
     * 级联字段名称
     */
    private String cascadeFieldName;

    /**
     * 级联选项ID
     */
    private String cascadeItemID;

    @JSONField(name = "M1")
    public String getItemID() {
        return itemID;
    }

    @JSONField(name = "M1")
    public void setItemID(String itemID) {
        this.itemID = itemID;
    }

    @JSONField(name = "M2")
    public String getCascadeFieldName() {
        return cascadeFieldName;
    }

    @JSONField(name = "M2")
    public void setCascadeFieldName(String cascadeFieldName) {
        this.cascadeFieldName = cascadeFieldName;
    }

    @JSONField(name = "M3")
    public String getCascadeItemID() {
        return cascadeItemID;
    }

    @JSONField(name = "M3")
    public void setCascadeItemID(String cascadeItemID) {
        this.cascadeItemID = cascadeItemID;
    }

}
