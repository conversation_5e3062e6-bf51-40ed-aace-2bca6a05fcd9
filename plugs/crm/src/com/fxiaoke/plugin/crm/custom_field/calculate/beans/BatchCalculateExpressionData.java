package com.fxiaoke.plugin.crm.custom_field.calculate.beans;

import com.alibaba.fastjson.annotation.JSONField;
import com.fxiaoke.plugin.crm.custom_field.beans.UserDefineFieldDataInfo;

import java.io.Serializable;
import java.util.List;

/**
 * Created by duyc on 2017/11/2.
 */

public class BatchCalculateExpressionData implements Serializable{

    private String groupID;
    private List<UserDefineFieldDataInfo> dataInfos;

    @JSONField(name = "M1")
    public String getGroupID() {
        return groupID;
    }

    @JSONField(name = "M1")
    public void setGroupID(String groupID) {
        this.groupID = groupID;
    }

    @JSONField(name = "M2")
    public List<UserDefineFieldDataInfo> getDataInfos() {
        return dataInfos;
    }

    @JSONField(name = "M2")
    public void setDataInfos(List<UserDefineFieldDataInfo> dataInfos) {
        this.dataInfos = dataInfos;
    }
}
