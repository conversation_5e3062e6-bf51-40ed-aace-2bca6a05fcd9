/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.custom_field.cascade.cascade_creator;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.facishare.fs.metadata.beans.fields.group.AreaNode;
import com.fxiaoke.plugin.crm.custom_field.beans.EnumDetailInfo;

/**
 * 城市联级
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2017/7/24
 */
public abstract class BaseCityCascadeDataCreator implements ICityCascadeDataCreator, IAsyncTaskCreator {

    /**
     * 城市级联数据
     */
    protected List<EnumDetailInfo> mCascadeData;
    protected ILoadCascadeDataCallback mLoadDataCallback;

    public BaseCityCascadeDataCreator(ILoadCascadeDataCallback loadDataCallback) {
        this.mLoadDataCallback = loadDataCallback;
    }

    @Override
    public void runOnAsyncTask(AreaNode node) {
        new LoadCascadeDataTask(mLoadDataCallback, this).execute(node);
    }

    /**
     * 设置list中对象mChildren字段为空
     *
     * @param list 数据源
     * @param node 节点位置
     */
    public void setChildrenNull(List<EnumDetailInfo> list, AreaNode node) {
        if (isListEmpty(list)) {
            return;
        }
        for (EnumDetailInfo info : list) {
            if (node == AreaNode.PROVINCE) {
                info.mChildren = null;
            } else if (node == AreaNode.CITY) {
                setChildrenNull(info.mChildren, AreaNode.PROVINCE);
            }
        }
    }

    public void sort(List<EnumDetailInfo> list) {
        Collections.sort(list, new Comparator<EnumDetailInfo>() {
            @Override
            public int compare(EnumDetailInfo o1, EnumDetailInfo o2) {
                if (o1 != null && o2 != null) {
                    return o1.mPinYinStr.compareTo(o2.mPinYinStr);
                } else {
                    return 0;
                }
            }
        });
    }

    public static boolean isListEmpty(List list) {
        return list == null || list.isEmpty();
    }
}
