/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.custom_field.beans;

import java.io.Serializable;

import com.fxiaoke.fxlog.module.CrmLog;
import com.fxiaoke.plugin.crm.custom_field.LookUpType;
import com.lidroid.xutils.util.ReflectXUtils;

import android.text.TextUtils;

/**
 * Created by liuyu on 2016/7/12.
 * 查找字段-属性
 */
public class ReferRuleObj implements Serializable{
    private static final long serialVersionUID = 4811799529075475463L;

    private static final String TAG = ReferRuleObj.class.getSimpleName().toString();
    /** 当前字段类型*/
    public LookUpType mModelType;
    /** 当前字段类型-原始值，用于自定义对象跳转*/
    public String mRawType;
    /** 是否单选*/
    public boolean mOnlyChooseOne = true;
    /** 上级字段名字*/
    public String mSuperModelName;

    public static ReferRuleObj parseReferRuleObj(String str) {
        ReferRuleObj result = null;
        CrmLog.d(TAG, "parseReferRuleObj " + str);
        if (!TextUtils.isEmpty(str)) {
            String[] rules = str.split("\\|");
            int len = rules.length;
            if (len > 0) {
                result = new ReferRuleObj();
                result.mRawType = rules[0];
                result.mModelType = LookUpType.translateType(rules[0]);
            }
            if (len > 1) {
                result.mOnlyChooseOne = ReflectXUtils.parseInt(rules[1]) == 0 ? true : false;
            }
            if (len > 2) {
                result.mSuperModelName = rules[2];
            }
        } else {
        }
        if (result == null) {
            //服务器数据出错 为了客户端不崩溃 加了一个默认的
            result = new ReferRuleObj();
        }
        return result;
    }
}
