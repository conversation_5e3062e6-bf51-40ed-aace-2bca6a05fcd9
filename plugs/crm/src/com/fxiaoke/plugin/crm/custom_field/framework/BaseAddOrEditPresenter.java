package com.fxiaoke.plugin.crm.custom_field.framework;

import java.util.ArrayList;
import java.util.List;

import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.pluginapi.fileserver.FileServiceBinder;
import com.facishare.fs.pluginapi.fileserver.IFileServer;
import com.fxiaoke.fxlog.module.CrmLog;
import com.fxiaoke.location.api.FsLocationResult;
import com.fxiaoke.plugin.crm.BaseActivity;
import com.fxiaoke.plugin.crm.attach.CrmAttachUploadImpl;
import com.fxiaoke.plugin.crm.attach.api.AttachUtils;
import com.fxiaoke.plugin.crm.common_view.model_views.abstract_views.CustomFieldModelView;
import com.fxiaoke.plugin.crm.common_view.model_views.concrete_views.multiimage.MultiImageUploadController;
import com.fxiaoke.plugin.crm.common_view.model_views.concrete_views.multiimage.MultiImageUtils;
import com.fxiaoke.plugin.crm.custom_field.CustomFieldUtils;
import com.fxiaoke.plugin.crm.custom_field.IsUploadRunningException;
import com.fxiaoke.plugin.crm.custom_field.PrepareDataBeforeNet;
import com.fxiaoke.plugin.crm.custom_field.beans.UserDefineFieldDataInfo;
import com.fxiaoke.plugin.crm.custom_field.beans.UserDefinedFieldInfo;
import com.fxiaoke.plugin.crm.custom_field.calculate.SFARemoteExpressionExecutor;
import com.fxiaoke.plugin.crm.custom_field.preprocess.IBeforeAddOrEditHandleCallBack;
import com.fxiaoke.plugin.crm.custom_field.preprocess.IBeforeAddOrEditHandler;
import com.fxiaoke.plugin.crm.sync.beans.FieldOwnerType;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import androidx.fragment.app.FragmentActivity;

/**
 * <AUTHOR>
 *         <b>DATE</b> 2016/5/10.
 */
public abstract class BaseAddOrEditPresenter<T extends BaseUserDefinedAddOrEditContract.View> implements
        BaseUserDefinedAddOrEditContract.Presenter {

    private final static String TAG = BaseAddOrEditPresenter.class.getSimpleName().toString();

    protected FieldOwnerType mFieldOwnerType;
    protected List<UserDefinedFieldInfo> mFieldInfos;
    protected List<UserDefineFieldDataInfo> mDataInfoList;
    protected T mView;
    private IFileServer mFileServer;
    private ServiceConnection mUploadServiceConnection;
    private CrmAttachUploadImpl mUploader;
    protected boolean mIsEditType, mIsAddedToDetail, mIsEditedToDetail;
    protected BaseActivity mActivity;

    protected IBeforeAddOrEditHandler mBeforeAddOrEditHandler;

    /**
     * 图片上传器
     */
    protected MultiImageUploadController mUploadController;

    private FsLocationResult mFsLocationResult;

    protected abstract void onUpdate(List<UserDefineFieldDataInfo> dataInfos);

    protected abstract void onAdd(List<UserDefineFieldDataInfo> dataInfos);

    protected abstract void onAddFailed(String error);

    protected abstract void onUpdateFailed(String error);

    /**
     * 默认构造，内部维护自定义模板，使用缓存的字段模板
     *
     * @param activity
     * @param ownerType
     * @param isEditType
     * @param dataInfoList
     * @param view
     */
    @SuppressWarnings("All")
    public BaseAddOrEditPresenter(BaseActivity activity, FieldOwnerType ownerType, boolean isEditType,
                                  boolean isAddedToDetail,
                                  List<UserDefineFieldDataInfo> dataInfoList, T view) {
        this.mFieldOwnerType = ownerType;
        this.mDataInfoList = dataInfoList;
        this.mIsEditType = isEditType;
        this.mIsAddedToDetail = isAddedToDetail;
        this.mView = view;
        this.mActivity = activity;
        this.mView.setPresenter(this);
        mUploadController = createUploadController();
        mBeforeAddOrEditHandler = mView.getIBeforeAddOrEditHandler();
        onCreate();
    }

    public BaseAddOrEditPresenter(BaseActivity activity, FieldOwnerType ownerType, boolean isEditType,
                                  boolean isAddedToDetail,
                                  boolean isEditedToDetail, List<UserDefineFieldDataInfo> dataInfoList, T view) {
        this(activity, ownerType, isEditType, isAddedToDetail, dataInfoList, view);
        this.mIsEditedToDetail = isEditedToDetail;
    }

    /**
     * 对象列表传入字段模板，不需要本地缓存数据
     *
     * @param activity
     * @param isEditType
     * @param fieldInfos
     * @param dataInfoList
     * @param view
     */
    public BaseAddOrEditPresenter(BaseActivity activity, boolean isEditType, List<UserDefinedFieldInfo> fieldInfos,
                                  List<UserDefineFieldDataInfo> dataInfoList, T view) {
        mFieldInfos = fieldInfos;
        this.mDataInfoList = dataInfoList;
        this.mIsEditType = isEditType;
        this.mView = view;
        this.mActivity = activity;
        this.mView.setPresenter(this);
        mUploadController = createUploadController();
        mBeforeAddOrEditHandler = mView.getIBeforeAddOrEditHandler();
        onCreate();
    }

    @Override
    public void start() {
        //使用构造方法传入的字段模板，或缓存的或业务传入的
        dealWithDefinedFields();
    }

    private void dealWithDefinedFields() {
        if (callbackDefinedFieldsAuto()) {
            callbackDefinedFieldsInfos();
        } else {
            callbackDefinedFieldsManual();
        }
    }

    /**
     * 自动回调自定义数据 默认自动
     * 否则进入callbackDefinedFieldsManual
     *
     * @return
     */
    protected boolean callbackDefinedFieldsAuto() {
        return true;
    }

    protected void callbackDefinedFieldsManual() {

    }

    protected void callbackDefinedFieldsInfos() {
        mView.updateCustomViews(mFieldInfos, mDataInfoList);
    }

    @Override
    /**
     * 新建 / 编辑时，保存
     */
    public void onSaveClick(final List<CustomFieldModelView> modelViews) {
        if (mBeforeAddOrEditHandler != null) {
            mBeforeAddOrEditHandler.onClickConfirm(new IBeforeAddOrEditHandleCallBack() {
                private static final long serialVersionUID = 6186223349008712745L;

                @Override
                public void notifyGoOn() {
                    if (mIsEditType) {
                        onUpdateClick(modelViews);
                    } else {
                        onAddClick(modelViews);
                    }
                }

                @Override
                public void notifyKeep() {
                    //do nothing
                }

                @Override
                public FragmentActivity getActivity() {
                    return mActivity;
                }
            });
        } else {
            if (mIsEditType) {
                onUpdateClick(modelViews);
            } else {
                onAddClick(modelViews);
            }
        }
    }

    /**
     * 新建时保存
     *
     * @param modelViews
     */
    private void onAddClick(List<CustomFieldModelView> modelViews) {
        if (!isDataPrepared(modelViews)) {
            return;
        }
        mView.showLoading();
        mDataInfoList = getDataInfos(modelViews);
        try {
            mUploadController.uploadPics(MultiImageUtils.UDFDIList2GSPIBList(mDataInfoList));
        } catch (IsUploadRunningException e) {
            e.printStackTrace();
            onAddFailed(null);
        }

    }

    private void onUpdateClick(List<CustomFieldModelView> modelViews) {
        if (!checkData(modelViews)) {
            return;
        }
        mView.showLoading();
        mDataInfoList = getDataInfos(modelViews);
        try {
            mUploadController.uploadPics(MultiImageUtils.UDFDIList2GSPIBList(mDataInfoList));
        } catch (IsUploadRunningException e) {
            e.printStackTrace();
            onUpdateFailed(null);
        }

    }

    /**
     * 获取 ModelView 的数据
     *
     * @param modelViews
     *
     * @return
     */
    public final List<UserDefineFieldDataInfo> getDataInfos(List<CustomFieldModelView> modelViews) {
        List<CustomFieldModelView> args = new ArrayList<>();
        if (modelViews != null) {
            args.addAll(modelViews);
        }
        if (mIsEditType) {
            return CustomFieldUtils.getUserDefineFieldDataInfos(args);
        } else {
            return CustomFieldUtils.getUserDefineFieldDataInfosFilterNull(args);
        }
    }

    /**
     * 检查数据必填项
     * (销售阶段有特殊逻辑,需要控制是否检查必填项,给子类留出控制入口)
     *
     * @param modelViews
     *
     * @return
     */
    protected boolean checkData(List<CustomFieldModelView> modelViews) {
        return isDataPrepared(modelViews);
    }

    /**
     * 数据是否符准备好了
     *
     * @return true 可以提交给服务器 false 不可以
     */
    protected boolean isDataPrepared(List<CustomFieldModelView> modelViews) {
        String checkResult = CustomFieldUtils.checkUserInput(modelViews);
        if (checkResult != null) {
            ToastUtils.show(I18NHelper.getFormatText("crm.common.text.input_plase" /* 请填写{0} */, checkResult));
            return false;
        }
        if (!isAttachAllUpload()) {
            ToastUtils.show(I18NHelper.getText("crm.inventory.AddInventoryProductsPresenter.1")/* 请等待附件上传完毕 */);
            return false;
        }
        if (SFARemoteExpressionExecutor.getInstance(mActivity).isCalculating()) {
            ToastUtils.show(I18NHelper.getText("meta.modify.AddOrEditProvider.2991")/* 有表达式正在计算，请稍后再提交 */);
            return false;
        }
        return true;
    }

    /**
     * 判断附件是否都上传成功
     *
     * @return true 附件都上传成功
     */
    protected boolean isAttachAllUpload() {
        CrmAttachUploadImpl uploader = null;
        if (mFileServer != null) {
            uploader = (CrmAttachUploadImpl) mFileServer.getFileUploader(String.valueOf(hashCode()));
        }
        return uploader == null || uploader.getUploadTaskList().size() == 0;
    }

    @Override
    public void updateLocation(FsLocationResult fsLocationResult) {
        mFsLocationResult = fsLocationResult;
    }

    private MultiImageUploadController createUploadController() {
        String bizTag = "";
        try {
            bizTag = mFieldOwnerType.coreObjType.uploadTag;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new MultiImageUploadController(mActivity,new MultiImageUploadController.MultiImageUploadCallback() {
            @Override
            public void onSuccess(int index, int id, int totalLengh) {
                CrmLog.d(TAG, "onSuccess index " + index);
                mActivity.updateLoadingContent(I18NHelper.getFormatText("crm.custom_field.BaseBatchAddOrEditAct.2"/* 上传图片%1$s/%2$s */,
                        String.valueOf(index + 1), String.valueOf(totalLengh)));
            }

            @Override
            public void onSuccess() {
                CrmLog.d(TAG, "onSuccess");
                onUploadSuccess();
            }

            @Override
            public void onFailed(int index, int id, int totalLengh) {
                CrmLog.d(TAG, "onFailed index " + index);
                onUploadFailed();
            }

            @Override
            public void onFailed() {
                CrmLog.d(TAG, "onFailed");
                onUploadFailed();
            }

            @Override
            public void onProgress(int index, int id, float percent) {

            }
        }, bizTag);
    }

    @Override
    public void onCreate() {
        Intent serviceIntent = new Intent();
        serviceIntent.setAction(IFileServer.SERVER_ATION);
        serviceIntent.setComponent(IFileServer.g_FileServiceComponentName);
        mUploadServiceConnection = new ServiceConnection() {
            @Override
            public void onServiceConnected(ComponentName name, IBinder service) {
                if (service instanceof FileServiceBinder) {
                    mFileServer = ((FileServiceBinder) service).getServer();
                    mUploader = AttachUtils.getDefaultUploader(mFileServer, String.valueOf(hashCode()));
                    mView.dealUploaderCreateSuccess();
                }
            }

            @Override
            public void onServiceDisconnected(ComponentName name) {

            }
        };
        mActivity.bindService(serviceIntent, mUploadServiceConnection, Context.BIND_AUTO_CREATE);
        mUploadController.bindUploaderService();
    }

    @Override
    public void onDestroy() {
        if (mUploadServiceConnection != null) {
            mActivity.unbindService(mUploadServiceConnection);
        }
        if (mUploader != null) {
            mUploader.destroy();
            mUploader = null;
        }
        mUploadController.destroy();
    }

    @Override
    public CrmAttachUploadImpl getUploader() {
        return mUploader;
    }

    protected void onUploadSuccess() {
        PrepareDataBeforeNet.prepare(mDataInfoList);
        mView.dismissLoading();
        mView.deliverUDFDataInfos(mDataInfoList);
        if (mIsEditType) {
            onUpdate(mDataInfoList);
        } else {
            onAdd(mDataInfoList);
        }
    }

    protected void onUploadFailed() {
        mView.dismissLoading();
        if (mIsEditType) {
            onUpdateFailed(I18NHelper.getText("ac.appcenter.presenter.edit_failed")/* 编辑失败 */);
        } else {
            onAddFailed(I18NHelper.getText("meta.source.MetaRemoteTestDataSource.3029")/* 新建失败 */);
        }
    }
}
