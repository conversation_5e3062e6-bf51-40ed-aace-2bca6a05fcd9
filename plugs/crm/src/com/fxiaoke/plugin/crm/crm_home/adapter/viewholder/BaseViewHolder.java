package com.fxiaoke.plugin.crm.crm_home.adapter.viewholder;

import android.view.View;
import android.view.ViewGroup;

import com.fxiaoke.plugin.crm.crm_home.event.OnCrmMenuClickListener;
import com.fxiaoke.plugin.crm.crm_home.menu.RowMenuBean;

/**
 * Created by xiangd on 2018/8/21.
 */
public abstract class BaseViewHolder {

    private View mRootView;
    private boolean mEditMode = false;
    /**
     * 是否展示Crm menu 消息数量
     */
    private boolean mShowMenuMessageCount = false;
    protected OnCrmMenuClickListener mOnCrmMenuClickListener;

    public BaseViewHolder(ViewGroup parentView) {
        this.mRootView = createView(parentView);
    }

    public void setEditMode(boolean editMode) {
        mEditMode = editMode;
    }

    public boolean isEditMode() {
        return mEditMode;
    }

    public void setShowMenuMessageCount(boolean showMenuMessageCount) {
        mShowMenuMessageCount = showMenuMessageCount;
    }

    public boolean isShowMenuMessageCount() {
        return mShowMenuMessageCount;
    }

    public void setOnMenuItemClickListener(OnCrmMenuClickListener listener) {
        mOnCrmMenuClickListener = listener;
    }

    public View getView() {
        return mRootView;
    }

    public abstract View createView(ViewGroup parentView);


    public abstract void updateView(RowMenuBean rowMenuBean, int position);
}
