package com.fxiaoke.plugin.crm.crm_home.view;

import java.util.List;

import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.common_utils.StringUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.fs.commonviews.dynamicgridview.DynamicGridView;
import com.fs.commonviews.dynamicgridview.DynamicGridView.OnCreateShadowBitmapListener;
import com.fs.commonviews.dynamicgridview.DynamicGridView.OnDropListener;
import com.fxiaoke.fscommon_res.utils.BrandColorRenderUtils;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.crm_home.adapter.FrequentMenuGroupAdapter;
import com.fxiaoke.plugin.crm.crm_home.event.OnCrmMenuClickListener;
import com.fxiaoke.plugin.crm.crm_home.event.OnMenuDeleteClickListener;
import com.fxiaoke.plugin.crm.crm_home.menu.CrmMenu;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import androidx.annotation.Nullable;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.AdapterView.OnItemLongClickListener;
import android.widget.FrameLayout;
import android.widget.TextView;

/**
 * 拖放功能常用菜单项
 * <p>
 * Created by xiangd on 2018/8/15.
 */
public class FrequentMenuDragLayout extends FrameLayout {

    /**
     * 最小常用菜单项数
     */
    public static final int MIN_COMMON_USE_COUNT = 3;
    /**
     * 最大常用菜单项数
     */
    public static final int MAX_COMMON_USE_COUNT = 7;

//    private View mGroupImgView;
    private TextView mGroupNameView;
    private DynamicGridView mDynamicGridView;

    private FrequentMenuGroupAdapter mGridAdapter;
    /**
     * 是否启用拖放模式
     */
    private boolean mDragModeEnable;
    private OnItemLongClickListener mOnItemLongClickListener;
    private OnDropListener mOnDropListener;
    private OnCrmMenuClickListener mOnCrmMenuClickListener;

    public FrequentMenuDragLayout(Context context) {
        this(context, null);
    }

    public FrequentMenuDragLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FrequentMenuDragLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.item_one_crm_menu_group, this, true);
//        mGroupImgView = findViewById(R.id.groupImgView);
//        BrandColorRenderUtils.changeViewBackgroundDrawable(mGroupImgView);
        mGroupNameView = findViewById(R.id.groupNameView);
        initDynamicGridView();
    }

    private void initDynamicGridView() {
        mDynamicGridView = findViewById(R.id.menuGridView);
        mDynamicGridView.setSelector(new BitmapDrawable());
//        mDynamicGridView.setDisableScrollVertical(true);
        int NUM_COLUMN = 3;
        int screenWidth = FSScreen.getScreenWidth();
        int horizontalSpacing = FSScreen.dip2px(0.5f);
        // 24 = 12 + 12 左右边距
        int columnWidth = (screenWidth - NUM_COLUMN * horizontalSpacing) / NUM_COLUMN;
        mDynamicGridView.setNumColumns(NUM_COLUMN);
        mDynamicGridView.setHorizontalSpacing(horizontalSpacing);
        mDynamicGridView.setVerticalSpacing(horizontalSpacing);
        mDynamicGridView.setColumnWidth(columnWidth);
        // 禁用编辑时Item抖动显示效果
        mDynamicGridView.setWobbleInEditMode(false);
        mGridAdapter = new FrequentMenuGroupAdapter(getContext());
        mDynamicGridView.setAdapter(mGridAdapter);
        // 自定义拖放时悬浮Bitmap的效果
        mDynamicGridView.setOnCreateShadowBitmapListener(new OnCreateShadowBitmapListener() {
            @Override
            public Bitmap onCreateShadowBitmap(View view) {
                view.setDrawingCacheEnabled(true);
                Bitmap drawingCacheBitmap = view.getDrawingCache();
                float ratio = 1.08f;
                int scaleWidth = (int) (drawingCacheBitmap.getWidth() * ratio);
                int scaleHeight = (int) (drawingCacheBitmap.getHeight() * ratio);
                Bitmap floatBitmap = Bitmap.createScaledBitmap(drawingCacheBitmap, scaleWidth, scaleHeight, true);
                view.setDrawingCacheEnabled(false);
                return floatBitmap;
            }
        });
        mDynamicGridView.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                CrmMenu crmMenu = (CrmMenu) mGridAdapter.getItem(position);
                if (mGridAdapter.notCanReorderMenu(crmMenu)) {
                    return;
                }
                if (mOnCrmMenuClickListener != null) {
                    mOnCrmMenuClickListener.onMenuItemClick(crmMenu, mDragModeEnable);
                }
            }
        });

        // 默认不启用编辑模式
        setDragModeEnable(false);
    }

    public void setOnMenuItemClickListener(OnCrmMenuClickListener listener) {
        mOnCrmMenuClickListener = listener;
    }

    public void setOnDeleteClickListener(OnMenuDeleteClickListener listener) {
        mGridAdapter.setOnDeleteClickListener(listener);
    }

    public void updateGroupName(String groupName) {
        String tag = "  "+I18NHelper.getText("com.crm.home.menu.default.tag")/*按住拖动调整排序*/;
        String sGroupName = TextUtils.isEmpty(groupName) ? "--" : groupName;
        SpannableString spanString = new SpannableString(sGroupName + tag);
        StringUtils.setSpan(
                spanString,
                tag,
                new AbsoluteSizeSpan(14, true),
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        StringUtils.setSpan(
                spanString,
                tag,
                new ForegroundColorSpan(Color.parseColor("#A9ABB3")),
                Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
        mGroupNameView.setText(spanString);
    }

    public void setGroupImgVisible(boolean isVisible) {
//        mGroupImgView.setVisibility(isVisible ? VISIBLE : INVISIBLE);
    }

    /**
     * 返回拖放排序后的菜单列表
     */
    public List<CrmMenu> getDraggedMenuList() {
        return mGridAdapter.getRealMenuList();
    }

    public void updateDataList(List<CrmMenu> menuList) {
        mGridAdapter.checkSizeUpdateList(menuList);
        updateGroupName(I18NHelper.getFormatText("crm.menu.text.common_use"/* 常用({0}/{1}) */,
                menuList.size()+"", MAX_COMMON_USE_COUNT+""));
    }

    public void addOneMenu(CrmMenu menu) {
        mGridAdapter.addOneMenu(menu);
        updateGroupName(I18NHelper.getFormatText("crm.menu.text.common_use"/* 常用({0}/{1}) */,
                mGridAdapter.getRealMenuSize()+"", MAX_COMMON_USE_COUNT+""));
    }

    public void removeOneMenu(CrmMenu menu) {
        mGridAdapter.removeOneMenu(menu);
        updateGroupName(I18NHelper.getFormatText("crm.menu.text.common_use"/* 常用({0}/{1}) */,
                mGridAdapter.getRealMenuSize()+"", MAX_COMMON_USE_COUNT+""));
    }

    /**
     * 是否启用拖放模式
     */
    public void setDragModeEnable(boolean dragModeEnable) {
        if (mDragModeEnable == dragModeEnable) {
            return;
        }
        mDragModeEnable = dragModeEnable;
        mDynamicGridView.setEditModeEnabled(dragModeEnable);

        if (mOnItemLongClickListener == null) {
            mOnItemLongClickListener = new OnItemLongClickListener() {
                @Override
                public boolean onItemLongClick(AdapterView<?> parent, View view, int position, long id) {
                    if (mGridAdapter.notCanReorderMenu((CrmMenu) mGridAdapter.getItem(position))) {
                        return true;
                    }
                    // 长按进入编辑模式
                    mDynamicGridView.startEditMode(position);
                    return true;
                }
            };
        }
        mDynamicGridView.setOnItemLongClickListener(dragModeEnable ? mOnItemLongClickListener : null);

        if (mOnDropListener == null) {
            mOnDropListener = new OnDropListener() {
                @Override
                public void onActionDrop() {
                    // 拖完放开后退出编辑模式
                    mDynamicGridView.stopEditMode();
                }
            };
        }
        mDynamicGridView.setOnDropListener(dragModeEnable ? mOnDropListener : null);
        mGridAdapter.setDragMode(dragModeEnable);
    }

}
