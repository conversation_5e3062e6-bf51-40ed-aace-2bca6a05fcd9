/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.IComponents.actions.approve;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.billy.cc.core.component.CC;
import com.facishare.fs.pluginapi.crm.ICcCRMActions;
import com.facishare.fs.pluginapi.crm.beans.WorkFlowDataInfo;
import com.facishare.fs.pluginapi.crm.type.ICrmBizApiName;
import com.facishare.fs.workflow.activities.EditWorkFlowInfoAct;
import com.facishare.fs.workflow.beans.TradeFlowStepStatus;
import com.facishare.fs.workflow.beans.WorkFlowInfo;
import com.facishare.fs.workflow.beans.WorkFlowStepInfo;
import com.facishare.fs.workflow.beans.WorkFlowType;
import com.fxiaoke.fscommon.util.CCActComAdapter;
import com.fxiaoke.plugin.crm.IComponents.ICcAction;

import android.content.Context;
import androidx.annotation.Nullable;
import android.text.TextUtils;

/**
 * 自由审批流跳转到更换负责人页面
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2019/8/27
 */
public class CcFreeApprovalChangeApprover extends CCActComAdapter implements ICcAction {

    @Nullable
    @Override
    protected Object createIntent(CC cc) {
        Context context = cc.getContext();
        List<WorkFlowDataInfo> freeWorkFlowList = new ArrayList<>();
        String dataId = cc.getParamItem(ICcCRMActions.ParamKeysApprove.DATA_ID);
        String objApiName = cc.getParamItem(ICcCRMActions.ParamKeysApprove.OBJ_API_NAME);
        int currentLevel = cc.getParamItem(ICcCRMActions.ParamKeysApprove.CURRENT_LEVEL, 0);
        Object workflowInfo = cc.getParamItem(ICcCRMActions.ParamKeysApprove.WORK_FLOW_INFO, null);
        if (workflowInfo != null) {
            List<WorkFlowDataInfo> list =
                    JSONObject.parseArray(JSONObject.toJSONString(workflowInfo), WorkFlowDataInfo.class);
            if (list != null && !list.isEmpty()) {
                freeWorkFlowList.addAll(list);
            }
        }
        WorkFlowInfo workFlowInfo = new WorkFlowInfo();
        if (TextUtils.equals(objApiName, ICrmBizApiName.SALES_ORDER_API_NAME)) {
            workFlowInfo.type = WorkFlowType.ORDER.type;
        } else if (TextUtils.equals(objApiName, ICrmBizApiName.RETURN_ORDER_API_NAME)) {
            workFlowInfo.type = WorkFlowType.RETURN_ORDER.type;
        }
        workFlowInfo.workFlowSteps = new ArrayList<>();
        int size = freeWorkFlowList.size();
        int currentPosition = size;
        boolean haveGetPosition = false;
        for (int i = 0; i < size; i++) {
            WorkFlowDataInfo dataInfo = freeWorkFlowList.get(i);
            if (dataInfo == null) {
                continue;
            }
            if (!haveGetPosition && dataInfo.status == TradeFlowStepStatus.UN_KNOW.key) {
                currentPosition = i;
                haveGetPosition = true;
            }
            workFlowInfo.workFlowSteps.add(new WorkFlowStepInfo(dataInfo));
            workFlowInfo.workFlowID = dataInfo.workFlowID;
        }
        return EditWorkFlowInfoAct.getIntent(context, workFlowInfo, dataId, currentPosition, currentLevel);
    }

    @Override
    protected boolean willSendActResult() {
        return true;
    }
}
