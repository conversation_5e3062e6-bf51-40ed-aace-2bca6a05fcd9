/*
 * Copyright (C) 2022 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.IComponents.actions;

import com.billy.cc.core.component.CC;
import com.facishare.fs.metadata.beans.MetaData;
import com.facishare.fs.pluginapi.crm.type.ICrmBizApiName;
import com.fxiaoke.plugin.crm.IComponents.ICcAction;
import com.fxiaoke.plugin.crm.customer.highsea.HighSeaUtils;
import com.fxiaoke.plugin.crm.metadataImpl.actions.PaasObjSessionAction;

import android.text.TextUtils;
import io.reactivex.android.schedulers.AndroidSchedulers;

/**
 * author: wangrz
 * date: 2022/3/24 20:59
 * description: 【公海/线索池列表】群入口
 */
public class CcEntryBusinessGroupChatVC implements ICcAction {
    @Override
    public boolean onCall(CC cc) {
        MetaData metaData = new MetaData(cc.getParams());
        String apiName = metaData.getString("apiName");
        String objectID = metaData.getString("objectID");
        AndroidSchedulers.mainThread().scheduleDirect(new Runnable() {
            @Override
            public void run() {
                if (TextUtils.equals(apiName, ICrmBizApiName.HIGHSEAS_API_NAME)) {
                    //进入公海群
                    if (!TextUtils.isEmpty(objectID)) {
                        HighSeaUtils.go2HighSeaGroupSession(cc.getContext(), objectID);
                    }
                } else if (TextUtils.equals(apiName, ICrmBizApiName.SALESCLUE_POOL_API_NAME)) {
                    //进入线索池群
                    if (!TextUtils.isEmpty(objectID)) {
                        PaasObjSessionAction.start(cc.getContext(), apiName, objectID, "");
                    }
                }
            }
        });

        return false;
    }
}
