/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.IComponents.actions;

import com.billy.cc.core.component.CC;
import com.billy.cc.core.component.CCResult;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.js.utils.JsApiHelper;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.facishare.fs.metadata.web.MetaDataJsApiFragActivity;
import com.facishare.fs.pluginapi.jsapi.SendNotificationModel;
import com.fxiaoke.fscommon.util.CCActComAdapter;
import com.fxiaoke.plugin.crm.IComponents.ICcAction;

import android.os.Bundle;
import android.text.TextUtils;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import de.greenrobot.event.core.ISubscriber;
import de.greenrobot.event.core.MainSubscriber;

/**
 * Created by zhouz on 2019-07-11.
 */
public class CcWebView extends CCActComAdapter implements ICcAction {
    @Nullable
    @Override
    protected Object createIntent(CC cc) {
        String url = cc.getParamItem("url");
        String inputData = cc.getString("inputData");
        String title = cc.getString("title");
        Boolean openInBrowser = cc.getBoolean("openInBrowser");
        String source = cc.getString("source");
        if (TextUtils.isEmpty(url)&&TextUtils.isEmpty(source)){
            return I18NHelper.getText("qx.qr_scan.guide.param_error");
        }
        if(!TextUtils.isEmpty(url)){
            url=url.replace("api.fqixin.net", "www.fxiaoke.com");
        }
        if(openInBrowser!=null && openInBrowser){
            return JsApiHelper.getBrowserIntent(url);
        }else{
            return MetaDataJsApiFragActivity.getIntent(cc.getContext(), url, inputData,
                    title, source, WebViewHook.class);
        }

    }

    @Override
    protected boolean willSendActResult() {
        return true;
    }

    public static class WebViewHook extends  MetaDataJsApiFragActivity.PageHook {
        private ISubscriber mSubscriber;
        public WebViewHook(FragmentActivity context) {
            super(context);
        }

        @Override
        public void onPostCreate(Bundle savedInstanceState) {
            super.onPostCreate(savedInstanceState);
            mSubscriber = new MainSubscriber<SendNotificationModel>() {
                @Override
                public void onEventMainThread(SendNotificationModel model) {
                    unregister();
                    setResult(model);
                }
            };
            mSubscriber.register();
        }

        @Override
        public void onDestroy() {
            super.onDestroy();
            if (mSubscriber != null){
                mSubscriber.unregister();
            }
        }

        private void setResult(SendNotificationModel model){
            if (model == null || model.params == null) {
                return;
            }
            if (TextUtils.equals(model.name,"returnResult")) {
                MetaDataUtils.sendCcResultSafe(mContext, CCResult.success(model.params));
            }
        }
    }
}
