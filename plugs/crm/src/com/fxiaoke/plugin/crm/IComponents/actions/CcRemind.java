/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.IComponents.actions;

import com.billy.cc.core.component.CC;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.fxiaoke.fscommon.util.CCActComAdapter;
import com.fxiaoke.plugin.crm.IComponents.ICcAction;

import androidx.annotation.Nullable;

/**
 * 发CRM提醒
 * Created by zhouz on 2019-07-11.
 */
public class CcRemind extends CCActComAdapter implements ICcAction {

    @Nullable
    @Override
    protected Object createIntent(CC cc) {
        return HostInterfaceManager.getIFeed().selectRemindRawDataIntent(true);
    }

    @Override
    protected boolean willSendActResult() {
        return true;
    }
}
