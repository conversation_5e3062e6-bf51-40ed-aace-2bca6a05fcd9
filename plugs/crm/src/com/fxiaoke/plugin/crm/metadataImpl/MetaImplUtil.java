/*
 * Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.metadataImpl;

import com.facishare.fs.common_utils.FSScreen;

import android.content.Context;
import android.graphics.Color;
import android.widget.TextView;

/**
 * Created by zhouz on 2018/11/5.
 */
public class MetaImplUtil {
    public static TextView createStatusTextView(Context context) {
        TextView textView = new TextView(context);
        textView.setTextSize(13);
        textView.setCompoundDrawablePadding(FSScreen.dip2px(5));
        textView.setTextColor(Color.parseColor("#a8aaa9"));
        return textView;
    }
}
