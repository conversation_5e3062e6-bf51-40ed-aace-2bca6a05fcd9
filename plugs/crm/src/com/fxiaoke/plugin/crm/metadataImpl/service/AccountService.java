/*
 * Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.metadataImpl.service;

import com.facishare.fs.i18n.I18NHelper;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.facishare.fs.metadata.config.contract.IMetaDataAccount;
import com.facishare.fs.pluginapi.AccountManager;
import com.facishare.fs.pluginapi.ContactsHostManager;
import com.facishare.fs.pluginapi.IStartActForResult;
import com.facishare.fs.pluginapi.contact.beans.Organization;
import com.facishare.fs.pluginapi.contact.beans.SelectSendRangeConfig;
import com.facishare.fs.pluginapi.contact.beans.User;
import com.fs.beans.beans.MainCircleInfos;
import com.fxiaoke.plugin.crm.Shell;

import android.content.Context;

/**
 * Created by zhenjb on 2017/1/16.
 */

public class AccountService implements IMetaDataAccount {
    public static final String RESULTE_ANSWER_KEY = "resultkey";

    @Override
    public String getUserUniqueKey() {
        return Shell.getUniformId();
    }

    @Override
    public User getCurrentUser() {
        return Shell.getUser();
    }

    @Override
    public User getUserById(int i) {
        return Shell.getUserById(i);
    }

    @Override
    public Organization getCurUserMainDep() {
        MainCircleInfos MCircles = AccountManager.getAccount().getMainCircleInfos();
        if (MCircles == null || !MCircles.hasCircleInfoList()) {
            return null;
        }
        return Shell.getOrganizationById(MCircles.infos.get(0).circleId);
    }

    @Override
    public Organization getDepByDepId(int id) {
        return Shell.getOrganizationById(id);
    }

    @Override
    public List<Organization> getCurUserAllDeps() {
        return null;
    }

    @Override
    public void goSelectUserPage(IStartActForResult activity, int requestCode, boolean single, boolean showEmpTab,
                                 boolean showDepTab, Map<Integer, String> empsMap, Map<Integer, String> depsMap,
                                 Integer filterOrganization,int selectOption,ArrayList<Integer> desinatedDeps) {
        if (!showEmpTab) {
            if (showDepTab) {
                int maxCnt = 0;
                if (single) {
                    maxCnt = 1;
                }
                String title = I18NHelper.getText("common.depart_filter.des.choose_depart");/*选择部门 */
                if(selectOption==2){
                    //仅选组织
                    title = I18NHelper.getText("common.depart_filter.des.choose_orginzation");/*选择组织 */
                }
				Shell.selectDep(activity, requestCode, title, depsMap, null, maxCnt, filterOrganization,selectOption,desinatedDeps);
            }
        } else if (showDepTab) {
            Shell.selectRangeOnlyEmpAndDepPage(activity, requestCode, I18NHelper.getText("bi.ui.DataBoardHomeAct.2121")/* 选择员工和部门 */, single, false, null, -1, null, null,
                    empsMap, depsMap);
        } else {
            int maxChoiceCnt = -1;
            if (single) {
                maxChoiceCnt = 1;
            }
            Shell.selectEmp(activity, requestCode, I18NHelper.getText("xt.reset_password_act.text.choose_member")/* 选择员工 */, false, true, false, maxChoiceCnt, null, empsMap, null,
                    null, false, false,0);
        }
    }


    /**
     * 选择员工入口
     *  @param activity
     * @param title               标题
     * @param noSelf              是否排除自己
     * @param isFirst             自己是否在最前面
     * @param onlyChooseOne       是否单选
     * @param multiChoiceMaxCount 多选人数限制
     * @param multiChoicePrompt   多选人数超限提示
     * @param backFillEmps             回填员工
     * @param filterEmps          过滤员工
     * @param desinatedEmps       指定选择员工
     * @param isHideMyName        是否将自己名字隐藏，显示为私密
     * @param disableSelectAll    是否禁用选人控件中的标题栏上的全选按钮
     * @param filterOrganization 组织id只展示当前组织下的数据
     */
    @Override
    public void selectEmp(IStartActForResult activity, int requestCode, String title,
                          boolean noSelf,
                          boolean isFirst,
                          boolean onlyChooseOne, int multiChoiceMaxCount, String multiChoicePrompt,
                          Map<Integer, String> backFillEmps,
                          int[] filterEmps, ArrayList<Integer> desinatedEmps, boolean isHideMyName,
                          boolean disableSelectAll,Integer filterOrganization) {
        Shell.selectEmp(activity, requestCode, title, noSelf, isFirst, onlyChooseOne, multiChoiceMaxCount,
                multiChoicePrompt, backFillEmps, filterEmps, desinatedEmps, isHideMyName, disableSelectAll,filterOrganization);
    }
    @Override
    public void goSelectUserPage(IStartActForResult activity, int requestCode, SelectSendRangeConfig sendRangeConfig) {
        ContactsHostManager.getContacts().selectSendRangePage(activity,requestCode,sendRangeConfig);
    }

    @Override
    public String getEmpResultAnswerKey() {
        return RESULTE_ANSWER_KEY;
    }

    @Override
    public String getDepResultAnswerKey() {
        return RESULTE_ANSWER_KEY;
    }

    @Override
    public List<Integer> getPickedEmployees() {
        return Shell.getUserIdsSelected();
    }

    @Override
    public List<Integer> getPickedDepartments() {
        return Shell.getOrgIdsSelected();
    }

    @Override
    public String getBusinessAccount() {
        return Shell.getBusinessAccount();
    }

    @Override
    public void go2UserPage(Context context, int userId) {
        Shell.go2UserPage(context, userId);
    }
}
