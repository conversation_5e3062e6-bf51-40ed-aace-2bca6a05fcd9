/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.metadataImpl.actions.checkaction;

import com.facishare.fs.metadata.actions.basic.CheckAction;
import com.facishare.fs.metadata.config.MetaDataConfig;
import com.facishare.fs.metadata.modify.duplicatecheck.action.MetaActionConfig;
import com.facishare.fs.modelviews.MultiContext;

/**
 * <AUTHOR>
 * @date 2019/4/22 10:36
 * @description
 */
public class MetaCheckDetailAction extends CheckAction<MetaActionConfig> {

    public MetaCheckDetailAction(MultiContext context) {
        super(context);
    }

    @Override
    public void start(MetaActionConfig target) {
        if (target == null || target.objectData == null) {
            return;
        }
        startActivity(MetaDataConfig.getOptions().getNavigator().getDetailIntent(getMultiContext().getContext(),
                target.objectData.getObjectDescribeApiName(), target.objectData.getID()));
    }
}
