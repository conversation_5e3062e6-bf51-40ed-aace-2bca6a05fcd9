/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.metadataImpl.factory;

import com.facishare.fs.metadata.actions.OperationItem;
import com.facishare.fs.metadata.actions.basic.CheckAction;
import com.facishare.fs.metadata.config.factory.DefaultCheckOperationFactory;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.plugin.crm.metadataImpl.actions.checkaction.MetaAddTeamMemberAction;
import com.fxiaoke.plugin.crm.metadataImpl.actions.checkaction.MetaChangePartnerOwnerAction;
import com.fxiaoke.plugin.crm.metadataImpl.actions.checkaction.MetaCheckAdminAction;
import com.fxiaoke.plugin.crm.metadataImpl.actions.checkaction.MetaCheckDetailAction;
import com.fxiaoke.plugin.crm.metadataImpl.actions.checkaction.MetaCheckOwnerAction;

/**
 * <AUTHOR>
 * @date 2019/4/22 10:13
 * @description 所有对象通用的查重按钮的操作，查看详情，联系负责人，联系管理员
 */
public class MetaCheckOperationFactory extends DefaultCheckOperationFactory {

    public MetaCheckOperationFactory(String apiName) {
        super(apiName);
    }

    @Override
    public CheckAction getMetaCheckAction(String actionName, MultiContext context) {
        if (actionName != null) {
            switch (actionName) {
                case OperationItem.ACTION_METACHECK_DETAIL:
                    return new MetaCheckDetailAction(context);
                case OperationItem.ACTION_METACHECK_OWNER:
                    return new MetaCheckOwnerAction(context);
                case OperationItem.ACTION_METACHECK_ADMIN:
                    return new MetaCheckAdminAction(context);
                case OperationItem.ACTION_ADD_TEAM_MEMBER:
                    return new MetaAddTeamMemberAction(context);
                case OperationItem.ACTION_CHANGE_PARTNER_OWNER2:
                    return new MetaChangePartnerOwnerAction(context);
            }
        }
        return super.getMetaCheckAction(actionName, context);
    }
}
