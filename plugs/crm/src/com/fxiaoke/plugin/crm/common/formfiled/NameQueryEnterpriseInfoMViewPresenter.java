package com.fxiaoke.plugin.crm.common.formfiled;

import java.util.HashMap;
import java.util.Map;

import com.facishare.fs.metadata.beans.FormFieldViewArg;
import com.facishare.fs.modelviews.ModelView;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.plugin.crm.enterpriseinfo.EnterpriseInfoConstants;

/**
 * <AUTHOR>
 * @date: 2020/9/3 11:09
 * @description:
 */
public class NameQueryEnterpriseInfoMViewPresenter extends NameICRegisterBaseMViewPresenter {

    public NameQueryEnterpriseInfoMViewPresenter(String objApiName,
                                                 String objectMappingActionName) {
        super(objApiName, objectMappingActionName);
    }

    @Override
    protected NameICRegisterEditTextMView createCustomEditView(MultiContext context,
                                                               String objApiName,
                                                               String objectMappingActionName,
                                                               FormFieldViewArg formFieldViewArg) {
        return new NameQueryEnterpriseInfoEditTextMView(context, objApiName, objectMappingActionName, formFieldViewArg);
    }

    @Override
    protected Map<String, Object> getMultiResultValue(ModelView modelView, FormFieldViewArg arg) {
        Map<String, Object> result = super.getMultiResultValue(modelView, arg);
        if(result == null){
            result = new HashMap<>();
        }
        if (modelView instanceof NameQueryEnterpriseInfoEditTextMView) {
            NameQueryEnterpriseInfoEditTextMView editTextMView =
                    (NameQueryEnterpriseInfoEditTextMView) modelView;
            //企业库字段隐藏仍赋值，禁用字段不赋值
            if (editTextMView.isIsSupportEnterpriseInfoQuery() && !editTextMView.isShowEnterpriseInfoView() && editTextMView.isActiveOfEnterpriseFiled()) {
                result.put(EnterpriseInfoConstants.API_ENTERPRISE_INFO, editTextMView.getEnterpriseInfoDataId());
            }
        }
        return result;
    }
}
