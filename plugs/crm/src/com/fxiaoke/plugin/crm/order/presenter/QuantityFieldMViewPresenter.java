/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.order.presenter;

import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.metadata.beans.fields.Field;
import com.facishare.fs.metadata.dataconverter.converter.IFieldContext;
import com.facishare.fs.metadata.dataconverter.converter.SingleChoiceConverter;
import com.facishare.fs.metadata.list.modelviews.ListItemFieldArg;
import com.facishare.fs.metadata.list.modelviews.field.IListItemFieldView;
import com.facishare.fs.metadata.list.modelviews.field.presenter.TextListFieldMViewPresenter;
import com.facishare.fs.modelviews.ModelView;
import com.facishare.fs.pluginapi.crm.type.ICrmBizApiName;

import android.text.TextUtils;

/**
 * 特殊处理数量字段Label显示逻辑，如果单位字段有值，则将单位值添加到数量字段的label上
 * 如：单位值是“个”，则数量字段Label值展示为：数量（个）
 */
public class QuantityFieldMViewPresenter extends TextListFieldMViewPresenter {

    @Override
    public boolean accept(ListItemFieldArg arg) {
        String apiName = arg.formField.getApiName();
        return super.accept(arg) && TextUtils.equals(apiName, "quantity");
    }

    @Override
    protected void updateFieldView(ModelView modelView, ListItemFieldArg arg) {
        super.updateFieldView(modelView, arg);
        String unitFieldLabel = getUnitFieldLabel(arg);
        if (!TextUtils.isEmpty(unitFieldLabel)) {
            String label = arg.formField.getLabel();
            label += "(" + unitFieldLabel + ")";
            ((IListItemFieldView) modelView).updateTitle(label);
        }
    }

    /**
     * 获取单位字段的label
     */
    private String getUnitFieldLabel(ListItemFieldArg arg) {
        if (arg == null || arg.objectData == null) {
            return null;
        }
        Field actualUnit = arg.objectDescribe == null ? null
                : arg.objectDescribe.getFields().get("actual_unit");
        String actualUnitLabel =
                getSingleChoiceFieldLabel(arg.objectData.getString("actual_unit"), actualUnit);
        if (!TextUtils.isEmpty(actualUnitLabel)) {//优先取实际单位
            return actualUnitLabel;
        }
        String unitFieldName = getUnitFieldName(arg);
        String unit = arg.objectData.getString(unitFieldName);//再取基准单位
        if (!TextUtils.isEmpty(unit)) {
            return unit;
        }
        Object productObj = arg.objectData.get("product_id__ro");//最后从产品中取产品的单位
        if (productObj instanceof JSONObject) {
            return ((JSONObject) productObj).getString("unit");
        }
        return null;
    }

    /**
     * 获取单位字段的fieldName
     */
    private String getUnitFieldName(ListItemFieldArg arg) {
        String objApiName = arg.objectDescribe == null ? "" : arg.objectDescribe.getApiName();
        switch (objApiName) {
            case ICrmBizApiName.QUOTE_LINES_API_NAME:
                return "quote_lines_unit";
            case ICrmBizApiName.NEW_OPPORTUNITY_LINES_API_NAME:
                return "new_opporutnity_lines_unit";
            case ICrmBizApiName.ORDER_PRODUCT_API_NAME:
            default:
                return "unit";
        }
    }

    /**
     * 获取单选字段的label
     */
    private String getSingleChoiceFieldLabel(Object data, Field field) {
        return new SingleChoiceConverter().convert(data, new IFieldContext() {
            @Override
            public Field getField() {
                return field == null ? new Field() : field;
            }
        });
    }
}
