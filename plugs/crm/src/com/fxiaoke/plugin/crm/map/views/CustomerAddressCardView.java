/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.map.views;

import com.facishare.fs.i18n.I18NHelper;
import com.amap.api.maps.AMapUtils;
import com.amap.api.maps.model.LatLng;
import com.fxiaoke.location.api.FsLocationResult;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.utils.CrmStrUtils;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.LinearLayout;
import android.widget.TextView;

/**
 * 地图界面下方的地址信息卡片View
 * <p/>
 * Created by xiangd on 2016/3/29.
 */
public class CustomerAddressCardView extends LinearLayout implements OnClickListener {

    private static final String TAG = CustomerAddressCardView.class.getSimpleName().toString();

    private View mCusmterCardLayout;
    private TextView mNameTextView;
    private TextView mDistanceTextView;
    private View mVerticalLineView;
    private TextView mAddressTextView;
    private View mNavigateView;
    private View mArrowView;

    private Context mContext;
    /**
     * 定位地址信息
     */
    private FsLocationResult mMyLocation;
    /**
     * 客户地址信息
     */
    private FsLocationResult mCustomerAddress;
    /**
     * 客户ID
     */
    private String mCustomerID;
    /**
     * 客户名称
     */
    private String mCustomerName;
    /**
     * 当前显示的是客户地址信息
     */
    private boolean mShowCustomer = true;

    private OnCardClickListener mOutListener;

    public interface OnCardClickListener {
        public void onNaviClick();

        public void onCardClick();
    }

    public CustomerAddressCardView(Context context) {
        this(context, null);
    }

    public CustomerAddressCardView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CustomerAddressCardView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        initView();
    }

    private void initView() {
        View view = LayoutInflater.from(mContext).inflate(R.layout.map_customer_detail_address_info_card, this, false);
        addView(view);

        mCusmterCardLayout = findViewById(R.id.customer_card);
        // 导航图标
        mNavigateView = findViewById(R.id.navigate_textView);
        // 客户名称
        mNameTextView = (TextView) findViewById(R.id.name_textView);
        // 当前与客户的距离
        mDistanceTextView = (TextView) findViewById(R.id.distance_textView);
        mVerticalLineView = findViewById(R.id.vertical_line_view);
        // 客户地址
        mAddressTextView = (TextView) findViewById(R.id.address_textView);
        mArrowView = findViewById(R.id.fcrm_icon_drill);

        mCusmterCardLayout.setOnClickListener(this);
        mNavigateView.setOnClickListener(this);
    }

    public void setOnCardClickListener(OnCardClickListener listener) {
        mOutListener = listener;
    }

    @Override
    public void onClick(View view) {
        int i = view.getId();
        if (i == R.id.customer_card) {
            if (mOutListener != null) {
                mOutListener.onCardClick();
            }

        } else if (i == R.id.navigate_textView) {
            if (mOutListener != null) {
                mOutListener.onNaviClick();
            }

        } else {
        }
    }

    public void setCustomerID(String customerID) {
        mCustomerID = customerID;
    }

    public void setCustomerName(String customerName) {
        mCustomerName = customerName;
    }

    public void setMyLocation(FsLocationResult myLocation) {
        mMyLocation = myLocation;
        if (mShowCustomer) {
            updateUI(mCustomerAddress, false);
        }
    }

    public void updateUI(FsLocationResult locationResult, boolean isMyLocation) {
        if (isMyLocation) {
            mMyLocation = locationResult;
        } else {
            mCustomerAddress = locationResult;
        }

        mShowCustomer = isMyLocation ? false : true;
        mCusmterCardLayout.setClickable(mShowCustomer);
        mNavigateView.setVisibility(mShowCustomer ? View.VISIBLE : View.GONE);
        //mArrowView.setVisibility(mShowCustomer ? View.VISIBLE : View.GONE);

        mNameTextView.setText(mShowCustomer ? mCustomerName : I18NHelper.getText("crm.layout.my_location_card.1815")/* 我的位置 */);

        if (!isMyLocation && mCustomerAddress != null && mMyLocation != null) {
            float lineDistance = AMapUtils.calculateLineDistance(
                    new LatLng(mMyLocation.getLatitude(), mMyLocation.getLongitude())
                    , new LatLng(mCustomerAddress.getLatitude(), mCustomerAddress.getLongitude()));
            String distance = I18NHelper.getFormatText("crm.views.CustomerAddressCardView.v1.1285"/* 距您%s */ , CrmStrUtils.calcDistance(lineDistance));
            mDistanceTextView.setText(distance);
            mDistanceTextView.setVisibility(View.VISIBLE);
            mVerticalLineView.setVisibility(View.VISIBLE);
        } else {
            mDistanceTextView.setVisibility(View.GONE);
            mVerticalLineView.setVisibility(View.GONE);
        }

        String address = locationResult.getAddress();
        if (TextUtils.isEmpty(address)) {
            address = I18NHelper.getText("crm.layout.my_location_card.1814")/* 未知位置 */;
        }
        mAddressTextView.setText(address);
    }

    public void showArrow(boolean show)
    {
        if(mArrowView != null)
        {
            if(show)
            {
                mArrowView.setVisibility(View.VISIBLE);
            }else {
                mArrowView.setVisibility(View.GONE);
            }
        }
    }
}
