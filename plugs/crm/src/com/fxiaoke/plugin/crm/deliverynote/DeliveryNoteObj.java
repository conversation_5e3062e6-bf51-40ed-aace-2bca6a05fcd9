/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.deliverynote;

import androidx.annotation.DrawableRes;
import android.text.TextUtils;

import com.fxiaoke.plugin.crm.R;

/**
 * Created by wubb on 2018/1/15.
 * 服务端定义的发货单对象
 */

public class DeliveryNoteObj {
    public static final String NAME="name";//发货单编号
    public static final String SALES_ORDER_ID="sales_order_id";//销售订单编号  关联字段
    public static final String ACCOUNT_ID="account_id";//客户编号
    public static final String DELIVERY_DATE="delivery_date";//发货日期
    public static final String EXPRESS_ORG="express_org";//物流公司
    public static final String EXPRESS_ORDER_ID="express_order_id";//物流单号
    public static final String TOTAL_DELIVERY_MONEY="total_delivery_money";//发货总金额    6.3新增
    public static final String REMARK="remark";//备注
    public static final String RECEIVE_DATE="receive_date";//收货日期    6.3新增
    public static final String RECEIVE_REMARK="receive_remark";//收货备注    6.3新增
    public static final String STATUS="status";//状态
    public static final String DELIVERY_WAREHOUSE_ID="delivery_warehouse_id";//发货仓库
    public static final String DELIVERY_ERP_WAREHOUSE_ID="delivery_erp_warehouse_id";//ERP发货仓库 6.3.4新增
    public static final String SIGNATURE_ATTACHMENT="signature_attachment";//电子签章附件
    public static final String SHIP_TO_ADD="ship_to_add";//收货地址 6.3.4新增
    public static final String CONSIGNEE="consignee";//收货人 652新增
    public static final String CONSIGNEE_PHONE_NUMBER="consignee_phone_number";//收货人电话 652新增
    public static final String DELIVERY_MODE="delivery_mode";//发货方式 7.0新增
    public static final String REAL_STOCK_SUM="real_stock_sum";//实际库存总量  7.3新增

    public static class Status{
        public static final String UN_DELIVERY="un_delivery";//未发货
        public static final String IN_APPROVAL="in_approval";//审核中
        public static final String HAS_DELIVERY="has_delivered";//已发货
        public static final String CHANGING="changing";//变更中
        public static final String INVALID="invalid";//已作废
        public static final String RECEIVED="received";//已收货

        @DrawableRes
        public static int getStatusIconResId(String value){
            if(TextUtils.equals(value,UN_DELIVERY))
            {
                return R.drawable.kuaixiao_visit_status_not_start;//未发货
            }
            else if(TextUtils.equals(value,IN_APPROVAL))
            {
                return R.drawable.kuaixiao_visit_status_going;//审核中
            }
            else if(TextUtils.equals(value,HAS_DELIVERY))
            {
                return R.drawable.kuaixiao_visit_status_finish;//已发货
            }
            else if(TextUtils.equals(value,CHANGING))
            {
                return R.drawable.kuaixiao_visit_status_going;//变更中
            }
            else if(TextUtils.equals(value,INVALID))
            {
                return R.drawable.kuaixiao_visit_status_unavailable;//已作废
            }
            else if(TextUtils.equals(value,RECEIVED))
            {
                return R.drawable.kuaixiao_visit_status_finish;//已收货
            }
            return 0;
        }
    }
}
