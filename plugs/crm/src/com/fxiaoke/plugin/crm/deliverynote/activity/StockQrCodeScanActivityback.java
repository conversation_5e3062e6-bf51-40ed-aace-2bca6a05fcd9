package com.fxiaoke.plugin.crm.deliverynote.activity;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.SurfaceHolder;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.i18n.I18NButton;
import com.facishare.fs.i18n.I18NEditText;
import com.facishare.fs.metadata.beans.Layout;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.ObjectDescribe;
import com.facishare.fs.metadata.list.beans.ListItemArg;
import com.facishare.fs.metadata.list.select_obj.picker.MultiObjectPicker;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.facishare.fs.pluginapi.crm.type.ICrmBizApiName;
import com.facishare.fs.qr.QrCodeScanActivityback;
import com.fxiaoke.fscommon.util.CommonDataContainer;
import com.fxiaoke.fscommon.util.ImmerseLayoutUtil;
import com.fxiaoke.fscommon_res.qrcode.IQrScanProcessor;
import com.fxiaoke.fscommon_res.qrcode.QrCodeScanArgs;
import com.fxiaoke.fscommon_res.qrcode.QrScanProcessorHolder;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.deliverynote.DeliveryNoteProductObj;
import com.fxiaoke.plugin.crm.deliverynote.actions.BaseStockDetailLookupAction;
import com.fxiaoke.plugin.crm.deliverynote.utils.StockUtils;
import com.fxiaoke.plugin.crm.selectsku.SKUUtils;
import com.fxiaoke.plugin.crm.selectsku.selected.SKUSelectedAct;
import com.gyf.immersionbar.ImmersionBar;
import com.lidroid.xutils.util.KeyboardUtils;

import java.util.Collection;
import java.util.List;

public class StockQrCodeScanActivityback extends QrCodeScanActivityback implements IStockQrCodeScanActivity {
    private static final int KEY_REQUEST_CODE_4_SELECTED = 45463;//跳转到已选页面request_code
    public static Intent getStockIntent(Context ctx, QrCodeScanArgs args) {
        Intent intent = new Intent(ctx, StockQrCodeScanActivityback.class);
        CommonDataContainer.getInstance().saveData(EXTRA_ARGS, args);
        try {
            intent.putExtra(EXTRA_ARGS,args);
        } catch (Exception e){
            e.printStackTrace();
        } catch (Error e){
            e.printStackTrace();
        }
        return intent;
    }

    private I18NButton mInputDataBtn,mOkBtn,mConfirmInputBtn;
    private ImageView mFlashLightImage;
    private LinearLayout mInputLayout,mProductsLayout,mProductInfoLayout;
    private I18NEditText mInputEdit;
    private ImageView mProductCar;
    private TextView mCountView;

    private TextView tvTitle,tvUnit,tvSpecs;

    private ObjectDescribe mObjectDescribe;
    private Layout mLayout;
    private MultiObjectPicker mPicker;

    public void updateObjectInfo(ObjectDescribe objectDescribe,Layout layout,MultiObjectPicker picker){
        mObjectDescribe=objectDescribe;
        mLayout=layout;
        mPicker=picker;
    }

    public void updatePicker(MultiObjectPicker picker) {
        mPicker=picker;
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_stock_qr_code_scan_back;
    }

    @Override
    protected void initTitle() {
        super.initTitle();
        mCommonTitleView.setBackgroundColor(Color.TRANSPARENT);
    }

    @Override
    protected void initView( ) {
        super.initView();
        mInputDataBtn=findViewById(R.id.btn_input_data);
        mFlashLightImage=findViewById(R.id.iv_flashlight);
        mLightCtrlView.setVisibility(View.GONE);
        mInputLayout=findViewById(R.id.ll_input);
        mInputEdit=findViewById(R.id.et_input);
        mProductsLayout=findViewById(R.id.ll_products);
        mProductInfoLayout=findViewById(R.id.ll_product_info);

        mProductCar=findViewById(R.id.iv_products_car);
        mOkBtn=findViewById(R.id.btn_ok);
        mConfirmInputBtn=findViewById(R.id.btn_confirm_input);

        mCountView=findViewById(R.id.tv_count);

        tvTitle=findViewById(R.id.tv_product_title);
        tvUnit=findViewById(R.id.tv_unit);
        tvSpecs=findViewById(R.id.tv_specs);

        mFlashLightImage.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mFlashLightImage.setSelected(!mFlashLightImage.isSelected());
                controlLight();
            }
        });

        mInputDataBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mInputLayout.setVisibility(View.VISIBLE);
                mProductsLayout.setVisibility(View.GONE);
                KeyboardUtils.showSoftInput(mInputEdit);
            }
        });
        KeyboardUtils.registerSoftInputChangedListener(this, new KeyboardUtils.OnSoftInputChangedListener() {
            @Override
            public void onSoftInputChanged(int height) {
                if(height>0){
                    mInputLayout.setVisibility(View.VISIBLE);
                    mInputDataBtn.setVisibility(View.GONE);
                    mProductsLayout.setVisibility(View.GONE);
                } else {
                    mInputLayout.setVisibility(View.GONE);
                    mInputDataBtn.setVisibility(View.VISIBLE);
                    mProductsLayout.setVisibility(View.VISIBLE);
                }
            }
        });

        mProductCar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                List<ObjectData> selectedList = mPicker == null ? null : mPicker.getSelectedList();
                List<ListItemArg> args = MetaDataUtils.getListItemArgs(selectedList, mObjectDescribe, mLayout);
                startActivityForResult(SKUSelectedAct.getIntent(StockQrCodeScanActivityback.this, args), KEY_REQUEST_CODE_4_SELECTED);
            }
        });

        mOkBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setResult(BaseStockDetailLookupAction.RESULT_GOTO_BATCH_EDIT_ACT);
                finish();
            }
        });

        mConfirmInputBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onConfirmInputBarCode();
            }
        });

        if(!TextUtils.equals(StockUtils.getScanCodeType(),"1")) {
            mProductCar.setVisibility(View.GONE);
        }
    }

    private void onConfirmInputBarCode(){
        Collection<IQrScanProcessor> list = QrScanProcessorHolder.getInstance().getProcessorList();
        StockScanCodeProcessor stockScanCodeProcessor=null;
        for(IQrScanProcessor processor : list){
            if(processor instanceof StockScanCodeProcessor){
                stockScanCodeProcessor=(StockScanCodeProcessor)processor;
                break;
            }
        }

        if(stockScanCodeProcessor!=null){
            String qrCode=mInputEdit.getText().toString();
            if(TextUtils.isEmpty(qrCode)){
                ToastUtils.show("条形码不能为空");
                return;
            }
            stockScanCodeProcessor.processResult(this,qrCode,null);
            KeyboardUtils.hideSoftInput(mInputEdit);
        }
    }

    public void updateProductInfo(ObjectData objectData, ObjectDescribe objectDescribe){
        String title="";
        String unit__r = "";
        String specs = "";
        if(TextUtils.equals(objectData.getObjectDescribeApiName(), ICrmBizApiName.PRODUCT_API_NAME)){
            title=objectData.getName();
            int unit=objectData.getInt(DeliveryNoteProductObj.UNIT);
            unit__r = StockUtils.getUnitLabel(objectDescribe,unit);
            specs=objectData.getString("product_spec","");
        } else {
            title=objectData.getString(DeliveryNoteProductObj.PRODUCT_ID+"__r","");
            unit__r = objectData.getString(DeliveryNoteProductObj.UNIT+"__r","");
            specs=objectData.getString(DeliveryNoteProductObj.SPECS,"");
        }

        tvTitle.setText(title);
        if(!TextUtils.equals(StockUtils.getScanCodeType(),"1")) {
            String batchOrSerial=objectData.getString(DeliveryNoteProductObj.BATCH_ID+"__r");
            if(TextUtils.isEmpty(batchOrSerial)) {
                if(TextUtils.equals(objectData.getObjectDescribeApiName(),ICrmBizApiName.SERIAL_NUMBER_API_NAME)) {
                    batchOrSerial=objectData.getName();
                } else {
                    batchOrSerial=objectData.getString(DeliveryNoteProductObj.SERIAL_NUMBER_ID+"__r");
                }
            }
            tvUnit.setText(batchOrSerial);
            tvSpecs.setVisibility(View.GONE);
        } else {
            tvUnit.setText("单位 "+ unit__r);
            tvSpecs.setText("规格 "+specs);
            tvSpecs.setVisibility(View.VISIBLE);
        }

        mProductInfoLayout.setVisibility(View.VISIBLE);
    }

    public void showProductInfoAnimate(){
        PropertyValuesHolder translationX =PropertyValuesHolder.ofFloat("translationX",-mProductCar.getLeft()+mProductCar.getWidth()/2-mProductInfoLayout.getWidth()/2);
        PropertyValuesHolder translationY =PropertyValuesHolder.ofFloat("translationY",mProductsLayout.getTop()-mProductsLayout.getHeight()/2-mProductInfoLayout.getTop());
        PropertyValuesHolder scaleX = PropertyValuesHolder.ofFloat("scaleX",0);
        PropertyValuesHolder scaleY = PropertyValuesHolder.ofFloat("scaleY",0);

        ObjectAnimator animator = ObjectAnimator.ofPropertyValuesHolder(mProductInfoLayout,translationX,translationY,scaleX,scaleY);
        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                mProductInfoLayout.setVisibility(View.GONE);

                mCountView.setVisibility(View.VISIBLE);
                mCountView.setText(mPicker.getSelectedCount()+"");

                mProductInfoLayout.setTranslationX(1.0f);
                mProductInfoLayout.setTranslationY(1.0f);
                mProductInfoLayout.setScaleX(1.0f);
                mProductInfoLayout.setScaleY(1.0f);
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animator.setStartDelay(500);
        animator.start();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        resetImmersionBar(R.id.title, Color.BLACK);
    }

    @Override
    protected void onDestroy() {
        try{
            KeyboardUtils.removeLayoutChangeListener(getWindow().getDecorView());
        } catch (Exception e){

        }
        super.onDestroy();
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
        super.surfaceChanged(holder, format, width, height);
        FrameLayout.LayoutParams params = (FrameLayout.LayoutParams)mInputDataBtn.getLayoutParams();
        params.leftMargin=mScannerSurfaceView.getScannerRect().left;
        params.rightMargin=mScannerSurfaceView.getScannerRect().right;
        params.width=mScannerSurfaceView.getScannerRect().width();
    }

    public void resetImmersionBar(int id, int color){
        try{
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                ImmerseLayoutUtil.setImmerseTitleView(this, id);
                getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN|View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            }else{
                ImmersionBar.with(this)
                        .fitsSystemWindows(true)  //使用该属性,必须指定状态栏颜色
                        .statusBarColor(color)
                        .keyboardMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
                        .init();
            }
        }catch (Exception e){
            FCLog.e("zds", "resetImmersionBar exception : " + e.getMessage());
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(requestCode==KEY_REQUEST_CODE_4_SELECTED) {
            if(mPicker==null) return;
            mPicker.pickBatch(SKUUtils.getRemovedDatasFromSelectedList(), false);//移除掉在已选页面移除的数据
            int count = mPicker.getSelectedCount();
            if(count==0){
                mCountView.setVisibility(View.GONE);
            } else {
                mCountView.setText(count+"");
            }
        }
    }
}