package com.fxiaoke.plugin.crm.deliverynote.api.callbacks;

import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.plugin.crm.deliverynote.api.results.BatchGetCanDeliverProductsResult;

public interface OnBatchGetCanDeliveryProductsCallback {
    void onSuccess(BatchGetCanDeliverProductsResult result);
    void onFailed(WebApiFailureType failureType, int httpStatusCode, String error, int errorCode,
                  int enterpriseID);
}
