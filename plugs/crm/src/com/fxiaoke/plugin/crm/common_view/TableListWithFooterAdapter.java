/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.common_view;

import java.util.HashMap;
import java.util.List;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.beans.ObjectDataKeys;
import com.facishare.fs.metadata.beans.Operator;
import com.facishare.fs.metadata.beans.RefTabObject;
import com.facishare.fs.metadata.beans.fields.FilterInfo;
import com.facishare.fs.metadata.detail.AllRelationObjsAct;
import com.facishare.fs.metadata.detail.RelationObjListConfig;
import com.facishare.fs.metadata.detail.fragment.DetailMDTabFrag;
import com.facishare.fs.metadata.list.beans.search_query.SearchQueryInfo;
import com.facishare.fs.metadata.modify.modelviews.table.TableListAdapter;
import com.facishare.fs.metadata.modify.modelviews.table.TableListItemArg;
import com.facishare.fs.modelviews.ModelView;
import com.facishare.fs.modelviews.MultiContext;

import android.text.TextUtils;
import android.view.View;

/**
 * 与TableItemWithFooterMVGroup对应的Adapter
 * Created by jiangh on 2017/11/27.
 */

public class TableListWithFooterAdapter extends TableListAdapter {

    private List<RefTabObject> mRefTabObjects;
    // 存储不同业务类型的数据总数, k = record type
    private HashMap<String, Integer> recordTypeCounter;
    private DetailMDTabFrag.DetailMDFragArg mFragArg;
    private String mMasterApiName;

    public TableListWithFooterAdapter(MultiContext context, int scene, String masterApiName) {
        super(context, scene);
        mMasterApiName = masterApiName;
    }

    public void setRefTabObjects(List<RefTabObject> refTabObjects){
        mRefTabObjects = refTabObjects;
    }

    public void setMDFragArg(DetailMDTabFrag.DetailMDFragArg fragArg){
        mFragArg = fragArg;
    }

    public void setRecordTypeCounter(HashMap<String, Integer> counterMap){
        recordTypeCounter = counterMap;
    }

    @Override
    public ModelView createModelView(MultiContext context, int position, TableListItemArg listItemArg) {
        return new TableItemWithFooterMVGroup(context);
    }

    @Override
    public void updateModelView(ModelView modelView, int position, final TableListItemArg listItemArg) {
        super.updateModelView(modelView, position, listItemArg);
        if (modelView instanceof TableItemWithFooterMVGroup) {
            final TableItemWithFooterMVGroup itemMVGroup = (TableItemWithFooterMVGroup) modelView;
            //是最后一个或者下一个是不同业务类型
            boolean nextIsDifferentType = (position + 1 == getCount() || !TextUtils.equals(getItem(position).recordType,
                    getItem(position + 1).recordType));

            // 展示查看更多的布局
            checkShowMore(itemMVGroup, position, listItemArg, nextIsDifferentType);
            itemMVGroup.showArrow(false);
        }
    }

    private void checkShowMore(TableItemWithFooterMVGroup itemMVGroup, int position, final TableListItemArg listItemArg, boolean nextIsDifferentType){

        if(listItemArg == null || mFragArg == null){
            return;
        }
        if(nextIsDifferentType){
            itemMVGroup.setTotalCountLayoutVisibility(View.VISIBLE);
            final String currentRecordType = getItem(position).recordType;
            if(recordTypeCounter != null && recordTypeCounter.get(currentRecordType) != null){
                int totalCount = recordTypeCounter.get(currentRecordType);
                itemMVGroup.setTotalCount(totalCount);
            }
            String describe = I18NHelper.getFormatText("crm.bizevent.BizAction.v1.1680"/* 查看全部 */ , getItem(position).objectDescribe.getDisplayName());
            itemMVGroup.setDescribeText(describe);
            itemMVGroup.setTotalCountLayoutClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (getContext() == null || listItemArg.objectData == null) {
                        return;
                    }

                    SearchQueryInfo.Builder sQueryInfoBuilder = new SearchQueryInfo.Builder();
                    RelationObjListConfig config = RelationObjListConfig.builder()
                            .lookupRelatedListName(getRelatedListName(listItemArg.objectDescribe.getApiName()))
                            .sourceData(mFragArg.masterObjectData)
                            .setSourceObjApiName(mFragArg.masterObjectDescribe.getApiName())
                            .setTargetObjApiName(listItemArg.objectDescribe.getApiName())
                            .setSearchQueryInfo(sQueryInfoBuilder.filter(
                                    new FilterInfo(ObjectDataKeys.RECORD_TYPE, Operator.EQ, currentRecordType)).build())
                            .build();
                    getMultiContext().startActivity(AllRelationObjsAct.getIntent(getContext(), config));
                }
            });
        }else{
            itemMVGroup.setTotalCountLayoutVisibility(View.GONE);
        }
    }

    private String getRelatedListName(String apiName){
        if(TextUtils.isEmpty(apiName)){
            return null;
        }
        if(mRefTabObjects != null && mRefTabObjects.size() > 0){
            for(RefTabObject refTabObject : mRefTabObjects){
                if(apiName.equals(refTabObject.getApiName())){
                    return refTabObject.getRelatedListName();
                }
            }
        }
        return null;
    }
}