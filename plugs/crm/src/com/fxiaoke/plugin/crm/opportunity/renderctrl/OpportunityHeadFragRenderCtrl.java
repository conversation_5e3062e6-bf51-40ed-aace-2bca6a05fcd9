/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.opportunity.renderctrl;

import java.util.Map;

import com.facishare.fs.metadata.commonviews.lazyrender.LazyRenderTask;
import com.facishare.fs.metadata.detail.viewrenders.HeadFragViewRenderCtrl;
import com.facishare.fs.modelviews.MultiContext;

import androidx.annotation.NonNull;

/**
 * Created by zhouz on 2019/1/23.
 */
public class OpportunityHeadFragRenderCtrl extends HeadFragViewRenderCtrl {
    public OpportunityHeadFragRenderCtrl(
            @NonNull MultiContext multiContext) {
        super(multiContext);
    }

    @NonNull
    @Override
    protected Map<Class<? extends LazyRenderTask>, Integer> getRenderOrderMap() {
        Map<Class<? extends LazyRenderTask>, Integer> orderMap = super.getRenderOrderMap();
        orderMap.put(SaleActionViewRenderTask.class, 32);
        return orderMap;
    }
}
