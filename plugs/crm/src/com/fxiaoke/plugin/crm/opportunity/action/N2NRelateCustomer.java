/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.opportunity.action;

import com.facishare.fs.metadata.actions.RelateDataContext;
import com.facishare.fs.metadata.actions.RelatedChecker;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.Operator;
import com.facishare.fs.metadata.beans.fields.FilterInfo;
import com.facishare.fs.metadata.list.beans.search_query.SearchQueryInfo;
import com.facishare.fs.metadata.list.select_obj.picker.PickObjConfig;
import com.facishare.fs.metadata.modify.backfill.BackFillInfo;
import com.facishare.fs.metadata.modify.backfill.BackFillInfos;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.fxiaoke.plugin.crm.common.MetaFieldKeys;
import com.fxiaoke.plugin.crm.customer.CustomerUtils;

import android.app.Activity;
import androidx.annotation.NonNull;

/**
 * 需要特殊处理客户字段的多对多关系
 * Created by zhouz on 2019-05-10.
 */
public class N2NRelateCustomer extends RelatedChecker {
    public N2NRelateCustomer(Activity activity,
                             RelateDataContext relateDataContext) {
        super(activity, relateDataContext);
    }


    @Override
    protected void appendBackFillData(RelateDataContext relateDataContext, BackFillInfos finalBackFillInfos) {
        super.appendBackFillData(relateDataContext, finalBackFillInfos);
        BackFillInfo fillInfo = CustomerUtils.getAccountIdBackFillInfo(relateDataContext.getObjectData());
        if (fillInfo != null) {
            finalBackFillInfos.getBackFillInfoMap().put(fillInfo.fieldName, fillInfo);
        }
    }

    @Override
    public void handleSelectConfig(RelateDataContext relateDataContext, @NonNull PickObjConfig.Builder builder) {
        super.handleSelectConfig(relateDataContext, builder);
        ObjectData sourceData = relateDataContext.getObjectData();
        if (sourceData != null && !MetaDataUtils.isEmpty(sourceData.get(MetaFieldKeys.Customer.CUSTOMER_ID))) {
            builder.searchQueryParams( new SearchQueryInfo.Builder().filter(new FilterInfo(MetaFieldKeys.Customer.CUSTOMER_ID,
                    Operator.EQ, sourceData.getString(MetaFieldKeys.Customer.CUSTOMER_ID))).build());
        }
    }
}
