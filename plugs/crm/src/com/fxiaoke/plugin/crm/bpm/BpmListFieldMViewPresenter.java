/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.bpm;

import java.text.DecimalFormat;
import java.util.Date;

import com.facishare.fs.common_utils.DateTimeUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.beans.MetaDataParser;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.dataconverter.DefaultContentDataConverter;
import com.facishare.fs.metadata.dataconverter.converter.IFieldContentConverter;
import com.facishare.fs.metadata.dataconverter.converter.IFieldContext;
import com.facishare.fs.metadata.dataconverter.converter.IObjectDataFieldContext;
import com.facishare.fs.metadata.list.modelviews.ListItemFieldArg;
import com.facishare.fs.metadata.list.modelviews.field.IListItemFieldView;
import com.facishare.fs.metadata.list.modelviews.field.presenter.TextListFieldMViewPresenter;
import com.facishare.fs.modelviews.ModelView;
import com.fxiaoke.plugin.crm.order.utils.MDOrderProductUtils;

import android.text.TextUtils;

public class BpmListFieldMViewPresenter extends TextListFieldMViewPresenter {

    @Override
    public boolean accept(ListItemFieldArg listItemFieldArg) {
        return TextUtils.equals(listItemFieldArg.formField.getApiName(), BpmTaskObj.BPM_REMINDLATENCY_API_NAME)
                || TextUtils.equals(listItemFieldArg.formField.getApiName(), BpmTaskObj.BPM_TIMEOUTTIME_API_NAME);
    }

    @Override
    public IFieldContentConverter getContentConverter(ListItemFieldArg arg) {
        return new DefaultContentDataConverter() {
            @Override
            protected String convertValue(Object value, IFieldContext context) {
                if (context instanceof IObjectDataFieldContext) {
                    if (value != null) {
                        String hours = null;
                        if (TextUtils.equals(context.getField().getApiName(), BpmTaskObj
                                .BPM_REMINDLATENCY_API_NAME)) {
                            long time = MetaDataParser.parseLong(value);
                            hours = new DecimalFormat("0.00").format(time / 3600000f) +
                                    I18NHelper.getText("xt.holiday_detail_header.des.hour")/* 小时 */;
                        } else if (TextUtils.equals(context.getField().getApiName(),
                                BpmTaskObj.BPM_TIMEOUTTIME_API_NAME)) {
                            long time = MetaDataParser.parseLong(value);
                            hours = DateTimeUtils.formatRemainingTime(new Date(time));
                        }
                        return hours;
                    }
                }
                return super.convert(value, context);
            }
        };
    }

//    @Override
//    protected void updateFieldView(ModelView modelView, ListItemFieldArg formFieldViewArg) {
//        super.updateFieldView(modelView, formFieldViewArg);
//        if (formFieldViewArg.value != null) {
//            IListItemFieldView fieldView = (IListItemFieldView) modelView;
//
//            String hours = null;
//            if (TextUtils.equals(formFieldViewArg.formField.getApiName(), BpmTaskObj
//                    .BPM_REMINDLATENCY_API_NAME)) {
//                long time = MetaDataParser.parseLong(formFieldViewArg.value);
//                hours = new DecimalFormat("0.00").format(time / 3600000) +
//                        I18NHelper.getText("xt.holiday_detail_header.des.hour")/* 小时 */;
//            } else if (TextUtils.equals(formFieldViewArg.formField.getApiName(),
//                    BpmTaskObj.BPM_TIMEOUTTIME_API_NAME)) {
//                long time = (long) formFieldViewArg.value;
//                hours = DateTimeUtils.formatRemainingTime(new Date(time));
//            }
//            fieldView.updateContent(hours);
//        }
//
//    }
}
