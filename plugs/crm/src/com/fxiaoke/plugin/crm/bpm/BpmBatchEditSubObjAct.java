/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.bpm;

import java.util.HashMap;
import java.util.Map;

import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.modify.AutoClearFocusAct;
import com.facishare.fs.metadata.modify.MetaModifyRootFragment;
import com.fxiaoke.plugin.crm.R;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.fragment.app.FragmentManager;
import android.text.TextUtils;
import android.view.View;

/**
 * Bpm编辑从对象
 */
public class BpmBatchEditSubObjAct extends AutoClearFocusAct implements BpmBatchEditSubContract.View {

    private static final String API_NAME = "apiName";
    private static final String DATA_ID = "dataId";
    private static final String TASK_ID = "taskId";
    private static final String INSTANCE_ID = "instanceId";
    private String mApiName;
    private String mDataId;
    private String mTaskId;
    private String mInstanceId;
    private MetaModifyRootFragment mModifyRootFragment;
    private BpmBatchEditSubPresenter mPresenter;

    public static Intent getIntent(Context context, String apiName, String dataId, String taskId, String instanceId) {
        Intent intent = new Intent(context, BpmBatchEditSubObjAct.class);
        intent.putExtra(API_NAME, apiName);
        intent.putExtra(DATA_ID, dataId);
        intent.putExtra(TASK_ID, taskId);
        intent.putExtra(INSTANCE_ID, instanceId);
        return intent;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_batch_edit_subobj_frag);
        if (!initData(savedInstanceState)) {
            ToastUtils.show(I18NHelper.getText("bi.ui.BiOpportunityListAct.2124")/* 缺少必要参数 */);
            finish();
            return;
        }
        mPresenter = new BpmBatchEditSubPresenter()
                .setMultiContext(mMultiContext)
                .setApiName(mApiName)
                .setDataId(mDataId)
                .setView(this);
        initTitleEx();
        mCommonTitleView.addLeftAction(
                I18NHelper.getText("commonfunc.dialog_fragment_fsmail_choose_attachment_menu.text.cancel")/* 取消 */,
                new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        onBackPressed();
                    }
                });
        mCommonTitleView.addRightAction(I18NHelper.getText("meta.modify.save")/* 保存 */, new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                if (mModifyRootFragment != null) {
                    Map<String, String> otherParams = new HashMap<>();
                    otherParams.put("biz", "workflow_bpm");
                    otherParams.put("bizId", mTaskId);
                    otherParams.put("otherBizId", mInstanceId);
                    mModifyRootFragment.setOtherParams(otherParams).save();
                }
            }
        });
        mPresenter.start();
    }

    /**
     * 初始化数据
     */
    private boolean initData(Bundle bundle) {
        if (bundle != null) {
            mApiName = bundle.getString(API_NAME);
            mDataId = bundle.getString(DATA_ID);
            mTaskId = bundle.getString(TASK_ID);
            mInstanceId = bundle.getString(INSTANCE_ID);
        } else {
            mApiName = getIntent().getStringExtra(API_NAME);
            mDataId = getIntent().getStringExtra(DATA_ID);
            mTaskId = getIntent().getStringExtra(TASK_ID);
            mInstanceId = getIntent().getStringExtra(INSTANCE_ID);
        }
        if (TextUtils.isEmpty(mApiName) && TextUtils.isEmpty(mDataId)) {
            return false;
        }
        return true;
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(API_NAME, mApiName);
        outState.putString(DATA_ID, mDataId);
    }

    @Override
    public void initFragment() {
        FragmentManager fm = getSupportFragmentManager();
        mModifyRootFragment = (MetaModifyRootFragment) fm.findFragmentById(R.id.container);
        if (mModifyRootFragment == null) {
            mModifyRootFragment = MetaModifyRootFragment.newInstance(null, mPresenter.getConfig());
            fm.beginTransaction().add(R.id.container, mModifyRootFragment).commit();
        }
        mModifyRootFragment.setFinishDelegate(mPresenter.getFinishDelegate());
    }

    @Override
    public void updateView() {
        //设置默认选中从对象tab
        mModifyRootFragment.setCurrentItem(1);
        String displayName = mModifyRootFragment.getDisplayName();
        if (!TextUtils.isEmpty(displayName)) {
            mCommonTitleView.setMiddleText(displayName);
        }
    }

    @Override
    public void finishSelf() {
        finish();
    }
}
