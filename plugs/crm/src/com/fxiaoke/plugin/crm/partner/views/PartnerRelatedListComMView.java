/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.partner.views;

import androidx.fragment.app.FragmentActivity;
import android.view.View;

import com.afollestad.materialdialogs.DialogFragmentWrapper;
import com.afollestad.materialdialogs.MaterialDialog;
import com.facishare.fs.metadata.modify.modelviews.componts.RelatedListComMView;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.plugin.crm.bcr.scanner.ScanAddAction;
import com.facishare.fs.metadata.detail.MetaRelatedOperationCtrl;
import com.fxiaoke.plugin.crm.webmenu.WebMenuItem2;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/9/17 15:20
 * @description 合作伙伴相关卡片上的新建处理
 */
public class PartnerRelatedListComMView extends RelatedListComMView {

    public PartnerRelatedListComMView(MultiContext context) {
        super(context);
    }

    @Override
    protected void onAddClick() {

        MetaRelatedOperationCtrl scanOpsCtrl = new MetaRelatedOperationCtrl(){

            @Override
            public void onManualAdd() {
                PartnerRelatedListComMView.super.onAddClick();
            }

            @Override
            public void onScanAdd() {
                mAddRelatedAction.setScanner(new ScanAddAction(getMultiContext()));
                mAddRelatedAction.start(PartnerRelatedListComMView.this);
            }
        };
        //只添加手动新建和扫名片操作
        List<WebMenuItem2> webMenuItems = new ArrayList<>();
        webMenuItems.add(MetaRelatedOperationCtrl.MANUALADD);
        webMenuItems.add(MetaRelatedOperationCtrl.SCANADD);

        final String[] items = new String[webMenuItems.size()];
        for (int i = 0; i < webMenuItems.size(); i++) {
            items[i] = webMenuItems.get(i).getText();
        }

        DialogFragmentWrapper.showList((FragmentActivity) getContext(), items, new MaterialDialog.ListCallback() {
            @Override
            public void onSelection(MaterialDialog dialog, View itemView, int which, CharSequence text) {
                String select = items[which];
                if (select.equals(MetaRelatedOperationCtrl.MANUALADD.getText())) {
                    scanOpsCtrl.onManualAdd();
                } else if (select.equals(MetaRelatedOperationCtrl.SCANADD.getText())) {
                    scanOpsCtrl.onScanAdd();
                }
            }
        });
    }
}
