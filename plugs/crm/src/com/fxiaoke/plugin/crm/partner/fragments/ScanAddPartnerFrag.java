/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.partner.fragments;

import static android.app.Activity.RESULT_CANCELED;
import static android.app.Activity.RESULT_OK;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.modify.MetaModifyConfig;
import com.facishare.fs.metadata.modify.presenter.ModifyBottomActionPresenter;
import com.facishare.fs.pluginapi.contact.beans.LocalContactEntity;
import com.fxiaoke.fscommon_res.common_view.CommonTitleView;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.partner.utils.PartnerUtils;
import com.fxiaoke.plugin.crm.scanmp.ScanMPConstants;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

/**
 * Created by dongmj on 2018/4/3.
 */

public class ScanAddPartnerFrag extends AddOrEditPartnerFrag {

    public static final String KEY_BUNDLE = "scan_bundle";

    private TextView mSaveContinueTV, mSaveTV;
    private Bundle mBundle;
    private LocalContactEntity localContactEntity;
    private ObjectData mObjectData;
    private CommonTitleView mCommonTitleView;
    private boolean mSaveSuccess;
    private String mPartnerType;
    private ObjectData mOrignalData;

    public static ScanAddPartnerFrag newInstance(ModifyMasterFragArg config,Bundle mExtraData) {
        ScanAddPartnerFrag frag = new ScanAddPartnerFrag();
        Bundle bundleFrag = new Bundle();
        bundleFrag.putSerializable(KEY_FRAG_ARG, config);
        bundleFrag.putBundle(KEY_BUNDLE, mExtraData);
        frag.setArguments(bundleFrag);
        return frag;
    }

    /**
     * 处理UI，将表单view插到相应的位置
     * @param inflater
     * @param container
     * @param savedInstanceState
     * @return
     */
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View rootView = LayoutInflater.from(getContext()).inflate(R.layout.layout_scan_add_partner, null, false);
        View fragRootView = super.onCreateView(inflater, container, savedInstanceState);
        LinearLayout addorEidtLayout = (LinearLayout)rootView.findViewById(R.id.layout_add_or_edit_frag);
        addorEidtLayout.removeAllViews();
        addorEidtLayout.addView(fragRootView);
        initRootView(rootView);
        return rootView;
    }

    private void initRootView(View rootView) {
        mSaveContinueTV = (TextView) rootView.findViewById(R.id.save_continue_scan);
        mSaveTV = (TextView) rootView.findViewById(R.id.save);
        mSaveTV.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if(mPresenter != null){
                    mPresenter.setIsContinueScan(false);
                    mPresenter.commit();
                }
            }
        });
        mSaveContinueTV.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                onSaveAndContinueClick();
            }
        });
    }

    private void onSaveAndContinueClick() {
        if(mPresenter != null){
            mPresenter.setIsContinueScan(true);
            mPresenter.commit();
        }
    }

    @Override
    protected void initView(View root) {
        super.initView(root);
        mBottomActionPresenter.showBottomActionBar(false);
    }

    @Override
    protected void initBottomBar() {
        mBottomActionPresenter = new ModifyBottomActionPresenter(mAddOrEditMViewGroup,
                mBottomActionContainer, mBottomActionButton);
        mBottomActionPresenter.showBottomActionBar(false);
        //bottomBar不需要设置联动
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        super.initData(savedInstanceState);
        if (savedInstanceState == null) {
            if (getArguments() != null) {
                mBundle = getArguments().getBundle(KEY_BUNDLE);
            }
        } else {
            mBundle = savedInstanceState.getBundle(KEY_BUNDLE);
        }
        //保留初始进界面后台返回的原始data数据
        if(mFragArg != null && mFragArg.config!= null){
            mOrignalData = mFragArg.config.getObjectData();
        }
        //获得传入的entity扫名片数据，进行回填 并触发相应的计算接口
        if(mBundle != null && mBundle.getSerializable(ScanMPConstants.KEY_SCAN_MP_DATAS) != null){
            localContactEntity = (LocalContactEntity) mBundle.getSerializable(ScanMPConstants.KEY_SCAN_MP_DATAS);
            transferMpDataToMetaData();
        }
    }

    @Override
    public void onSafeSaveInstanceState(@NonNull Bundle outState) {
        super.onSafeSaveInstanceState(outState);
        outState.putBundle(KEY_BUNDLE, mBundle);
    }

    public void setCommonTitleView(CommonTitleView commonTitleView){
        this.mCommonTitleView = commonTitleView;
        initTitleEx();
    }

    public void initTitleEx() {
        if(mCommonTitleView != null){
            mCommonTitleView.setTitle(I18NHelper.getText("crm.activitys.PartnerListActivity.1180")/* 扫名片 */);
            mCommonTitleView.removeAllRightActions();
            mCommonTitleView.removeAllLeftActions();
            mCommonTitleView.addLeftBackAction(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (mSaveSuccess) {
                        getActivity().setResult(RESULT_OK, new Intent());
                    } else {
                        getActivity().setResult(RESULT_CANCELED, new Intent());
                    }
                    getActivity().finish();
                }
            });
        }
    }

    private void transferMpDataToMetaData() {
        if (localContactEntity == null) {
            return;
        }
        mObjectData = PartnerUtils.parseCardDataToPartnerObjectData(localContactEntity);
        resetData();
    }

    /**
     * 重置界面数据
     */
    public void resetData() {
        MetaModifyConfig config = mFragArg.config;
        if (config != null) {
            ObjectData newObjectData = mOrignalData;
            newObjectData.getMap().putAll(mObjectData.getMap());
            config.setMasterObjectData(newObjectData);
        }
    }

    /**
     * 保存成功并继续扫名片
     */
    public void setSaveSuccess() {
        mSaveSuccess = true;
    }

}