/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.partner.contract;

import com.facishare.fs.metadata.BasePresenter;
import com.facishare.fs.metadata.BaseView;
import com.facishare.fs.metadata.beans.ObjectData;

/**
 * Author:  wangrz
 * Date:    2020/1/16 19:26
 * Remarks:
 */
public interface AddOrEditPartnerContract {
    interface View extends BaseView<Presenter> {

        /**
         * 合作伙伴名称查重结果处理
         * @param checkExist 是否存在重复
         */
        void handlePartnerNameCheckResult(boolean checkExist);

    }

    interface Presenter extends BasePresenter {

        void setView(View view);

        /**
         * 设置是否继续扫描
         *
         * @param continueScan
         */
        void setIsContinueScan(boolean continueScan);

        /**
         * 触发提交或保存
         */
        void commit();

        /**
         * 合作伙伴名称查重
         *
         * @param partnerId   编辑时：id
         * @param partnerName 输入的名字
         */
        void checkPartnerNameDuplicate(String partnerId, String partnerName);
    }
}
