/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.stock.activity;

import android.content.Context;
import android.content.Intent;
import androidx.fragment.app.Fragment;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.beans.ObjectDescribe;
import com.facishare.fs.metadata.beans.fields.FilterInfo;
import com.facishare.fs.metadata.list.MetaDataListFrag;
import com.facishare.fs.metadata.list.MetaDataListSearchAct;
import com.facishare.fs.metadata.tick.MetaDataBizOpID;
import com.facishare.fs.metadata.tick.MetaDataBizTick;
import com.fxiaoke.plugin.crm.stock.StockObj;
import com.fxiaoke.plugin.crm.stock.fragments.StockListFrag;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by wubb on 2018/4/3.
 */

public class StockListSearchAct extends MetaDataListSearchAct {
    public static Intent getIntent(Context context, ObjectDescribe objectDescribe) {
        Intent intent = new Intent(context, StockListSearchAct.class);
        intent.putExtra(OBJECT_DESCRIBE, objectDescribe);
        return intent;
    }

    @Override
    protected void onSearchContent(String searchStr) {
        MetaDataBizTick.clListTick(MetaDataBizOpID.Search, getTargetApiName());
        FilterInfo filterInfo = createSearchFilter(StockObj.PRODUCT_ID+".name",searchStr);
        MetaDataListFrag listFrag = (MetaDataListFrag) contentFragment;
        List<FilterInfo> filterInfoList=new ArrayList<>();
        filterInfoList.add(filterInfo);
        listFrag.extendFilter(filterInfoList);
    }

    @Override
    protected String getHintStr() {
        return I18NHelper.getText("crm.activity.StockListAct.1123")/* 搜索产品名称 */;
    }

    @Override
    protected Fragment getContentFragment() {
        return StockListFrag.getInstance(getTargetApiName());
    }
}
