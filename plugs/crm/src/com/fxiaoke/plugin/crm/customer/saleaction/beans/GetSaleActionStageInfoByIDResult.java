package com.fxiaoke.plugin.crm.customer.saleaction.beans;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.fs.metadata.beans.Layout;
import com.facishare.fs.metadata.beans.MetaDataParser;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.ObjectDescribe;
import com.facishare.fs.metadata.beans.components.ComponentKeys;
import com.facishare.fs.metadata.beans.components.ComponentType;
import com.facishare.fs.metadata.beans.fields.Field;
import com.facishare.fs.metadata.beans.formfields.FormFieldKeys;
import com.facishare.fs.metadata.beans.sections.SectionKeys;
import com.facishare.fs.pluginapi.crm.authority.AllAuthData;
import com.facishare.fs.pluginapi.crm.authority.CrmFunctionRightInfo;
import com.facishare.fs.pluginapi.crm.beans.CustomerInfo;
import com.facishare.fs.pluginapi.crm.beans.OpportunityInfo;
import com.facishare.fs.pluginapi.crm.type.ICrmBizApiName;
import com.fxiaoke.plugin.crm.custom_field.beans.UserDefineFieldDataInfo;
import com.fxiaoke.plugin.crm.custom_field.beans.UserDefinedFieldInfo;
import com.fxiaoke.plugin.crm.opportunity.beans.OpportunitySaleActionStageInfo;
import com.fxiaoke.plugin.crm.remind.beans.SaleActionConfirmInfo;
import com.facishare.fs.common_utils.JsonHelper;

import android.text.TextUtils;

public class GetSaleActionStageInfoByIDResult implements Serializable {
    private static final long serialVersionUID = 6424028459595324984L;

    private static final int INVISIBLE = 0;//无权限
    private static final int READ_ONLY = 1;//只读
    private static final int READ_WRITE = 2;//读写

    /**
     * 当前销售阶段
     */
    @JSONField(name = "M1")
    public OpportunitySaleActionStageInfo saleActionStageInfo;
    /**
     * 预设字段结构信息列表
     */
    @JSONField(name = "M2")
    public List<UserDefinedFieldInfo> userDefinedFields;
    /**
     * 预设字段数据信息
     */
    @JSONField(name = "M3")
    public List<UserDefineFieldDataInfo> userDefineFieldDatas;
    /**
     * 关联客户字段结构
     */
    @JSONField(name = "M5")
    public List<UserDefinedFieldInfo> customerFields;
    /**
     * 关联客户字段数据
     */
    @JSONField(name = "M6")
    public List<UserDefineFieldDataInfo> customerFieldDatas;
    /**
     * 销售阶段数据ID
     */
    @JSONField(name = "M7")
    public String saleActionStageUDefID;
    /**    * 	*/
    @JSONField(name = "M8")
    public int leaderID;
    /**    * 	*/
    @JSONField(name = "M9")
    public OpportunityInfo opportunity;
    /**
     * 赢单是否新建订单
     */
    @JSONField(name = "M10")
    public boolean isAllowTradeIfWin;
    /**
     * 销售阶段ID列表（正序排列）
     */
    @JSONField(name = "M11")
    public List<OpportunitySaleActionStageInfo> saleActionStages;
    /**
     * 目标销售阶段
     */
    @JSONField(name = "M12")
    public OpportunitySaleActionStageInfo targetSaleActionStageInfo;
    /**
     * 上级确认信息（包括订单编号，目标阶段ID，目标状态，目标输单原因）
     */
    @JSONField(name = "M13")
    public SaleActionConfirmInfo saleActionConfirmInfo;

    @JSONField(name = "M4")
    public List<CrmFunctionRightInfo> crmFunctionRightInfoDatas;
    /**
     * 关联商机字段结构
     */
    @JSONField(name = "M14")
    public List<UserDefinedFieldInfo> oppoFields;
    /**
     * 关联商机字段数据
     */
    @JSONField(name = "M15")
    public List<UserDefineFieldDataInfo> oppoFieldDatas;
    /**
     * 客户信息
     */
    @JSONField(name = "M16")
    public CustomerInfo customerInfo;

    @JSONField(name = "M21")
    public List<String> customerRelationApiNames;

    @JSONField(name = "M22")
    public List<String> opportunityRelationApiNames;

    /**
     * 权限信息
     */
    @JSONField(serialize = false, deserialize = false)
    public List<AllAuthData> mAuthList;

    @JSONField(serialize = false, deserialize = false)
    public ObjectDescribe customerObjectDescribe;

    @JSONField(serialize = false, deserialize = false)
    public ObjectDescribe opportunityObjectDescribe;

    @JSONField(serialize = false, deserialize = false)
    public ObjectData customerObjectData;

    @JSONField(serialize = false, deserialize = false)
    public ObjectData opportunityObjectData;

    @JSONField(serialize = false, deserialize = false)
    public Layout customerObjectLayout;

    @JSONField(serialize = false, deserialize = false)
    public Layout opportunityObjectLayout;

    public GetSaleActionStageInfoByIDResult() {
    }

    @JSONCreator
    public GetSaleActionStageInfoByIDResult(
            @JSONField(name = "M1") OpportunitySaleActionStageInfo saleActionStageInfo,
            @JSONField(name = "M2") List<UserDefinedFieldInfo> userDefinedFields,
            @JSONField(name = "M3") List<UserDefineFieldDataInfo> userDefineFieldDatas,
            @JSONField(name = "M4") List<CrmFunctionRightInfo> functionRights,
            @JSONField(name = "M5") List<UserDefinedFieldInfo> customerFields,
            @JSONField(name = "M6") List<UserDefineFieldDataInfo> customerFieldDatas,
            @JSONField(name = "M7") String saleActionStageUDefID,
            @JSONField(name = "M8") int leaderID,
            @JSONField(name = "M9") OpportunityInfo opportunity,
            @JSONField(name = "M10") boolean isAllowTradeIfWin,
            @JSONField(name = "M11") List<OpportunitySaleActionStageInfo> saleActionStages,
            @JSONField(name = "M12") OpportunitySaleActionStageInfo targetSaleActionStageInfo,
            @JSONField(name = "M13") SaleActionConfirmInfo saleActionConfirmInfo,
            @JSONField(name = "M14") List<UserDefinedFieldInfo> oppoFields,
            @JSONField(name = "M15") List<UserDefineFieldDataInfo> oppoFieldDatas,
            @JSONField(name = "M16") CustomerInfo customerInfo,
            @JSONField(name = "M17") String customerDescribe,
            @JSONField(name = "M18") String opportunityDescribe,
            @JSONField(name = "M19") String customerData,
            @JSONField(name = "M20") String opportunityData,
            @JSONField(name = "M21") List<String> customerRelationApiNames,
            @JSONField(name = "M22") List<String> opportunityRelationApiNames) {
        this.saleActionStageInfo = saleActionStageInfo;
        this.userDefinedFields = userDefinedFields;
        this.userDefineFieldDatas = userDefineFieldDatas;
        this.crmFunctionRightInfoDatas = functionRights;
        //转为枚举数据
        mAuthList = new ArrayList<>();
        AllAuthData authData;
        if (functionRights != null && functionRights.size() > 0) {
            for (CrmFunctionRightInfo info : functionRights) {
                authData = AllAuthData.transAuthData(info);
                mAuthList.add(authData);
            }
        }
        this.customerFields = customerFields;
        this.customerFieldDatas = customerFieldDatas;
        this.saleActionStageUDefID = saleActionStageUDefID;
        this.leaderID = leaderID;
        this.opportunity = opportunity;
        this.isAllowTradeIfWin = isAllowTradeIfWin;
        this.saleActionStages = saleActionStages;
        this.targetSaleActionStageInfo = targetSaleActionStageInfo;
        this.saleActionConfirmInfo = saleActionConfirmInfo;
        this.oppoFields = oppoFields;
        this.oppoFieldDatas = oppoFieldDatas;
        this.customerInfo = customerInfo;
        this.customerRelationApiNames = customerRelationApiNames;
        this.opportunityRelationApiNames = opportunityRelationApiNames;

        customerObjectDescribe = toObjectDescribe(customerDescribe);
        opportunityObjectDescribe = toObjectDescribe(opportunityDescribe);
        customerObjectData = toObjectData(customerData);
        if (customerObjectData != null){
            if (opportunity != null){
                customerObjectData.setName(opportunity.customerName());//取商机下的客户名称
            }
        }
        opportunityObjectData = toObjectData(opportunityData);
        if (opportunityObjectData != null){
            if (opportunity != null){
                opportunityObjectData.setName(opportunity.mainField());//取商机名称
            }
        }
        customerObjectLayout = buildLayout(ICrmBizApiName.CUSTOMER_ACCOUNT_API_NAME, customerRelationApiNames);
        opportunityObjectLayout = buildLayout(ICrmBizApiName.OPPORTUNITY_API_NAME, opportunityRelationApiNames);
    }

    private ObjectDescribe toObjectDescribe(String describeString) {
        if (!TextUtils.isEmpty(describeString)) {
            JSONObject jsonObject = JSONObject.parseObject(describeString);
            if (jsonObject != null){
                JSONObject resultObj = jsonObject.getJSONObject("result");
                if (resultObj != null){
                    JSONArray descArray = resultObj.getJSONArray("objectDescribeList");
                    if (descArray != null && !descArray.isEmpty()){
                        JSONObject desc = descArray.getJSONObject(0);
                        if (desc != null){
                            return JsonHelper.jsonObjectToJavaObject(desc,ObjectDescribe.class);
//                            return desc.toJavaObject(ObjectDescribe.class);
                        }
                    }
                }
            }
        }
        return null;
    }

    private ObjectData toObjectData(String dataString) {
        if (!TextUtils.isEmpty(dataString)) {
            Map<String, Object> dataMap =
                    JSONObject.parseObject(dataString, new TypeReference<Map<String, Object>>() {
                    });
            if (dataMap != null) {
                return new ObjectData(dataMap);
            }
        }
        return null;
    }

    private Layout buildLayout(String objApiName, List<String> apiNames) {
        if (apiNames == null) {
            apiNames = new ArrayList<>();
        }

        Map<String, Field> fieldMap = null;
        if (TextUtils.equals(objApiName, ICrmBizApiName.CUSTOMER_ACCOUNT_API_NAME)) {
            fieldMap = customerObjectDescribe == null ? null : customerObjectDescribe.getFields();
        } else if (TextUtils.equals(objApiName, ICrmBizApiName.OPPORTUNITY_API_NAME)) {
            fieldMap = opportunityObjectDescribe == null ? null : opportunityObjectDescribe.getFields();
        }
        boolean fieldMapEmpty = fieldMap == null || fieldMap.isEmpty();
        List<Map<String, Object>> formFields = new ArrayList<>();
        Iterator it = apiNames.iterator();
        while (it.hasNext()){
            String apiName = (String) it.next();
            if (TextUtils.isEmpty(apiName)){
                continue;
            }
            Map<String, Object> field = new HashMap<>();
            int permission = READ_WRITE;
            boolean isActive = true;
            if (!fieldMapEmpty) {
                Field f = fieldMap.get(apiName);
                if (f != null) {
                    Object permissionObj = f.get("field_Permission");
                    if (permissionObj != null) {
                        permission = MetaDataParser.parseInt(permissionObj);
                    }
                    isActive = f.isActive();
                }
            }
            if (permission == INVISIBLE || !isActive){
                it.remove();//移除掉不展示的字段
                continue;
            }
            field.put("field_name", apiName);
            field.put(FormFieldKeys.Common.IS_READ_ONLY, permission == READ_ONLY);
            formFields.add(field);
        }

        Map<String, Object> fieldFormMap = new HashMap<>();
        fieldFormMap.put(SectionKeys.Common.FORM_FIELDS, formFields);

        List<Map<String, Object>> formFieldList = new ArrayList<>();
        formFieldList.add(fieldFormMap);

        Map<String, Object> fieldSectionMap = new HashMap<>();
        fieldSectionMap.put(ComponentKeys.Common.FIELD_SECTION, formFieldList);
        fieldSectionMap.put(ComponentKeys.Common.API_NAME, "form_component");
        fieldSectionMap.put(ComponentKeys.Common.TYPE, ComponentType.FORM.key);

        List<Map<String, Object>> componentList = new ArrayList<>();
        componentList.add(fieldSectionMap);

        Layout layout = new Layout();
        layout.setComponentMaps(componentList);
        return layout;
    }

    /**
     * 是否存在字段
     *
     * @return true:存在字段
     */
    public boolean isExistFields(){
        return hasUserDefinedFields() || hasCustomerFields() || hasOpportunityFields();
    }

    /**
     * 是否有阶段反馈字段
     *
     * @return true：有阶段反馈字段
     */
    public boolean hasUserDefinedFields(){
        return userDefinedFields != null && !userDefinedFields.isEmpty();
    }

    /**
     * 是否有客户字段
     * @return true：有客户字段
     */
    public boolean hasCustomerFields(){
        return customerRelationApiNames != null && !customerRelationApiNames.isEmpty()
                && customerObjectDescribe != null && !customerObjectDescribe.getFields().isEmpty();
    }

    /**
     * 是否有商机字段
     * @return true：有商机字段
     */
    public boolean hasOpportunityFields(){
        return opportunityRelationApiNames != null && !opportunityRelationApiNames.isEmpty()
                && opportunityObjectDescribe != null && !opportunityObjectDescribe.getFields().isEmpty();
    }
}
