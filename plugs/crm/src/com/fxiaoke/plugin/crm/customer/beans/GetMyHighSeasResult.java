package com.fxiaoke.plugin.crm.customer.beans;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;

public class GetMyHighSeasResult implements Serializable{
	//
	@JSONField(name="M1")
	public List<HighSeasInfo> mHighSeasList;
 
	public GetMyHighSeasResult(){}
	@JSONCreator
	public GetMyHighSeasResult(
			@JSONField(name="M1")
			List<HighSeasInfo> mHighSeasList) {
		this.mHighSeasList = mHighSeasList;
	}
}
