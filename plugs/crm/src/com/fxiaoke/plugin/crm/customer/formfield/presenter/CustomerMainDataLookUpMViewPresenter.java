/*
 * Copyright (C) 2021 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.customer.formfield.presenter;

import com.facishare.fs.metadata.beans.FormFieldViewArg;
import com.facishare.fs.metadata.modify.modelviews.field.LookUpMView;
import com.facishare.fs.metadata.modify.modelviews.field.presenter.LookUpMViewPresenter;
import com.facishare.fs.modelviews.ModelView;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.plugin.crm.customer.CustomerConstant;
import com.fxiaoke.plugin.crm.customer.CustomerConstants;
import com.fxiaoke.plugin.crm.customer.formfield.CustomerMainDataLookUpMView;
import com.fxiaoke.plugin.crm.deliverynote.DeliveryNoteObj;
import com.fxiaoke.plugin.crm.deliverynote.modelviews.field.SalesOrderLookUpMView;

import android.text.TextUtils;

/**
 * <AUTHOR>
 * @date: 2021/1/12 18:08
 * @description:
 */
public class CustomerMainDataLookUpMViewPresenter extends LookUpMViewPresenter {
    @Override
    protected ModelView createEditView(MultiContext context, FormFieldViewArg formFieldViewArg) {
        return new CustomerMainDataLookUpMView(context);
    }

    @Override
    public boolean accept(FormFieldViewArg arg) {
        return super.accept(arg) &&
                TextUtils.equals(arg.formField.getApiName(), CustomerConstants.API_ACCOUNT_MAIN_DATA_ID);
    }
}
