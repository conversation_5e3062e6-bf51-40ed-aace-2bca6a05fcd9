/*
 * Copyright (C) 2021 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.customer.formfield.presenter;

import com.facishare.fs.metadata.beans.FormFieldViewArg;
import com.facishare.fs.metadata.modify.modelviews.field.PhoneNumberMView;
import com.facishare.fs.metadata.modify.modelviews.field.presenter.PhoneNumberAttributionMViewPresenter;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.plugin.crm.customer.formfield.CustomerFormFieldUtils;

/**
 * <AUTHOR>
 * @date: 2021/1/13 10:50
 * @description:
 */
public class CustomerPhoneNumberAttributionMViewPresenter extends PhoneNumberAttributionMViewPresenter {

    @Override
    protected PhoneNumberMView createPhoneNumberMView(MultiContext context,
                                                      FormFieldViewArg formFieldViewArg,
                                                      boolean isEditView) {
        //支持客户主数据,需要屏蔽手机号归属地查询
        boolean isSupportCustomerMainData = CustomerFormFieldUtils.hasCustomerMainData(formFieldViewArg);
        return createPhoneNumberMView(context, formFieldViewArg, isEditView, !isSupportCustomerMainData);
    }
}
