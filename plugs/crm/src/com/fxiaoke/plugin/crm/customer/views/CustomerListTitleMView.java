/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.customer.views;

import java.util.ArrayList;
import java.util.List;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.list.beans.ListItemArg;
import com.facishare.fs.metadata.list.modelviews.ListItemFieldArg;
import com.facishare.fs.metadata.list.modelviews.ListTitleMView;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.common.ListTitleMViewHandler;
import com.fxiaoke.plugin.crm.customer.CustomerConstants;

import androidx.annotation.NonNull;

/**
 * Created by xiangd on 2018/11/15.
 */
public class CustomerListTitleMView extends ListTitleMView<ListItemArg> {

    public CustomerListTitleMView(@NonNull MultiContext multiContext) {
        super(multiContext);
    }

    @Override
    protected void updateTitleMView(ListItemFieldArg titleFieldArg) {
        if (titleFieldArg != null) {
            titleFieldArg.setContentLeftPrefix(ListTitleMViewHandler.getContentLeftPrefix(getTagViewConfigList(titleFieldArg)));
        }
        super.updateTitleMView(titleFieldArg);
    }

    private List<ListTitleMViewHandler.TagViewConfig> getTagViewConfigList(ListItemFieldArg titleFieldArg) {
        List<ListTitleMViewHandler.TagViewConfig> tagList = new ArrayList<>();
        tagList.add(ListTitleMViewHandler.createTagViewConfig(
                I18NHelper.getText("crm.layout.layout_custom_list_item.1884")/* 即将收回 */,
                getMultiContext().getContext().getResources().getColor(R.color.model_left_icon_back_color),
                CustomerConstants.API_IS_REMIND_RECYCLING,
                titleFieldArg));
        tagList.add(ListTitleMViewHandler.createTagViewConfig(
                I18NHelper.getText("xt.send_schedule.des.repeat")/* 重复 */,
                getMultiContext().getContext().getResources().getColor(R.color.model_left_icon_back_color),
                CustomerConstants.IS_DUPLICATED,
                titleFieldArg));
        return tagList;
    }
}
