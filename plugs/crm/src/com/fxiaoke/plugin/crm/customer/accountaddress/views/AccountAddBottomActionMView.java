/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.customer.accountaddress.views;

import android.content.Context;
import androidx.annotation.NonNull;
import android.view.View;

import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.list.beans.ListItemArg;
import com.facishare.fs.modelviews.ModelView;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.plugin.crm.customer.accountaddress.ButtonOptionList;
import com.fxiaoke.plugin.crm.customer.accountaddress.event.RefreshListEvent;
import com.fxiaoke.plugin.crm.customer.accountaddress.event.RefreshMDFrag;
import com.fxiaoke.plugin.crm.customer.accountaddress.service.AccountAddrFinService;
import com.fxiaoke.plugin.crm.common.MetaFieldKeys;
import com.fxiaoke.plugin.crm.customer.CustomerConstant;
import com.fxiaoke.fscommon.http.WebApiExecutionCallbackWrapper;

import de.greenrobot.event.core.PublisherEvent;

/**
 * <AUTHOR>
 * @date 2019/2/2616:53
 * @description 客户地址-通用列表中底部操作
 */
public class AccountAddBottomActionMView extends AccountBottomActionMView {

    public AccountAddBottomActionMView(@NonNull MultiContext multiContext, int position, ListItemArg listItemArg, ButtonOptionList optionList) {
        super(multiContext, position, listItemArg,optionList);
    }

    @Override
    protected View onCreateView(Context context) {
        View view = super.onCreateView(context);
        mTextfirst.setText(I18NHelper.getText("crm.layout.item_multi_address_select.1936")/* 主地址 */);
        mTextSecond.setText(I18NHelper.getText("crm.layout.item_multi_address_select.1937")/* 默认收货地址 */);
        return view;
    }

    @Override
    public void updateModelView(ModelView modelView, int position, ListItemArg listItemArg) {
        super.updateModelView(modelView, position, listItemArg);
        //主地址
        boolean showMain = getFieldValue(listItemArg, CustomerConstant.AccountAdd.IS_DEFAULT_ADD);
        mCheckFirst.setChecked(showMain);
        //默认收货地址
        boolean showDefult = getFieldValue(listItemArg, CustomerConstant.AccountAdd.IS_SHIP_TO_ADD);
        mCheckSecond.setChecked(showDefult);
    }

    @Override
    protected void onFirstLayoutClicked(ListItemArg listItemArg, final int position) {
        super.onFirstLayoutClicked(listItemArg, position);
        if (listItemArg == null && listItemArg.objectData == null) {
            return;
        }
        showLoading();
        String accountId = listItemArg.objectData.getString(MetaFieldKeys.Customer.CUSTOMER_ID);
        String locationId = listItemArg.objectData.getID();
        AccountAddrFinService.setMain(locationId, accountId, new WebApiExecutionCallbackWrapper<Object>(Object.class,getMultiContext().getContext()) {

            @Override
            public void succeed(Object response) {
                dismissLoading();
                PublisherEvent.post(new RefreshListEvent(RefreshListEvent.TYPE_SET_MAIN,position));
                PublisherEvent.post(new RefreshMDFrag());
                ToastUtils.show(I18NHelper.getText("crm.presenter.AddOrEditMarketingEventPresenter.1279")/* 操作成功 */);
            }

            @Override
            public void failed(String error) {
                super.failed(error);
                dismissLoading();
                ToastUtils.show(error);
            }
        });
    }

    @Override
    protected void onSecondLayoutClicked(ListItemArg listItemArg, final int position) {
        super.onSecondLayoutClicked(listItemArg, position);
        if (listItemArg == null && listItemArg.objectData == null) {
            return;
        }
        showLoading();
        String accountId = listItemArg.objectData.getString(MetaFieldKeys.Customer.CUSTOMER_ID);
        String locationId = listItemArg.objectData.getID();
        AccountAddrFinService.setDefult(locationId, accountId, new WebApiExecutionCallbackWrapper<Object>(Object.class,getMultiContext().getContext()) {

            @Override
            public void succeed(Object response) {
                dismissLoading();
                PublisherEvent.post(new RefreshListEvent(RefreshListEvent.TYPE_SET_DEFULT,position));
                PublisherEvent.post(new RefreshMDFrag());
                ToastUtils.show(I18NHelper.getText("crm.presenter.AddOrEditMarketingEventPresenter.1279")/* 操作成功 */);
            }

            @Override
            public void failed(String error) {
                super.failed(error);
                dismissLoading();
                ToastUtils.show(error);
            }
        });
    }

    @Override
    protected boolean deleteEnable(ListItemArg listItemArg) {
        //是否是主地址，主地址不可删除
        boolean isMainAdd = getFieldValue(listItemArg, CustomerConstant.AccountAdd.IS_DEFAULT_ADD);
        if (isMainAdd) {
            ToastUtils.show(I18NHelper.getText("crm.accountaddress.bottom_action_view.cannot_abolish")/* 主地址不能作废 */);
        }
        return !isMainAdd;
    }
}
