/*
 * Copyright (C) 2021 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.customer.formfield;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

import com.facishare.fs.common_utils.function.Consumer;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.beans.FormFieldViewArg;
import com.facishare.fs.metadata.beans.MetaData;
import com.facishare.fs.metadata.modify.modelviews.field.AbsClickableItemMView;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.facishare.fs.modelviews.ModelView;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.plugin.crm.customer.CustomerConstants;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import io.reactivex.android.schedulers.AndroidSchedulers;

/**
 * author: wangrz
 * date: 2021/6/15 18:03
 * description: 配送商/经销商字段
 */
public class DistributorsNameSelectMView extends AbsClickableItemMView {
    private DistributorsNameSelectMView mDistributorsNameMView;
    private DistributorsNameSelectMView mDealersNameMView;
    private List<String> mSelectedDistributorIds;
    private List<String> mSelectedDealersIds;

    public DistributorsNameSelectMView(MultiContext context, FormFieldViewArg formFieldViewArg) {
        super(context);
        initSelectedData(formFieldViewArg);
    }

    private void initSelectedData(FormFieldViewArg formFieldViewArg) {
        if(formFieldViewArg != null && formFieldViewArg.objectData != null) {
            mSelectedDistributorIds = formFieldViewArg.objectData.getList(CustomerConstants.API_UP_DISTRIBUTORS__C,
                    String.class);
            mSelectedDealersIds = formFieldViewArg.objectData.getList(CustomerConstants.API_UP_DEALERS__C,
                    String.class);
        }
    }

    @Override
    protected View onCreateView(Context context) {
        View view = super.onCreateView(context);
        //如果当前字段是配送商，需要获取经销商字段
        if(isDistributors()) {
            initDealersNameMView();
        }
        //如果当前字段是经销商，需要获取配送商字段
        if(isDealers()){
            initDistributorsNameMView();
        }
        return view;
    }

    /**
     * 当前字段是否是经销商
     * @return
     */
    private boolean isDealers (){
        return getArg() != null && TextUtils.equals(getArg().formField.getFieldName(),
                CustomerConstants.API_UP_DEALERS_NAME__C);
    }
    /**
     * 当前字段是否是配送商
     * @return
     */
    private boolean isDistributors (){
        return getArg() != null && TextUtils.equals(getArg().formField.getFieldName(),
                CustomerConstants.API_UP_DISTRIBUTORS_NAME__C);
    }

    /**
     * 是否存在配送商字段
     * @return
     */
    private boolean hasDistributorsMView (){
        return mDistributorsNameMView != null;
    }
    private void initDealersNameMView(){
        AndroidSchedulers.mainThread().scheduleDirect(new Runnable() {
            @Override
            public void run() {
                try {
                    if (mDealersNameMView == null) {
                        ModelView modelView = getAddOrEditMViewGroup().getFieldModelByFieldName(
                                CustomerConstants.API_UP_DEALERS_NAME__C);
                        if (modelView != null && modelView instanceof DistributorsNameSelectMView) {
                            mDealersNameMView = (DistributorsNameSelectMView) modelView;
                            if(mDealersNameMView.isReadOnly()) {
                                mDealersNameMView.updateContentHint(I18NHelper.getFormatText("crm"
                                                + ".accountobj.edit.dealersNameFieldHint"/*自动计算字段，请先选择配送商*/,
                                        getField().getLabel()));
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void initDistributorsNameMView(){
        AndroidSchedulers.mainThread().scheduleDirect(new Runnable() {
            @Override
            public void run() {
                try {
                    if (mDistributorsNameMView == null) {
                        ModelView modelView = getAddOrEditMViewGroup().getFieldModelByFieldName(
                                CustomerConstants.API_UP_DISTRIBUTORS_NAME__C);
                        if (modelView != null && modelView instanceof DistributorsNameSelectMView) {
                            mDistributorsNameMView = (DistributorsNameSelectMView) modelView;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @Override
    protected void onContentClick() {
        //如果是当前存在配送商，点击配送商或者经销商跳转都是一样的页面：选配送商页面；只存在经销商字段：点击跳转选经销商页面
        CustomerFormFieldUtils.go2SelectDealer(getContext(),
                isDistributors() || hasDistributorsMView()?
                        CustomerConstants.API_UP_DISTRIBUTORS__C : CustomerConstants.API_UP_DEALERS__C,
                mSelectedDistributorIds,
                getObjectData(), new Consumer<List<Map<String, Object>>>() {
                    @Override
                    public void accept(List<Map<String, Object>> maps) {
                        handleSelectedData(maps);
                    }
                });
    }

    private void handleSelectedData(List<Map<String, Object>> list){
        LinkedHashSet<String> selectedDistributorIds = new LinkedHashSet<>();
        LinkedHashSet<String> selectedDealersIds = new LinkedHashSet<>();
        LinkedHashSet<String> selectedDistributorNames = new LinkedHashSet<>();
        LinkedHashSet<String> selectedDealersNames = new LinkedHashSet<>();
        if (list != null && !list.isEmpty()) {
            for (Map<String, Object> item : list) {
                MetaData metaData = new MetaData(item);
                /**
                 * id:配送商id
                 * name:配送商名称
                 * typeid:经销商id
                 * typename:经销商名称
                 */
                String id = metaData.getString("id");
                String name = metaData.getString("name");
                String typeid = metaData.getString("typeid");
                String typename = metaData.getString("typename");
                if (TextUtils.isEmpty(id) || TextUtils.isEmpty(name) || TextUtils.isEmpty(typeid) || TextUtils.isEmpty(typename)) {
                    continue;
                }
                selectedDistributorIds.add(id);
                selectedDistributorNames.add(name);

                selectedDealersIds.add(typeid);
                selectedDealersNames.add(typename);
            }
        }
        if(mSelectedDistributorIds == null){
            this.mSelectedDistributorIds = new ArrayList<>();
        }else {
            this.mSelectedDistributorIds.clear();
        }
        this.mSelectedDistributorIds.addAll(selectedDistributorIds);

        if(mSelectedDealersIds == null){
            this.mSelectedDealersIds = new ArrayList<>();
        }else {
            this.mSelectedDealersIds.clear();
        }
        this.mSelectedDealersIds.addAll(selectedDealersIds);
        //当前字段是经销商
        if(isDealers()){
            updateContent(!selectedDealersNames.isEmpty() ? TextUtils.join(",",
                    selectedDealersNames) : null);
            updateDistributorsMView(selectedDistributorNames);
        }else if(isDistributors()) {
            updateContent(!selectedDistributorNames.isEmpty() ? TextUtils.join(",",
                    selectedDistributorNames) : null);
            updateDealersMView(selectedDealersNames);
        }
    }

    private void updateDistributorsMView(LinkedHashSet<String> names){
        if(mDistributorsNameMView == null) {
            initDistributorsNameMView();
        }
        updateTargetMView(mDistributorsNameMView, names);
    }

    private void updateDealersMView(LinkedHashSet<String> names){
        if(mDealersNameMView == null) {
            initDealersNameMView();
        }
        updateTargetMView(mDealersNameMView, names);
    }

    private void updateTargetMView(DistributorsNameSelectMView view,
                                    LinkedHashSet<String> names){
        if(view != null){
            view.updateContent(names != null && !names.isEmpty() ? TextUtils.join(",",
                    names) : null);
            view.updateSelectedIds(mSelectedDealersIds, mSelectedDistributorIds);
        }
    }

    public void updateSelectedIds(List<String> selectedDealersIds,
                                  List<String> selectedDistributorIds){
        if(this.mSelectedDealersIds == null){
            this.mSelectedDealersIds = new ArrayList<>();
        }else {
            mSelectedDealersIds.clear();
        }
        mSelectedDealersIds.addAll(selectedDealersIds == null ? new ArrayList<>() :
                selectedDealersIds);

        if(this.mSelectedDistributorIds == null){
            this.mSelectedDistributorIds = new ArrayList<>();
        }else {
            mSelectedDistributorIds.clear();
        }
        mSelectedDistributorIds.addAll(selectedDistributorIds == null ? new ArrayList<>() :
                selectedDistributorIds);
    }

    public void copyList(){

    }

    public List<String> getSelectedDistributorIds() {
        return mSelectedDistributorIds;
    }

    public List<String> getSelectedDealersIds() {
        return mSelectedDealersIds;
    }
}
