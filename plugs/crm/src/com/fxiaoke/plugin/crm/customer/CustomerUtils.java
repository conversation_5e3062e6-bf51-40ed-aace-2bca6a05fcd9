package com.fxiaoke.plugin.crm.customer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.beans.Layout;
import com.facishare.fs.metadata.beans.MetaData;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.ObjectDataKeys;
import com.facishare.fs.metadata.beans.Operator;
import com.facishare.fs.metadata.beans.fields.FieldKeys;
import com.facishare.fs.metadata.beans.fields.FilterInfo;
import com.facishare.fs.metadata.list.select_obj.picker.PickObjConfig;
import com.facishare.fs.metadata.modify.backfill.BackFillInfo;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.contact.beans.LocalContactEntity;
import com.facishare.fs.pluginapi.crm.type.ICrmBizApiName;
import com.fxiaoke.cmviews.view.NoContentView;
import com.fxiaoke.plugin.crm.common.MetaFieldKeys;
import com.fxiaoke.plugin.crm.custom_field.CustomFieldUtils;
import com.fxiaoke.plugin.crm.customer.beans.HighSeasInfo;
import com.fxiaoke.plugin.crm.customer.formfield.CustomerFormFieldUtils;
import com.fxiaoke.plugin.crm.enterpriseinfo.EnterpriseInfoConstants;
import com.fxiaoke.plugin.crm.enums.ObjLifeStatus;
import com.fxiaoke.plugin.crm.utils.CrmUtils;

import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;

/**
 * <b>创建时间</b> 2015/9/7
 *
 * <AUTHOR>
 */
public class CustomerUtils {

    public static ObjectData parseCardDataToCustomerObjectData(LocalContactEntity entity) {
        Map<String, Object> objMap = new HashMap<>();
        if (entity != null) {
            objMap.put(CustomFieldUtils.META_DATA_NAME, entity.getCompany());
            objMap.put(CustomFieldUtils.META_DATA_ADDRESS, entity.getAddress());
            objMap.put(CustomFieldUtils.META_DATA_TEL, TextUtils.join(CustomFieldUtils.TEL_PART, getCardPhoneList(entity)));
            objMap.put(CustomFieldUtils.META_DATA_URL, entity.getWebSite());
            objMap.put(CustomFieldUtils.META_DATA_FAX, !TextUtils.isEmpty(entity.getWorkFax()) ? entity.getWorkFax() : entity.getHomeFax());
        }
        return new ObjectData(objMap);
    }

    @NonNull
    public static List<String> getCardPhoneList(LocalContactEntity entity) {
        String homeTel = entity.getHomePhone();
        String workTel = entity.getWorKPhone();
        List<String> tel = new ArrayList<>();
        if (!TextUtils.isEmpty(entity.getCompanyPhone())) {
            tel.add(entity.getCompanyPhone());
        }
        if (!TextUtils.isEmpty(workTel)) {
            tel.add(workTel);
        }
        if (!TextUtils.isEmpty(homeTel)) {
            tel.add(homeTel);
        }
        if (!TextUtils.isEmpty(entity.getCallbackPhone())) {
            tel.add(entity.getCallbackPhone());
        }
        if (!TextUtils.isEmpty(entity.getCompanyMainPhone())) {
            tel.add(entity.getCompanyMainPhone());
        }
        if (!TextUtils.isEmpty(entity.getCarPhone())) {
            tel.add(entity.getCarPhone());
        }
        if (tel.size() > 5) {
            for (int i = 5; i < tel.size(); ) {
                tel.remove(i);
            }
        }
        return tel;
    }

    /**
     * 从ObjectData解析出客户名称对应的BackFillInfo回填数据对象
     *
     * @param objectData
     * @return
     */
    public static BackFillInfo getAccountIdBackFillInfo(ObjectData objectData) {
        if (objectData == null) {
            return null;
        }

        String accountId = objectData.getString(MetaFieldKeys.Customer.CUSTOMER_ID);
        String accountName = objectData.getString(MetaFieldKeys.Customer.CUSTOMER_NAME);
        if (!TextUtils.isEmpty(accountId) && !TextUtils.isEmpty(accountName)) {
            return new BackFillInfo(MetaFieldKeys.Customer.CUSTOMER_ID, accountId, accountName, false);
        }
        return null;
    }

    /**
     * 判断地址信息格式是合法
     */
    public static boolean hasValidAddress(String address) {
        if (TextUtils.isEmpty(address)) {
            return false;
        }
        // 完整的格式为： 12.45#%$5.34#%$北京市海淀区知春路卫星大厦
        String[] result = address.split(CrmUtils.LAT_LNG_DIVIDER2);
        if (result.length < 2
                || TextUtils.equals(result[0], "null")
                || TextUtils.equals(result[1], "null")) {
            return false;
        }
        return true;
    }

    /**
     * 设置客户报备人Id
     * @param objectData
     * @param checkerId
     */
    public static void setFillingCheckerId(ObjectData objectData, String checkerId){
        int id = 0;
        try {
            id = TextUtils.isEmpty(checkerId) ? 0 : Integer.parseInt(checkerId);
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if (id > 0) {
                List<String> checkerIds = new ArrayList<>(1);
                checkerIds.add(checkerId);
                objectData.put(CustomerConstants.API_FILING_CHECKER, checkerIds);
            }
        }
    }

    /**
     * 设置电话归属地
     */
    public static void setFillingPhoneNumberAttributionResult(ObjectData materData,
                                                              ObjectData filedData){
        if(materData == null || filedData == null || filedData.getMap().isEmpty()){
            return;
        }
        materData.putAll(filedData.getMap());
    }

    /**
     * 设置电话归属地
     */
    public static void setFillingEnterpriseInfoDataId(ObjectData materData,
                                                      String enterpriseInfoDataId){
        if(materData == null){
            return;
        }
        materData.put(EnterpriseInfoConstants.API_ENTERPRISE_INFO, enterpriseInfoDataId);
    }

    /**
     * 转型公海数据到元数据格式
     * @param highSeasList
     * @return
     */
    public static List<ObjectData> transforToDataObjectList(List<HighSeasInfo> highSeasList) {
        List<ObjectData> result = new ArrayList<>();
        if(highSeasList != null && !highSeasList.isEmpty()){
            for (HighSeasInfo info: highSeasList) {
                ObjectData objectData = new ObjectData();
                objectData.put(ObjectDataKeys.ID, info.mHighSeasID);
                objectData.put(ObjectDataKeys.NAME, info.mName);
                objectData.put(MetaFieldKeys.Pool.KEY_ACCOUNTS_COUNT, info.mCustomerCounts);
                objectData.put(MetaFieldKeys.Pool.KEY_UNALLOCATED_ACCOUNTS_COUNT, info.mUnAllocatedCustomerCount);
                objectData.put(MetaFieldKeys.Pool.KEY_ALLOW_MEMBER_VIEW_LOG, info.mAllowMemberViewLog);
                objectData.put(MetaFieldKeys.Pool.KEY_ROLE_TYPE, info.mRoleType);
                result.add(objectData);
            }
        }
        return result;
    }

    /**
     * 处理地图模式下筛选无数据 view
     * @param noContentView
     * @param listener
     */
    public static void handleNoContentViewOfMapMode(NoContentView noContentView, View.OnClickListener listener){
        if(noContentView == null){
            return;
        }
        noContentView.setText(I18NHelper.getText("xt.notice_search_activity.text.no_match_result")/* 无匹配结果 */);
        noContentView.initBtn(I18NHelper.getText("crm.contact.ContactsFragment.1630")/* 重置筛选项 */,listener);
        LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) noContentView.getImageView().getLayoutParams();
        params.height = FSScreen.dip2px(120);
        params.topMargin = 0;
        params.bottomMargin = FSScreen.dip2px(20);
        noContentView.setButtonMarginTop(32);
    }

    /**
     * 获取客户主数据特定筛选条件，使用场景：新建客户选择 lookUp 客户主数据
     * @return
     */
    public static FilterInfo getSelectCustomerMainDataListFilter() {
        //未生效、审核中、已作废
        String[] filters = new String[] {
                ObjLifeStatus.INEFFECTIVE.key,
                ObjLifeStatus.UNDER_REVIEW.key,
                ObjLifeStatus.INVALID.key};
        return new FilterInfo("life_status", Operator.NHASANYOF,
                Arrays.asList(filters));
    }

    public static boolean isSupportCustomerMainData(Layout layout) {
        if (layout != null) {
            return CustomerFormFieldUtils.hasField(MetaDataUtils.getAllFormFieldMapFromLayout(layout),
                    CustomerConstants.API_ACCOUNT_MAIN_DATA_ID);
        }
        return false;
    }

    /**
     * 处理编辑客户新建客户地址从对象：选择联系人和新建联系人需要筛选客户下的联系人
     * @param pickObjConfigBuilder
     * @param customerData
     */
    public static void handlePickContactConfigBeforeGo2SelectObjOfAccountAddrDetail(PickObjConfig.Builder pickObjConfigBuilder,
                                                                                    ObjectData customerData){
        if (pickObjConfigBuilder != null && customerData != null
                && TextUtils.equals(customerData.getObjectDescribeApiName(), ICrmBizApiName.ACCOUNT_API_NAME)
                && !TextUtils.isEmpty(customerData.getID())) {
            //设置筛选当前客户下的联系人
            pickObjConfigBuilder.addFilter(new FilterInfo(MetaFieldKeys.AccountAddObj.ACCOUNT_ID,
                    Operator.EQ, customerData.getID()));
            //设置列表新建时回填客户字段
            pickObjConfigBuilder.add(new BackFillInfo(MetaFieldKeys.AccountAddObj.ACCOUNT_ID,
                    customerData.getID(),
                    customerData.getName(), false));
            //当前 associatedObjectData (客户地址数据)里面确保需要有 account_id 数据（平台配置的 where 条件里面有）
            ObjectData associatedObjectData = pickObjConfigBuilder.build().getAssociatedObjectData();
            if (associatedObjectData != null && TextUtils
                    .isEmpty(associatedObjectData.getString(MetaFieldKeys.AccountAddObj.ACCOUNT_ID))) {
                associatedObjectData.put(MetaFieldKeys.AccountAddObj.ACCOUNT_ID, customerData.getID());
            }
            pickObjConfigBuilder.associatedObjectData(associatedObjectData);
        }
    }


    public static void preprocessCopyAccountAddrDetailData(ObjectData objectData){
        if (objectData != null) {
            //将赋值过来的数据:是否为主地址字段值为 true 的修改为 false
            if (objectData.getBoolean(CustomerConstant.AccountAdd.IS_DEFAULT_ADD)) {
                objectData.put(CustomerConstant.AccountAdd.IS_DEFAULT_ADD, false);
            }
            //将赋值过来的数据:是否为默认地址字段值为 true 的修改为 false
            if (objectData.getBoolean(CustomerConstant.AccountAdd.IS_SHIP_TO_ADD)) {
                objectData.put(CustomerConstant.AccountAdd.IS_SHIP_TO_ADD, false);
            }
        }
    }

    public static void preprocessCopyAccountFinInfoDetailData(ObjectData objectData){
        if (objectData != null) {
            //将赋值过来的数据:是否为默认字段值为 true 的修改为 false
            if (objectData.getBoolean(CustomerConstant.AccountFin.IS_DEFAULT)) {
                objectData.put(CustomerConstant.AccountFin.IS_DEFAULT, false);
            }
        }
    }

    public static String[] getAreaFiledKeys() {
        return new String[] {FieldKeys.CASCADE_REGION.COUNTRY,
                FieldKeys.CASCADE_REGION.PROVINCE,
                FieldKeys.CASCADE_REGION.CITY,
                FieldKeys.CASCADE_REGION.DISTRICT,
                "town"};
    }

    /**
     * 处理客户国家省市区乡镇选择后的数据:底层只往data 更新了 code, 没有 name(__r)
     * @param nameStr
     * @param objectData
     */
    public static void updateAreaName(String nameStr, ObjectData objectData) {
        if (!TextUtils.isEmpty(nameStr)) {
            String[] names = nameStr.split("/");
            String[] fileds = getAreaFiledKeys();
            if (objectData != null) {
                for (int i = 0; i < names.length; i++) {
                    String name = names[i];
                    if (i < fileds.length) {
                        objectData.put(fileds[i] + "__r", name);
                    }
                }
            }
        }
    }

    /**
     * 更新当前表单地区字段值
     * @param currentFormData 当前的表单数据
     * @param sourceData 联动的表单数据
     */
    public static void updateAreaData(ObjectData currentFormData, ObjectData sourceData) {
        if (sourceData == null || currentFormData == null) {
            return;
        }
        String[] fileds = getAreaFiledKeys();
        currentFormData.put(CustomFieldUtils.META_DATA_LOCATION,
                sourceData.getString(CustomFieldUtils.META_DATA_LOCATION));
        currentFormData.put(CustomFieldUtils.META_DATA_ADDRESS,
                sourceData.getString(CustomFieldUtils.META_DATA_ADDRESS));
        for (String filed : fileds) {
            currentFormData.put(filed, sourceData.getString(filed));
            String nameKey = filed + "__r";
            currentFormData.put(nameKey, sourceData.getString(nameKey));
        }
    }

    /**
     * 获取客户列表 limit
     * @return
     */
    public static MetaData getCustomerListLimitConfig() {
        Map<String, Object> configMap = HostInterfaceManager.getCloudCtrlManager().getObjConfig("crm.accountobj.list.limitConfig", Map.class);
        MetaData configData = new MetaData(configMap);
        return configData;
    }
}
