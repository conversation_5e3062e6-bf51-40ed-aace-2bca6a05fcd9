/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.leads.leadstransfer.fragment;

import java.util.List;

import org.jetbrains.annotations.NotNull;

import com.facishare.fs.metadata.ILoadingView;
import com.facishare.fs.metadata.actions.item_choice.OnFieldSelectedCallback;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.fields.Option;
import com.facishare.fs.metadata.beans.formfields.SelectOneFormField;
import com.facishare.fs.metadata.list.beans.StageResult;
import com.facishare.fs.metadata.modify.AddOrEditProvider;
import com.facishare.fs.metadata.modify.MetaModifyConfig;
import com.facishare.fs.metadata.modify.Scene;
import com.facishare.fs.metadata.modify.modelviews.AddOrEditMViewGroup;
import com.facishare.fs.metadata.modify.modelviews.field.EditTextMView;
import com.facishare.fs.metadata.modify.modelviews.field.LookUpMView;
import com.facishare.fs.metadata.modify.modelviews.field.SelectOneMView;
import com.facishare.fs.modelviews.ModelView;
import com.facishare.fs.pluginapi.crm.type.CoreObjType;
import com.fxiaoke.cmviews.view.TopNoticeView;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.common.BaseAddOrEditObjFrag;
import com.fxiaoke.plugin.crm.common.MetaFieldKeys;
import com.fxiaoke.plugin.crm.leads.leadstransfer.LeadsTransferConstants;
import com.fxiaoke.plugin.crm.leads.leadstransfer.activity.LeadsTransferTabAct;
import com.fxiaoke.plugin.crm.leads.leadstransfer.beans.OnNewOpportunityMasterFragListener;
import com.fxiaoke.plugin.crm.leads.leadstransfer.contract.LeadsToNewOpportContract;
import com.fxiaoke.plugin.crm.leads.leadstransfer.events.LeadsToCustomerUpdateEvent;
import com.fxiaoke.plugin.crm.leads.leadstransfer.presenter.LeadsToNewOpportPresenter;
import com.fxiaoke.plugin.crm.leads.leadstransfer.view.Leads2NewOpportAddOrEditMViewGroup;
import com.fxiaoke.plugin.crm.newopportunity.NewOpportunityConstant;
import com.fxiaoke.plugin.crm.newopportunity.detail.SalesProcessMView;
import com.fxiaoke.plugin.crm.newopportunity.util.NewOpportunityUtils;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import androidx.annotation.NonNull;
import de.greenrobot.event.core.ISubscriber;
import de.greenrobot.event.core.MainSubscriber;

public class LeadsToNewOpportunityFrag extends BaseAddOrEditObjFrag<LeadsToNewOpportContract.Presenter> implements LeadsToNewOpportContract.View,
        OnNewOpportunityMasterFragListener {
    private TopNoticeView mNoticeView;
    private SelectOneMView mSalesProcessMView;//销售流程
    private EditTextMView mProbabilityMView;//赢率
    private SelectOneMView mSalesStageMView;//商机所在阶段
    protected boolean mIsPreview;
    private LookUpMView mCustomerLookUpMView;
    protected String mCustomerId;
    protected String mCustomerName;
    private boolean mIsCreateCustomerMode;//是否是新建的客户类型

    public static LeadsToNewOpportunityFrag newInstance(ObjectData objectData,
                                                        String recordType,
                                                        boolean isPreview) {
        LeadsToNewOpportunityFrag frag = new LeadsToNewOpportunityFrag();
        Bundle bundle = new Bundle();
        MetaModifyConfig config = MetaModifyConfig.builder()
                .setApiName(CoreObjType.NewOpportunity.apiName)
                .setObjectData(objectData)
                .setRecordTypeId(recordType)
                .setToDetailAct(false)
                .setEditType(false)
                .build();
        bundle.putSerializable(MODIFY_CONFIG, config);
        bundle.putBoolean(LeadsTransferConstants.IS_PREVIEW, isPreview);
        frag.setArguments(bundle);
        return frag;
    }

    @Override
    protected LeadsToNewOpportContract.Presenter createAddOrEditPresenter() {
        return new LeadsToNewOpportPresenter(this, mConfig);
    }

    @Override
    public void onSafeSaveInstanceState(@NonNull Bundle outState) {
        super.onSafeSaveInstanceState(outState);
        outState.putSerializable(LeadsTransferConstants.IS_PREVIEW, mIsPreview);
    }

    @Override
    protected List<ISubscriber> onGetEvents() {
        FCLog.d(TAG, "onGetEvents ");
        List<ISubscriber> subscriberList = super.onGetEvents();
        subscriberList.add(new MainSubscriber<LeadsToCustomerUpdateEvent>() {
            @Override
            public void onEventMainThread(LeadsToCustomerUpdateEvent event) {
                handleCustomerBackFill(event.customerData);
            }
        });
        return subscriberList;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        super.initData(savedInstanceState);
        if (savedInstanceState == null) {
            if (getArguments() != null) {
                mIsPreview = getArguments().getBoolean(LeadsTransferConstants.IS_PREVIEW);
            }
        } else {
            mIsPreview = savedInstanceState.getBoolean(LeadsTransferConstants.IS_PREVIEW);
        }
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fragment_leads_to_opportunity;
    }

    @Override
    protected void initView(View root) {
        super.initView(root);
        mNoticeView = root.findViewById(R.id.notice_view);
        mNoticeView.setStrengthen(true);
        mNoticeView.setTip(LeadsTransferConstants.TIP_NEW_OPPORTUNITY_EXIST);

        mAddOrEditMViewGroup.setShowNotRequired(true);
        if(!mIsPreview) {
            mBottomActionPresenter.updateActionText(true);
        }
    }

    @NotNull
    @Override
    protected AddOrEditMViewGroup createAddOrEditMViewGroup() {
        return new Leads2NewOpportAddOrEditMViewGroup(mMultiContext);
    }

    @Override
    protected void initBottomBar() {
        //预览模式不展示
        if(mIsPreview) {
            mBottomActionContainer.setVisibility(View.GONE);
            return;
        }
        super.initBottomBar();
    }
    @Override
    protected int getScene() {
        if(mIsPreview){
            return Scene.INFO;
        }
        return super.getScene();
    }

    @Override
    protected void resetFields() {
        if (mObjectDescribe != null && mObjectDescribe.getFieldMaps() != null) {
            //展示模式需要展示价目表字段，不区分是否支持商机2.0明细（因为支持商机2.0明细可支持选择价目表）
            if (!mIsPreview) {
                mObjectDescribe.getFieldMaps().remove(NewOpportunityConstant.PRICE_BOOK_ID);
            }
            mObjectDescribe.getFieldMaps().remove(MetaFieldKeys.Leads.LEADS_ID);
        }
    }

    @Override
    protected void dealSpecialModelViews() {
        super.dealSpecialModelViews();
        handleOpportunityInfo();
        handleCustomerNameView();
    }

    /**
     * 处理销售流程、商机所在阶段、赢率字段
     * 商机所在阶段：新建时去销售流程中第一个阶段；编辑时，取当前所在阶段
     * 赢率：新建、编辑时，取商机所在阶段的赢率
     */
    private void handleOpportunityInfo() {
        mProbabilityMView = NewOpportunityUtils.getEditModel(NewOpportunityConstant.PROBABILITY, mAddOrEditMViewGroup);
        mSalesProcessMView =
                NewOpportunityUtils.getSelectOneModel(NewOpportunityConstant.SALES_PROCESS, mAddOrEditMViewGroup);
        mSalesStageMView =
                NewOpportunityUtils.getSelectOneModel(NewOpportunityConstant.SALES_STAGE, mAddOrEditMViewGroup);
        if (mSalesProcessMView != null) {
            mSalesProcessMView.setReadOnly(mIsPreview);
            mSalesProcessMView.setOnFieldSelectedCallback(new OnFieldSelectedCallback<Option>() {
                @Override
                public void onFieldSelected(List<Option> selectedList) {
                    if (selectedList == null || selectedList.isEmpty() || selectedList.get(0) == null) {
                        NewOpportunityUtils.updateProbabilityAndSalesStageInfo(mProbabilityMView, null,
                                mSalesStageMView, null);
                    } else {
                        mPresenter.getSalesStages(selectedList.get(0).getValue());
                    }
                }
            });
        }
    }

    @Override
    protected void handleCustomerNameModelBizAfterRendered(ModelView modelView) {
        super.handleCustomerNameModelBizAfterRendered(modelView);
        if (modelView instanceof EditTextMView) {
            //商机2.0名称
            EditTextMView model = (EditTextMView) modelView;
            model.addContentOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View view, boolean hasFocus) {
                    if (!hasFocus) {
                        // 失去焦点之后进行查重操作
                        mPresenter.checkNewOpportunityExist(model.getResult());
                    } else {
                        showNoticeView(false);
                    }
                }
            });
        }
    }

    private void handleCustomerNameView(){
        ModelView modelView =
                mAddOrEditMViewGroup.getFieldModelByFieldName(MetaFieldKeys.Customer.CUSTOMER_ID);
        if(modelView instanceof LookUpMView){
            mCustomerLookUpMView = (LookUpMView) modelView;
            mCustomerLookUpMView.setReadOnly(true);
        }
    }
    @Override
    public void updateSalesStages(StageResult response) {
        if (response == null
                || response.getStages() == null
                || response.getStages().isEmpty()
                || response.getStages().get(0) == null) {
            NewOpportunityUtils.updateProbabilityAndSalesStageInfo(mProbabilityMView, null, mSalesStageMView, null);
        } else {
            StageResult.SimpleStage simpleStage = response.getStages().get(0);
            SelectOneFormField selectOneField = mSalesStageMView == null ? null : mSalesStageMView.getFormField();
            List<Option> options = NewOpportunityUtils.getSalesStageOptions(response.getStages(), selectOneField);
            NewOpportunityUtils.updateProbabilityAndSalesStageInfo(mProbabilityMView, simpleStage.getWinRate(),
                    mSalesStageMView, options);
        }
    }

    @Override
    public void onCheckNewOpportunityResult(boolean repeat) {
        showNoticeView(repeat);
    }

    private void showNoticeView(boolean show) {
        mNoticeView.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    public void checkAndPrepareData() {
        if (mAddOrEditProvider == null) {
            return;
        }
        mAddOrEditProvider.prepareData(mAddOrEditMViewGroup, (ILoadingView) mMultiContext.getContext(),
                new AddOrEditProvider.DataPrepareCallback() {
                    @Override
                    public void dataPrepared(ObjectData objectData) {
                        mObjectData = objectData;
                        ((LeadsTransferTabAct)mActivity).onDataPrepareSuccess();
                    }
                });
    }

    public ObjectData getOpportunityObjectData() {
        return mObjectData;
    }

    protected void handleCustomerBackFill(ObjectData customerData) {
        if(customerData == null){
            customerData = new ObjectData();
        }
        if(mCustomerLookUpMView != null) {
            if(TextUtils.isEmpty(customerData.getID())){
                customerData.setId(LeadsTransferConstants.INVALID_CUSTOMER_ID);

            }
            if(TextUtils.equals(customerData.getID(), LeadsTransferConstants.INVALID_CUSTOMER_ID)){
                //由于线索转换新建的客户还不存在，只更新商机2.0表单中的客户姓名
                if(mCustomerLookUpMView != null){
                    mCustomerLookUpMView.setContentText(customerData.getName());
                }
            }

            //如果编辑客户名称失焦触发，不执行以下操作，只有切换转换客户模式或者更换已选客户时候触发
            if(!customerData.getBoolean(LeadsTransferConstants.LEADS_2_CREATE_CUSTOMER_NAME_CHANGED)) {
                mIsCreateCustomerMode = customerData.getBoolean(LeadsTransferConstants.LEADS_2_CUSTOMER_CREATE_MODE);
                mCustomerId = mCustomerLookUpMView.getCurDataID();
                mCustomerName = mCustomerLookUpMView.getCurDataName();
                mCustomerLookUpMView.updateValueAndNotifyChild(customerData);
            }
        }
    }

    @Override
    public String getCustomerId() {
        return mCustomerId;
    }

    @Override
    public boolean isCreateCustomerMode() {
        return mIsCreateCustomerMode;
    }

    public boolean isRenderEndOfNewOpportunity() {
        return mPresenter instanceof LeadsToNewOpportPresenter && ((LeadsToNewOpportPresenter)mPresenter).isRenderEndOfNewOpportunity();
    }

    @Override
    public void onRenderEnd() {
        if (mSalesProcessMView instanceof SalesProcessMView) {
            ((SalesProcessMView) mSalesProcessMView).getSalesProcessList();
        }
    }
}
