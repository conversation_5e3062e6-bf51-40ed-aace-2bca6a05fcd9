/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.leads;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.billy.cc.core.component.CC;
import com.billy.cc.core.component.CCResult;
import com.billy.cc.core.component.CCUtil;
import com.facishare.fs.common_utils.StringUtils;
import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.actions.item_choice.SelectFieldHelper;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.ObjectDescribe;
import com.facishare.fs.metadata.beans.fields.Field;
import com.facishare.fs.metadata.beans.fields.Option;
import com.facishare.fs.metadata.beans.fields.SelectOneField;
import com.facishare.fs.metadata.modify.MetaModifyUtil;
import com.facishare.fs.metadata.utils.ModelViewUtils;
import com.facishare.fs.pluginapi.crm.controller.objfield.SelectObjFieldConfig;
import com.facishare.fs.metadata.beans.fields.IObjFieldInfo;
import com.fxiaoke.fscommon.util.CommonDataContainer;
import com.fxiaoke.plugin.crm.BaseActivity;
import com.fxiaoke.plugin.crm.R;
import com.fxiaoke.plugin.crm.common_view.model_views.concrete_views.customfieldviews.ClickModel3;
import com.fxiaoke.plugin.crm.common_view.model_views.concrete_views.customfieldviews.EditTextModel3;
import com.fxiaoke.plugin.crm.leads.contract.LeadsHandleContract;
import com.fxiaoke.plugin.crm.leads.presenter.LeadsHandlePresenter;
import com.fxiaoke.plugin.crm.selectfield.select.SelectObjFieldActivity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewStub;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.TextView;

/**
 * 线索编辑处理结果页面
 * Created by lixn on 2016/6/24.
 * 备注：线索与跟进中使用的是本页面，处理结果作为销售记录发送开关，统一修改为不做本地记录，默认关闭
 * 710按钮操作标准化后，该页面旧版本使用
 */
@Deprecated
public class LeadsHandleResultAct extends BaseActivity implements LeadsHandleContract.View{
    private LeadsHandleContract.Presenter mPresenter;

    public enum LeadsHandleType{
        /**
         * 线索无效
         */
        LEAD_CLOSE,
        /**
         * 线索跟进
         */
        LEADS_FOLLOWUP
    }
    public static final int REQUEST_CODE_SELECT_CLOSE_REASON = 1001;
    public static final String KEY_OBJECTDATA = "key_objectData";
    public static final String KEY_OBJECTDESCRIBE = "key_objectDescribe";
    public static final String KEY_OBJECTNAME = "key_objectName";
    public static final String KEY_OBJECTID = "key_objectId";
    public static final String KEY_LAST_HANDLE_RESULT = "key_last_handle_result";
    public static final String KEY_HANDLE_IS_SEND = "key_handle_is_send";
    public static final String KEY_LEADS_HANDLE_TYPE = "key_leads_handle_type";//是否是线索无效操作
    public static final String KEY_CLOSE_REASON = "key_close_reason";//线索无效原因
    public static final String KEY_CLOSE_REASON_INFO = "key_close_reason_info";//保存已选的线索无效原因
    public static final String KEY_CLOSE_REASON_LIST = "key_close_reason_list";//保存线索无效原因list
    public static final String KEY_HANDLE_RESULT = "key_handleResult";//处理结果
    public static final String KEY_HANDLE_RESULT_ERRORMSG = "key_handleResult_errorMsg";//处理失败结果
    private static final String ADD_LEADS_FILE = "add_leads_file";
    private static final String ADD_LEADS_SEND_SALERECORD = "add_leads_send_salerecord";
    private LinearLayout layoutHandleResultContainer;
    private EditTextModel3 editTextModel;
    private CheckBox mCheckBox;
    private ClickModel3 mClickModel;
    private TextView tvSectionName;
    private String mLastDealResult;
    private ObjectData mObjectData;
    private ObjectDescribe mObjectDescribe;
    private String mObjectName;
    private String mObjectId;
    private LeadsHandleType leadsHandleType;
    private List<Option> mCloseReasonList;//无效原因列表
    private Option mSelectedReason;//已选的无效原因
    private SelectFieldHelper<Option> mSelectFieldHelper;
    private boolean isSendSaleRecord;
    private boolean isShowCloseLeadClickModel;//是否展示无效原因

    public static Intent getIntent(Context context, LeadsHandleType leadsHandleType, ObjectData objectData) {
        return getIntent(context, leadsHandleType, objectData, null);
    }

    public static Intent getIntent(Context context, LeadsHandleType leadsHandleType, ObjectData objectData, ObjectDescribe objectDescribe) {
        Intent intent = new Intent(context, LeadsHandleResultAct.class);
        intent.putExtra(KEY_LEADS_HANDLE_TYPE, leadsHandleType);
        if(objectData != null) {
            CommonDataContainer.getInstance().saveData(KEY_OBJECTDATA, objectData);
        }
        if(objectDescribe != null) {
            CommonDataContainer.getInstance().saveData(KEY_OBJECTDESCRIBE, objectDescribe);
        }
        return intent;
    }


    @Override
    protected void onCreate(Bundle arg0) {
        super.onCreate(arg0);
        setContentView(R.layout.layout_leands_handle_result);
        if(!initData(arg0)){
            ToastUtils.show(I18NHelper.getText("bi.ui.BiOpportunityListAct.2124")/* 缺少必要参数 */);
            finish();
            return;
        }
        initPresenter();
        initTitleEx();
        initEditSectionView();

        // 将上次的处理结果带进来
        if (!StringUtils.isNullString(mLastDealResult)) {
            editTextModel.getEditTextView().setText(mLastDealResult);
            editTextModel.getEditTextView().setSelection(mLastDealResult.length());
        }
        mCheckBox.setChecked(false);//线索无效默认不勾选
        mCheckBox.setClickable(!StringUtils.isNullString(mLastDealResult) &&
                mLastDealResult.trim().length() > 0);//处理结果不为空时可以操作
        //监听switch开关
        mCheckBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                isSendSaleRecord = isChecked;
            }
        });
        if (leadsHandleType == LeadsHandleType.LEAD_CLOSE) {
            initCloseLeadInfo();
        }
    }

    private boolean initData(Bundle arg) {
        if (arg != null) {
            mObjectName = arg.getString(KEY_OBJECTNAME);
            mObjectId = arg.getString(KEY_OBJECTID);
            mLastDealResult = (String) arg.get(KEY_LAST_HANDLE_RESULT);
            leadsHandleType = (LeadsHandleType) arg.getSerializable(KEY_LEADS_HANDLE_TYPE);
            if (leadsHandleType == LeadsHandleType.LEAD_CLOSE) {
                mCloseReasonList = (List<Option>) arg.getSerializable(KEY_CLOSE_REASON_LIST);
                mSelectedReason = (Option) arg.getSerializable(KEY_CLOSE_REASON_INFO);
            }
        } else {
            mObjectData = (ObjectData) CommonDataContainer.getInstance().getAndRemoveSavedData(KEY_OBJECTDATA);
            mObjectDescribe = (ObjectDescribe) CommonDataContainer.getInstance().getAndRemoveSavedData(KEY_OBJECTDESCRIBE);
            if (mObjectData != null) {
                mObjectName = mObjectData.getName();
                mObjectId = mObjectData.getID();
                mLastDealResult = mObjectData.getString(LeadsConstants.API_LEADS_COMPLETED_RESULT);
            }
            leadsHandleType = (LeadsHandleType) getIntent().getSerializableExtra(KEY_LEADS_HANDLE_TYPE);
            if (leadsHandleType == LeadsHandleType.LEAD_CLOSE) {
                if(mObjectDescribe != null) {
                    mCloseReasonList = getReasonList(mObjectDescribe);
                }
            }

        }
        return !TextUtils.isEmpty(mObjectId);

    }

    /**
     * 无效原因枚举列表
     * @param objectDescribe
     * @return
     */
    private List<Option> getReasonList(ObjectDescribe objectDescribe){
        List<Option> closeReasonList = new ArrayList<>();
        if (objectDescribe != null) {
            Field field = objectDescribe.getFields().get(LeadsConstants.CLOSE_REASON);
            if (field != null) {
                //无效原因字段是否启用
                if (field.isActive()) {
                    SelectOneField backReasonField = field.to(SelectOneField.class);
                    List<Option> optionsList = backReasonField.getOptionsOfMetaData();
                    for (Option option : optionsList) {
                        if(option.isNotUsable()){
                            continue;
                        }
                        closeReasonList.add(option);
                    }
                }
            }
        }
        return closeReasonList;
    }

    private void initPresenter(){
        mPresenter = new LeadsHandlePresenter(this, leadsHandleType, mObjectName, mObjectId);
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        if (outState != null) {
            outState.putString(KEY_OBJECTNAME, mObjectName);
            outState.putString(KEY_OBJECTID, mObjectId);
            outState.putString(KEY_LAST_HANDLE_RESULT, editTextModel.getEditTextView().getText().toString());
            outState.putSerializable(KEY_LEADS_HANDLE_TYPE, leadsHandleType);
            //无效操作、无效原因选项存在
            if (leadsHandleType == LeadsHandleType.LEAD_CLOSE && mCloseReasonList != null && mCloseReasonList.size() > 0) {
                outState.putSerializable(KEY_CLOSE_REASON_LIST, (Serializable) mCloseReasonList);
                outState.putSerializable(KEY_CLOSE_REASON_INFO, mSelectedReason);//保存已选择的无效原因
            }
        }
    }

    @Override
    protected void initTitleEx() {
        super.initTitleEx();
        if(leadsHandleType == LeadsHandleType.LEAD_CLOSE) {
            mCommonTitleView.setTitle(I18NHelper.getText("crm.crm.LeadsHandleResultAct.6")/* 线索无效 */);
        }else {
            mCommonTitleView.setTitle(I18NHelper.getText("crm.crm.LeadsHandleResultAct.leads_follow_up")/* 线索跟进中 */);
        }
        mCommonTitleView.addLeftBackAction(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        mCommonTitleView.addRightAction(I18NHelper.getText("av.common.string.confirm")/* 确定 */, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String handleResult = editTextModel.getEditTextView().getText().toString().trim();
                if(leadsHandleType == LeadsHandleType.LEAD_CLOSE){
                    if(isShowCloseLeadClickModel && (mSelectedReason == null || TextUtils.isEmpty(mSelectedReason.getValue()))){
                        //展示无效原因，但未选择
                        ToastUtils.show(I18NHelper.getText("crm.crm.LeadsHandleResultAct.7")/* 请选择无效原因！ */);
                    }else {
                        //未展示无效原因或者展示无效原因，并选择了
                        mPresenter.commit(handleResult, isSendSaleRecord, mSelectedReason == null ? null :
                                mSelectedReason.getValue(), mSelectedReason.getString(IObjFieldInfo.KEY_INPUT_TEXT));
                    }
                }else {
                    if (StringUtils.isNullString(handleResult) || handleResult.trim().length() <= 0) {
                        ToastUtils.show(I18NHelper.getText("crm.leads.LeadsHandleResultAct.1331")/* 请填写处理结果 */);
                    } else {
                        mPresenter.commit(handleResult,isSendSaleRecord, "", "");
                    }
                }
            }
        });
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        MetaModifyUtil.handleEditTextFocusState(this,this,ev);
        return super.dispatchTouchEvent(ev);
    }

    /**
     * 初始化处理结果输入控件
     */
    private void initEditSectionView(){
        tvSectionName = findViewById(R.id.tv_section_name2);
        layoutHandleResultContainer = findViewById(R.id.layout_handleResult_container);
        mCheckBox = (CheckBox) findViewById(R.id.checkbox_send);
        tvSectionName.setText(I18NHelper.getText("crm.bizevent.BizAction.1684")/* 处理 */);

        editTextModel = new EditTextModel3(this);
        //跟进中，处理结果是必填项，需要设置必填标识
        if(leadsHandleType != LeadsHandleType.LEAD_CLOSE) {
            //设置必填标识
            ModelViewUtils.setRequiredFieldTitle(editTextModel.getTitleView(), I18NHelper.getText("crm.layout"
                    + ".layout_leands_handle_result.1851")/* 处理结果
             */);
        }else {
            editTextModel.setTitle(I18NHelper.getText("crm.layout.layout_leands_handle_result.1851")/* 处理结果 */);
        }
        editTextModel.setHint(I18NHelper.getText("bi.layout.frag_fieldtype_text_number.2159")/* 请输入 */);
        editTextModel.getTitleView().setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
        editTextModel.showDivider(true);
        editTextModel.getEditTextView().addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                //处理结果不为空（包含空格）时可以操作
                if(!TextUtils.isEmpty(s.toString()) && s.toString().trim().length() > 0) {
                    mCheckBox.setClickable(true);
                }else {
                    mCheckBox.setClickable(false);
                    //处理结果为空，设置未勾选
                    mCheckBox.setChecked(false);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        layoutHandleResultContainer.addView(editTextModel.getView());
    }

    /**
     * 控制选择无效原因控件是否展示
     */
    private void initChooseCloseReasonView() {
        isShowCloseLeadClickModel = true;
        ViewStub viewStub = findViewById(R.id.selectCloseReasonViewStub);
        View view = viewStub.inflate();
        LinearLayout container = view.findViewById(R.id.select_close_reason_container);
        TextView sectionTextName = view.findViewById(R.id.tv_section_name1);
        sectionTextName.setText(I18NHelper.getText("crm.controller.LeadsMoreOpsWMController.1339")/* 无效 */);
        mClickModel = new ClickModel3(this);
        mClickModel.setEditOrShow(true);
        //设置必填标识
        ModelViewUtils.setRequiredFieldTitle(mClickModel.getTitleView(), I18NHelper.getText("crm.crm"
                + ".LeadsHandleResultAct.1")/*
        无效原因 */);
        mClickModel.setHint(I18NHelper.getText("crm.layout.select_task_priority.7280")/* 请选择 */);
        mClickModel.getTitleView().setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
        mClickModel.showDivider(false);
        container.addView(mClickModel.getView());
        mClickModel.getView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mCloseReasonList.size() == 0) {
                    return;
                }
                go2SelectObjFieldActivity();
            }
        });
    }

    /**
     * 跳转选择无效原因
     */
    private void go2SelectObjFieldActivity() {
        isEmptySelectFieldHelper();
        List<Option> recoverList = mSelectFieldHelper.getRecoverList(mSelectedReason);
        SelectObjFieldConfig config = mSelectFieldHelper.newSelectObjFieldConfig(I18NHelper.getText
                ("crm.crm.LeadsHandleResultAct.1")/* 无效原因 */, true, mCloseReasonList, recoverList);
        mContext.startActivityForResult(SelectObjFieldActivity.getIntent(mContext, config),
                REQUEST_CODE_SELECT_CLOSE_REASON);
    }

    private void isEmptySelectFieldHelper() {
        if (mSelectFieldHelper == null) {
            mSelectFieldHelper = new SelectFieldHelper<>();
        }
    }

    /**
     * 处理 关于无效原因信息
     */
    private void initCloseLeadInfo() {
        //先处理服务端返回的无效操作信息
        if (mCloseReasonList != null && mCloseReasonList.size() > 0) {
            //activity 恢复情况下，存在无效原因列表，展示该选择控件
            initChooseCloseReasonView();
            //展示上次选择的
            updateCloseReasonTextView();
        }
    }

    private void updateCloseReasonTextView(){
        if (mSelectedReason != null) {
            String showContent = mSelectedReason.getFieldLabel();
            if(mSelectedReason.isOtherOption()){
                if(!TextUtils.isEmpty(mSelectedReason.getString(IObjFieldInfo.KEY_INPUT_TEXT))){
                    showContent = showContent + "：" + mSelectedReason.getString(IObjFieldInfo.KEY_INPUT_TEXT);
                }
            }
            mClickModel.setContentText(showContent);
        }
    }


    @Override
    public void onSuccess() {
          onResponse(true, "");
    }

    @Override
    public void onFailure(String errorMsg) {
        onResponse(false, errorMsg);
    }

    /**
     * 设置操作结果
     * @param error
     */
    private void onResponse(boolean isSuccess, String error) {
        error = TextUtils.isEmpty(error) ? "business failure" : error;
        String ccCallId = CCUtil.getNavigateCallId(this);
        if(!TextUtils.isEmpty(ccCallId)){
            if(isSuccess){
                CC.sendCCResult(ccCallId, CCResult.success());
            }else {
                CC.sendCCResult(ccCallId, CCResult.error(error));
            }
        }
        setResult(isSuccess, error);
        finish();
    }

    private void setResult(boolean isSuccess, String error){
        Intent intent = new Intent();
        //处理结果是否成功
        intent.putExtra(KEY_HANDLE_RESULT, isSuccess);
        if (!isSuccess) {
            //失败异常信息
            intent.putExtra(KEY_HANDLE_RESULT_ERRORMSG, error);
        }
        setResult(RESULT_OK, intent);
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data == null || resultCode != RESULT_OK) {
            return;
        }
        if (requestCode == REQUEST_CODE_SELECT_CLOSE_REASON) {
            List<IObjFieldInfo> selectedList = ((List<IObjFieldInfo>) data.getSerializableExtra
                    (SelectObjFieldActivity.KEY_SELECTED_FIELD));
            isEmptySelectFieldHelper();
            mSelectFieldHelper.updateSrcBySelectedInfo(mCloseReasonList, selectedList);
            if (selectedList != null && selectedList.size() > 0) {
                mSelectedReason = mSelectFieldHelper.getSelectedDataByUniqueId(mCloseReasonList, selectedList.get(0)
                        .uniqueId());
                updateCloseReasonTextView();
            }
        }
    }

    @Override
    public void setPresenter(LeadsHandleContract.Presenter presenter) {
        this.mPresenter = presenter;
    }
}
