/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.leads.leadstransfer.presenter;

import java.util.ArrayList;
import java.util.List;

import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.pluginapi.crm.type.ICrmBizApiName;
import com.fxiaoke.fscommon.http.WebApiExecutionCallbackWrapper;
import com.fxiaoke.fscommon.sandbox.SandboxUtils;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.plugin.crm.basic_setting.BasicSettingHelper;
import com.fxiaoke.plugin.crm.lib.api.BasicSettingService;
import com.fxiaoke.plugin.crm.lib.bean.ConfigInfo;
import com.fxiaoke.plugin.crm.lib.bean.GetConfigInfoListByKeysResult;
import com.fxiaoke.plugin.crm.leads.leadstransfer.api.LeadsTransferService;
import com.fxiaoke.plugin.crm.leads.leadstransfer.beans.DuplicatePreCheckResult;
import com.fxiaoke.plugin.crm.leads.leadstransfer.contract.LeadsTransferTabContract;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.text.TextUtils;
import io.reactivex.Single;
import io.reactivex.SingleEmitter;
import io.reactivex.SingleOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

/**
 * Author: duyc
 * Date: 2019/3/15
 */
public class LeadsTransferTabPresenter implements LeadsTransferTabContract.Presenter {

    private LeadsTransferTabContract.View mView;

    private String mLeadsID;
    private ObjectData mCustomerInfo;
    private ObjectData mPartnerInfo;
    private ObjectData mContactInfo;
    private ObjectData mOpportunityInfo;

    public LeadsTransferTabPresenter(LeadsTransferTabContract.View view, String leadsID,
                                     ObjectData customerInfo,
                                     ObjectData partnerInfo,
                                     ObjectData contactInfo,
                                     ObjectData opportunityInfo) {
        mView = view;
        mLeadsID = leadsID;
        mCustomerInfo = customerInfo;
        mPartnerInfo = partnerInfo;
        mContactInfo = contactInfo;
        mOpportunityInfo = opportunityInfo;
    }

    @SuppressLint("CheckResult")
    @Override
    public void start() {
        whetherNeedCheckLookRelateObjRight()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(Schedulers.io())
                .subscribe(new Consumer<Boolean>() {
                    @Override
                    public void accept(Boolean result) throws Exception {
                        mView.onNeedCheckLookRelateObjRight(result);
                        checkObjectDuplicatePrevious(mLeadsID, mCustomerInfo, mPartnerInfo, mContactInfo, null);
                    }
                });

    }

    private Single<Boolean> whetherNeedCheckLookRelateObjRight() {
        if(mView.isPreview()){
            return Single.just(false);
        }
        return Single.create(new SingleOnSubscribe<Boolean>() {
            @Override
            public void subscribe(SingleEmitter<Boolean> emitter) throws Exception {
                //是否开启了"当线索与他人的客户重复时，允许用户在转换时关联至该客户"的配置
                List<String> keys = new ArrayList<>(1);
                keys.add(BasicSettingHelper.ConfigParams.LEADS_TRANSFER_RIGHT_SETTING.value);
                BasicSettingService.getConfigInfoListByKeys(keys,
                        new WebApiExecutionCallbackWrapper<GetConfigInfoListByKeysResult>(
                                GetConfigInfoListByKeysResult.class, (Activity) mView.getContext()) {
                            @Override
                            public void succeed(GetConfigInfoListByKeysResult response) {
                                boolean needCheckLookRight = false;
                                if (response != null && response.mConfigInfoList != null) {
                                    for (ConfigInfo configInfo : response.mConfigInfoList) {
                                        if (TextUtils.equals(configInfo.mKey,
                                                BasicSettingHelper.ConfigParams.LEADS_TRANSFER_RIGHT_SETTING.value)) {
                                            String value = configInfo.mValue;
                                            if (!TextUtils.isEmpty(value)) {
                                                try {
                                                    //没有开启，需要校验
                                                    if (Integer.parseInt(value) == 0) {
                                                        needCheckLookRight = true;
                                                        break;
                                                    }
                                                } catch (Exception e) {
                                                    e.printStackTrace();
                                                }

                                            }
                                        }
                                    }
                                }
                                emitter.onSuccess(needCheckLookRight);
                            }

                            @Override
                            public void failed(String error) {
                                super.failed(error);
                                emitter.onSuccess(false);
                            }
                        });
            }
        });

    }

    @Override
    public void checkObjectDuplicatePrevious(String leadsID,
                                             ObjectData customerInfo,
                                             ObjectData partnerInfo,
                                             ObjectData contactInfo,
                                             ObjectData opportunityInfo) {
        LeadsTransferService.checkObjectDuplicatePrevious(mLeadsID,
                customerInfo == null ? null : customerInfo.getMap(),
                partnerInfo == null ? null : partnerInfo.getMap(),
                contactInfo == null ? null : contactInfo.getMap(),
                opportunityInfo == null ? null : opportunityInfo.getMap(),
                null, false, true, 1,
                new WebApiExecutionCallbackWrapper<DuplicatePreCheckResult>(DuplicatePreCheckResult.class, SandboxUtils.getActivityByContext(mView.getContext())) {

                    @Override
                    public void succeed(DuplicatePreCheckResult response) {
                        handlePreviousCheckRepeatResult(response);
                    }

                    @Override
                    public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                        super.failed(failureType, httpStatusCode, error);
                        ToastUtils.show(error);
                        handlePreviousCheckRepeatResult(null);
                    }
                });
    }

    private void handlePreviousCheckRepeatResult(DuplicatePreCheckResult response) {
        DuplicatePreCheckResult.DuplicateObject customerResult = null;
        DuplicatePreCheckResult.DuplicateObject partnerResult = null;
        DuplicatePreCheckResult.DuplicateObject contactResult = null;
        DuplicatePreCheckResult.DuplicateObject opportunityResult = null;
        if (response != null && response.getValues() != null && !response.getValues().isEmpty()) {
            for (DuplicatePreCheckResult.DuplicateObject duplicateObject : response.getValues()) {
                if (TextUtils.equals(ICrmBizApiName.ACCOUNT_API_NAME, duplicateObject.getObjectApiName())) {
                    customerResult = duplicateObject;
                } else if (TextUtils.equals(ICrmBizApiName.PARTNER_API_NAME, duplicateObject.getObjectApiName())) {
                    partnerResult = duplicateObject;
                } else if (TextUtils.equals(ICrmBizApiName.CONTACT_API_NAME, duplicateObject.getObjectApiName())) {
                    contactResult = duplicateObject;
                } else if (TextUtils.equals(ICrmBizApiName.OPPORTUNITY_API_NAME, duplicateObject.getObjectApiName())) {
                    opportunityResult = duplicateObject;
                }
            }
        }
        if (customerResult != null && customerResult.getOtherInfo() != null) {
            mView.onEnableTransExistCustomer(customerResult.getOtherInfo().isEnableTransExistCustomer());
        }
        mView.onPreviousCheckObjectResult(customerResult, partnerResult, contactResult, opportunityResult);
    }

}
