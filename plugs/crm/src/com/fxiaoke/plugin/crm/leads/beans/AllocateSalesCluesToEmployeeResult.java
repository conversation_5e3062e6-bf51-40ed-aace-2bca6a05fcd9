package com.fxiaoke.plugin.crm.leads.beans;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;

/**
 * 分配线索给员工返回 
 *
 */
public class AllocateSalesCluesToEmployeeResult {
    /**	* 成功ID列表	*/
    @JSONField(name="M1")
    public List<String> successList;
    /**	* 失败原因列表	*/
    @JSONField(name="M2")
    public List<String> failedList;
    /**	* 异常失败列表	*/
    @JSONField(name="M3")
    public List<String> errorList;

    public AllocateSalesCluesToEmployeeResult() {}

    @JSONCreator
    public AllocateSalesCluesToEmployeeResult(@JSONField(name="M1") List<String> successList,
                                              @JSONField(name="M2") List<String> failedList,
                                              @JSONField(name="M3") List<String> errorList) {
        this.successList = successList;
        this.failedList = failedList;
        this.errorList = errorList;
    }
}
