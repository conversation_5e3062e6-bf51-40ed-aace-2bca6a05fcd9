/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.leads.leadstransfer.beans;

import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.modify.duplicatecheck.MetaDataCheckResultData;
import com.facishare.fs.metadata.modify.duplicatecheck.MetaDataCheckResultTabData;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/4/1312:53
 * @description
 */
public class CheckReultDataCofig implements Serializable {
    //线索id 接口中使用
    public String leadsId;
    //要上传的对象数据 接口中使用
    public Map<String, ObjectData> uploadInfos;
    //
    public List<String> apiNames;
    //传递过来tab,只用于tab展示
    public List<MetaDataCheckResultTabData> tabDescribes;
    //对应tab中的数据
    public Map<String, MetaDataCheckResultData> metaDataCheckResultDatas;
    //是否返回的tab中的列表数据都为空
    private boolean isAllEmpty;

    public CheckReultDataCofig setLeadsId(String leadsId) {
        this.leadsId = leadsId;
        return this;
    }

    public CheckReultDataCofig setApinames(List<String> apiNames) {
        this.apiNames = apiNames;
        return this;
    }

    public CheckReultDataCofig setTabDescribes(List<MetaDataCheckResultTabData> tabDescribes) {
        this.tabDescribes = tabDescribes;
        return this;
    }

    public CheckReultDataCofig setMetaDataCheckResultDatas(Map<String, MetaDataCheckResultData> metaDataCheckResultDatas) {
        this.metaDataCheckResultDatas = metaDataCheckResultDatas;
        return this;
    }

    public CheckReultDataCofig setUploadInfos(Map<String, ObjectData> uploadInfos) {
        this.uploadInfos = uploadInfos;
        return this;
    }

    public CheckReultDataCofig setAllEmpty(boolean allEmpty) {
        this.isAllEmpty = allEmpty;
        return this;
    }

    public boolean hasCheckResult() {
        return tabDescribes != null && !tabDescribes.isEmpty() && !isAllEmpty;
    }
}
