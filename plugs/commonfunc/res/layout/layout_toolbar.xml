<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="24dp"
    android:layout_marginRight="24dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp">

    <ImageView
        android:id="@+id/btn_draw_free"
        style="@style/toolbar_button_style"
        android:src="@drawable/button_draw_free_selector" />

    <View
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1" />

    <ImageView
        android:id="@+id/btn_draw_mosaic"
        style="@style/toolbar_button_style"
        android:src="@drawable/button_draw_mosaic_selector" />

    <View
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1" />

    <ImageView
        android:id="@+id/btn_draw_rect"
        style="@style/toolbar_button_style"
        android:src="@drawable/button_draw_rect_selector" />

    <View
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1" />

    <ImageView
        android:id="@+id/btn_draw_circle"
        style="@style/toolbar_button_style"
        android:src="@drawable/button_draw_circle_selector" />

    <View
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1" />

    <ImageView
        android:id="@+id/btn_draw_arrow"
        style="@style/toolbar_button_style"
        android:src="@drawable/button_draw_arrow_selector" />

    <View
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1" />

    <ImageView
        android:id="@+id/btn_draw_text"
        style="@style/toolbar_button_style"
        android:src="@drawable/button_draw_text_selector" />

    <View
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1" />

    <ImageView
        android:id="@+id/btn_clip"
        style="@style/toolbar_button_style"
        android:src="@drawable/btn_edit_clip" />

</LinearLayout>