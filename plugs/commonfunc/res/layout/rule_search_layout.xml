<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:id="@+id/menubar"
    android:layout_width="fill_parent"
    android:layout_height="45dp"
    
    android:background="#bb5390E0"
    android:paddingBottom="10dp"
    android:paddingTop="10dp" >

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/rulebut"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/rule_layout_height"
        android:layout_alignParentLeft="true"
        android:layout_marginLeft="20dp"
        android:clickable="true"
        android:drawableRight="@drawable/arrow_down"
        android:gravity="center"
        i18n:fstext="commonfunc.rule_search_layout.text.rule"
        android:textSize="@dimen/l_text_size"
            android:layout_marginStart="20dp"
            android:drawableEnd="@drawable/arrow_down"
            android:layout_alignParentStart="true" />

    <View
        android:id="@+id/centerline"
        android:layout_width="1px"
        android:layout_height="15dp"
        android:layout_centerVertical="true"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@id/rulebut"
        android:background="#ffffff"
        android:visibility="invisible"
            android:layout_marginStart="10dp"
            android:layout_toEndOf="@id/rulebut" />

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/calendarmonth"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/rule_layout_height"
        android:layout_alignParentRight="true"
        android:layout_marginRight="10dp"
        android:clickable="true"
        android:drawableLeft="@drawable/botton_calender_month_un"
        android:gravity="center"
        i18n:fstext="commonfunc.rule_search_layout.text.this_month"
        android:textSize="@dimen/l_text_size"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="10dp"
            android:drawableStart="@drawable/botton_calender_month_un" />

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/calendarweek"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/rule_layout_height"
        android:layout_marginRight="10dp"
        android:layout_toLeftOf="@id/calendarmonth"
        android:clickable="true"
        android:drawableLeft="@drawable/botton_calender_week_un"
        android:gravity="center"
        android:onClick="btnClick"
        i18n:fstext="commonfunc.rule_search_layout.text.this_week"
        android:textSize="@dimen/l_text_size"
            android:layout_marginEnd="10dp"
            android:drawableStart="@drawable/botton_calender_week_un"
            android:layout_toStartOf="@id/calendarmonth" />

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/calendarday"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/rule_layout_height"
        android:layout_marginRight="10dp"
        android:layout_toLeftOf="@id/calendarweek"
        android:clickable="true"
        android:drawableLeft="@drawable/botton_calender_day"
        android:gravity="center"
        i18n:fstext="commonfunc.rule_search_layout.text.this_day"
        android:textSize="@dimen/l_text_size"
            android:layout_marginEnd="10dp"
            android:layout_toStartOf="@id/calendarweek"
            android:drawableStart="@drawable/botton_calender_day" />

    <ImageView
        android:id="@+id/arrow_iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginRight="10dp"
        android:layout_toLeftOf="@id/calendarday"
        android:src="@drawable/arrow_right"
            android:layout_toStartOf="@id/calendarday"
            android:layout_marginEnd="10dp" />

</RelativeLayout>