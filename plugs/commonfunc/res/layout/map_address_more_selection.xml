<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="#FFF"
    android:orientation="vertical" >
    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
        android:id="@+id/title"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/title_height" />

    <RelativeLayout
        android:id="@+id/map_layout_selection"
        android:layout_width="fill_parent"
        android:layout_height="45dp"
        android:background="@drawable/top_bar"
        android:gravity="center_vertical"
        android:orientation="horizontal" >

        <com.facishare.fs.sizectrlviews.SizeControlButton
            android:id="@+id/btnLeft"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:background="#00000000"
            android:clickable="true"
            android:gravity="center_vertical"
            i18n:fstext="commonfunc.dialog_fragment_fsmail_choose_attachment_menu.text.cancel"
            android:textColor="@color/titie_text_bg"
            android:textSize="16dip"
                android:layout_marginStart="10dp"
                android:layout_alignParentStart="true" />

        <!-- <TextView -->
        <!-- android:id="@+id/txtLeft" -->
        <!-- android:layout_width="wrap_content" -->
        <!-- android:layout_height="wrap_content" -->
        <!-- android:layout_marginTop="6dp" -->
        <!-- android:layout_alignParentLeft="true" -->
        <!-- android:layout_marginLeft="10dp" -->
        <!-- android:clickable="true" -->
        <!-- android:gravity="center_vertical" -->
        <!-- i18n:fstext="commonfunc.dialog_fragment_fsmail_choose_attachment_menu.text.cancel" -->
        <!-- android:textColor="@color/titie_text_bg" -->
        <!-- android:textSize="16dip"/> -->

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/txtCenter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            i18n:fstext="commonfunc.map_address_more_selection.text.select_address"
            android:textColor="@color/titie_text_bg"
            android:textSize="20dip" />

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/txtRight"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginRight="10dp"
            android:layout_marginTop="6dp"
            android:clickable="true"
            android:gravity="center_vertical"
            i18n:fstext="av.common.string.confirm"
            android:textColor="#AEAEAE"
            android:textSize="18dip"
            android:visibility="gone"
                android:layout_marginEnd="10dp"
                android:layout_alignParentEnd="true" />
    </RelativeLayout>

    <ListView
        android:id="@+id/lv_map_address"
        style="@style/default_listview_style" >
    </ListView>

</LinearLayout>