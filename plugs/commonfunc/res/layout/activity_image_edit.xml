<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000">

    <com.fxiaoke.plugin.commonfunc.imageedit.view.ImageEditView
        android:id="@+id/image_edit_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:keepScreenOn="true" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/layout_actionbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:background="@drawable/top_background_shader"
            android:paddingLeft="12dp"
            android:paddingBottom="32dp"
            android:paddingTop="36dp"
            android:paddingRight="12dp"
                android:paddingEnd="12dp"
                android:paddingStart="12dp">

            <TextView
                android:id="@+id/page_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top|start"
                android:gravity="center"
                android:paddingLeft="8dp"
                android:paddingTop="6dp"
                android:paddingRight="12dp"
                android:paddingBottom="6dp"
                android:text="取消"
                android:textColor="#FFFFFF"
                android:textSize="15dp"
                    android:paddingStart="8dp"
                    android:paddingEnd="12dp" />

            <ImageView
                android:id="@+id/btn_revoke"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_gravity="top|center_horizontal"
                android:layout_marginTop="0dp"
                android:padding="2dp"
                android:scaleType="centerInside"
                android:src="@drawable/button_edit_revoke_selector" />

            <TextView
                android:id="@+id/btn_edit_finish"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top|end"
                android:background="@drawable/button_background"
                android:gravity="center"
                android:paddingLeft="12dp"
                android:paddingTop="6dp"
                android:paddingRight="12dp"
                android:paddingBottom="6dp"
                android:text="完成"
                android:textColor="#FFFFFF"
                android:textSize="14dp"
                    android:paddingEnd="12dp"
                    android:paddingStart="12dp" />
        </FrameLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_alignParentBottom="true"
            android:background="@drawable/toolbar_background_shader"
            android:paddingTop="32dp"
            android:paddingBottom="24dp">

            <LinearLayout
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:orientation="vertical">

                <com.fxiaoke.plugin.commonfunc.imageedit.view.PaintBarView
                    android:id="@+id/paint_bar"
                    android:layout_width="match_parent"
                    android:layout_height="28dp"
                    android:layout_marginBottom="28dp"
                    android:paddingLeft="26dp"
                    android:paddingRight="26dp"
                    android:visibility="gone"
                        android:paddingEnd="26dp"
                        android:paddingStart="26dp" />

                <include
                    android:id="@+id/layout_toolbar_draw"
                    layout="@layout/layout_toolbar" />

                <include
                    android:id="@+id/layout_toolbar_modify"
                    layout="@layout/layout_toolbar_modify"
                    android:visibility="gone" />
            </LinearLayout>

            <include
                layout="@layout/layout_toolbar_clip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:visibility="gone" />
        </RelativeLayout>
    </RelativeLayout>
</FrameLayout>