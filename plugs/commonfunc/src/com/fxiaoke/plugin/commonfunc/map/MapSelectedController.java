/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.commonfunc.map;

import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.amap.api.maps.MapView;
import com.facishare.fs.common_utils.FSScreen;
import com.fxiaoke.fscommon_res.fsmap.FsGaodeMap;
import com.fxiaoke.fscommon_res.fsmap.FsGoogleMap;
import com.fxiaoke.fscommon_res.fsmap.FsMapView;
import com.fxiaoke.fscommon_res.fsmap.IFsMap;
import com.fxiaoke.location.api.FsLocType;
import com.fxiaoke.location.impl.FsMultiLocationManager;
import com.fxiaoke.plugin.commonfunc.R;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Environment;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.Toast;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

public class MapSelectedController extends LinearLayout {

    Activity activiy ;
    Context cxt;
    public MapSelectedController(Context context) {
        super(context);
        initview(context);
    }

    public MapSelectedController(Context context, AttributeSet attrs) {
        super(context, attrs);
        initview(context);
    }

    ListView listview;

    ProgressBar pbar;

    ImageView searchView;

    RelativeLayout centerview;

    private View v;

    NotifyView mTopTip;
    private FsMapView mFsMapView;
    private void initview(Context context) {
        activiy = (Activity) context;
        cxt = context;
        v = LayoutInflater.from(getContext()).inflate(R.layout.select_address_layout, null);
        addView(v);
        mFsMapView = v.findViewById(R.id.fsMap);
        listview = (ListView) v.findViewById(R.id.select_list);
        pbar = (ProgressBar) v.findViewById(R.id.searchround_progress);
		RelativeLayout.LayoutParams layoutpara = new RelativeLayout.LayoutParams(LayoutParams.MATCH_PARENT,
				FSScreen.getScreenHeight()/2);
        listview.setLayoutParams(layoutpara);
        centerview = (RelativeLayout) v.findViewById(R.id.center_layout);
        searchView = (ImageView) v.findViewById(R.id.search_iv);
        mTopTip = (NotifyView) v.findViewById(R.id.tip_notification);
        mTopTip.setTipText(I18NHelper.getText("xt.map_select.des.find_reason")/* 当前定位不准? 查找原因 */);
    }

    public ListView getListView() {
        return listview;
    }

    public IFsMap getFsMap(){
        return this.mFsMapView.getFsMap();
    }

    public IFsMap swichMap(){
        return this.mFsMapView.swichMap();
    }

    public ProgressBar getProgressBar() {
        return pbar;
    }

    public ImageView getSearchView() {
        searchView.setVisibility(View.VISIBLE);
        return searchView;
    }

    public void setCenterGone() {
        centerview.setVisibility(View.GONE);
    }

    public void setCenterVisable() {
        centerview.setVisibility(View.VISIBLE);
    }

    public View getViewById(int id) {
        return v.findViewById(id);
    }

    public NotifyView getNotifyView() {
        return mTopTip;
    }

}
