package com.fxiaoke.plugin.commonfunc.fileselect;


import com.facishare.fs.common_utils.permission.GrantedExecuter;
import com.facishare.fs.i18n.I18NHelper;

import android.Manifest;
import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.ViewTreeObserver.OnGlobalLayoutListener;
import android.widget.AbsListView;
import android.widget.AbsListView.OnScrollListener;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.Button;
import android.widget.FrameLayout.LayoutParams;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.ImageView.ScaleType;
import android.widget.TextView;

import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.pluginapi.file.beans.FileInfo;
import com.facishare.fs.pluginapi.pic.bean.ImageObjectVO;
import com.facishare.fs.pluginapi.pic.bean.ThumbnailVO;
import com.facishare.fs.utils_fs.FsSdcardUtils;
import com.fxiaoke.fscommon.adapter.NormalBaseAdapter;
import com.fxiaoke.fscommon.files.FileUtil;
import com.fxiaoke.fscommon_res.permission.PermissionExecuter;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.fxlog.FCTimePoint;
import com.fxiaoke.plugin.commonfunc.App;
import com.fxiaoke.plugin.commonfunc.R;
import com.lidroid.xutils.util.FileStorageUtils;
import com.lidroid.xutils.util.FsIOUtils;
import com.nostra13.universalimageloader.core.DisplayImageOptions;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.assist.ImageScaleType;
import com.nostra13.universalimageloader.core.download.ImageDownloader.Scheme;
import com.nostra13.universalimageloader.core.listener.PauseOnScrollListener;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
public class FilePreviewAllImageActivity extends BasePhotoActivity {
	List<FileInfo> listdatas;
	private GridView imageGridView = null;
	private FileAllPicGridViewAdpter adapter = null;
	private int mImageThumbSpacing;
	private TextView mTxtDisplayInfo = null;
	private Button buttonDisplayInfo = null;
	public static final String CHOOSEDIMAGE_KEY = "CHOOSEDIMAGE_KEY";
	// 相册名称
	private TextView txtCenter = null;
	ArrayList<FileInfo> hasChoosedList;
	private ProgressDialog mProgressDialog;
	private final static int SCAN_OK = 1,COPY_FINISH=2;
	private Handler mHandler = new Handler() {
		@Override
		public void handleMessage(Message msg) {
			super.handleMessage(msg);
			switch (msg.what) {
				case SCAN_OK:
					// 关闭进度条
					if (mProgressDialog != null && mProgressDialog.isShowing()) {
						mProgressDialog.dismiss();
					}
					initGridView();
					break;
				case COPY_FINISH:
					// 关闭进度条
					if (mProgressDialog != null && mProgressDialog.isShowing()) {
						mProgressDialog.dismiss();
					}
					FCLog.i("FilePreviewAllImageActivity","COPY_FINISH");
					if(actionBtn == ACTION_SAVE){
						saveClose();
					}else{
						backClose();
					}

					break;
			}
		}
	};

	private boolean isCopyImage = false;
	private int actionBtn,ACTION_SAVE=1,ACTION_BACK=2;

	protected Object actClass = null;
	@Override
	public void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		setContentView(R.layout.file_preview_all_image_gridview_layout);
		initGestureDetector();
		findViewAndInitTitle();


		Intent intent = getIntent();
		if(intent!=null){
			actClass= intent.getParcelableExtra(FileMainActivity.SOURCE_ACTIVITY_KEY);
			if (actClass==null){
				actClass= intent.getSerializableExtra(FileMainActivity.SOURCE_ACTIVITY_KEY);
			}
		}

		initData();
	}

	private void findViewAndInitTitle() {
		imageGridView = (GridView) findViewById(R.id.imageGridView);
		mTxtDisplayInfo = (TextView) findViewById(R.id.txtDisplayInfo);
		buttonDisplayInfo = (Button) findViewById(R.id.buttonDisplayInfo);
		initTitle();
	}

	private void initTitle() {
		initTitleCommon();
		mCommonTitleView.setMiddleText(I18NHelper.getText("mail.common.common.image")/* 图片 */);

		mCommonTitleView.addLeftAction(R.string.return_before_new_normal, new View.OnClickListener() {
			@Override
			public void onClick(View v) {
				backClose();
			}
		});

		mCommonTitleView.addRightAction(I18NHelper.getText("commonfunc.dialog_fragment_fsmail_choose_attachment_menu.text.cancel")/* 取消 */, new OnClickListener() {

			@Override
			public void onClick(View v) {
				setResult(Activity.RESULT_CANCELED);
				finish();
			}
		});
	}

	private void initData() {
		if (!PermissionExecuter.hasPermission(this, Manifest.permission
				.WRITE_EXTERNAL_STORAGE)) {
			new PermissionExecuter().requestPermissions(this, Manifest.permission.WRITE_EXTERNAL_STORAGE,
					new GrantedExecuter() {

						@Override
						public void exe() {
							initData();
						}
					});
			return;
		}
		mProgressDialog = ProgressDialog.show(this, null, I18NHelper.getText("xt.frag_service.text.loading")/* 正在加载... */);
		new Thread(new Runnable() {

			@Override
			public void run() {
				Intent intent = getIntent();
				if (intent != null) {
					readIntent(getIntent());
					mImageThumbSpacing = getResources().getDimensionPixelSize(R.dimen.image_thumbnail_spacing);
					hasChoosedList = (ArrayList<FileInfo>) intent.getSerializableExtra(CHOOSEDIMAGE_KEY);

//					FCTimePoint.start("imageFile2");
//					ArrayList<FileInfo> fileInfoList = FileUtil.getFileDatas(App.getInstance(), FileUtil.IMAGEKEY, FileMainActivity.fileDataPath/*AttachLoad.getAttachPath()*/);
//					FCTimePoint.end("imageFile2");

//					adapter = new FileAllPicGridViewAdpter(FilePreviewAllImageActivity.this, imageGridView, fileInfoList);

					// 通知Handler扫描图片完成
//					mHandler.sendEmptyMessage(SCAN_OK);
					FileUtil.loadImageFileInfoList(App.getInstance(),mFileInfoPageLoader);

				}
			}
		}).start();

	}
	FileUtil.IFileInfoPageLoadListener mFileInfoPageLoader = new FileUtil.IFileInfoPageLoadListener(){

		@Override
		public void onLoad(ArrayList<FileInfo> fileInfoList, int pageNo, boolean isLastPage) {
			runOnUiThread(new Runnable() {
				@Override
				public void run() {
					if (pageNo <= 0) {
						adapter = new FileAllPicGridViewAdpter(FilePreviewAllImageActivity.this, imageGridView, fileInfoList);
						// 关闭进度条
						if (mProgressDialog != null && mProgressDialog.isShowing()) {
							mProgressDialog.dismiss();
						}
						initGridView();
					} else {
						adapter.addDataList(fileInfoList);
					}
				}
			});

		}
	};
	private void initGridView() {
//		setAdapter(adapter);
		adapter.setmTxtDisplayInfo(mTxtDisplayInfo, buttonDisplayInfo);
//		mImageList = fileInfoListToImageObjList(hasChoosedList);
		adapter.setSelectedImgList(hasChoosedList);
		imageGridView.setAdapter(adapter);
		imageGridView.getViewTreeObserver().addOnGlobalLayoutListener(new OnGlobalLayoutListener() {
			@Override
			public void onGlobalLayout() {
				if (adapter.getCount() > 0) {
					int width = imageGridView.getWidth();
					final int columnWidth = (width / 4) - mImageThumbSpacing;
					adapter.setItemHeight(columnWidth);
				}
			}
		});
	}

	private ArrayList<FileInfo> setAThumbnailToFileInfoList(ArrayList<FileInfo> fileInfoList, ArrayList<ThumbnailVO> thumbnailList) {
		ArrayList<FileInfo> returnFileInfoList = fileInfoList;
		for(FileInfo fileInfo:returnFileInfoList){
			for (ThumbnailVO thumbnail : thumbnailList) {
				if (thumbnail.image_id.equals(fileInfo.imageId)) {
					fileInfo.thumbnail_data = thumbnail.data;
					break;
				}
			}
		}
		return returnFileInfoList;
	}

	//android11 以上拷贝图片到内置目录
	private boolean copySelectImages(int action){

		if(FsSdcardUtils.isScopedStorage() && adapter != null && !isCopyImage) {
			ArrayList<FileInfo> mImgList = adapter.getSelectedImgList();
			if (mImgList != null && mImgList.size() > 0) {
				if(mProgressDialog!=null && !mProgressDialog.isShowing()){
					mProgressDialog.show();
				}

				FCLog.i("FilePreviewAllImageActivity","copySelectImages");
				new Thread(){
					@Override
					public void run() {

						for(FileInfo bean: mImgList){
							if(bean != null){
								File file = null;
								if(!TextUtils.isEmpty(bean.uri)){
									file =  FsSdcardUtils.copyUri2FsTempDir(FilePreviewAllImageActivity.this, Uri.parse(bean.uri),bean.Name);
								}
								if(file!=null){
									String path = file.getAbsolutePath();
									FCLog.i("FilePreviewAllImageActivity","copySelectImages:"+ path);
									bean.Path =path;
								}else{
									FCLog.i("FilePreviewAllImageActivity","copySelectImage fail:"+bean.Path);
								}

							}
						}

						actionBtn=action;
						isCopyImage = true;
						mHandler.sendEmptyMessage(COPY_FINISH);
					}
				}.start();
				return true;
			}
		}
		return false;
	}

	/**
	 * button的点击事件
	 * @param v
	 */
	public void clickOK(View v) {
		if (adapter!=null&&adapter.getSelectedImgList()!=null&&adapter.getSelectedImgList().isEmpty()) {
			ToastUtils.show(I18NHelper.getText("common.file_preview_all_img.guide.please_select_img")/* 请选择一张图片! */);
		} else {
			saveClose();
		}
	}




	public void saveClose() {
		//确定
		if (adapter != null) {

			if( copySelectImages(ACTION_SAVE)){//android11 以上需要先拷贝到内置目录，走异步线程
				return;
			}
			Intent it = new Intent();
			it.putExtra(FileBrowserActivity.FILEINFOKEY, /*imageObjListToFileInfoList(*/adapter.getSelectedImgList());
			setResult(50, it);
		}
		finish();
	}


	public void backClose() {

		//返回
		if (adapter != null) {
			if(copySelectImages(ACTION_BACK)){
				return;
			}
			Intent it = new Intent();
			it.putExtra(FileBrowserActivity.FILEINFOKEY, /*imageObjListToFileInfoList(*/adapter.getSelectedImgList());
			setResult(49, it);
		}
		finish();
	}

	@Override
	protected void onPause() {
		super.onPause();
	}

	@Override
	protected void onDestroy() {
		super.onDestroy();

	}
	private  void pauseOnScroll(AbsListView view,OnScrollListener custom){
		PauseOnScrollListener lis=new PauseOnScrollListener(ImageLoader.getInstance(), true, true, custom);
		view.setOnScrollListener(lis);
	}
	public class FileAllPicGridViewAdpter extends NormalBaseAdapter {
		DisplayImageOptions displayOption;
		private LayoutParams mImageViewLayoutParams;

		private int mItemHeight;

		private ArrayList<FileInfo> mSelectedImgList = new ArrayList<FileInfo>();

		private TextView mTxtDisplayInfo = null;
		private Button button_ok = null;
		//追加显示的数据
		void addDataList(List newList) {
			if (newList == null) {
				return;
			}
			mData.addAll(newList);
			notifyDataSetChanged();
		}

		public FileAllPicGridViewAdpter(final Context context, AbsListView listView, ArrayList<FileInfo> list) {
//			super(context, listView, list);
			super(context, list);
			DisplayImageOptions.Builder builder = new DisplayImageOptions.Builder()
					.context(App.getInstance())
					.considerExifParams(true)
					.showImageOnLoading(R.drawable.default_photo)
					.showImageForEmptyUri(R.drawable.image_download_fail_icon)
					.showImageOnFail(R.drawable.image_download_fail_icon)
					.cacheInMemory(true)
					.cacheOnDisk(true)
					.imageScaleType(ImageScaleType.EXACTLY)
					.normalScaleType(ScaleType.CENTER_CROP)
					.specifyScaleType(ScaleType.CENTER)
					.bitmapConfig(Bitmap.Config.RGB_565);
			displayOption=builder.build();
			pauseOnScroll(listView, null);
			listView.setOnItemClickListener(new OnItemClickListener() {
				@Override
				public void onItemClick(AdapterView<?> parent, View view, int position, long id) {

					final FileInfo imgObj = (FileInfo) parent.getItemAtPosition(position);
					boolean noContain = !mSelectedImgList.contains(imgObj);
					if (noContain&& mSelectedImgList.size() >= FileMainActivity.fileCount) {
						ToastUtils.fileErrEx();
						return;
					}
					long dataTotalSize = imgObj.Size;
					if(dataTotalSize >= FileMainActivity.fileSize){
						ToastUtils.fileErr();
						return;
					}
					if(imgObj.Name!=null&&imgObj.Name.length() >FileMainActivity.fileNamelength){
						ToastUtils.fileNameErr();
						return;
					}
					if (!FsIOUtils.isFileExist(imgObj.Path)){
						ToastUtils.fileNoErr();
						return;
					}
					if (!FsIOUtils.isFileSize(imgObj.Path)){
						ToastUtils.fileMinErr();
						return;
					}
					Holder holder = (Holder) view.getTag();
					if (noContain) {
						mSelectedImgList.add(imgObj);
						holder.imgSelect.setVisibility(View.VISIBLE);
					} else {
						mSelectedImgList.remove(imgObj);
						holder.imgSelect.setVisibility(View.GONE);
					}
					dataTotalSize = getAllSize(mSelectedImgList);
					changeDisplayContent(dataTotalSize);
				}
			});
		}
		private long getAllSize(ArrayList<FileInfo> imgList) {
			long count = 0;
			for(FileInfo image:imgList){
				long size =image.Size;
				count = count + size;
			}
			return count;
		}

		/**
		 *  获取已选的图片文件列表
		 * @return
		 */
		public ArrayList<FileInfo> getSelectedImgList() {
			return mSelectedImgList;
		}
		/**
		 *  保存上次已选的图片文件列表
		 * @return
		 */
		public void setSelectedImgList(ArrayList<FileInfo> selectedImgList) {
			if (selectedImgList == null) {
				return;
			}
			this.mSelectedImgList = selectedImgList;
			long dataTotalSize = getAllSize(selectedImgList);
			changeDisplayContent(dataTotalSize);
		}
		public void changeDisplayContent(long data){
			if (mTxtDisplayInfo != null && button_ok!=null) {
				if (mSelectedImgList.isEmpty()) {
					button_ok.setText(I18NHelper.getText("av.common.string.confirm")/* 确定 */);
					mTxtDisplayInfo.setText(I18NHelper.getText("commonfunc.file_main_act.text.a_total_of_0K")/* 共0.00K */);
				} else {
					button_ok.setText(I18NHelper.getFormatText("xt.fsfilterpopwindow.text.determine.1",String
							.valueOf(mSelectedImgList.size()))/* 确定({0}) */);
					mTxtDisplayInfo.setText(I18NHelper.getText("xt.seeindeplevelfragment.text.total")/* 共 */+ FileStorageUtils.formatFileSize(data));
				}
			}
		}
		public void setmTxtDisplayInfo(TextView mTxtDisplayInfo,Button pTextViewSelectrangeShow) {
			this.mTxtDisplayInfo = mTxtDisplayInfo;
			button_ok = pTextViewSelectrangeShow;
		}

		@Override
		public View getView(int position, View convertView, ViewGroup parent) {
			Holder holder = null;
			if (convertView == null) {
				holder = new Holder();
				convertView = LayoutInflater.from(context).inflate(R.layout.file_preview_all_image_grid_item, null);
				holder.imgThumbnail = (ImageView) convertView.findViewById(R.id.imgThun);
				holder.imgSelect = (ImageView) convertView.findViewById(R.id.imgSelect);
				convertView.setTag(holder);
			} else {
				holder = (Holder) convertView.getTag();
			}

			final FileInfo fileInfo = (FileInfo) this.getItem(position);
			// Check the height matches our calculated column width
			if (holder.imgThumbnail.getLayoutParams().height != mItemHeight) {
				if (mImageViewLayoutParams != null) {
					holder.imgThumbnail.setLayoutParams(mImageViewLayoutParams);
					holder.imgSelect.setLayoutParams(mImageViewLayoutParams);
				}
			}

			if (isContainFileInfoInImgList(fileInfo)) {
				holder.imgSelect.setVisibility(View.VISIBLE);
			} else {
				holder.imgSelect.setVisibility(View.GONE);
			}

			ImageObjectVO obj = new ImageObjectVO();
			obj.position = position;
			obj.id = fileInfo.imageId;
			obj.uri = fileInfo.uri;
			obj.display_name = fileInfo.Name;
			obj.data = fileInfo.Path;
			obj.count = fileInfo.Size + "";
			obj.thumbnail_data = fileInfo.thumbnail_data;
//			setImageView(obj, holder.imgThumbnail, obj.data);

			String displayUri;
			if(FsSdcardUtils.isScopedStorage()){
				displayUri = obj.uri;
			}else{
				displayUri = Scheme.FILE.wrap(obj.data);
			}

			ImageLoader.getInstance().displayImage(displayUri, holder.imgThumbnail,
					displayOption);

			return convertView;
		}

		private boolean isContainFileInfoInImgList(FileInfo newFileInfo) {
			boolean isContain = false;
			for(FileInfo oldFileInfo: mSelectedImgList){
				if(newFileInfo!=null && oldFileInfo.Path!=null&&oldFileInfo.Path.equalsIgnoreCase(newFileInfo.Path)){
					int index = mSelectedImgList.indexOf(oldFileInfo);
					mSelectedImgList.set(index, newFileInfo);
//					mImgList.remove(oldFileInfo);
//					mImgList.add(newFileInfo);
					isContain = true;
				}
			}
			return isContain;
		}

		public class Holder {
			public ImageView imgSelect;
			public ImageView imgThumbnail;
		}

		public void setItemHeight(int columnWidth) {
			if (mItemHeight == columnWidth) {
				return;
			}
			mItemHeight = columnWidth;
			if (mImageViewLayoutParams == null) {
				mImageViewLayoutParams = new LayoutParams(LayoutParams.FILL_PARENT, columnWidth);
			}
			notifyDataSetChanged();
		}
	}

}
