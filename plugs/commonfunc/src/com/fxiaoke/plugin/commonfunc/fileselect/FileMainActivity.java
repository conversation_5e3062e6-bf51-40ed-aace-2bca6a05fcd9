package com.fxiaoke.plugin.commonfunc.fileselect;

import com.facishare.fs.common_utils.permission.GrantedExecuter;
import com.facishare.fs.i18n.I18NHelper;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.IFileAttach;
import com.facishare.fs.pluginapi.config.ReleaseType;
import com.facishare.fs.pluginapi.file.beans.FileInfo;
import com.fxiaoke.fscommon.files.FileUtil;
import com.fxiaoke.fscommon_res.permission.PermissionExecuter;
import com.fxiaoke.fxsocketlib.businessctrl.FcpUploadTask;
import com.fxiaoke.plugin.commonfunc.BaseActivity;
import com.fxiaoke.plugin.commonfunc.R;
import com.fxiaoke.stat_engine.StatEngine;
import com.lidroid.xutils.util.FileStorageUtils;

import android.Manifest;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.Button;
import android.widget.TextView;

public class FileMainActivity extends BaseActivity implements OnClickListener {
    public static final String IS_FROM_TRAIN_HELPER = "is_from_train_helper";
    private List<FileInfo> listdatas = new ArrayList<FileInfo>();
    private TextView textView_selectrange_show;//显示选中大小
    private Button button_ok;
    public static long fileCount = 10;
    public static long fileSize = FcpUploadTask.FCP_UPLOAD_FILE_LIMIT;//目前是借助fcp上传，需遵守fcp的文件大小限制
    public static long fileNamelength = 200;//
    public static String FILEINFOTYPE = "fileinfo_type";//标识是从选文件返回
    public static int FILEINFOTYPE_KEY = 1;//标识是从选文件返回
    static String fileDataPath = FSContextManager.getCurUserContext().getSDOperator().getUserDownFilePath().getPath();
    /**
     * 返回页面class，0505改造后不再需要这个键值
     */
    @Deprecated
    public static final String SOURCE_ACTIVITY_KEY = "source_activity_key";
    // anjx add 0825 支持传入最大选择数，以及超过最大数目时的提示
    public static final String MAX_SELECT_KEY = "max_select_key";
    public static final String OUT_OF_RANGE_TOAST_KEY = "out_of_range_toast_key";
    public static String outOfRangToast = I18NHelper.getText("common.image_select.guide.img_count_limit")/* 您已超过最大选择数 */;
    // anjx add end
    protected Object actClass = null;
    Context ctx;
    public static String FILEINFOKEY = "fileinfo_key";//返回数据
    private boolean mShowMediaLL = true;//是否显示媒体
    private ArrayList<CharSequence> mFileTypes;//过滤文件类型

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // TODO Auto-generated method stub
        super.onCreate(savedInstanceState);
        setContentView(R.layout.file_main_act);
        ctx = this;/*HostInterfaceManager.getHostInterface().getApp();*/
        initTitle();
        initData();
        initView();
    }

    private void initData() {
        try {
            listdatas = (List<FileInfo>) getIntent().getSerializableExtra("fileinfo_key");
        } catch (Exception e) {
            e.printStackTrace();
        }
        //		listdatas =;
        fileCount = 10;
        outOfRangToast = I18NHelper.getText("common.image_select.guide.img_count_limit")/* 您已超过最大选择数 */;
        int index = getIntent().getIntExtra("index", 0);
        if (index > 0) {
            fileCount = 1;
        }
        //anjx add start 0825
        int maxSelect = getIntent().getIntExtra(MAX_SELECT_KEY, -1);
        if (maxSelect > 0) {
            fileCount = maxSelect;
        }
        String outOfRangToastStr = getIntent().getStringExtra(OUT_OF_RANGE_TOAST_KEY);
        if (!TextUtils.isEmpty(outOfRangToastStr)) {
            outOfRangToast = outOfRangToastStr;
        }
        Intent intent = getIntent();
        if (intent != null) {
            actClass = intent.getParcelableExtra(FileMainActivity.SOURCE_ACTIVITY_KEY);
            if (actClass == null) {
                actClass = intent.getSerializableExtra(FileMainActivity.SOURCE_ACTIVITY_KEY);
            }
            mFileTypes = intent.getCharSequenceArrayListExtra(IFileAttach.FILTER_FILE_TYPE_LIST);
        }
        mShowMediaLL = getIntent().getBooleanExtra(IFileAttach.IS_SHOW_MEDIA, true);
    }

    private void initTitle() {
        initTitleCommon();
        mCommonTitleView.setMiddleText(I18NHelper.getText("th.material.audio.local_file")/* 本地文件 */);
        //anjx 0911 屏蔽返回按钮，与确定按钮逻辑一致，功能重复有歧义。
        //		mCommonTitleView.addLeftAction(R.string.return_before_new_normal, new View.OnClickListener() {
        //			@Override
        //			public void onClick(View v) {
        //				backClose();
        //			}
        //		});
        mCommonTitleView.addRightAction(I18NHelper.getText("commonfunc.dialog_fragment_fsmail_choose_attachment_menu.text.cancel")/* 取消 */, new OnClickListener() {
            @Override
            public void onClick(View v) {
                StatEngine.tick("TA_app_android_addmaterial_submit_cancel", "0");
                clearback();
            }
        });
    }

    private void initView() {

        if (!mShowMediaLL) {
            findViewById(R.id.ll_media).setVisibility(View.GONE);
        }
        textView_selectrange_show = (TextView) findViewById(R.id.textView_selectrange_show);

        if (!isPlay()) {
            findViewById(R.id.RelativeLayout_phone_mon).setOnClickListener(this);
            findViewById(R.id.RelativeLayout_phone_mon).setVisibility(View.VISIBLE);
        } else {
            findViewById(R.id.RelativeLayout_phone_mon).setVisibility(View.GONE);
        }
        if (FileUtil.getFilePathCard(ctx, FileUtil.SDCARD_INTERNAL).isMounted()&&!isPlay()) {//内
            findViewById(R.id.RelativeLayout_sd_mon).setOnClickListener(this);
            findViewById(R.id.RelativeLayout_sd_mon).setVisibility(View.VISIBLE);
        } else {
            findViewById(R.id.RelativeLayout_sd_mon).setVisibility(View.GONE);
        }
        if (FileUtil.getFilePathCard(ctx, FileUtil.SDCARD_EXTERNAL).isMounted()&&!isPlay()) {//外
            findViewById(R.id.RelativeLayout_sd_mon_more).setOnClickListener(this);
            findViewById(R.id.RelativeLayout_sd_mon_more).setVisibility(View.VISIBLE);
        } else {
            findViewById(R.id.RelativeLayout_sd_mon_more).setVisibility(View.GONE);
        }
        findViewById(R.id.RelativeLayout_my_file).setOnClickListener(this);
        //确定
        button_ok = (Button) findViewById(R.id.button_ok);
        button_ok.setOnClickListener(this);
        showSelect();
        initCountText();
    }
    boolean isPlay(){
        return ReleaseType.isPlay(HostInterfaceManager.getHostInterface().getReleaseType());
    }
    private void initCountText(){
        if (!PermissionExecuter.hasPermission(this, Manifest.permission
                .WRITE_EXTERNAL_STORAGE)) {
            new PermissionExecuter().requestPermissionsWithBusinessMessage(this, Manifest.permission.WRITE_EXTERNAL_STORAGE,
                    new GrantedExecuter() {

                        @Override
                        public void exe() {
                            initCountText();
                        }
                    },
                    I18NHelper.getText("commonfunc.file_main_act.text.permission", "请开启纷享销客的存储权限，以便使用上传附件功能"),
                    I18NHelper.getText("common.permission.request_storage_permission_title", "需要获得存储权限")
            );
            return;
        }
        //图片
        findViewById(R.id.RelativeLayout_phone_image).setOnClickListener(this);
        ((TextView) findViewById(R.id.textView_image))
                .setText("(" + FileUtil.getFileCount(ctx, FileUtil.IMAGEKEY) + ")");
        //媒体
        findViewById(R.id.RelativeLayout_media).setOnClickListener(this);
        ((TextView) findViewById(R.id.textView_media))
                .setText("(" + FileUtil.getFileCount(ctx, FileUtil.MEDIAKEY) + ")");
        //音乐
        findViewById(R.id.RelativeLayout_music).setOnClickListener(this);
        ((TextView) findViewById(R.id.textView_music))
                .setText("(" + FileUtil.getFileCount(ctx, FileUtil.MUSICKEY) + ")");
    }

    private int getAllSize() {
        int count = 0;
        if (listdatas != null && listdatas.size() > 0) {
            for (int i = 0; i < listdatas.size(); i++) {
                count += listdatas.get(i).Size;
            }
        }
        return count;
    }

    private void showSelect() {
        if (listdatas != null && listdatas.size() > 0) {
            button_ok.setText(I18NHelper.getText("xt.fsfilterpopwindow.text.determine")/* 确定( */ + listdatas.size() + ")");
            textView_selectrange_show.setText(I18NHelper.getText("xt.seeindeplevelfragment.text.total")/* 共 */ + FileStorageUtils.formatFileSize(getAllSize()));
        } else {
            button_ok.setText(I18NHelper.getText("av.common.string.confirm")/* 确定 */);
            textView_selectrange_show.setText(I18NHelper.getText("commonfunc.file_main_act.text.a_total_of_0K")/* 共0.00K */);
        }
    }

    @Override
    public void onClick(View v) {
        // TODO Auto-generated method stub
        Intent eintent;
        int i = v.getId();
        if (i == R.id.ib_info_return_upper) {
            backClose();

        } else if (i == R.id.RelativeLayout_phone_image) {
            eintent = new Intent(context, FilePreviewAllImageActivity.class);
            eintent.putExtra(FilePreviewAllImageActivity.CHOOSEDIMAGE_KEY, (Serializable) listdatas);
            if (actClass != null) {
                eintent.putExtra(FileMainActivity.SOURCE_ACTIVITY_KEY, (ComponentName) actClass);
            }
            handlerIntent(eintent);
            startActivityForResult(eintent, 201);

        } else if (i == R.id.RelativeLayout_media) {
            eintent = new Intent(context, FileMediaActivity.class);
            eintent.putExtra(FileMediaActivity.FILETYPE, FileUtil.MEDIAKEY);
            eintent.putExtra(FileMediaActivity.TITLEKEY, I18NHelper.getText("commonfunc.activityjumputil.text.media")/* 媒体 */);
            eintent.putExtra(FileMediaActivity.FILEINFOKEY, (Serializable) listdatas);
            if (actClass != null) {
                if (actClass instanceof Class) {
                    eintent.putExtra(FileMainActivity.SOURCE_ACTIVITY_KEY, (Class) actClass);
                } else if (actClass instanceof ComponentName) {
                    eintent.putExtra(FileMainActivity.SOURCE_ACTIVITY_KEY, (ComponentName) actClass);
                }
            }
            handlerIntent(eintent);
            startActivityForResult(eintent, 201);

        } else if (i == R.id.RelativeLayout_music) {
            eintent = new Intent(context, FileMediaActivity.class);
            eintent.putExtra(FileMediaActivity.FILETYPE, FileUtil.MUSICKEY);
            eintent.putExtra(FileMediaActivity.TITLEKEY, I18NHelper.getText("commonfunc.filemainactivity.text.item_music")/* 音乐 */);
            eintent.putExtra(FileMediaActivity.FILEINFOKEY, (Serializable) listdatas);
            if (actClass != null) {
                if (actClass instanceof Class) {
                    eintent.putExtra(FileMainActivity.SOURCE_ACTIVITY_KEY, (Class) actClass);
                } else if (actClass instanceof ComponentName) {
                    eintent.putExtra(FileMainActivity.SOURCE_ACTIVITY_KEY, (ComponentName) actClass);
                }
            }
            handlerIntent(eintent);
            startActivityForResult(eintent, 201);

        } else if (i == R.id.RelativeLayout_my_file) {
            eintent = new Intent(context, FileMediaActivity.class);
            eintent.putExtra(FileMediaActivity.FILETYPE, FileUtil.MYFILEKEY);
            if (mFileTypes != null) {
                eintent.putExtra(IFileAttach.FILTER_FILE_TYPE_LIST, mFileTypes);
            }
            eintent.putExtra(FileMediaActivity.TITLEKEY, I18NHelper.getText("commonfunc.filemainactivity.text.i_downloaded_the_attachment")/* 我下载的附件 */);
            eintent.putExtra(FileMediaActivity.FILEINFOKEY, (Serializable) listdatas);
            if (actClass != null) {
                if (actClass instanceof Class) {
                    eintent.putExtra(FileMainActivity.SOURCE_ACTIVITY_KEY, (Class) actClass);
                } else if (actClass instanceof ComponentName) {
                    eintent.putExtra(FileMainActivity.SOURCE_ACTIVITY_KEY, (ComponentName) actClass);
                }
            }
            handlerIntent(eintent);
            startActivityForResult(eintent, 201);

        } else if (i == R.id.RelativeLayout_phone_mon) {
            eintent = new Intent(context, FileBrowserActivity.class);
            eintent.putExtra(FileBrowserActivity.FILETITLE, I18NHelper.getText("commonfunc.filemainactivity.text.storage_of_phone")/* 手机内存 */);
            if (mFileTypes != null) {
                eintent.putExtra(IFileAttach.FILTER_FILE_TYPE_LIST, mFileTypes);
            }
            eintent.putExtra(FileBrowserActivity.FILEPAHT, "/");
            eintent.putExtra(FileBrowserActivity.FILEINFOKEY, (Serializable) listdatas);
            if (actClass != null) {
                if (actClass instanceof Class) {
                    eintent.putExtra(FileMainActivity.SOURCE_ACTIVITY_KEY, (Class) actClass);
                } else if (actClass instanceof ComponentName) {
                    eintent.putExtra(FileMainActivity.SOURCE_ACTIVITY_KEY, (ComponentName) actClass);
                }
            }
            handlerIntent(eintent);
            startActivityForResult(eintent, 201);

        } else if (i == R.id.RelativeLayout_sd_mon) {
            eintent = new Intent(context, FileBrowserActivity.class);
            eintent.putExtra(FileBrowserActivity.FILETITLE, I18NHelper.getText("commonfunc.file_main_act.text.sd_card_memory")/* SD卡内存 */);
            eintent.putExtra(FileBrowserActivity.FILEPAHT,
                    FileUtil.getFilePathCard(ctx, FileUtil.SDCARD_INTERNAL).getMountPoint());///storage/emulated/0
            eintent.putExtra(FileBrowserActivity.FILEINFOKEY, (Serializable) listdatas);
            if (mFileTypes != null) {
                eintent.putExtra(IFileAttach.FILTER_FILE_TYPE_LIST, mFileTypes);
            }
            if (actClass != null) {
                if (actClass instanceof Class) {
                    eintent.putExtra(FileMainActivity.SOURCE_ACTIVITY_KEY, (Class) actClass);
                } else if (actClass instanceof ComponentName) {
                    eintent.putExtra(FileMainActivity.SOURCE_ACTIVITY_KEY, (ComponentName) actClass);
                }
            }
            handlerIntent(eintent);
            startActivityForResult(eintent, 201);

        } else if (i == R.id.RelativeLayout_sd_mon_more) {
            String string = FileUtil.getFilePathCard(ctx, FileUtil.SDCARD_EXTERNAL).getMountPoint();
            eintent = new Intent(context, FileBrowserActivity.class);
            if (mFileTypes != null) {
                eintent.putExtra(IFileAttach.FILTER_FILE_TYPE_LIST, mFileTypes);
            }
            eintent.putExtra(FileBrowserActivity.FILETITLE, I18NHelper.getText("commonfunc.filemainactivity.text.expansion_card_memory")/* 扩展卡内存 */);
            eintent.putExtra(FileBrowserActivity.FILEPAHT, string);
            eintent.putExtra(FileBrowserActivity.FILEINFOKEY, (Serializable) listdatas);
            if (actClass != null) {
                if (actClass instanceof Class) {
                    eintent.putExtra(FileMainActivity.SOURCE_ACTIVITY_KEY, (Class) actClass);
                } else if (actClass instanceof ComponentName) {
                    eintent.putExtra(FileMainActivity.SOURCE_ACTIVITY_KEY, (ComponentName) actClass);
                }
            }
            handlerIntent(eintent);
            startActivityForResult(eintent, 201);

        } else if (i == R.id.button_ok) {
            close();

        } else if (i == R.id.textView_title_right) {
            clearback();

        } else {
        }
    }

    private void clearback() {
        setResult(RESULT_CANCELED);
        finish();
    }

    private void backClose() {
        //这个是返回键，被屏蔽了。
        Intent intent = getIntent();
        //选择数据
        intent.putExtra(FileBrowserActivity.FILEINFOKEY, (Serializable) listdatas);
        intent.putExtra(FileMainActivity.FILEINFOTYPE, FileMainActivity.FILEINFOTYPE_KEY);
        setResult(RESULT_OK, intent);
        finish();
    }

    @Override
    protected void close() {
        //确定
        Intent intent = new Intent();
        if (actClass != null) {
            if (actClass instanceof Class) {
                intent = new Intent(context, (Class) actClass);
            } else if (actClass instanceof ComponentName) {
                intent.setComponent((ComponentName) actClass);
            }

        }
        //选择数据
        intent.putExtra(FileBrowserActivity.FILEINFOKEY, (Serializable) listdatas);
        intent.putExtra(FileMainActivity.FILEINFOTYPE, FileMainActivity.FILEINFOTYPE_KEY);
        setResult(RESULT_OK, intent);
        finish();
    }

    private static final int REQUEST_SELECT_FILE = 1;

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        //48取消，49选文件返回，50选文件确定
        if (resultCode == RESULT_CANCELED) {
            //取消
            setResult(RESULT_CANCELED);
            finish();
        } else {
            if (data == null) {
                return;
            }
            List<FileInfo> lists = (List<FileInfo>) data.getSerializableExtra(FileBrowserActivity.FILEINFOKEY);
            if (listdatas == null) {
                listdatas = new ArrayList<FileInfo>();
            }
            listdatas.clear();
            if (lists != null && lists.size() > 0) {
                listdatas.addAll(lists);
            }
            showSelect();
            if (resultCode == 50) {
                //调用确定逻辑
                close();
            }
        }
    }
}