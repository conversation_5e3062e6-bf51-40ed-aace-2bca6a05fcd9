/*
 * Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.commonfunc.api;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.facishare.fs.common_utils.StringUtils;
import com.facishare.fs.pluginapi.IFileAttach;
import com.facishare.fs.pluginapi.IStartActForResult;
import com.facishare.fs.pluginapi.file.beans.FileInfo;
import com.fxiaoke.fxlog.DebugEvent;
import com.fxiaoke.fxlog.FCLog;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Intent;
import androidx.pluginmgr.PluginManager;

/**
 * 附件接口的实现类
 * <b>创建时间</b> 2016/10/19
 *
 * <AUTHOR>
 */
public class FileAttachImpl implements IFileAttach {
    /**
     * commonfunc插件的包名
     */
    private static final String COMMONFUNC_PKGNAME = "com.fxiaoke.plugin.commonfunc";

    private static final DebugEvent TAG = new DebugEvent("FileAttachImpl");

    @Override
    public void goToAttach(Activity act, int reqCode, int max_select_key, String out_of_range_toast_key, String
            targetAct, ComponentName componentName, ArrayList<CharSequence> filterFileTypeList, boolean isShowMedia) {
        Intent intent = new Intent();
        intent.putExtra(OUT_OF_RANGE_TOAST_KEY, out_of_range_toast_key);
        intent.putExtra(MAX_SELECT_KEY, max_select_key);
        intent.putExtra(SOURCE_ACTIVITY_KEY, componentName);
        intent.putCharSequenceArrayListExtra(FILTER_FILE_TYPE_LIST, filterFileTypeList);
        intent.putExtra(IS_SHOW_MEDIA, isShowMedia);
        if (StringUtils.isNullString(targetAct)) {
            intent.setComponent(
                    new ComponentName(FCLog.g_HostPkgName, COMMONFUNC_PKGNAME + ".fileselect.FileMainActivity"));
        } else {
            intent.setComponent(new ComponentName(FCLog.g_HostPkgName, COMMONFUNC_PKGNAME + ".fileselect." + targetAct));
        }
        PluginManager.getInstance().startActivityForResult(act, intent, reqCode);
    }

    /**
     * @param act
     * @param reqCode                返回码
     * @param listFileInfo           list集合
     * @param out_of_range_toast_key 可以选择附件的最大数量
     * @param targetAct              目标activity
     * @param componentName
     */
    @Override
    public void goToAttach(Activity act, int reqCode, List<FileInfo> listFileInfo, String out_of_range_toast_key,
                           String targetAct, ComponentName componentName) {
        Intent intent = new Intent();
        if (listFileInfo != null) {
            if (!listFileInfo.isEmpty()) {
                intent.putExtra(FILEINFOKEY, (Serializable) listFileInfo);
            }
        }

        intent.putExtra(OUT_OF_RANGE_TOAST_KEY, out_of_range_toast_key);
        intent.putExtra(SOURCE_ACTIVITY_KEY, componentName);
        if (StringUtils.isNullString(targetAct)) {
            intent.setComponent(
                    new ComponentName(FCLog.g_HostPkgName, COMMONFUNC_PKGNAME + ".fileselect.FileMainActivity"));
            PluginManager.getInstance().startActivityForResult(act, intent, reqCode);
        } else {
            intent.setComponent(new ComponentName(FCLog.g_HostPkgName, COMMONFUNC_PKGNAME + ".fileselect." + targetAct));
            PluginManager.getInstance().startActivityForResult(act, intent, reqCode);
        }
    }

    @Override
    public void goToAttach(Activity act, int reqCode, boolean isSetComponentName, List<FileInfo> listFileInfo,
                           String out_of_range_toast_key, String targetAct, ComponentName componentName) {
        goToAttach(act, reqCode, -1, isSetComponentName, listFileInfo, out_of_range_toast_key, targetAct,
                componentName);
    }

    /**
     * @param act
     * @param reqCode                返回码
     * @param listFileInfo           list集合
     * @param out_of_range_toast_key 可以选择附件的最大数量
     * @param targetAct              目标activity
     * @param componentName
     */
    @Override
    public void goToAttach(Activity act, int reqCode, int max_select_key, boolean isSetComponentName,
                           List<FileInfo> listFileInfo, String out_of_range_toast_key, String targetAct,
                           ComponentName componentName) {
        Intent intent = new Intent();
        if (listFileInfo != null) {
            if (!listFileInfo.isEmpty()) {
                intent.putExtra(FILEINFOKEY, (Serializable) listFileInfo);
            }
        }
        intent.putExtra(OUT_OF_RANGE_TOAST_KEY, out_of_range_toast_key);
        intent.putExtra(MAX_SELECT_KEY, max_select_key);
        intent.putExtra(SOURCE_ACTIVITY_KEY, componentName);
        if (StringUtils.isNullString(targetAct)) {
            intent.setComponent(
                    new ComponentName(FCLog.g_HostPkgName, COMMONFUNC_PKGNAME + ".fileselect.FileMainActivity"));
        } else {
            intent.setComponent(new ComponentName(FCLog.g_HostPkgName, COMMONFUNC_PKGNAME + ".fileselect." + targetAct));
        }
        PluginManager.getInstance().startActivityForResult(act, intent, reqCode);
    }

    @Override
    public void goToAttach(IStartActForResult act, int reqCode, int max_select_key, String out_of_range_toast_key,
                           String targetAct, ComponentName componentName, ArrayList<CharSequence> filterFileTypeList,
                           boolean isShowMedia) {
        Intent intent = new Intent();
        intent.putExtra(OUT_OF_RANGE_TOAST_KEY, out_of_range_toast_key);
        intent.putExtra(MAX_SELECT_KEY, max_select_key);
        intent.putExtra(SOURCE_ACTIVITY_KEY, componentName);
        intent.putCharSequenceArrayListExtra(FILTER_FILE_TYPE_LIST, filterFileTypeList);
        intent.putExtra(IS_SHOW_MEDIA, isShowMedia);
        if (StringUtils.isNullString(targetAct)) {
            intent.setComponent(
                    new ComponentName(FCLog.g_HostPkgName, COMMONFUNC_PKGNAME + ".fileselect.FileMainActivity"));
        } else {
            intent.setComponent(new ComponentName(FCLog.g_HostPkgName, COMMONFUNC_PKGNAME + ".fileselect." + targetAct));
        }
        act.startActivityForResult(intent, reqCode);
    }

    @Override
    public void goToAttach(IStartActForResult act, int reqCode, List<FileInfo> listFileInfo,
                           String out_of_range_toast_key, String targetAct, ComponentName componentName) {
        Intent intent = new Intent();
        if (listFileInfo != null) {
            if (!listFileInfo.isEmpty()) {
                intent.putExtra(FILEINFOKEY, (Serializable) listFileInfo);
            }
        }

        intent.putExtra(OUT_OF_RANGE_TOAST_KEY, out_of_range_toast_key);
        intent.putExtra(SOURCE_ACTIVITY_KEY, componentName);
        if (StringUtils.isNullString(targetAct)) {
            intent.setComponent(
                    new ComponentName(FCLog.g_HostPkgName, COMMONFUNC_PKGNAME + ".fileselect.FileMainActivity"));
            act.startActivityForResult(intent, reqCode);
        } else {
            intent.setComponent(new ComponentName(FCLog.g_HostPkgName, COMMONFUNC_PKGNAME + ".fileselect." + targetAct));
            act.startActivityForResult(intent, reqCode);
        }
    }

    @Override
    public void goToAttach(IStartActForResult act, int reqCode, boolean isSetComponentName, List<FileInfo> listFileInfo,
                           String out_of_range_toast_key, String targetAct, ComponentName componentName) {
        goToAttach(act, reqCode, -1, isSetComponentName, listFileInfo, out_of_range_toast_key, targetAct,
                componentName);
    }

    @Override
    public void goToAttach(IStartActForResult act, int reqCode, int max_select_key, boolean isSetComponentName,
                           List<FileInfo> listFileInfo, String out_of_range_toast_key, String targetAct,
                           ComponentName componentName) {
        Intent intent = new Intent();
        if (listFileInfo != null) {
            if (!listFileInfo.isEmpty()) {
                intent.putExtra(FILEINFOKEY, (Serializable) listFileInfo);
            }
        }
        intent.putExtra(OUT_OF_RANGE_TOAST_KEY, out_of_range_toast_key);
        intent.putExtra(MAX_SELECT_KEY, max_select_key);
        intent.putExtra(SOURCE_ACTIVITY_KEY, componentName);
        if (StringUtils.isNullString(targetAct)) {
            intent.setComponent(
                    new ComponentName(FCLog.g_HostPkgName, COMMONFUNC_PKGNAME + ".fileselect.FileMainActivity"));
        } else {
            intent.setComponent(new ComponentName(FCLog.g_HostPkgName, COMMONFUNC_PKGNAME + ".fileselect." + targetAct));
        }
        act.startActivityForResult(intent, reqCode);
    }

    @Override
    public Intent getGoToAttachIntent(List<FileInfo> listFileInfo, String out_of_range_toast_key, String targetAct,
                                      ComponentName componentName) {
        Intent intent = new Intent();
        if (listFileInfo != null) {
            if (!listFileInfo.isEmpty()) {
                intent.putExtra(FILEINFOKEY, (Serializable) listFileInfo);
            }
        }

        intent.putExtra(OUT_OF_RANGE_TOAST_KEY, out_of_range_toast_key);
        intent.putExtra(SOURCE_ACTIVITY_KEY, componentName);
        if (StringUtils.isNullString(targetAct)) {
            intent.setComponent(
                    new ComponentName(FCLog.g_HostPkgName, COMMONFUNC_PKGNAME + ".fileselect.FileMainActivity"));
        } else {
            intent.setComponent(new ComponentName(FCLog.g_HostPkgName, COMMONFUNC_PKGNAME + ".fileselect." + targetAct));
        }
        return intent;
    }
}
