import org.gradle.internal.os.OperatingSystem
import groovy.json.JsonSlurper

apply from: "utils.gradle"
apply from: "libmode.gradle"
def g_isci = System.getenv("CI_SERVER").equals("yes");
gradle.ext.g_isci = g_isci;
println("ci env: " + g_isci)

def isNeedProguardIn() {
    return true
}


gradle.ext.isNeedProguard = this.&isNeedProguardIn
println("need proguard " + isNeedProguardIn())
/**
 * 子模块更新
 */
def submodule() {
    println("system: " + OperatingSystem.current().getFamilyName())
    println("inJenkins: " + inJenkins)
    if (inJenkins.equals("yes")) {
        //jenkins自带更新子模块，跳过这一步
        return
    }
    if (gradle.g_isci) {
        //git lab runner
    } else {
        //本地开发，如果发生问题，请手动删除submodule对应的目录再尝试编译
//        println("submodule update start")
//        def cmd = 'git submodule sync --recursive'
//        println(cmd.execute().text)
//        cmd = 'git submodule update --init --recursive'
//        println(cmd.execute().text)
//        println("submodule update end")
    }
}

submodule()

def modifyLibVersion(projectName, newVersion) {
    File settings = new File(rootProject.getProjectDir().getAbsolutePath() + "/libconfig.cfg")
    String content = FileUtil.getFileStr(settings)
    String reg = "\"$projectName\":\\s*\".*?\\..*?\""
    String mycode = "\"$projectName\":\"$newVersion\""
    println(reg + " " + mycode)
    content = content.replaceAll(reg, mycode)
    FileUtil.writeToFile(content.getBytes("utf-8"), settings)
}
/****************************配置项目的类型****************************************/
//工程类型1
//不带资源文件的lib工程可以归到该分组下
//这个变量里的module编译后的jar包会自动拷贝到moduleJarLibs目录，该目录下的jar包会打包到host工程的apk
//该分组下的module同时也需要在libmanager module的build.gradle文件中追加provided依赖
def moduleJarProjects = [] as List<String>
//需要经常调试的maven工程加入如下列表
def moduleJarProjects_maven = ["EventBus", "FxLog", "xUtils", "UniversalImgLoader","performance",
                               "pluginapi", "FxDbLib", "pluginapi_contacts", "pluginapi_account", "FXStatEngine",
                               "data_impl", "user_context", "fscommon_nores", "locationLib",
                               "okgo", "okserver", "Multidex", "qixinlib", "CrmStyleViews", "KWQin", "tinker",
                               "IntelliOperation", "crmlib", "docpreviewlib", "remote_service", "LiteAVLib",
                               "metadata-sdk","metadata-beans","modelview","fsprobuf", "commonViews", "paylib", "beans","openCVLibrary320"] as List<String>
/**
 * jar aar 依赖库的版本号定义
 */
gradle.ext {
    modifyLibVersion = this.&modifyLibVersion
    getLibRefConfig()
}

def getLibRefConfig() {

    def slurper = new JsonSlurper()
    File ff = new File(rootProject.projectDir.absolutePath + "/libconfig.cfg")
    def libconfig = slurper.parseText(ff.text)
    gradle.ext.versionmap = libconfig
}
//需要打包到multi-dex的module，应对65536的问题
//该分组下的module同时也需要在libmanager module的build.gradle文件中追加provided依赖
def dexJarProjects_JIAGU = ["FxSocketLib", "fshttp", "contactssynclib"] as List<String>
def dexJarProjects_Main = [ "FxLog","Multidex","tinker","fsmaindex"] as List<String>
//工程类型2
//apk 插件工程
//追加新插件时需要在该变量中追加对应的module名称
def pluginsApk = ["fxiaoke",  "avcall", "crm", "commonfunc", "bi", "trainhelper", "fsmail", "hideplug", "shortvideo"]
def downloadPluginsApk = []
def pluginsSeqidMap = ["fxiaoke": 1, "avcall": 1, "crm": 1, "commonfunc": 1, "bcr": 1, "bi": 1, "trainhelper": 1,
                       "fsmail" : 1, "hideplug": 1, "shortvideo": 1]
gradle.ext.moduleJarProjects = moduleJarProjects
gradle.ext.moduleJarProjects_temp = moduleJarProjects_maven
gradle.ext.dexJarProjects_JIAGU = dexJarProjects_JIAGU
gradle.ext.dexJarProjects_Main = dexJarProjects_Main
gradle.ext.pluginsApk = pluginsApk
gradle.ext.downloadPluginsApk = downloadPluginsApk
gradle.ext.sonarHost = "http://oss.firstshare.cn/sonarqube/"
gradle.ext.mavenUrl = "https://maven.foneshare.cn/artifactory/libs-release-local"
gradle.ext.mavenDevUrl = "https://maven.foneshare.cn/artifactory/libs-snapshot-local"
gradle.ext.mavenUserName = "deployer"
gradle.ext.mavenPwd = "fxiaoke"
gradle.ext.pluginsSeqidMap = pluginsSeqidMap
gradle.ext.debugUseReleaseKeyStore = false
gradle.ext.gitversion=""
/**
 * 初始化环境变量
 * @return
 */
def initFsEnvironment() {
    gradle.ext.versionName = "9.6.3"
    gradle.ext.versionName_bcr = "9.6.3"

    //support_v4，FxLog等lib如果改为31编译，会报错support_v4:compileDebugJavaWithJavac 编译器 (1.8.0_74) 中出现异常错误。
    //通过各种命令也查不出具体原因 gradlew :support_v4:compileDebugJavaWithJavac --stacktrace -info --debug --scan
    gradle.ext.compileSdkVersionForLib = 30

    gradle.ext.compileSdkVersion = 31
    gradle.ext.buildToolsVersion = "28.0.3"
    gradle.ext.targetSdkVersion = 30
    gradle.ext.minSdkVersion = 21
    gradle.ext.javaVersion = JavaVersion.VERSION_1_8
    gradle.ext.kotlinVersion = "1.3.61"
    gradle.ext.wechatVersion = "6.7.9"

    gradle.ext.andoidxVersion = "1.0.0"
    gradle.ext.andoidxConstraintlayoutVersion = "1.1.3"

    gradle.ext.supportLibVersion = "28.0.0"
    gradle.ext.fastjsonVersion = "fs.1.1.65"
    gradle.ext.rxjava2Version = "2.2.7"
    gradle.ext.rxandroid2Version = "2.1.1"
    gradle.ext.constraintLayoutVersion = "1.1.3"

    //单元测试相关(依赖方式参照lib metadata-sdk)
    gradle.ext.junitVersion = '4.12'
    gradle.ext.mockitoVersion = "2.23.4"
    gradle.ext.robolectricVersion = "4.3"//4.4有bug

    gradle.ext.chameleonVersion = "0.0.2"

    gradle.ext.outDir = rootProject.getProjectDir().getAbsolutePath() + "/output"
    gradle.ext.seqidMap = new HashMap<String, Integer>()


    //    Aliyun SDK核心库
    gradle.ext.externalAlivcSVideoPro = 'com.aliyun.aio:AliVCSDK_UGC:6.3.0' //5.短视频 + 播放器
//    gradle.ext.externalAlivcSVideoPro = "com.aliyun.video.android:svideopro:3.36.0";//专业版
//    gradle.ext.externalAlivcSVideoCore = "com.aliyun.video.android:core:1.2.2"
//    gradle.ext.externalAlivcConan = 'com.alivc.conan:AlivcConan:1.0.3'
//    externalAlivcSVideoTemplate = "com.aliyun.video.android:svideotemplate:0.0.1"//使用剪同款功能必须依赖
//    gradle.ext.externalAlivcFFmpeg = 'com.aliyun.video.android:AlivcFFmpeg:4.3.1'//短视频与播放器共用。两个SDK同时接入，请用该版本。
    gradle.ext.externalAndroidDesign = 'com.google.android.material:material:1.6.0'
}

def initTinkerId() {
    gradle.ext.tinkerId = getTinkerId()
}

/**
 * 初始化签名信息
 */
def initSignatureInfo() {
    gradle.ext.keyAlias = ""
    gradle.ext.keyPassword = ""
    gradle.ext.storePassword = ""
    File signFile = null;
    println "initSignatureInfo-------"
    if(packageType.equals("MENGNIU")){
        println "use mengniu keystore"
        if (OperatingSystem.current().isWindows()) {
            gradle.ext.storeFile = "c:\\KeyStore"
            signFile = new File("c:\\KeyStore\\mengniu_keystore.properties")
        } else if (OperatingSystem.current().isLinux() || OperatingSystem.current().isMacOsX()) {
            gradle.ext.storeFile = "/opt/KeyStore"
            signFile = new File("/opt/KeyStore/mengniu_keystore.properties")
        } else {
            gradle.ext.storeFile = ""
            signFile = new File("")
        }

    }else{
        println "use fs keystore"
        if (OperatingSystem.current().isWindows()) {
            gradle.ext.storeFile = "c:\\KeyStore"
            signFile = new File("c:\\KeyStore\\keystore.properties")
        } else if (OperatingSystem.current().isLinux() || OperatingSystem.current().isMacOsX()) {
            gradle.ext.storeFile = "/opt/KeyStore"
            signFile = new File("/opt/KeyStore/keystore.properties")
        } else {
            gradle.ext.storeFile = ""
            signFile = new File("")
        }
    }
    if (signFile.exists()) {
        Properties p = new Properties()
        signFile.withInputStream { stream ->
            p.load(stream)
        }
        gradle.ext.keyAlias = p.keyAlias
        gradle.ext.keyPassword = p.keyPassword
        gradle.ext.storeFile = p.storeFile
        gradle.ext.storePassword = p.storePassword
    }
}
/**
 * 创建输出目录
 * @return
 */
def createOutputDir() {
    createDir(gradle.outDir)
    createDir(gradle.outDir + "/mappings")
}

def getTinkerId() {
    def verNums = gradle.versionName.tokenize('.');
    def tinkerId = 0;
    if (verNums != null && verNums.size >= 3) {
        tinkerId = (verNums[0] as Integer) * 100000 + (verNums[1] as Integer) * 10000 + (verNums[2] as Integer) * 1000 + (expandVersionCode as Integer);
    }
    return tinkerId;
}

initFsEnvironment()
initTinkerId()
initSignatureInfo()
createOutputDir()
def flutterProjectRoot = rootProject.projectDir.parentFile.toPath()

def plugins = new Properties()
def pluginsFile = new File(flutterProjectRoot.toFile(), '.flutter-plugins')
if (pluginsFile.exists()) {
    pluginsFile.withReader('UTF-8') { reader -> plugins.load(reader) }
}

plugins.each { name, path ->
    def pluginDirectory = flutterProjectRoot.resolve(path).resolve('android').toFile()
    include ":$name"
    project(":$name").projectDir = pluginDirectory
}
//loadAll为true时会去加载所有的工程，loadAll为false时以下工程不会加载
//":fastjson", ":EventBus", ':MaterialDialogs', ':ViewPagerIndicator',
// ':QrCodeScan', ':pulltorefresh', ":okgo", ":okserver"
def loadAll = true
def flutterTest = false
if(flutterTest){
    include ":host_flutter_test"
    project(':host_flutter_test').projectDir = new File('host/host_flutter_test')
}
if (loadAll) {
    include ':FxLog', ':libmanager', ':contactssynclib', ':androidsvg',  ':LiteAVLib', ':performance', ':anticheat',
            ":EventBus",':leakcanaryutil-debug', ':leakcanaryutil-release',
            ":Emojicon", ":commonViews", ":fsprobuf",
            ":FXStatEngine", ":xUtils", ":UniversalImgLoader", ":FxDbLib", ":android-pluginmgr",
            ":pluginapi_contacts", ":pluginapi_account", ":beans", ":pluginapi", ":FxSocketLib",
            ":data_impl", ":host_main", ':cmViews', ':jsApi', ':MaterialDialogs', ':commonfunc', ':hideplug', ':crm',
            ':bi', ':fsmail',':pay',
            ':ViewPagerIndicator', ':QrCodeScan', ':pulltorefresh', ":fxiaoke",
            ":user_context", ":paylib", ":docpreviewlib", ":fscommon_res", ":fscommon_nores", ':fshttp',
            ':locationLib', ":okgo", ":okserver", ":Multidex", ":sizectrlviews", ":qixinlib", ":CrmStyleViews",
            ":KWQin", "subscaleview", "trainhelper", "IntelliOperation", ":tinker", ":support_v4", ":shortvideo",
            ":crmlib", ":remote_service",":metadata-sdk",":metadata-beans",":modelview" ,":flowpropeller",
            ":workflow-sdk",":fsinit",":fsmaindex",":openCVLibrary320",":fspanorama"
    //host--begin
    project(':host_main').projectDir = new File('host/host_main')

//host--end
//plugs--begin
    project(':bi').projectDir = new File('plugs/bi')
    project(':commonfunc').projectDir = new File('plugs/commonfunc')
    project(':crm').projectDir = new File('plugs/crm')
    project(':fsmail').projectDir = new File('plugs/fsmail')
    project(':pay').projectDir = new File('plugs/pay')
    project(':fxiaoke').projectDir = new File('fxiaoke')
    project(':trainhelper').projectDir = new File('plugs/trainhelper')
    project(':hideplug').projectDir = new File('plugs/hideplug')
    project(':shortvideo').projectDir = new File('plugs/shortvideo')
    project(':anticheat').projectDir = new File('plugs/anticheat')
//plugs--end
//libs with resources--begin
    project(':sizectrlviews').projectDir = new File('lib/sizectrlviews')
    project(':androidsvg').projectDir = new File('lib/androidsvg')
    project(':cmViews').projectDir = new File('lib/cmViews')
    project(':CrmStyleViews').projectDir = new File('lib/CrmStyleViews')
    project(':jsApi').projectDir = new File('lib/jsApi')
    project(':ViewPagerIndicator').projectDir = new File('lib/ViewPagerIndicator')
    project(':QrCodeScan').projectDir = new File('lib/QrCodeScan')
    project(':pulltorefresh').projectDir = new File('lib/pulltorefresh')
    project(':MaterialDialogs').projectDir = new File('lib/MaterialDialogs')
//project(':slideDateTimePicker').projectDir = new File('lib/slideDateTimePicker')
    project(':fscommon_res').projectDir = new File('lib/fscommon_res')
    project(':fscommon_nores').projectDir = new File('lib/fscommon_nores')
    project(':fshttp').projectDir = new File('lib/fshttp')
    project(':okgo').projectDir = new File('lib/okgo')
    project(':okserver').projectDir = new File('lib/okserver')
    project(':subscaleview').projectDir = new File('lib/subscaleview')
    project(':metadata-sdk').projectDir = new File('lib/metadata-sdk')
    project(':flowpropeller').projectDir = new File('lib/flowpropeller')
    project(':workflow-sdk').projectDir = new File('lib/workflow-sdk')
//libs with resources--end
//libs no resources--begin
    project(':FxLog').projectDir = new File('lib/FxLog')
    project(':EventBus').projectDir = new File('lib/EventBus')
    project(':Emojicon').projectDir = new File('lib/Emojicon')
    project(':commonViews').projectDir = new File('lib/commonViews')
    project(':FXStatEngine').projectDir = new File('lib/FXStatEngine')
    project(':xUtils').projectDir = new File('lib/xUtils')
    project(':UniversalImgLoader').projectDir = new File('lib/UniversalImgLoader')
    project(':FxDbLib').projectDir = new File('lib/FxDbLib')
    project(':android-pluginmgr').projectDir = new File('lib/android-pluginmgr')
    project(':pluginapi_contacts').projectDir = new File('lib/pluginapi_contacts')
    project(':pluginapi_account').projectDir = new File('lib/pluginapi_account')
    project(':beans').projectDir = new File('lib/beans')
    project(':pluginapi').projectDir = new File('lib/pluginapi')
    project(':openCVLibrary320').projectDir = new File('lib/openCVLibrary320')
    project(':fspanorama').projectDir = new File('lib/fspanorama')
    project(':FxSocketLib').projectDir = new File('lib/FxSocketLib')
    project(':data_impl').projectDir = new File('host/data_impl')
    project(":user_context").projectDir = new File("lib/user_context")
    project(":paylib").projectDir = new File("lib/paylib")
    project(":docpreviewlib").projectDir = new File("lib/docpreviewlib")
    project(":LiteAVLib").projectDir = new File("lib/LiteAVLib")
    project(':contactssynclib').projectDir = new File('lib/contactssynclib')
    project(':Multidex').projectDir = new File('lib/Multidex')
    project(':qixinlib').projectDir = new File('lib/qixinlib')
    project(':crmlib').projectDir = new File('lib/crmlib')
    project(':KWQin').projectDir = new File('lib/KWQin')
    project(':IntelliOperation').projectDir = new File('lib/IntelliOperation')
    project(':metadata-beans').projectDir = new File('lib/metadata-beans')
    project(':modelview').projectDir = new File('lib/modelview')
    project(':fsinit').projectDir = new File('lib/fsinit')
    project(':fsmaindex').projectDir = new File('lib/fsmaindex')
//libs no resources--end
//libs to second dex--begin
    project(':fsprobuf').projectDir = new File('lib/fsprobuf')
//libs to second dex--end
    project(':libmanager').projectDir = new File('lib/libmanager')
    project(':locationLib').projectDir = new File('lib/locationLib')
    project(':tinker').projectDir = new File('lib/tinker')
    project(':support_v4').projectDir = new File('lib/support_v4')
    project(':remote_service').projectDir = new File('lib/remote_service')
    project(':performance').projectDir = new File('lib/performance')

    project(':leakcanaryutil-debug').projectDir = new File('lib/leakcanaryutil/leakcanaryutil-debug')
    project(':leakcanaryutil-release').projectDir = new File('lib/leakcanaryutil/leakcanaryutil-release')

} else {
    include ':FxLog', ':libmanager', ':contactssynclib', ":Emojicon", ":commonViews", ":fsprobuf",
            ":FXStatEngine", ":xUtils", ":UniversalImgLoader", ":FxDbLib", ":android-pluginmgr",
            ":pluginapi_contacts", ":pluginapi_account", ":beans", ":pluginapi", ":FxSocketLib",
            ":data_impl", ":host_main", ':cmViews', ':jsApi', ':commonfunc', ':hideplug', ':crm', ':bi', ':fsmail',':pay',':anticheat',
            ":fxiaoke", ":user_context", ":paylib", ":docpreviewlib", ":fscommon_res", ":fscommon_nores",
            ':fshttp', ':locationLib', ":sizectrlviews",":androidsvg", ":qixinlib", ":CrmStyleViews", ":KWQin",
            "subscaleview", "trainhelper",
            "IntelliOperation", ":tinker", ":support_v4", ":shortvideo", ":crmlib", ":remote_service", ":LiteAVLib",
            ":metadata-sdk",":metadata-beans",":modelview",":performance" , ":flowpropeller", ":workflow-sdk",":fsinit","openCVLibrary320",":fspanorama"
    //host--begin
    project(':host_main').projectDir = new File('host/host_main')
//host--end
//plugs--begin
    project(':bi').projectDir = new File('plugs/bi')
    project(':commonfunc').projectDir = new File('plugs/commonfunc')
    project(':crm').projectDir = new File('plugs/crm')
    project(':fsmail').projectDir = new File('plugs/fsmail')
    project(':pay').projectDir = new File('plugs/pay')
    project(':fxiaoke').projectDir = new File('fxiaoke')
    project(':trainhelper').projectDir = new File('plugs/trainhelper')
    project(':hideplug').projectDir = new File('plugs/hideplug')
    project(':shortvideo').projectDir = new File('plugs/shortvideo')
    project(':anticheat').projectDir = new File('plugs/anticheat')
//plugs--end
//libs with resources--begin
    project(':sizectrlviews').projectDir = new File('lib/sizectrlviews')
    project(':androidsvg').projectDir = new File('lib/androidsvg')
    project(':CrmStyleViews').projectDir = new File('lib/CrmStyleViews')
    project(':cmViews').projectDir = new File('lib/cmViews')
    project(':jsApi').projectDir = new File('lib/jsApi')
//    project(':ViewPagerIndicator').projectDir = new File('lib/ViewPagerIndicator')
//    project(':QrCodeScan').projectDir = new File('lib/QrCodeScan')
//    project(':pulltorefresh').projectDir = new File('lib/pulltorefresh')
//    project(':MaterialDialogs').projectDir = new File('lib/MaterialDialogs')
    project(':fscommon_res').projectDir = new File('lib/fscommon_res')
    project(':fscommon_nores').projectDir = new File('lib/fscommon_nores')
    project(':fshttp').projectDir = new File('lib/fshttp')
//    project(':okgo').projectDir = new File('lib/okgo')
//    project(':okserver').projectDir = new File('lib/okserver')
    project(':subscaleview').projectDir = new File('lib/subscaleview')
    project(':metadata-sdk').projectDir = new File('lib/metadata-sdk')
    project(':flowpropeller').projectDir = new File('lib/flowpropeller')
    project(':workflow-sdk').projectDir = new File('lib/workflow-sdk')
//libs with resources--end
//libs no resources--begin
    project(':FxLog').projectDir = new File('lib/FxLog')
//    project(':EventBus').projectDir = new File('lib/EventBus')
    project(':Emojicon').projectDir = new File('lib/Emojicon')
    project(':commonViews').projectDir = new File('lib/commonViews')
    project(':FXStatEngine').projectDir = new File('lib/FXStatEngine')
    project(':xUtils').projectDir = new File('lib/xUtils')
    project(':UniversalImgLoader').projectDir = new File('lib/UniversalImgLoader')
    project(':FxDbLib').projectDir = new File('lib/FxDbLib')
    project(':android-pluginmgr').projectDir = new File('lib/android-pluginmgr')
    project(':pluginapi_contacts').projectDir = new File('lib/pluginapi_contacts')
    project(':pluginapi_account').projectDir = new File('lib/pluginapi_account')
    project(':beans').projectDir = new File('lib/beans')
    project(':pluginapi').projectDir = new File('lib/pluginapi')
    project(':openCVLibrary320').projectDir = new File('lib/openCVLibrary320')
    project(':fspanorama').projectDir = new File('lib/fspanorama')
    project(':FxSocketLib').projectDir = new File('lib/FxSocketLib')
    project(':data_impl').projectDir = new File('host/data_impl')
    project(":user_context").projectDir = new File("lib/user_context")
    project(":paylib").projectDir = new File("lib/paylib")
    project(":docpreviewlib").projectDir = new File("lib/docpreviewlib")
    project(":LiteAVLib").projectDir = new File("lib/LiteAVLib")
    project(':contactssynclib').projectDir = new File('lib/contactssynclib')
    project(':qixinlib').projectDir = new File('lib/qixinlib')
    project(':crmlib').projectDir = new File('lib/crmlib')
    project(':metadata-beans').projectDir = new File('lib/metadata-beans')
    project(':modelview').projectDir = new File('lib/modelview')
    project(':fsinit').projectDir = new File('lib/fsinit')
    project(':fsmaindex').projectDir = new File('lib/fsmaindex')
//libs no resources--end
//libs to second dex--begin
    project(':fsprobuf').projectDir = new File('lib/fsprobuf')
//libs to second dex--end
    project(':libmanager').projectDir = new File('lib/libmanager')
    project(':locationLib').projectDir = new File('lib/locationLib')
    project(':KWQin').projectDir = new File('lib/KWQin')
    project(':IntelliOperation').projectDir = new File('lib/IntelliOperation')
    project(':tinker').projectDir = new File('lib/tinker')
    project(':support_v4').projectDir = new File('lib/support_v4')
    project(':remote_service').projectDir = new File('lib/remote_service')
    project(':performance').projectDir = new File('lib/performance')

}
