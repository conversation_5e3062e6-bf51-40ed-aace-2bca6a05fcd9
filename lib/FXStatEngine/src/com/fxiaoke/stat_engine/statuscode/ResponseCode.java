
package com.fxiaoke.stat_engine.statuscode;

/**
 * 向服务器请求监控上传控制信息时，服务器端返回的响应码
 * 
 * <AUTHOR>
 */
public class ResponseCode {

    /** 成功 */
    public static final int CODE_OK = 200;
    /** 服务端异常 */
    public static final int CODE_SERVER_EXCEPTION = 500;
    /** 公司不在白名单范围 */
    public static final int CODE_COMPANY_NO_IN = 501;
    /** 雇员不在白名单范围 */
    public static final int CODE_EMP_NO_IN = 502;
    /** 移动端系统不符 */
    public static final int CODE_OS_NO = 503;
    /** 移动端系统版本不符 */
    public static final int CODE_OSVERSION_NO = 504;
    /** 移动设备型号不符 */
    public static final int CODE_DEVICE_NO = 505;
    /** APP版本不符 */
    public static final int CODE_APPVERSION_NO = 506;
    /** 当前时间不能上传 */
    public static final int CODE_TIME_NO = 507;
    /** 队列满员需等待 */
    public static final int CODE_QUEUE_FULL = 508;

}
