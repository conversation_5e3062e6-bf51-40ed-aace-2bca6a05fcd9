/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.stat_engine.beans;

import java.util.regex.Pattern;

import com.alibaba.fastjson.annotation.JSONField;
import com.fxiaoke.stat_engine.utils.LogUtils;

import android.util.Log;

/**
 * 崩溃率统计需要被忽略掉时正则匹配实体Bean
 * <p/>
 * Created by xiangd on 2017/1/20.
 */
public class CrashIgnoreBean {

    /**
     * 正则表达式
     */
    @JSONField(name = "M1")
    public String regExpression;

    @JSONField(name = "M2")
    public int flags;

    public CrashIgnoreBean() {
    }

    public String getRegExpression() {
        return regExpression;
    }

    @Override
    public String toString() {
        return "CrashIgnoreBean[regEx=" + regExpression + ",flags=" + flags + "]";
    }

    /**
     * 指定的Crash是否与当前正则匹配
     *
     * @param crashInfo Crash信息
     */
    public boolean isMatchCrash(String crashInfo) {
        try {
            return Pattern.compile(regExpression, flags).matcher(crashInfo).find();
        } catch (Exception e) {
            LogUtils.w("CrashIgnoreBean", "isMatchCrash," + Log.getStackTraceString(e));
        }
        return false;
    }

}
