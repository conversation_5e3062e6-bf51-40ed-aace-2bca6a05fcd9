/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.stat_engine;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.facishare.fs.db.ADbUeEventManager;
import com.fxiaoke.stat_engine.events.StatEvent;
import com.fxiaoke.stat_engine.events.session.UeEventSession;

/**
 * Created by xiongtj on 2017/8/16.
 */
public class DbUeEventManager extends ADbUeEventManager {

    private Map<String,CountUeEventSession> cacheMap=new ConcurrentHashMap<>();

    public static void init(){
        if(manager==null){
            manager=new DbUeEventManager();
        }
    }

    @Override
    public void startTick() {
        if(!dbUeEventEnable){
            return;
        }
        String tag = getTag();
//        String keyCache=tag+"_"+Thread.currentThread().getId();
        String keyCache=""+Thread.currentThread().getId();
        CountUeEventSession cues = cacheMap.get(keyCache);
        if(cues==null){
            UeEventSession ueEventSession = new UeEventSession(StatEvent.ueEvent(tag));
            cues=new CountUeEventSession(ueEventSession);
            cacheMap.put(keyCache,cues);
        }
        cues.startTick();

    }

    @Override
    public void endTick() {
        if(!dbUeEventEnable){
            return;
        }
//        String tag = getTag();
//        String keyCache=tag+"_"+Thread.currentThread().getId();
        String keyCache=""+Thread.currentThread().getId();
        CountUeEventSession cues = cacheMap.get(keyCache);
        if(cues!=null){
            boolean end=cues.endTick();
            if(end){
                cacheMap.remove(keyCache);
            }
        }

    }

    class CountUeEventSession{
        public int count;
        public UeEventSession ueEventSession;

        public CountUeEventSession(UeEventSession ueEventSession){
            this.ueEventSession=ueEventSession;
        }

        public void startTick(){
            count++;
            if(count==1) {
                ueEventSession.startTick();
            }
        }

        public boolean endTick(){
            count--;
            if(count==0) {
                ueEventSession.endTick();
                return true;
            }
            return false;
        }

    }
}
