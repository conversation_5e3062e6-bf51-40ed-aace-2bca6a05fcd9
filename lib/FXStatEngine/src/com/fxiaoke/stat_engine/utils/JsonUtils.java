package com.fxiaoke.stat_engine.utils;

import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import android.util.Log;

public final class JsonUtils {

    private static final String TAG = JsonUtils.class.getSimpleName();

    public static JSONObject fromJSonStream(InputStream in) {
        if (in == null) {
            return null;
        }
        try {
            byte[] bt = new byte[1024];
            int len;
            StringBuilder builder = new StringBuilder();
            while ((len = in.read(bt, 0, 1024)) != -1) {
                builder.append(new String(bt, 0, len));
            }
            return new JSONObject(builder.toString());
        } catch (IOException e) {
            LogUtils.w(TAG, "IOException," + Log.getStackTraceString(e));
        } catch (JSONException e) {
            LogUtils.w(TAG, "JSONException," + Log.getStackTraceString(e));
        }

        return null;
    }

}
