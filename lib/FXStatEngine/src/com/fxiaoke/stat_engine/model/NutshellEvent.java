
package com.fxiaoke.stat_engine.model;

import com.fxiaoke.stat_engine.EngineManager;
import com.fxiaoke.stat_engine.EventStorer;
import com.fxiaoke.stat_engine.WorkTask;

/**
 * 统计事件
 *
 * <AUTHOR>
 */
public abstract class NutshellEvent {

    private static final String TAG = NutshellEvent.class.getSimpleName();

    /**
     * 统计事件类型，详见{{@link EventType}。
     */
    private int mEventType;
    /**
     * 统计事件对应的动作类型
     */
    private EventSource mEventSource;
    /**
     * 是否需要实时上传
     */
    private boolean mNeedUploadImmediately;
    /**
     * 移动数据网络下,是否能够上传
     */
    private boolean mCanUploadOnMobile;
    /**
     * 是否需要实时写出文件，不需要缓存满才写入。默认5条写一次文件,详见{@link EventStorer#CACHE_EVENT_VALUE}参数
     */
    private boolean flushNow = false;

    public NutshellEvent(int eventType, EventSource eventSource, boolean uploadImmediately, boolean canUploadOnMobile) {
        mEventType = eventType;
        mEventSource = eventSource;
        mNeedUploadImmediately = uploadImmediately;
        mCanUploadOnMobile = canUploadOnMobile;
    }

    /**
     * 该事件是否需要立即上传服务器
     *
     * @return boolean
     */
    public boolean isNeedUploadNow() {
        return mNeedUploadImmediately;
    }

    public void setNeedUploadNow(boolean needUploadNow) {
        mNeedUploadImmediately = needUploadNow;
    }

    /**
     * 该事件是否在移动数据网络下也能上传
     *
     * @return boolean
     */
    public boolean canUploadOnMobile() {
        return mCanUploadOnMobile;
    }

    public void setCanUploadOnMobile(boolean mCanUploadOnMobile) {
        this.mCanUploadOnMobile = mCanUploadOnMobile;
    }

    public void setEventType(int mEventType) {
        this.mEventType = mEventType;
    }

    public int getEventType() {
        return mEventType;
    }

    public void setActionType(EventSource mEventSource) {
        this.mEventSource = mEventSource;
    }

    public EventSource getActionType() {
        return mEventSource;
    }

    public boolean isFlushNow() {
        return flushNow;
    }

    public void setFlushNow(boolean flushNow) {
        this.flushNow = flushNow;
    }

    /**
     * 生成埋点文件名Key
     */
    public String generateFileNameKey() {
        return EngineManager.getEnvType() + "_" + mCanUploadOnMobile + "_" + mEventType + "_" + mEventSource;
    }

    /**
     * 返回的Json串中不标识账号设备信息的字段
     */
    public abstract String toJsonStr();

    /**
     * 返回的Json串中携带标识账号设备信息的字段(M1-M8)
     */
    public abstract String toJsonStr(boolean withHeader);

    public abstract StatEventEntity getEventEntity();

    @Override
    public String toString() {
        return toJsonStr();
    }

    /**
     * 提交事件
     */
    public void commit() {
        EngineManager.sendWorkTaskMsg(WorkTask.TASK_STORE_EVENT_TO_LOG_FILE, this);
    }

}
