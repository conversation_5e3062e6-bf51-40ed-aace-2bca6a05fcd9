
package com.fxiaoke.stat_engine.model.checkbean;

import java.io.Serializable;

import org.json.JSONObject;

import com.fxiaoke.stat_engine.statuscode.ResponseCode;
import com.lidroid.xutils.util.ReflectXUtils;

/**
 * 是否上传Socket日志文件的结果实体类
 *
 * <AUTHOR>
 */
public class SLogCheckResultInfo implements Serializable, ICheckInfo {

    private static final long serialVersionUID = 3007367177888254120L;
    //"M1"
    public String mCode;
    //"M2"
    public long mWaitTime;
    //"M3"
    public long mLogLevel;

    public SLogCheckResultInfo(String mCode, long mWaitTime, long mLogLevel) {
        this.mCode = mCode;
        this.mWaitTime = mWaitTime;
        this.mLogLevel = mLogLevel;
    }

    public static SLogCheckResultInfo fromJSONObject(JSONObject jsonObject) {
        if (jsonObject == null) {
            return null;
        }
        return new SLogCheckResultInfo(jsonObject.optString("M1", "0"),
                jsonObject.optLong("M2", 0),
                jsonObject.optLong("M3", 0));
    }

    @Override
    public boolean isCanUpload() {
        return ResponseCode.CODE_OK == ReflectXUtils.parseInt(mCode);
    }

    @Override
    public String toString() {
        return "SLogCheckResultInfo [mCode=" + mCode + ", mWaitTime=" + mWaitTime + ", mLogLevel=" + mLogLevel + "]";
    }

}
