disableLintTask()
apply plugin: 'com.android.library'

android {
    compileSdkVersion gradle.compileSdkVersionForLib
    buildToolsVersion gradle.buildToolsVersion
    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
//            jniLibs.srcDir 'libs'
            aidl.srcDirs=['src']
            java.srcDirs=['src']
            res.srcDirs=['res']
            assets.srcDirs = ['assets'] //多了一个assets目录
        }
    }

    compileOptions {
        sourceCompatibility gradle.javaVersion
        targetCompatibility gradle.javaVersion
    }

    defaultConfig {
//        applicationId "fxlog.fxiaoke.com.fxlog"
        minSdkVersion 14
        targetSdkVersion gradle.targetSdkVersion
        versionCode 1
        versionName gradle.versionmap.get(project.name)
    }

    packagingOptions {
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

}

dependencies {
    compileOnly files(rootProject.getRootDir().getAbsolutePath() + "/libs/httpmime-4.1.3.jar")
    compileOnly files(rootProject.getRootDir().getAbsolutePath() + "/libs/httpclient-4.5.6.jar")
//    compileOnly files(rootProject.getRootDir().getAbsolutePath() + "/libs/httpcore-4.4.10.jar")
    compileOnly files(rootProject.getRootDir().getAbsolutePath() + "/libs/fastjson_${gradle.fastjsonVersion}.jar")

    compileOnly depLibProjectWithMavenOrSource("FxLog")
    compileOnly depLibProjectWithMavenOrSource("xUtils")
    compileOnly files(rootProject.getRootDir().getAbsolutePath() + "/libs/hera-release.aar")
}
