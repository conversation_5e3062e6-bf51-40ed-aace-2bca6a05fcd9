package com.fxiaoke.fscommon.avatar.engine;

import com.fxiaoke.avatar.page.GlobalPageManager;
import com.fxiaoke.fscommon.avatar.engine.interfaces.EngineJobListener;
import com.fxiaoke.fscommon.avatar.interfaces.IAppStatusListener;
import com.fxiaoke.fscommon.avatar.threadpool.ComparableFutureTask;
import com.fxiaoke.fscommon.avatar.utils.Trace;
import com.fxiaoke.fscommon.weex.bundle.BundleInfo;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.PriorityBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class PackageDownloadManager implements EngineJobListener {

//    private Map<String, AvatarJob> jobs = new HashMap<>();
    private final static  String TAG = PackageDownloadManager.class.getSimpleName();
    private int corePoolSize = 1;
    private int maximumPoolSize = 8;
    private long keepAliveTime = 60;
    private final AtomicInteger mThreadNum = new AtomicInteger(1);

    private final BlockingQueue<Runnable> queue = new PriorityBlockingQueue<Runnable>(
            10, new Comparator<Runnable>() {
        @Override
        public int compare(Runnable t1, Runnable t2) {
            if(t1 instanceof ComparableFutureTask && t2 instanceof ComparableFutureTask){
                return ((ComparableFutureTask) t1).getPriority() - ((ComparableFutureTask) t2).getPriority();
            }
            return 0;
        }
    });

    private ThreadPoolExecutor executor = new ThreadPoolExecutor(corePoolSize,
            maximumPoolSize,
            keepAliveTime,
            TimeUnit.SECONDS,
            queue,
            runnable -> {
                Thread thread = new Thread(runnable,
                        "AvatarJobDispatcher_threadpool_" +
                                (mThreadNum.getAndIncrement()));
                return thread;
            }
    );


    private void enqueue(IEngineJob job){

        ComparableFutureTask task = new ComparableFutureTask((Callable) job, job.getPriority());

        executor.submit(task);
    }


    Map<String, IEngineJob> jobs = new HashMap<>();


    public synchronized void prepareAppByUser(String appid, IAppStatusListener lis){
        Trace.i(TAG, "prepareAppByUser appid: " + appid);
        if(!waitForExistingJob(appid, lis)){
            Trace.i(TAG, "prepareAppByUser new EngineJob: " + appid);
            EngineJob newjob = new EngineJob(appid, EngineJob.JOB_TYPE_PREPARE_USER, lis, this);
            newjob.highPriority();
            jobs.put(appid, newjob);
            enqueue(newjob);
        }
    }


    public synchronized void prepareSubPackage(String appid, String appMd5, String subid, String subMd5,  IAppStatusListener lis){
//        Log.d(TAG, Log.getStackTraceString( new Throwable()));
        String loginfo = " 【subid: " + subid + " subMd5: " + subMd5 + " appid: " + appid+ " appMd5: " + appMd5 + "】";
        Trace.i(TAG, "prepareSubPackage"+loginfo );
        if(!waitForExistingJob(appMd5 + subMd5, lis)) {
            Trace.i(TAG, "prepareSubPackage new EngineJob" +loginfo );
            SubPackageEngineJob newjob = new SubPackageEngineJob(appid, appMd5, subid, subMd5, lis, this);
            jobs.put(appMd5+subMd5, newjob);
            enqueue(newjob);
        } else{
            Trace.i(TAG, "prepareSubPackage exist EngineJob" + loginfo);
        }
    }



    public synchronized void prepareAppByPolling(List<BundleInfo> blist){
        if(blist == null){
            return;
        }

        Trace.e(TAG, "prepareAppByPolling blist: " + blist.toString());
        for(BundleInfo item:blist){
            if(!waitForExistingJob(item.name, null)){
                EngineJob newjob = new EngineJob(item, EngineJob.JOB_TYPE_PREPARE_POLLING, null ,this);
                jobs.put(item.name, newjob);
                enqueue(newjob);
            }
        }
    }


    public synchronized void prepareAppByDebug(BundleInfo item, IAppStatusListener lis){

        if(GlobalPageManager.getInstance().getActivityCountByAppid(item.name) >1){
            if(lis != null )
                lis.onReady(item.name);
            return;
        }
        Trace.e(TAG, "prepareAppByDebug item: " + item.toString());
        EngineJob newjob = new EngineJob(item, EngineJob.JOB_TYPE_PREPARE_DEBUG, lis ,this);
        jobs.put(item.name, newjob);
        enqueue(newjob);
    }




    private boolean waitForExistingJob(
            String jobId,
            IAppStatusListener cb
    ){
        IEngineJob job = jobs.get(jobId);
        Trace.e(TAG, "waitForExistingJob in: " + jobId);
        if(job != null) {
            if(cb != null){
                Trace.e(TAG, "waitForExistingJob addCallback: " + cb.hashCode());
                job.addCallback(cb);
            }
            Trace.e(TAG, "waitForExistingJob return true: " + jobId);
            return true;
        }
        Trace.e(TAG, "waitForExistingJob return false: " + jobId);
        return false;

    }


    @Override
    public synchronized void onEngineJobComplete(IEngineJob engineJob) {
        jobs.remove(engineJob.getJobId());
    }

    @Override
    public synchronized void onEngineJobCancelled(IEngineJob engineJob) {
        jobs.remove(engineJob.getJobId());
    }
}
