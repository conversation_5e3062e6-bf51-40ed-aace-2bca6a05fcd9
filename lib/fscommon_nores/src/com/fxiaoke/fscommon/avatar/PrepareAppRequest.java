package com.fxiaoke.fscommon.avatar;


import com.fxiaoke.fscommon.avatar.interfaces.IAppStatusListener;

public class PrepareAppRequest {

    final static int TYPE_PREPARE = 1;
    final static int TYPE_POLLINGAPP = 2;
    final static int TYPE_POLLINGFRAME = 3;

    String appid;
    IAppStatusListener lis;

    int type;



    public String getAppid(){
        return appid;
    }


    public void onPrepareFail(){
        if(lis != null){
            lis.onFail("", "");
        }
    }
    public void onPrepareCompleted(){
        if(lis != null){
            lis.onReady(appid);
        }
    }
}
