package com.fxiaoke.fscommon.avatar.config;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

import com.facishare.fs.pluginapi.Account;
import com.facishare.fs.pluginapi.AccountManager;
import com.fxiaoke.fscommon.avatar.AvatarManager;
import com.fxiaoke.fscommon.avatar.avatarContext;
import com.fxiaoke.fscommon.avatar.config.interfaces.IConfigManager;
import com.fxiaoke.fscommon.avatar.utils.Trace;
import com.fxiaoke.fscommon.avatar.utils.avatarFileUtils;
import com.fxiaoke.fscommon.weex.bundle.Bundle;
import com.fxiaoke.fscommon.weex.bundle.BundleInfo;
import com.fxiaoke.fscommon.weex.bundle.SubBundle;
import com.fxiaoke.fxlog.mmap.FileUtils;
import com.tencent.mmkv.MMKV;
import com.weidian.lib.hera.main.HeraService;
import com.weidian.lib.hera.utils.FileUtil;
import com.weidian.lib.hera.utils.SharePreferencesUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

public class ConfigManagerService  implements IConfigManager {

    private final  static  String TAG = ConfigManagerService.class.getSimpleName();

    public static  class ConfigManagerServiceHolder{
        public static ConfigManagerService ins = new ConfigManagerService();
    }

    private HashMap<String, Boolean> updatelist = new HashMap<>();

    public static ConfigManagerService getInstance(){
        return ConfigManagerServiceHolder.ins;
    }
    private  MMKV kv;
    private  MMKV debugKV;

    private boolean isInit;


    @Override
    public synchronized void init(Context context) {
        if(!isInit){
            isInit = true;
            if(context == null){
                context = avatarContext.getInstance().getContext();
            }
            MMKV.initialize(context);
            kv = MMKV.mmkvWithID("avatarkv");
            debugKV = MMKV.mmkvWithID("avatarDebugKv");
        }
    }


    public synchronized List<BundleInfo> getAllDBConfig() {
        List<BundleInfo> list = new ArrayList<>();
        String[] allkeys = kv.allKeys();
        if(allkeys == null){
            return list;
        }
        for(String key : allkeys){
            Trace.d(TAG, "exist Config: " + key);
            if(!key.startsWith("framework") ){
                BundleInfo temp = kv.decodeParcelable(key, BundleInfo.class);
                temp.configKey = key;
                list.add(temp);
            }
        }
        Trace.d(TAG, "getAllDBconfig: " + list.toString());
        return list;
    }


    @Override
    public synchronized List<BundleInfo> getAllConfig() {
        List<BundleInfo> list = new ArrayList<>();
        String[] allkeys = kv.allKeys();
        if(allkeys == null){
            return list;
        }
        String accountPrefix = getAccountPrefix();
        if(TextUtils.isEmpty(accountPrefix)){
            return list;
        }
        for(String key : allkeys){
            Trace.d(TAG, "exist Config: " + key);
            if(key.startsWith("framework") || key.startsWith(accountPrefix)){
                BundleInfo temp = kv.decodeParcelable(key, BundleInfo.class);
                temp.configKey = key;
                try{
                    if(key.startsWith("framework")){
                        temp.version = key.split("_")[1];
                    }
                }catch (Exception e){

                }
                list.add(temp);
            }
        }
//        Trace.d(TAG, "getAllConfig: " + list.toString());
        return list;
    }

    @Override
    public void clearFrameworkConfig() {
        List<BundleInfo> list = new ArrayList<>();
        String[] allkeys = kv.allKeys();
        if(allkeys == null){
            return;
        }
        for(String key : allkeys){
            if(key.startsWith("framework")){
                Trace.d(TAG, "clearFrameworkConfig: " + key);
                kv.remove(key);
            }
        }
    }


    @Override
    public synchronized BundleInfo getConfig(String appid) {
        return findConfigByAppid(appid, wrapKey(appid));
    }

    @Override
    public BundleInfo getFramworkConfig(String version) {
       String frame = "framework" + "_" + version;
       BundleInfo info =  findConfigByAppid(frame, frame);
       if(info != null){
//           Trace.e(TAG, "getFramworkConfig result: " + info.toString());
       }
       return info;
    }


    private BundleInfo findConfigByAppid(String appid, String wrappedAppid){
        BundleInfo info = kv.decodeParcelable(wrappedAppid, BundleInfo.class, null);
//        if(info == null){
//            info = kv.decodeParcelable(appid, BundleInfo.class, null);
//        }
        if(info != null){
//            Trace.d(TAG, "getConfig: " + info.toString());
        }
        else
            Trace.e(TAG, "getConfig result: not found config of appid:  " + appid);
        return info;
    }


    @Override
    public synchronized void addConfig(BundleInfo info) {
        kv.encode(wrapKey(info), info);
        Trace.d(TAG, "addConfig key: " + wrapKey(info) + " detail: " + info.toString());
    }

    @Override
    public synchronized void updateConfig(BundleInfo newinfo) {

        Trace.e(TAG, "updateConfig key: " + wrapKey(newinfo) + " detail: " + newinfo.toString());
        BundleInfo info = kv.decodeParcelable(wrapKey(newinfo), BundleInfo.class, null);
        if(info == null){
            updatelist.put(newinfo.name, true);
            addConfig(newinfo);
        }else{
            updatelist.put(newinfo.name, true);
            kv.encode(wrapKey(newinfo), newinfo);
        }

    }


    public synchronized void updateSubPackageConfig(BundleInfo mainPackage, BundleInfo subPackage) {

        Trace.e(TAG, "updateSubPackageConfig key: " + wrapKey(mainPackage) + " mainPackage: " +
                mainPackage.toString() + " subpackage: " + subPackage.toString());
        BundleInfo info = kv.decodeParcelable(wrapKey(mainPackage), BundleInfo.class, null);
        if(info == null){
            Trace.e(TAG, "updateSubPackageConfig info == null");
        }else{
            if(info.subList != null){
                SubBundle target = null;
                Trace.e(TAG, "updateSubPackageConfig mainPackage.subList != null");
                for(SubBundle item :info.subList){
                    if(item.bundleName.equals(subPackage.name)){
                        target = item;
                        break;
                    }
                }
                if(target != null){
                    Trace.e(TAG, "updateSubPackageConfig find target");
                    target.md5 = subPackage.md5;
                }else{
                    Trace.e(TAG, "updateSubPackageConfig new target then add");
                    SubBundle sb = new SubBundle();
                    sb.bundleName = subPackage.name;
                    sb.md5 = subPackage.md5;
                    info.subList.add(sb);
                }
            }else{
                Trace.e(TAG, "updateSubPackageConfig mainPackage.subList == null");
                info.subList = new ArrayList<>();
                SubBundle sb = new SubBundle();
                sb.bundleName = subPackage.name;
                sb.md5 = subPackage.md5;
                info.subList.add(sb);
            }

            updatelist.put(info.name, true);

            Trace.e(TAG, "updateSubPackageConfig result: " + info.toString());
            kv.encode(wrapKey(info), info);
        }

    }


    public boolean appDirty(String appid){
        if(updatelist.get(appid) != null){
            return updatelist.get(appid);
        }
        return true;
    }

    @Override
    public synchronized void delConfig(BundleInfo info) {

    }


    private String wrapKey(String key){
        return getAccountPrefix() + key;
    }


    private String wrapKey(BundleInfo info){

        if(info.name.equals("framework")){

            if(TextUtils.isEmpty(info.version)){
                Trace.e(TAG, "wrapKey framework version should not is empty: " + info.toString());
                return "";
            }

            return info.name +  "_" + info.version;
        }

        if(!TextUtils.isEmpty(info.accountPrefix)){
            return info.accountPrefix + info.name;
        }
        return getAccountPrefix() + info.name;
    }


    public String getAccountPrefix() {
        Account mAccount = AccountManager.getAccount();
//        Trace.e(TAG, "getAccountPrefix: islogin:" + mAccount.isLogin()+" eaName:"+mAccount.getEnterpriseName()+" ei:"+mAccount.getEnterpriseId());
        if (mAccount != null && mAccount.isLogin()) {
            String enterpriseId = String.valueOf(mAccount.getEnterpriseId());
            String employeeId = String.valueOf(mAccount.getEmployeeId());
            return enterpriseId + "_" + employeeId + "_";
        }
        return "";

    }


    public void setDebugAppId(String appid, String url, String host, String project){
        BundleInfo info = new BundleInfo();
        info.name = appid;
        info.url = url;
        Trace.e(TAG, "setDebugAppId: " + host);

        debugKV.encode(appid, info);
        debugKV.encode(appid+"_dbghost", host);
        debugKV.encode(appid+"_fileurl", url);
        debugKV.encode(appid+"_project", project);
        saveSP(host, appid);

    }


    public BundleInfo getDebugApp(String appid){
        return debugKV.decodeParcelable(appid,BundleInfo.class, null);
    }


    public void removeAppConfig(BundleInfo info){
        kv.remove(wrapKey(info));
    }

    public void removeDebugApp(String appid){
        debugKV.remove(appid);
        try{
		//关闭本地调试仍然使用包
            BundleInfo info = getConfig(appid);
            kv.remove(wrapKey(info));
        }catch (Exception e){

        }
    }

    @Override
    public String getDebugHost(String appid) {
        return debugKV.decodeString(appid +"_dbghost", "");
    }

    public String getDebugFileUrl(String appid) {
        return debugKV.decodeString(appid +"_fileurl", "");
    }

    public String getDebugProject(String appid) {
        return debugKV.decodeString(appid +"_project", "");
    }


    private void saveSP(String host, String appid){
       SharedPreferences  sp =  SharePreferencesUtil.getSharedPreference(
               avatarContext.getInstance().getContext(), "debugapp");
        String appids = sp.getString("debugAppIds", "");
        if(TextUtils.isEmpty(appids)){
            appids = appid;
        }else{
            appids = appids + "," +appid;
        }
        SharedPreferences.Editor  editor = sp.edit();
        editor.putString("debugAppIds", appids);
        editor.putString("debugHost", host);
        editor.commit();
    }

    public String getDebugHost(){
        SharedPreferences  sp =  SharePreferencesUtil.getSharedPreference(
                avatarContext.getInstance().getContext(), "debugapp");
        return  sp.getString("debugHost", "");
    }
    public String getDebugAppIds(){
        SharedPreferences  sp =  SharePreferencesUtil.getSharedPreference(
                avatarContext.getInstance().getContext(), "debugapp");
        return  sp.getString("debugAppIds", "");
    }

    public String[] getDebugAppIdArray(){
        SharedPreferences  sp =  SharePreferencesUtil.getSharedPreference(
                avatarContext.getInstance().getContext(), "debugapp");
        return  sp.getString("debugAppIds", "").split(",");
    }

    public void removeDebugAppId(String appid){
        SharedPreferences  sp =  SharePreferencesUtil.getSharedPreference(
                avatarContext.getInstance().getContext(), "debugapp");
        String[] all = sp.getString("debugAppIds", "").split(",");
        ArrayList<String> idLists = new ArrayList<>();
        Collections.addAll(idLists, all);
        idLists.remove(new String(appid));

        String appids = "";
        for(int i = 0; i < idLists.size(); i++){
            appids = appids + idLists.get(i) ;
            if(i != idLists.size()-1){
                appids  = appids + ",";
            }
        }
        SharedPreferences.Editor  editor = sp.edit();
        editor.putString("debugAppIds", appids);
        editor.commit();

        if(appid.contains("@")) {
            String subId = appid.substring(0, appid.indexOf("@"));
            String bundleId = appid.substring(appid.indexOf("@") + 1);
            BundleInfo info = ConfigManagerService.getInstance().getConfig(bundleId);


            SubBundle target = null;
            Trace.e(TAG, "updateSubPackageConfig mainPackage.subList != null");
            for(SubBundle item :info.subList){
                if(item.bundleName.equals(subId)){
                    target = item;
                    break;
                }
            }
            if(target != null){
                String dir = avatarFileUtils.getAppSoureDir(info) +
                        File.separator + "subpackage" + File.separator + subId +
                        File.separator + target.md5;

                FileUtils.deleteDir(new File(dir));

            }

            if(info != null) {
                HeraService.cleardirtyCache(Arrays.asList(bundleId));
            }
        }
    }

    public void clearDB(){
        kv.clear();
        debugKV.clear();
    }
}

