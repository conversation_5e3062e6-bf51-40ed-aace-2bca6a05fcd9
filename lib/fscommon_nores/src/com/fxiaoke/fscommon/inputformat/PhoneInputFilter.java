/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.fscommon.inputformat;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.common_utils.ToastUtils;

import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextUtils;

/**
 * Created by zhouz on 2017/12/13.
 */

public class PhoneInputFilter implements InputFilter {
    @Override
    public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
        String sourceStr = source.toString();
        if (start == end) {
            // 处理删除情况
            return "";
        } else if (!TextUtils.isEmpty(sourceStr)
                && sourceStr.length() == 1
                && !sourceStr.matches("[0-9\\-\\+,;]")) {
            //单个字符输入的情况
            ToastUtils.show(I18NHelper.getText("qx.cross_out_profile_person_info.des.input_correct_phone_number")/* 请输入正确格式的手机号码 */);
            return dest.subSequence(dstart, dend);
        } else {
            String tmp = sourceStr.replaceAll("[^0-9\\-\\+,;]", "");
            return dest.subSequence(dstart, dend) + tmp;
        }
    }
}
