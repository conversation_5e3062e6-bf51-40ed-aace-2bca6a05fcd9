package com.fxiaoke.fscommon.share;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.encryption.IOUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.pluginapi.HostFunction;
import com.facishare.fs.pluginapi.jsapi.JsApiBroadcastHelper;
import com.fxiaoke.fscommon.files.FileHelper;
import com.fxiaoke.fscommon.files.FileUtil;
import com.fxiaoke.fscommon.image.BitmapHelper;
import com.fxiaoke.fscommon.share.events.WXResponseEvent;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.fxiaoke.fxlog.FCLog;
import com.nostra13.universalimageloader.core.DisplayImageOptions;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.assist.ImageSize;
import com.nostra13.universalimageloader.core.listener.SimpleImageLoadingListener;
import com.nostra13.universalimageloader.utils.IoUtils;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.modelbiz.ChooseCardFromWXCardPackage;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.tencent.mm.opensdk.modelmsg.SendAuth;
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX;
import com.tencent.mm.opensdk.modelmsg.WXAppExtendObject;
import com.tencent.mm.opensdk.modelmsg.WXEmojiObject;
import com.tencent.mm.opensdk.modelmsg.WXFileObject;
import com.tencent.mm.opensdk.modelmsg.WXImageObject;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.modelmsg.WXMiniProgramObject;
import com.tencent.mm.opensdk.modelmsg.WXMusicObject;
import com.tencent.mm.opensdk.modelmsg.WXTextObject;
import com.tencent.mm.opensdk.modelmsg.WXVideoObject;
import com.tencent.mm.opensdk.modelmsg.WXWebpageObject;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.security.MessageDigest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import androidx.core.content.FileProvider;
import okhttp3.Headers;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * Created by wbb on 2018/3/12.
 * 微信分享工具类
 */
public class WXShareHelper {
	private static final String TAG="WXShareHelper";
	public static final String LAUNCH_MINI_APP_CALLBACK_KEY = "launchWXMiniProgramCallback";
	public static final String CHOOSE_INVOICE_FROM_WX_CALLBACK = "chooseInvoiceFromWXCallback";
	//调用发送到微信接口监听器
	public interface SendToWXListener{
		void onSend(boolean success);
	}

	public interface WXCommonCallback{
		void onSuccess(Object data);
		void onFailed(int errCode, String errMessage);
		void onCanceled();
	}


	public enum MiniProgramType{
		RELEASE(I18NHelper.getText("common.share_weichat.guide.release_version")/* 正式版 */,0),
		TEST(I18NHelper.getText("common.share_weichat.guide.test_version")/* 测试版 */,1),
		PREVIEW(I18NHelper.getText("common.share_weichat.guide.preview_version")/* 预览版 */,2);
		public String name;
		public int value;
		MiniProgramType(String name,int value){
			this.value=value;
		}
	}
	private static final int TIMELINE_SUPPORTED_VERSION = 0x21020001;
	final int THUMB_SIZE=150;
	public static final String WX_APPID;

	private static final int SHARE_FILE_LIMIT_MB = 10;
	private static final int SHARE_FILE_LIMIT_SIZE = SHARE_FILE_LIMIT_MB * 1024 * 1024;

	static {
		String appId= HostFunction.getInstance().getWeixinAppId();
		Context app = HostFunction.getInstance().getApp();
		if(TextUtils.equals(app.getPackageName(),"com.facishare.fsneice")){
			appId="wxfd4559cb5ab411d0";//纷享销客内测包微信appId
		}else if(TextUtils.equals(app.getPackageName(),"com.mengniu.msv")){
			appId="wx42a01a374d25dab4";
		}

		WX_APPID=appId;
	}
	
	private IWXAPI wxapi=null;
	private ExecutorService mExecutorService=Executors.newCachedThreadPool();
	private HashMap<String, WXCommonCallback> wxCommonCallbackHashMap;
	private BroadcastReceiver mWXResponseBroadcast;
	private Context mContext;
	
	public WXShareHelper(Context context)
	{
		mContext=context;
		FCLog.d(TAG,"wx_appid="+WX_APPID);
		wxapi= WXAPIFactory.createWXAPI(context.getApplicationContext(), WX_APPID,false);
	}


	public void handleIntent(Intent intent, IWXAPIEventHandler handler)
	{
		wxapi.handleIntent(intent, handler);
	}

	/**
	 * 判断微信是否安装
	 * @return
	 */
	public boolean isWXAppInstalled()
	{
		return wxapi.isWXAppInstalled();
	}

	/**
	 * 判断微信是否支持朋友圈功能
	 * @return
	 */
	public boolean isWXTimelineSupported(){
		int wxSdkVersion=wxapi.getWXAppSupportAPI();
		return wxSdkVersion>=TIMELINE_SUPPORTED_VERSION?true:false;
	}

	/**
	 * 打开微信
	 * @return
	 */
	public boolean openWXApp(){
		return wxapi.openWXApp();
	}

	/**
	 * 把当前APP注册到微信
	 */
	public void registerAppToWX(){
		wxapi.registerApp(WX_APPID);
	}

	/**
	 * 把当前APP从微信反注册
	 */
	public void unregisterAppFromWX()
	{
		wxapi.unregisterApp();
	}

	private void initWXCallbackThings(){
		wxCommonCallbackHashMap = new HashMap<>();
		mWXResponseBroadcast=JsApiBroadcastHelper.registerBroadcast(mContext, JsApiBroadcastHelper.ACTION_WX_RESPONSE_EVENT, data -> {
			WXResponseEvent wxResponseEvent=data instanceof WXResponseEvent?(WXResponseEvent)data:null;
			if(wxResponseEvent==null) return;
			if(wxCommonCallbackHashMap == null) return;

			WXCommonCallback callback = null;
			String callbackKey = null;
			if ("requestWXOAuth".equals(wxResponseEvent.source)) {
				callbackKey = wxResponseEvent.state;
			} else if ("launchWXMiniProgram".equals(wxResponseEvent.source)) {
				callbackKey = LAUNCH_MINI_APP_CALLBACK_KEY;
			} else if ("chooseInvoiceFromWX".equals(wxResponseEvent.source)) {
				callbackKey = CHOOSE_INVOICE_FROM_WX_CALLBACK;
			}
			callback = wxCommonCallbackHashMap.get(callbackKey);

			if(callback != null){
				wxCommonCallbackHashMap.remove(callbackKey);
				switch(wxResponseEvent.errCode){
					case BaseResp.ErrCode.ERR_OK:
						callback.onSuccess(wxResponseEvent);
						break;
					case BaseResp.ErrCode.ERR_USER_CANCEL:
						callback.onCanceled();
						break;
					case BaseResp.ErrCode.ERR_AUTH_DENIED:
						callback.onFailed(BaseResp.ErrCode.ERR_AUTH_DENIED, TextUtils.isEmpty(wxResponseEvent.errStr)? "err_auth_denied" : wxResponseEvent.errStr);
						break;
					case BaseResp.ErrCode.ERR_UNSUPPORT:
						callback.onFailed(BaseResp.ErrCode.ERR_UNSUPPORT, TextUtils.isEmpty(wxResponseEvent.errStr)? "err_unsupport" : wxResponseEvent.errStr);
						break;
					default:
						callback.onFailed(BaseResp.ErrCode.ERR_COMM, TextUtils.isEmpty(wxResponseEvent.errStr)? "err_unknown" : wxResponseEvent.errStr);
						break;
				}
			}
		});
	}

	private boolean checkWXAppInstalled(){
		if(FCLog.isFsPlayPkg()){
			ToastUtils.show(I18NHelper.getText("common.share_weichat.not_support")/* Google Play包不支持微信分享 */);
			return false;
		}

		if(!isWXAppInstalled()){
			ToastUtils.show(I18NHelper.getText("common.share_weichat.guide.need_weichat_app")/* 请安装微信 */);
			return false;
		}
		return true;
	}

	/**
	 * 发送文本消息到微信
	 * @param text 文本消息
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public boolean sendTextToWX(String text,boolean sendToFriend)
	{
		if(!checkWXAppInstalled()) return false;
		if(TextUtils.isEmpty(text)) return false;
		WXTextObject txtObj=new WXTextObject();
		txtObj.text=text;
		
		WXMediaMessage msg = new WXMediaMessage();
		msg.mediaObject = txtObj;

		SendMessageToWX.Req req = new SendMessageToWX.Req();
		req.transaction = buildTransaction("text");
		req.message = msg;
		req.scene = sendToFriend ? SendMessageToWX.Req.WXSceneSession : SendMessageToWX.Req.WXSceneTimeline;
		return wxapi.sendReq(req);
	}

	/**
	 * !!! 注意： 这个接口不再推荐使用，请使用 sendImage(Bitmap bm, String imageLocalPath, boolean sendToFriend) !!!
	 * 发送图片消息到微信
	 * @param bm 位图对象
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	@Deprecated
	public boolean sendImage(Bitmap bm,boolean sendToFriend){
		if(!checkWXAppInstalled()) return false;
		if(bm==null) return false;
		WXImageObject imgObj = new WXImageObject(bm);

		WXMediaMessage msg = new WXMediaMessage();
		msg.mediaObject = imgObj;

		Bitmap thumb = BitmapHelper.getImageThumbnailBySize(bm, 1024*32);
//		Bitmap thumb = BitmapHelper.getImageThumbnail(bm, THUMB_SIZE);

		msg.setThumbImage(thumb);

		SendMessageToWX.Req req = new SendMessageToWX.Req();
		req.transaction = buildTransaction("img");
		req.message = msg;
		req.scene = sendToFriend ? SendMessageToWX.Req.WXSceneSession : SendMessageToWX.Req.WXSceneTimeline;
		boolean result=wxapi.sendReq(req);
		if(!thumb.isRecycled()){
			thumb.recycle();
		}
		return result;
	}

	/**
	 * 发送图片消息到微信最新接口，可以避免TransactionTooLargeException
	 * @param bm 位图对象
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public boolean sendImage(Bitmap bm, String imageLocalPath, boolean sendToFriend){
		if(!checkWXAppInstalled()) return false;
		if(bm==null) return false;
		if(TextUtils.isEmpty(imageLocalPath)) return false;

		String fileSharePath = getShareFilePath(imageLocalPath);
		if(fileSharePath == null) {
			ToastUtils.show(I18NHelper.getText("qx.file_preview.des.file_unexist")/* 文件不存在 */);
			return false;
		}

		WXImageObject imgObj = new WXImageObject();
		imgObj.setImagePath(fileSharePath);

		WXMediaMessage msg = new WXMediaMessage();
		msg.mediaObject = imgObj;

//		Bitmap thumb = BitmapHelper.getImageThumbnail(bm, THUMB_SIZE);
		Bitmap thumb = compressBitmap(bm);
		msg.setThumbImage(thumb);

		SendMessageToWX.Req req = new SendMessageToWX.Req();
		req.transaction = buildTransaction("img");
		req.message = msg;
		req.scene = sendToFriend ? SendMessageToWX.Req.WXSceneSession : SendMessageToWX.Req.WXSceneTimeline;
		boolean result=wxapi.sendReq(req);
		if(!thumb.isRecycled()){
			thumb.recycle();
		}
		return result;
	}

	private Bitmap compressBitmap(Bitmap bitmap){
		Bitmap thumb = BitmapHelper.getImageThumbnailBySize(bitmap, 1024*32);
		if(thumb == null){
			thumb = BitmapHelper.getImageThumbnail(bitmap, THUMB_SIZE);
		}
		return thumb;
	}


	/**
	 * 发送图片消息到微信
	 * @param filePath 本地文件路径
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public boolean sendImageToWX(String filePath, boolean sendToFriend)
	{
		if(!checkWXAppInstalled()) return false;
		if(filePath.isEmpty() || filePath==null) return false;
		File file=new File(filePath);
		if(!file.exists()) return false;

		Bitmap bm = BitmapFactory.decodeFile(filePath);
		boolean result=sendImage(bm, filePath, sendToFriend);
		if(!bm.isRecycled()){
			bm.recycle();
		}
		return result;
	}

	/**
	 * 发送网络图片消息到微信
	 * @param imageUrl 网络图片的url
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public void sendNetworkImageToWXAsync(final String imageUrl, final boolean sendToFriend,final SendToWXListener listener)
	{
		if(!checkWXAppInstalled()) return;
		if(TextUtils.isEmpty(imageUrl)) {
			if(listener!=null)
				listener.onSend(false);
			return;
		}
		mExecutorService.execute(new Runnable() {
			@Override
			public void run() {

				ImageLoader.getInstance().loadImage(imageUrl, (ImageSize)null, new DisplayImageOptions.Builder().cacheOnDisk(true).build(),  new SimpleImageLoadingListener(){
					@Override
					public void onLoadingComplete(String imageUri, View view, Bitmap loadedImage, String fileLocalPath, Map<String, String> headerMap){
						if(loadedImage == null) {
							if(listener != null)
								listener.onSend(false);
							return;
						}

						boolean success = sendImage(loadedImage, fileLocalPath, sendToFriend);
						if(!loadedImage.isRecycled()){
							loadedImage.recycle();
						}
						if(listener!=null)
							listener.onSend(success);
					}
				});
			}
		});
	}
	public String getFileUri(Context context, File file) {
		if (file == null || !file.exists()) {
			return null;
		}

		Uri contentUri = FileProvider.getUriForFile(context,
				FCLog.getHostPkgName()+".fileprovider",  // 要与`AndroidManifest.xml`里配置的`authorities`一致，假设你的应用包名为com.example.app
				file);
		// 授权给微信访问路径
		context.grantUriPermission("com.tencent.mm",  // 这里填微信包名
				contentUri, Intent.FLAG_GRANT_READ_URI_PERMISSION);
		return contentUri.toString();   // contentUri.toString() 即是以"content://"开头的用于共享的路径
	}

	// 判断微信版本是否为7.0.13及以上
	public boolean checkVersionValid(Context context) {
		return wxapi.getWXAppSupportAPI() >= 0x27000D00;
	}

	// 判断Android版本是否7.0及以上
	public boolean checkAndroidNotBelowN() {
		return android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N;
	}

	private String getShareFilePath(String filePath){

		File file=new File(filePath);
		if(!file.exists()) {
			return null;
		}

		String shareFilePath = filePath;
		if (checkVersionValid(mContext) && checkAndroidNotBelowN()) {
			// 使用contentPath作为文件路径进行分享
			File targetDir = new File(mContext.getExternalFilesDir(null) + "/shareData/");
			if (!targetDir.exists()) {
				targetDir.mkdirs();
			}
			String providerContentFileName = file.getName();
			try {
				File providerContentFile = new File(targetDir, providerContentFileName);
				FileUtil.copyFile(file, providerContentFile);
				String contentPath = getFileUri(mContext, providerContentFile);
				if (!TextUtils.isEmpty(contentPath)) {
					shareFilePath = contentPath;
				}
			} catch (Exception e) {
				shareFilePath = filePath;
				FCLog.e(TAG, "sendFileToWX by fileprovider is failed...");
				FCLog.e(TAG, Log.getStackTraceString(e));
			}
		}
		return shareFilePath;
	}
	/**
	 * 发送文件到微信
	 * @param filePath 本地文件路径
	 * @param title 文件标题
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public boolean sendFileToWX(String filePath,String title,boolean sendToFriend){
		if(!checkWXAppInstalled()) return false;
		if(TextUtils.isEmpty(filePath) || TextUtils.isEmpty(title)) return false;
		String shareFilePath = getShareFilePath(filePath);
		if(shareFilePath == null){
			ToastUtils.show(I18NHelper.getText("qx.file_preview.des.file_unexist")/* 文件不存在 */);
			return false;
		}
		// 使用原有方式传递文件路径进行分享
		WXFileObject fileObj = new WXFileObject();
		fileObj.filePath = shareFilePath;

		WXMediaMessage msg = new WXMediaMessage();
		msg.mediaObject = fileObj;
		msg.title=title;

		SendMessageToWX.Req req = new SendMessageToWX.Req();
		req.transaction = buildTransaction("file");
		req.message = msg;
		req.scene = sendToFriend ? SendMessageToWX.Req.WXSceneSession : SendMessageToWX.Req.WXSceneTimeline;
		boolean isSuccess = wxapi.sendReq(req);
		if (!isSuccess) {
			String shareFileErrorTip = I18NHelper.getText("xt.send_enter_prise_notify_task.text.share_file_error", "分享文件失败");
			ToastUtils.show(shareFileErrorTip);
		}
		return isSuccess;
	}

	/**
	 * 发送emoji表情到微信
	 * @param filePath emoji表情图片路径
	 * @param title 微信卡片消息的标题
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public boolean sendEmojiToWX(String filePath,String title,boolean sendToFriend)
	{
		if(!checkWXAppInstalled()) return false;
		if(TextUtils.isEmpty(filePath) || TextUtils.isEmpty(title)) return false;
		File file=new File(filePath);
		if(!file.exists()) return false;
		WXEmojiObject emojiObj = new WXEmojiObject();
		emojiObj.emojiPath=filePath;
		
		WXMediaMessage msg = new WXMediaMessage();
		msg.mediaObject = emojiObj;
		msg.title=title;
		
		Bitmap bmp = BitmapFactory.decodeFile(filePath);
//		Bitmap thumb = BitmapHelper.getImageThumbnail(bmp, THUMB_SIZE);
		Bitmap thumb = compressBitmap(bmp);
		bmp.recycle();
		msg.setThumbImage(thumb);
		
		SendMessageToWX.Req req = new SendMessageToWX.Req();
		req.transaction = buildTransaction("emoji");
		req.message = msg;
		req.scene = sendToFriend ? SendMessageToWX.Req.WXSceneSession : SendMessageToWX.Req.WXSceneTimeline;
		boolean result=wxapi.sendReq(req);
		if(!bmp.isRecycled()){
			bmp.recycle();
		}
		if(!thumb.isRecycled()){
			thumb.recycle();
		}
		return result;
	}

	/**
	 * 发送音乐消息到微信
	 * @param url 音乐文件的url
	 * @param title 音乐的标题
	 * @param description 音乐的描述
	 * @param thumbnail 音乐图片的缩略图
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public boolean sendMusicToWX(String url,String title,String description, Bitmap thumbnail, boolean sendToFriend)
	{
		if(!checkWXAppInstalled()) return false;
		if(TextUtils.isEmpty(url) || TextUtils.isEmpty(title) || thumbnail==null) return false;
		WXMusicObject musicObj = new WXMusicObject();
		musicObj.musicUrl=url;
		
		WXMediaMessage msg = new WXMediaMessage();
		msg.mediaObject = musicObj;
		msg.title=title;
		msg.description=description;
		Bitmap thumbnail2=null;
		if(thumbnail.getWidth()>THUMB_SIZE || thumbnail.getHeight()>THUMB_SIZE){
			thumbnail2=BitmapHelper.getImageThumbnail(thumbnail,THUMB_SIZE);
		}
		if(thumbnail2!=null){
			msg.setThumbImage(thumbnail2);
		}
		else{
			msg.setThumbImage(thumbnail);
		}
		
		SendMessageToWX.Req req = new SendMessageToWX.Req();
		req.transaction = buildTransaction("music");
		req.message = msg;
		req.scene = sendToFriend ? SendMessageToWX.Req.WXSceneSession : SendMessageToWX.Req.WXSceneTimeline;
		boolean result=wxapi.sendReq(req);
		if(thumbnail2!=null && !thumbnail2.isRecycled()){
			thumbnail2.recycle();
		}
		return result;
	}

	/**
	 * 发送音乐消息到微信
	 * @param url 音乐文件的url
	 * @param title 音乐的标题
	 * @param description 音乐的描述
	 * @param thumbnailUrl 音乐图片的缩略图url
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public void sendMusicToWXAsync(final String url,final String title,final String description, final String thumbnailUrl, final boolean sendToFriend, final SendToWXListener listener)
	{
		if(!checkWXAppInstalled()) return;
		if(TextUtils.isEmpty(url) || TextUtils.isEmpty(title) || TextUtils.isEmpty(thumbnailUrl)) {
			if(listener!=null){
				listener.onSend(false);
			}
			return;
		}
		mExecutorService.execute(new Runnable() {
			@Override
			public void run() {
				Bitmap thumbnail=ImageLoader.getInstance().loadImageSync(thumbnailUrl, new ImageSize(THUMB_SIZE,THUMB_SIZE));
				if(thumbnail==null){
					if(listener!=null){
						listener.onSend(false);
					}
					return;
				}
				boolean success=sendMusicToWX(url,title,description,thumbnail,sendToFriend);
				if(!thumbnail.isRecycled()){
					thumbnail.recycle();
				}
				if(listener!=null){
					listener.onSend(success);
				}
			}
		});
	}

	/**
	 * 发送视频到微信
	 * @param url 视频的url
	 * @param title 视频的标题
	 * @param description 视频的描述
	 * @param thumbnail 视频文件的缩略图
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public boolean sendVideoToWX(String url,String title,String description, Bitmap thumbnail, boolean sendToFriend)
	{
		if(!checkWXAppInstalled()) return false;
		if(TextUtils.isEmpty(url) || TextUtils.isEmpty(title) || thumbnail==null) return false;
		WXVideoObject videoObj = new WXVideoObject();
		videoObj.videoUrl=url;
		
		WXMediaMessage msg = new WXMediaMessage();
		msg.mediaObject = videoObj;
		msg.title=title;
		msg.description=description;
		Bitmap thumbnail2=null;
		if(thumbnail.getWidth()>THUMB_SIZE || thumbnail.getHeight()>THUMB_SIZE){
			thumbnail2=BitmapHelper.getImageThumbnail(thumbnail,THUMB_SIZE);
		}
		if(thumbnail2!=null){
			msg.setThumbImage(thumbnail2);
		}
		else{
			msg.setThumbImage(thumbnail);
		}
		
		SendMessageToWX.Req req = new SendMessageToWX.Req();
		req.transaction = buildTransaction("video");
		req.message = msg;
		req.scene = sendToFriend ? SendMessageToWX.Req.WXSceneSession : SendMessageToWX.Req.WXSceneTimeline;
		boolean result=wxapi.sendReq(req);
		if(thumbnail2!=null && !thumbnail2.isRecycled()){
			thumbnail2.recycle();
		}
		return result;
	}

	/**
	 * 发送视频到微信
	 * @param url 视频的url
	 * @param title 视频的标题
	 * @param description 视频的描述
	 * @param thumbnailUrl 视频文件的缩略图url
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public void sendVideoToWXAsync(final String url, final String title, final String description, final String thumbnailUrl, final boolean sendToFriend, final SendToWXListener listener)
	{
		if(!checkWXAppInstalled()) return;
		if(TextUtils.isEmpty(url) || TextUtils.isEmpty(title) || thumbnailUrl==null) {
			if(listener!=null){
				listener.onSend(false);
			}
			return;
		}
		mExecutorService.execute(new Runnable() {
			@Override
			public void run() {
				Bitmap thumbnail=ImageLoader.getInstance().loadImageSync(thumbnailUrl, new ImageSize(THUMB_SIZE,THUMB_SIZE));
				if(thumbnail==null){
					if(listener!=null){
						listener.onSend(false);
					}
					return;
				}
				boolean success=sendVideoToWX(url,title,description,thumbnail,sendToFriend);
				if(!thumbnail.isRecycled()){
					thumbnail.recycle();
				}
				if(listener!=null){
					listener.onSend(success);
				}
			}
		});
	}

	/**
	 * 发送网页到微信
	 * @param url 网页的url
	 * @param title 网页的标题
	 * @param description 网页的描述
	 * @param thumbnail 网页的缩略图
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public boolean sendWebPageToWX(String url,String title,String description, Bitmap thumbnail, boolean sendToFriend)
	{
		if(!checkWXAppInstalled()) return false;
		if(TextUtils.isEmpty(url) || TextUtils.isEmpty(title) || thumbnail==null) return false;
		WXWebpageObject webpageObj = new WXWebpageObject();
		webpageObj.webpageUrl=url;

		WXMediaMessage msg = new WXMediaMessage();
		msg.mediaObject = webpageObj;
		msg.title=title;
		msg.description=description;
		Bitmap thumbnail2=null;
		if(thumbnail.getWidth()>THUMB_SIZE || thumbnail.getHeight()>THUMB_SIZE){
			thumbnail2=BitmapHelper.getImageThumbnail(thumbnail,THUMB_SIZE);
		}
		if(thumbnail2!=null){
			msg.setThumbImage(thumbnail2);
		}
		else{
			msg.setThumbImage(thumbnail);
		}

		SendMessageToWX.Req req = new SendMessageToWX.Req();
		req.transaction = buildTransaction("webpage");
		req.message = msg;
		req.scene = sendToFriend ? SendMessageToWX.Req.WXSceneSession : SendMessageToWX.Req.WXSceneTimeline;
		boolean result=wxapi.sendReq(req);
		if(thumbnail2!=null && !thumbnail2.isRecycled()){
			thumbnail2.recycle();
		}
		return result;
	}

	/**
	 * 发送网页到微信
	 * @param url 网页的url
	 * @param title 网页的标题
	 * @param description 网页的描述
	 * @param thumbnailUrl 网页的缩略图url
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public void sendWebPageToWXAsync(final String url, final String title, final String description,
									final String thumbnailUrl, final boolean sendToFriend,
									final SendToWXListener listener)
	{
		if(!checkWXAppInstalled()) return;
		if(TextUtils.isEmpty(url) || TextUtils.isEmpty(title) || TextUtils.isEmpty(thumbnailUrl)){
			if(listener!=null){
				listener.onSend(false);
			}
			return;
		}
		mExecutorService.execute(new Runnable() {
			@Override
			public void run() {
				String thumbnailUrl2 = "";
				if (!thumbnailUrl.toLowerCase().startsWith("http")) {
					thumbnailUrl2 = WebApiUtils.getDownloadUrlForImg(thumbnailUrl,WebApiUtils.ImageType.SMALL);
				} else {
					thumbnailUrl2 = thumbnailUrl;
				}
				Bitmap thumbnail=ImageLoader.getInstance().loadImageSync(thumbnailUrl2, new ImageSize(THUMB_SIZE,THUMB_SIZE));
				if(thumbnail==null){
					thumbnail=ImageLoader.getInstance().loadImageSync(ShareHelper.iconFsLogo, new ImageSize(THUMB_SIZE,THUMB_SIZE));
				}
				if(thumbnail==null){
					if(listener!=null){
						listener.onSend(false);
					}
					return;
				}
				boolean success=sendWebPageToWX(url,title,description,thumbnail,sendToFriend);
				if(!thumbnail.isRecycled()){
					thumbnail.recycle();
				}
				if(listener!=null){
					listener.onSend(success);
				}
			}
		});
	}

	/**
	 * 发送APP扩展信息到微信，在微信端点击这条卡片消息，微信会回调发送这条消息的第三方APP，之后第三方APP就可以正常打开这个文件了
	 * @param extInfo 扩展信息，这个信息很重要，一定要填写
	 * @param title APP扩展的标题
	 * @param description APP扩展的描述
	 * @param thumbnail APP扩展的缩略图
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public boolean sendAppExtendToWX(String extInfo,String title,String description, Bitmap thumbnail, boolean sendToFriend)
	{
		if(!checkWXAppInstalled()) return false;
		if(TextUtils.isEmpty(extInfo) || TextUtils.isEmpty(title) || thumbnail==null) return false;
		WXAppExtendObject appExtObj = new WXAppExtendObject();
		appExtObj.extInfo=extInfo;

		WXMediaMessage msg = new WXMediaMessage();
		msg.mediaObject = appExtObj;
		msg.title=title;
		msg.description=description;
		Bitmap thumbnail2=null;
		if(thumbnail.getWidth()>THUMB_SIZE || thumbnail.getHeight()>THUMB_SIZE){
			thumbnail2=BitmapHelper.getImageThumbnail(thumbnail,THUMB_SIZE);
		}
		if(thumbnail2!=null){
			msg.setThumbImage(thumbnail2);
		}
		else{
			msg.setThumbImage(thumbnail);
		}

		SendMessageToWX.Req req = new SendMessageToWX.Req();
		req.transaction = buildTransaction("appdata");
		req.message = msg;
		req.scene = sendToFriend ? SendMessageToWX.Req.WXSceneSession : SendMessageToWX.Req.WXSceneTimeline;
		boolean result=wxapi.sendReq(req);
		if(thumbnail2!=null && !thumbnail2.isRecycled()){
			thumbnail2.recycle();
		}
		return result;
	}

	/**
	 * 发送APP扩展信息到微信，在微信端点击这条卡片消息，微信会回调发送这条消息的第三方APP，之后第三方APP就可以正常打开这个文件了
	 * @param extInfo 扩展信息，这个信息很重要，一定要填写
	 * @param title APP扩展的标题
	 * @param description APP扩展的描述
	 * @param thumbnailUrl APP扩展的缩略图url
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public void sendAppExtendToWX(final String extInfo,final String title,final String description, final String thumbnailUrl,
								  final boolean sendToFriend,final SendToWXListener listener)
	{
		if(!checkWXAppInstalled()) return;
		if(TextUtils.isEmpty(extInfo) || TextUtils.isEmpty(title) || TextUtils.isEmpty(thumbnailUrl)) {
			if(listener!=null){
				listener.onSend(false);
			}
			return;
		}
		mExecutorService.execute(new Runnable() {
			@Override
			public void run() {
				Bitmap thumbnail=ImageLoader.getInstance().loadImageSync(thumbnailUrl,new ImageSize(THUMB_SIZE,THUMB_SIZE));
				if(thumbnail==null){
					if(listener!=null){
						listener.onSend(false);
					}
					return;
				}
				boolean success=sendAppExtendToWX(extInfo,title,description,thumbnail,sendToFriend);
				if(!thumbnail.isRecycled()){
					thumbnail.recycle();
				}
				if(listener!=null){
					listener.onSend(success);
				}
			}
		});
	}

	/**
	 * 发送小程序到微信
	 * @param webPageUrl 小程序无法打开时，跳转到的网页
	 * @param userName 小程序的原始ID
	 * @param path 小程序的路径
	 * @param withShareTicket
	 * @param title 小程序的标题
	 * @param bmCover 小程序的图片，支持大图
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public boolean sendMiniProgramToWX(String webPageUrl,String userName,String path,boolean withShareTicket,
									   String title,Bitmap bmCover, boolean sendToFriend,MiniProgramType miniprogramType)
	{
		if(!checkWXAppInstalled()) return false;
		if(TextUtils.isEmpty(webPageUrl) || TextUtils.isEmpty(userName) || TextUtils.isEmpty(path) || TextUtils.isEmpty(title) || bmCover==null) return false;
		WXMiniProgramObject miniProgramObject = new WXMiniProgramObject();
		miniProgramObject.webpageUrl=webPageUrl;
		miniProgramObject.userName=userName;
		miniProgramObject.path=path;
		miniProgramObject.withShareTicket=withShareTicket;
		miniProgramObject.miniprogramType=miniprogramType.value;

		WXMediaMessage msg = new WXMediaMessage();
		msg.mediaObject = miniProgramObject;
		msg.title=title;
		msg.setThumbImage(bmCover);

		SendMessageToWX.Req req = new SendMessageToWX.Req();
		req.transaction = buildTransaction("miniprogram");
		req.message = msg;
		req.scene = sendToFriend ? SendMessageToWX.Req.WXSceneSession : SendMessageToWX.Req.WXSceneTimeline;
		return wxapi.sendReq(req);
	}

	/**
	 * 发送小程序到微信
	 * @param webPageUrl 小程序无法打开时，跳转到的网页
	 * @param userName 小程序的原始ID
	 * @param path 小程序的路径
	 * @param withShareTicket
	 * @param title 小程序的标题
	 * @param filePath 小程序图片的本地路径，支持大图
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public boolean sendMiniProgramToWX(String webPageUrl,String userName,String path,boolean withShareTicket,
									   String title,String filePath, boolean sendToFriend,MiniProgramType miniprogramType)
	{
		if(!checkWXAppInstalled()) return false;
		if(TextUtils.isEmpty(webPageUrl) || TextUtils.isEmpty(userName) || TextUtils.isEmpty(path) || TextUtils.isEmpty(title) || TextUtils.isEmpty(filePath)) return false;
		if(!FileHelper.fileExist(filePath)) return false;
		Bitmap bmCover=BitmapHelper.decodeBitmap(filePath);
		if(bmCover==null) return false;
		boolean result=sendMiniProgramToWX(webPageUrl, userName, path, withShareTicket, title, bmCover, sendToFriend,miniprogramType);
		if(!bmCover.isRecycled()){
			bmCover.recycle();
		}
		return result;
	}

    private static final int MAX_BYTE_SIZE = 131072; // 128 * 1024 byte
	/**
	 * 发送小程序到微信
	 * @param webPageUrl 小程序无法打开时，跳转到的网页
	 * @param userName 小程序的原始ID
	 * @param path 小程序的路径
	 * @param withShareTicket 分享小程序的时候是否带上shareTicket参数
	 * @param title 小程序的标题
	 * @param coverImageUrl 小程序图片的网络路径，支持大图
	 * @param sendToFriend true代表发送给微信好友，false代表发送到朋友圈
	 * @return
	 */
	public void sendMiniProgramToWXAsync(final String webPageUrl,final String userName,final String path,final boolean withShareTicket,
										 final String title, final String coverImageUrl, final boolean sendToFriend,final MiniProgramType miniprogramType,final SendToWXListener listener)
	{
		if(!checkWXAppInstalled()) return;
		if(TextUtils.isEmpty(webPageUrl) || TextUtils.isEmpty(userName) || TextUtils.isEmpty(path) || TextUtils.isEmpty(title) || TextUtils.isEmpty(coverImageUrl)) {
			if(listener!=null){
				listener.onSend(false);
			}
			return;
		}

		mExecutorService.execute(new Runnable() {
            @Override
            public void run() {
                Bitmap bmCover = ImageLoader.getInstance().loadImageSync(coverImageUrl);
                if(bmCover==null){
                    if(listener!=null){
                        listener.onSend(false);
                    }
                    return;
                }

                Bitmap scaledCover = bmCover;
                int imageSize = getBimapCompressedSizeByWXSDK(scaledCover);

                while(imageSize > MAX_BYTE_SIZE){
                    // MicroMsg.SDK.WXMediaMessage: checkArgs fail, thumbData should not be null or exceed 128kb
                    float ratio = MAX_BYTE_SIZE * 1.0f / imageSize * 0.85f;
                    ratio = (float)Math.sqrt(ratio);
                    Bitmap lastBM = scaledCover;
                    scaledCover = BitmapHelper.getImageThumbnailByScale(scaledCover, ratio);
                    lastBM.recycle();
                    imageSize = getBimapCompressedSizeByWXSDK(scaledCover);
                }

                if(scaledCover == null){
                    if(listener!= null){
                        listener.onSend(false);
                    }
                    return;
                }

                boolean success = sendMiniProgramToWX(webPageUrl, userName, path, withShareTicket, title, scaledCover, sendToFriend,miniprogramType);
                if(!bmCover.isRecycled()){
                    bmCover.recycle();
                }
                if(!scaledCover.isRecycled()){
                    scaledCover.recycle();
                }
                if(listener!=null){
                    listener.onSend(success);
                }

            }
        });



	}

	private int getBimapCompressedSizeByWXSDK(Bitmap source){
        int len = 0;
        ByteArrayOutputStream baos = null;
	    try{
            baos  = new ByteArrayOutputStream();
            source.compress(Bitmap.CompressFormat.JPEG, 85, baos);
            len = baos.toByteArray().length;
            baos.close();
        }catch(Exception e){
            e.printStackTrace();
        }
		return len;
	}

	private int getNetworkImageContentLength(String imageUrl) {
                okhttp3.Request.Builder requestBuilder = new Request.Builder();
                Request imgSizeRequest = requestBuilder.head().url(imageUrl).build();
                OkHttpClient mOkHttpClient = new OkHttpClient();
                Response response = null;
                int imageSize = 0;
                try {
                    response = mOkHttpClient.newCall(imgSizeRequest).execute();
                } catch (IOException e) {
                    e.printStackTrace();
                }finally {
                    if(response != null && response.isSuccessful()){
                        Headers headers = response.headers();
                        String contentLen = headers.get("content-length");
                        if(!TextUtils.isEmpty(contentLen)){
                            imageSize = Integer.parseInt(contentLen);
                        }
                    }
                }
                return imageSize;
    }

	/**
	 * 释放微信分享SDK对当前context的引用
	 */
	public void detach(){
		if(wxapi != null){
			wxapi.detach();
		}

		if(wxCommonCallbackHashMap != null){
			wxCommonCallbackHashMap.clear();
		}
		if(mWXResponseBroadcast!=null){
			mContext.unregisterReceiver(mWXResponseBroadcast);
		}
	}

	private String buildTransaction(final String type) {
		return TextUtils.isEmpty(type) ? String.valueOf(System.currentTimeMillis()) : type + System.currentTimeMillis();
	}

	/**
	 * 请求微信登录
	 * */
	public void requestWXOAuth(WXCommonCallback callback){
		if(!checkWXAppInstalled()) return;
		if(callback != null){ // lazy init
			initWXCallbackThings();
		}

		// send oauth request
		final SendAuth.Req req = new SendAuth.Req();
		req.scope = "snsapi_userinfo";
		String tempState = "Hades_" + SystemClock.uptimeMillis();
		req.state = tempState;
		wxapi.sendReq(req);

		if(wxCommonCallbackHashMap != null && callback != null){
			wxCommonCallbackHashMap.put(tempState, callback);
		}
	}

	/**
	 * 请求跳转到微信小程序
	 * */
	public void launchWXMiniProgram(final String userName,final String path, final MiniProgramType miniprogramType, WXCommonCallback callback){
		if(!checkWXAppInstalled()) return;
		FCLog.e("launchWXMiniProgram", "getWXAppSupportAPI = " + wxapi.getWXAppSupportAPI());
		if(callback != null){ // lazy init
			initWXCallbackThings();
		}
		WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
		req.userName = userName; // 填小程序原始id
		if(!TextUtils.isEmpty(path)){
			req.path = path;                  //拉起小程序页面的可带参路径，不填默认拉起小程序首页
		}

		req.miniprogramType = miniprogramType.value;// 可选打开 开发版，体验版和正式版
		wxapi.sendReq(req);

		if(wxCommonCallbackHashMap != null && callback != null){
			wxCommonCallbackHashMap.put(LAUNCH_MINI_APP_CALLBACK_KEY, callback);
		}
	}

	/**
	 * 从微信卡包里面选择发票
	 */
	public void chooseInvoiceFromWX(InvoiceModel model,WXCommonCallback callback) {
		if(callback != null){ // lazy init
			initWXCallbackThings();
		}
		ChooseCardFromWXCardPackage.Req req = new ChooseCardFromWXCardPackage.Req();
		req.appId=model.appId;
		req.cardType="INVOICE";
		req.nonceStr=model.nonceStr;//随机字符串，签名用，没有意义
		req.timeStamp=model.timeStamp;
		req.signType=model.signType;
		req.cardSign=model.cardSign;
		if(req.checkArgs()) {
			wxapi.sendReq(req);
		}
		if(wxCommonCallbackHashMap != null && callback != null){
			wxCommonCallbackHashMap.put(CHOOSE_INVOICE_FROM_WX_CALLBACK, callback);
		}
	}

	public InvoiceModel getDefaultInvoiceModel() {
		InvoiceModel model = new InvoiceModel();
		model.appId=WX_APPID;
		model.cardType="INVOICE";
		model.nonceStr="choose_invoice_from_fxiaoke_app";//随机字符串，签名用，没有意义
		model.timeStamp=(new Date().getTime() / 1000) +"";
		model.signType="SHA1";
		model.cardSign=getCardSign(model);
		return model;
	}

	private String getCardSign(InvoiceModel model) {
		Set<String> set = new TreeSet<>();
		set.add(model.appId);
		set.add(model.cardType);
		set.add(model.nonceStr);
		set.add(model.timeStamp);
		set.add(model.signType);

		StringBuilder sb = new StringBuilder();
		for(String item : set) {
			sb.append(item);
		}

		return getSHA1Digest(sb.toString());
	}

	private String getSHA1Digest(String msg) {
		String digest = null;
		try {
			MessageDigest messageDigest = MessageDigest.getInstance("SHA1");
			messageDigest.update(msg.getBytes("utf-8"));
			byte[] data = messageDigest.digest();
			digest = toHex(data);
		} catch (Exception e) {
			e.printStackTrace();
			FCLog.e(TAG,e.getMessage());
		}
		return digest;
	}

	private String toHex(byte[] buffer) {
		StringBuilder sb = new StringBuilder(buffer.length * 2);
		for(byte data : buffer) {
			sb.append(String.format("%02X",data));
		}
		return sb.toString();
	}

	public static class InvoiceModel {
		public String appId;
		public String nonceStr;
		public String cardType="INVOICE";
		public String timeStamp;
		public String signType;
		public String cardSign;
	}
}
