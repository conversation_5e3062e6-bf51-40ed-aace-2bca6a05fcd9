package com.fxiaoke.fscommon.weex.module;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import com.facishare.fs.common_utils.NativeIntentUtils;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.fxiaoke.fscommon.util.FsUrlUtils;
import com.taobao.weex.annotation.JSMethod;
import com.taobao.weex.common.WXModule;

public class RouteHelperModule extends WXModule {

    @JSMethod(uiThread = true)
    public void actionWithURL(String url) {
        if(TextUtils.isEmpty(url)){
            return;
        }
        try{
            if (mWXSDKInstance.getContext() instanceof Activity){
                Intent it = FsUrlUtils.buildIntent(mWXSDKInstance.getContext(), url);
                HostInterfaceManager.getHostInterface().startActivity((Activity)mWXSDKInstance.getContext(), it);
            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }

}
