/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.fscommon.weex.bundle;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.Objects;

public class SubBundle implements Parcelable {

    public String bundleName;

    public String md5;

    public SubBundle() {

    }

    public String getBundleName(){
        return bundleName;
    }

    public String getMd5(){
        return md5;
    }

    public SubBundle(Parcel in) {
        bundleName = in.readString();
        md5 = in.readString();
    }

    public static final Creator<SubBundle> CREATOR = new Creator<SubBundle>() {
        @Override
        public SubBundle createFromParcel(Parcel in) {
            return new SubBundle(in);
        }

        @Override
        public SubBundle[] newArray(int size) {
            return new SubBundle[size];
        }
    };

    public String toString() {
        StringBuilder sb = new StringBuilder();

        sb.append("{ name:");
        sb.append(this.bundleName);

        sb.append(", md5:");
        sb.append(this.md5.substring(0,7));

        sb.append("}");

        return sb.toString();

    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SubBundle subBundle = (SubBundle) o;
        return Objects.equals(bundleName, subBundle.bundleName) && Objects.equals(md5, subBundle.md5);
    }

    @Override
    public int hashCode() {
        return Objects.hash(bundleName, md5);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeString(bundleName);
        parcel.writeString(md5);
    }
}