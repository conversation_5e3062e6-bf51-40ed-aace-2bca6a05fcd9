/*
 * Copyright (C) 2024 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.fscommon.util;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;

public class MapData implements Serializable {
    private Map<String, Object> params;

    public MapData(Map<String, Object> params) {
        this.params = params;
        if (params == null) {
            this.params = new HashMap<>();
        }
    }
    public boolean containsKey(Object key) {
        if(this.params == null){
            return false;
        }
        return this.params.containsKey(key);
    }
    public Map<String, Object> getParams() {
        return params;
    }

    public Object get(Object key) {
        return params.get(key);
    }

    public com.alibaba.fastjson.JSONObject getJSONObject(String key) {
        Object value = params.get(key);
        if (value instanceof com.alibaba.fastjson.JSONObject) {
            return (com.alibaba.fastjson.JSONObject) value;
        } else {
            try {
                return value instanceof String ? JSON.parseObject((String) value) : (JSONObject) JSONObject.toJSON(value);

            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }
    }

    public JSONArray getJSONArray(String key) {
        Object value = params.get(key);
        if (value instanceof JSONArray) {
            return (JSONArray) value;
        } else {
            try {
                return value instanceof String ? (JSONArray) JSON.parse((String) value) : (JSONArray) JSONObject.toJSON(value);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }
    }

    public <T> T getObject(String key, Class<T> clazz) {
        Object obj = params.get(key);
        try {
            return TypeUtils.castToJavaBean(obj, clazz);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public Boolean getBoolean(String key) {
        Object value = params.get(key);
        try {
            return value == null ? null : TypeUtils.castToBoolean(value);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public byte[] getBytes(String key) {
        Object value = params.get(key);
        try {
            return value == null ? null : TypeUtils.castToBytes(value);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public boolean getBooleanValue(String key) {
        Object value = params.get(key);
        try {
            Boolean booleanVal = TypeUtils.castToBoolean(value);
            return booleanVal == null ? false : booleanVal;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public Byte getByte(String key) {
        Object value = params.get(key);
        try {
            return TypeUtils.castToByte(value);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public byte getByteValue(String key) {
        Object value = params.get(key);
        try {
            Byte byteVal = TypeUtils.castToByte(value);
            return byteVal == null ? 0 : byteVal;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    public Short getShort(String key) {
        Object value = params.get(key);
        try {
            return TypeUtils.castToShort(value);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public short getShortValue(String key) {
        Object value = params.get(key);
        try {
            Short shortVal = TypeUtils.castToShort(value);
            return shortVal == null ? 0 : shortVal;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    public Integer getInteger(String key) {
        Object value = params.get(key);
        try {
            return TypeUtils.castToInt(value);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public int getIntValue(String key) {
        Object value = params.get(key);
        try {
            Integer intVal = TypeUtils.castToInt(value);
            return intVal == null ? 0 : intVal;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    public Long getLong(String key) {
        Object value = params.get(key);
        try {
            return TypeUtils.castToLong(value);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public long getLongValue(String key) {
        Object value = params.get(key);
        try {
            Long longVal = TypeUtils.castToLong(value);
            return longVal == null ? 0L : longVal;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0L;
    }

    public Float getFloat(String key) {
        Object value = params.get(key);
        try {
            return TypeUtils.castToFloat(value);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public float getFloatValue(String key) {
        Object value = params.get(key);
        try {
            Float floatValue = TypeUtils.castToFloat(value);
            return floatValue == null ? 0.0F : floatValue;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0.0F;
    }

    public Double getDouble(String key) {
        Object value = params.get(key);
        try {
            return TypeUtils.castToDouble(value);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public double getDoubleValue(String key) {
        Object value = params.get(key);
        Double doubleValue = TypeUtils.castToDouble(value);
        try {
            return doubleValue == null ? 0.0D : doubleValue;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0.0D;
    }

    public BigDecimal getBigDecimal(String key) {
        Object value = params.get(key);
        try {
            return TypeUtils.castToBigDecimal(value);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public BigInteger getBigInteger(String key) {
        Object value = params.get(key);
        try {
            return TypeUtils.castToBigInteger(value);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public String getString(String key) {
        Object value = params.get(key);
        try {
            return value == null ? null : value.toString();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
