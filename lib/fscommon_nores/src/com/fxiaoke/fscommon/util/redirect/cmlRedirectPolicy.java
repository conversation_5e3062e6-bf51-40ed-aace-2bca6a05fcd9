package com.fxiaoke.fscommon.util.redirect;

import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.fscommon.util.CmlIntentUtils;

import org.mortbay.util.MultiMap;
import org.mortbay.util.UrlEncoded;

import java.net.URI;
import java.net.URLDecoder;

public  class cmlRedirectPolicy  extends baseRedirectPolicy {

    String action = "";
    String query = "";
    String json = "";

    @Override
    public String getAction(String url) {
        String ret = url;
        try {
            URI uri = new URI(url);
            String params = uri.getQuery();
            MultiMap multiMap = new MultiMap();
            UrlEncoded.decodeTo(url.substring(url.indexOf("?") + 1), multiMap, "UTF-8");
            String cmlUrl = multiMap.getString("wx_addr");
            if (TextUtils.isEmpty(cmlUrl)) {
                cmlUrl = multiMap.getString("cml_addr");
            }

            cmlUrl = URLDecoder.decode(cmlUrl);
            String[] arr = cmlUrl.split("\\?");
            if (arr.length != 2) {
                return url;
            }
             action = arr[0];
             query = arr[1];
             json = query2json(arr[1]);

        }catch (Exception e){
            e.printStackTrace();
        }
        return action;
    }

    @Override
    public String toFs(String url, String redirect) {
        String ret = "";
        if (!TextUtils.isEmpty(redirect)) {
            if (!TextUtils.isEmpty(json)) {
                ret = redirect + "?" + json;
            } else {
                ret = redirect;
            }
        }
        return ret;
    }

    @Override
    public String toBundle(String url, String redirect) {
        return toFs(url, redirect);
    }

    @Override
    public String toHttp(String url, String redirect) {
        String ret = "";
        if (TextUtils.isEmpty(json)) {
            ret = redirect;
            return ret;
        }
        ret = putplaceholder(redirect, JSON.parseObject(json));
        return ret;
    }

    @Override
    public String toCml(String url, String redirect) {
        String ret = "";
        if (!TextUtils.isEmpty(redirect)) {
            if (!TextUtils.isEmpty(json)) {
                ret = redirect + "?" + json;
            } else {
                ret = redirect;
            }
        }
        return CmlIntentUtils.cml2http(ret);
    }

    @Override
    public String toAva(String url, String redirect) {
        return (json == null || json.length() == 0) ? redirect : redirect + "?" + json;
    }


}
