/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.fscommon.util;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.common_utils.DateTimeUtils;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.fxiaoke.fscommon.files.PersistentGlobalDataBySP;
import com.fxiaoke.fxlog.FCLog;

import java.util.Date;
import java.util.Map;

/**
 * Created by yangwg on 2017/3/22.
 */
public class AccountInfoUtils {

    /**
     *  检查用户是否同意了隐私协议，不同意时，是不允许使用的
     * @param ctx
     * @return
     */
    public static boolean isAgreeAppPrivacy(Context ctx) {
        if(ctx == null){
            return false;
        }
        SharedPreferences sp = ctx.getSharedPreferences("AppPrivacy", Context.MODE_PRIVATE);
        if(sp == null){
            return false;
        }
        return sp.getBoolean("agree", false);
    }

    public static void savePrivacySP(Context ctx){
        SharedPreferences sp=ctx.getSharedPreferences("AppPrivacy",Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sp.edit();
        editor.putBoolean("agree",true);
        editor.putLong("agreeTime",System.currentTimeMillis());
        editor.apply();

    }

    public static long getPrivacyAgreeTime(Context ctx) {
        if(ctx == null){
            return -1;
        }
        SharedPreferences sp = ctx.getSharedPreferences("AppPrivacy", Context.MODE_PRIVATE);
        if(sp == null){
            return -1;
        }
        return sp.getLong("agreeTime", -1);
    }

    public static String getUpdatePrivacy(Context ctx) {
        if(ctx == null){
            return null;
        }
        SharedPreferences sp = ctx.getSharedPreferences("AppPrivacy", Context.MODE_PRIVATE);
        if(sp == null){
            return null;
        }
        return sp.getString("updatePrivacy", null);
    }

    public static boolean isUpdatePrivacyAvailable(Context context){
        long time = getUpdatePrivacyTime(context);
        if(time>0 && System.currentTimeMillis()>time) {
            return true;
        }

        return false;
    }

    private static long getUpdatePrivacyTime(Context ctx){
        try {
            SharedPreferences sp=ctx.getSharedPreferences("AppPrivacy",Context.MODE_PRIVATE);
            String content = sp.getString("updatePrivacy", null);
            if(content != null ) {
                JSONObject jo = JSON.parseObject(content);
                String timeStr = jo.getString("time");
                Date time = DateTimeUtils.parseSpaceString(timeStr);
                return time.getTime();
            }
        }catch (Exception e){
            FCLog.e("AppPrivacy", Log.getStackTraceString(e));
        }

        return 0;
    }

    public static void saveUpdatePrivacy(Context ctx,String content){
        try {
            JSONObject jo = JSON.parseObject(content);
            String timeStr = jo.getString("time");
            Date time = DateTimeUtils.parseSpaceString(timeStr);
            if(time!=null && time.getTime()>getPrivacyAgreeTime(ctx)){
                SharedPreferences sp=ctx.getSharedPreferences("AppPrivacy",Context.MODE_PRIVATE);
                sp.edit().putString("updatePrivacy",content).apply();
            }
        }catch (Exception e){
            FCLog.e("AppPrivacy", Log.getStackTraceString(e));
        }
    }


    public static void removeUpdatePrivacySP(Context ctx){
        SharedPreferences sp=ctx.getSharedPreferences("AppPrivacy",Context.MODE_PRIVATE);
        sp.edit().remove("updatePrivacy").apply();

    }

    /**
     * appinit时是否需要存储和电话权限
     * @param ctx
     * @return
     */
    public static boolean isPrepareNeedPermission(Context ctx){
        if(ctx == null){
            return false;
        }
        SharedPreferences sp = ctx.getSharedPreferences("AppPrivacy", Context.MODE_PRIVATE);
        if(sp == null){
            return false;
        }
        return sp.getBoolean("prepareNeedPermission", false);
    }

    public static void setPrepareNeedPermission(Context ctx,boolean b){
        SharedPreferences sp=ctx.getSharedPreferences("AppPrivacy",Context.MODE_PRIVATE);
        sp.edit().putBoolean("prepareNeedPermission",b).apply();
    }


    public static boolean isMySelf(String myEA, int myId, int defaultSendId, String fullSenderId) {
        String sendEA = myEA;
        int sendId = defaultSendId;
        if(TextUtils.isEmpty(fullSenderId)){
        }else{
            sendEA = getEAFromFullSenderID(fullSenderId,myEA);
            sendId = getIDFromFullSenderId(fullSenderId,defaultSendId);
        }
        return TextUtils.equals(myEA,sendEA) && myId == sendId;
    }

    /**
     * 为后边通用，所以改为public
     * @return
     */
    public static int getIDFromFullSenderId(String fullSenderId, int defaultSendId) {
        int sendId = defaultSendId;
        if(!TextUtils.isEmpty(fullSenderId)){
            String[] split = fullSenderId.split("\\.");
            if (split.length < 3) {
            }else{
                try {
                    sendId = Integer.parseInt(split[2]) ;
                } catch (NumberFormatException e) {
                }
            }
        }
        return sendId;
    }

    /**
     * 为后边通用，所以改为public
     * @return
     */
    public static String getEAFromFullSenderID(String fullSenderId,String defaultEA) {
        String sendEA = defaultEA;
        if(!TextUtils.isEmpty(fullSenderId)){
            String[] split = fullSenderId.split("\\.");
            if (split.length < 2) {
                sendEA = defaultEA;
            }else{
                if("e".equals(split[0]) || "E".equals(split[0])){//目前是E.fktest.111 的形式，第二位才是企业账号，其他的不是
                    sendEA = split[1] ;
                }else{
                    sendEA = defaultEA;
                }
            }
        }
        return sendEA;
    }
    /**
     * 当前帐号是否可以使用 科大讯飞 的语音识别功能
     */
    public static boolean canUseIFlytek() {
        if (PersistentGlobalDataBySP.isOverseas()) {
            return false;
        }
        return true;
    }

    public static boolean parseMapConfigBooleanValue(String cloudCtrKey, String parseKey) {
        return parseMapConfigBooleanValue(cloudCtrKey, parseKey, null);
    }

    public static boolean parseMapConfigBooleanValue(String cloudCtrKey, String parseKey, String defaultValue) {
        String srcConfig = HostInterfaceManager.getCloudCtrlManager().getStringConfig(cloudCtrKey, defaultValue);
        boolean canViewByAvatar = false;//FsUtils.isDebug()
        if (srcConfig != null) {
            try {//{"enable":true}
                JSONObject obj = JSON.parseObject(srcConfig);
                if(obj == null){
                    FCLog.i("AccountInfoUtils", "parseMapConfigBooleanValue failed-1 [" + cloudCtrKey + " : "+srcConfig+"]");
                    return canViewByAvatar;
                }
                Map<String, Object> objectMap = obj.getInnerMap();
                Object value = objectMap.get(parseKey);
                String valueString = value.toString();
                canViewByAvatar = Boolean.parseBoolean(valueString) || "1".equals(valueString);//兼容设置了值为1时，也认为是true
            } catch (Exception e) {
                FCLog.w("AccountInfoUtils", "parseMapConfigBooleanValue failed-2 [" + cloudCtrKey + " : "+srcConfig+"]", e);
            }
        }
        return canViewByAvatar;
    }

    public static String parseMapConfigStringValue(String cloudCtrKey, String parseKey) {
        return parseMapConfigStringValue(cloudCtrKey, parseKey, null);
    }

    public static String parseMapConfigStringValue(String cloudCtrKey, String parseKey, String defaultValue) {
        String srcConfig = HostInterfaceManager.getCloudCtrlManager().getStringConfig(cloudCtrKey, defaultValue);
        if (srcConfig != null) {
            try {
                Map<String, Object> objectMap = JSON.parseObject(srcConfig).getInnerMap();
                Object value = objectMap.get(parseKey);
                if (value != null) {
                    String valueString = value.toString();
                    return valueString;
                }
            } catch (Exception e) {
                FCLog.w("AccountInfoUtils", "parseMapConfigBooleanValue " + cloudCtrKey + " failed ", e);
            }
        }
        return null;
    }
}
