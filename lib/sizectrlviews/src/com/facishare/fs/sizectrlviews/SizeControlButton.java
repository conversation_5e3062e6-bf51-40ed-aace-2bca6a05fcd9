/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.sizectrlviews;

import com.facishare.fs.i18n.I18NButton;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.TypedValue;

/**
 * Created by zhouz on 2016/12/15.
 * 可以在设置界面调整字体大小的Button
 */
@SuppressWarnings("JavadocReference")
public class SizeControlButton extends I18NButton {

    private static final int sizeFactor = 1;//放大倍数
    private boolean isUsingOrg = false;//是否使用原生setSize方法
    private boolean isAllowControl = true;//是否允许控制

    public SizeControlButton(Context context) {
        super(context);
        initTextSize();
    }

    public SizeControlButton(Context context, AttributeSet attrs) {
        super(context, attrs);
        initAttrs(attrs, 0);
        initTextSize();
    }

    public SizeControlButton(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        initAttrs(attrs, defStyle);
        initTextSize();
    }

    private void initAttrs(AttributeSet attrs, int defStyle) {
        final TypedArray array = getContext().obtainStyledAttributes(
                attrs, R.styleable.SizeController, defStyle, 0);
        setAllowControl(array.getBoolean(R.styleable.SizeController_allowControl, true));
        array.recycle();
    }

    public boolean isAllowControl() {
        return isAllowControl;
    }

    public void setAllowControl(boolean allowControl) {
        isAllowControl = allowControl;
        invalidate();
    }

    /**
     * 是否允许控制字体大小
     */
    private boolean canControl() {
        if (!isAllowControl()) {
            return false;
        }
        if (SizeController.getInstance().hasNotAllowPkg()){
            if (SizeController.getInstance().isNotAllowPkg(getContext().getClass().getName())
                    || SizeController.getInstance().isNotAllowPkg(getContext().getPackageName())){
                return false;
            }
        }
        return true;
    }

    /**
     * 初始化字体大小受{@link fontSizeRate}影响
     */
    private void initTextSize() {
        if (SizeController.getInstance().isStandardSize()) {
            return;
        }
        if (canControl()) {
            setTextSize(TypedValue.COMPLEX_UNIT_PX, super.getTextSize());
        }
        isUsingOrg = false;
    }

    /**
     * 动态设置字体大小,受{@link fontSizeRate}影响
     */
    @Override
    public void setTextSize(int unit, float size) {
        if (SizeController.getInstance().isStandardSize() || !canControl()) {
            super.setTextSize(unit, size);
        } else {
            super.setTextSize(unit, size * SizeController.getInstance().getFontSizeRate());
        }
        isUsingOrg = false;
    }

    /**
     * 动态设置字体大小(sp),受{@link fontSizeRate}影响
     */
    @Override
    public void setTextSize(float size) {
        super.setTextSize(size);
        isUsingOrg = false;
    }

    /**
     * 原生动态设置字体大小
     */
    public void orgSetTextSize(int unit, float size) {
        super.setTextSize(unit, size);
        isUsingOrg = true;
    }

    /**
     * 原生动态设置字体大小(sp)
     */
    public void orgSetTextSize(float size) {
        super.setTextSize(TypedValue.COMPLEX_UNIT_SP, size);
        isUsingOrg = true;
    }

    /**
     * 获取字体大小(px),受{@link fontSizeRate}影响
     */
    @Override
    public float getTextSize() {
        if (SizeController.getInstance().isStandardSize() || !canControl()) {
            return super.getTextSize();
        } else {
            return isUsingOrg ? super.getTextSize() : (super.getTextSize() / SizeController.getInstance().getFontSizeRate());
        }
    }
}
