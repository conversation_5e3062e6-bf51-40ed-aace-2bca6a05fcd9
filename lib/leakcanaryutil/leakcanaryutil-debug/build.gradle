disableLintTask()
apply plugin: 'com.android.library'

android {
    compileSdkVersion gradle.compileSdkVersionForLib
    buildToolsVersion gradle.buildToolsVersion
    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
//            jniLibs.srcDir 'libs'
            aidl.srcDirs = ['src']
            java.srcDirs = ['src']
            res.srcDirs = ['res']
            assets.srcDirs = ['assets'] //多了一个assets目录
        }
    }

    compileOptions {
        sourceCompatibility gradle.javaVersion
        targetCompatibility gradle.javaVersion
    }

    defaultConfig {
        minSdkVersion 14
        targetSdkVersion 23
        versionCode 1
        versionName "1.0"
    }

    packagingOptions {
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

}
repositories {
    flatDir {
        dirs './'
    }
}

dependencies {
    implementation(name: 'leakcanary-watcher-release', ext: 'aar')
    implementation(name: 'leakcanary-analyzer-release', ext: 'aar')
    implementation(name: 'leakcanary-android-release', ext: 'aar')
    implementation 'com.github.markzhai:blockcanary-android:1.5.0'
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    compileOnly files(rootProject.getRootDir().getAbsolutePath() + "/moduleJarLibs_temp/FXStatEngine.jar")
}
