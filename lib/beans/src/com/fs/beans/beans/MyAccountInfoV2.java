package com.fs.beans.beans;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

public class MyAccountInfoV2 implements Serializable{
	//我的手机号
	@JSONField(name="M1")
	public String mMobile;
 
	public MyAccountInfoV2(){}
	@JSONCreator
	public MyAccountInfoV2(
			@JSONField(name="M1")
			String mMobile) {
		this.mMobile = mMobile;
	}

	@Override
	public String toString() {
		return new StringBuffer("mMobile:" + mMobile).toString();
	}
}
