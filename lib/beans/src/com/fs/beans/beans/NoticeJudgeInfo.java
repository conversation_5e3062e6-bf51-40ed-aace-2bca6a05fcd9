
package com.fs.beans.beans;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

public class NoticeJudgeInfo implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
     * NoticeJudgeTagID
     */
    @J<PERSON><PERSON>ield(name="M1")
    public final String NoticeJudgeTagID;
    /**
     * TagContent
     */
    @JSONField(name="M2")
    public final String TagContent;
    /**
     * JudgeCount
     */
    @J<PERSON><PERSON>ield(name="M3")
    public final int JudgeCount;

    @JSONCreator
    public NoticeJudgeInfo(@J<PERSON><PERSON><PERSON>(name="M1")
    String NoticeJudgeTagID,
            @JSO<PERSON>ield(name="M2")
            String TagContent,
            @JSONField(name="M3")
            int JudgeCount) {
        this.NoticeJudgeTagID = NoticeJudgeTagID;
        this.TagContent = TagContent;
        this.JudgeCount = JudgeCount;
    }
}
