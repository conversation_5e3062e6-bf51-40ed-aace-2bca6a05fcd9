package com.fs.beans.beans;

import java.util.Date;

import com.facishare.fs.pluginapi.contact.beans.EmpShortEntity;
import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

public class TimingMessageRemaindSimpleEntity {
	/**
	 * 主键ID
	 */
	@JSONField(name="a")
	public int timingMessageRemaindID;
	/**
	 * 子类型
	 */
	@JSONField(name="b")
	public int subType;
	/**
	 * 整形唯一业务数据ID（如feedid）
	 */
	@JSONField(name="c")
	public int dataID;
	/**
	 * 字符串唯一业务数据ID（如feedid+employeeID）
	 */
	@JSONField(name="d")
	public String dataStringID;
	/**
	 * 信息提醒时间
	 */
	@JSONField(name="e")
	public Date remaindTime;
	/**
	 * 信息
	 */
	@JSONField(name="j")
	public String message;
	/**
	 * 提醒员工ID 
	 */
	@JSONField(name="k")
	public int employeeID;
	/**
	 * 创建人ID 
	 */
	@JSONField(name="l")
	public int creatorID;
	/**
	 * 创建时间
	 */
	@JSONField(name="m")
	public Date createTime;
	/**
	 * 来源信息
	 */
	@JSONField(name="n")
	public String sourceMessage;
	/**
	 * 来源信息员工ID
	 */
	@JSONField(name="o")
	public int sourceEmployeeID;
	/**
	 * 创建人
	 */
	@JSONField(name="p")
	public EmpShortEntity creator;
	public TimingMessageRemaindSimpleEntity(){
		super();
	}
	@JSONCreator
	public TimingMessageRemaindSimpleEntity(@JSONField(name="a") int timingMessageRemaindID,
			@JSONField(name="b") int subType,
			@JSONField(name="c") int dataID,
			@JSONField(name="d") String dataStringID,
			@JSONField(name="e") Date remaindTime,
			@JSONField(name="j") String message,
			@JSONField(name="k") int employeeID,
			@JSONField(name="l") int creatorID,
			@JSONField(name="m") Date createTime,
			@JSONField(name="n") String sourceMessage,
			@JSONField(name="o") int sourceEmployeeID,
			@JSONField(name="p") EmpShortEntity creator) {
		this.timingMessageRemaindID = timingMessageRemaindID;
		this.subType = subType;
		this.dataID = dataID;
		this.dataStringID = dataStringID;
		this.remaindTime = remaindTime;
		this.message = message;
		this.employeeID = employeeID;
		this.creatorID = creatorID;
		this.createTime = createTime;
		this.sourceMessage = sourceMessage;
		this.sourceEmployeeID = sourceEmployeeID;
		this.creator = creator;
	}
}
