package com.fs.beans.beans;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

public class EmployeeCircleBelongingV2 implements Serializable{
	//员工ID
	@JSONField(name="M1")
	public int mEmployeeID;
	//该员工所属的部门ID列表
	@JSONField(name="M2")
	public List<Integer> mCircleIDList;
 
	public EmployeeCircleBelongingV2(){}
	@JSONCreator
	public EmployeeCircleBelongingV2(
			@JSONField(name="M1")
			int mEmployeeID,
			@JSONField(name="M2")
			List<Integer> mCircleIDList) {
		this.mEmployeeID = mEmployeeID;
		this.mCircleIDList = mCircleIDList;
	}
}
