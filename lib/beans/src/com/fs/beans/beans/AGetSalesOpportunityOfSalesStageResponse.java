package com.fs.beans.beans;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

public class AGetSalesOpportunityOfSalesStageResponse {
	/**
	 * 销售阶段对应销售机会预计成交金额集合信息
	 */
	@JSONField(name="a")
	public List<AExpectedSalesAmountItemOfSalesStage> expectedSalesAmountItems;
 
	@JSONCreator
	public AGetSalesOpportunityOfSalesStageResponse(@JSONField(name="a") List<AExpectedSalesAmountItemOfSalesStage> expectedSalesAmountItems) {
		this.expectedSalesAmountItems = expectedSalesAmountItems;
	}
	public AGetSalesOpportunityOfSalesStageResponse() {
	    super();
    }
}
