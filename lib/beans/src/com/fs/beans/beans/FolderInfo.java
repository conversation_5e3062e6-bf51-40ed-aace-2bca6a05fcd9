package com.fs.beans.beans;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

 

public class FolderInfo {
	/**
	* EI
	*/
	@JSONField(name="M1")
	public final int EI;
	/**
	* FolderID
	*/
	@JSONField(name="M2")
	public final String FolderID;
	/**
	* IsRoot
	*/
	@JSONField(name="M3")
	public final boolean IsRoot;
	/**
	* RootID
	*/
	@JSONField(name="M4")
	public final String RootID;
	/**
	* ParentID
	*/
	@JSONField(name="M5")
	public final String ParentID;
	/**
	* FolderName
	*/
	@JSONField(name="M6")
	public final String FolderName;
	/**
	* FolderPath
	*/
	@JSONField(name="M7")
	public final String FolderPath;
	/**
	* FolderLevel
	*/
	@JSONField(name="M8")
	public final int FolderLevel;
	/**
	* IsPartnerPublic
	*/
	@JSONField(name="M9")
	public final boolean IsPartnerPublic;
	/**
	* IsEnterprisePublic
	*/
	@JSONField(name="M10")
	public final boolean IsEnterprisePublic;
	/**
	* UpdateTime
	*/
	@JSONField(name="M11")
	public final long UpdateTime;
	/**
	* FolderCount
	*/
	@JSONField(name="M12")
	public final int FolderCount;
	/**
	* FileCount
	*/
	@JSONField(name="M13")
	public final int FileCount;

	@JSONCreator
	public FolderInfo(@JSONField(name="M1") int EI,
	@JSONField(name="M2") String FolderID,
	@JSONField(name="M3") boolean IsRoot,
	@JSONField(name="M4") String RootID,
	@JSONField(name="M5") String ParentID,
	@JSONField(name="M6") String FolderName,
	@JSONField(name="M7") String FolderPath,
	@JSONField(name="M8") int FolderLevel,
	@JSONField(name="M9") boolean IsPartnerPublic,
	@JSONField(name="M10") boolean IsEnterprisePublic,
	@JSONField(name="M11") long UpdateTime,
	@JSONField(name="M12") int FolderCount,
	@JSONField(name="M13") int FileCount) {
	this.EI = EI;
	this.FolderID = FolderID;
	this.IsRoot = IsRoot;
	this.RootID = RootID;
	this.ParentID = ParentID;
	this.FolderName = FolderName;
	this.FolderPath = FolderPath;
	this.FolderLevel = FolderLevel;
	this.IsPartnerPublic = IsPartnerPublic;
	this.IsEnterprisePublic = IsEnterprisePublic;
	this.UpdateTime = UpdateTime;
	this.FolderCount = FolderCount;
	this.FileCount = FileCount;
	}
}
