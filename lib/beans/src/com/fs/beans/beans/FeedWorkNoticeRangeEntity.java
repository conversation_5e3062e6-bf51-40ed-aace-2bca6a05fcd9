package com.fs.beans.beans;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

public class FeedWorkNoticeRangeEntity implements Serializable {
    private static final long serialVersionUID = -8166477994830630260L;
    /**
	 * 信息源ID
	 */
	@JSONField(name="a")
	public int feedID;
	/**
	 * 员工ID
	 */
	@JSONField(name="b")
	public int employeeID;
	/**
	 * 状态
	 * 1:未读
	 * 2:已读
	 * 3:已确认
	 */
	@JSONField(name="c")
	public int status;
	/**
	 * 读取时间
	 */
	@JSONField(name="d")
	public Date readTime;
	/**
	 * 确认时间
	 */
	@JSONField(name="e")
	public Date confirmTime;
	
	/**
	* 来源，同feed的source
	*/
	@JSONField(name="f")
	public int source;
	@JSONField(name="s1")
	public String sourceDescription;
	@JSONCreator
	public FeedWorkNoticeRangeEntity(@JSONField(name="a") int feedID,
			@JSONField(name="b") int employeeID,
			@JSONField(name="c") int status,
			@JSONField(name="d") Date readTime,
			@JSONField(name="e") Date confirmTime,
			@JSONField(name="f") int source,
			@JSONField(name="s1") String sourceDescription
			) {
		this.feedID = feedID;
		this.employeeID = employeeID;
		this.status = status;
		this.readTime = readTime;
		this.confirmTime = confirmTime;
		this.source = source;
		this.sourceDescription = sourceDescription;
	}
	public FeedWorkNoticeRangeEntity() {
	    super();
    }
}
