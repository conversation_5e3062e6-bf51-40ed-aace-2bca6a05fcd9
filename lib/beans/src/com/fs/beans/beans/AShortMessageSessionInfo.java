package com.fs.beans.beans;

import java.util.Date;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

public class AShortMessageSessionInfo {
	/**
	 * 会话ID。
	 */
	@JSONField(name="a")
	public int sessionID;
	/**
	 * 会话名称。
	 */
	@JSONField(name="b")
	public String name;
	/**
	 * 头像。
	 */
	@JSONField(name="c")
	public String profileImage;
	/**
	 * 会话参与人ID串（逗号分割）
	 */
	@JSONField(name="d")
	public String participantsIDs;
	/**
	 * 是否默认会话名称
	 */
	@JSONField(name="e")
	public boolean isDefaultName;
	/**
	 * 是否开启PUSH
	 */
	@JSONField(name="f")
	public boolean isPush;
	/**
	 * 是否置顶
	 */
	@JSONField(name="g")
	public boolean isTop;
	/**
	 * 是否显示昵称
	 */
	@JSONField(name="h")
	public boolean isShowNick;
	/**
	 * 置顶时间
	 */
	@JSONField(name="i")
	public Date setTopTime;
	/**
	 * 是否移除
	 */
	@JSONField(name="j")
	public boolean isDeleted;
	/**
	 * 最后清除的短消息ID(包括此消息也要清除)
	 */
	@JSONField(name="k")
	public int lastClearShortMessageID;
 
	@JSONCreator
	public AShortMessageSessionInfo(@JSONField(name="a") int sessionID,
			@JSONField(name="b") String name,
			@JSONField(name="c") String profileImage,
			@JSONField(name="d") String participantsIDs,
			@JSONField(name="e") boolean isDefaultName,
			@JSONField(name="f") boolean isPush,
			@JSONField(name="g") boolean isTop,
			@JSONField(name="h") boolean isShowNick,
			@JSONField(name="i") Date setTopTime,
			@JSONField(name="j") boolean isDeleted,
			@JSONField(name="k") int lastClearShortMessageID) {
		this.sessionID = sessionID;
		this.name = name;
		this.profileImage = profileImage;
		this.participantsIDs = participantsIDs;
		this.isDefaultName = isDefaultName;
		this.isPush = isPush;
		this.isTop = isTop;
		this.isShowNick = isShowNick;
		this.setTopTime = setTopTime;
		this.isDeleted = isDeleted;
		this.lastClearShortMessageID = lastClearShortMessageID;
	}
	
	public AShortMessageSessionInfo() {
	    super();
    }
	public AShortMessageSessionInfo(int sessionID,
			String name,
			String profileImage,
			String participantsIDs,
			boolean isDefaultName) {
		this.sessionID = sessionID;
		this.name = name;
		this.profileImage = profileImage;
		this.participantsIDs = participantsIDs;
		this.isDefaultName = isDefaultName;
	}

}
