package com.fs.beans.beans;

import com.fxiaoke.fshttp.web.KeyedObject;
import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

public class FeedRangeEntity implements KeyedObject {
	@JSONField(name="a")
	public int feedID;
	@JSONField(name="b")
	public int dataID;
	@JSONField(name="c")
	public boolean isCircle;
	public FeedRangeEntity(){
		super();
	}
	@JSONCreator
	public FeedRangeEntity(@JSONField(name="a") int feedID,
			@JSONField(name="b") int dataID,
			@JSONField(name="c") boolean isCircle) {
		super();
		this.feedID = feedID;
		this.dataID = dataID;
		this.isCircle = isCircle;
	}

	
	@Override
	public int getKey() {
		return this.feedID;
	}
}
