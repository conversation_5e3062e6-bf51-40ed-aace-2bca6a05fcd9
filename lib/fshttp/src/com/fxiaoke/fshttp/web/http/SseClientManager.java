package com.fxiaoke.fshttp.web.http;

import java.util.LinkedHashSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.alibaba.fastjson.JSONObject;

/**
 * SSE客户端管理器
 * 负责创建、查找、移除和管理SSE连接
 */
public class SseClientManager {
    private static volatile SseClientManager instance = null;

    private SseClientManager() {
    }

    public static SseClientManager getInstance() {
        if (instance == null) {
            synchronized (SseClientManager.class) {
                if (instance == null) {
                    instance = new SseClientManager();
                }
            }
        }
        return instance;
    }

    // 正在请求的sse链接，key是token_expireTimeStamp； value是SseClient
//    private final Map<String, SseClient> sseClientMap = new ConcurrentHashMap<>();
    private final ConcurrentLRUCache<String, SseClient> sseClientMap = new ConcurrentLRUCache(5);
    private final Map<String, Object> sseClientReferInfoMap = new ConcurrentHashMap<>();
    public synchronized void findOrCreateSseClient(String token, Long expireTimeStamp,
            SseClient.SseListener inputSseListener) {
        if (token == null || expireTimeStamp == null) {
            SseClientLogUtils.errorLog("findOrCreateSseClient failed: token or expireTimeStamp is null");
            return;
        }

        String sseClientId = getSseClientId(token, expireTimeStamp);
        SseClient sseClient = sseClientMap.get(sseClientId);

        if (sseClient == null) {
            SseClientLogUtils.infoLog("not findOrCreateSseClient sseId: " + sseClientId + ", and create new ");
//            SseClientLogUtils.debugLog(Log.getStackTraceString(new Exception("sseClientId:" + sseClientId)));
            sseClient = new SseClient();
            sseClient.setClientId(sseClientId);
            sseClientMap.put(sseClientId, sseClient);

            // "token", "67e586214d01287cdd7b369a", "mock", true
            // JSONObject jsonObject = new JSONObject();
            // jsonObject.put("token", "67e586214d01287cdd7b369a");
            // jsonObject.put("mock", true);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("token", token);
            jsonObject.put("expireTimeStamp", expireTimeStamp);

            sseClientMap.put(sseClientId, sseClient);
            sseClient.connectByDefaultUrl(token, jsonObject.toJSONString(), inputSseListener);
        } else {
            SseClientLogUtils.infoLog("findOrCreateSseClient sseId: " + sseClientId + ", and change listener ");
            sseClient.setListener(token, inputSseListener);
        }
    }

    public void removeSseClient(String sseClientId) {
        if (sseClientId == null) {
            return;
        }
        try {
            sseClientReferInfoMap.remove(sseClientId);
            SseClient client = sseClientMap.remove(sseClientId);
            if (client != null) {
                SseClientLogUtils.infoLog("Successfully removed SseClient with ID: " + sseClientId);
            }
        } catch (Exception e) {
            SseClientLogUtils.errorLog("Error removing SseClient: " + e.getMessage());
        }
    }

    /**
     * 生成SSE客户端ID
     * 
     * @param token           用户令牌
     * @param expireTimeStamp 过期时间戳
     * @return 客户端ID
     */
    public String getSseClientId(String token, Long expireTimeStamp) {
        return token + "_" + expireTimeStamp;
    }

    public void disconnect(String mSseClientId) {
        if (mSseClientId == null) {
            return;
        }
        SseClient sseClient = sseClientMap.get(mSseClientId);
        if (sseClient != null) {
            sseClient.disconnect();
        }
    }

    public boolean hasClient(String sseClientId) {
        if (sseClientId == null) {
            return false;
        }
        SseClient sseClient = sseClientMap.get(sseClientId);
        return sseClient != null;
    }
    public void saveReferInfo(String sseClientId, Object referInfo) {
        if (sseClientId == null) {
            return;
        }
        sseClientReferInfoMap.put(sseClientId, referInfo);
    }

    public void saveReferInfoByToken(String token, long timeStamp, Object referInfo) {
        if (token == null) {
            return;
        }
        String sseClientId = getSseClientId(token, timeStamp);
        sseClientReferInfoMap.put(sseClientId, referInfo);
        SseClientLogUtils.debugLog("saveReferInfoByToken sseClientId: " + sseClientId + ",referInfo:" + referInfo);
    }

    public String getSseClientIdByReferInfo(Object referValue) {
        String sseClientId = null;
        for (Map.Entry<String, Object> item : sseClientReferInfoMap.entrySet()) {
            if ((referValue + "").equals(item.getValue() + "")) {
                sseClientId = item.getKey();
            }
        }
        SseClientLogUtils.debugLog("getSseClientIdByReferInfo sseClientId: " + sseClientId + ",by referInfo:" + referValue);
        return sseClientId;
    }

    public SseClient getSseClient(String sseClientId) {
        if (sseClientId == null) {
            return null;
        }
        SseClient sseClient = sseClientMap.get(sseClientId);
        return sseClient;
    }

    public void clear() {
        sseClientMap.clear();
    }

    public void removeFinishedSseClient() {
        for (SseClient client : sseClientMap.cache.values()) {
            if(!client.isEnabled()){
                removeSseClient(client.getClientId());
            }
        }
    }

    protected class ConcurrentLRUCache<K, V> {
        private final int capacity;
        private final Map<K, V> cache;
        private final LinkedHashSet<K> accessOrder;
    
        public ConcurrentLRUCache(int capacity) {
            this.capacity = capacity;
            this.cache = new ConcurrentHashMap<>();
            this.accessOrder = new LinkedHashSet<>();
        }
    
        public synchronized V get(K key) {
            V value = cache.get(key);
            if (value != null) {
                // 移除旧的访问顺序
                accessOrder.remove(key);
                // 将 key 移到 LinkedHashSet 的末尾，表示最近访问
                accessOrder.add(key);
            }
            return value;
        }
    
        public synchronized void put(K key, V value) {
            if (cache.containsKey(key)) {
                // 如果 key 已经存在，先移除旧的访问顺序
                accessOrder.remove(key);
            } else if (cache.size() >= capacity) {
                // 如果缓存已满，移除最旧的元素
                K eldestKey = accessOrder.iterator().next();
                cache.remove(eldestKey);
                accessOrder.remove(eldestKey);
                SseClientLogUtils.warnLog("ConcurrentLRUCache cache.size() >= capacity remove eldestKey: " + eldestKey);
            }
            // 添加新的元素
            cache.put(key, value);
            accessOrder.add(key);
        }
        public synchronized V remove(K key) {
            if (cache.containsKey(key)) {
                // 如果 key 已经存在，先移除旧的访问顺序
                accessOrder.remove(key);
            }
            SseClientLogUtils.warnLog("ConcurrentLRUCache remove : " + key);
            return cache.remove(key);
        }

        public void clear() {
            cache.clear();
        }
    }
}
