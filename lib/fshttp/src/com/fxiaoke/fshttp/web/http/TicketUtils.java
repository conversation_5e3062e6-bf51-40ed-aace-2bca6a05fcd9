package com.fxiaoke.fshttp.web.http;

import java.util.ArrayList;
import java.util.List;

import org.json.JSONArray;
import org.json.JSONObject;

import android.app.Activity;
import android.content.SharedPreferences;

import com.facishare.fs.common_utils.JsonHelper;


public class TicketUtils {
	public static void saveTicket(List<FSTicket> NewTickets)
	{
    	List<FSTicket> fsTickets = TicketUtils.getTicket();
    	
    	if (fsTickets != null && fsTickets.size() > 0) {
			for (int i = 0; i < NewTickets.size(); i++) {
				for (int j = 0; j < fsTickets.size(); j++) {
					if (NewTickets.get(i).N.equals(fsTickets.get(j).N)) {
						fsTickets.remove(j);
						j--;
					}
				}
			}
		}
		
		
		
		fsTickets.addAll(NewTickets);
		String jsString = "";
		try {
			jsString = JsonHelper.toJsonString(fsTickets);
		} catch (Exception e) {
			// TODO: handle exception
		}
		
		saveTicketRef(jsString);
		
		
	}
	
	public static List<FSTicket> getTicket()
	{
		List<FSTicket> NewTickets = new ArrayList<FSTicket>();
//		String json = getTicketRef();
//		if (json != null) {
//			
//			NewTickets = jsonFormList(json);
//		}
		FSTicket ticket1 = new FSTicket();
		FSTicket ticket2 = new FSTicket();
		NewTickets.add(ticket1);
		NewTickets.add(ticket2);
		return NewTickets;
	}
	
	
	
	private static void saveTicketRef(String json) {
		SharedPreferences sp = WebApiUtils.getAppContext().getSharedPreferences("ticket", Activity.MODE_PRIVATE);
		sp.edit().putString("ticket", json).commit();
	}

	private static String getTicketRef() {
		SharedPreferences sp = WebApiUtils.getAppContext().getSharedPreferences("ticket", Activity.MODE_PRIVATE);
		return sp.getString("ticket", null);
	}
	
	public static void clearTicketRef()
	{
		SharedPreferences sp = WebApiUtils.getAppContext().getSharedPreferences("ticket", Activity.MODE_PRIVATE);
		sp.edit().putString("ticket", "").commit();
	}
	   /** 
     * 将一个json字串转为list 
     * @param props 
     * @return 
     */  
	private static List<FSTicket> jsonFormList(String json){  
    	
    	List<FSTicket> fsTickets = new ArrayList<FSTicket>();
  
        try {
            JSONArray jsonArray = new JSONArray(json); 
            if (jsonArray != null && jsonArray.length() >0) {
				for (int i = 0; i < jsonArray.length(); i++) {
					
					JSONObject jsonObject = (JSONObject) jsonArray.get(i);
					
					FSTicket fsTicket = new FSTicket();
					fsTicket.N = jsonObject.getString("N");
					fsTicket.V = jsonObject.getString("V");
					fsTicket.EV = jsonObject.getString("EV");
					
					fsTickets.add(fsTicket);
				}
			}
            
            
		} catch (Exception e) {
			// TODO: handle exception
		}
 
          
        return fsTickets;  
    }  
}
