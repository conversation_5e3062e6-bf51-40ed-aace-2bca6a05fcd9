package com.fs.fshttp;

import com.facishare.fs.i18n.I18NHelper;
import android.text.TextUtils;
import android.util.Log;

import com.core.http.callback.BaseCallBack;
import com.core.http.callback.StreamCallback;
import com.core.http.util.*;
import com.facishare.fs.common_utils.Base64;
import com.facishare.fs.common_utils.JsonHelper;
import com.facishare.fs.common_utils.StringUtils;
import com.fxiaoke.fshttp.web.http.Antaeus;
import com.fxiaoke.fshttp.web.http.PullParseService;
import com.fxiaoke.fshttp.web.http.ResultData;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.fxiaoke.fxlog.FCLog;

import org.apache.http.Header;
import org.apache.http.client.methods.HttpUriRequest;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;

import okhttp3.Response;


/**
 * FHEcallback
 * Created by lif on 2017/1/17.
 */

public abstract class FHECallback<T> extends BaseCallBack<T> implements okhttp3.Callback {


    private WebApiFailureType businessStatus;
    private Antaeus antaeus;
    private int failureCode;
    private String failureMessage;

    @Override
    public T handleResponse(Response response) throws IOException {
        InputStream stream = Util.cloneStream(response.body().byteStream());
        response.close();
        //解析xml
        ResultData resultData = PullParseService.pullResultData(stream);

        if(resultData == null || resultData.data.valueString == null) {
            this.failureMessage = I18NHelper.getText("lib.fhe_callback.msg.content_null")/* 非预期结果：httpresponse内容为空。 */;
            return null;
        }

        String jsonStr = resultData.data.valueString;
        T result = result2Object(resultData, jsonStr);

        //获取app升级信息
        antaeus = resultData.antaeus;


        int status = 0;
        try {
            status = Integer.parseInt(resultData.result.StatusCode);
            businessStatus = WebApiFailureType.getWebApiFailureType(status);
        } catch (Exception e) {
            businessStatus = WebApiFailureType.DfaultUnkown;
        }

        //如果成功
        if (status == WebApiFailureType.Success.getIndex()) {
            if (result != null) {
                return result;
            }
        } else {
            try {
                failureCode = Integer.parseInt(resultData.result.FailureCode);
            } catch (Exception e) {
                if (businessStatus == null) {
                    businessStatus = WebApiFailureType.DfaultUnkown;
                }
                failureCode = businessStatus.getIndex();
            }

            //获得faile的description
            String msgResult = resultData.result.FailureMessage;
            businessStatus.setResult(resultData.result);
            if (TextUtils.isEmpty(msgResult)) {
                failureMessage = WebApiFailureType.getError(status);
                if (status == WebApiFailureType.Failure.getIndex()) {
                    failureMessage = String.format(failureMessage, status, failureCode);
                }
            } else {
                failureMessage = msgResult;
            }
        }

        return result;
    }

    /**
     * 将请求结果转为对象
     */
    private T result2Object(ResultData resultData, String jsonStr) throws IOException {
        T result = null;

        Class<T> c = (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass())
                .getActualTypeArguments()[0];

        if (resultData.data.DataType.equals(WebApiUtils.FHE_DataType_Proto_B)) {    //pb
            Field f = null;
            try {
                f = c.getDeclaredField("PARSER");

                com.google.protobuf.Parser p = null;
                if (f != null) {
                    p = (com.google.protobuf.Parser) f.get(null);
                }
                if (p != null) {
                    result = (T) p.parseFrom(Base64.decode(jsonStr));

                }
            } catch (Exception e) {
                LogUtil.e("FHEcallback protobuf parse fail.", e);
                throw new IOException("FHEcallback protobuf parse fail");
            }
        } else {        //json
            result = JsonHelper.fromJsonString(jsonStr, c);
        }
        return result;
    }


//    private final void readFHE(final HttpUriRequest request, final int httpStatusCode,
//                                            final ResultData resultdata, final Header contentType,
//                                            final Header contentEncoding) {
//        this.httpStatusCode = httpStatusCode;
//
//        if (resultdata != null) {
//            if (stringCallback != null) {
//
//                T result = null;
//                String json = "";
//                try {
//                    json = resultdata.data.valueString;
//                    FCLog.d(TAG, "http rst:" + resultdata.data.valueString + getReqIdDes(cid));
//
//                    if (json != null && json.length() > 2) {
//                        Class<T> typeref = stringCallback.getTypeReferenceFHE();
//                        if (resultdata.data.DataType.equals(WebApiUtils.FHE_DataType_Proto_B)) {
//                            Field f = typeref.getDeclaredField("PARSER");
//                            com.google.protobuf.Parser p = null;
//                            if (f != null) {
//                                p = (com.google.protobuf.Parser) f.get(null);
//                            }
//                            if (p != null) {
//                                try {
//                                    result = (T) p.parseFrom(Base64.decode(resultdata.data.valueString));
//                                } catch (ClassCastException e) {
//                                    FCLog.w(TAG, Log.getStackTraceString(e));
//                                }
//                            }
//                        } else {
//                            result = JsonHelper.fromJsonString(json, typeref);
//                        }
//                    }
//                } catch (Exception e) {
//                    FCLog.w(TAG, Log.getStackTraceString(e));
//                    this.exceptionFailed("readFHE, " + Log.getStackTraceString(e));
//                    mFailureMessage = json;
//                    stringCallback.readJsonException(mExceptionString);
//                    // return;
//                }
//

//            } else if (fileCallback != null) {
//                byteArrayContent = StringUtils.base64ToByte(resultdata.attach.valueString);
//            }
//        } else {
//            this.mFailureMessage = I18NHelper.getText("lib.fhe_callback.msg.content_null")/* 非预期结果：httpresponse内容为空。 */;
//        }
//    }

}
