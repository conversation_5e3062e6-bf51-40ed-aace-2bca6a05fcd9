package com.fs.fshttp;

import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.fxiaoke.stat_engine.http.HttpLogEvent;
import com.fxiaoke.stat_engine.http.HttpLogTickManager;

import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.List;

import okhttp3.Call;
import okhttp3.Connection;
import okhttp3.EventListener;
import okhttp3.Handshake;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.Response;

public class HttpEventListener extends EventListener {
    public static final String PLAT_NATIVE = "native",PLAT_AVATAR = "avatar";
    public static final String PROTO_HTTP = "http",PROTO_FCP = "fcp";

    /**
     * 每次请求的标识
     */
    private HttpLogEvent bean;
    private long net_start_time;
    private long total_elapsed_time;
    private long dns_elapsed_time;
    private long connect_elapsed_time;
    private long request_elapsed_time;
    private long response_header_elapsed_time;
    private long response_body_elapsed_time;
    private long start_dns_elapsed_time;
    private long start_total_elapsed_time;
    private long start_connect_elapsed_time;
    private long start_request_elapsed_time;
    private long start_response_header_elapsed_time;
    private long start_response_body_elapsed_time;


    public HttpEventListener(String url,String plat,Object params) {
        this(url,plat,PROTO_HTTP,params,null,null,null);
    }
    public HttpEventListener(String url,String plat,Object params,String appId,String page,String netStartTime) {
        this(url,plat,PROTO_HTTP,params,appId,page,netStartTime);
    }
    public HttpEventListener(String url,String plat,String proto,Object params,String appId,String page,String netStartTime) {

        HttpLogTickManager tickManager = HttpLogTickManager.getInstance();
        if(tickManager==null ||!tickManager.isEnableHttpMonitorUpload()){
            return;
        }
        bean = new HttpLogEvent(url, WebApiUtils.versionCode);

        if(tickManager.isBlackUri(bean.uri)){
            bean = null;
            return;
        }
        bean.plat = plat;
        bean.proto =proto;
        bean.appId = appId;
        bean.page = page;
        if(!TextUtils.isEmpty(netStartTime)){
            try {
                net_start_time = Long.parseLong(netStartTime);
            }catch (NumberFormatException e){
            }
        }

        if(PLAT_AVATAR.equals(bean.plat)) {//小程序需要调用wx.tick_http,先放入缓存
            tickManager.putHttpLogEvent(bean);
        }

        boolean cloudEnableParam = tickManager.isEnableHttpMonitorUploadPrams();
        if(cloudEnableParam){
            if(params instanceof List){
                bean.param = JSON.toJSONString(params);
            }else if(params instanceof String){
                bean.param = params.toString();
            }
        }
    }



    @Override
    public void callStart(Call call) {
        super.callStart(call);
        start_total_elapsed_time = System.currentTimeMillis();
    }


    @Override
    public void dnsStart(Call call, String domainName) {
        super.dnsStart(call, domainName);
        start_dns_elapsed_time = System.currentTimeMillis();
    }

    @Override
    public void dnsEnd(Call call, String domainName, List<InetAddress> inetAddressList) {
        super.dnsEnd(call, domainName, inetAddressList);
        dns_elapsed_time = System.currentTimeMillis() - start_dns_elapsed_time;//dns解析耗时
    }

    @Override
    public void connectStart(Call call, InetSocketAddress inetSocketAddress, Proxy proxy) {
        super.connectStart(call, inetSocketAddress, proxy);
        start_connect_elapsed_time = System.currentTimeMillis();
    }

    @Override
    public void secureConnectStart(Call call) {
        super.secureConnectStart(call);
    }

    @Override
    public void secureConnectEnd(Call call, Handshake handshake) {
        super.secureConnectEnd(call, handshake);
    }

    @Override
    public void connectEnd(Call call, InetSocketAddress inetSocketAddress, Proxy proxy, Protocol protocol) {
        super.connectEnd(call, inetSocketAddress, proxy, protocol);
        connect_elapsed_time = System.currentTimeMillis() - start_connect_elapsed_time;
    }

    @Override
    public void connectFailed(Call call, InetSocketAddress inetSocketAddress, Proxy proxy, Protocol protocol, IOException ioe) {
        super.connectFailed(call, inetSocketAddress, proxy, protocol, ioe);
    }

    @Override
    public void connectionAcquired(Call call, Connection connection) {
        super.connectionAcquired(call, connection);
    }

    @Override
    public void connectionReleased(Call call, Connection connection) {
        super.connectionReleased(call, connection);
    }

    @Override
    public void requestHeadersStart(Call call) {
        super.requestHeadersStart(call);
        start_request_elapsed_time = System.currentTimeMillis();
    }

    @Override
    public void requestHeadersEnd(Call call, Request request) {
        super.requestHeadersEnd(call, request);
    }

    @Override
    public void requestBodyStart(Call call) {
        super.requestBodyStart(call);
    }

    @Override
    public void requestBodyEnd(Call call, long byteCount) {
        super.requestBodyEnd(call, byteCount);
        request_elapsed_time = System.currentTimeMillis() - start_request_elapsed_time;
        if(bean!=null){
            bean.reqSize = byteCount;
        }

    }

    @Override
    public void responseHeadersStart(Call call) {
        super.responseHeadersStart(call);
        start_response_header_elapsed_time = System.currentTimeMillis();
    }

    @Override
    public void responseHeadersEnd(Call call, Response response) {
        super.responseHeadersEnd(call, response);
        response_header_elapsed_time = System.currentTimeMillis() - start_response_header_elapsed_time;
        if(bean!=null){
            bean.code = response.code();
        }

    }


    @Override
    public void responseBodyStart(Call call) {
        super.responseBodyStart(call);
        start_response_body_elapsed_time = System.currentTimeMillis();

    }

    @Override
    public void responseBodyEnd(Call call, long byteCount) {
        super.responseBodyEnd(call, byteCount);
        response_body_elapsed_time = System.currentTimeMillis() - start_response_body_elapsed_time;
        if(bean!=null){
            bean.rspSize = byteCount;
        }

    }

    @Override
    public void callEnd(Call call) {
        super.callEnd(call);
        if(!HttpLogTickManager.getInstance().isEnableHttpMonitorUpload()||bean==null){
            return;
        }
        long end = System.currentTimeMillis();
        total_elapsed_time = end - start_total_elapsed_time;
        bean.stamp = start_total_elapsed_time;
        bean.status = "end";
        bean.nativeTotalCost = total_elapsed_time;
        bean.totalCost = total_elapsed_time;
        bean.dnsCost = dns_elapsed_time;
        bean.connectCost = connect_elapsed_time;
        bean.upCost = request_elapsed_time;
        bean.ttfbCost = response_header_elapsed_time;
        bean.downCost = response_body_elapsed_time;
//        if(net_start_time>0){
//            bean.avaTotalCost = end - net_start_time;
//        }
        if(PLAT_AVATAR.equals(bean.plat)){//小程序需要调用wx.tick_http，传递bizCost ,failCode,failMsg等,

        }else{
            HttpLogTickManager.getInstance().tick(bean);
        }

    }

    @Override
    public void callFailed(Call call, IOException ioe) { //第一种是在请求执行的过程中发生异常时。第二种是在请求结束后，关闭输入流时产生异常时。
        super.callFailed(call, ioe);
        if(!HttpLogTickManager.getInstance().isEnableHttpMonitorUpload()||bean==null){
            return;
        }

        total_elapsed_time = System.currentTimeMillis() - start_total_elapsed_time;

        bean.stamp = start_total_elapsed_time;
        bean.status = "fail";
        bean.error = Log.getStackTraceString(ioe);
        bean.totalCost = total_elapsed_time;

        HttpLogTickManager.getInstance().tick(bean);
    }

}

