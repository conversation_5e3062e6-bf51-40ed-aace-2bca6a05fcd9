/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.fxlog;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

import android.util.Log;

/**
 * Created by xiangd on 2016/12/19.
 */
class LogWriter extends BaseLogWriter {

    private static final String TAG = LogWriter.class.getSimpleName();
    private FileWriter mLogWriter;

    public LogWriter(File logFile, boolean writeDeviceInfo) {
        super(logFile);
        try {
            boolean exist = checkAndCreateNewFileIfNeed();
            mLogWriter = new FileWriter(logFile, true);
            if (writeDeviceInfo && !exist) {
                // 日志文件首行写入设备信息
                writeLog(TAG, LogLevel.INFO, getDeviceInfo());
            }
        } catch (IOException e) {
            Log.e(TAG, "init LogWriter, " + Log.getStackTraceString(e));
        }
    }



    @Override
    public synchronized void writeLog(String tag, int logLevel, String log) {
        writeLog(tag, LogLevel.getLevelDescription(logLevel), log);
    }

    /**
     * 将日志信息写入文件中
     */
    @Override
    public synchronized void writeLog(String tag, String logLevel, String log) {
        writeLog(getFormatLog(tag, logLevel, log));
    }

    @Override
    public synchronized void writeLog(String formatLog) {
        if (mLogWriter != null) {
            try {
                mLogWriter.append(formatLog);
                mLogWriter.flush();
            } catch (IOException e) {
                Log.e(TAG, "writeLog, " + Log.getStackTraceString(e));
            }
        } else {
            Log.e(TAG, "can not writeLog, mLogWriter = null");
        }
    }

    @Override
    public synchronized void flush() {
        try {
            if (mLogWriter != null) {
                mLogWriter.flush();
            }
        } catch (IOException e) {
            Log.e(TAG, "mLogWriter flush," + Log.getStackTraceString(e));
        }
    }

    @Override
    public synchronized void close() {
        try {
            if (mLogWriter != null) {
                mLogWriter.close();
            }
        } catch (IOException e) {
            Log.e(TAG, "mLogWriter close," + Log.getStackTraceString(e));
        }
    }

}
