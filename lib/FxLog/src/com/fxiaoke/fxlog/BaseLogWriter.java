package com.fxiaoke.fxlog;

import android.os.Build;
import android.os.Process;

import java.io.File;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created by xiangd on 2017/6/28.
 */

abstract class BaseLogWriter implements ILogWriter {

    private static DateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

    private File mLogFile;

    public BaseLogWriter(File mLogFile) {
        this.mLogFile = mLogFile;
    }

    @Override
    public long getFileSize() {
        return mLogFile.length();
    }

    @Override
    public String getFileName() {
        return mLogFile.getName();
    }

    protected boolean checkAndCreateNewFileIfNeed() throws IOException {
        boolean exist = mLogFile.exists();
        if (!exist) {
            if (!mLogFile.getParentFile().exists()) {
                mLogFile.getParentFile().mkdirs();
            }
            mLogFile.createNewFile();
        }
        return exist;
    }

    public String getDeviceInfo() {
        StringBuilder builder = new StringBuilder();
        builder.append("DeviceInfo [").append(Build.BRAND).append(", ")
                .append(Build.MODEL).append(", ")
                .append(Build.VERSION.RELEASE).append(", ")
                .append(Build.VERSION.SDK_INT).append("]");
        return builder.toString();
    }

    public static String getFormatLog(String tag, int logLevel, String log) {
        return getFormatLog(tag, LogLevel.getLevelDescription(logLevel), log);
    }

    public static String getFormatLog(String tag, String logLevel, String log) {
        StringBuilder builder = new StringBuilder();
        builder.append(DATE_FORMAT.format(new Date()))
                .append("\u0001").append(Process.myPid()).append("-").append(Process.myTid())
                .append("\u0001").append(logLevel)
                .append("\u0001").append(tag)
                .append("\u0001").append(log.replaceAll("\n|\r", "\u0002"))
                .append("\n");
        return builder.toString();
    }

}
