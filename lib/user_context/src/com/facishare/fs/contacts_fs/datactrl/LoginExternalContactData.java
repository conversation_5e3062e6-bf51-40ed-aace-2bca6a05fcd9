/*
 * Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.contacts_fs.datactrl;

import com.facishare.fs.contacts_fs.beans.EmployeeKey;
import com.facishare.fs.db.IContactDbHelper;
import com.facishare.fs.pluginapi.contact.beans.ExternalContact;
import com.facishare.fs.pluginapi.contact.beans.ExternalContactEnterprise;
import com.fxiaoke.fxlog.FCLog;
import com.lidroid.xutils.exception.DbException;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by pangc on 2018/7/30.
 */

public class LoginExternalContactData implements IExternalContactData {

    private static final String TAG = LoginExternalContactData.class.getSimpleName();

    private IContactDbHelper mDBHelper;

    public LoginExternalContactData(IContactDbHelper mContactDbHelper) {
        mDBHelper = mContactDbHelper;
    }

    @Override
    public void clear() {
        try {
            mDBHelper.getExternalContactDao().deleteAll();
            mDBHelper.getExternalContactEnterpriseDao().deleteAll();
        } catch (DbException e) {
            FCLog.e(TAG, "clear: " + e.getMessage());
        }
    }

    @Override
    public int getExtContactCount() {
        int count = 0;
        try {
            count = mDBHelper.getExternalContactDao().getCount();
        } catch (DbException e) {
            FCLog.e(TAG, "getExtContactCount: " + e.getMessage());
        }
        return count;
    }

    @Override
    public int getExtContactCountByEnterprise(String enterprise) {
        int count = 0;
        try {
            count = mDBHelper.getExternalContactDao().getCountByEnterprise(enterprise);
        } catch (DbException e) {
            FCLog.e(TAG, "getExtContactCount: " + e.getMessage());
        }
        return count;
    }

    @Override
    public ExternalContact getExternalContact(EmployeeKey employeeKey) {
        try {
            return mDBHelper.getExternalContactDao().findByEmployeeKey(employeeKey);
        } catch (DbException e) {
            FCLog.e(TAG, "getExternalContact: " + e.getMessage());
        }
        return null;
    }

    @Override
    public List<ExternalContact> getAllExternalContact() {
        try {
            return mDBHelper.getExternalContactDao().findAll();
        } catch (DbException e) {
            FCLog.e(TAG, "getAllExternalContact: " + e.getMessage());
        }
        return new ArrayList<>(0);
    }

    @Override
    public List<ExternalContact> getExternalContactByEnterprise(String enterpriseAccount) {
        try {
            return mDBHelper.getExternalContactDao().findAllByEnterpriseAccount(enterpriseAccount);
        } catch (DbException e) {
            FCLog.e(TAG, "getExternalContactByEnterprise: " + e.getMessage());
        }
        return new ArrayList<>(0);
    }

    @Override
    public void addOrUpdateExternalContact(ExternalContact externalContact) {
        try {
            mDBHelper.getExternalContactDao().saveOrUpdate(externalContact);
        } catch (DbException e) {
            FCLog.e(TAG, "addOrUpdateExternalContact: " + e.getMessage());
        }
    }

    @Override
    public void addOrUpdateExternalContacts(List<ExternalContact> externalContacts) {
        try {
            mDBHelper.getExternalContactDao().saveOrUpdateAll(externalContacts);
        } catch (DbException e) {
            FCLog.e(TAG, "addOrUpdateExternalContacts: " + e.getMessage());
        }
    }

    @Override
    public void deleteExternalContact(EmployeeKey employeeKey) {
        try {
            mDBHelper.getExternalContactDao().deleteByEmployeeKey(employeeKey);
        } catch (DbException e) {
            FCLog.e(TAG, "deleteExternalContact: " + e.getMessage());
        }
    }

    @Override
    public void deleteExternalContacts(List<EmployeeKey> employeeKeys) {
        try {
            mDBHelper.getExternalContactDao().deleteByEmployeeKeys(employeeKeys);
        } catch (DbException e) {
            FCLog.e(TAG, "deleteExternalContacts: " + e.getMessage());
        }
    }

    @Override
    public int getExtEnterpriseCount() {
        int count = 0;
        try {
            count = mDBHelper.getExternalContactEnterpriseDao().getCount();
        } catch (DbException e) {
            FCLog.e(TAG, "getExtEnterpriseCount: " + e.getMessage());
        }
        return count;
    }

    @Override
    public ExternalContactEnterprise getExternalContactEnterprise(String enterpriseAccount) {
        try {
            return mDBHelper.getExternalContactEnterpriseDao().findByEnterpriseAccount(enterpriseAccount);
        } catch (DbException e) {
            FCLog.e(TAG, "getExternalContactEnterprise: " + e.getMessage());
        }
        return null;
    }

    @Override
    public List<ExternalContactEnterprise> getAllExternalContactEnterprise() {
        try {
            return mDBHelper.getExternalContactEnterpriseDao().findAll();
        } catch (DbException e) {
            FCLog.e(TAG, "getAllExternalContactEnterprise: " + e.getMessage());
        }
        return new ArrayList<>(0);
    }

    @Override
    public void addOrUpdateExternalContactEnterprise(ExternalContactEnterprise externalContactEnterprise) {
        try {
            mDBHelper.getExternalContactEnterpriseDao().saveOrUpdate(externalContactEnterprise);
        } catch (DbException e) {
            FCLog.e(TAG, "addOrUpdateExternalContactEnterprise: " + e.getMessage());
        }
    }

    @Override
    public void addOrUpdateExternalContactEnterprise(List<ExternalContactEnterprise> externalContactEnterprise) {
        try {
            mDBHelper.getExternalContactEnterpriseDao().saveOrUpdateAll(externalContactEnterprise);
        } catch (DbException e) {
            FCLog.e(TAG, "addOrUpdateExternalContactEnterprise: " + e.getMessage());
        }
    }

    @Override
    public void deleteExternalContactEnterprise(String enterpriseAccount) {
        try {
            mDBHelper.getExternalContactEnterpriseDao().deleteByEnterpriseAccount(enterpriseAccount);
        } catch (DbException e) {
            FCLog.e(TAG, "deleteExternalContactEnterprise: " + e.getMessage());
        }
    }

    @Override
    public void deleteExternalContactEnterprise(List<String> enterpriseAccount) {
        try {
            mDBHelper.getExternalContactEnterpriseDao().deleteByEnterpriseAccountList(enterpriseAccount);
        } catch (DbException e) {
            FCLog.e(TAG, "deleteExternalContactEnterprise: " + e.getMessage());
        }
    }
}
