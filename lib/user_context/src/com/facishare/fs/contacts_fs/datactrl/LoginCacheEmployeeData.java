/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.contacts_fs.datactrl;

import com.facishare.fs.contacts_fs.picker.DepartmentPicker;
import com.facishare.fs.i18n.I18NHelper;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import com.alibaba.fastjson.TypeReference;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.db.IContactDbHelper;
import com.facishare.fs.db.dao.CircleEntityDao;
import com.facishare.fs.pluginapi.AccountManager;
import com.facishare.fs.pluginapi.IContactsDataDelegate;
import com.facishare.fs.pluginapi.contact.ContactConstants;
import com.facishare.fs.pluginapi.contact.beans.AEmpSimpleEntity;
import com.facishare.fs.pluginapi.contact.beans.CircleEntity;
import com.facishare.fs.pluginapi.contact.beans.CircleIndexLetter;
import com.facishare.fs.pluginapi.contact.beans.DimensionNode;
import com.facishare.fs.pluginapi.contact.beans.EmpIndexLetter;
import com.facishare.fs.pluginapi.contact.beans.EmpShortEntity;
import com.facishare.fs.pluginapi.contact.beans.StopEmpEntity;
import com.facishare.fs.pluginapi.contact.beans.User;
import com.fs.beans.beans.DetailEmployeeVo;
import com.fs.beans.beans.EmployeeBaseInfo;
import com.fs.beans.beans.GetDetailEmployeeResult;
import com.fs.beans.beans.UserInitialData;
import com.fxiaoke.dataimpl.msg.MsgDataController;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fxdblib.beans.EmployeeReferenceLocal;
import com.fxiaoke.fxlog.FCLog;
import com.lidroid.xutils.exception.DbException;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.util.LruCache;
import android.util.SparseArray;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 16-10-2015 14:27:37
 */
public class LoginCacheEmployeeData implements ICacheEmployeeData, IContactsDataDelegate {
    private Context mContext;
    private SparseArray<AEmpSimpleEntity> eMap = null;
    private IContactDbHelper mContactDbHelper;
    private List<Integer> mStagedAsteriskEmployees;
    private List<Integer> mStagedAsteriskCircles;
//    private boolean mIsALevelDataUpdating = false;
    private LruCache<Integer, String> circleSimpleCache = new LruCache<Integer, String>(1000);
    private final Object PRESENT = new Object();

    public static final String S_NOT_ENGLISH_STRING = "#";
    public static final int COMPANY_ALL = 999999;


    private EmpDepCache mEmpDepCache;

    private DimensionCache mDimensionCache;

    public LoginCacheEmployeeData(Context context, IContactDbHelper contactDbHelper,EmpDepCache empDepCache) {
        mContactDbHelper = contactDbHelper;
        mContext = context;
        mEmpDepCache = empDepCache;
    }

    public DimensionCache getDimensionCache() {
        if(mDimensionCache == null) {
            mDimensionCache = new DimensionCache(mContactDbHelper);
        }
        return mDimensionCache;
    }

    @Override
    public List<CircleIndexLetter> getOrderedCirclesOnlyIndexLetterThatEmpDirectIn(int empid){
        List<CircleIndexLetter> ret=new ArrayList<>();

        if (mEmpDepCache!=null){
            ret=mEmpDepCache.getOrderedDepsWithOnlyIndexLetterByIds(getDirectDepidsByEmpid(empid));
        }
        return ret;
    }
    List<Integer> getDirectDepidsByEmpid(int empid){
        List<Integer> ret=new ArrayList<>();
        if (mEmpDepCache!=null){
            AEmpSimpleEntity empSimpleEntity=mEmpDepCache.getAEmpSimpleEntity(empid);
            String depsStr=empSimpleEntity.getDepartment();
            if(depsStr != null){
                String[] depids=depsStr.split(",");
                if (depids!=null&&depids.length>0){
                    for (String depid:depids
                    ) {
                        try {
                            ret.add(Integer.valueOf(depid));
                        }catch (NumberFormatException e){
                            e.printStackTrace();
                        }
                    }
                }
            }
        }
        return ret;
    }
    @Override
    public List<CircleEntity> getOrderedCirclesThatEmpAllIn(int empid) {
        Map<Integer,Object> alldepids=new HashMap<>();
        List<Integer> depsids=getDirectDepidsByEmpid(empid);
        for (Integer depid :
                depsids) {
            alldepids.put(depid,PRESENT);
            CircleEntity ce=getDepByDepid(depid);
            String path=ce.getPath();
            if (path!=null&&path.length()>0){
                String[] cidsStr=path.split("-");
                if (cidsStr!=null&&cidsStr.length>0){
                    for (String cidStr :
                            cidsStr) {
                        try{
                            alldepids.put(Integer.valueOf(cidStr),PRESENT);
                        }catch (NumberFormatException e){
                            e.printStackTrace();
                        }
                    }
                }
            }
        }
        List<CircleEntity> ret=new ArrayList<>();
        if (mEmpDepCache!=null){
            List<CircleIndexLetter> ordered=mEmpDepCache.orderDepsWithIndexLetter(alldepids);
            for (CircleIndexLetter ce :
                    ordered) {
                ret.add(getDepByDepid(ce.getCircleID()));
            }
        }
        return ret;
    }

    @Override
    public List<Integer> getOrderedCirclesThatEmpDirectIn(int empid) {
        List<Integer> ret=new ArrayList<>();
        if (mEmpDepCache!=null){
            List<CircleIndexLetter> deps=getOrderedCirclesOnlyIndexLetterThatEmpDirectIn(empid);
            for (CircleIndexLetter ce :
                    deps) {
                ret.add(ce.getCircleID());
            }
        }
        return ret;
    }

    @Override
    public String getDepNames(String depids) {
        return getDepsString(depids);
    }

    @Override
    public Map<String, String> getDepNameList(List<String> depIdList) {
        Map<String, String> resultMap = new ConcurrentHashMap<>();
        try {
            int count = 0;
            for (String depid : depIdList) {
                if (TextUtils.isEmpty(depid)) {
                    continue;
                }
                Integer id = Integer.valueOf(depid);
                String name = circleSimpleCache.get(id);
                if (name == null) {
                    continue;
                } else {
                    resultMap.put(depid, name);
                    count++;
                }
            }
            if (count == depIdList.size()) {
                return resultMap;
            }
            List<CircleEntityDao.CircleSimpleEntity> simples = mContactDbHelper.getCircleEntityDao().findDirectSimpleByEmployeeId(depIdList);
            if (simples == null || simples.size() == 0) {
                return resultMap;
            }
            for (CircleEntityDao.CircleSimpleEntity simple : simples) {
                resultMap.put(simple.circleID + "", simple.name);
                circleSimpleCache.put(simple.circleID, simple.name);
            }
        } catch (Exception e) {
            FCLog.e("LoginCacheEmployeeData", Log.getStackTraceString(e));
        }
        return resultMap;
    }

    @Override
    public void updateDepSimpleCache(int depid, String name) {
        if (!TextUtils.isEmpty(name)) {
            circleSimpleCache.put(depid, name);
        }
    }

    @Override
    public String getDepsString(String depids) {
        String ret = "";
        if (TextUtils.isEmpty(depids)) {
            return ret;
        }
        String[] deps = depids.split(",");
        if (deps == null || deps.length == 0) {
            return ret;
        }
        try {
            int a = Integer.valueOf(deps[0]);
        } catch (NumberFormatException e) {
            return depids;
        }

        boolean isfirst = true;
        int i = 0;
        for (String depid : deps
                ) {
            Integer id = Integer.valueOf(depid);
            String name = circleSimpleCache.get(id);
            if (name == null) {
                ret = "";
                break;
            } else {
                i++;
                if (isfirst) {
                    isfirst = false;
                    ret += name;
                } else {
                    ret += "," + name;
                }
            }
        }
        if (i == deps.length) {
            return ret;
        }
        List<CircleEntityDao.CircleSimpleEntity> simples = null;
        try {
            simples = mContactDbHelper.getCircleEntityDao().findDirectSimpleByEmployeeId(Arrays.asList(depids));
        } catch (DbException e) {
            e.printStackTrace();
        }
        if (simples == null || simples.size() == 0) {
            return ret;
        }
        isfirst = true;
        for (CircleEntityDao.CircleSimpleEntity simple : simples
                ) {
            if (isfirst) {
                isfirst = false;
                ret += simple.name;
            } else {
                ret += "," + simple.name;
            }
            circleSimpleCache.put(simple.circleID, simple.name);
        }
        return ret;
    }


    public List<EmpIndexLetter> getAllOrderEmployees(int depid) {
        return mEmpDepCache.getAllOrderEmployees(depid);

    }

    public synchronized void loadCache() {
//        if (!mIsALevelDataUpdating) {
        FSContextManager.getCurUserContext().getContactSynchronizer().loadEmployeeData();
//        }

    }

    public List<AEmpSimpleEntity> getEmployeeCache() {
//        List<AEmpSimpleEntity> employeeList = new ArrayList<>();
////        AEmployeeData data = getCache();
////
////        if (data != null) {
////            employeeList.addAll(data.employees);
////        }
////        return employeeList;

        return  mEmpDepCache.getAllEmployees();

    }

    public EmpDepCache getEmpDepCache(){
        return mEmpDepCache;
    }

    /**
     * 获取全部已停用员工
     *
     * @return List<StopEmpEntity>
     */
    @Override
    public List<StopEmpEntity> getAllStopEmp() {
        List<StopEmpEntity> stopEmpEntities = new ArrayList<>();
        try {
            stopEmpEntities = mContactDbHelper.getStopEmployeeEntityDao().findAll();
        } catch (DbException e) {
            e.printStackTrace();
        }
        return stopEmpEntities;
    }

    /**
     * 根据ID获取已停用员工
     * @param ids
     * @return List<StopEmpEntity>
     */
    @Override
    public List<StopEmpEntity> getStopEmpByIds(List<Integer> ids) {
        List<StopEmpEntity> stopEmpEntities = new ArrayList<>();
        try {
            stopEmpEntities = mContactDbHelper.getStopEmployeeEntityDao().findByIds(ids);
        } catch (DbException e) {
            e.printStackTrace();
        }
        return stopEmpEntities;
    }

//    private List<AEmpSimpleEntity> getEmployeeCacheOrig() {
//        List<AEmpSimpleEntity> employeeList = null;
//        AEmployeeData data = getCache();
//
//        if (data != null) {
//            employeeList = data.employees;
//        }
//        return employeeList;
//    }

    public synchronized SparseArray<AEmpSimpleEntity> getEmployeeMapCache() {
        if (eMap != null && eMap.size() > 0) {
            return eMap;
        } else {
            eMap = new SparseArray<>();
        }

        List<AEmpSimpleEntity> aEmpSimpleEntities = mEmpDepCache.getAllEmployees();
        int count = 0;
        if (aEmpSimpleEntities != null && aEmpSimpleEntities.size() > 0) {
            count = aEmpSimpleEntities.size();
        }

        for (int i = 0; i < count; i++) {
            eMap.append(aEmpSimpleEntities.get(i).employeeID, aEmpSimpleEntities.get(i));
        }

        return eMap;
    }

    @Override
    public List<CircleEntity> getCirclesCache() {
//        List<CircleEntity> circleList = new ArrayList<>();
//        AEmployeeData data = getCache();
//
//        if (data != null) {
//            circleList.addAll(data.circles);
//        }
//        return circleList;
        return mEmpDepCache.getAllDeps();
    }

    @Override
    public List<EmpIndexLetter> getOrderEmployeeCache() {
        return mEmpDepCache.getOrderEmps();
    }
    @Override
    public int getAllEmpCount(){
        int ret=0;
        if (mEmpDepCache!=null){
            ret=mEmpDepCache.getAllEmpCount();
        }
        return ret;
    }


    @Override
    public List<CircleIndexLetter> getOrderCirclesCache() {
        return mEmpDepCache.getOrderDeps();
    }

    public CircleEntity getCircleEntityForId(int circleId) {
         return mEmpDepCache.getCircleEntity(circleId);

//        List<CircleEntity> circleEntities = getCirclesCache();
//        if (circleEntities != null && circleEntities.size() > 0) {
//            for (CircleEntity circleEntity : circleEntities) {
//                if (circleEntity.circleID == circleId) {
//                    return circleEntity;
//                }
//            }
//        }
//
//        return  null;

//        // 处理全公司的逻辑
//        CircleEntity ret = new CircleEntity();
//        if (COMPANY_ALL == circleId) {
//            ret.circleID = circleId;
//            ret.name = FSContextManager.getCurUserContext().getAccount().getAllCompany();
//        } else {
//            ret.circleID = -1;
//            ret.name = I18NHelper.getText("lib.logincacheemployeedata.text.deactivate_department")/* 停用部门 */;
//        }
//        return ret;
    }

    // 修改同事星标
    public void setEmpStar(int empid, boolean isAsterisk) {
        AEmpSimpleEntity aEmpSimpleEntity = mEmpDepCache.getAEmpSimpleEntity(empid);

        if (aEmpSimpleEntity != null) {
            aEmpSimpleEntity.setAsterisk(isAsterisk);
        }
    }

    // 修改同事星标
    public void setCircleStar(int circleId, boolean isAsterisk) {

        CircleEntity circleEntity = mEmpDepCache.getCircleEntity(circleId);
        if(circleEntity != null){
            circleEntity.setAsterisk(isAsterisk);
        }
//        List<CircleEntity> circleEntities = getCirclesCache();
//
//        if (circleEntities != null && circleEntities.size() > 0) {
//            for (CircleEntity circleEntity : circleEntities) {
//                if (circleEntity.circleID == circleId) {
//                    circleEntity.setAsterisk(isAsterisk);
//                }
//            }
//        }

    }

    public List<CircleEntity> FilterNullDataCircle(List<CircleEntity> dataCircleEntities) {

        if (dataCircleEntities != null && dataCircleEntities.size() > 0) {

            for (int i = 0; i < dataCircleEntities.size(); i++) {
                if (dataCircleEntities.get(i) != null && dataCircleEntities.get(i).name.length() == 0) {
                    dataCircleEntities.remove(i);
                    i--;
                }
            }
        }

        return dataCircleEntities;
    }

    /**
     * 获得我所在的部门
     */
    public List<Integer> getMyAllCircles() {
        int id = FSContextManager.getCurUserContext().getAccount().getEmployeeIntId();
        AEmpSimpleEntity ae = mEmpDepCache.getAEmpSimpleEntity(id);
        String[] circls = ae.getDepartment().split(",");
        List<Integer> list = new ArrayList<>();
        for(String cid:circls){
            try{
                list.add(Integer.parseInt(cid));
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        return list;
    }

    /**
     * 根据id集合获得员工的IndexLetter列表
     */
    public List<EmpIndexLetter> getOrderedEmpListWihtOnlyIndexLetterByArrayID(Collection<Integer> collection) {
        return mEmpDepCache.getOrderedOnlyLetterEmpList(collection);
    }
    /**
     * 根据Id集合获取不排序的员工信息集合
     * */
    public List<AEmpSimpleEntity> getEmpListByArrayID(Collection<Integer> collection) {
        return mEmpDepCache.getJobedEmpList(collection);
    }

    /**
     * 根据Id集合获取排序的员工信息集合
     * */
    public List<AEmpSimpleEntity> getOrderedEmpListByArrayID(Collection<Integer> collection) {
        if (collection == null || collection.size() < 1) {
            return new ArrayList<AEmpSimpleEntity>(0);
        }

        List<AEmpSimpleEntity> ret=new ArrayList<>();
        List<EmpIndexLetter> ordered=mEmpDepCache.orderEmpsWithIndexLetter(collection);
        for (EmpIndexLetter ce :
                ordered) {
            ret.add(mEmpDepCache.getAEmpSimpleEntity(ce.employeeID));
        }
        return ret;
    }
    public Collection<AEmpSimpleEntity> getOrderedEmpListByEmpList(Collection<AEmpSimpleEntity> collection){
        Collection<AEmpSimpleEntity> ret=new ArrayList<>();
        if (collection == null || collection.size() < 1) {
            return ret;
        }
        List<EmpIndexLetter> ordered=mEmpDepCache.orderEmpsByEmpList(collection);
        for (EmpIndexLetter ce :
                ordered) {
            ret.add(mEmpDepCache.getAEmpSimpleEntity(ce.employeeID));
        }
        collection.clear();
        collection.addAll(ret);
        return collection;
    }

    @Override
    public EmpDepCache.EmpWithPostion getEmpPostionInOrderList(int EmpId) {
        return mEmpDepCache.getEmpPostionInOrderList(EmpId);
    }

    public List<EmpDepCache.EmpWithPostion> getEmpPostionInOrderList(Set<Integer> ids) {
        List<EmpDepCache.EmpWithPostion> ret=new ArrayList<>();
        Iterator<Integer> it=ids.iterator();
        while (it.hasNext()){
            EmpDepCache.EmpWithPostion pos=mEmpDepCache.getEmpPostionInOrderList(it.next());
            if(pos!=null){
                ret.add(pos);
            }
        }

        return ret;
    }
    public EmpDepCache.DepWithPostion getDepPostionInOrderList(int EmpId) {
        return mEmpDepCache.getDepPostionInOrderList(EmpId);
    }

    public List<EmpDepCache.DepWithPostion> getDepPostionInOrderList(Set<Integer> ids) {
        List<EmpDepCache.DepWithPostion> ret=new ArrayList<>();
        Iterator<Integer> it=ids.iterator();
        while (it.hasNext()){
            EmpDepCache.DepWithPostion pos=mEmpDepCache.getDepPostionInOrderList(it.next());
            if(pos!=null){
                ret.add(pos);
            }
        }

        return ret;
    }
    private AEmpSimpleEntity findStopEmpEntityById(int id){
        return mEmpDepCache.findStopEmpById(id);
    }

    public List<CircleEntity> getDepListByArrayID(Collection<Integer> collection) {
        return getDepListByArrayID(collection, false);
    }
    /**
     * 根据id集合获得部门的信息列表
     */
    public List<CircleEntity> getDepListByArrayID(Collection<Integer> collection, boolean searchStopCircle) {
        if (collection == null || collection.size() < 1) {
            return new ArrayList<CircleEntity>(0);
        }
        ArrayList<CircleEntity> departments = new ArrayList<CircleEntity>(collection.size());
        for (Integer id : collection) {
            CircleEntity os = mEmpDepCache.getCircleEntity(id);
            if (os != null) {
                departments.add(os);
            }
        }
        if (searchStopCircle) {
            DepartmentPicker.addStopCircles(collection, departments);
        }
        return departments;
    }

    //根据circleOrder字段排序
    public List<CircleEntity> getOrderedDepListByDeps(Collection<CircleEntity> collection){
        List<CircleEntity> ret=new ArrayList<>();
        do {
            if (collection==null||collection.size()==0){
                break;
            }
            if (mEmpDepCache!=null){
                ret=mEmpDepCache.getCircleOrderList(collection);
            }
        }while (false);

        return ret;
    }

    @Override
    public List<CircleEntity> getOrderedChargedDepList(int empid) {
        List<CircleEntity> ret=new ArrayList<>();
        do {
            if (mEmpDepCache==null){
                break;
            }
            SparseArray<OrganizationStructure> depscache=mEmpDepCache.getOsCache();
            if (depscache==null||depscache.size()==0){
                break;
            }
            for (int i = 0; i < depscache.size(); i++) {
                OrganizationStructure dep = depscache.valueAt(i);
                if (dep.getDep().getPrincipalId()==empid){
                    ret.add(dep.getDep());
                }
            }
            if (ret.size()>0){

                HashMap<Integer, Object> idmaps = new HashMap<>();
                for (CircleEntity ce : ret) {
                    idmaps.put(ce.circleID, PRESENT);
                }
                ret = mEmpDepCache.orderDeps(idmaps);
            }
        }while (false);

        return ret;
    }



    @Override
    public List<CircleEntity> getOrderedDepListByArrayID(Collection<Integer> collection) {
        if (collection == null || collection.size() < 1) {
            return new ArrayList<CircleEntity>(0);
        }

        Map<Integer,Object> list=new HashMap<>();
        for (int id:
             collection) {
            list.put(id,PRESENT);
        }
        List<CircleEntity> ret=new ArrayList<>();
        List<CircleIndexLetter> ordered = mEmpDepCache.orderDepsWithIndexLetter(list);
        for (CircleIndexLetter ce :
                ordered) {
            ret.add(mEmpDepCache.getCircleEntity(ce.circleID));
        }
        return ret;
    }
    @Override
    public List<CircleIndexLetter> getOrderedDepListWihtOnlyIndexLetterByArrayID(Collection<Integer> collection) {
        if (collection == null || collection.size() < 1) {
            return new ArrayList<CircleIndexLetter>(0);
        }

        Map<Integer,Object> list=new HashMap<>();
        for (int id:
                collection) {
            list.put(id,PRESENT);
        }
        List<CircleIndexLetter> ordered = mEmpDepCache.orderDepsWithIndexLetter(list);

        return ordered;
    }

    public HashMap<Integer, String> getDepMapByArrayID(Collection<Integer> collection) {
        if (collection == null || collection.size() < 1) {
            return null;
        }
        HashMap<Integer, String> departmentsMap = new LinkedHashMap<Integer, String>();
        for (Integer id : collection) {
            CircleEntity emp = mEmpDepCache.getCircleEntity(id);
            if (emp != null) {
                departmentsMap.put(emp.circleID, emp.getI18NName());
            }
        }
        return departmentsMap;
    }

    @SuppressLint("UseSparseArrays")
    public HashMap<Integer, String> getEmpMapByArrayID(Collection<Integer> collection) {
        HashMap<Integer, String> data = new HashMap<Integer, String>(0);
        AEmpSimpleEntity entity;
        for (Integer id : collection) {
            if ((entity = mEmpDepCache.getAEmpSimpleEntity(id)) != null) {
                data.put(id, entity.name);
            }
        }
        return data;
    }

    /**
     * 批量获取员工的头像
     */
    @SuppressLint("UseSparseArrays")
    public HashMap<Integer, AEmpSimpleEntity> getEmployeeMapByArrayID(
            Collection<Integer> collection) {
        HashMap<Integer, AEmpSimpleEntity> data = new HashMap<>(0);
        AEmpSimpleEntity entity;
        for (Integer id : collection) {
            if ((entity = mEmpDepCache.getAEmpSimpleEntity(id)) != null) {
                data.put(id, entity);
            }
        }
        return data;
    }

    public EmpShortEntity getEmpShortEntity() {
        int id = FSContextManager.getCurUserContext().getAccount().getEmployeeIntId();

        AEmpSimpleEntity aEmpSimpleEntity = mEmpDepCache.getAEmpSimpleEntity(id);
        if(aEmpSimpleEntity != null){
            EmpShortEntity mEmpShortEntity =
                    new EmpShortEntity(aEmpSimpleEntity.employeeID, aEmpSimpleEntity.name,
                            aEmpSimpleEntity.profileImage, aEmpSimpleEntity.getNameSpell());
            return mEmpShortEntity;
        }
        return null;

//
//        List<AEmpSimpleEntity> list = getEmployeeCacheOrig();
//        if (list != null) {
//            for (AEmpSimpleEntity aEmpSimpleEntity : list) {
//                if (aEmpSimpleEntity.employeeID == id) {
//                    EmpShortEntity mEmpShortEntity =
//                            new EmpShortEntity(aEmpSimpleEntity.employeeID, aEmpSimpleEntity.name,
//                                    aEmpSimpleEntity.profileImage, aEmpSimpleEntity.getNameSpell());
//                    return mEmpShortEntity;
//                }
//            }
//        }
//        AEmpSimpleEntity aEmpSimpleEntity = findStopEmpEntityById(id);
//        if(aEmpSimpleEntity != null){
//            EmpShortEntity mEmpShortEntity =
//                    new EmpShortEntity(aEmpSimpleEntity.employeeID, aEmpSimpleEntity.name,
//                            aEmpSimpleEntity.profileImage, aEmpSimpleEntity.getNameSpell());
//            return mEmpShortEntity;
//        }
//
//        return null;
    }

    public void updateEmpInfo(int employeeID, String profileImage) {

        AEmpSimpleEntity aEmpSimpleEntity = mEmpDepCache.getAEmpSimpleEntity(employeeID);
        if(aEmpSimpleEntity != null){
            aEmpSimpleEntity.profileImage = profileImage;
        }

//        List<AEmpSimpleEntity> list = getEmployeeCacheOrig();
//        if (list != null) {
//            for (AEmpSimpleEntity aEmpSimpleEntity : list) {
//                if (aEmpSimpleEntity.employeeID == employeeID) {
//                    aEmpSimpleEntity.profileImage = profileImage;
//                }
//            }
//        }
    }

    public EmpShortEntity getEmpShortEntity(int employeeID) {
//        List<AEmpSimpleEntity> list = getEmployeeCacheOrig();
//        if (list != null) {
//            for (AEmpSimpleEntity aEmpSimpleEntity : list) {
//                if (aEmpSimpleEntity.employeeID == employeeID) {
//                    EmpShortEntity mEmpShortEntity =
//                            new EmpShortEntity(aEmpSimpleEntity.employeeID, aEmpSimpleEntity.name,
//                                    aEmpSimpleEntity.profileImage, aEmpSimpleEntity.getNameSpell());
//                    return mEmpShortEntity;
//                }
//            }
//        }
//
//        AEmpSimpleEntity aEmpSimpleEntity = findStopEmpEntityById(employeeID);
//        if(aEmpSimpleEntity != null){
//            EmpShortEntity mEmpShortEntity =
//                    new EmpShortEntity(aEmpSimpleEntity.employeeID, aEmpSimpleEntity.name,
//                            aEmpSimpleEntity.profileImage, aEmpSimpleEntity.getNameSpell());
//            return mEmpShortEntity;
//        }
        AEmpSimpleEntity aEmpSimpleEntity = mEmpDepCache.getAEmpSimpleEntity(employeeID);
        if(aEmpSimpleEntity != null){
            EmpShortEntity mEmpShortEntity =
                    new EmpShortEntity(aEmpSimpleEntity.employeeID, aEmpSimpleEntity.name,
                            aEmpSimpleEntity.profileImage, aEmpSimpleEntity.getNameSpell());
            return mEmpShortEntity;
        }

        return getEmpShortEntityNullData(employeeID);
    }

    public AEmpSimpleEntity getEmpShortEntityEX(int employeeID) {
        return mEmpDepCache.getUserFromLocal(employeeID).getmAEmpSimpleEntity();
    }

    public String getEmpNameById(int employeeId) {
        AEmpSimpleEntity employee = getEmpShortEntityEX(employeeId);
        return employee == null ? "" : employee.name;
    }

    // ---新企信用
    public EmpShortEntity getEmpShortEntityNew(int employeeID) {
        // List<AEmpSimpleEntity> list = CacheEmployeeData.getEmployeeCache();
        EmpShortEntity mEmpShortEntity = null;

        AEmpSimpleEntity aEmpSimpleEntity = mEmpDepCache.getAEmpSimpleEntity(employeeID);
//
//        if (getEmployeeMapCache() != null && getEmployeeMapCache().size() > 0) {
//            aEmpSimpleEntity = getEmployeeMapCache().get(employeeID);
//        }
//
//        if (aEmpSimpleEntity == null) {
//            aEmpSimpleEntity = findStopEmpEntityById(employeeID);
//        }

        if (aEmpSimpleEntity != null) {
            mEmpShortEntity = new EmpShortEntity(aEmpSimpleEntity.employeeID, aEmpSimpleEntity.name,
                    aEmpSimpleEntity.profileImage, aEmpSimpleEntity.getNameSpell());
            return mEmpShortEntity;
        }

        mEmpShortEntity = getEmpShortEntityNullData(employeeID);

        return mEmpShortEntity;
    }

    public boolean isPeople(String name) {
        boolean ret = false;
        if(!TextUtils.isEmpty(name)){
            List<Integer> list = mEmpDepCache.findEmpByName(name);
            if(list != null && list.size() != 0){
                ret = true;
            }
        }
        return ret;
    }

    // ---新企信用
    public AEmpSimpleEntity getEmpShortEntityEXNew(int employeeID) {
        AEmpSimpleEntity aEmpSimpleEntity = mEmpDepCache.getAEmpSimpleEntity(employeeID);
        if (aEmpSimpleEntity != null) {
            return aEmpSimpleEntity;
        }

        aEmpSimpleEntity = getAEmpSimpleEntityNullData(employeeID);
        return aEmpSimpleEntity;
    }

    // ---新企信用，过滤了“离职者”等无效数据。
    public AEmpSimpleEntity getEmpShortEntityExceptLeaversEXNew(int employeeID) {
        AEmpSimpleEntity aEmpSimpleEntity = mEmpDepCache.getAEmpSimpleEntityWithoutFormerEmp(employeeID);

        if (aEmpSimpleEntity != null) {
            return aEmpSimpleEntity;
        }

        return null;
    }

//    public CircleEntity getOrgByDepid(int depid) {
//        CircleEntity ret = null;
//        //        if (mEmployeeData!=null && mEmployeeData.circles != null && mEmployeeData.circles.size() > 0) {
//        //            for (CircleEntity c : mEmployeeData.circles) {
//        //                if (c.circleID == depid) {
//        //                    ret = c;
//        //                    break;
//        //                }
//        //            }
//        //        }
//
//        ret= mEmpDepCache.getCircleEntity(depid);
//        if (ret == null) {
//            ret = getNullCircle(depid);
//        }
//        return ret;
//
//    }


    /**
     * 根据员工id获取所在的部门的所有组织
     * @param empid
     * @return
     */
    @Override
    public List<CircleEntity> getOrgsByEmpId(int empid) {

        List<CircleEntity> ret=new ArrayList<>();
        if (mEmpDepCache!=null){
            AEmpSimpleEntity empSimpleEntity=mEmpDepCache.getAEmpSimpleEntity(empid);
            String depsStr=empSimpleEntity.getDepartment();
            if(depsStr != null){
                String[] depids=depsStr.split(",");
                if (depids!=null&&depids.length>0){
                    for (String depid:depids
                    ) {
                        try {
                            CircleEntity dep = getOrgByDepId(Integer.valueOf(depid));
                            if(dep!=null){
                                ret.add(dep);
                            }
                        }catch (NumberFormatException e){
                            e.printStackTrace();
                        }
                    }
                }
            }

        }
        return ret;

    }

    /**
     * 根据员工id获取所在的部门的所有组织id
     * */
    @Override
    public List<Integer> getOrgIdsByEmpId(int empid) {
        List<Integer> ret=new ArrayList<>();
        if (mEmpDepCache!=null){
            AEmpSimpleEntity empSimpleEntity=mEmpDepCache.getAEmpSimpleEntity(empid);
            String depsStr=empSimpleEntity.getDepartment();
            if(depsStr != null){
                String[] depIds=depsStr.split(",");
                if (depIds!=null&&depIds.length>0){
                    for (String depId:depIds) {
                        try {
                            CircleEntity dep = getOrgByDepId(Integer.valueOf(depId));
                            if(dep!=null){
                                ret.add(dep.getCircleID());
                            }
                        }catch (NumberFormatException e){
                            e.printStackTrace();
                        }
                    }
                }
            }

        }
        return ret;
    }

    /**
     * 根据员工id获取所在的主属组织
     * @param empid
     * @return
     */
    @Override
    public CircleEntity getMainOrgByEmpId(int empid) {
        if (mEmpDepCache!=null){
            AEmpSimpleEntity empSimpleEntity=mEmpDepCache.getAEmpSimpleEntity(empid);
           int depId = empSimpleEntity.getMainDepartment();
           return getOrgByDepId(depId);

        }
        return null;
    }




    /**
     * 获取部门的主属组织
     * @param depId
     * @return
     */
    public CircleEntity getOrgByDepId(int depId){
        CircleEntity dep = mEmpDepCache.getCircleEntity(depId);
        if(dep != null){
            while (dep.parentID>0){
                dep  = mEmpDepCache.getCircleEntity(dep.parentID);
                if(dep ==null){
                    break;
                }
                if(ContactConstants.DEP_ORGANIZATION.equals(dep.recordType)){
                    return dep;
                }
            }
        }
        return null;

    }



    public CircleEntity getDepByDepid(int depid) {
        CircleEntity ret = null;
        //        if (mEmployeeData!=null && mEmployeeData.circles != null && mEmployeeData.circles.size() > 0) {
        //            for (CircleEntity c : mEmployeeData.circles) {
        //                if (c.circleID == depid) {
        //                    ret = c;
        //                    break;
        //                }
        //            }
        //        }

        ret= mEmpDepCache.getCircleEntity(depid);
        if (ret == null) {
            ret = getNullCircle(depid);
        }
        return ret;

    }

    //处理空部门
    public CircleEntity getNullCircle(int depid) {
        CircleEntity ret = new CircleEntity();
        if (COMPANY_ALL == depid) {
            ret.name = FSContextManager.getCurUserContext().getAccount().getAllCompany();
        } else {
            ret.name = I18NHelper.getText("lib.logincacheemployeedata.text.deactivate_department")/* 停用部门 */;
        }
        ret.setNameSpell("tybm");
        ret.circleID = depid;

        return ret;
    }

    public String getDepNameByDepId(int depId) {
        CircleEntity circleEntity = getDepByDepid(depId);
        return circleEntity == null ? "" : circleEntity.getI18NName();
    }

    private EmpShortEntity getEmpShortEntityNullData(int eid) {
        EmpShortEntity aEmpSimpleEntity = new EmpShortEntity();
        if (mContext != null) {

            EmployeeReferenceLocal erl =
                    MsgDataController.getInstace(mContext).getEmployeeReferenceLocalByEid(eid);
            if (erl == null) {

            } else {
                aEmpSimpleEntity.name = erl.getEmployeeName();
                aEmpSimpleEntity.employeeID = eid;
            }
        }

        if (aEmpSimpleEntity.name == null) {
            String defaultUserName = User.defaultUserName(eid);
            aEmpSimpleEntity.name = defaultUserName;
            aEmpSimpleEntity.nameSpell = defaultUserName;
        }

        return aEmpSimpleEntity;
    }

    private AEmpSimpleEntity getAEmpSimpleEntityNullData(int eid) {
        AEmpSimpleEntity aEmpSimpleEntity = new AEmpSimpleEntity();
        if (mContext != null) {
            EmployeeReferenceLocal erl =
                    MsgDataController.getInstace(mContext).getEmployeeReferenceLocalByEid(eid);
            if (erl != null) {
                aEmpSimpleEntity.name = erl.getEmployeeName();
                aEmpSimpleEntity.employeeID = eid;
            }
        }

        if (aEmpSimpleEntity.name == null) {
            String defaultUserName = User.defaultUserName(eid);
            aEmpSimpleEntity.name = defaultUserName;
            aEmpSimpleEntity.setNameSpell(defaultUserName);
            aEmpSimpleEntity.employeeID = eid;
        }

        return aEmpSimpleEntity;
    }

    /**
     * 从通讯录缓存获取自己的员工信息 初始化自己的信息
     */
    // todo..
    public void initEmpSelfInfo(final WebApiExecutionCallback<EmpShortEntity> callback) {
        int id = FSContextManager.getCurUserContext().getAccount().getEmployeeIntId();
        List<Integer> ids = new ArrayList<>();
        ids.add(id);
        FSContextManager.getCurUserContext().getContactSynchronizer().getDetailEmployeesByIds(ids,
                new WebApiExecutionCallback<GetDetailEmployeeResult>() {

                    @Override
                    public Class<GetDetailEmployeeResult> getTypeReferenceFHE() {
                        return GetDetailEmployeeResult.class;
                    }

                    @Override
                    public TypeReference<WebApiResponse<GetDetailEmployeeResult>> getTypeReference() {
                        return new TypeReference<WebApiResponse<GetDetailEmployeeResult>>() {
                        };
                    }

                    @Override
                    public void completed(Date time, GetDetailEmployeeResult response) {
                        if (response != null) {
                            if (response.getEmployees() != null && response.getEmployees().size() > 0) {
                                DetailEmployeeVo detailEmployeeVo = response.getEmployees().get(0);
                                EmpShortEntity mEmpShortEntity =
                                        new EmpShortEntity(detailEmployeeVo.getId(), detailEmployeeVo.getName(),
                                                detailEmployeeVo.getProfileImage(),
                                                detailEmployeeVo.getNameSpell());
                                callback.completed(time, mEmpShortEntity);
                            }
                        }

                    }
                });
    }

    private synchronized void setEmapData(AEmployeeData response) {
//        if (response != null && response.employees != null && response.employees.size() > 0) {
//            if (eMap == null) {
//                eMap = new SparseArray<>();
//            }
//
//            eMap.clear();
//            int count = response.employees.size();
//            for (int i = 0; i < count; i++) {
//                eMap.put(response.employees.get(i).employeeID, response.employees.get(i));
//            }
//        }
//        if (eMap != null) {
//
//            AEmpSimpleEntity helper = new AEmpSimpleEntity();
//
//            helper.employeeID = Default_ID_fshelper;
//
//            helper.name = "纷享客服";
//
//            helper.setNameSpell( "fenxiangtuandui");
//
//            eMap.put(Default_ID_fshelper, helper);
//        }
    }

    private void fixNotEnglishNameSpell(AEmployeeData response) {
//        if (response == null) {
//            return;
//        }
//        if (response.employees != null) {
//            for (AEmpSimpleEntity info : response.employees) {
//                if (info.getNameSpell() != null && info.getNameSpell().matches("^[^a-zA-Z].*")) {
//                    info.setSecondNameSpell(info.getNameSpell());
//                    info.setNameSpell(S_NOT_ENGLISH_STRING);
//                }
//            }
//        }
//        if (response.circles != null) {
//            for (CircleEntity info : response.circles) {
//                if (info.getNameSpell() != null && info.getNameSpell().matches("^[^a-zA-Z].*")) {
//                    info.setSecondNameSpell(info.getNameSpell());
//                    info.setNameSpell(S_NOT_ENGLISH_STRING);
//                }
//            }
//        }
    }

    public synchronized void clear() {
//        mEmployeeData = null;
//        eMap = null;
    }

    @Override
    public void updateALevelCache(AEmployeeData employeeData) {
//        if (employeeData == null) {
//            return;
//        }
//
//        mEmployeeData = employeeData;
//        fixNotEnglishNameSpell(employeeData);
//        setEmapData(employeeData);
////        ContactPluginDataHelper.updateContactPluginData(employeeData);
//
//        if (mStagedAsteriskEmployees != null || mStagedAsteriskCircles != null) {
//            updatePLevelCache(mStagedAsteriskEmployees, mStagedAsteriskCircles);
//        }
//        PublisherEvent.post(new ContactLoadEvent());
        //EventBus.getDefault().post();
    }

    private void updatePLevelCache(List<Integer> asteriskEmployeeIDList,
                                  List<Integer> asteriskCircleIDList) {
        //如果还没有全量通讯录缓存，则先缓存P Level数据
//        if (mEmployeeData == null) {
//            mStagedAsteriskEmployees = asteriskEmployeeIDList;
//            mStagedAsteriskCircles = asteriskCircleIDList;
//            return;
//        }
//
//        // 比较员工加星情况
//        // asteriskEmployeeIDList是null时表示没变化，则不更新
//        if (asteriskEmployeeIDList != null && mEmployeeData.employees != null) {
//            for (AEmpSimpleEntity e : mEmployeeData.employees) {
//                if (asteriskEmployeeIDList.contains(e.employeeID)) {
//                    e.setAsterisk(true);
//                } else {
//                    e.setAsterisk(false);
//                }
//            }
//        }
//
//        // 比较部门加星情况
//        if (asteriskCircleIDList != null && mEmployeeData.circles != null) {
//            for (CircleEntity e : mEmployeeData.circles) {
//                if (asteriskCircleIDList.contains(e.circleID)) {
//                    e.setAsterisk(true);
//                } else {
//                    e.setAsterisk(false);
//                }
//            }
//        }
//
//        mStagedAsteriskEmployees = null;
//        mStagedAsteriskCircles = null;
    }

    /**
     * 获取通讯录中的用户
     */
    public List<AEmpSimpleEntity> getAEmpSimpleEntity(final String[] userIDArray) {
        final List<AEmpSimpleEntity> aEmpSimpleEntities = new ArrayList<AEmpSimpleEntity>();
        for (int i = 0; i < userIDArray.length; i++) {
            AEmpSimpleEntity aEmp = mEmpDepCache.getAEmpSimpleEntity(Integer.parseInt(userIDArray[i]));
            if(aEmp != null){
                aEmpSimpleEntities.add(aEmp);
            }else{
                aEmp = getAEmpSimpleEntityNullData(Integer.parseInt(userIDArray[i]));
                if (aEmp == null) {
                    aEmp = new AEmpSimpleEntity();
                    aEmp.employeeID = Integer.parseInt(userIDArray[i]);
                }

                aEmpSimpleEntities.add(aEmp);
            }
        }
        return aEmpSimpleEntities;
    }

    public AEmpSimpleEntity findAEmpSimleEntityById( int id ){
        return mEmpDepCache.getUserFromLocal(id).getmAEmpSimpleEntity();
    }

    @Override
    public OrganizationStructure getOrganizationStructure(int id) {
        return mEmpDepCache.getOrganizationStructure(id);
    }

    @Override
    public List<Integer> getAllEmployeeIds(int depid) {
        return mEmpDepCache.getAllEmployeeIds(depid);
    }

    @Override
    public List<Integer> getOrderedAllEmployeeIds(int depid) {
        List<Integer> ret=new ArrayList<>();
        if (mEmpDepCache!=null){
            ret=mEmpDepCache.getOrderedAllEmployeeIds(depid);
        }
        return ret;
    }

    @Override
    public List<Integer> getOrderedAllEmployeeIds(int[] depids) {
        List<Integer> ret=new ArrayList<>();
        if (depids==null||depids.length==0){
            return ret;
        }
        if (mEmpDepCache!=null){
            ret=mEmpDepCache.getOrderedAllEmployeeIds(depids);
        }

        return ret;
    }

    @Override
    public List<Integer> getAllDepIds(int id) {
        return mEmpDepCache.getAllDepIds(id);
    }

    @Override
    public List<Integer> getDirectEmployeeIds(int depid) {
        return mEmpDepCache.getDirectEmployeeIds(depid);
    }

    @Override
    public List<EmpIndexLetter> getOrderedDirectEmpsWithOnlyIndexLetter(int depid) {
        return mEmpDepCache.getOrderedDirectEmpsWithOnlyIndexLetter(depid);
    }
    @Override
    public List<Integer> getDirectDepIds(int depid) {
        return mEmpDepCache.getDirectDepIds(depid);
    }

    @Override
    public List<Integer> getOrderAllDepIds(int id) {
        return mEmpDepCache.getOrderAllDepIds(id);
    }

    @Override
    public List<Integer> getOrderDirectDepIds(int depid) {
        return mEmpDepCache.getOrderDirectDepIds(depid);
    }

    @Override
    public List<CircleEntity> getOrderedDirectDeps(int depid) {
        List<CircleEntity> ret=new ArrayList<>();
        if (mEmpDepCache==null){
            return ret;
        }
        OrganizationStructure os = mEmpDepCache.getOsCache().get(depid);
        if (os==null){
            return ret;
        }
        SparseArray<Object> list = os.getChildDepList();

        HashMap<Integer, Object> idmaps = new HashMap<>();

        int size = list.size();
        for(int i=0; i<size;i++){
            idmaps.put(list.keyAt(i), PRESENT);
        }

        ret = mEmpDepCache.orderDeps(idmaps);
        return ret;
    }
    @Override
    public List<AEmpSimpleEntity> getOrderedDirectEmps(int depid) {
        List<AEmpSimpleEntity> ret=new ArrayList<>();
        if (mEmpDepCache==null){
            return ret;
        }
        OrganizationStructure os = mEmpDepCache.getOsCache().get(depid);
        if (os==null){
            return ret;
        }
        SparseArray<Object> list = os.getEmpList();

        HashMap<Integer, Integer> idmaps = new HashMap<>();

        int size = list.size();
        for(int i=0; i<size;i++){
            idmaps.put(list.keyAt(i), list.keyAt(i));
        }

        ret = mEmpDepCache.orderEmps(idmaps);
        return ret;
    }
    @Override
    public SparseArray<OrganizationStructure> getOsCache(){
        return mEmpDepCache.getOsCache();
    }
    @Override
    public List<CircleEntity> getDirectDeps(int id) {
        return mEmpDepCache.getDirectDeps(id);
    }
    @Override
    public List<AEmpSimpleEntity> getDirectEmployees(int id) {
        return mEmpDepCache.getDirectEmployees(id);
    }

    @Override
    public List<EmpIndexLetter> getHaveEmailEmps() {
        return mEmpDepCache.getHaveEmailEmps();
    }

    @Override
    public List<EmpIndexLetter> getHaveMobileEmps() {
        return mEmpDepCache.getHaveMobileEmps();
    }

    @Override
    public List<EmpIndexLetter> getHaveEmailOrMobileEmps() {
        return mEmpDepCache.getHaveEmailOrMobileEmps();
    }

    public List<EmpIndexLetter> getStarEmpList(){
        return mEmpDepCache.getmStarEmpList();
    }
    public List<CircleIndexLetter> getStarDepList(){
        return mEmpDepCache.getmStarDepList();
    }


    public List<AEmpSimpleEntity> getAEmpSimpleEntity(final int[] userIDS) {

        List<String> userIdList = new ArrayList<String>();

        for (int userID : userIDS) {

            userIdList.add(String.valueOf(userID));
        }

        return getAEmpSimpleEntity(userIdList.toArray(new String[userIdList.size()]));
    }

    public List<AEmpSimpleEntity> getAEmpSimpleEntity(List<Integer> userIDS) {

        List<String> userIdList = new ArrayList<String>();

        Iterator<Integer> iterator = userIDS.iterator();

        while (iterator.hasNext()) {

            userIdList.add(String.valueOf(iterator.next()));
        }

        return getAEmpSimpleEntity(userIdList.toArray(new String[userIdList.size()]));
    }


    @Override
    public void updatePLevelCache(UserInitialData data) {
//        updatePLevelCache(data.getEmployeeAsteriskIds(), data.getDepartmentAsteriskIds());
//
//        if (eMap != null) {
//            AEmpSimpleEntity aEmpSimpleEntity =
//                    eMap.get(FSContextManager.getCurUserContext().getAccount().getEmployeeIntId());
//            if (aEmpSimpleEntity != null) {
//                data.update(aEmpSimpleEntity);
//            }
//        }

    }

    public static ArrayList<Integer> convertEmpList(List<AEmpSimpleEntity> emps) {
        ArrayList<Integer> ret = new ArrayList<Integer>();
        if (emps != null) {
            for (AEmpSimpleEntity emp : emps) {
                ret.add(emp.employeeID);
            }
        }
        return ret;
    }

    @Override
    public void updateEmployeeInfo(int employeeId, EmployeeBaseInfo info) {
//        AEmpSimpleEntity aEmpSimpleEntity = eMap.get(employeeId);
//        if (aEmpSimpleEntity != null) {
//            aEmpSimpleEntity.setMobile(info.mobile);
//            aEmpSimpleEntity.setGender(info.gender);
//            aEmpSimpleEntity.setEmail(info.email);
//            aEmpSimpleEntity.setLeaderID(info.leader != null ? info.leader.employeeID : 0);
//            aEmpSimpleEntity.setTel(info.tel);
//        }
    }

    @Override
    public DimensionNode getDimensionTree(String type) {
        return getDimensionCache().getDimensionTree(type);
    }

    @Override
    public List<DimensionNode> getDimensionList(List<String> id) {
        return getDimensionCache().getDimensionList(id);
    }

    @Override
    public void clearAllDimensionCache() {
        getDimensionCache().clearDimensionCache();
    }

    @Override
    public void onUpdateCacheStart() {
//        mIsALevelDataUpdating = true;
    }

    @Override
    public void onUpdateCacheEnd() {
//        mIsALevelDataUpdating = false;
    }
}
