
package com.facishare.fs.db;

import com.facishare.fs.db.dao.*;
import com.lidroid.xutils.exception.DbException;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 16-10-2015 14:27:38
 */
public interface IContactDbHelper extends IDbHelper{

    public DimensionEntityDao getDimensionEntityDao();

    public AEmpSimpleEntityDao getAEmpSimpleEntityDao();

    public OtherEmployeeEntityDao getOtherEmployeeEntityDao();

    public StopEmployeeEntityDao getStopEmployeeEntityDao();

    public CircleEntityDao getCircleEntityDao();

    public OtherCircleEntityDao getOtherCircleEntityDao();

    public EmployeeKeyWordEntityDao getEmployeeKeyWordEntityDao();

    public CircleKeywordEntityDao getCircleKeywordEntityDao();

    public CustomerServiceDao getCustomerServiceDao();

    public FriendEnterpriseDao getFriendEnterpriseDao();

    public FriendEnterpriseEmployeeDao getFriendEnterpriseEmployeeDao();

    public ExternalContactDao getExternalContactDao();

    public ExternalContactEnterpriseDao getExternalContactEnterpriseDao();

    public ThirdEmployeeDataDao getThirdEmployeeDataDao();

    ExtEmpDao getExtEmpDao();
}
