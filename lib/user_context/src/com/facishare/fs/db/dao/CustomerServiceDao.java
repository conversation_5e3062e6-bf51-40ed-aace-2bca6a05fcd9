/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.db.dao;

import android.content.ContentValues;

import com.facishare.fs.db.ContactDbColumn;
import com.facishare.fs.pluginapi.contact.beans.coustomer.CustomerService;
import com.lidroid.xutils.exception.DbException;

import com.tencent.wcdb.Cursor;

import java.util.List;

/**
 * Created by 钟光燕 on 2016/3/30.
 * ===================================================
 * <p/>
 * code is m h l
 * <p/>
 * ===================================================
 */
public class CustomerServiceDao extends BaseDao<CustomerService> implements ContactDbColumn.CustomerServiceColumn{
    @Override
    public String getTableName() {
        return _tabName;
    }

    @Override
    public CustomerService cursorToClass(Cursor cursor) {
        CustomerService customerService = new CustomerService() ;
        customerService.setAppId(cursor.getString(cursor.getColumnIndex(appId)));
        customerService.setAppName(cursor.getString(cursor.getColumnIndex(appName)));
        customerService.setImageUrl(cursor.getString(cursor.getColumnIndex(imageUrl)));
        customerService.setDesc(cursor.getString(cursor.getColumnIndex(desc)));
        customerService.setAppFlag(cursor.getInt(cursor.getColumnIndex(appFlag)));
        customerService.setAppType(cursor.getInt(cursor.getColumnIndex(appType)));
        customerService.setServiceType(cursor.getInt(cursor.getColumnIndex(serviceType)));
        return customerService;
    }

    @Override
    public ContentValues getContentValues(CustomerService object) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(appId, object.getAppId());
        contentValues.put(appName, object.getAppName());
        contentValues.put(imageUrl, object.getImageUrl());
        contentValues.put(desc, object.getDesc());
        contentValues.put(appType, object.getAppType());
        contentValues.put(appFlag, object.getAppFlag());
        contentValues.put(serviceType, object.getServiceType());
        return contentValues;
    }


    public List<CustomerService> findCustomerServiceByKeyWord(String keyword) throws DbException {
        String sql = "select * from customerService customer where customer.appName like \"%" + keyword + "%\" or customer.desc like \"%"+ keyword +"%\"";
        return findAllBySql(sql) ;
    }

    public CustomerService findCustomerServiceById(String appId) throws DbException {
        String sql = "select * from customerService customer where customer.appID like \"%"+ appId +"%\"";
        List<CustomerService> temp = findAllBySql(sql) ;
        if (temp !=null && !temp.isEmpty()){
            return temp.get(0) ;
        }
        return null ;
    }
}
