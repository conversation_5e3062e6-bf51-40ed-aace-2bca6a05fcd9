package com.fxiaoke.fxdblib.beans;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.facishare.fs.NoProguard;

/**
 * Created by yangwg on 2019/5/22.
 */
@NoProguard
public class MixMessageElement implements Serializable{
    //混排消息元素类型，文本("T")或者图片("I")
    private String type ;
    //图文混排的消息内容数据 文本：字符串；图片：json字符串{"tempFilePath":"", "fileExtension":"", "OriginalFileName":"", "isHD":false}
    //文本类型时参考：List<FeedTextBlockVo>
    //图片类型时参考：ImgMsgData 和 List<MsgEntity>
    private String content;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String data) {
        this.content = data;
    }
    //图文消息展示时用的数据封装对象
    ImgMsgData imgMsgData;

    public ImgMsgData getImgMsgData() {
        if (!isImageType()) {
            return null;
        }
        if (imgMsgData == null && content != null && content.length() > 0) {
            imgMsgData = JSON.parseObject(content, ImgMsgData.class);
        }
        return imgMsgData;
    }

    public void setImgMsgData(ImgMsgData imgMsgData) {
        this.imgMsgData = imgMsgData;
    }


    List<FeedTextBlockVo> feedTextBlockVos;

    public List<FeedTextBlockVo> getFeedTextBlockVos() {
        return feedTextBlockVos;
    }

    public void setFeedTextBlockVos(List<FeedTextBlockVo> feedTextBlockVos) {
        this.feedTextBlockVos = feedTextBlockVos;
    }
    MixMessageImageContent sendImageContent;
    public MixMessageImageContent getSendImageContent() {
        return sendImageContent;
    }

    public void setSendImageContent(MixMessageImageContent sendImageContent) {
        this.sendImageContent = sendImageContent;
    }

    public boolean isImageType() {
        return MixMessageElementType.Image.equals(type);
    }

    public boolean isTextType() {
        return MixMessageElementType.Text.equals(type);
    }
}
