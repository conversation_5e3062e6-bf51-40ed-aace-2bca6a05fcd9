/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.fxdblib.beans;

import android.text.TextUtils;

import com.lidroid.xutils.db.annotation.Table;

import java.io.Serializable;

/**
 * Created by 钟光燕 on 2016/4/11.
 * ===================================================
 * <p>
 * 6.1版本开始不再使用此类
 * <p>
 * ===================================================
 */
@Table(name = "secondLevelSession", execAfterTableCreated = "CREATE UNIQUE INDEX index_secondLevelSession_sessionid ON secondLevelSession(Sessionid);"
        + "CREATE INDEX index_secondLevelSession_subcategory ON secondLevelSession(SessionSubCategory)")
@Deprecated
public class SecondLevelSession extends SessionListRec implements Serializable {
        String parentSessionId ;
        String originSessionCategory ;
        String originSessionSubCategory ;


        public String getParentSessionId() {

                if (TextUtils.isEmpty(parentSessionId)){
                      return getRootParentSessionId() ;
                }
                return parentSessionId ;
        }

        public void setParentSessionId(String parentSessionId) {
                this.parentSessionId = parentSessionId;
        }

        public String getOriginSessionCategory() {
                return originSessionCategory;
        }

        public void setOriginSessionCategory(String originSessionCategory) {
                this.originSessionCategory = originSessionCategory;
        }

        public String getOriginSessionSubCategory() {
                return originSessionSubCategory;
        }

        public void setOriginSessionSubCategory(String originSessionSubCategory) {
                this.originSessionSubCategory = originSessionSubCategory;
        }


}
