/*
 * Copyright (C) 2023 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.fxdblib.beans.rich_text;

import java.io.Serializable;
import java.util.List;

public class Content implements Serializable {
    public Content() {
        super();
    }

    private int index;

    private List<Marks> marks;

    private String text;

    private String type;

    public void setIndex(int index) {
        this.index = index;
    }

    public int getIndex() {
        return this.index;
    }

    public void setMarks(List<Marks> marks) {
        this.marks = marks;
    }

    public List<Marks> getMarks() {
        return this.marks;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getText() {
        return this.text;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return this.type;
    }

    private List<Content> content;

    public void setContent(List<Content> content) {
        this.content = content;
    }

    public List<Content> getContent() {
        return this.content;
    }

    private Attrs attrs;

    public void setAttrs(Attrs attrs) {
        this.attrs = attrs;
    }

    public Attrs getAttrs() {
        if (this.attrsForMobile != null) {
            return attrsForMobile;
        }
        return this.attrs;
    }
    private Attrs attrsForMobile;

    public Attrs getAttrsForMobile() {
        return attrsForMobile;
    }

    public void setAttrsForMobile(Attrs attrsForMobile) {
        this.attrsForMobile = attrsForMobile;
    }

    int employeeId;
    String tenantId;

    public int getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(int employeeId) {
        this.employeeId = employeeId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    String title;//9.2.5协议新增字段，目前用于tag类型的控件渲染内容用

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    //本地用变量
    private List<Content> unionContentList;

    public List<Content> getUnionContentList() {
        return unionContentList;
    }

    public void setUnionContentList(List<Content> unionContentList) {
        this.unionContentList = unionContentList;
    }
    //本地用变量
    boolean isFakeData = false;

    public boolean isFakeData() {
        return isFakeData;
    }

    public void setFakeData(boolean fakeData) {
        isFakeData = fakeData;
    }
    //本地用变量
    int limitHeight = 0;

    public int getLimitHeight() {
        return limitHeight;
    }

    public void setLimitHeight(int limitHeight) {
        this.limitHeight = limitHeight;
    }
    //本地用变量
    int limitMaxWidth = 0;

    public int getLimitMaxWidth() {
        return limitMaxWidth;
    }

    public void setLimitMaxWidth(int limitMaxWidth) {
        this.limitMaxWidth = limitMaxWidth;
    }
    //本地用变量，记录本控件的宽应该显示多少
    private int localFieldForWidth = 0;
    //本地用变量，记录本控件占父布局的比例是多少
    private int localFieldForWeight = 0;
//    private int localFieldForHeight = 0;

    public int getLocalFieldForWidth() {
        return localFieldForWidth;
    }

    public void setLocalFieldForWidth(int localFieldForWidth) {
        this.localFieldForWidth = localFieldForWidth;
    }

    public int getLocalFieldForWeight() {
        return localFieldForWeight;
    }

    public void setLocalFieldForWeight(int localFieldForWeight) {
        this.localFieldForWeight = localFieldForWeight;
    }

//    public int getLocalFieldForHeight() {
//        return localFieldForHeight;
//    }
//
//    public void setLocalFieldForHeight(int localFieldForHeight) {
//        this.localFieldForHeight = localFieldForHeight;
//    }
    boolean supportMarkdown = false;

    public boolean isSupportMarkdown() {
        return supportMarkdown;
    }

    public void setSupportMarkdown(boolean supportMarkdown) {
        this.supportMarkdown = supportMarkdown;
    }
}
