package com.fxiaoke.fxdblib.beans;

import java.io.Serializable;

/**
 * Created by 钟光燕 on 2016/6/17.
 * ===================================================
 * <p>
 * code is m h l
 * <p>
 * ===================================================
 */
public class WorkbenchEntityVo implements Serializable{

    private String SessionSubCategory ;
    private String SessionName ;
    private String SessionIcon  ;
    private String Uri ;

    public String getWorkType() {
        return SessionSubCategory;
    }

    public void setWorkType(String sessionSubCategory) {
        SessionSubCategory = sessionSubCategory;
    }

    public String getSessionName() {
        return SessionName;
    }

    public void setSessionName(String sessionName) {
        SessionName = sessionName;
    }

    public String getSessionIcon() {
        return SessionIcon;
    }

    public void setSessionIcon(String sessionIcon) {
        SessionIcon = sessionIcon;
    }

    public String getUri() {
        return Uri;
    }

    public void setUri(String uri) {
        Uri = uri;
    }
}
