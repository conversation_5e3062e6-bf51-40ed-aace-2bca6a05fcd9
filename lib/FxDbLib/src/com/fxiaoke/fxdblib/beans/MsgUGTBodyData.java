/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.fxdblib.beans;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * Created by yangwg on 2016/2/29.
 */
public class MsgUGTBodyData implements Serializable{

//        "body": {
//            "elements": [
//                {
//                    "color": "#BE5050",
//                    "fontSize": 0,
//                    "linkAddress": "fs: //feed/crm/456",
//                    "content": "客户名称",
//                    "newLine": true
//                },
//                {
//                    "color": "#BE5050",
//                    "fontSize": 12,
//                    "linkAddress": "fs: //feed/crm/456",
//                    "content": "成就金额",
//                    "newLine": true
//                }
//            ]
//        },

    @JSONField(name="icon")
    public String icon;
    @JSONField(name="title")
    public MsgUGTElementItemData title;
    public MsgUGTBodyData(){
        super();
    }
    @JSONCreator
    public MsgUGTBodyData(@JSONField(name="icon") String icon, @JSONField(name="title") MsgUGTElementItemData title){
        this.icon = icon;
        this.title = title;
    }
}
