/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.fxdblib.beans.sessiondefine;

import java.io.Serializable;

/**
 * Created by yangwg on 2017/3/3.
 */
public class TmpMsgInfo implements Serializable {
    long LastMessageId;
    long LastMessageSenderId;
    String LastMessageType;
    String LastMessageSummary;
    long LastMessageTime;
    int LastMessageStatus;//0,发送成功，1，发送中，2，发送失败,10,语音未读,20,R,21,H
    String lastMessageFullSenderId;
    String LastMessageSenderName;
    String ClientPostId;

    public long getLastMessageId() {
        return LastMessageId;
    }

    public void setLastMessageId(long lastMessageId) {
        LastMessageId = lastMessageId;
    }

    public long getLastMessageSenderId() {
        return LastMessageSenderId;
    }

    public void setLastMessageSenderId(long lastMessageSenderId) {
        LastMessageSenderId = lastMessageSenderId;
    }

    public String getLastMessageFullSenderId() {
        return lastMessageFullSenderId;
    }

    public void setLastMessageFullSenderId(String lastMessageFullSenderId) {
        this.lastMessageFullSenderId = lastMessageFullSenderId;
    }

    public String getLastMessageSenderName() {
        return LastMessageSenderName;
    }

    public void setLastMessageSenderName(String lastMessageSenderName) {
        LastMessageSenderName = lastMessageSenderName;
    }


    public String getLastMessageType() {
        return LastMessageType;
    }

    public void setLastMessageType(String lastMessageType) {
        LastMessageType = lastMessageType;
    }

    public String getLastMessageSummary() {
        return LastMessageSummary;
    }

    public void setLastMessageSummary(String lastMessageSummary) {
        LastMessageSummary = lastMessageSummary;
    }

    public long getLastMessageTime() {
        return LastMessageTime;
    }

    public void setLastMessageTime(long lastMessageTime) {
        LastMessageTime = lastMessageTime;
    }

    public int getLastMessageStatus() {
        return LastMessageStatus;
    }

    public void setLastMessageStatus(int lastMessageStatus) {
        LastMessageStatus = lastMessageStatus;
    }

    public String getClientPostId() {
        return ClientPostId;
    }

    public void setClientPostId(String clientPostId) {
        ClientPostId = clientPostId;
    }

    @Override
    public String toString() {
        return new StringBuilder("LMI: ").append(LastMessageId)
                .append(" ,LMType: ").append(LastMessageType)
                .append(" ,LMTime: ").append(LastMessageTime)
                .append(" ,LMSI: ").append(LastMessageSenderId)
                .append(" ,LMFSI: ").append(lastMessageFullSenderId)
                .append(" ,LMSN: ").append(LastMessageSenderName)
                .append(" ,LMS: ").append(LastMessageStatus)
                .append(" ,LMSC: ").append(LastMessageSummary)
                .append(" ,CPId: ").append(ClientPostId).toString();

    }
}
