package com.fxiaoke.fxdblib.beansBc;



import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.fxdblib.beans.AudioMsgData;
import com.fxiaoke.fxdblib.beans.EmotionMsgData;
import com.fxiaoke.fxdblib.beans.FileMsgData;
import com.fxiaoke.fxdblib.beans.ImgMsgData;
import com.fxiaoke.fxdblib.beans.LocationMsgData;
import com.fxiaoke.fxdblib.beans.MsgEntity;
import com.fxiaoke.fxdblib.beans.MsgTypeKey;
import com.fxiaoke.fxdblib.beans.VoteMsgData;
import com.fxiaoke.fxdblib.beans.WorkItemMsgData;
import com.fxiaoke.fxdblib.beans.WorkNoticeMsgData;
import com.fxiaoke.fxdblib.beans.WorkScheduleMsgData;
import com.lidroid.xutils.db.annotation.Finder;
import com.lidroid.xutils.db.annotation.Id;
import com.lidroid.xutils.db.annotation.Table;
import com.lidroid.xutils.db.annotation.Transient;
@Table(name = "chatmessage_temp" , execAfterTableCreated = "CREATE INDEX index_chatmessage_temp_sessionid ON chatmessage_temp(Sessionid);"
		+ "CREATE INDEX index_chatmessage_temp_TargetUserId ON chatmessage_temp(TargetUserId);"
		+ "CREATE INDEX index_chatmessage_temp_ClientPostId ON chatmessage_temp(ClientPostId)")
public class BSessionMessageTemp {
    
    @Id
    int id;
    
    
    @Finder(valueColumn = "id", targetColumn = "LocalMessageId")
    List<MsgEntity> Entities;
    
    String Sessionid;
    long PreviousMessageId;
    String MessageType;
    long MessageTime;
    String UserProperty0;
    
    int MsgSendingStatus;//1 sending,2 failed
    String TargetUserId;
    @Transient
    String SenderId;
    String Content;
    String ClientPostId;
    String Partnerid = "";
    
    List<Integer> AtXiaoUserIds;
    List<String> AtBaichuanUserIds;
    
    @Transient
    AudioMsgData AudioMsgData;
    @Transient
    ImgMsgData ImgMsgData;
    @Transient
    LocationMsgData LocationMsgData;
    @Transient
    FileMsgData FileMsgData;
    @Transient
    EmotionMsgData EmotionMsgData;
    @Transient
    WorkNoticeMsgData WorkNoticeMsgData;
    @Transient
    WorkItemMsgData workItemMsgData;
    @Transient
    WorkScheduleMsgData workScheduleMsgData;
    @Transient
    VoteMsgData voteMsgData;
    
    
    
    public String getPartnerid() {
		return Partnerid;
	}
	public void setPartnerid(String partnerid) {
		Partnerid = partnerid;
	}
	public VoteMsgData getVoteMsgData() {
        return voteMsgData;
    }
    public void setVoteMsgData(VoteMsgData voteMsgData) {
        this.voteMsgData = voteMsgData;
    }
    public int getMsgSendingStatus() {
        return MsgSendingStatus;
    }
    public void setMsgSendingStatus(int msgSendingStatus) {
        MsgSendingStatus = msgSendingStatus;
    }
    
    
    public List<MsgEntity> getEntities() {
        return Entities;
    }
    public void setEntities(List<MsgEntity> entities) {
        this.Entities = entities;
    }
    public MsgEntity getEntity(int type){
        MsgEntity ret=null;
        do {
            if (Entities==null) {
                break;
            }
            for (MsgEntity en : Entities) {
                if (en.getEntitytype()==type) {
                    ret=en;
                    break;
                }
            }
        } while (false);
        
        return ret;
    }
    public void addEntity(MsgEntity entity) {
        if (Entities==null) {
            Entities=new ArrayList<MsgEntity>();
        }
        int i=0;
        for (MsgEntity item : Entities) {
            if (item.getEntitytype()==entity.getEntitytype()) {
                Entities.set(i, entity);
                break;
            }
            i++;
        }
        if (i==Entities.size()) {
            
            this.Entities.add(entity);
        }
    }
    public void refreshEntitiesLocalMsgid() {
        if (Entities==null) {
            return;
        }
        for (MsgEntity en : Entities) {
            en.setLocalMessageid(id);
        }
    }
    public int getId() {
        return id;
    }
    public void setId(int id) {
        this.id = id;
    }
    public String getSessionid() {
        return Sessionid;
    }
    public void setSessionid(String sessionid) {
        Sessionid = sessionid;
    }
    
    public long getPreviousMessageId() {
        return PreviousMessageId;
    }
    public void setPreviousMessageId(long previousMessageId) {
        PreviousMessageId = previousMessageId;
    }
    public String getMessageType() {
        return MessageType;
    }
    public void setMessageType(String messageType) {
        MessageType = messageType;
        parseContent(Content);
    }
    public long getMessageTime() {
        return MessageTime;
    }
    public String getMessageDate() {
        SimpleDateFormat sdf=new SimpleDateFormat("hh:mm:ss");
        Date d=new Date(MessageTime);
        return sdf.format(d);
    }
    public void setMessageTime(long messageTime) {
        MessageTime = messageTime;
    }
    public String getUserProperty0() {
        return UserProperty0;
    }
    public void setUserProperty0(String userProperty0) {
        UserProperty0 = userProperty0;
    }
    public String getSenderId() {
        return SenderId;
    }
    public void setSenderId(String senderId) {
        SenderId = senderId;
    }
    public String getContent() {
        if (Content==null) {
            Content=serilizeContent();
        }
        return Content;
    }
    public void setContent(String content) {
        Content = content;
        parseContent(Content);
    }
    
    public String getClientPostId() {
        return ClientPostId;
    }
    public void setClientPostId(String clientPostId) {
        ClientPostId = clientPostId;
    }
    public AudioMsgData getAudioMsgData() {
        return AudioMsgData;
    }
    public void setAudioMsgData(AudioMsgData audioMsgData) {
        this.AudioMsgData = audioMsgData;
    }
    public ImgMsgData getImgMsgData() {
        return ImgMsgData;
    }
    public void setImgMsgData(ImgMsgData imgMsgData) {
        this.ImgMsgData = imgMsgData;
    }
    public LocationMsgData getLocationMsgData() {
        return LocationMsgData;
    }
    public void setLocationMsgData(LocationMsgData locationMsgData) {
        this.LocationMsgData = locationMsgData;
    }
    public FileMsgData getFileMsgData() {
        return FileMsgData;
    }
    public void setFileMsgData(FileMsgData fileMsgData) {
        this.FileMsgData = fileMsgData;
    }
    public EmotionMsgData getEmotionMsgData() {
        return EmotionMsgData;
    }
    public void setEmotionMsgData(EmotionMsgData emotionMsgData) {
        this.EmotionMsgData = emotionMsgData;
    }
    public WorkNoticeMsgData getWorkNoticeMsgData() {
        return WorkNoticeMsgData;
    }
    public void setWorkNoticeMsgData(WorkNoticeMsgData workNoticeMsgData) {
        WorkNoticeMsgData = workNoticeMsgData;
    }

    public WorkItemMsgData getWorkItemMsgData() {
        return workItemMsgData;
    }
    public void setWorkItemMsgData(WorkItemMsgData workItemMsgData) {
        this.workItemMsgData = workItemMsgData;
    }
    public WorkScheduleMsgData getWorkScheduleMsgData() {
        return workScheduleMsgData;
    }
    public void setWorkScheduleMsgData(WorkScheduleMsgData workScheduleMsgData) {
        this.workScheduleMsgData = workScheduleMsgData;
    }
    void parseContent(String content){
        if (MessageType==null || content==null || content.length()==0 || content.equals("null")) {
            return;
        }
        if (MessageType.equals("A")) {
            AudioMsgData=JSON.parseObject(content, AudioMsgData.class);
            
        }else if (MessageType.equals("I")) {
            ImgMsgData=JSON.parseObject(content, ImgMsgData.class);
        }else if (MessageType.equals("D")) {
            FileMsgData=JSON.parseObject(content, FileMsgData.class);
        }else if (MessageType.equals("E")) {
            EmotionMsgData=JSON.parseObject(content, EmotionMsgData.class);
        }else if (MessageType.equals("L")) {
            LocationMsgData=JSON.parseObject(content, LocationMsgData.class);
        }else if (MessageType.equals(MsgTypeKey.MSG_WorkNotice_key)) {
            WorkNoticeMsgData=JSON.parseObject(content, WorkNoticeMsgData.class);
        }else if (MessageType.equals(MsgTypeKey.MSG_Work_Item_key)) {
            workItemMsgData=JSON.parseObject(content, WorkItemMsgData.class);
        }else if (MessageType.equals(MsgTypeKey.MSG_Work_Schdule_key)) {
            workScheduleMsgData=JSON.parseObject(content, WorkScheduleMsgData.class);
        }else if (MessageType.equals(MsgTypeKey.MSG_vote_key)) {
            voteMsgData =JSON.parseObject(content, VoteMsgData.class);
        }else {
            
        }
    }
    String serilizeContent(){
        
        String content="";
        if (MessageType==null||MessageType.length()==0 || MessageType.equals("null")) {
        }else {
            
            if (MessageType.equals("A")) {
                content=JSON.toJSONString(AudioMsgData);
                
            }else if (MessageType.equals("I")) {
                content=JSON.toJSONString(ImgMsgData);
            }else if (MessageType.equals("D")) {
                content=JSON.toJSONString(FileMsgData);
            }else if (MessageType.equals("E")) {
                content=JSON.toJSONString(EmotionMsgData);
            }else if (MessageType.equals("L")) {
                content=JSON.toJSONString(LocationMsgData);
            }else if (MessageType.equals(MsgTypeKey.MSG_WorkNotice_key)) {
                content=JSON.toJSONString(WorkNoticeMsgData);
            }else if (MessageType.equals(MsgTypeKey.MSG_Work_Item_key)) {
                content=JSON.toJSONString(workItemMsgData);
            }else if (MessageType.equals(MsgTypeKey.MSG_Work_Schdule_key)) {
                content=JSON.toJSONString(workScheduleMsgData);
            }else if (MessageType.equals(MsgTypeKey.MSG_vote_key)) {
                content =JSON.toJSONString(voteMsgData);
            }else {
                
            }
        }
        return content;
    }
    public String getTargetUserId() {
        return TargetUserId;
    }
    public void setTargetUserId(String targetUserId) {
        TargetUserId = targetUserId;
    }
    public String getThumbnailLocal() {
        String ret="";
        if (Entities!=null) {
            
            for (MsgEntity me : Entities) {
                if (me.getServerFileName()!=null&& ImgMsgData!=null&&me.getServerFileName().equals(ImgMsgData.getThumbnail())) {
                    ret=me.getLocalPath();
                    break;
                }
                if (me.getEntitytype()==0) {
                    ret=me.getLocalPath();
                    break;
                }
            }
        }
        return ret;
    }
    public String getImgLocal() {
        String ret="";
        if (Entities!=null) {
            
            for (MsgEntity me : Entities) {
                if (me.getServerFileName()!=null&& ImgMsgData!=null && me.getServerFileName().equals(ImgMsgData.getImage())) {
                    ret=me.getLocalPath();
                    break;
                }
                if (me.getEntitytype()==1) {
                    ret=me.getLocalPath();
                    break;
                }
            }
        }
        return ret;
    }
    
    public String getImgHDLocal() {
        String ret="";
        if (Entities!=null) {
            
            for (MsgEntity me : Entities) {
                if (me.getServerFileName()!=null&& ImgMsgData!=null&&me.getServerFileName().equals(ImgMsgData.getHDImg())) {
                    ret=me.getLocalPath();
                    break;
                }
                if (me.getEntitytype()==2) {
                    ret=me.getLocalPath();
                    break;
                }
            }
        }
        return ret;
    }
    
    public List<Integer> getAtXiaoUserIds() {
        return AtXiaoUserIds;
    }

    public void setAtXiaoUserIds(List<Integer> AtXiaoUserIds) {
        this.AtXiaoUserIds = AtXiaoUserIds;
    }

    public List<String> getAtBaichuanUserIds() {
        return AtBaichuanUserIds;
    }

    public void setAtBaichuanUserIds(List<String> AtBaichuanUserIds) {
        this.AtBaichuanUserIds = AtBaichuanUserIds;
    }
    
//  
    
//  @Foreign(column = "userid", foreign = "id")
//  User target;
//  
//  @Foreign(column = "entityid", foreign = "id")
//  MsgEntity entity;
//  Date time;
}
