package com.fxiaoke.cmviews;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.GestureDetector.SimpleOnGestureListener;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.ProgressBar;
import android.widget.ZoomButtonsController;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

@Deprecated
@SuppressLint("NewApi")
public class FsWebview extends WebViewEx {
	/**
	 * 设置缩放比例的初始值。为百分比的值，100就是不缩放，100以下就是缩小
	 */
	private static final int INIT_SCALE_SIZE = 100;
	
	private ProgressBar mProgressBar;
	
//	/**
//	 * 绿色进度条
//	 */
//	private ImageView progressBar;//
	public interface onViewEvent{
		public void onSingleTapUp();
	}
	onViewEvent mEvent;
	GestureDetector mGestureDetector;
	Handler mHander = new Handler();
	public FsWebview(Context context) {
		super(context);
		initWebview(context);
	}
	public FsWebview(Context context, AttributeSet attrs, int defStyle) {
		super(context, attrs, defStyle);
		initWebview(context);
	}
	public FsWebview(Context context, AttributeSet attrs, int defStyle,
					 boolean privateBrowsing) {
		super(context, attrs, defStyle, privateBrowsing);
		initWebview(context);
	}
	@SuppressLint("NewApi")
	public FsWebview(Context context, AttributeSet attrs) {
		super(context, attrs);
		initWebview(context);
	}
	public void setEventLis(onViewEvent e){
		mEvent=e;
	}
	
	 public void setWebChromeClient(WebChromeClient client) {
		 if(client instanceof WebChromeClientNeededByProgressBar){
			 super.setWebChromeClient(client);
		 }else{
			 if(mWebChromeClientNeededByProgressBar!=null && client!=null){
				 mWebChromeClientNeededByProgressBar.setExternalChromeClient(client);
			 }
		 }
	  }
	
	
	 
	private void initWebview(Context context) {
		
		LayoutInflater layoutInflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
		View view = layoutInflater.inflate(R.layout.view_fswebview_progressbar_layout,this);
		mProgressBar =  (ProgressBar)(view.findViewById(R.id.progress_bar));
		
		mGestureDetector = new GestureDetector(context, new MyOnGestureListener());

		final WebSettings webSettings = this.getSettings();

		webSettings.setJavaScriptCanOpenWindowsAutomatically(true);
		//websettings.setNavDump(true);
        webSettings.setSupportZoom(true);
		webSettings.setUseWideViewPort(true);
		webSettings.setLoadWithOverviewMode(true);; 
		webSettings.setDomStorageEnabled(true);	
		webSettings.setRenderPriority(WebSettings.RenderPriority.HIGH);
		webSettings.setJavaScriptEnabled(true);
		webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
		webSettings.setAllowFileAccess(true);  
		webSettings.setAppCacheEnabled(true);
		webSettings.setAppCacheMaxSize(1024 * 1024 * 5); 
		webSettings.setLoadWithOverviewMode(true);
		webSettings.setPluginState(WebSettings.PluginState.ON);
		
		webSettings.setBlockNetworkImage(false);

		this.setWebViewClient(new WebViewClientEx(this) {
			@Override
			public boolean shouldOverrideUrlLoading (WebView view, String url) {
		    	return super.shouldOverrideUrlLoading(view, url);				
			}
		});
		this.setWebChromeClient(mWebChromeClientNeededByProgressBar);
		//控制webview上的滚动条在webview页面内
		this.setScrollBarStyle(View.SCROLLBARS_INSIDE_OVERLAY);  
		this.setVerticalScrollbarOverlay(false);//让滚动条浮动在页面上
		this.setHorizontalScrollbarOverlay(false);//让滚动条浮动在页面上  	 
		this.requestFocus();
		
		
		//有些手机需要特殊处理，比如摩托的MT917，需在单击时去掉支持缩放
		if(!checkIsNeedControlZoomInTouchEvent()){
			//this.setInitialScale(INIT_SCALE_SIZE);//设置初始不缩放
			webSettings.setBuiltInZoomControls(true); 
			webSettings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NORMAL);
			webSettings.setUseWideViewPort(true);//无限缩放，就需要使用大视图模式，
			
			//控制缩放按钮不显示
			if(Integer.valueOf(Build.VERSION.SDK) < 11){//2.3以下系统中隐藏掉系统的缩放按钮
				this.setZoomControlGone(this);
			}else{//2.3以上系统中隐藏掉系统的缩放按钮
				invokeReflectMethod(webSettings, "setDisplayZoomControls", new Class[]{Boolean.TYPE}, new Object[]{false});
			}
		}
		
	}
	/**
	 * 是否需要特殊处理缩放控制的机器
	 * @return
	 */
	private boolean checkIsNeedControlZoomInTouchEvent() {
		boolean isNeed = false;
		if("motorola".equalsIgnoreCase(String.valueOf(Build.MANUFACTURER))
				&&"MT917".equalsIgnoreCase(String.valueOf(Build.MODEL))
				){
			isNeed = true;
		}
		return isNeed;
	}

	public void refreshUrl(final String url) {
		mHander.post(new Runnable() {
			@Override 
			public void run() {
				FsWebview.this.loadUrl(url);
			}
		});
	}
	
	public Object invokeReflectMethod(Object classObject, String pMethod, Class<?>[] pParamClasses, Object[] pParam) {
		try {
			Method setTestMode = classObject.getClass().getMethod(pMethod, pParamClasses);
			return setTestMode.invoke(classObject, pParam);
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		return null;
	}

	@Override 
	public boolean dispatchTouchEvent(MotionEvent event) {
		if(!checkIsNeedControlZoomInTouchEvent()){
			mGestureDetector.onTouchEvent(event);
			return super.dispatchTouchEvent(event);
		}else{
			final WebSettings webSettings = this.getSettings();
			if (event.getPointerCount() > 1) {
				//this.setInitialScale(INIT_SCALE_SIZE);//设置初始不缩放
				webSettings.setBuiltInZoomControls(true);
				webSettings.setBuiltInZoomControls(true); 
				webSettings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NORMAL);
				webSettings.setUseWideViewPort(true);//无限缩放，就需要使用大视图模式，
				
				//控制缩放按钮不显示
				if(Integer.valueOf(Build.VERSION.SDK) < 11){//2.3以下系统中隐藏掉系统的缩放按钮
					this.setZoomControlGone(this);
				}else{//2.3以上系统中隐藏掉系统的缩放按钮
					invokeReflectMethod(webSettings, "setDisplayZoomControls", new Class[]{Boolean.TYPE}, new Object[]{false});
				}
//			dismissZoomControl();
			} else {// <=0
				// 单点触屏
//			setZoomControlsVisible(false);o
//				setZoomControlGone(this);
//				invokeReflectMethod(this.getSettings(), "setDisplayZoomControls", new Class[]{Boolean.TYPE}, new Object[]{false});
				webSettings.setBuiltInZoomControls(false);
			}
			try {
				mGestureDetector.onTouchEvent(event);
				return super.dispatchTouchEvent(event);
			} catch (Exception e) {
				return true;
			}
		}
	}	
	
	public void setZoomControlGone(View view) {
		Class classType;
		Field field;
		try {
			classType = WebView.class;
			field = classType.getDeclaredField("mZoomButtonsController");
			if(field!=null){
				field.setAccessible(true);
				ZoomButtonsController mZoomButtonsController = new ZoomButtonsController(view);
				mZoomButtonsController.getZoomControls().setVisibility(View.GONE);
				try {
					field.set(view, mZoomButtonsController);
				}  catch (Exception e) {
//					e.printStackTrace();
				}
			}
		}  catch (Exception e) {
//			e.printStackTrace();
		}
	}
	class MyOnGestureListener extends SimpleOnGestureListener {
        @Override 
        public boolean onSingleTapUp(MotionEvent e) {
            if (mEvent!=null) {
				mEvent.onSingleTapUp();
			}
            return false;
        }

        @Override 
        public void onLongPress(MotionEvent e) {
        }

        @Override 
        public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
            return false;
        }

        @Override 
        public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
            return false;
        }

        @Override 
        public void onShowPress(MotionEvent e) {
        }

        @Override 
        public boolean onDown(MotionEvent e) {
            return false;
        }

        @Override 
        public boolean onDoubleTap(MotionEvent e) {
            return false;
        }

        @Override 
        public boolean onDoubleTapEvent(MotionEvent e) {
            return false;
        }

        @Override 
        public boolean onSingleTapConfirmed(MotionEvent e) {
            return false;
        }
    }
	private String getActionName(int action) {
        String name = "";
        switch (action) {
            case MotionEvent.ACTION_DOWN: {
                name = "ACTION_DOWN";
                break;
            }
            case MotionEvent.ACTION_MOVE: {
                name = "ACTION_MOVE";
                break;
            }
            case MotionEvent.ACTION_UP: {
                name = "ACTION_UP";
                break;
            }
            default:
            break;
        }
        return name;
    }
	
	private WebChromeClientNeededByProgressBar mWebChromeClientNeededByProgressBar =
			new WebChromeClientNeededByProgressBar(this);

	public class WebChromeClientNeededByProgressBar extends WebChromeClientEx {

		private WebChromeClient mExternalChromeClient;

		public WebChromeClientNeededByProgressBar(FsWebview webview) {
			super(webview);
		}

		public void setExternalChromeClient(WebChromeClient client){
			mExternalChromeClient = client;
		}
		
		@Override
		public void onProgressChanged(WebView view, int newProgress) {			
			mProgressBar.setProgress(newProgress);
			if (newProgress == 100) {
				mProgressBar.setVisibility(GONE);
			} else {				
				mProgressBar.setVisibility(VISIBLE);							
			}
			
			if(mExternalChromeClient != null){
				mExternalChromeClient.onProgressChanged(view, newProgress);				
			}			
			super.onProgressChanged(view, newProgress);
		}
	 }
}
