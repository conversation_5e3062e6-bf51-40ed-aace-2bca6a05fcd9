/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.cmviews.wheels;

import android.annotation.TargetApi;
import android.content.Context;
import android.os.Build;
import android.util.AttributeSet;
import android.widget.LinearLayout;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 多位数字滚动控件
 * Created by wangkw on 2016/3/24.
 */
public class ScrollNumberView extends LinearLayout {

    private static final String PATTERN_MONEY = "#,##0.00";
    private List<NumberWheelView> numViews = new ArrayList<>();
    private double maxFloat;

    public ScrollNumberView(Context context) {
        super(context);
    }

    public ScrollNumberView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @TargetApi(Build.VERSION_CODES.HONEYCOMB)
    public ScrollNumberView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public ScrollNumberView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }


    /**
     * 设置数字
     *
     * @param num      传入的浮点数会变成两位小数
     * @param animated true是滚动到，false是直接变化
     */
    public void setNumber(double num, boolean animated) {
        if (num > maxFloat) {
            num = maxFloat;
        }
        String numStr = getMoneyString(num);
        int index = numViews.size() - 1;
        for (int i = numStr.length() - 1; i >= 0; i--) {
            char c = numStr.charAt(i);
            if (c != ',' && c != '.') {
                int target = Integer.parseInt(String.valueOf(c));
                numViews.get(index).stopAt(target, animated);
                index--;
            }
        }
        for (; index >= 0; index--) {
            numViews.get(index).stopAt(0, animated);
        }
    }


    public void startScroll() {
        if (numViews.isEmpty()) {
            return;
        }
        for (NumberWheelView wheel : numViews) {
            wheel.startScroll();
        }
    }

    /**
     * 设置最大值，数字会在0~最大值之间滚动
     * 实现方式是解析最大值为两位小数字符串，根据位数生成一个个滚动view
     *
     * @param maxFloat
     */
    public void intMaxNum(double maxFloat) {
        this.maxFloat = maxFloat;
        String maxStr = getMoneyString(maxFloat);
        removeAllViews();
        numViews.clear();
        boolean first = true;
        for (int i = 0; i < maxStr.length(); i++) {
            char c = maxStr.charAt(i);
            if (c == ',' || c == '.') {
                numViews.get(numViews.size() - 1).setLabel(String.valueOf(c));
            } else {
                int max = 9;
                if (first) {
                    max = Integer.parseInt(String.valueOf(c));
                    first = false;
                }
                addScrollNum(max);
            }
        }
    }

    /**
     * 添加单个滚动view
     *
     * @param max 数字会在0~max之间滚动
     */
    private void addScrollNum(int max) {
        NumberWheelView wheel = new NumberWheelView(getContext());
        numViews.add(wheel);
        wheel.setAdapter(new NumericWheelAdapter(0, max));
        addView(wheel);
    }

    /**
     * 获取两位小数（即金额）
     *
     * @param num
     * @return
     */
    public static String getMoneyString(Double num) {
        if (num == null) {
            num = 0.0;
        }
        DecimalFormat format = new DecimalFormat();
        format.applyPattern(PATTERN_MONEY);
        return format.format(num);
    }


}
