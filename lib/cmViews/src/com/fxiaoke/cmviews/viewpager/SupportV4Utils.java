/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.cmviews.viewpager;

import androidx.fragment.app.FragmentTransaction;

/**
 *
 */
public class SupportV4Utils {

    /**
     * 为了解决crm中调用此方法编译不通过的问题，临时方案
     * @param transaction
     */
    public static void commitNowAllowingStateLoss(FragmentTransaction transaction) {
        if (transaction != null) {
            transaction.commitNowAllowingStateLoss();
        }
    }

}
