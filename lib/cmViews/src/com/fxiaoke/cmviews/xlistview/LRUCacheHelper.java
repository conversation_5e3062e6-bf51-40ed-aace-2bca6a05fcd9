package com.fxiaoke.cmviews.xlistview;

import android.app.ActivityManager;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Build;
import android.util.LruCache;


/**
 * 缓存
 * <AUTHOR>
 *
 */
public class LRUCacheHelper {
    private static LruCache<String, Bitmap> lruCacheManage(Context context) {
        int memClass = ((ActivityManager)context.getSystemService(Context.ACTIVITY_SERVICE)).getMemoryClass();
        int cacheSize = 1024 * 1024 * memClass / 24;
        LruCache<String, Bitmap> imageCache1 = new LruCache<String, Bitmap>(cacheSize) {

            @Override 
            protected Bitmap create(String key) {
                return super.create(key);
            }

            @Override 
            protected void entryRemoved(boolean evicted, String key, Bitmap oldValue, Bitmap newValue) {
                super.entryRemoved(evicted, key, oldValue, newValue);
            }

            @Override 
            protected int sizeOf(String key, Bitmap value) {
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
                    return value.getRowBytes() * value.getHeight();                   
                } else {
                    return value.getAllocationByteCount();
                }
            }
        };
        
        return imageCache1;
    }
    
    private static LruCache<String, Bitmap> mInstance;
    
    protected static LruCache<String, Bitmap> getCache(Context context) {
        if (mInstance == null) {
            mInstance = lruCacheManage(context);
        }
        return mInstance;
    }
}
