package com.facishare.fs.common_utils;

import android.content.Context;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.util.Log;

/**
 * Created by wubb on 2017/11/15.
 * 手机系统多媒体文件工具类
 */

public class MediaStoreHelper {
    /**
     * 通过扫描手机的指定路径来更新手机相册，文件管理器等的媒体文件状态，
     * 主要用的就是当手机指定路径下面的文件有变化，及时更新相关的媒体文件管理器的内容
     * @param context
     * @param paths
     */
    public static void scanFile(Context context, String...paths)
    {
        MediaScannerConnection.scanFile(context, paths, null, new MediaScannerConnection.OnScanCompletedListener() {
            @Override
            public void onScanCompleted(String path, Uri uri) {
                if(uri!=null){
                    Log.d("MediaStoreHelper",path+" was added to media store");
                }
            }
        });
    }
}
