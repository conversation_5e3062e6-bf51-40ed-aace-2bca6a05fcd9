/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.common_utils.photo;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

import android.content.Context;

/**
 * Created by xiongtj on 2017/6/29.
 */
public class ImageTypeUtils {

    public enum ImageType{


        Jpeg(1),Png(2),Webp(3),SVG(4),Animated(5);

        public int type;

        ImageType(int type){
            this.type = type;
        }

    }

    public static int getImageType(Context ctx ,int resId){
        InputStream inputStream = null;
        byte[] b = new byte[35];
        try {
            ctx.getResources().openRawResource(resId);
            inputStream.read(b, 0, b.length);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if(null != inputStream) {
                try {
                    inputStream.close();
                } catch (IOException e) {}
            }
        }
        return getImageType(b);
    }


    public static int getImageType(InputStream inputStream){
        byte[] b = getBytes(inputStream);
        return getImageType(b);
    }

    public static int getImageType(File file){
        byte[] b = getBytes(file);
        return getImageType(b);
    }
    public static int getImageType(byte[] b){
        if(isJpeg(b)){
            return ImageType.Jpeg.type;
        }else if(isPng(b)){
            return ImageType.Png.type;
        }else if(isGif(b)||isAnimWebp(b)){
            return ImageType.Animated.type;
        }else if(isWebp(b)){
            return ImageType.Webp.type;
        }else if(isSvg(b)){
            return ImageType.SVG.type;
        }

        return 0;
    }

    public static boolean isAnimatedImage(File file){
        byte[] b = getBytes(file);
        if(b.length!=0){
            if(isGif(b)||isAnimWebp(b)){
                return true;
            }
        }

        return false;

    }

    public static boolean onlyCacheDisk(File file){
        byte[] b = getBytes(file);
        if(b.length!=0){
            if(isGif(b)||isAnimWebp(b)||isSvg(b)){
                return true;
            }
        }

        return false;

    }

    public static byte[] getBytes(File file){
        try {
            return getBytes(new FileInputStream(file));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new byte[35];
    }

    public static byte[] getBytes(InputStream inputStream){
        byte[] b = new byte[35];
        try {
            if(inputStream!=null){
                inputStream.read(b, 0, b.length);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if(null != inputStream) {
                try {
                    inputStream.close();
                } catch (IOException e) {}
            }
        }
        return b;
    }


    public static boolean isSvg(byte[] data) {
        return data != null && data.length > 5?
                ((data[0] == 60 && data[1] == 63 && data[2] == 120&& data[3] == 109&& data[4] == 108)||(data[0] == 60 && data[1] == 115 && data[2] == 118&& data[3] == 103)):false;
    }

    public static boolean isGif(byte[] data) {
        return data != null && data.length > 3?data[0] == 71 && data[1] == 73 && data[2] == 70:false;
    }

    public static boolean isPng(byte[] data) {
        return data != null && data.length > 3?data[1] == 80 && data[2] == 78 && data[3] == 71:false;
    }

    public static boolean isJpeg(byte[] data) {
        return data != null && data.length > 9?data[6] == 74 && data[7] == 70 && data[8] == 73 && data[9] == 70:false;
    }

    public static boolean isWebp(byte[] data) {
        return data != null && data.length > 12?data[8] == 87 && data[9] == 69 && data[10] == 66 && data[11] == 80:false;
    }

    public static boolean isAnimWebp(byte[] data) {
        return data != null && data.length > 33?data[8] == 87 && data[9] == 69 && data[10] == 66 && data[11] == 80 && data[30] == 65 && data[31] == 78 && data[32] == 73 && data[33] == 77:false;
    }
}
