<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
  -->
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle" >
            <stroke android:color="#fcb058" android:width="1dp"/>
            <solid android:color="#ffe5e5e5"/>
            <corners android:radius="3dp"/>
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle" >
            <stroke android:color="#fcb058" android:width="1dp"/>
            <solid android:color="#ffffff"/>
            <corners android:radius="3dp"/>
        </shape>
    </item>
</selector>
