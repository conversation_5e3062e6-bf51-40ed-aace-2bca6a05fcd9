/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.fscommon_res.weex.module;

import java.util.HashMap;
import java.util.Map;

import com.afollestad.materialdialogs.DialogFragmentWrapper;
import com.afollestad.materialdialogs.MaterialDialog;
import com.facishare.fs.dialogs.CommonDialog;
import com.facishare.fs.dialogs.LoadingProDialog;
import com.fxiaoke.fscommon_res.R;
import com.fxiaoke.fscommon_res.activity.FCBaseActivity;
import com.taobao.weex.annotation.JSMethod;
import com.taobao.weex.bridge.JSCallback;
import com.taobao.weex.common.WXModule;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import androidx.fragment.app.FragmentActivity;
import android.view.View;
import android.widget.EditText;

/**
 * xiongtj
 */
public class CommonDialogModule extends WXModule {
    private static final String TAG = CommonDialogModule.class.getSimpleName();

    private Map<String, Dialog> dialogMap = new HashMap<>();

//    private int loadingFlag = 0;

    @JSMethod(uiThread = true)
    public void showTwoButtonDialog(final String ref, String message, String okBtnName, String cancleBtnName, final JSCallback
            jsOkCallback, final JSCallback
                                            jsCancelCallback
    ) {
        if (isActivityDestory()) {
            return;
        }
        final CommonDialog dialog;
        if (dialogMap.get(ref) != null) {
            dialog = (CommonDialog) dialogMap.get(ref);
        } else {
            dialog = CommonDialog.createTwoButtonDialog(mWXSDKInstance.getContext(), message);
            CommonDialog
                    .myDiaLogListener
                    myDiaLogListener = new CommonDialog.myDiaLogListener() {

                @Override
                public void onClick(View view) {
                    if (view.getId() == R.id.button_mydialog_cancel) {
                        dialog.dismiss();
                        dialogMap.remove(ref);
                        if (jsCancelCallback != null) {
                            jsCancelCallback.invoke(null);
                        }

                    } else if (view.getId() == R.id.button_mydialog_enter) {
                        dialog.dismiss();
                        dialogMap.remove(ref);
                        if (jsOkCallback != null) {
                            jsOkCallback.invoke(null);
                        }
                    }

                }
            };

            dialog.initTwoButtonDialogListenerTShow(cancleBtnName,
                    okBtnName,
                    myDiaLogListener);
            dialogMap.put(ref, dialog);
        }
        dialog.show();

    }

    @JSMethod(uiThread = true)
    public void showOneButtonDialog(final String ref, String title, String message, String okBtnName,
                                    final JSCallback jsOkCallback) {
        if (isActivityDestory()) {
            return;
        }
        final CommonDialog dialog;
        if (dialogMap.get(ref) != null) {
            dialog = (CommonDialog) dialogMap.get(ref);
        } else {
            dialog = CommonDialog.createOneButtonDialog(mWXSDKInstance.getContext(), title, message, okBtnName);
            CommonDialog.myDiaLogListener myDiaLogListener = new CommonDialog.myDiaLogListener() {
                @Override
                public void onClick(View view) {
                    if (view.getId() == R.id.button_mydialog_enter) {
                        dialog.dismiss();
                        dialogMap.remove(ref);
                        if (jsOkCallback != null) {
                            jsOkCallback.invoke(null);
                        }
                    }

                }
            };
            dialog.setDialogListener(myDiaLogListener);
            dialog.show();
            dialogMap.put(ref, dialog);
        }
        dialog.show();
    }

    @JSMethod(uiThread = true)
    public void showEditDialog(final String ref, String title, String okBtnName, String cancleBtnName, String defaultText,
                               String hint,
                               final JSCallback
                                       jsOkCallback, final JSCallback
                                       jsCancelCallback
    ) {
        if (isActivityDestory()) {
            return;
        }
        final CommonDialog dialog;
        if (dialogMap.get(ref) != null) {
            dialog = (CommonDialog) dialogMap.get(ref);
        } else {
            dialog = CommonDialog.createTwoButtonDialog(mWXSDKInstance.getContext(), null);
            dialog.setTitle(title);
            dialog.setShowType(CommonDialog.FLAG_EditView);
            CommonDialog
                    .myDiaLogListener
                    myDiaLogListener = new CommonDialog.myDiaLogListener() {

                @Override
                public void onClick(View view) {
                    if (view.getId() == R.id.button_mydialog_cancel) {
                        dialog.dismiss();
                        dialogMap.remove(ref);
                        if (jsCancelCallback != null) {
                            jsCancelCallback.invoke(null);
                        }

                    } else if (view.getId() == R.id.button_mydialog_enter) {
                        dialog.dismiss();
                        dialogMap.remove(ref);
                        if (jsOkCallback != null) {
                            final EditText editText = dialog.getEditTextView();
                            jsOkCallback.invoke(editText.getText().toString());
                        }
                    }

                }
            };

            dialog.initTwoButtonDialogListenerTShow(cancleBtnName,
                    okBtnName,
                    myDiaLogListener);
            EditText editText = dialog.getEditTextView();
            editText.setText(defaultText);
            editText.setSelection(editText.length());
            editText.setHint(hint);
            dialogMap.put(ref, dialog);
        }
        dialog.show();

    }

    @JSMethod(uiThread = true)
    public void dismissDialog(String ref) {
        if (isActivityDestory()) {
            return;
        }

//        loadingFlag--;
//        if (loadingFlag < 0) {
//            loadingFlag = 0;
//        }

        final Dialog dialog = dialogMap.get(ref);
        if (dialog != null) {
            dialog.dismiss();
            dialogMap.remove(ref);
        }
    }

    /**
     * 显示进度条
     */
    @JSMethod(uiThread = true)
    public void showLoadingDialog(final String ref, String message, boolean cancleable, final JSCallback
            jsCancelCallback) {
        if (isActivityDestory()) {
            return;
        }
        final MaterialDialog mLoadingProDialog;
        if (dialogMap.get(ref) != null) {
            mLoadingProDialog = (MaterialDialog) dialogMap.get(ref);
        } else {
            mLoadingProDialog = DialogFragmentWrapper.showIndeterminateProgressSafe(
                    (FragmentActivity) mWXSDKInstance.getContext(), message, cancleable);
            mLoadingProDialog.setCanceledOnTouchOutside(false);
            mLoadingProDialog.setCancelable(cancleable);
            if (cancleable) {
                mLoadingProDialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
                    @Override
                    public void onCancel(DialogInterface dialog) {
//                        loadingFlag = 0;
                        dialogMap.remove(ref);
                        if (jsCancelCallback != null) {
                            jsCancelCallback.invoke(null);
                        }
                    }
                });
            }
            mLoadingProDialog.setMessage(message);
            dialogMap.put(ref, mLoadingProDialog);
        }

//        loadingFlag++;

        mLoadingProDialog.show();
    }


    @JSMethod(uiThread = true)
    public void setLoadingMessage(String ref, String message) {
        if (isActivityDestory()) {
            return;
        }
        final Dialog mLoadingProDialog = dialogMap.get(ref);
        if (mLoadingProDialog != null && mLoadingProDialog instanceof LoadingProDialog) {
            ((LoadingProDialog) mLoadingProDialog).setMessage(message);
        }
    }
    boolean isActivityDestory(){
        boolean ret=false;
        Context context = mWXSDKInstance.getContext();
        if (context instanceof Activity && (((Activity) context).isFinishing() ||
                (context instanceof FCBaseActivity && ((FCBaseActivity)context).isDestroyed()))) {
            ret=true;
        }
        return ret;
    }
    @Override
    public void onActivityDestroy() {
        if (dialogMap != null) {
            dialogMap.clear();
        }
    }
}
