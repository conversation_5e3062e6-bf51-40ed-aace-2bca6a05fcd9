/*
 * Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.fscommon_res.guide.dialog.view;

import com.facishare.fs.common_utils.FSScreen;
import com.fxiaoke.fscommon_res.R;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

/**
 * ViewPager底部圆点指示器
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2017/11/1
 */
public class DotIndicatorView extends View {

    private int mCheckedColor;//选中的颜色
    private int mNormalColor;//未选中颜色
    private int mDotCount;//圆点数量
    private float mDotRadius;//圆点半径
    private float mDotSpace;//两个圆点之间的间隔
    private int mCheckedIndex = 1;//选中的索引
    private Paint mPaint;//画笔

    public DotIndicatorView(Context context) {
        this(context, null);
    }

    public DotIndicatorView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DotIndicatorView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        getAttribute(context, attrs);
        initPaint();
    }

    private void getAttribute(Context context, AttributeSet attributeSet) {
        TypedArray typedArray = context.obtainStyledAttributes(attributeSet, R.styleable.dot_indicator_view);
        mNormalColor = typedArray.getColor(R.styleable.dot_indicator_view_normalColor, Color.parseColor("#dfe3e8"));
        mCheckedColor = typedArray.getColor(R.styleable.dot_indicator_view_checkedColor, Color.parseColor("#919eab"));
        mDotRadius = typedArray.getDimension(R.styleable.dot_indicator_view_radius, FSScreen.dip2px(4));
        mDotCount = typedArray.getInteger(R.styleable.dot_indicator_view_dotCount, 3);
        mDotSpace = typedArray.getDimension(R.styleable.dot_indicator_view_dotSpace, FSScreen.dip2px(11));
        typedArray.recycle();
    }

    private void initPaint() {
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setStyle(Paint.Style.FILL);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        //宽高值取计算出来的默认值
        setMeasuredDimension(getDefaultWidth(), getDefaultHeight());
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (mDotCount <= 0) {
            return;
        }
        float radius = mDotRadius;
        float centerX = radius + getPaddingLeft();
        float centerY = radius + getPaddingTop();
        for (int i = 0; i < mDotCount; i++) {
            mPaint.setColor(i == mCheckedIndex ? mCheckedColor : mNormalColor);
            canvas.drawCircle(centerX, centerY, radius, mPaint);
            centerX += radius * 2 + mDotSpace;
        }
    }

    /**
     * 获取默认需要的宽
     *
     * @return
     */
    private int getDefaultWidth() {
        if (mDotCount == 0) {
            return 0;
        }
        int padding = getPaddingLeft() + getPaddingRight();
        float width = mDotCount * mDotRadius * 2 + (mDotCount - 1) * mDotSpace;
        return (int) (width + padding);
    }

    /**
     * 获取默认需要的高
     *
     * @return
     */
    private int getDefaultHeight() {
        int padding = getPaddingTop() + getPaddingBottom();
        float height = mDotRadius * 2;
        return (int) (height + padding);
    }

    /**
     * 根据MeasureSpecMode类型获取Size值
     *
     * @param defaultSize 默认值
     * @param measureSpec 测量
     *
     * @return 需要的Size值
     */
    private int getSize(int defaultSize, int measureSpec) {
        int mode = MeasureSpec.getMode(measureSpec);
        int size = MeasureSpec.getSize(measureSpec);
        switch (mode) {
            case MeasureSpec.UNSPECIFIED:
                return defaultSize;
            case MeasureSpec.AT_MOST:
                return defaultSize > size ? size : defaultSize;
            case MeasureSpec.EXACTLY:
                return size;
            default:
                return 0;
        }
    }

    /**
     * 更新选中的索引
     *
     * @param index 索引
     */
    public void updateCheckedIndex(int index) {
        this.mCheckedIndex = index < 0 ? 0 : index;
        invalidate();
    }

    /**
     * 更新点的数量
     *
     * @param count 数量
     */
    public void updateDotCount(int count) {
        this.mDotCount = count < 0 ? 0 : count;
        invalidate();
    }
}
