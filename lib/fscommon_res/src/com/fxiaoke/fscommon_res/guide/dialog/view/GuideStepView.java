/*
 * Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.fscommon_res.guide.dialog.view;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.fxiaoke.fscommon_res.R;
import com.fxiaoke.fscommon_res.guide.dialog.info.DialogGuideInfo;
import com.fxiaoke.fscommon_res.guide.dialog.info.DialogGuideType;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

/**
 * 新手引导View
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2017/10/31
 */
public class GuideStepView extends FrameLayout {

    private ImageView mImageView;
    private TextView mStepText;
    private TextView mStepDescText;
    private TextView mCloseView;
    private TextView mDetailInfoView;

    private DialogGuideInfo mGuideInfo;
    private OnCloseClickListener mCloseClickListener;

    public GuideStepView(Context context) {
        this(context, null);
    }

    public GuideStepView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public GuideStepView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        View view =
                LayoutInflater.from(context).inflate(R.layout.layout_crm_guide_step, this, true);
        initView(view);
    }

    private void initView(View view) {
        mImageView = (ImageView) view.findViewById(R.id.image);
        mStepText = (TextView) view.findViewById(R.id.tv_step);
        mStepDescText = (TextView) view.findViewById(R.id.tv_step_desc);
        mDetailInfoView = view.findViewById(R.id.tv_detail_info);
        mDetailInfoView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                Context context = v.getContext();
                String url = mGuideInfo == null ? null : mGuideInfo.getIntroductionUrl();
                if (TextUtils.isEmpty(url)) {
                    return;
                }
                String title = I18NHelper.getText("crm.utils.HelperDocManager.1561")/* CRM帮助 */;
                HostInterfaceManager.getHostInterface().getJsApiWebActivity()
                        .startActivity(context, url, title);
            }
        });
        mCloseView = (TextView) view.findViewById(R.id.tv_close);
        mCloseView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mCloseClickListener != null) {
                    mCloseClickListener.onCloseClick();
                }
            }
        });
    }

    public void updateView(DialogGuideInfo guideInfo, int step) {
        this.mGuideInfo = guideInfo;
        if (guideInfo == null) {
            return;
        }
        int imgResId = guideInfo.getStepImage(step);
        String title = guideInfo.getStepTitle(step);
        String desc = guideInfo.getStepDescription(step);
        boolean isLastStep = (step == (guideInfo.stepCount() - 1));
        boolean hasImg = imgResId > 0;
        if (hasImg) {
            mImageView.setImageResource(imgResId);
        }
        mImageView.setVisibility(hasImg ? VISIBLE : INVISIBLE);
        boolean hasStepTitle = !TextUtils.isEmpty(title);
        if (hasStepTitle) {
            mStepText.setText(title);
        }
        mStepText.setVisibility(hasStepTitle ? VISIBLE : INVISIBLE);
        boolean hasStepDesc = !TextUtils.isEmpty(desc);
        if (hasStepDesc) {
            mStepDescText.setText(desc);
        }
        mStepDescText.setVisibility(hasStepDesc ? VISIBLE : INVISIBLE);

        mCloseView.setVisibility(isLastStep ? VISIBLE : INVISIBLE);
        DialogGuideType guideType = guideInfo.getGuideType();
        boolean showDetailInfo =
                guideType == DialogGuideType.ORDER || guideType == DialogGuideType.QUOTE;
        mDetailInfoView.setVisibility((showDetailInfo && isLastStep) ? VISIBLE : GONE);
    }

    public void setCloseClickListener(OnCloseClickListener closeClickListener) {
        this.mCloseClickListener = closeClickListener;
    }

    public interface OnCloseClickListener {
        void onCloseClick();
    }
}
