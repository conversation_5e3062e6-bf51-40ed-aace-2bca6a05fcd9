package com.fxiaoke.fscommon_res.view.datepickerviews.widget;

import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.i18n.I18NHelper;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.EditText;
import android.widget.NumberPicker;

/**
 * 作用：作为一个和NumberPicker 放在一起的TextView出现
 * Created by zhangd on 2017/1/18.
 */

public class CustomStyleNumberPicker extends NumberPicker {
    public CustomStyleNumberPicker(Context context) {
        super(context);
    }

    public CustomStyleNumberPicker(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CustomStyleNumberPicker(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void addView(View child, int index,
                        android.view.ViewGroup.LayoutParams params) {
        super.addView(child, index, params);
        updateView(child);
    }

    private EditText mText;
    public void updateView(View view) {
        if (view instanceof EditText) {
            mText = (EditText)view;
            //这里修改字体的属性
            mText.setTextColor(Color.parseColor("#999999"));
            mText.setTextSize(12);
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        if (mText!=null) {
            if (I18NHelper.Language.ZH_CN.value.equalsIgnoreCase( I18NHelper.getInstance().getCurrentLang()) || I18NHelper.Language.ZH_TW.value.equalsIgnoreCase( I18NHelper.getInstance().getCurrentLang())) {
                super.onMeasure(MeasureSpec.makeMeasureSpec(FSScreen.dip2px(36), MeasureSpec.EXACTLY),
                        heightMeasureSpec);
            }else {
                super.onMeasure(MeasureSpec.makeMeasureSpec(FSScreen.dip2px(64), MeasureSpec.EXACTLY),
                        heightMeasureSpec);
            }

        }else {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        }
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        return true;
    }
}
