package com.fxiaoke.fscommon_res.view.datepickerviews.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.NumberPicker;

import com.fxiaoke.fscommon_res.R;
import com.fxiaoke.fscommon_res.view.datepickerviews.base.ITimePicker;

import java.text.DateFormatSymbols;
import java.util.Calendar;
import java.util.Locale;

/**
 * Created by zhangd on 2016/9/20.
 */
public class TimePickerView extends FrameLayout implements ITimePicker, ICalendarListener{

    private static final int HOURS_IN_HALF_DAY = 12;

    /**
     * A no-op callback used in the constructor to avoid null checks later in
     * the code.
     */
    private static final OnTimeChangedListener NO_OP_CHANGE_LISTENER = new OnTimeChangedListener() {
        public void onTimeChanged(TimePickerView view, int hourOfDay, int minute) {
        }
    };

    // state
    private boolean mIs24HourView = false;

    private boolean mIsAm;

    // ui components
    private final NumberPicker mHourSpinner;

    private final NumberPicker mMinuteSpinner;

    private final NumberPicker mAmPmSpinner;

    private final String[] mAmPmStrings;

    // callbacks
    private OnTimeChangedListener mOnTimeChangedListener;
    private OnCalendarChangedListener mOnCalendarChangedListener;

    private Calendar mTempCalendar;

    private Locale mCurrentLocale;

    private int mMinuteInterval = 5;

    @Override
    public void setCalendar(Calendar time) {
        if (time == null) {
            return;
        }
        updateTime(time.get(Calendar.HOUR_OF_DAY), time.get(Calendar.MINUTE));
    }

    @Override
    public void setEndCalendar(Calendar time) {

    }

    @Override
    public void setMinDate(Calendar min) {

    }

    @Override
    public void setMaxDate(Calendar max) {

    }

    @Override
    public Calendar getCalendar() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.HOUR_OF_DAY, getCurrentHour());
        c.set(Calendar.MINUTE, getCurrentMinute());
        c.set(Calendar.MILLISECOND, 0);
        return c;
    }

    @Override
    public Calendar getEndCalendar() {
        return getCalendar();
    }

    @Override
    public boolean consumeConfirmClick() {
        return true;
    }

    /**
     * The callback interface used to indicate the time has been adjusted.
     */
    public interface OnTimeChangedListener {

        /**
         * @param view The view associated with this listener.
         * @param hourOfDay The current hour.
         * @param minute The current minute.
         */
        void onTimeChanged(TimePickerView view, int hourOfDay, int minute);
    }

    public TimePickerView(Context context, boolean is24HourView) {
        this(context, null, is24HourView);
    }

    public TimePickerView(Context context, AttributeSet attrs) {
        this(context, attrs, 0, true);
    }

    public TimePickerView(Context context, AttributeSet attrs, boolean is24HourView) {
        this(context, attrs, 0, is24HourView);
    }

    public TimePickerView(Context context, AttributeSet attrs, int defStyleAttr, boolean is24HourView) {
        super(context, attrs, defStyleAttr);
        mIs24HourView = is24HourView;
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(
                Context.LAYOUT_INFLATER_SERVICE);
        inflater.inflate(R.layout.fc_time_picker_view, this, true);
        // hour
        mHourSpinner = (NumberPicker) findViewById(R.id.hour);
        mHourSpinner.setDescendantFocusability(NumberPicker.FOCUS_BLOCK_DESCENDANTS);
        mHourSpinner.setFormatter(new TwoDigitFormatter());
        mHourSpinner.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker numberPicker, int oldVal, int newVal) {
                if (!is24HourView()) {
                    if ((oldVal == 12 - 1 && newVal == 12)
                            || (oldVal == 12 && newVal == 12 - 1)) {
                        mIsAm = !mIsAm;
                        updateAmPmControl();
                    }
                }
                notifyDateChanged();
            }
        });

        updateHourControl();
        // minute
        mMinuteSpinner = (NumberPicker) findViewById(R.id.minute);
        mMinuteSpinner.setDescendantFocusability(NumberPicker.FOCUS_BLOCK_DESCENDANTS);
        updateMinuteSpinner();

        // 获取本地 am/pm 字符串，并将他们用到 spinner 中
        mAmPmStrings = new DateFormatSymbols().getAmPmStrings();

        // am/pm
        mAmPmSpinner = (NumberPicker) findViewById(R.id.amPm);
        mAmPmSpinner.setDescendantFocusability(NumberPicker.FOCUS_BLOCK_DESCENDANTS);
        mAmPmSpinner.setMinValue(0);
        mAmPmSpinner.setMaxValue(1);
        mAmPmSpinner.setDisplayedValues(mAmPmStrings);
        mAmPmSpinner.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker picker, int i, int i1) {
                picker.requestFocus();
                mIsAm = !mIsAm;
                updateAmPmControl();
            }
        });

        updateAmPmControl();

        mTempCalendar = Calendar.getInstance();
        int minute = mTempCalendar.get(Calendar.MINUTE);
        int hour = mTempCalendar.get(Calendar.HOUR_OF_DAY);

        updateTime(hour, minute);
    }

    public void setOnTimeChangedListener(
            OnTimeChangedListener onTimeChangedListener) {
        mOnTimeChangedListener = onTimeChangedListener;
    }

    public void setOnCalendarChangedListener(
            OnCalendarChangedListener onCalendarChangedListener) {
        mOnCalendarChangedListener = onCalendarChangedListener;
    }

    private boolean is24HourView() {
        return mIs24HourView;
    }

    private void updateAmPmControl() {
        if (mIs24HourView) {
            mAmPmSpinner.setVisibility(View.GONE);
        } else {
            mAmPmSpinner.setVisibility(View.VISIBLE);
            int index = mIsAm ? Calendar.AM : Calendar.PM;
            mAmPmSpinner.setValue(index);
        }
    }

    /**
     * 设置时间间隔后，显示的分钟个数小于5，numberPicker就不循环了
     * */
    public void setMinuteInterval(int interval) {
        if (interval <= 0 || 60 < interval) {
            return;
        }
        mMinuteInterval = interval;
        updateMinuteSpinner();
    }

    private void notifyDateChanged() {
        if (mOnTimeChangedListener != null) {
            mOnTimeChangedListener.onTimeChanged(this, getCurrentHour(), getCurrentMinute());
        }
        if (mOnCalendarChangedListener != null) {
            mOnCalendarChangedListener.onCalendarChanged(getCalendar());
        }
    }

    private void updateMinuteSpinner() {
        int minuteMax = 60 / mMinuteInterval;
        String[] minuteStr = new String[minuteMax];
        for (int i = 0; i < minuteStr.length; i++) {
            String temp = String.valueOf(i * mMinuteInterval);
            if (temp.length() == 1) {
                temp =  "0" + temp;
            }
            minuteStr[i] = temp;
        }


        mMinuteSpinner.setDisplayedValues(null);
        mMinuteSpinner.setMinValue(0);
        mMinuteSpinner.setMaxValue(minuteMax - 1);
        mMinuteSpinner.setDisplayedValues(minuteStr);
        mMinuteSpinner.setOnLongPressUpdateInterval(100);
//        mMinuteSpinner.setFormatter(new TwoDigitFormatter());
        mMinuteSpinner.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker numberPicker, int oldVal, int newVal) {
                notifyDateChanged();
                int minValue = mMinuteSpinner.getMinValue();
                int maxValue = mMinuteSpinner.getMaxValue();
                if (oldVal == maxValue && newVal == minValue) {
                    int newHour = mHourSpinner.getValue() + 1;
                    if (!is24HourView() && newHour == 12) {
                        mIsAm = !mIsAm;
                        updateAmPmControl();
                    }
                    mHourSpinner.setValue(newHour);
                } else if (oldVal == minValue && newVal == maxValue) {
                    int newHour = mHourSpinner.getValue() - 1;
                    if (!is24HourView() && newHour == 12 - 1) {
                        mIsAm = !mIsAm;
                        updateAmPmControl();
                    }
                    mHourSpinner.setValue(newHour);
                }
            }
        });
    }

    public void updateTime(int hourOfDay, int minuteOfHour) {
        boolean seted = false;
        // set hour
        if (hourOfDay != getCurrentHour()) {
            if (!mIs24HourView) {
                if (hourOfDay >= 12) {
                    mIsAm = false;
                    if (hourOfDay > 12) {
                        hourOfDay = hourOfDay - 12;
                    }
                } else {
                    mIsAm = true;
                    if (hourOfDay == 0) {
                        hourOfDay = 12;
                    }
                }
                updateAmPmControl();
            }
            mHourSpinner.setValue(hourOfDay);
            seted = true;
        }

        // set minute
        if (minuteOfHour != mMinuteSpinner.getValue() * mMinuteInterval) {
            mMinuteSpinner.setValue(minuteOfHour / mMinuteInterval);
            seted = true;
        }
        if (seted) {
            notifyDateChanged();
        }
    }

    public int getCurrentHour() {
        int currentHour = mHourSpinner.getValue();
        if (mIs24HourView) {
            return currentHour;
        } else if (mIsAm) {
            return currentHour % 12;
        } else {
            return (currentHour % 12) + 12;
        }
    }

    public int getCurrentMinute() {
        return mMinuteSpinner.getValue() * mMinuteInterval;
    }

    private void updateHourControl() {
        if (mIs24HourView) {
            mHourSpinner.setMinValue(0);
            mHourSpinner.setMaxValue(23);
        } else {
            mHourSpinner.setMinValue(1);
            mHourSpinner.setMaxValue(12);
        }
    }

    /**
     * Use a custom NumberPicker formatting callback to use two-digit minutes
     * strings like "01". Keeping a static formatter etc. is the most efficient
     * way to do this; it avoids creating temporary objects on every call to
     * format().
     */
    private static class TwoDigitFormatter implements NumberPicker.Formatter {
        final StringBuilder mBuilder = new StringBuilder();

        char mZeroDigit = '0';
        java.util.Formatter mFmt;

        final Object[] mArgs = new Object[1];

        TwoDigitFormatter() {
            final Locale locale = Locale.getDefault();
            init(locale);
        }

        private void init(Locale locale) {
            mFmt = createFormatter(locale);
        }

        public String format(int value) {
            final Locale currentLocale = Locale.getDefault();
            if (mZeroDigit != '0') {
                init(currentLocale);
            }
            mArgs[0] = value;
            mBuilder.delete(0, mBuilder.length());
            mFmt.format("%02d", mArgs);
            return mFmt.toString();
        }

        private java.util.Formatter createFormatter(Locale locale) {
            return new java.util.Formatter(mBuilder, locale);
        }
    }
}
