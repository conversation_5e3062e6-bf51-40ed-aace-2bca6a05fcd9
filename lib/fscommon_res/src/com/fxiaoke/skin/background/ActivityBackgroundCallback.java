package com.fxiaoke.skin.background;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;
import android.widget.FrameLayout;

import com.fxiaoke.fxlog.FCLog;

/**
 * Activity背景生命周期回调
 * 监听Activity的生命周期事件，自动应用和清理背景
 */
public class ActivityBackgroundCallback implements Application.ActivityLifecycleCallbacks {
    private static final String TAG = "newui_debug";
    
    private GradientBackgroundManager mManager;
    
    public ActivityBackgroundCallback(GradientBackgroundManager manager) {
        this.mManager = manager;
    }

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
        // 在Activity创建时应用背景
        if (mManager != null) {
            FCLog.d(TAG, "onActivityCreated: " + activity.getClass().getSimpleName());
        }
    }

    @Override
    public void onActivityStarted(Activity activity) {
        // 不处理
    }

    @Override
    public void onActivityResumed(Activity activity) {
        // 在Activity恢复时应用背景，确保在UI完全加载后应用
        if (mManager != null) {
            // 早期过滤检查，避免不必要的方法调用
            if (mManager.isFilterEnabled() && activity != null) {
                String className = activity.getClass().getName();
                if (mManager.getFilteredClassNames().contains(className) || 
                    mManager.getFilteredClassNames().contains(activity.getClass().getSimpleName())) {
                    FCLog.d(TAG, "onActivityResumed: Activity已被过滤，跳过处理: " + activity.getClass().getSimpleName());
                    FrameLayout decorView = (FrameLayout) activity.getWindow().getDecorView();
                    decorView.setBackgroundColor(0xffffffff);
                    return;
                }
            }
            
            FCLog.w(TAG, "!!!onActivityResumed: " + activity.getClass().getSimpleName());
            mManager.applyBackground(activity);
        }
    }

    @Override
    public void onActivityPaused(Activity activity) {
        // 不处理
    }

    @Override
    public void onActivityStopped(Activity activity) {
        // 不处理
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
        // 不处理
    }

    @Override
    public void onActivityDestroyed(Activity activity) {
        // 在Activity销毁时清理背景资源
        if (mManager != null) {
            // 早期过滤检查，避免不必要的方法调用
            if (mManager.isFilterEnabled() && activity != null) {
                String className = activity.getClass().getName();
                if (mManager.getFilteredClassNames().contains(className) || 
                    mManager.getFilteredClassNames().contains(activity.getClass().getSimpleName())) {
                    FCLog.d(TAG, "onActivityDestroyed: Activity已被过滤，跳过处理: " + activity.getClass().getSimpleName());
                    return;
                }
            }
            
            FCLog.d(TAG, "onActivityDestroyed: " + activity.getClass().getSimpleName());
            mManager.clearBackground(activity);
        }
    }
} 