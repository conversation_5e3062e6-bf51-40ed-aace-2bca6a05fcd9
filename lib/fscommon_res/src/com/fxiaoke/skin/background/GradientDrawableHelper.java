package com.fxiaoke.skin.background;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.LayerDrawable;

import com.fxiaoke.fxlog.FCLog;

/**
 * 渐变背景Drawable生成工具类
 * 用于根据GradientTheme生成对应的GradientDrawable
 */
public class GradientDrawableHelper {
    private static final String TAG = "GradientDrawableHelper";

    /**
     * 根据主题创建渐变Drawable
     * 
     * @param theme 渐变主题
     * @return 对应的Drawable（可能是GradientDrawable或LayerDrawable），如果失败返回null
     */
    public static Drawable createGradientDrawable(GradientTheme theme) {
        if (theme == null) {
            FCLog.e(TAG, "createGradientDrawable: 主题为空");
            return null;
        }

        try {
            GradientDrawable gradientDrawable;
            switch (theme.getGradientType()) {
                case LINEAR:
                    gradientDrawable = createLinearGradientDrawable(theme);
                    break;
                case RADIAL:
                    gradientDrawable = createRadialGradientDrawable(theme);
                    break;
                case SWEEP:
                    gradientDrawable = createSweepGradientDrawable(theme);
                    break;
                default:
                    FCLog.e(TAG, "不支持的渐变类型: " + theme.getGradientType());
                    gradientDrawable = createLinearGradientDrawable(theme);
                    break;
            }

            // 如果有底色，创建LayerDrawable
            if (theme.getBackgroundColor() != Color.TRANSPARENT) {
                return createLayerDrawable(theme.getBackgroundColor(), gradientDrawable);
            } else {
                return gradientDrawable;
            }
        } catch (Exception e) {
            FCLog.e(TAG, "创建渐变Drawable失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 创建LayerDrawable实现底色+渐变层效果
     * 
     * @param backgroundColor 底色
     * @param gradientDrawable 渐变层
     * @return LayerDrawable
     */
    private static LayerDrawable createLayerDrawable(int backgroundColor, GradientDrawable gradientDrawable) {
        // 创建底色层
        ColorDrawable backgroundLayer = new ColorDrawable(backgroundColor);
        
        // 创建LayerDrawable
        Drawable[] layers = new Drawable[]{backgroundLayer, gradientDrawable};
        LayerDrawable layerDrawable = new LayerDrawable(layers);
        
        FCLog.d(TAG, "创建LayerDrawable: 底色=" + String.format("#%08X", backgroundColor) + ", 渐变层已叠加");
        return layerDrawable;
    }

    /**
     * 创建线性渐变Drawable
     * 
     * @param theme 渐变主题
     * @return 线性渐变GradientDrawable
     */
    public static GradientDrawable createLinearGradientDrawable(GradientTheme theme) {
        GradientDrawable drawable = new GradientDrawable();
        drawable.setShape(GradientDrawable.RECTANGLE);
        
        // 设置渐变方向
        GradientDrawable.Orientation orientation = getOrientationFromAngle(theme.getAngle());
        drawable.setOrientation(orientation);
        
        // 设置渐变颜色
        if (theme.isCenterColorUsed()) {
            // 三色渐变
            int[] colors = new int[]{
                theme.getStartColor(), 
                theme.getCenterColor(), 
                theme.getEndColor()
            };
            
            // 设置位置数组以支持centerPosition
            float centerPos = theme.getCenterPosition();
            if (centerPos > 0 && centerPos < 1) {
                float[] positions = new float[]{0.0f, centerPos, 1.0f};
                drawable.setColors(colors);
                // 注意：GradientDrawable不直接支持positions，这里记录日志说明限制
                FCLog.d(TAG, "创建三色线性渐变（centerPosition=" + centerPos + "）: " 
                        + String.format("#%08X", theme.getStartColor()) 
                        + " -> " + String.format("#%08X", theme.getCenterColor())
                        + " -> " + String.format("#%08X", theme.getEndColor())
                        + " 注意：Android GradientDrawable不支持自定义位置");
            } else {
                drawable.setColors(colors);
                FCLog.d(TAG, "创建三色线性渐变: " + String.format("#%08X", theme.getStartColor()) 
                        + " -> " + String.format("#%08X", theme.getCenterColor())
                        + " -> " + String.format("#%08X", theme.getEndColor()));
            }
        } else {
            // 双色渐变
            int[] colors = new int[]{theme.getStartColor(), theme.getEndColor()};
            drawable.setColors(colors);
            FCLog.d(TAG, "创建双色线性渐变: " + String.format("#%08X", theme.getStartColor()) 
                    + " -> " + String.format("#%08X", theme.getEndColor()));
        }
        
        return drawable;
    }

    /**
     * 创建径向渐变Drawable
     * 
     * @param theme 渐变主题
     * @return 径向渐变GradientDrawable
     */
    public static GradientDrawable createRadialGradientDrawable(GradientTheme theme) {
        GradientDrawable drawable = new GradientDrawable();
        drawable.setShape(GradientDrawable.RECTANGLE);
        drawable.setGradientType(GradientDrawable.RADIAL_GRADIENT);
        
        // 设置渐变半径（如果指定了的话）
        if (theme.getRadius() > 0) {
            drawable.setGradientRadius(theme.getRadius());
        }
        
        // 设置渐变颜色
        if (theme.isCenterColorUsed()) {
            int[] colors = new int[]{
                theme.getStartColor(), 
                theme.getCenterColor(), 
                theme.getEndColor()
            };
            drawable.setColors(colors);
        } else {
            int[] colors = new int[]{theme.getStartColor(), theme.getEndColor()};
            drawable.setColors(colors);
        }
        
        FCLog.d(TAG, "创建径向渐变，半径: " + theme.getRadius());
        return drawable;
    }

    /**
     * 创建扫描渐变Drawable
     * 注意：GradientDrawable不直接支持扫描渐变，这里创建一个近似的径向渐变
     * 
     * @param theme 渐变主题
     * @return 扫描渐变GradientDrawable（实际为径向渐变）
     */
    public static GradientDrawable createSweepGradientDrawable(GradientTheme theme) {
        // 由于GradientDrawable不支持扫描渐变，我们创建一个径向渐变作为替代
        FCLog.w(TAG, "GradientDrawable不支持扫描渐变，使用径向渐变替代");
        
        GradientDrawable drawable = new GradientDrawable();
        drawable.setShape(GradientDrawable.RECTANGLE);
        drawable.setGradientType(GradientDrawable.RADIAL_GRADIENT);
        
        // 设置渐变颜色
        if (theme.isCenterColorUsed()) {
            int[] colors = new int[]{
                theme.getStartColor(), 
                theme.getCenterColor(), 
                theme.getEndColor()
            };
            drawable.setColors(colors);
        } else {
            int[] colors = new int[]{theme.getStartColor(), theme.getEndColor()};
            drawable.setColors(colors);
        }
        
        return drawable;
    }

    /**
     * 根据角度获取GradientDrawable的Orientation
     * 
     * @param angle 角度（0-360）
     * @return 对应的Orientation
     */
    private static GradientDrawable.Orientation getOrientationFromAngle(int angle) {
        // 将角度标准化到0-360度
        angle = angle % 360;
        if (angle < 0) {
            angle += 360;
        }
        
        // 根据角度映射到对应的Orientation
        // GradientDrawable.Orientation只支持8个方向
        if (angle >= 337.5 || angle < 22.5) {
            return GradientDrawable.Orientation.LEFT_RIGHT;  // 0度：从左到右
        } else if (angle >= 22.5 && angle < 67.5) {
            return GradientDrawable.Orientation.BL_TR;       // 45度：从左下到右上
        } else if (angle >= 67.5 && angle < 112.5) {
            return GradientDrawable.Orientation.BOTTOM_TOP;  // 90度：从下到上
        } else if (angle >= 112.5 && angle < 157.5) {
            return GradientDrawable.Orientation.BR_TL;       // 135度：从右下到左上
        } else if (angle >= 157.5 && angle < 202.5) {
            return GradientDrawable.Orientation.RIGHT_LEFT;  // 180度：从右到左
        } else if (angle >= 202.5 && angle < 247.5) {
            return GradientDrawable.Orientation.TR_BL;       // 225度：从右上到左下
        } else if (angle >= 247.5 && angle < 292.5) {
            return GradientDrawable.Orientation.TOP_BOTTOM;  // 270度：从上到下
        } else {
            return GradientDrawable.Orientation.TL_BR;       // 315度：从左上到右下
        }
    }

    /**
     * 为drawable设置圆角（可选功能）
     * 
     * @param drawable Drawable实例
     * @param cornerRadius 圆角半径
     */
    public static void setCornerRadius(Drawable drawable, float cornerRadius) {
        if (drawable == null || cornerRadius <= 0) {
            return;
        }
        
        if (drawable instanceof GradientDrawable) {
            ((GradientDrawable) drawable).setCornerRadius(cornerRadius);
        } else if (drawable instanceof LayerDrawable) {
            setLayerDrawableCornerRadius((LayerDrawable) drawable, cornerRadius);
        }
    }

    /**
     * 为LayerDrawable设置圆角
     * 遍历所有层并设置圆角
     * 
     * @param layerDrawable LayerDrawable实例
     * @param cornerRadius 圆角半径
     */
    private static void setLayerDrawableCornerRadius(LayerDrawable layerDrawable, float cornerRadius) {
        if (layerDrawable == null || cornerRadius <= 0) {
            return;
        }
        
        // 遍历所有层并设置圆角
        for (int i = 0; i < layerDrawable.getNumberOfLayers(); i++) {
            Drawable layer = layerDrawable.getDrawable(i);
            if (layer instanceof GradientDrawable) {
                ((GradientDrawable) layer).setCornerRadius(cornerRadius);
            }
        }
    }
} 