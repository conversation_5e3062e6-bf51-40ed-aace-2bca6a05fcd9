package com.fxiaoke.skin.background;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiParameterList;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fshttp.web.http.WebApiUtils;

import java.util.Date;

public class ThemeDataHelper {

    /**
     * 保存主题数据
     * @param themeId 主题ID，作为key
     * @param themeValue 主题数据，作为value
     * @param callback 回调
     */
    public static void saveThemeData(String themeId, String themeValue, WebApiExecutionCallback<String> callback) {
        WebApiParameterList params = WebApiParameterList.create()
                .with("key", themeId)
                .with("value", themeValue);
        
        // 创建带有自定义headers的回调包装器
        WebApiExecutionCallback<String> wrappedCallback = new WebApiExecutionCallback<String>() {
            @Override
            public TypeReference<WebApiResponse<String>> getTypeReference() {
                return new TypeReference<WebApiResponse<String>>() {};
            }

            @Override
            public Class<String> getTypeReferenceFHE() {
                return String.class;
            }

            @Override
            public void completed(Date time, String response) {
                if (callback != null) {
                    callback.completed(time, response);
                }
            }

            @Override
            public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                if (callback != null) {
                    callback.failed(failureType, httpStatusCode, error);
                }
            }

            @Override
            public void failed(WebApiFailureType failureType, int httpStatusCode, String error, int errorCode, int enterpriseID) {
                if (callback != null) {
                    callback.failed(failureType, httpStatusCode, error, errorCode, enterpriseID);
                }
            }
        };

        WebApiUtils.postAsync("userMenu", "saveUserMenuModel", params, wrappedCallback);
    }

    /**
     * 获取主题数据
     * @param themeId 主题ID，作为key
     * @param callback 回调
     */
    public static void getThemeData(String themeId, WebApiExecutionCallback<String> callback) {
        WebApiParameterList params = WebApiParameterList.create()
                .with("key", themeId);
        
        // 创建带有自定义headers的回调包装器
        WebApiExecutionCallback<String> wrappedCallback = new WebApiExecutionCallback<String>() {
            @Override
            public TypeReference<WebApiResponse<String>> getTypeReference() {
                return new TypeReference<WebApiResponse<String>>() {};
            }

            @Override
            public Class<String> getTypeReferenceFHE() {
                return String.class;
            }

            @Override
            public void completed(Date time, String response) {
                if (callback != null) {
                    callback.completed(time, response);
                }
            }

            @Override
            public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                if (callback != null) {
                    callback.failed(failureType, httpStatusCode, error);
                }
            }

            @Override
            public void failed(WebApiFailureType failureType, int httpStatusCode, String error, int errorCode, int enterpriseID) {
                if (callback != null) {
                    callback.failed(failureType, httpStatusCode, error, errorCode, enterpriseID);
                }
            }
        };

        WebApiUtils.postAsync("userMenu", "getUserMenuModel", params, wrappedCallback);
    }
} 