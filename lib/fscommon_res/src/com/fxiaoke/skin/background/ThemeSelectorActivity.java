package com.fxiaoke.skin.background;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Process;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.GridView;
import android.widget.TextView;
import android.widget.Toast;

import com.facishare.fs.dialogs.CommonDialog;
import com.facishare.fs.i18n.I18NHelper;
import com.fxiaoke.fscommon.brandcolor.BrandColorDataProvider;
import com.fxiaoke.fscommon_res.R;
import com.fxiaoke.fscommon_res.activity.FCBaseActivity;
import com.fxiaoke.fscommon_res.utils.BrandColorRenderUtils;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.fxiaoke.fxlog.FCLog;
import com.lidroid.xutils.util.AppInfoUtils;

import java.util.List;

import androidx.annotation.Nullable;

/**
 * 主题选择器Activity
 * 提供主题预览和选择功能
 */
public class ThemeSelectorActivity extends FCBaseActivity implements ThemePreviewAdapter.OnThemeSelectedListener {
    private static final String TAG = "ThemeSelectorActivity";
    
    private GridView mGridView;
    private ThemePreviewAdapter mAdapter;
    private TextView mBtnBack;
    private TextView mBtnSave;
    private TextView mBtnRestoreDefault;
    
    private ThemeManager mThemeManager;
    private SkinTheme mSelectedTheme;
    
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_theme_selector);
        initTitle();
        initViews();
        initThemeManager();
        setupGridView();
        loadThemes();
        
    }

    private void initTitle() {
        initTitleCommon();
        // Title初始化
        mCommonTitleView.setMiddleText(I18NHelper.getText("xt.setting_new_style_layout.text.theme")/* 设置 */);
        mCommonTitleView.addLeftBackAction(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }
    
    /**
     * 初始化视图
     */
    private void initViews() {
        mGridView = findViewById(R.id.gv_themes);
//        mBtnBack =(TextView) findViewById(R.id.btn_back);
        mBtnSave = findViewById(R.id.btn_save);
        mBtnRestoreDefault = findViewById(R.id.btn_restore_default);
        
        // 设置点击事件
//        mBtnBack.setOnClickListener(v -> onBackPressed());
        mBtnSave.setOnClickListener(v -> saveSelectedTheme());
        mBtnRestoreDefault.setOnClickListener(v -> restoreDefaultTheme());
    }
    
    /**
     * 初始化主题管理器
     */
    private void initThemeManager() {
        mThemeManager = ThemeManager.getInstance();
    }
    
    /**
     * 设置GridView
     */
    private void setupGridView() {
        mAdapter = new ThemePreviewAdapter(this);
        mAdapter.setOnThemeSelectedListener(this);
        mGridView.setAdapter(mAdapter);
        
        // 添加布局监听器，动态计算item尺寸
        mGridView.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (mAdapter.getCount() > 0) {
                    int width = mGridView.getWidth();
                    int padding = mGridView.getPaddingLeft() + mGridView.getPaddingRight();
                    int spacing = 8; // 对应xml中的horizontalSpacing
                    int availableWidth = width - padding - (spacing * 2); // 3列需要2个间距
                    int columnWidth = availableWidth / 3;
                    // 设置item高度，保持合适的宽高比
                    int itemHeight = (int) (columnWidth * 1.2f); // 手机预览需要更高的比例
                    mAdapter.setItemHeight(itemHeight);
                    
                }
            }
        });
    }
    
    /**
     * 加载主题数据
     */
    private void loadThemes() {
        try {
            List<SkinTheme> themes = mThemeManager.loadDefaultThemes();
            mAdapter.setThemes(themes);
            
            // 设置当前选中的主题
            String currentThemeId = mThemeManager.getCurrentThemeId();
            int selectedPosition = findThemePosition(themes, currentThemeId);
            if (selectedPosition >= 0) {
                mAdapter.setSelectedPosition(selectedPosition);
                mSelectedTheme = themes.get(selectedPosition);
            }
            
        } catch (Exception e) {
            FCLog.e(TAG, "load theme fail: " + e.getMessage());
        }
    }
    
    /**
     * 查找主题在列表中的位置
     */
    private int findThemePosition(List<SkinTheme> themes, String themeId) {
        if (themeId == null) return -1;
        
        for (int i = 0; i < themes.size(); i++) {
            if (themeId.equals(themes.get(i).getTitle())) {
                return i;
            }
        }
        return -1;
    }
    
    /**
     * 保存选中的主题
     */
    private void saveSelectedTheme() {
        if (mSelectedTheme == null) {
            showToast("please select a theme ");
            return;
        }
        
        try {
            boolean success = mThemeManager.ThemeSave(mSelectedTheme);
            if (success) {
                showRestartDialog();
            } else {
            }
            FCLog.d(TAG, "save theme: " + mSelectedTheme.getTitle() + ", result: " + success);
        } catch (Exception e) {
            FCLog.e(TAG, "save theme fail: " + e.getMessage());
        }



    }
    
    /**
     * 恢复默认主题
     */
    private void restoreDefaultTheme() {

        try {
            boolean success = mThemeManager.restoreDefaultTheme();
            showRestartDialog();
        } catch (Exception e) {
            FCLog.e(TAG, "恢复默认主题失败: " + e.getMessage());
            showToast("恢复默认主题失败");
        }
    }
    
    @Override
    public void onThemeSelected(SkinTheme theme, int position) {
        mSelectedTheme = theme;
        FCLog.d(TAG, "用户选择主题: " + theme.getTitle() + ", 位置: " + position);
    }
    
    /**
     * 显示Toast消息
     */
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public void onBackPressed() {
        setResult(RESULT_CANCELED);
        super.onBackPressed();
    }

    private void showRestartDialog() {
        Activity ac = this;
        String message = I18NHelper.getText("theme_apply_restart_app", "主题设置需重启APP生效，是否重启？");
        String okButtonTile = I18NHelper.getText("timezone.restartdialog.confirm_button", "重启");
        Runnable cancelClickCallback = new Runnable() {
            @Override
            public void run() {
            }
        };
        Runnable confirmClickCallback = new Runnable() {
            @Override
            public void run() {
                popAllActivity();
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        kill(ac);
                        android.os.Process.killProcess(android.os.Process.myPid());
                    }
                }, 300);
            }
        };
        showTwoButtonDialog(message, okButtonTile, cancelClickCallback, confirmClickCallback);
    }


    public static void kill(Context context){
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> processInfoList=manager.getRunningAppProcesses();
        for (ActivityManager.RunningAppProcessInfo info:processInfoList) {
            if(AppInfoUtils.getJsapiProcessName(context).equals(info.processName)){
                Process.killProcess(info.pid);
                break;
            }
        }
    }


    public  void popAllActivity() {
        if(WebApiUtils.isSwitchingAccounts()){
            return;
        }
        Intent intent = new Intent();
        intent.setPackage(this.getApplication().getPackageName());
        intent.setAction(FCBaseActivity.ACTION_EXIT);
        this.getApplication().sendBroadcast(intent);
    }

    private void showTwoButtonDialog(String message, String okButtonTitle, Runnable cancelClickCallback, Runnable confirmClickCallback) {
        final CommonDialog commonDialog=CommonDialog.createTwoButtonDialog(this,null);
        //时区业务涉及的弹框需要用户明确操作才可以关闭，故不允许弹框外侧的取消操作
        commonDialog.setCanceledOnTouchOutside(false);
        commonDialog.setBackCancelable(false);
        commonDialog.setCancelable(false);
        commonDialog.setShowType(CommonDialog.FLAG_TwoButton);
        commonDialog.setTitle(I18NHelper.getText("timezone.tipdialog.title", "提示"));
        commonDialog.setMessage(message);
        commonDialog.setOkButtonTitle(okButtonTitle);
        commonDialog.setPositiveButtonColor(Color.parseColor("#FF8000"));
        commonDialog.initTwoButtonDialogListenerTShow(new CommonDialog.myDiaLogListener() {
            @Override
            public void onClick(View view) {
                commonDialog.dismiss();
                if (view.getId() == com.fxiaoke.fscommon_res.R.id.button_mydialog_cancel) {
                    if(cancelClickCallback!=null){
                        cancelClickCallback.run();
                    }
                } else if (view.getId() == com.fxiaoke.fscommon_res.R.id.button_mydialog_enter) {
                    if(confirmClickCallback!=null){
                        confirmClickCallback.run();
                    }
                }
            }
        });
    }



} 