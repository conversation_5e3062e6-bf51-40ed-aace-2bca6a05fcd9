package com.fxiaoke.skin.background;

import android.app.Activity;
import android.app.Application;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.widget.FrameLayout;

import com.fxiaoke.fscommon.brandcolor.BrandColorDataProvider;
import com.fxiaoke.fxlog.FCLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 渐变背景管理器
 * 负责管理渐变背景主题和应用背景到Activity
 * 使用GradientDrawable方式实现
 * 从ThemeManager获取主题数据，不再直接管理SharedPreferences
 */
public class GradientBackgroundManager implements ThemeManager.OnThemeChangeListener {
    private static final String TAG = "newui_debug";

    private static GradientBackgroundManager instance;

    private GradientTheme mCurrentTheme;
    private Application mApplication;
    private ActivityBackgroundCallback mCallback;
    private Map<String, GradientTheme> mThemes;
    private Map<Activity, Drawable> mActivityBackgrounds;
    private boolean mInitialized = false;
    
    // 过滤器相关字段
    private Set<String> mFilteredClassNames;
    private boolean mFilterEnabled = true;
    
    private GradientBackgroundManager() {
        mThemes = new HashMap<>();
        mActivityBackgrounds = new HashMap<>();
        mFilteredClassNames = new HashSet<>();
        addFilteredClassName("com.fxiaoke.host.IndexActivity");
        addFilteredClassName("com.facishare.fs.account_system.LoginActivity");
        addFilteredClassName("com.facishare.fs.account_system.xlogin.NewLoginAct2");
        addFilteredClassName("com.facishare.fs.account_system.ShowNewGuideMapActivity");
        addFilteredClassName("com.facishare.fs.account_system.login.ui.login.EmailLoginActivity");
        addFilteredClassName("com.facishare.fs.account_system.find_pwd.FindPasswordActivity");
        initDefaultThemes();
    }

    /**
     * 获取单例实例
     */
    public static synchronized GradientBackgroundManager getInstance() {
        if (instance == null) {
            instance = new GradientBackgroundManager();
        }
        return instance;
    }

    /**
     * 初始化背景管理器
     * 
     * @param application Application实例
     */
    public void init(Application application) {
        if (application == null || mInitialized) {
            return;
        }
        
        mApplication = application;
        mCallback = new ActivityBackgroundCallback(this);
        mApplication.registerActivityLifecycleCallbacks(mCallback);
        
        // 初始化ThemeManager并注册监听器
        ThemeManager themeManager = ThemeManager.getInstance();
        themeManager.init(application);
        themeManager.addThemeChangeListener(this);
        
        // 从ThemeManager获取当前主题
        loadCurrentThemeFromManager();
        
        mInitialized = true;
        FCLog.i(TAG, "GradientBackgroundManager initialization completed");
    }
    
    /**
     * 从ThemeManager加载当前主题
     */
    private void loadCurrentThemeFromManager() {
        try {
            ThemeManager themeManager = ThemeManager.getInstance();
            
            // 检查是否有个人主题设置
            if (themeManager.hasPersonalTheme()) {
                SkinTheme currentSkinTheme = themeManager.getCurrentTheme();
                if (currentSkinTheme != null) {
                    GradientTheme gradientTheme = currentSkinTheme.toGradientTheme();
                    if (gradientTheme != null) {
                        // 确保主题已添加到本地缓存
                        addTheme(gradientTheme);
                        mCurrentTheme = gradientTheme;
                        FCLog.i(TAG, "从ThemeManager加载个人主题: " + currentSkinTheme.getTitle());
                    } else {
                        FCLog.w(TAG, "无法将SkinTheme转换为GradientTheme: " + currentSkinTheme.getTitle());
                        setDefaultTheme();
                    }
                } else {
                    FCLog.w(TAG, "ThemeManager返回的当前主题为null");
                    setDefaultTheme();
                }
            } else {
                // 检查是否有企业品牌色
                boolean hasSkin = BrandColorDataProvider.getInstance().hasBrandSkinColor(mApplication);
                if (hasSkin) {
                    mCurrentTheme = null;
                    FCLog.i(TAG, "使用企业品牌色，不应用个人主题");
                } else {
                    setDefaultTheme();
                }
            }
        } catch (Exception e) {
            FCLog.e(TAG, "从ThemeManager加载主题时发生异常: " + e.getMessage());
            setDefaultTheme();
        }
    }
    
    /**
     * 设置默认主题
     */
    private void setDefaultTheme() {
        mCurrentTheme = mThemes.get("default");
        FCLog.i(TAG, "使用默认主题: " + (mCurrentTheme != null ? mCurrentTheme.getThemeId() : "null"));
    }

    /**
     * 检查是否有个人主题
     * 
     * @return true表示有个人主题，false表示没有
     */
    public boolean hasPersonalTheme() {
        return ThemeManager.getInstance().hasPersonalTheme();
    }

    /**
     * 应用背景到Activity
     * 
     * @param activity 目标Activity
     */
    public void applyBackground(Activity activity) {
        if (activity == null || mCurrentTheme == null) {
            FCLog.e(TAG, "applyBackground: activity or theme is null");
            return;
        }

        if(activity.getClass().getSimpleName().contains("FsMPTransActivity")){
            return;
        }

        // 检查Activity是否被过滤
        String className = activity.getClass().getName();
        if (isClassNameFiltered(className)) {
            FCLog.d(TAG, "Activity was filtered, skip setting background: " + activity.getClass().getSimpleName());
            FrameLayout decorView = (FrameLayout) activity.getWindow().getDecorView();
            decorView.setBackgroundColor(0xffffffff);
            return;
        }
        
        try {
            FCLog.d(TAG, "apply Background  to Activity: " + activity.getClass().getSimpleName());

            //第一步：让commonTitleView 延伸到状态栏
            int startColor = mCurrentTheme.getStartColor();
            ImmersionUtil.updateCommonTitleViewTopPadding(activity);

            //第二步：设置透明沉浸式状态栏
            ImmersionUtil.setTransparentStatusBarWithTextColor(activity, startColor);
            
            // 创建渐变背景drawable
            Drawable backgroundDrawable = GradientDrawableHelper.createGradientDrawable(mCurrentTheme);
            if (backgroundDrawable == null) {
                FCLog.e(TAG, "Create gradient background failed");
                return;
            }

            //第三步： 获取decorView并设置背景
            FrameLayout decorView = (FrameLayout) activity.getWindow().getDecorView();
            decorView.setBackground(backgroundDrawable);
            
            // 保存背景drawable引用
            mActivityBackgrounds.put(activity, backgroundDrawable);
            FCLog.d(TAG, "apply Background success, activity: " + activity.getClass().getSimpleName());
            
        } catch (Exception e) {
            FCLog.e(TAG, "Exception occurred while setting background: " + e.getMessage());
        }
    }
    
    /**
     * 切换主题
     * 注意：此方法已弃用，请使用ThemeManager.saveAndApplyTheme()方法
     * 
     * @param themeId 主题ID
     * @return 是否切换成功
     */
    @Deprecated
    public boolean switchTheme(String themeId) {
        FCLog.w(TAG, "switchTheme方法已弃用，请使用ThemeManager.saveAndApplyTheme()");
        return switchThemeInternal(themeId, false);
    }
    
    /**
     * 内部主题切换方法
     * 
     * @param themeId 主题ID
     * @param skipSave 是否跳过保存（用于从ThemeManager监听器调用时）
     * @return 是否切换成功
     */
    private boolean switchThemeInternal(String themeId, boolean skipSave) {
        if (themeId == null || !mThemes.containsKey(themeId)) {
            FCLog.e(TAG, "switchThemeInternal: invalid theme id: " + themeId);
            return false;
        }
        
        FCLog.i(TAG, "Switch theme: " + themeId);
        mCurrentTheme = mThemes.get(themeId);
        
        // 更新所有已设置背景的Activity
        updateAllActivityBackgrounds();
        
        return true;
    }
    
    /**
     * 更新所有Activity的背景
     */
    private void updateAllActivityBackgrounds() {
        for (Map.Entry<Activity, Drawable> entry : mActivityBackgrounds.entrySet()) {
            Activity activity = entry.getKey();
            
            if (activity != null && !activity.isFinishing()) {
                FCLog.d(TAG, "Update activity background: " + activity.getClass().getSimpleName());
                // 重新应用背景
                applyBackground(activity);
            }
        }
    }
    
    /**
     * 获取当前主题
     */
    public GradientTheme getCurrentTheme() {
        return mCurrentTheme;
    }
    
    /**
     * 添加自定义主题
     * 
     * @param theme 主题
     * @return 是否添加成功
     */
    public boolean addTheme(GradientTheme theme) {
        if (theme == null || theme.getThemeId() == null) {
            FCLog.e(TAG, "addTheme: but theme id is null");
            return false;
        }
        
        FCLog.i(TAG, "Add theme: " + theme.getThemeId() + ", " + theme.getThemeName());
        mThemes.put(theme.getThemeId(), theme);
        return true;
    }
    
    /**
     * 获取所有可用主题
     */
    public List<GradientTheme> getAllThemes() {
        return new ArrayList<>(mThemes.values());
    }
    
    /**
     * 清除Activity背景
     */
    public void clearBackground(Activity activity) {
        if (activity == null) {
            return;
        }
        
        // 检查Activity是否被过滤
        String className = activity.getClass().getName();
        if (isClassNameFiltered(className)) {
            FCLog.d(TAG, "Activity was filtered, skip background clearing: " + activity.getClass().getSimpleName());
            return;
        }
        
        FCLog.d(TAG, "Clear activity background: " + activity.getClass().getSimpleName());
        
        // 移除背景drawable引用
        mActivityBackgrounds.remove(activity);
        
        try {
            // 重置decorView背景
            FrameLayout decorView = (FrameLayout) activity.getWindow().getDecorView();
            decorView.setBackground(null);
        } catch (Exception e) {
            FCLog.e(TAG, "Exception occurred while clearing background: " + e.getMessage());
        }
    }

    public void clearAllBackground(){
        for (Map.Entry<Activity, Drawable> entry : mActivityBackgrounds.entrySet()) {
            Activity activity = entry.getKey();

            if (activity != null && !activity.isFinishing()) {
                FCLog.d(TAG, "Clear activity background: " + activity.getClass().getSimpleName());
                // 清除背景
                clearBackground(activity);
            }
        }
    }
    
    /**
     * 添加需要过滤的Activity类名
     * 
     * @param className Activity的完整类名或简单类名
     */
    public void addFilteredClassName(String className) {
        if (className == null || className.trim().isEmpty()) {
            FCLog.w(TAG, "addFilteredClassName: class name is empty or invalid");
            return;
        }
        
        String trimmedClassName = className.trim();
        if (mFilteredClassNames.add(trimmedClassName)) {
            FCLog.i(TAG, "Add filtered class name: " + trimmedClassName);
        } else {
            FCLog.d(TAG, "Class name already exists in filter list: " + trimmedClassName);
        }
    }
    
    /**
     * 移除过滤的Activity类名
     * 
     * @param className Activity的完整类名或简单类名
     */
    public void removeFilteredClassName(String className) {
        if (className == null || className.trim().isEmpty()) {
            FCLog.w(TAG, "removeFilteredClassName: class name is empty or invalid");
            return;
        }
        
        String trimmedClassName = className.trim();
        if (mFilteredClassNames.remove(trimmedClassName)) {
            FCLog.i(TAG, "Remove filtered class name: " + trimmedClassName);
        } else {
            FCLog.d(TAG, "Class name does not exist in filter list: " + trimmedClassName);
        }
    }
    
    /**
     * 清空所有过滤的Activity类名
     */
    public void clearFilteredClassNames() {
        int count = mFilteredClassNames.size();
        mFilteredClassNames.clear();
        FCLog.i(TAG, "Clear filter class name list, removed " + count + " class names");
    }
    
    /**
     * 设置过滤功能是否启用
     * 
     * @param enabled true启用过滤，false禁用过滤
     */
    public void setFilterEnabled(boolean enabled) {
        mFilterEnabled = enabled;
        FCLog.i(TAG, "Filter function" + (enabled ? " enabled" : " disabled"));
    }
    
    /**
     * 获取过滤功能是否启用
     * 
     * @return true表示启用，false表示禁用
     */
    public boolean isFilterEnabled() {
        return mFilterEnabled;
    }
    
    /**
     * 获取所有过滤的Activity类名
     * 
     * @return 过滤类名集合的副本
     */
    public Set<String> getFilteredClassNames() {
        return new HashSet<>(mFilteredClassNames);
    }
    
    /**
     * 检查Activity类名是否被过滤
     * 
     * @param className Activity的类名
     * @return true表示被过滤，false表示不被过滤
     */
    private boolean isClassNameFiltered(String className) {
        if (!mFilterEnabled || className == null) {
            return false;
        }
        
        try {
            // 检查完整类名匹配
            if (mFilteredClassNames.contains(className)) {
                return true;
            }
            
            // 检查简单类名匹配
            String simpleClassName = className.substring(className.lastIndexOf('.') + 1);
            if (mFilteredClassNames.contains(simpleClassName)) {
                return true;
            }
            
            return false;
        } catch (Exception e) {
            FCLog.e(TAG, "Exception occurred while checking class name filter: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 初始化默认主题集合
     */
    private void initDefaultThemes() {
        FCLog.i(TAG, "Initialize default theme collection");
        
        // 使用 ThemeManager 加载主题数据，将"碧海金沙"设为默认主题
        try {
            ThemeManager themeManager = ThemeManager.getInstance();
            List<SkinTheme> skinThemes = themeManager.loadDefaultThemes();
            
            if (skinThemes != null && !skinThemes.isEmpty()) {
                FCLog.i(TAG, "Loaded " + skinThemes.size() + " themes from ThemeManager");
                
                int successCount = 0;
                boolean hasDefaultTheme = false;
                
                for (SkinTheme skinTheme : skinThemes) {
                    if (skinTheme != null) {
                        try {
                            GradientTheme gradientTheme = skinTheme.toGradientTheme();
                            if (gradientTheme != null) {
                                // 将"碧海金沙"设为默认主题，其他主题使用标题作为ID
                                String themeId;
                                if ("碧海金沙".equals(skinTheme.getTitle())) {
                                    themeId = "default";
                                    hasDefaultTheme = true;
                                    FCLog.i(TAG, "Set 'Blue Sea Golden Sand' as default theme");
                                } else {
                                    themeId = skinTheme.getTitle();
                                }
                                
                                gradientTheme.setThemeId(themeId);
                                gradientTheme.setThemeName(skinTheme.getTitle());
                                
                                mThemes.put(themeId, gradientTheme);
                                successCount++;
                                FCLog.d(TAG, "Successfully added theme: " + skinTheme.getTitle() + " (ID: " + themeId + ")");
                            } else {
                                FCLog.w(TAG, "Theme conversion failed: " + skinTheme.getTitle() + ", mainColor: " + skinTheme.getMainColor());
                            }
                        } catch (Exception e) {
                            FCLog.e(TAG, "Exception occurred while converting theme: " + skinTheme.getTitle() + ", error: " + e.getMessage());
                        }
                    }
                }
                
                FCLog.i(TAG, "Successfully added " + successCount + " themes from ThemeManager");
                
                // 如果"碧海金沙"主题未能正确设置为默认，使用备用主题
                if (!hasDefaultTheme) {
                    FCLog.w(TAG, "'Blue Sea Golden Sand' theme not found or conversion failed, using fallback default theme");
                }
            } else {
                FCLog.w(TAG, "ThemeManager returned empty theme list, using fallback themes");
            }
        } catch (Exception e) {
            FCLog.e(TAG, "Exception occurred while loading ThemeManager themes: " + e.getMessage());
            FCLog.w(TAG, "Using fallback themes");
        }
        
        FCLog.i(TAG, "Default theme initialization completed, total " + mThemes.size() + " themes");
    }
    
    /**
     * 添加默认备用主题（当"碧海金沙"主题转换失败时使用）
     */
    private void addDefaultFallbackTheme() {
        // 创建一个简单的蓝色渐变作为默认主题
        GradientTheme defaultTheme = GradientTheme.createLinear(
                "default", 
                "默认主题", 
                Color.parseColor("#2196F3"), 
                Color.parseColor("#64B5F6"), 
                90
        );
        mThemes.put("default", defaultTheme);
        FCLog.i(TAG, "Added default fallback theme");
    }
    

    

    @Override
    public void onThemeChanged(SkinTheme theme) {
        FCLog.i(TAG, "收到主题变更通知: " + (theme != null ? theme.getTitle() : "null"));
    }
    
    /**
     * ThemeManager监听器回调：主题已清除
     */
    @Override
    public void onThemeCleared() {
        FCLog.i(TAG, "收到主题清除通知");

    }

    /**
     * 清除主题ID
     * 注意：此方法已弃用，请使用ThemeManager.restoreDefaultTheme()方法
     */
    @Deprecated
    public void clearThemeId() {
        ThemeManager.getInstance().restoreDefaultTheme();
    }
} 