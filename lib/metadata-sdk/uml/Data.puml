@startuml

class MetaData {
    protected Map<String, Object> map
}

package Field<PERSON><PERSON>s {
    interface FieldKeys.Common

    interface FieldKeys.SingleChoiceKeys extends FieldKeys.Common

    interface FieldKeys.TextKeys extends FieldKeys.Common
}

class Field extends MetaData implements FieldKeys.Common {
    public String getApiName();
    public String getLabel();
}

class SingleChoiceField extends Field implements FieldKeys.SingleChoiceKeys

class TextField extends Field implements FieldKeys.TextKeys


package  FormFieldKeys {
    interface Common

    interface SingleChoiceKeys extends Common

    interface TextKeys extends Common
}

class FormField extends MetaData implements Common {
    public String getApiName();
    public String getLabel();
}

class SingleC<PERSON>iceFormField extends FormField implements SingleChoiceKeys

class TextFormField extends FormField implements TextKeys


@enduml