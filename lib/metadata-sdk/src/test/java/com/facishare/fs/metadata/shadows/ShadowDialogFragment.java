/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.shadows;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import com.afollestad.materialdialogs.DialogFragmentWrapper;

import android.app.Dialog;
import androidx.fragment.app.FragmentManager;

/**
 * <AUTHOR>
 *         <b>DATE</b> 2016/10/9.
 */
@Implements(DialogFragmentWrapper.FsDialogFragment.class)
public class ShadowDialogFragment {
    Dialog mDialog;
    @Implementation
    public void show(FragmentManager manager, String tag) {
        mDialog.show();
    }

    @Implementation
    public void setDialog(Dialog dialog) {
        mDialog = dialog;
    }
}
