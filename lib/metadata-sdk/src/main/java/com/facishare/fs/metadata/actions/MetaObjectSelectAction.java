package com.facishare.fs.metadata.actions;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.common_utils.function.BiConsumer;
import com.facishare.fs.common_utils.function.Consumer;
import com.facishare.fs.metadata.actions.basic.ActivityAction2;
import com.facishare.fs.metadata.beans.FilterScene;
import com.facishare.fs.metadata.beans.MetaData;
import com.facishare.fs.metadata.beans.MetaDataParser;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.config.MetaDataConfig;
import com.facishare.fs.metadata.list.select_obj.MetaSelectObjByH5Hook;
import com.facishare.fs.metadata.list.select_obj.ObjectSelectRouter;
import com.facishare.fs.metadata.list.select_obj.picker.MultiObjectPicker;
import com.facishare.fs.metadata.list.select_obj.picker.PickMode;
import com.facishare.fs.metadata.list.select_obj.picker.PickObjConfig;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.facishare.fs.metadata.web.MetaDataJsApiFragActivity;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.fscommon.avatar.IAvaOpenerCallback;
import com.fxiaoke.fscommon.util.FsUrlUtils;
import com.fxiaoke.fscommon_res.avatar.AvaOpener;
import com.fxiaoke.fxlog.FCLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 封装跳转选对象列表操作
 * 包含逻辑：
 * 1. 路由
 * Created by zhouz on 2020/9/29.
 */
public class MetaObjectSelectAction extends ActivityAction2<Void, MetaObjectSelectAction> {
    private String TAG = MetaObjectSelectAction.class.getSimpleName();
    private PickObjConfig mPickObjConfig;//原生需要的参数
    private BiConsumer<List<ObjectData>, Intent> mCallBack;
    private Consumer<JSONObject> mRawCallBack;
    private Map<String, Object> mJsonParams;//从 js 场景过来的透传参数


    public MetaObjectSelectAction(MultiContext context) {
        super(context);
    }

    public MetaObjectSelectAction setCallBack(
            BiConsumer<List<ObjectData>, Intent> callBack) {
        mCallBack = callBack;
        return this;
    }

    public MetaObjectSelectAction setRawCallBack(
            Consumer<JSONObject> callBack) {
        mRawCallBack = callBack;
        return this;
    }

    public MetaObjectSelectAction setPickObjConfig(
            PickObjConfig pickObjConfig) {
        mPickObjConfig = pickObjConfig;
        return this;
    }

    public MetaObjectSelectAction setJsonParams(
            Map<String, Object>  jsonParams) {
        mJsonParams = jsonParams;
        return this;
    }

    @Override
    public void start(Void target) {
        if (mPickObjConfig==null){
            return;
        }
        startNav();
    }

    private void startNav(){
        ObjectSelectRouter router = new ObjectSelectRouter(mPickObjConfig.getApiName());
        String sourceObjApiName = null;
        if (mPickObjConfig.getScene() ==PickObjConfig.SCENE_FORM && mPickObjConfig.getAssociatedObjectData()!=null){
            sourceObjApiName=mPickObjConfig.getAssociatedObjectData().getObjectDescribeApiName();
        }else if (mPickObjConfig.getScene()==PickObjConfig.SCENE_RELATED){
            sourceObjApiName=mPickObjConfig.getSourceData().getObjectDescribeApiName();
        }
        String sourceFieldName = mPickObjConfig.getLookupFieldName();
        String url = router.getMatchUrl(sourceObjApiName, sourceFieldName);
        FsUrlUtils.FsScheme scheme = FsUrlUtils.FsScheme.ofUri(url);
        if(scheme== FsUrlUtils.FsScheme.HTTP||scheme== FsUrlUtils.FsScheme.HTTPS){
            h5Nav(url);
        } else if (scheme == FsUrlUtils.FsScheme.AVA){
            avaNav(url);
        }else {
          defaultNav();
        }
    }

    private void avaNav(String url){
        AvaOpener.getInstance().openAvaPage(getContext(), url,new HashMap<>(getMapParams()),
                new IAvaOpenerCallback() {
                    @Override
                    public void onAvaCallback(JSONObject data) {
                        if (data==null){
                            FCLog.e(TAG, "nav2 ava SelectObject: result null");
                            return;
                        }
                        if (mRawCallBack != null){
                            mRawCallBack.accept(data);
                            return;
                        }
                        MetaData metaParams = new MetaData(data);
                        ArrayList<ObjectData> selected = (ArrayList<ObjectData>) metaParams.getMetaDataList("objectDataList",
                                ObjectData.class);
                        callBack(selected,new Intent());
                    }
                });
    }

    /**H5界面*/
    private void h5Nav(String url){
        String json = JSON.toJSONString(getMapParams());
        startActivityForResult(MetaDataJsApiFragActivity.getIntent(getContext(), url,
                json,MetaSelectObjByH5Hook.class), DEFAULT_REQUEST_CODE);
    }

    private Map<String, Object> getMapParams(){
        if (mJsonParams != null&&!mJsonParams.isEmpty()){
            return mJsonParams;
        }
        return  trans2Map(mPickObjConfig);
    }

    public Map<String, Object> trans2Map(PickObjConfig config){
        Map<String, Object> jo = new HashMap<>();
        jo.put("targetApiName", config.getApiName());
        String scenestr = "normal";
        if(config.getScene()==PickObjConfig.SCENE_FORM){
            scenestr="form";
        }else if(config.getScene()==PickObjConfig.SCENE_RELATED){
            scenestr="relate";
        }
        jo.put("scene", scenestr);
        jo.put("includeAssociated", config.isIncludeAssociated());
        jo.put("filters", config.getParams().getFilterInfos());
        jo.put("wheres", config.getParams().getWheres());
        jo.put("isSingle", config.getMode() == PickMode.SINGLE);
        jo.put("disableAdd", config.isDisableAdd());
        if(config.getBackFillInfos() != null&&config.getBackFillInfos().getBackFillInfoMap()!=null){
            jo.put("backFill", MetaDataUtils.backFill2jsMapForCcModify(config.getBackFillInfos()));
        }
        jo.put("lookupRelatedListName", config.getLookupRelatedListName());
        jo.put("lookupFieldName", config.getLookupFieldName());
        if(config.getSourceData()!=null){
            jo.put("sourceObjectData", config.getSourceData().getMap());
        }
        if(config.getAssociatedObjectData()!=null){
            jo.put("formObjectData", config.getAssociatedObjectData().getMap());
        }
        if(config.getMasterObjectData() != null){
            jo.put("masterObjectData", config.getMasterObjectData().getMap());
        }
        if(config.getInitDatas() != null){
            jo.put("selectedData", MetaDataParser.getMapList(config.getInitDatas()));
        }
        if(config.getDetailDatas() != null){
            jo.put("details", config.getDetailDatas());
        }
        if(config.getMaxCount() < Integer.MAX_VALUE){
            jo.put("maxCount", config.getMaxCount());
        }
        if(!TextUtils.isEmpty(config.getAbovePrompt())){
            jo.put("abovePrompt", config.getAbovePrompt());
        }
        if(config.getMinCount() > 0) {
            jo.put("minCount", config.getMinCount());
        }
        if(!TextUtils.isEmpty(config.getBelowPrompt())){
            jo.put("belowPrompt", config.getBelowPrompt());
        }
        if(config.getExtFilterScenes() != null && !config.getExtFilterScenes().isEmpty()){
            List<FilterScene> filterSceneList = config.getExtFilterScenes();
            List<Map<String, Object>> targetList = new ArrayList<>();
            for (FilterScene filterScene: filterSceneList) {
                Map<String, Object> targetScene = new HashMap<>();
                targetScene.put("api_name", filterScene.apiName);
                targetScene.put("label", filterScene.label);
                targetScene.put("is_default", filterScene.is_default);
                targetScene.put("is_hidden", filterScene.is_hidden);
                targetScene.put("extend_object_data", filterScene.getFillInFormData());
                targetScene.put("extend_filters", filterScene.filters);
                targetList.add(targetScene);
            }
            jo.put("extraScenes", targetList);
        }
        return jo;
    }

    /**默认为原生界面*/
    private void defaultNav(){
        startActivityForResult(MetaDataConfig.getOptions().getNavigator().getSelectIntent(getContext(),
                mPickObjConfig), DEFAULT_REQUEST_CODE);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(resultCode!= Activity.RESULT_OK){
            return;
        }
        if (requestCode == DEFAULT_REQUEST_CODE) {
            MultiObjectPicker picker = MultiObjectPicker.getPickerByIntent(data);
            if (picker != null) {
                List<ObjectData> selectedDataList = picker.getSelectedList();
                callBack(selectedDataList, data);
            }else {
                FCLog.i("MetaObjectSelectAction", "onActivityResult picker is null");
            }
        }
    }

    private void callBack(List<ObjectData> selectedDataList,@NonNull Intent data){
        if (mCallBack!=null){
            mCallBack.accept(selectedDataList, data);
        }
    }
}
