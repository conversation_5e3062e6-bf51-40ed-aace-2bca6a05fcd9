/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.list.modelviews.field.presenter;

import com.facishare.fs.metadata.beans.fields.FieldType;
import com.facishare.fs.metadata.list.modelviews.ListItemFieldArg;
import com.facishare.fs.metadata.modify.modelviews.field.presenter.QuoteMViewPresenter;

import androidx.annotation.Nullable;
import android.text.TextUtils;

/**
 * 引用
 * Created by zhouz on 2019-08-08.
 */
public class QuoteListFieldMVPst extends TextListFieldMViewPresenter {
    @Override
    public boolean accept(ListItemFieldArg listItemFieldArg) {
        return listItemFieldArg != null &&
                listItemFieldArg.field.getFieldType()== FieldType.QUOTE;
    }

    @Override
    public CharSequence getValueSpanString(@Nullable final ListItemFieldArg arg,
                                           CharSequence showStr) {
        if (arg == null){
            return null;
        }
        CharSequence quoteStr = QuoteMViewPresenter.getQuoteOptionStr(arg.objectData, arg.formField);
        if (!TextUtils.isEmpty(quoteStr)){
            return quoteStr;
        }else {
            return super.getValueSpanString(arg, showStr);
        }
    }
}
