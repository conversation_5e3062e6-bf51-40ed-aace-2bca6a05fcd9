/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.modify.modelviews.field;

import com.facishare.fs.metadata.R;
import com.facishare.fs.metadata.beans.MetaDataParser;
import com.facishare.fs.modelviews.MultiContext;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import android.view.View;
import android.widget.CompoundButton;

/**
 * 切换/开关 类型的ModelView，适用于两种状态的切换操作
 * <b>创建时间</b> by ruicb on 2016/10/26.
 */

public class SwitchMView extends AbsEditableItemMView {
    private final String KEY_SAVE_CUR_RESULT = "KEY_SAVE_CUR_RESULT";
    @Override
    protected int getLayoutId() {
        return R.layout.meta_item_model_switch;
    }

    /**
     * 选择框
     */
    private SwitchCompat mSwitch;

    public SwitchMView(MultiContext context) {
        super(context);
    }

    @Override
    protected View onCreateView(Context context) {
        View rootView = super.onCreateView(context);
        mSwitch =  rootView.findViewById(R.id.switch_model_checkBox);
        mSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                notifyOnValueChanged();
                if (!invokedSetChecked){
                    notifyOnManualChanged();
                }
            }
        });
        return rootView;
    }

    @Override
    public void setReadOnly(boolean readOnly) {
        super.setReadOnly(readOnly);
        if (mSwitch != null) {
            mSwitch.setEnabled(!readOnly);
        }
    }

    public SwitchCompat getSwitch() {
        return mSwitch;
    }

    private boolean invokedSetChecked=false;
    /**
     * 设置是否选中
     *
     * @param isChecked true：选中
     */
    public void setChecked(boolean isChecked) {
        invokedSetChecked=true;
        mSwitch.setChecked(isChecked);
        invokedSetChecked=false;
    }

    @Override
    public void updateContent(Object object) {
        setChecked(MetaDataParser.parseBoolean(object));
    }

    /**
     * 获取选中状态
     *
     * @return true：选中
     */
    public boolean isChecked() {
        return mSwitch.isChecked();
    }

    @Override
    public boolean isEmpty() {
        return false;
    }

    @Override
    public Object getResult() {
        return isChecked();
    }

    @Override
    public Bundle assembleInstanceState() {
        Bundle data = super.assembleInstanceState();
        data.putBoolean(KEY_SAVE_CUR_RESULT, isChecked());
        return data;
    }

    @Override
    public void restoreInstanceState(@Nullable Bundle savedInstanceState) {
        super.restoreInstanceState(savedInstanceState);
        if (savedInstanceState != null) {
            setChecked(savedInstanceState.getBoolean(KEY_SAVE_CUR_RESULT));
        }
    }
}
