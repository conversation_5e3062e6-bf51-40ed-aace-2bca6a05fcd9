/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.dataconverter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.facishare.fs.bpm.data.source.RequestCallBack;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.ObjectDescribe;
import com.facishare.fs.metadata.beans.fields.Field;
import com.facishare.fs.metadata.beans.fields.FieldKeys;
import com.facishare.fs.metadata.beans.fields.FieldType;
import com.facishare.fs.metadata.beans.fields.group.AreaGroupField;
import com.facishare.fs.metadata.beans.fields.group.GroupFieldKeys;
import com.facishare.fs.metadata.beans.formfields.FormField;
import com.facishare.fs.metadata.dataconverter.converter.AreaDataConvertRunner;
import com.facishare.fs.metadata.dataconverter.converter.ConverterContext;
import com.facishare.fs.metadata.dataconverter.converter.IFieldContentConverter;
import com.facishare.fs.metadata.dataconverter.converter.IObjectDataFieldContext;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import io.reactivex.Observable;
import io.reactivex.Single;
import io.reactivex.SingleEmitter;
import io.reactivex.SingleObserver;
import io.reactivex.SingleOnSubscribe;
import io.reactivex.SingleSource;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;

/**
 * 一个字段的value转换为显示值的帮助类，对象开销比较大，使用的地方最好只实例化一个对象
 *
 * Created by zhouz on 2019/1/9.
 */
public class FieldConvertHelper {
    private IDataConverterFactory<IFieldContentConverter, FormField> converterFactory = new DefaultContentDataConverterFac();
    public FieldConvertHelper() {
    }

    /**批量转换数据为显示文本*/
    public void batchConvert(List<String> fieldApiNames, ObjectData data,
                             ObjectDescribe describe, int scene,
                             RequestCallBack.DataCallBack<Map<String, String>> callBack){
        batchConvert(fieldApiNames, data, describe, scene)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SingleObserver<Map<String, String>>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onSuccess(Map<String, String> contentMap) {
                        callBack.onDataLoaded(contentMap);
                    }

                    @Override
                    public void onError(Throwable e) {
                        callBack.onDataNotAvailable(e.getMessage());
                    }
                });
    }

    /**批量转换数据为显示文本 rxjava实现*/
    public Single<Map<String, String>> batchConvert(List<String> fieldApiNames, ObjectData data,
                                       ObjectDescribe describe, int scene){
        if (fieldApiNames==null||fieldApiNames.isEmpty()||data==null||data.getMap().isEmpty()){
            return Single.just(new HashMap<>());
        }
        return Observable.fromIterable(fieldApiNames)
                .flatMapSingle(new Function<String, SingleSource<Map<String, String>>>() {
                    @Override
                    public SingleSource<Map<String, String>> apply(String fieldName) throws Exception {
                        Field field = null;
                        if (describe != null && describe.getFieldMaps() != null && describe.getFieldMaps().get(fieldName)!=null){
                            field = new Field(describe.getFieldMaps().get(fieldName));
                        }
                        return convertOneFieldValue(data.get(fieldName), field, data, scene)
                                .map(
                                new Function<String, Map<String, String>>() {
                                    @Override
                                    public Map<String, String> apply(String s) throws Exception {
                                        Map<String, String> rst = new HashMap<>();
                                        rst.put(fieldName, s);
                                        return rst;
                                    }
                                });
                    }
                })
                .toList()
                .map(new Function<List<Map<String, String>>, Map<String, String>>() {
                    @Override
                    public Map<String, String> apply(List<Map<String, String>> maps)
                            throws Exception {
                        Map<String, String> rst = new HashMap<>();
                        for (Map<String, String> item : maps){
                            rst.putAll(item);
                        }
                        return rst;
                    }
                })
                .subscribeOn(Schedulers.computation());

    }

    /**
     * 一个字段的value转换为显示值（what组件字段暂不支持，图片，附件等非文本展示的类型不支持）
     *
     * @param value 字段值
     * @param field 字段描述
     * @param wholeObjectData 完整对象数据
     * @param dataConvertView 使用转换结果的实例（如果是listview，应该传view的引用，）
     */
    public void convertSingleFieldValue(@Nullable Object value, @NonNull Field field, @Nullable ObjectData
            wholeObjectData, @NonNull IDataConvertView dataConvertView) {
        if (value == null){
            dataConvertView.onDataConverted(null, null);
            return;
        }
        IDataConvertRunner runner = SyncDataConvertRunner.getInstance();
        FieldType fieldType = field.getFieldType();
        if (fieldType == FieldType.COUNTRY || fieldType == FieldType.PROVINCE || fieldType == FieldType.CITY ||
                fieldType == FieldType.DISTRICT){
            runner = AreaDataConvertRunner.getInstance();
        }
        final Field targetField = createFieldForConvert(field);
        IFieldContentConverter converter = converterFactory.createConverter(targetField.to(FormField.class));
        if (wholeObjectData == null) {
            wholeObjectData = new ObjectData();
            wholeObjectData.put(field.getApiName(), value);
        }
        final ObjectData finalWholeObjectData = wholeObjectData;
        runner.execute(dataConvertView, converter, value, new IObjectDataFieldContext() {
            @Override
            public ObjectData getObjectData() {
                return finalWholeObjectData;
            }

            @Override
            public Field getField() {
                return targetField;
            }
        });
    }

    private Single<String> convertOneFieldValue(@Nullable Object value, Field field,
                                                @Nullable final ObjectData
             wholeObjectData, int scene){
        return Single.create(new SingleOnSubscribe<String>() {
            @Override
            public void subscribe(SingleEmitter<String> emitter) throws Exception {
                if (value==null){
                    emitter.onSuccess("");
                    return;
                }
                if (field==null){
                    emitter.onSuccess(value.toString());
                    return;
                }
                final Field targetField = createFieldForConvert(field);
                IFieldContentConverter converter = converterFactory.createConverter(targetField.to(FormField.class));
                ObjectData objectData = wholeObjectData;
                if (objectData == null) {
                    objectData = new ObjectData();
                    objectData.put(field.getApiName(), value);
                }
                ObjectData finalObjectData = objectData;
                String rst = converter.convert(value, new ConverterContext() {
                    @Override
                    public ObjectData getObjectData() {
                        return finalObjectData;
                    }

                    @Override
                    public Field getField() {
                        return targetField;
                    }

                    @Override
                    public int getScene() {
                        return scene;
                    }
                });
                emitter.onSuccess(rst);
            }
        });
    }

    /**国家省市区字段特殊转为组件字段*/
    public static Field createFieldForConvert(@NonNull Field targetField) {
        FieldType targetFieldType = targetField.to(FormField.class).getFieldType();
        if (FieldType.COUNTRY == targetFieldType) {
            targetField = createAreaGroupField(GroupFieldKeys.Area.AREA_COUNTRY, targetField);
        }else if (FieldType.PROVINCE == targetFieldType){
            targetField = createAreaGroupField(GroupFieldKeys.Area.AREA_PROVINCE, targetField);
        }else if (FieldType.CITY == targetFieldType){
            targetField = createAreaGroupField(GroupFieldKeys.Area.AREA_CITY, targetField);
        }else if (FieldType.DISTRICT == targetFieldType){
            targetField = createAreaGroupField(GroupFieldKeys.Area.AREA_DISTRICT, targetField);
        }
        return targetField;
    }


    private static AreaGroupField createAreaGroupField(String areaFieldKey, Field areaField) {
        AreaGroupField areaGroupField = new AreaGroupField(new HashMap<String, Object>());
        areaGroupField.put(FieldKeys.Common.TYPE, FieldType.GROUP.key);
        areaGroupField.put(GroupFieldKeys.Common.GROUP_TYPE, GroupFieldKeys.Type.AREA);
        Map<String, String> childFieldKeyMap = new HashMap<>();
        childFieldKeyMap.put(areaFieldKey, areaField.getApiName());
        areaGroupField.getMap().put(GroupFieldKeys.Common.FIELDS, childFieldKeyMap);
        Map<String, Field> childFieldMap = new HashMap<>();
        childFieldMap.put(areaField.getApiName(), areaField);
        areaGroupField.setupMergedFieldMap(childFieldMap);
        return areaGroupField;
    }
}
