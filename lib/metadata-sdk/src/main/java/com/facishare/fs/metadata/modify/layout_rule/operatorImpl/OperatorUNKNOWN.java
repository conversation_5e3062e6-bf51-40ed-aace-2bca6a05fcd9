package com.facishare.fs.metadata.modify.layout_rule.operatorImpl;

import java.util.List;

import com.facishare.fs.metadata.beans.fields.Field;
import com.facishare.fs.metadata.modify.layout_rule.IOperatorExecutor;

/**
 * Created by zhouz on 2018/3/27.
 */

public class OperatorUNKNOWN implements IOperatorExecutor {
    @Override
    public boolean matchFilter(Field field, List filterValues, Object actualValue) {
        return false;
    }
}
