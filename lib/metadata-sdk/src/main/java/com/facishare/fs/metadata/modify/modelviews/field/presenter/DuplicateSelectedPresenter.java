package com.facishare.fs.metadata.modify.modelviews.field.presenter;

import com.facishare.fs.metadata.beans.FormFieldViewArg;
import com.facishare.fs.metadata.beans.fields.FieldType;
import com.facishare.fs.metadata.modify.modelviews.field.DuplicateSelectMView;
import com.facishare.fs.metadata.modify.modelviews.field.TextMView;
import com.facishare.fs.modelviews.ModelView;
import com.facishare.fs.modelviews.MultiContext;

/**
 * Created by weilh on 2017/4/7.
 */

public class DuplicateSelectedPresenter extends BaseFieldMViewPresenter{

    @Override
    public boolean accept(FormFieldViewArg arg) {
        return arg != null && arg.formField != null && arg.formField.getFieldType() == FieldType.DUPLICATE;
    }

    @Override
    protected ModelView createShowView(MultiContext context, FormFieldViewArg formFieldViewArg) {
        return new TextMView(context);
    }

    @Override
    protected ModelView createEditView(MultiContext context, FormFieldViewArg formFieldViewArg) {
        return new DuplicateSelectMView(context);
    }
}
