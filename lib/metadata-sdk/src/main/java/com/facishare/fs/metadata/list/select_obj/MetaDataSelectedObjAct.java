/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.list.select_obj;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentTransaction;
import android.view.View;
import android.widget.TextView;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.BaseActivity;
import com.facishare.fs.metadata.R;
import com.facishare.fs.metadata.list.beans.ListItemArg;
import com.fxiaoke.fscommon.util.CommonDataContainer;

import java.util.List;

/**
 * Author:  wangrz
 * Date:    2020/6/11 14:44
 * Remarks: 通用选对象已选页面
 */
public class MetaDataSelectedObjAct extends BaseActivity {
    //已选列表中数据集合
    public static final String KEY_LIST_ITEM_SELECTED = "list_item_selected";
    //已选列表中移除的数据集合
    public static final String KEY_LIST_ITEM_ARGS_REMOVED_FROM_SELECTED_LIST = "key_list_item_args_removed_from_selected_list";
    public static final String KEY_API_NAME = "apiName";
    public static final String KEY_OBJECT_DISPLAY_NAME = "objectDisplayName";
    private TextView mSelectAllBtn;
    private MetaDataSelectedObjFrag mFragment;
    private String mApiName;
    private String mObjectDisplayName;
    public static Intent getIntent(Context context, String apiName, String objectDisplayName, List<ListItemArg> args) {
        Intent intent = new Intent(context, MetaDataSelectedObjAct.class);
        intent.putExtra(KEY_API_NAME, apiName);
        intent.putExtra(KEY_OBJECT_DISPLAY_NAME, objectDisplayName);
        CommonDataContainer.getInstance().saveData(KEY_LIST_ITEM_SELECTED, args);
        return intent;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_selected_object);
        initData(savedInstanceState);
        initTitleEx();
        loadFragment();
    }

    @Override
    public void onSafeSaveInstanceState(@NonNull Bundle outState) {
        super.onSafeSaveInstanceState(outState);
        outState.putString(KEY_OBJECT_DISPLAY_NAME, mObjectDisplayName);
        outState.putString(KEY_API_NAME, mApiName);
    }

    protected void initData(Bundle arg0) {
        if (arg0 == null) {
            Intent intent = getIntent();
            if (intent != null) {
                mApiName = intent.getStringExtra(KEY_API_NAME);
                mObjectDisplayName = intent.getStringExtra(KEY_OBJECT_DISPLAY_NAME);
            }
        } else {
            mApiName = arg0.getString(KEY_OBJECT_DISPLAY_NAME);
            mObjectDisplayName = arg0.getString(KEY_OBJECT_DISPLAY_NAME);
        }
    }

    @Override
    protected void initTitleEx() {
        super.initTitleEx();
        mCommonTitleView.setTitle( I18NHelper.getFormatText("metadata.selectObj.selectedObjTitle", mObjectDisplayName)/* 已选{0} */);
        mCommonTitleView.addLeftBackAction(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        mSelectAllBtn = mCommonTitleView
                .addRightAction(I18NHelper.getText("th.base.view.select_all")/* 全选 */, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mFragment.selectedAll();
                        String text = mFragment.isSelectedAll()
                                ? I18NHelper.getText("crm.selectobjecttype.SelectedObjTypeAct.875")/* 反选 */
                                : I18NHelper.getText("th.base.view.select_all")/* 全选 */;
                        mSelectAllBtn.setText(text);
                    }
                });
    }

    private void loadFragment() {
        FragmentTransaction transaction1 = getSupportFragmentManager().beginTransaction();
        mFragment = MetaDataSelectedObjFrag.newInstance(mApiName);
        transaction1.replace(R.id.frag_container, mFragment).commitAllowingStateLoss();
        mFragment.setSelectedListChangeListener(new MetaDataSelectedObjFrag.OnSelectedListChangeListener() {
            @Override
            public void onCheckListChanged(boolean isSelectedAll) {
                String text = isSelectedAll
                        ? I18NHelper.getText("crm.selectobjecttype.SelectedObjTypeAct.875")/* 反选 */
                        : I18NHelper.getText("th.base.view.select_all")/* 全选 */;
                mSelectAllBtn.setText(text);
            }
        });
        FragmentTransaction transaction2 = getSupportFragmentManager().beginTransaction();
        MetaDataSelectedObjBarFrag barFragment = MetaDataSelectedObjBarFrag.newInstance();
        transaction2.replace(R.id.bottom_fragment, barFragment).commitAllowingStateLoss();
        barFragment.setRemoveListener(new MetaDataSelectedObjBarFrag.IRemoveListener() {
            @Override
            public void onRemoveClick() {
                if (mFragment != null) {
                    mFragment.remove();
                }
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        CommonDataContainer.getInstance().removeSavedData(KEY_LIST_ITEM_SELECTED);
    }

    @Override
    public void onBackPressed() {
        List<ListItemArg> removedDataList = mFragment.getRemovedDataList();
        CommonDataContainer.getInstance().saveData(KEY_LIST_ITEM_ARGS_REMOVED_FROM_SELECTED_LIST, removedDataList);
        setResult(RESULT_OK);
        finish();
    }
}
