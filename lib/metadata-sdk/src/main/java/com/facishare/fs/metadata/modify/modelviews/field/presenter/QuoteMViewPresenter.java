package com.facishare.fs.metadata.modify.modelviews.field.presenter;

import java.util.Arrays;

import com.facishare.fs.metadata.beans.FormFieldViewArg;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.fields.FieldKeys;
import com.facishare.fs.metadata.beans.fields.FieldType;
import com.facishare.fs.metadata.beans.fields.QuoteField;
import com.facishare.fs.metadata.beans.formfields.FormField;
import com.facishare.fs.metadata.beans.formfields.FormFieldKeys;
import com.facishare.fs.metadata.modify.modelviews.field.QuoteMView;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.facishare.fs.modelviews.ModelView;
import com.facishare.fs.modelviews.MultiContext;

import android.text.SpannableStringBuilder;
import android.text.TextUtils;

/**
 * 引用字段的Presenter
 * Created by weilh on 2017/7/11.
 */

public class QuoteMViewPresenter extends BaseFieldMViewPresenter{
    FormFieldMPresenterCtrl mMViewCtrl;

    public QuoteMViewPresenter(
            FormFieldMPresenterCtrl MViewCtrl) {
        mMViewCtrl = MViewCtrl;
    }

    @Override
    public boolean accept(FormFieldViewArg formFieldViewArg) {
        boolean accept = formFieldViewArg != null && formFieldViewArg.formField.getFieldType() == FieldType.QUOTE;
        if (accept){
            QuoteField countFormField = formFieldViewArg.formField.to(QuoteField.class);
            String returnType = countFormField.getQuoteFieldType();
            if (TextUtils.isEmpty(returnType) || TextUtils.equals(FieldType.QUOTE.key, returnType)) {
                accept = false;
            }
        }
        return  accept;
    }

    public static FormFieldViewArg createReturnTypeArg(FormFieldViewArg srcArg) {
        QuoteField quoteField = srcArg.formField.to(QuoteField.class);
        String returnType = quoteField.getQuoteFieldType();
        //地址和图片走对应组件，别的都用 text
        if (!FieldType.ADDRESS.key.equals(quoteField.getQuoteFieldType())
                && !FieldType.IMAGE.key.equals(quoteField.getQuoteFieldType())){
            returnType = FieldType.LONG_TEXT.key;
        }

        Object value = srcArg.value;
        CharSequence quoteStr = getQuoteOptionStr(srcArg.objectData, srcArg.formField);
        if (!TextUtils.isEmpty(quoteStr)){
            value = quoteStr;
        }

        FormField formField = new FormField();
        formField.putAll(quoteField.getMap());
        formField.put(FormFieldKeys.Common.RENDER_TYPE, returnType);
        formField.put(FieldKeys.Common.TYPE, returnType);
        return new FormFieldViewArg(srcArg.objectDescribe, null, formField, value,
                srcArg.component, srcArg.mScene);
    }

    BaseFieldMViewPresenter createMViewPst(FormFieldViewArg arg){
        mMViewCtrl.getConfig().cache(false);
        BaseFieldMViewPresenter presenter =
                (BaseFieldMViewPresenter) mMViewCtrl.getModelViewPresenter(arg);
        mMViewCtrl.getConfig().cache(true);
        return presenter;
    }

    @Override
    protected ModelView createShowView(MultiContext context, FormFieldViewArg formFieldViewArg) {
        FormFieldViewArg returnTypeArg = createReturnTypeArg(formFieldViewArg);
        return createMViewPst(returnTypeArg).createShowView(context, returnTypeArg);
    }

    @Override
    protected void updateShowView(ModelView showModelView, FormFieldViewArg formFieldViewArg) {
        FormFieldViewArg returnTypeArg = createReturnTypeArg(formFieldViewArg);
        showModelView.setArg(returnTypeArg);
        createMViewPst(returnTypeArg).updateShowView(showModelView, returnTypeArg);
    }

    @Override
    protected ModelView createEditView(MultiContext context, FormFieldViewArg formFieldViewArg) {
        QuoteMView modelView = new QuoteMView(context);
        modelView.setArg(formFieldViewArg);
        modelView.init();
        modelView.setSingleLine(false);
        return modelView;
    }

    public static CharSequence getQuoteOptionStr(ObjectData objectData, FormField formField){
        if (objectData == null){
            return null;
        }
        String quoteType = formField.to(QuoteField.class).getQuoteFieldType();
        if (FieldType.getFieldType(quoteType) == FieldType.SELECT_MANY ||
                FieldType.getFieldType(quoteType) == FieldType.RECORD_TYPE ||
                FieldType.getFieldType(quoteType) == FieldType.SELECT_ONE){
            //引用字段特殊显示，放在__r里
            String quoteStr =
                    objectData.getString(formField.getApiName()+MetaDataUtils.EXT__R);
            if (!TextUtils.isEmpty(quoteStr)){
                SpannableStringBuilder ssb = new SpannableStringBuilder();
                int i=0;
                char[] temp = new char[3];
                char[] chars = quoteStr.toCharArray();
                for (char c :chars){
                    temp[i++]=c;
                    if (i==3){
                        boolean isDeleteStr = temp[0]=='#' && temp[1]=='%' && temp[2]=='$';
                        if (isDeleteStr){
                            ssb.append(MetaDataUtils.getDeleteOptionShowStr());
                            i=0;
                        }else {
                            ssb.append(temp[0]);
                            temp[0]=temp[1];
                            temp[1]=temp[2];
                            i=2;
                        }
                    }
                }
                if (i != 0){
                    ssb.append(String.valueOf(Arrays.copyOf(temp, i)));
                }
                return ssb;
            }
        }
        return null;
    }
}
