/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.list.beans;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import android.text.TextUtils;

/**
 * 商机2.0阶段列表实体
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2018/8/13
 */
public class StageResult implements Serializable {

    private List<SimpleStage> stages;
    private boolean probabilityViewOnInstance;//是否展示赢率

    public List<SimpleStage> getStages() {
        return stages;
    }

    public void setStages(List<SimpleStage> stages) {
        this.stages = stages;
    }

    public boolean isProbabilityViewOnInstance() {
        return probabilityViewOnInstance;
    }

    public void setProbabilityViewOnInstance(boolean probabilityViewOnInstance) {
        this.probabilityViewOnInstance = probabilityViewOnInstance;
    }

    public static class SimpleStage implements IFlowStageInfo {
        private String name;//阶段选项的名称
        private String description;//推进器在定义时，此阶段的注释
        private String stageId;//对象阶段字段的选项值id
        private int orderId;//推进器中此阶段的顺序
        private Object extension;//阶段的扩展信息

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getStageId() {
            return stageId;
        }

        public void setStageId(String stageId) {
            this.stageId = stageId;
        }

        public int getOrderId() {
            return orderId;
        }

        public void setOrderId(int orderId) {
            this.orderId = orderId;
        }

        public Object getExtension() {
            return extension;
        }

        public void setExtension(Object extension) {
            this.extension = extension;
        }

        public FlowStageStatusEnum getSalesStageStatus() {
            if (extension != null && extension instanceof Map) {
                Object o = ((Map) extension).get("status");
                String s = String.valueOf(o);
                if (TextUtils.isDigitsOnly(s)) {
                    return FlowStageStatusEnum.getFlowStageStatusByKey(Integer.parseInt(s));
                }
            }
            return FlowStageStatusEnum.UNKNOWN;
        }

        public String getSalesStatusName() {
            if (extension != null && extension instanceof Map) {
                Object o = ((Map) extension).get("statusName");
                return String.valueOf(o);
            }
            return "";
        }

        public String getStatusName() {
            return getName();
        }

        public String getWinRate() {
            if (extension != null && extension instanceof Map) {
                Object o = ((Map) extension).get("probability");
                return String.valueOf(o);
            }
            return "";
        }

    }
}
