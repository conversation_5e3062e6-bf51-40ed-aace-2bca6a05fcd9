package com.facishare.fs.metadata.modify.modelviews.componts;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.R;
import com.facishare.fs.metadata.actions.MetaAddTeamMemberAction;
import com.facishare.fs.metadata.actions.OperationItem;
import com.facishare.fs.metadata.actions.TeamMemberAddContext;
import com.facishare.fs.metadata.beans.MetaTeamMemberInfo;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.ObjectDescribe;
import com.facishare.fs.metadata.beans.TeamMemberInfo;
import com.facishare.fs.metadata.beans.components.ComponentKeys;
import com.facishare.fs.metadata.beans.components.UserListComponent;
import com.facishare.fs.metadata.beans.fields.EmbeddedObjectListField;
import com.facishare.fs.metadata.beans.formfields.FormField;
import com.facishare.fs.metadata.detail.relatedteam.TeamMemberActivity;
import com.facishare.fs.metadata.detail.relatedteam.TeamMemberUtils;
import com.facishare.fs.metadata.modify.modelviews.componts.beans.ComponentViewArg;
import com.facishare.fs.metadata.tick.MetaDataBizOpID;
import com.facishare.fs.metadata.tick.MetaDataBizTick;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.facishare.fs.modelviews.MultiContext;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.View;

/**
 * 相关团队的卡片
 *
 * <AUTHOR>
 *         <b>DATE</b> 2016/12/13.
 */

public class UserListMViewGroup extends BaseCardComMView implements TeamMemberAddContext{
    private UserListComponent mComponent;
    private ObjectData sourceData;
    private List<ObjectData> userDatas;
    private boolean mEditable;
    private final String KEY_SAVE_ACTION_DATA = "Key_Save_Action_Data";
    private MetaAddTeamMemberAction addTeamMemberAction;

    public UserListMViewGroup(MultiContext context) {
        super(context);
        init();
        addTeamMemberAction = new MetaAddTeamMemberAction(context);
    }


    public void updateView(ComponentViewArg arg) {
        UserListComponent component = arg.component.to(UserListComponent.class);
        List<FormField> formFieldList = component.getIncludeFields();
        if (formFieldList == null || formFieldList.isEmpty()) return;
        String fieldApiName = formFieldList.get(0).getFieldName();
        EmbeddedObjectListField embeddedObjectListField = arg.objectDescribe.getFields().get(fieldApiName).to
                (EmbeddedObjectListField.class);
        List<ObjectData> dataList = arg.objectData.getMetaDataList(fieldApiName, ObjectData.class);
        //最多只显示2条
        int size = 0;
        if (dataList != null && !dataList.isEmpty()) {
            size = dataList.size() >= 2 ? 2 : dataList.size();
        }
        List<ObjectData> showList = new ArrayList<>(size);
        for (int i =0 ; i < size; i++) {
            showList.add(dataList.get(i));
        }
        updateView(arg.component.to(UserListComponent.class),
                embeddedObjectListField, showList, arg.objectData, dataList == null ? 0 : dataList.size(), arg.objectDescribe);
    }


    public void updateView(UserListComponent component, EmbeddedObjectListField field, List<ObjectData> dataList,
                           final ObjectData sourceData, int total, ObjectDescribe objectDescribe) {
        this.mComponent = component;
        this.sourceData = sourceData;
        this.userDatas = dataList;
        mEditable = editable();
        removeAllActions();
        if (mEditable) {
            addRightAction(R.drawable.metadata_obj_add, new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                   onAddClick(sourceData);
                }
            });
        }
        setTitle(mComponent.getHeader());
        updateObjs(dataList, field, objectDescribe);
        updateTotalCount(I18NHelper.getFormatText("crm.bizevent.BizAction.v1.1680"/*查看全部{0}*/, component.getHeader()), total);
    }

    private void onAddClick(ObjectData sourceData) {
        if (sourceData == null) return;
        addTeamMemberAction.start(this);
    }

    public void updateObjs(List<ObjectData> dataList, EmbeddedObjectListField field, ObjectDescribe objectDescribe) {
        removeAllModelViews();
        if (dataList == null || dataList.isEmpty() || field == null) return;

        for (int i = 0; i < dataList.size(); i++) {
            ObjectData objectData = dataList.get(i);
            addObjView(objectData, field, objectDescribe);
            if (i != dataList.size() - 1) {
                getViewContainer().addView(getDividerView(64));
            }
        }
    }

    private void addObjView(ObjectData objectData, EmbeddedObjectListField field, ObjectDescribe objectDescribe) {
        UserObjectMView userObjectMView = new UserObjectMView(getMultiContext());
        userObjectMView.update(field.getEmbeddedFields(), mComponent.getIncludeFields(),objectData, objectDescribe);
        addModelView(userObjectMView);
    }

    private boolean editable() {
        if (mComponent == null) return false;
        return MetaDataUtils.hasButton(mComponent.getButtons(), OperationItem.ACTION_ADD_RELATED_MEMBER);
    }

    @Override
    protected void onTotalLayoutClick() {
        super.onTotalLayoutClick();
        if (sourceData == null) return;
        if (ComponentKeys.Constants.RELATED_TEAM_API_NAME.equals(mComponent.getApiName())) {
            String objApiName = sourceData.getObjectDescribeApiName();
            MetaDataBizTick.clObjDetail(objApiName, MetaDataBizOpID.ViewAll, "SaleTeam", sourceData.getID());
            startActivity(TeamMemberActivity.getIntent(getContext(), objApiName, sourceData.getID()));
        }
    }

    @Override
    public TeamMemberInfo getOwner() {
        if (userDatas == null || userDatas.isEmpty()) return null;
        TeamMemberInfo owner = new TeamMemberInfo();
        for (ObjectData objectData : userDatas) {
            MetaTeamMemberInfo metaTeamMemberInfo = objectData.to(MetaTeamMemberInfo.class);
            String ownerType =  metaTeamMemberInfo.getTeamRole();
            if (TeamMemberUtils.isOwner(ownerType)) {
                owner.employeeIDList = metaTeamMemberInfo.getEmployees();
                owner.permissionTypeEnum = metaTeamMemberInfo.getPermissionType();
                owner.teamMemberTypes = new ArrayList<>(Collections.singleton(ownerType));
                return owner;
            }
        }
        return null;
    }

    @Override
    public String getObjectDescribeApiName() {
        return sourceData.getObjectDescribeApiName();
    }

    @Override
    public String getObjectDataID() {
        return sourceData.getID();
    }

    @Override
    public boolean disableSelectAll() {
        return false;
    }

    @Override
    public Bundle assembleInstanceState() {
        Bundle data = super.assembleInstanceState();
        data.putBundle(KEY_SAVE_ACTION_DATA, addTeamMemberAction.assembleInstanceState());
        return data;
    }

    @Override
    public void restoreInstanceState(@Nullable Bundle savedInstanceState) {
        super.restoreInstanceState(savedInstanceState);
        if(savedInstanceState != null) {
            addTeamMemberAction.restoreInstanceState(savedInstanceState.getBundle(KEY_SAVE_ACTION_DATA), this);
        }
    }
}
