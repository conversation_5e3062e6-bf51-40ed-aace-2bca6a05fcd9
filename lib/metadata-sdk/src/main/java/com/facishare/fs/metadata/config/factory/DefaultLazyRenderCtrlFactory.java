/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.config.factory;

import com.facishare.fs.metadata.commonviews.lazyrender.LazyRenderCtrl;
import com.facishare.fs.metadata.detail.viewrenders.HeadFragViewRenderCtrl;
import com.facishare.fs.metadata.detail.viewrenders.NormalTabFragViewRenderCtrl;
import com.facishare.fs.modelviews.MultiContext;

import androidx.annotation.NonNull;

/**
 * Created by zhouz on 2019/1/23.
 */
public class DefaultLazyRenderCtrlFactory implements ILazyRenderCtrlFactory {
    /**对象apiName*/
    protected String apiName;

    public DefaultLazyRenderCtrlFactory(String apiName) {
        this.apiName = apiName;
    }

    @Override
    public LazyRenderCtrl getHeadFragViewRenderCtrl(@NonNull MultiContext multiContext) {
        return new HeadFragViewRenderCtrl(multiContext);
    }

    @Override
    public LazyRenderCtrl getNormalTabFragViewRenderCtrl(@NonNull MultiContext multiContext) {
        return new NormalTabFragViewRenderCtrl(multiContext);
    }
}
