/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.modify.modelviews.field;

import com.facishare.fs.common_utils.function.Function;
import com.facishare.fs.metadata.R;
import com.facishare.fs.metadata.actions.DateTimeSelectAction;
import com.facishare.fs.metadata.beans.MetaDataParser;
import com.facishare.fs.metadata.beans.fields.FieldType;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.facishare.fs.modelviews.MultiContext;

import android.os.Bundle;
import androidx.annotation.Nullable;

/**
 * 选时间公用的ModelView
 * 支持选日期（yyyy-MM-dd）、选时间（HH:mm）和选日期+时间（yyyy-MM-dd HH:mm）
 * Created by ruicb on 2016/9/27.
 */
public class DateTimeMView extends AbsClickableItemMView implements DateTimeSelectAction.DateTimeSelectCallBack {

    /**
     * 选时间的Action
     */
    private DateTimeSelectAction mAction;
    private final String KEY_SAVE_TIME = "KEY_SAVE_TIME";
    private Function<Long, Boolean> mPreManuallyUpdateBlocker;

    public DateTimeMView(MultiContext context, FieldType type) {
        super(context);
        mAction = new DateTimeSelectAction(context);
        int mPickerType = DateTimeSelectAction.DATE_TIME;
        if (type == FieldType.DATE) {
            mPickerType = DateTimeSelectAction.DATE;
        }else if (type == FieldType.TIME) {
            mPickerType = DateTimeSelectAction.TIME;
        }
        mAction = new DateTimeSelectAction(context, mPickerType, false, this);
        showArrow(false);
    }

    @Override
    protected void onContentClick() {
        if (mAction != null && mCanEdit) {
            mAction.start(null);
        }
    }

    /**
     * 得到选时间的Action
     *
     * @return
     */
    public DateTimeSelectAction getAction() {
        return mAction;
    }

    public void setTime(Long time) {
        if (MetaDataUtils.equals(time, mValue)){
            return;
        }
        mAction.setStartTime(time);
        super.updateContent(time);
        notifyOnValueChanged();
    }

    @Override
    public void updateContent(Object object) {
        setTime(MetaDataParser.parseJLong(object));
    }

    private boolean mCanEdit = true;

    public void setEditable(boolean canEdit) {
        this.mCanEdit = canEdit;
    }

    public void setRightAction(boolean show) {
        clearRightActions();
        if (show) {
            addRightAction(R.drawable.meta_time,null);
        }
    }

    @Override
    public void showArrow(boolean show) {
        super.showArrow(false);
    }

    @Override
    public void setReadOnlyStyle(boolean readOnly) {
        super.setReadOnlyStyle(readOnly);
        setRightAction(!readOnly);
    }

    /**
     * 判断内容是否为空
     *
     * @return true：为空
     */
    @Override
    public boolean isEmpty() {
        return mAction.getStartTime() == null;
    }

    @Override
    public void onTimeSelect(Long startTime, Long endTime) {
        if (!isBlockManuallyUpdate(startTime)) {
            updateTimeManually(startTime);
        }
    }

    @Override
    public void onClear() {
        if (!isBlockManuallyUpdate(null)) {
            updateTimeManually(null);
        }
    }

    public void updateTimeManually(Long time) {
        Object orgValue = mValue;
        updateContent(time);
        if (!MetaDataUtils.equals(time, orgValue)) {
            notifyOnManualChanged();
        }
    }

    @Override
    public void onCancel() {

    }

    @Override
    public Bundle assembleInstanceState() {
        Bundle data = super.assembleInstanceState();
        if (!isEmpty()) {
            data.putLong(KEY_SAVE_TIME, mAction.getStartTime());
        }
        return data;
    }

    @Override
    public void restoreInstanceState(@Nullable Bundle savedInstanceState) {
        super.restoreInstanceState(savedInstanceState);
        if (savedInstanceState != null) {
            setTime(savedInstanceState.getLong(KEY_SAVE_TIME));
        }
    }

    private boolean isBlockManuallyUpdate(Long time) {
        return mPreManuallyUpdateBlocker != null && mPreManuallyUpdateBlocker.apply(time);
    }

    public void setPreManuallyUpdateBlocker(Function<Long, Boolean> preManuallyUpdateBlocker) {
        this.mPreManuallyUpdateBlocker = preManuallyUpdateBlocker;
    }
}
