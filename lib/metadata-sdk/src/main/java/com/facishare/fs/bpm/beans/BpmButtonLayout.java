/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.bpm.beans;

import java.io.Serializable;
import java.util.Map;

import com.facishare.fs.metadata.beans.ButtonOption;
import com.facishare.fs.metadata.beans.ObjectData;

/**
 * 按钮布局
 * */
public class BpmButtonLayout implements Serializable {
    public ButtonOption button;
    public Map<String, Object> objectData;

    public ButtonOption getButton() {
        return button;
    }

    public void setButton(ButtonOption button) {
        this.button = button;
    }

    public Map<String, Object>  getObjectData() {
        return objectData;
    }

    public void setObjectData(Map<String, Object> objectData) {
        this.objectData = objectData;
    }
}
