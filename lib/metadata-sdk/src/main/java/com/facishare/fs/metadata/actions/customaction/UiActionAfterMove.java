package com.facishare.fs.metadata.actions.customaction;

import java.util.Map;

import com.afollestad.materialdialogs.DialogFragmentWrapper;
import com.alibaba.fastjson.JSON;
import com.facishare.fs.metadata.TransparentAct;
import com.facishare.fs.metadata.actions.basic.ActivityAction;
import com.facishare.fs.metadata.beans.MetaData;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.facishare.fs.metadata.web.MetaDataJsApiFragActivity;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.fscommon.sandbox.SandboxContextManager;
import com.fxiaoke.fscommon.util.FsUrlUtils;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.fxiaoke.fshttp.web.sandbox.ISandboxContext;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.TextUtils;

/**
 * ui 按钮的 uiaction 后动作
 * http://wiki.firstshare.cn/pages/viewpage.action?pageId=135679094
 * Created by zhouz on 2020/11/11.
 */
public class UiActionAfterMove extends ActivityAction<Void> {
    private MetaData actionDes;

    public UiActionAfterMove(MultiContext context) {
        super(context);
    }

    public UiActionAfterMove setAction(Map<String, Object> action) {
        if (action!=null){
            this.actionDes = new MetaData(action);
        }else {
            actionDes=null;
        }
        return this;
    }

    @Override
    public void start(Void target) {
        if (actionDes==null){
            return;
        }
        dispatchAction();
    }

    private void dispatchAction(){
        String action = actionDes.getString("action");
        if (TextUtils.isEmpty(action)){
            return;
        }
        switch (action){
            case "OpenDialogAction":
                openDialogAction();
                break;
            case "AlertAction":
                alertAction();
                break;
            case "AppAction":
                appAction();
                break;
        }
    }

    private static final String customComponentUrl=
            "/fsh5/cuscomp-vessel/index.html#/uiaction/vessel";
    /**跳转自定义组件*/
    private void openDialogAction(){
        String url = WebApiUtils.getWebViewRequestUrl()+customComponentUrl;
        ISandboxContext sandboxContext = SandboxContextManager.getInstance().getContext(getContext());
        if(sandboxContext!=null&&sandboxContext.isUpEa()){
            url = url.concat("?fsAppId="+sandboxContext.getFsAppId()).concat("&upstreamEa="+sandboxContext.getEa());
        }
        startActivity(MetaDataJsApiFragActivity.getIntent(getContext(), url,
                JSON.toJSONString(actionDes.getMap()), null));
    }

    /**弹窗提醒*/
    private void alertAction(){
        String text = actionDes.getString("text");
        if (TextUtils.isEmpty(text)){
            return;
        }
        BasicDialogPageHook.start(getContext(), text);
    }

    /**兼容执行 uiaction 的界面调用完成后就关闭，但依然要展示 dialog 的场景*/
    public static class BasicDialogPageHook extends TransparentAct.PageHook {
        private static String KEY_TEXT="KEY_TEXT";
        public static void start(Context context, String text){
            Bundle data = new Bundle();
            data.putString(KEY_TEXT, text);
            TransparentAct.start(context, BasicDialogPageHook.class, data);
        }

        public BasicDialogPageHook(MultiContext multiContext, Bundle data) {
            super(multiContext, data);
        }

        private String getText(){
            return mData!=null?mData.getString(KEY_TEXT):"";
        }

        @Override
        public void onPostCreate(Bundle savedInstanceState) {
            super.onPostCreate(savedInstanceState);
            DialogFragmentWrapper.FsDialogFragment dialog =
                    DialogFragmentWrapper.showBasic(mMultiContext.getContext(), getText());
            dialog.setDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    finish();
                }
            });
        }
    }

    /**跳转各种 link*/
    private void appAction(){
        String link = actionDes.getString("url");
        FsUrlUtils.gotoAction(getActivity(), MetaDataUtils.fitHttpUrl(link));
    }
}
