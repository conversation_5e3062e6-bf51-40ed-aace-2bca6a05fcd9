/*
 * Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.list.modelviews;

import com.facishare.fs.metadata.R;
import com.facishare.fs.metadata.list.beans.ListItemArg;
import com.facishare.fs.modelviews.AutoModelViewGroup;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.cmviews.view.DynamicViewStub;

import android.content.Context;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

/**
 * 列表单列字段MVGroup基类
 * Created by zhouz on 2018/10/30.
 */
public abstract class AbsListFieldMVGroup<T extends ListItemArg> extends AutoModelViewGroup<ListItemFieldArg, Void> implements
        IUpdateModelView<T> {
    protected LinearLayout mFieldContainer;
    protected DynamicViewStub mTopStub,mBottomStub;
    private DynamicViewStub mPrivateBottomStub;//预制对象不要使用这个
    private ListContentAdapter<T> mContentAdapter;//列表内容适配器

    public AbsListFieldMVGroup(@NonNull MultiContext multiContext) {
        super(multiContext);
    }

    public ListContentAdapter<T> getContentAdapter() {
        if (mContentAdapter == null) {
            mContentAdapter = new ListContentAdapter<>();
        }
        return mContentAdapter;
    }

    public void setContentAdapter(ListContentAdapter<T> contentAdapter) {
        mContentAdapter = contentAdapter;
    }

    @Override
    protected View onCreateView(Context context) {
        View root = LayoutInflater.from(context).inflate(getLayoutResId(), null);
        mTopStub = root.findViewById(R.id.top_stub);
        mFieldContainer = root.findViewById(R.id.field_list_container);
        mBottomStub = root.findViewById(R.id.bottom_stub);
        mPrivateBottomStub = root.findViewById(R.id.private_bottom_stub);
        return root;
    }

    @LayoutRes
    protected int getLayoutResId() {
        return R.layout.layout_meta_list_field_single_column;
    }

    @Override
    public ViewGroup getViewContainer(View view) {
        return mFieldContainer;
    }

    /**
     * 更新渲染前触发,尽量避免修改原始数据
     */
    protected void beforeUpdate(T listItemArg) {
    }

    /**
     * 子类在这里进行UI重渲染逻辑
     */
    protected void onUpdate(T listItemArg) {
    }

    public DynamicViewStub getPrivateBottomStub() {
        return mPrivateBottomStub;
    }
}
