package com.facishare.fs.metadata.modify.checker;

import java.util.ArrayList;
import java.util.List;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.fields.Field;
import com.facishare.fs.metadata.beans.fields.group.AreaGroupField;
import com.facishare.fs.metadata.modify.modelviews.field.AreaMView;
import com.facishare.fs.metadata.utils.MetaDataUtils;

import android.text.TextUtils;

/**
 * Created by zhouz on 2017/9/21.
 */

public class AreaChecker extends DefaultFieldMViewChecker {
    @Override
    public boolean isStandard(Object content, IFieldCheckerContext ruleContext, boolean checkRequired) {
        if (ruleContext != null && checkRequired && ruleContext instanceof IAreaCheckerContext) {
            IAreaCheckerContext areaCheckerContext = (IAreaCheckerContext) ruleContext;
            AreaGroupField areaGroupField = areaCheckerContext.getField();
            if (areaGroupField != null) {
                boolean standard = true;
                List<Field> usableFieldList = areaGroupField.getUsableFieldList();
                ObjectData selectData = (ObjectData) content;
                List<String> labels = new ArrayList<>();
                boolean curSelectLevelHasChild = true;
                if (selectData != null && selectData.getExtMap() !=null && selectData.getExtMap().containsKey(AreaMView
                        .KEY_IS_NEED_CHECK_NOTNULL)) {
                    curSelectLevelHasChild = (boolean)selectData.getExtValue(AreaMView.KEY_IS_NEED_CHECK_NOTNULL);
                }
                for (Field field : usableFieldList) {
                    if (field != null && (field.isRequired() || areaCheckerContext.isAreaRequired()) &&
                            (selectData == null || MetaDataUtils.isEmpty(selectData.get(field.getApiName())))) {
                        //当前层级必填，没有数据，省市区结构中存在当前层级时
                        if (curSelectLevelHasChild) {
                            labels.add(field.getLabel());
                            standard = false;
                        }
                        break;
                    }
                }
                if (!standard) {
                    mNotStandardInfo = I18NHelper.getFormatText("meta.checker.AreaChecker.2981",
                            areaGroupField.getLabel(),TextUtils.join("、", labels))/* {0}中 {1}未选择 */;
                    return false;
                }
            }
        }
        return true;
    }
}
