/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.list.flowstage.stageviews;

import com.facishare.fs.metadata.list.beans.IFlowStageInfo;

/**
 * 类名
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2018/8/8
 */
public interface OnStageClickListener {

    /**
     * 当StageView点击时的回调
     *
     * @param stageView 点击的阶段View
     * @param position  点击的View所处的位置
     * @param stageInfo 当前点击View对应的数据
     */
    void onStageClick(StageView stageView, int position, IFlowStageInfo stageInfo);
}
