/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.actions;

import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.ObjectDescribe;

/**
 * <AUTHOR>
 * @date 2019/8/14 16:52
 * @description
 */
public interface MetaDataAddMultiContext extends MetaDataAddContext,LoadingContext {

    ObjectData getObjectData();

    ObjectDescribe getObjectDescribe();
}
