/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.list.select_obj;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.R;
import com.fxiaoke.cmviews.custom_fragment.FsFragment;
import com.lidroid.xutils.util.FSViewUtils;

/**
 * Author:  wangrz
 * Date:    2020/6/11 14:44
 * Remarks: 通用选对象已选列表页面底部Bar
 */
public class MetaDataSelectedObjBarFrag extends FsFragment {

    private IRemoveListener mRemoveListener;

    public static MetaDataSelectedObjBarFrag newInstance() {
        MetaDataSelectedObjBarFrag fragment = new MetaDataSelectedObjBarFrag();
        Bundle args = new Bundle();
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.layout_select_object_bar, null);
        initView(view);
        return view;
    }

    private void initView(View view) {
        view.findViewById(R.id.text_select_title).setVisibility(View.INVISIBLE);
        view.findViewById(R.id.textView_selectrange_show).setVisibility(View.INVISIBLE);
        View rootView = view.findViewById(R.id.selected_text_layout);
        final Button removeButton = (Button) rootView.findViewById(R.id.btn_confirm);
        rootView.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
            @Override
            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop,
                                       int oldRight, int oldBottom) {
                int expendWidth = FSScreen.dip2px(mActivity, 12);
                int expendHeight = FSScreen.dip2px(mActivity, 12);
                FSViewUtils.addViewTouchRangeDp(removeButton, expendWidth, expendHeight, expendWidth, expendHeight);
            }
        });
        removeButton.setText(I18NHelper.getText("meta.layout.layout_multi_form_edit.2919")/* 移除 */);
        removeButton.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                if (mRemoveListener != null) {
                    mRemoveListener.onRemoveClick();
                }
            }
        });
    }

    public void setRemoveListener(IRemoveListener removeListener) {
        this.mRemoveListener = removeListener;
    }

    /**
     * 移除监听回调
     */
    public interface IRemoveListener {

        void onRemoveClick();
    }
}
