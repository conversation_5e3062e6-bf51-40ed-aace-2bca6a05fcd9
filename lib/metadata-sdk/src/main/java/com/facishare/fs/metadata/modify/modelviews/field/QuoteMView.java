package com.facishare.fs.metadata.modify.modelviews.field;

import com.facishare.fs.metadata.R;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.fields.FieldType;
import com.facishare.fs.metadata.beans.fields.QuoteField;
import com.facishare.fs.metadata.commonviews.TextWatcherAdapter;
import com.facishare.fs.metadata.config.MetaDataConfig;
import com.facishare.fs.metadata.modify.modelviews.IFormItemView;
import com.facishare.fs.metadata.modify.modelviews.field.presenter.PicChoiceMViewPresenter;
import com.facishare.fs.metadata.modify.modelviews.field.presenter.QuoteMViewPresenter;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.facishare.fs.modelviews.MultiContext;
import com.facishare.fs.pluginapi.location.PluginFsLocationResult;
import com.fxiaoke.location.api.FsLocationResult;

import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.view.View;

/**
 * 引用字段
 * Created by weilh on 2017/7/11.
 */

public class QuoteMView extends AbsEditableItemMView {
    private MultiPicChoiceMView mPicChoiceMView;
    public QuoteMView(MultiContext context) {
        super(context);
    }

    @Override
    protected View onCreateView(Context context) {
        if (FieldType.IMAGE.key.equals(getField().getQuoteFieldType())){
            mPicChoiceMView = new MultiPicChoiceMView(getMultiContext());
            mPicChoiceMView.setArg(QuoteMViewPresenter.createReturnTypeArg(getArg()));
            mPicChoiceMView.init();
            return mPicChoiceMView.getView();
        }else {
            View root = super.onCreateView(context);
            addContentTextChangeListener(new TextWatcherAdapter() {
                @Override
                public void afterTextChanged(Editable s) {
                    notifyOnValueChanged();
                }
            });
            root.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    toShowAddress();
                }
            });
            return root;
        }
    }

    @Override
    public void setReadOnlyStyle(boolean readOnly) {
        super.setReadOnlyStyle(readOnly);
        if (mPicChoiceMView != null){
            mPicChoiceMView.setReadOnlyStyle(readOnly);
        }
    }

    @Override
    public void setRequiredStyle(boolean required) {
        super.setRequiredStyle(required);
        if (mPicChoiceMView != null){
            mPicChoiceMView.setRequiredStyle(required);
        }
    }

    @Override
    protected int getLayoutId() {
        return R.layout.meta_item_info_view;
    }

    @Override
    public void updateContent(Object object) {
        if (FieldType.IMAGE.key.equals(getField().getQuoteFieldType())){
            if (mPicChoiceMView != null){
                PicChoiceMViewPresenter.updateImageMViewShow(mPicChoiceMView, getArg(), object);
            }
        }else if (FieldType.ADDRESS.key.equals(getField().getQuoteFieldType())) {
            mAddress = MetaDataUtils.parseAddress(object);
        }
        super.updateContent(object);
    }

    /**清除缓存数据*/
    private void clearCache() {
        mAddress = null;
    }

    /**清除缓存数据再更新*/
    public void clearOldDataCacheThenUpdate(Object value, ObjectData src) {
        clearCache();
        updateContent(value);
        //兼容引用单选多选选项删除场景
        CharSequence quoteStr = QuoteMViewPresenter.getQuoteOptionStr(src,
                    getFormField());
        if (!TextUtils.isEmpty(quoteStr)){
            super.setContentText(quoteStr);
        }
    }

    @Override
    public void setContentText(CharSequence content) {
        super.setContentText(content);
        //地址特殊处理
        if (FieldType.ADDRESS.key.equals(getField().getQuoteFieldType())) {
            clearRightActions();
            if (mAddress != null) {
                addRightAction(R.drawable.address_locate, null);
            }
        }
    }

    private FsLocationResult mAddress;
    private void toShowAddress() {
        if (mAddress == null) {
            return;
        }
        PluginFsLocationResult fsLocationResult =
                new PluginFsLocationResult(mAddress.getLatitude(), mAddress.getLongitude());
        fsLocationResult.setAddress(mAddress.getAddress());
        MetaDataConfig.getOptions().getAddressService().showAddress(getContext(), fsLocationResult);
    }

    @Override
    public QuoteField getField() {
        return super.getField().to(QuoteField.class);
    }

    @Override
    public Object getResult() {
        return mValue;
    }

    @Override
    public boolean isEmpty() {
        if (mPicChoiceMView != null){
            return mPicChoiceMView.isEmpty();
        }
        return super.isEmpty();
    }
}
