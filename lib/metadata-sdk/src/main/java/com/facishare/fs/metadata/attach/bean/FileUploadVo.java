/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.attach.bean;

import com.facishare.fs.pluginapi.fileserver.upload.FileUploadAbstractVo;

/**
 * Created by xudd on 2016/3/24.
 */
public class FileUploadVo extends FileUploadAbstractVo {

    public boolean mIsNetDiskFile;
    public long mSize;
    //标记该task属于哪个附件字段
    public String mIndex;
    public String mTmpPath;

    public FileUploadVo() {

    }
    public FileUploadVo(boolean isNetDiskFile, long size, String index){
        mIsNetDiskFile = isNetDiskFile;
        mSize = size;
        mIndex = index;
    }
}
