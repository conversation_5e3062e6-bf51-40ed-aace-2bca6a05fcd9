/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.detail;

import java.util.Map;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.BaseWebSearchActivity;
import com.facishare.fs.metadata.beans.ObjectDataKeys;
import com.facishare.fs.metadata.beans.ObjectDescribe;
import com.facishare.fs.metadata.beans.Operator;
import com.facishare.fs.metadata.beans.fields.FilterInfo;
import com.facishare.fs.metadata.detail.fragment.RelationObjsFrag;
import com.facishare.fs.metadata.list.select_obj.IBarConfirm;
import com.facishare.fs.metadata.list.select_obj.MetaDataSelectObjectBarFrag;
import com.facishare.fs.metadata.list.select_obj.picker.PickMode;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.facishare.fs.pickerutils.MOPController;
import com.facishare.fs.pickerutils.MOPCounter;
import com.facishare.fs.pluginapi.crm.biz_api.IMetaData;
import com.fxiaoke.fscommon.util.CommonDataContainer;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import android.text.TextUtils;
import android.view.View;

/**
 * Author:  wangrz
 * Date:    2019/6/6 17:54
 * Remarks:
 */
public class AllRelationSearchObjAct extends BaseWebSearchActivity implements IBarConfirm {
    protected static final String CONFIG = "config";
    protected static final String SEARCH_FIELD = "search_field";
    protected static final String TARGET_DISPLAY_NAME = "targetDisplayName";
    protected static final String SEARCH_HINT = "searchHint";
    protected static final String OBJECT_DESCRIBE = "objectDescribe";
    protected RelationObjListConfig mConfig;
    protected MOPCounter mMOPCounter;
    private String mSearchField;
    private ObjectDescribe mObjectDescribe;
    private RelationObjsFrag mFragment;
    private MetaDataSelectObjectBarFrag mBarFrag;
    public static Intent getIntent(Context context, RelationObjListConfig config,
                                   MOPCounter mopCounter, String searchField,
                                   ObjectDescribe objectDescribe ) {
        Intent intent = new Intent(context, AllRelationSearchObjAct.class);
        intent.putExtra(CONFIG, config);
        intent.putExtra(MOPController.KEY_COUNTER, mopCounter);
        intent.putExtra(SEARCH_FIELD, searchField);
        CommonDataContainer.getInstance().saveData(OBJECT_DESCRIBE, objectDescribe);
        return intent;
    }

    @Override
    public void onSafeSaveInstanceState(Bundle outState) {
        super.onSafeSaveInstanceState(outState);
        outState.putSerializable(CONFIG, mConfig);
        outState.putSerializable(MOPController.KEY_COUNTER, mMOPCounter);
        outState.putString(SEARCH_FIELD, mSearchField);
    }

    @Override
    protected boolean initData(Bundle savedInstanceState) {
        Intent intent = getIntent();
        if (savedInstanceState == null) {
            mConfig = (RelationObjListConfig) intent.getSerializableExtra(CONFIG);
            mMOPCounter = (MOPCounter) intent.getSerializableExtra(MOPController.KEY_COUNTER);
            mSearchField = intent.getStringExtra(SEARCH_FIELD);
        } else {
            mConfig = (RelationObjListConfig) savedInstanceState.getSerializable(CONFIG);
            mMOPCounter = (MOPCounter) savedInstanceState.getSerializable(MOPController.KEY_COUNTER);
            mSearchField = savedInstanceState.getString(SEARCH_FIELD);
        }
        if (TextUtils.isEmpty(mSearchField)) {
            mSearchField = ObjectDataKeys.NAME;
        }
        mObjectDescribe =
                (ObjectDescribe) CommonDataContainer.getInstance().getAndRemoveSavedData(OBJECT_DESCRIBE);
        return mConfig != null;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        super.initView(savedInstanceState);
        //多选模式显示底部选择条
        if (mConfig.mActionType ==RelationObjListConfig.ACTION_TYPE_SELECT && mConfig.mPickMode== PickMode.MULTI) {
            mBarFrag = MetaDataSelectObjectBarFrag.getInstance(getTargetDisplayName(), mMOPCounter);
            setBottomFrag(mBarFrag);
        }
    }

    @Override
    protected Fragment getContentFragment() {
        if (mFragment == null) {
            mFragment = RelationObjsFrag.getInstance(mConfig, mMOPCounter);
        }
        return mFragment;
    }

    @Override
    protected void onSearchContent(String searchStr) {
        Map<String,String> fieldMap = MetaDataUtils.getRelatedListSearchOperator(mConfig.targetObjApiName);
        String tempFieldName = null;
        String operator = null;
        if(fieldMap!=null && !fieldMap.isEmpty()){
            tempFieldName = fieldMap.get(IMetaData.LIST_CUSTOMIZED_SEARCH_APINAME);
            operator = fieldMap.get(IMetaData.LIST_CUSTOMIZED_SEARCH_OPERATOR);
        }
        if(TextUtils.isEmpty(tempFieldName)){
            tempFieldName = mSearchField;
        }
        if(TextUtils.isEmpty(operator)){
            operator = Operator.LIKE;
        }
        FilterInfo filterInfo = new FilterInfo(tempFieldName, operator, searchStr);
        mFragment.searchRefresh(filterInfo);
    }

    @Override
    protected String getSearchKey() {
        return "metadata_" + mConfig.targetObjApiName;
    }

    @Override
    protected String getHintStr() {
        String searchFiledLabel = getTargetDisplayName();
        Map<String,String> fieldMap = MetaDataUtils.getRelatedListSearchOperator(mConfig.targetObjApiName);
        if(fieldMap!=null && !fieldMap.isEmpty()){
            String apiName = fieldMap.get(IMetaData.LIST_CUSTOMIZED_SEARCH_APINAME);
            String filedLabel = MetaDataUtils.getNameLabelFromDescribe(mObjectDescribe,
                    apiName);
            if(!TextUtils.isEmpty(filedLabel)){
                searchFiledLabel = filedLabel;
            }
        }
       return I18NHelper.getFormatText("crm.layout.layout_select_product.v1.1825"/* 搜索 */ , searchFiledLabel);
    }

    private String getTargetDisplayName(){
        return  mObjectDescribe != null ? mObjectDescribe.getDisplayName() : "";
    }

    @Override
    public void onClickConfirm(View v) {
        setResult(RESULT_OK);
        finish();
    }

    @Override
    public void onClickSelectedCount() {

    }
}
