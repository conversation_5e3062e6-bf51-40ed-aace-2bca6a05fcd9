package com.facishare.fs.metadata.modify.count;

import java.util.List;

import com.facishare.fs.metadata.beans.formfields.CountFormField;

import android.text.TextUtils;

/**
 * Created by zhouz on 2017/7/28.
 */

public abstract class CountListener {
    public CountListener(List<CountFormField> countFormFields) {
        mCountFormFields = countFormFields;
    }

    private List<CountFormField> mCountFormFields;

    public List<CountFormField> getCountFormFields() {
        return mCountFormFields;
    }

    public void setCountFormFields(
            List<CountFormField> countFormFields) {
        mCountFormFields = countFormFields;
    }

    public boolean match(String subObjApiName) {
        if (mCountFormFields == null)
            return false;
        for (CountFormField countFormField : mCountFormFields) {
            if (TextUtils.equals(subObjApiName, countFormField.getSubObjApiName()))
                return true;
        }
        return false;
    }

    public abstract void onChanged(List<CountResult> resultList);
}
