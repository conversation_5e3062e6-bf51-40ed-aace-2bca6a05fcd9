/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.modify.uievent;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.UiEventRemind;
import com.facishare.fs.metadata.modify.master_detail.IMetaModifyFragContainer;
import com.facishare.fs.metadata.modify.master_detail.IModifyDetailFrag;
import com.facishare.fs.metadata.modify.modelviews.field.AbsItemMView;
import com.facishare.fs.metadata.modify.modelviews.table.TableListItemArg;
import com.facishare.fs.metadata.modify.remote_calculate.CalculatorUtil;
import com.facishare.fs.metadata.utils.ModelViewUtils;

import android.app.Activity;
import androidx.annotation.NonNull;
import android.text.TextUtils;

/**
 * Created by zhouz on 2019-08-12.
 */
public class UiNormalModify extends BaseUiImpl {
    /**主从fragment容器*/
    IMetaModifyFragContainer mdFragContainer;

    public UiNormalModify(UiEventExecutor executor,
                          IMetaModifyFragContainer mdFragContainer) {
        super(executor);
        this.mdFragContainer = mdFragContainer;
    }

    @Override
    protected boolean checkSetUp() {
        boolean hasUIEvents = mExecutor.mMasterLayout.getEvents() != null && !mExecutor.mMasterLayout.getEvents().isEmpty();
        if (!hasUIEvents){
            return super.checkSetUp();
        }
        //从对象数据必须加载完且渲染完
        List<IModifyDetailFrag> detailFragList = mdFragContainer.getDetailFragments();
        if (detailFragList != null) {
            for (IModifyDetailFrag detailFrag : detailFragList) {
                if (!detailFrag.isDataLoaded()){
                    return false;
                }
            }
        }
        return super.checkSetUp();
    }

    @Override
    protected Map<String, Object> getMasterData(EventTaskInfo taskInfo) {
        Map<String, Object> rst = mdFragContainer.getMasterFragment().getObjectData().getMap();
        if(taskInfo.fieldChangeExtraData != null){
            rst.putAll(taskInfo.fieldChangeExtraData.getMap());
        }
        return rst;
    }

    @Override
    protected Map<String, Map<String, Map<String, Object>>> getDetailData(EventTaskInfo taskInfo) {
        return CalculatorUtil.getDetailDataFromModifyFragContainer(mdFragContainer);
    }

    @Override
    Activity getContext() {
        return mdFragContainer.getMasterFragment().getMultiContext().getContext();
    }

    @Override
    protected void updateData(JSONObject data, @NonNull EventTaskInfo taskInfo) {
        //先回填临时额外数据
        if (taskInfo.fieldChangeExtraData!=null){
            updateContentView(mdFragContainer.getMasterFragment().getAddOrEditGroup(), taskInfo.fieldChangeExtraData);
        }
        if (data==null){
            return;
        }
        for (String apiName : data.keySet()){
            JSONObject objDatas = data.getJSONObject(apiName);
            if (objDatas == null){
                continue;
            }
            if (TextUtils.equals(mExecutor.mMasterDescribeApiName, apiName)){
                updateContentView(mdFragContainer.getMasterFragment().getAddOrEditGroup(),
                        new ObjectData(objDatas.getInnerMap()));
            }else {
                IModifyDetailFrag detailFrag = mdFragContainer.getDetailFragment(apiName);
                if (detailFrag == null){
                    continue;
                }
                Map<String, ObjectData> detailNewIndexData = new HashMap<>();
                JSONObject updateDatas = objDatas.getJSONObject("u");
                if (updateDatas != null){
                    for (String index : updateDatas.keySet()){
                        ObjectData oneUpdateData = new ObjectData();
                        if (updateDatas.get(index) != null){
                            oneUpdateData.putAll(updateDatas.getJSONObject(index).getInnerMap());
                        }
                        detailNewIndexData.put(index, oneUpdateData);
                    }
                }
                JSONArray addDatas = objDatas.getJSONArray("a");
                if (addDatas != null){
                    for (int i=0; i<addDatas.size(); i++){
                        JSONObject addData = addDatas.getJSONObject(i);
                        if (addData != null){
                            ObjectData oneAddObjData = new ObjectData();
                            oneAddObjData.setObjectDescribeApiName(apiName);
                            oneAddObjData.putAll(addData.getInnerMap());
                            //新增数据构造数字类型 index，用于保证添加顺序
                            detailNewIndexData.put(TableListItemArg.uniqueCode(), oneAddObjData);
                        }
                    }
                }
                detailFrag.getTableComponentMView().triggerUiDataUpdate(detailNewIndexData);
            }
        }
    }

    @Override
    protected void showRedRemindAlert(@NonNull EventTaskInfo taskInfo, @NonNull UiEventRemind remind) {
        if (TextUtils.equals(mExecutor.getCurrentObjApiName(), taskInfo.apiName)){
            AbsItemMView fieldMV =
                    mdFragContainer.getMasterFragment().getAddOrEditGroup()
                            .getFieldModelByFieldName(taskInfo.fieldName);
            if (fieldMV != null){
                ModelViewUtils.alertFieldView(mExecutor.getContext(),
                        fieldMV, remind.content);
            }
        }
    }
}
