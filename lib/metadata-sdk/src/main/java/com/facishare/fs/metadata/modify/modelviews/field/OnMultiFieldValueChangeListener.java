package com.facishare.fs.metadata.modify.modelviews.field;

import java.util.List;

import com.facishare.fs.metadata.beans.ObjectData;

import androidx.annotation.NonNull;

/**
 * 编辑态下多个字段value同时变化场景的监听
 * 暂时仅国家省市区字段使用
 * Created by zhouz on 2018/3/28.
 */

public interface OnMultiFieldValueChangeListener {
    /**
     * @param fieldMView 被监听字段的modelView
     * @param changedFields 改变的字段
     * @param fieldsData 存有改变字段的新value的map
     */
    void onValueChanged(@NonNull AbsEditableItemMView fieldMView, List<String> changedFields,
                        ObjectData fieldsData);
}
