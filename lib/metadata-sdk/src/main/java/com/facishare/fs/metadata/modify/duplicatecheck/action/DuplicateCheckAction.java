package com.facishare.fs.metadata.modify.duplicatecheck.action;

import com.facishare.fs.metadata.actions.basic.ActivityAction;
import com.facishare.fs.metadata.config.MetaDataConfig;
import com.facishare.fs.metadata.router.CrmObjListUrlGenerator;
import com.facishare.fs.modelviews.MultiContext;


/**
 * 查重Action
 * <AUTHOR> by 2019/01/09
 * */
public class DuplicateCheckAction extends ActivityAction<String> {

    public DuplicateCheckAction(MultiContext context) {
        super(context);
    }

    @Override
    public void start(String apiName) {
        new CrmObjListUrlGenerator("DuplicateCheckObj").add("api_name",apiName).startActivity(getActivity());
//        MetaDataConfig.getOptions().getOperationService().startDuplicateCheck(getContext(),apiName);
    }
}
