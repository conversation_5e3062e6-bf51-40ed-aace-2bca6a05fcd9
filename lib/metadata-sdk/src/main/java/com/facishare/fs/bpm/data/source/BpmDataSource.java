package com.facishare.fs.bpm.data.source;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.bpm.beans.BpmCompleteResult;
import com.facishare.fs.bpm.beans.BpmUseApiNameResult;
import com.facishare.fs.bpm.beans.CircleType;
import com.facishare.fs.bpm.beans.GetSomeConfigResult;
import com.facishare.fs.bpm.beans.GetWorkflowInstanceLogResult;
import com.facishare.fs.bpm.beans.InstanceState;
import com.facishare.fs.bpm.beans.MAvailableWorkflow;
import com.facishare.fs.bpm.beans.MSimpleTask;
import com.facishare.fs.bpm.beans.MTaskLog;
import com.facishare.fs.bpm.beans.ObjectUnCompletedTask;
import com.facishare.fs.bpm.beans.SimpleWorkflow;
import com.facishare.fs.bpm.beans.WorkflowInstanceVO;
import com.facishare.fs.metadata.beans.BpmValidationRuleMessage;
import com.facishare.fs.metadata.beans.ObjectData;
import com.fxiaoke.plugin.crm.common_view.warnview.WarnResult;

import android.app.Activity;
import androidx.annotation.Nullable;
import io.reactivex.Single;

/**
 * Created by zhouz on 2017/4/28.
 */

public interface BpmDataSource {
    String NULL_RESULT_MSG = "result is null";
    Single<BpmValidationRuleMessage> getValidateRule(ObjectData objectData, String objectApiName, Map<String,
            Object> details, String option);
    void getAvailableWorkflow(GetAvailableWorkflowCallBack callBack);

    void getAvailableWorkflowByObject(String apiName, String objectID, GetAvailableWorkflowByObjCallBack callBack);

    void getAllDefinitionList(GetAllDefinitionCallBack callBack);

    void getInstanceListByObject(Integer pageSize, Integer pageNumber, String orderBy, Boolean asc, String
            objectID, InstanceState instanceState, GetInstanceListCallBack callBack);

    void cancelInstance(String instanceID,String reason, CancelInstanceCallBack callBack);

    void startInstance(String outlineID, String objectID, String apiName,
                       @Nullable Map<String, String> variables, StartInstanceCallBack callBack);

    void getInstanceList(Integer pageSize, Integer pageNumber, String orderBy, Boolean asc, String
            sourceWorkflowId, CircleType circleType, InstanceState instanceState, String workflowName,
                         GetInstanceListCallBack callBack);

    void getLog(String instanceID, GetLogCallBack callBack);

    void completeTask(String taskId, String opinion, String objectID, Map<String, Object> result, Map<String, Object> nextTaskAssignee, int addOrReplaceNextTaskAssignee,
                      CompleteTaskCallBack callBack);

    void updateAndCompleteTask(String taskId, String opinion, String apiName, String objectID,
                               Map<String, Object> result, Map<String, Object> nextTaskAssignee,
                               int addOrReplaceNextTaskAssignee, Integer validationRule, UpdateAndCompleteCallBack callBack);

    void updateData(String apiName, String objectID, Map<String, Object> value, Integer validationRule,String taskId,
                    UpdateDataCallBack callBack);

    void getTodoTask(Integer pageNumber, Integer pageSize, Boolean isCompleted, String keyword, GetTodoTasksCallBack
            callBack);

    void getTodoTasksByObject(String apiName, String objectID, GetTodoTasksByObjectCallBack callBack);

    void changeTaskHandler(String taskID, List<String> candidateIds, ChangeTaskHandlerCallBack callBack);

    void getObjectDataOwner(String apiName, String objectDataID, GetObjectDataOwnerCallBack callBack);

    void getWorkFlowInstanceLog(String instanceId, GetWorkflowInstanceLogCallBack callBack);

    void reTryOrIgnore(String taskId, String executeType, String rowNum,ReTryOrIgnoreCallBack callBack);
    void afterActionReTryOrIgnore(String instanceId, String executeType, String rowNum, ReTryOrIgnoreCallBack callBack);

    void clearCrmRemind(Activity activity,ClearCrmRemindCallBack callBack);

    Single<GetSomeConfigResult> getSomeConfig();

    Single<GetSomeConfigResult> getSomeConfig(String id,String apiName,String objId,String linkAppId,String linkAppType);

    void getBpmUseApiNames(Activity activity,RequestCallBack.DataCallBack<BpmUseApiNameResult> callBack);
    void getBpmButtonLayout(String objId,String btnApiName,RequestCallBack.DataCallBack callBack);

    interface ReTryOrIgnoreCallBack extends RequestCallBack.ActionCallBack{}

    interface GetSomeConfigCallBack extends RequestCallBack.DataCallBack<String> {

    }

    interface ClearCrmRemindCallBack extends RequestCallBack.DataCallBack<Boolean> {

    }

    interface GetAvailableWorkflowCallBack extends RequestCallBack.DataCallBack<List<MAvailableWorkflow>> {
    }

    interface GetAvailableWorkflowByObjCallBack extends RequestCallBack.DataCallBack<List<MAvailableWorkflow>> {
    }

    interface CancelInstanceCallBack extends RequestCallBack.ActionCallBack {
    }

    interface StartInstanceCallBack {
        void onSuccess(WarnResult result);
        void onFailed(String error);
    }

    interface GetInstanceListCallBack {
        void onDataLoaded(List<WorkflowInstanceVO> instanceVOList, int totalCount,Map<String, JSONObject> employeeInfo);

        void onDataNotAvailable(String error);
    }

    interface GetLogCallBack extends RequestCallBack.DataCallBack<List<MTaskLog>> {
    }

    interface CompleteTaskCallBack extends RequestCallBack.ActionCallBack {
    }

    interface UpdateAndCompleteCallBack{
        void onSuccess(BpmCompleteResult result);
        void onFailed(String error);
    }

    interface UpdateDataCallBack extends RequestCallBack.ActionCallBack {
    }

    interface GetWorkflowInstanceLogCallBack extends RequestCallBack.DataCallBack<GetWorkflowInstanceLogResult> {

    }

    interface GetTodoTasksCallBack {
        void onDataLoaded(List<MSimpleTask> simpleTaskList, int total);

        void onDataNotAvailable(String error);
    }

    interface GetTodoTasksByObjectCallBack extends RequestCallBack.DataCallBack<List<ObjectUnCompletedTask>> {
    }

    interface ChangeTaskHandlerCallBack extends RequestCallBack.ActionCallBack {

    }

    interface GetObjectDataOwnerCallBack extends RequestCallBack.DataCallBack<List<String>> {

    }

    interface GetAllDefinitionCallBack extends RequestCallBack.DataCallBack<List<SimpleWorkflow>> {

    }
}
