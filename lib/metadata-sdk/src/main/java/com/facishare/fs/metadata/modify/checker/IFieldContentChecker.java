package com.facishare.fs.metadata.modify.checker;

import com.facishare.fs.metadata.modify.IDataChecker;

/**
 * Created by zhouz on 2017/6/12.
 */

public interface IFieldContentChecker extends IDataChecker<Object> {
    /**
     * 数据是否合格
     * @param data 校验的数据
     * @param rule 校验规则
     * @return true 数据合格，false 不合格，可以通过{@link #getNotStandardDescription()}来获取不合格的提示信息
     */
    boolean isStandard(Object data, IFieldCheckerContext rule);

    /**
     * 数据是否合格
     * @param data 校验的数据
     * @param ruleContext 校验环境
     * @param checkRequired 是否校验必填
     * @return true 数据合格，false 不合格，可以通过{@link #getNotStandardDescription()}来获取不合格的提示信息
     */
    boolean isStandard(Object data, IFieldCheckerContext ruleContext, boolean checkRequired);
}
