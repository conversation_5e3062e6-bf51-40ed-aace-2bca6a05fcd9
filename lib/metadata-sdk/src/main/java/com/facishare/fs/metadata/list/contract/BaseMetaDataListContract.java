/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.list.contract;

import java.util.List;

import com.facishare.fs.common_utils.IActivityResult;
import com.facishare.fs.metadata.BasePresenter;
import com.facishare.fs.metadata.actions.IAddSmartFormContext;
import com.facishare.fs.metadata.actions.OperationItem;
import com.facishare.fs.metadata.beans.FilterScene;
import com.facishare.fs.metadata.beans.Layout;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.ObjectDescribe;
import com.facishare.fs.metadata.beans.fields.Field;
import com.facishare.fs.metadata.beans.fields.FilterInfo;
import com.facishare.fs.metadata.list.beans.search_query.OrderInfo;
import com.facishare.fs.metadata.list.beans.search_query.SearchQueryInfo;
import com.facishare.fs.metadata.list.webmenu.MetaWMController;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.fscommon_res.view.HorizontalListActionBar;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

public interface BaseMetaDataListContract {
    interface MetaDataBaseView<T extends MetaDataBasePresenter> extends IAddSmartFormContext {

        void setPresenter(T presenter);

        /**
         * 显示loading
         */
        void showDialogLoading();

        /**
         * 隐藏loading
         */
        void dismissLoading();

        MultiContext getMultiContext();

        /**
         * 更新顶部操作
         *
         * @param buttonItems
         */
        void updateTopActions(List<OperationItem> buttonItems);

        /**
         * 更新排序
         * */
        void updateOrderInfo(OrderInfo orderInfo);

        /**
         * 更新筛选条件
         * */
        void updateFilters(List<FilterInfo> filters);

        /**
         * 设置筛选条件并刷新
         * */
        void setFiltersAndRefreshList(List<FilterInfo> filters);
        /**
         * 设置排序并刷新
         * */
        void setOrderInfoAndRefreshList(OrderInfo selectedOrderInfo);

        /**只刷新列表数据*/
        void refreshList();
        /**更新选中的场景*/
        void onUpdateSelectedScene(FilterScene mCurrentFilterScene);
        /**更新标题*/
        void updateTitle(String title,String objectName);
        /**按钮前置操作*/
        void actionBarBeforeClick(View v);
        /**apiName*/
        String getTargetApiName();
        /**获取请求的筛选条件*/
        SearchQueryInfo getSearchQueryInfo();

    }

    interface MetaDataBasePresenter extends BasePresenter,
            IActivityResult {
        /**
         * 透传数据
         */
        void setExtraData(Bundle extraData);
        MetaWMController getOpsController();
        void onRefreshed(List<ObjectData> objectDataList, ObjectDescribe objectDescribe, Layout layout);
        void setHorizontalListActionBar(HorizontalListActionBar horizontalListActionBar);

        boolean hasFilters();

        void enableSortButton(boolean enable);
        /**
         * 展示场景列表
         * */
        void showScenesView(View anchor);

        /**
         * 获取场景
         * */
        List<FilterScene> getScenesList();

        /**
         * 获取当前场景
         * */
        FilterScene getCurrentScene();

        /**
         * 重置筛选项
         */
        void resetFilter(boolean shouldRefresh);

        /**
         * 传入标题箭头view
         * */
        void setTitleView(TextView titleCenterSVG);
    }
}
