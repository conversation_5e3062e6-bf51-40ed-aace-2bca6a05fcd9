package com.facishare.fs.metadata.modify.duplicatecheck;

import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.metadata.R;
import com.facishare.fs.metadata.list.beans.ListItemArg;
import com.facishare.fs.metadata.list.modelviews.ListContentAdapter;
import com.facishare.fs.metadata.list.modelviews.ListItemContentMView;
import com.facishare.fs.modelviews.ModelView;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.cmviews.view.DynamicViewStub;

import android.content.Context;
import androidx.annotation.LayoutRes;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

/**
 * 自定义对象查重结果ModelView
 *
 * <AUTHOR> by 2018/11/12
 */
public class MetaDataCheckResultMView extends ModelView {

    protected ListItemContentMView<ListItemArg> listItemContentMView;
    protected ImageView mPickView;
    protected DynamicViewStub mFieldStub, mBottomStub;
    protected View bottomView;
    protected View mBottomSpace;

    public MetaDataCheckResultMView(MultiContext multiContext) {
        super(multiContext);
        listItemContentMView = new ListItemContentMView(multiContext);
        listItemContentMView.setContentAdapter(createAdapter());
    }

    protected ListContentAdapter<ListItemArg> createAdapter() {
        return new MetaDataCheckResultListContentAdapter();
    }

    @Override
    protected View onCreateView(Context context) {
        View rootView = LayoutInflater.from(context).inflate(R.layout.meta_data_check_result_item, null);
        mPickView = rootView.findViewById(R.id.picker_view);
        mFieldStub = rootView.findViewById(R.id.field_stub);
        mBottomStub = rootView.findViewById(R.id.bottom_stub);
        mBottomSpace = rootView.findViewById(R.id.bottom_space);
        mFieldStub.setInflatedView(listItemContentMView.getView()).inflate();
        int padding = FSScreen.dip2px(15);
        listItemContentMView.getView().setPadding(0, padding, 0, padding);
        Integer bottomViewId = inflatedButton();
        if (bottomViewId != 0) {
            bottomView = LayoutInflater.from(context).inflate(bottomViewId, null);
            mBottomStub.setInflatedView(bottomView).inflate();
        }
        return rootView;
    }

    /**
     * 更新View
     */
    public void updateModelViews(ListItemArg listItemArg) {
        listItemContentMView.updateModelView(listItemArg);
    }

    /**
     * 加载按钮布局
     */
    protected @LayoutRes
    Integer inflatedButton() {
        return 0;
    }

    /**
     * 是否展示分割线
     */
    public void showBottomSpace(boolean show) {
        mBottomSpace.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    public ImageView getPickView() {
        return mPickView;
    }

    @Override
    public boolean isEmpty() {
        return false;
    }
}
