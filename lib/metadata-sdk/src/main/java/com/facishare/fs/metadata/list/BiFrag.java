package com.facishare.fs.metadata.list;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.R;
import com.facishare.fs.metadata.beans.ObjectDescribe;
import com.facishare.fs.metadata.beans.fields.FilterInfo;
import com.facishare.fs.metadata.list.contract.ListBiContract;
import com.facishare.fs.metadata.list.presenter.ListBiPresenter;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.sizectrlviews.SizeControlTextView;
import com.fxiaoke.cmviews.custom_fragment.FsFragment;
import com.fxiaoke.fshttp.web.http.WebApiUtils;

import java.util.List;

/**
 * 大列表顶部展示的Bi报表
 * */
public class BiFrag extends FsFragment implements ListBiContract.BView {
    private View rootView;
    private SizeControlTextView webTopLeftTag;
    private SizeControlTextView webTopRightGoTo;//跳转分析按钮
    private MetaDataListWebFrag mMetaDataListWebFrag;
    private String apiName;
    private String displayName;
    private String jsonString,fieldsJsonStr;//H5传递的图表筛选条件及图表状态
    protected boolean isFromH5;//是否是点击H5的图跳进来的
    private LinearLayout filterLinear;//筛选条件容器
    private RefreshListFromWeb mRefreshListFromWeb;
    private List<FilterInfo> filterInfoList;//H5传递的筛选
    private ListBiPresenter listBiPresenter;
    private boolean isFragmentInitFinished;//Web是否初始化完成
    private ObjectDescribe mObjectDescribe;


    public interface RefreshListFromWeb{
        void refreshListFromWeb(List<FilterInfo> filterInfos);
        void canShowBi();
    }
    public BiFrag setRefreshListFromWeb(RefreshListFromWeb refreshListFromWeb) {
        mRefreshListFromWeb = refreshListFromWeb;
        return this;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        rootView= inflater.inflate(R.layout.metadata_list_bi, container, false);
        listBiPresenter=new ListBiPresenter(this).setFromH5(isFromH5);
        initView();
        listBiPresenter.getBiModelList();
        return rootView;
    }

    public BiFrag setFilterInfoList(List<FilterInfo> filterInfoList) {
        this.filterInfoList=filterInfoList;
        return this;
    }

    public BiFrag setApiName(String apiName) {
        this.apiName = apiName;
        return this;
    }

    public BiFrag setFieldsJsonStr(String fieldsJsonStr) {
        this.fieldsJsonStr=fieldsJsonStr;
        return this;
    }

    public BiFrag setJsonString(String jsonString) {
        this.jsonString = jsonString;
        return this;
    }

    public BiFrag setFromH5(boolean fromH5) {
        isFromH5 = fromH5;
        return this;
    }

    /**
     * 初始化View
     * */
    private void initView(){
        webTopLeftTag = rootView.findViewById(R.id.metadata_web_top_tag);
        webTopRightGoTo = rootView.findViewById(R.id.metadata_web_top_goto);
        filterLinear=rootView.findViewById(R.id.filter_linear);
        listBiPresenter.setArrowView(webTopLeftTag);
        if(!isFromH5){
            webTopLeftTag.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if(listBiPresenter!=null){
                        listBiPresenter.showBiMenu(webTopLeftTag);
                    }
                }
            });
        }
        webTopRightGoTo.setOnClickListener(v -> {
            if(listBiPresenter!=null){
                listBiPresenter.goToAnalysisView();
            }

        });
        initH5WebFragment();
    }

    @Override
    public String getApiName() {
        return apiName;
    }

    @Override
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 更新各个Tag
     * */
    public void updateTag(String str) {
        displayName=str;
        SizeControlTextView mRelationObjLeftBar = rootView.findViewById(R.id.metadata_top_tag);
        mRelationObjLeftBar.setText(I18NHelper.getFormatText("com.crm.metadata.list.top.web.list",str));
        webTopRightGoTo.setText(I18NHelper.getFormatText("com.crm.metadata.list.top.web.analysis",str));
    }

    /**
     * 加载Web
     * */
    private void initH5WebFragment() {
        LinearLayout linear=rootView.findViewById(R.id.list_head_frag_container);
        ViewGroup.LayoutParams params = linear.getLayoutParams();
        params.height=  Math.round(FSScreen.getScreenWidth()*0.69f);
        linear.setLayoutParams(params);
        mMetaDataListWebFrag = MetaDataListWebFrag.getInstance();
        mMetaDataListWebFrag.setBusinessType(getClass().getName() + "_metadata_list_h5web");
        mMetaDataListWebFrag.setFragmentInitFinishedListener(() ->
        {isFragmentInitFinished=true; initH5JsWebView();});
        mMetaDataListWebFrag.setApiName(apiName)
                .setFromH5(isFromH5)
                .setJsonString(jsonString)
                .setRefreshListFromWeb((filterInfos,jFieldsMap) -> {
                    filterInfoList=filterInfos;
                    if(jFieldsMap!=null){
                        fieldsJsonStr=jFieldsMap.toJSONString();
                    }
                    setFilterLinearTextView(mObjectDescribe);
                    if(mRefreshListFromWeb!=null){
                        mRefreshListFromWeb.refreshListFromWeb(filterInfos);
                    }
                });
        FragmentManager fm = getFragmentManager();
        FragmentTransaction ft = fm.beginTransaction();
        ft.replace(R.id.list_head_frag_container, mMetaDataListWebFrag);
        ft.commitAllowingStateLoss();
        rootView.findViewById(R.id.filter_image).setOnClickListener(v -> {
            if(listBiPresenter!=null){
                listBiPresenter.showFilterDialog(getFragmentManager());
            }
        });
        if(!isFromH5){
            webTopRightGoTo.setVisibility(View.VISIBLE);
            rootView.findViewById(R.id.metadata_top_tag).setVisibility(View.VISIBLE);
            rootView.findViewById(R.id.metadata_web_top_arrow).setVisibility(View.VISIBLE);
        }else{
            rootView.findViewById(R.id.metadata_top_tag).setVisibility(View.GONE);
        }
    }
    private String beforeUrl;
    public void setTopWebViewUrl(String url){
        if (TextUtils.isEmpty(url) || url.equals(beforeUrl)) {
            mMetaDataListWebFrag.reload();
        } else {
            mMetaDataListWebFrag.loadUrl(WebApiUtils.getWebViewRequestUrl() + url);
            rootView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    mMetaDataListWebFrag.reload();
                }
            }, 300);
        }
        beforeUrl = url;
//        mMetaDataListWebFrag.loadUrl(WebApiUtils.getWebViewRequestUrl() + url);
//        mMetaDataListWebFrag.loadUrl( "javascript:window.location.reload(true)" );
    }

    private void initH5JsWebView() {
        if(isFragmentInitFinished&&listBiPresenter!=null&&!TextUtils.isEmpty(listBiPresenter.getCurrentLayoutId())) {
            mMetaDataListWebFrag.initJsApi(null);
            String baseUrl =
                    HostInterfaceManager.getCloudCtrlManager().getStringConfig("ObjectShowInsightURL",
                            "/h5app/bi-report?fromapp=1#/insight/");
            String url = baseUrl + apiName+"/"+listBiPresenter.getCurrentLayoutId();
            setTopWebViewUrl(url);
        }
    }

    public void setTopTagVisibilit(int visibility) {
        rootView.findViewById(R.id.metadata_top_tag).setVisibility(visibility);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(mMetaDataListWebFrag!=null){
            try{
                mMetaDataListWebFrag.onActivityResult(requestCode, resultCode, data);
            }catch (Exception e){
            }
        }
    }

    @Override
    public void showBiView() {
        if(mRefreshListFromWeb!=null){
            mRefreshListFromWeb.canShowBi();
            updateTagAndUrl();
        }
    }

    /**
     * 更新web的地址
     * */
    public void updateTagAndUrl(){
        if(webTopLeftTag!=null&&listBiPresenter!=null) {
            webTopLeftTag.setText(listBiPresenter.getCurrentLayoutName());
        }
        initH5JsWebView();
    }

    /**
     * 设置展示的条件
     * */
    public void setFilterLinearTextView(ObjectDescribe objectDescribe){
        mObjectDescribe=objectDescribe;
        if(listBiPresenter!=null){
            listBiPresenter.setFilterInfoList(filterInfoList)
                    .setFieldsJsonStr(fieldsJsonStr)
                    .setFilterLinearTextView(mObjectDescribe,filterLinear);
        }
        if(isFromH5){
            rootView.findViewById(R.id.metadata_top_filter_rel).setVisibility(View.VISIBLE);//筛选条件容器
        }
    }
}
