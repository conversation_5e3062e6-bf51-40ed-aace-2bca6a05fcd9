/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.bpm.beans;

import java.io.Serializable;

/**
 * 下节点任务信息
 * */
public class BpmNextTaskInfo implements Serializable {
    private String nextTaskId;
    private int activityInstanceId;
    private String workflowInstanceId;

    public String getNextTaskId() {
        return nextTaskId;
    }

    public void setNextTaskId(String nextTaskId) {
        this.nextTaskId = nextTaskId;
    }

    public int getActivityInstanceId() {
        return activityInstanceId;
    }

    public void setActivityInstanceId(int activityInstanceId) {
        this.activityInstanceId = activityInstanceId;
    }

    public String getWorkflowInstanceId() {
        return workflowInstanceId;
    }

    public void setWorkflowInstanceId(String workflowInstanceId) {
        this.workflowInstanceId = workflowInstanceId;
    }
}
