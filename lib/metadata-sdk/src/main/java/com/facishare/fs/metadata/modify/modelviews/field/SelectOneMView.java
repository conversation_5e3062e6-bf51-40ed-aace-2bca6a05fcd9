/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.modify.modelviews.field;

import java.util.ArrayList;
import java.util.List;

import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.actions.item_choice.OnFieldSelectedCallback;
import com.facishare.fs.metadata.actions.item_choice.SelectFieldHelper;
import com.facishare.fs.metadata.beans.MetaDataParser;
import com.facishare.fs.metadata.beans.ObjectDataKeys;
import com.facishare.fs.metadata.beans.OptionsResult;
import com.facishare.fs.metadata.beans.fields.Option;
import com.facishare.fs.metadata.beans.formfields.SelectOneFormField;
import com.facishare.fs.metadata.data.source.MetaDataRepository;
import com.facishare.fs.metadata.modify.modelviews.field.presenter.SingleChoiceMViewPresenter;
import com.facishare.fs.metadata.utils.ModelViewUtils;
import com.facishare.fs.metadata.utils.SharedPreferencesUtils;
import com.facishare.fs.modelviews.ModelView;
import com.facishare.fs.modelviews.MultiContext;
import com.facishare.fs.modelviews.relation.ParentChangeListener;

import android.os.Bundle;
import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

/**
 * 元数据单选的Model,在单选的SingleChoiceModel基础上 添加了数据变化后对孩子和父亲的影响控制
 *
 * <AUTHOR>
 *         <b>DATE</b> 2016/11/2.
 */

public class SelectOneMView extends SingleChoiceMView<Option> implements ParentChangeListener {
    protected Option mSelectedParent = null;
    private final String KEY_SAVE_SELECTED_PARENT = "KEY_SAVE_SELECTED_PARENT";

    private IRequestRemoteOptionsContext mIRequestRemoteOptionsContext;
    protected CascadeSelectChildImpl mCascadeSelectChild;

    public SelectOneMView(MultiContext context) {
        super(context);
        setParentChangeListener(this);
        mCascadeSelectChild=new CascadeSelectChildImpl(this);
    }

    @Override
    protected void onContentClick() {
        SelectOneMView parentModel = mCascadeSelectChild.getSelectOneParent();
        if (parentModel != null && mSelectedParent == null) {
            String parentLabel = parentModel.getFormField().getLabel();
            ModelViewUtils.alertFieldView(getMultiContext().getContext(), parentModel,
                    I18NHelper.getFormatText("crm.visit_action.hint.choose_first"/* 请先选择  */, parentLabel));
            return;
        }
        if (mOnPreSelectCallback != null && mOnPreSelectCallback.onPreSelectCallback()) {
            return;
        }
        if (getFormField().remoteCall4Options()) {//如果需要通过接口获取单选选项，则请求数据
            requestRemoteOptions();
        } else {
            mAction.start(null);
        }
    }

    @Override
    protected void onSelected(List<Option> selectedList) {
        super.onSelected(selectedList);
        saveSelected(selectedList!=null&&!selectedList.isEmpty()?selectedList.get(0):null);
    }

    @Override
    public void updateContent(Object object) {
        if (mAction != null) {
            if (object==null){
                mAction.setSelected(null);
            }else {//考虑级联关系
                String value = object.toString();
                List<String> values = new ArrayList<>();
                values.add(value);
                MetaDataParser.filterInvalidValues(getOptionsByParentSelected(mSelectedParent), values);
                if(values.isEmpty()){
                    mAction.setSelected(null);
                }else {
                    mAction.setSelected(value);
                }
            }
        }
    }

    /**更新其他文本内容*/
    public void  setOtherContent(String otherText){
        if (mAction != null) {
            SelectFieldHelper.fillOtherText2Src(mAction.getObjList(), otherText);
        }
    }

    private OnFieldSelectedCallback<Option> mOnFieldSelectedCallback;

    public void setOnFieldSelectedCallback(
            OnFieldSelectedCallback<Option> onFieldSelectedCallback) {
        mOnFieldSelectedCallback = onFieldSelectedCallback;
    }

    @Override
    public void onFieldSelected(List<Option> selectedList) {
        super.onFieldSelected(selectedList);
        notifyOnValueChanged();
        if (mOnFieldSelectedCallback != null) {
            mOnFieldSelectedCallback.onFieldSelected(selectedList);
        }
        notifyChildrenChanged();
    }


    /**多货币字段记忆上次选择*/
    private void saveSelected(Option option){
        if(TextUtils.equals(getFormField().getApiName(),
                ObjectDataKeys.CURRENCY_TYPE)){
            SharedPreferencesUtils.getMetaDataSPOperator()
                    .save(SingleChoiceMViewPresenter.currencyTypeSp,option!=null?option.getValue():"");
        }
    }

    @Override
    public void notifyChildrenChanged(Object curValue) {
        super.notifyChildrenChanged(mAction.getSelectedObj());
    }

    @Override
    public void onParentChanged(@Nullable ModelView parentModelView, @Nullable Object parentValue) {
        mSelectedParent = (Option) parentValue;
        List<Option> newSelectObjList = getOptionsByParentSelected(mSelectedParent);
        Option currentSelectedObj = mAction.getSelectedObj();
        mAction.setObjList(newSelectObjList);
        if (mSelectedParent == null){//父空
            if (currentSelectedObj != null){//原来有选中项
                mAction.setSelectedData(null);//清空
            }
            return;
        }

        if (currentSelectedObj != null){//原来有选中项
            if (mCascadeSelectChild.containsChild(mSelectedParent, currentSelectedObj)) {//原来的选中项匹配新的 parent
                return;
            }
        }

        Option autoSelect = null;
        if (newSelectObjList.size() ==1 && isRequired()){//必填，且只有一个选项时自动选中
            autoSelect = newSelectObjList.get(0);
        }
        if (autoSelect != currentSelectedObj){//和之前选中项不同时才触发回调
            mAction.setSelectedData(autoSelect);
        }
    }

    /**
     * 从父亲选中的列表中 获取当前孩子的选项
     *
     * @param parentSelected 父亲的选中节点
     *
     * @return 当前可选列表
     */
    @NonNull
    protected List<Option> getOptionsByParentSelected(Option parentSelected) {
        return mCascadeSelectChild.getOptionsByParentSelected(parentSelected, getOptionsUsable());
    }

    protected List<Option> getOptionsUsable(){
        SelectOneFormField selectOneFormField = getFormField().to(SelectOneFormField.class);
        return selectOneFormField.getOptionsUsable();
    }

    @Override
    public Bundle assembleInstanceState() {
        Bundle data = super.assembleInstanceState();
        data.putSerializable(KEY_SAVE_SELECTED_PARENT, mSelectedParent);
        return data;
    }

    @Override
    public void restoreInstanceState(@Nullable Bundle savedInstanceState) {
        super.restoreInstanceState(savedInstanceState);
        if (savedInstanceState != null) {
            mSelectedParent = (Option) savedInstanceState.getSerializable(KEY_SAVE_SELECTED_PARENT);
        }
    }

    @Override
    public boolean isDataStandard(boolean checkRequired) {
        return getDataChecker() == null || getDataChecker().isStandard(getAction().getSelectedObj(), this, checkRequired);
    }

    /**
     * 请求单选选项值
     */
    private void requestRemoteOptions() {
        String dataId = null;
        String dataObjApiName = null;
        String targetObjApiName = null;
        String sourceObjApiName = null;
        if (mIRequestRemoteOptionsContext != null){
            dataId = mIRequestRemoteOptionsContext.getDataId();
            dataObjApiName = mIRequestRemoteOptionsContext.getDataObjApiName();
            targetObjApiName = mIRequestRemoteOptionsContext.getTargetObjApiName();
            sourceObjApiName = mIRequestRemoteOptionsContext.getSourceObjApiName();
        }
        String action = getFormField().getRemoteCall4OptionsUrl();
        showLoading();
        MetaDataRepository.getInstance(getMultiContext().getContext())
                .getLazyLoadOptions(dataId, dataObjApiName, targetObjApiName, sourceObjApiName, action)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(Schedulers.io())
                .subscribe(new Consumer<OptionsResult>() {
                    @Override
                    public void accept(OptionsResult result) throws Exception {
                        dismissLoading();
                        List<Option> options = result == null ? null : result.toOptions();
                        mAction.setObjList(options);
                        mAction.start(null);
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        dismissLoading();
                        ToastUtils.show(throwable.getMessage());
                    }
                });
    }

    public void setRequestRemoteOptionsContext(IRequestRemoteOptionsContext remoteOptionParam) {
        this.mIRequestRemoteOptionsContext = remoteOptionParam;
    }

    /**
     * 请求单选选项接口时上下文接口，提供相关参数
     */
    public interface IRequestRemoteOptionsContext {

        /**
         * 获取数据id,如：产品id
         */
        String getDataId();

        /**
         * 数据对象的ApiName,如：产品ProductObj
         */
        String getDataObjApiName();

        /**
         * 目标对象的ApiName，如：单位UnitInfoObj
         */
        String getTargetObjApiName();

        /**
         * 源对象的ApiName，如订单产品SalesOrderProductObj
         */
        String getSourceObjApiName();
    }
}
