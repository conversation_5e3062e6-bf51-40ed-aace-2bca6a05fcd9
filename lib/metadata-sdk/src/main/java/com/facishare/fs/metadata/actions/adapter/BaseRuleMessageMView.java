/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.actions.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import android.view.View;

import com.facishare.fs.metadata.R;
import com.facishare.fs.metadata.beans.ValidationMultiRuleMessage;
import com.facishare.fs.modelviews.ModelView;
import com.facishare.fs.modelviews.MultiContext;
import com.fxiaoke.cmviews.view.DynamicViewStub;

/**
 * <AUTHOR>
 * @date 2019/4/18 9:34
 * @description
 */
public abstract class BaseRuleMessageMView extends ModelView {

    protected DynamicViewStub topLayout;
    protected DynamicViewStub bottomLayout;
    
    public BaseRuleMessageMView(@NonNull MultiContext multiContext) {
        super(multiContext);
    }

    @Override
    protected View onCreateView(Context context) {
        View rootView = View.inflate(context, R.layout.meta_data_validation_rule_multi_message, null);
        topLayout = (DynamicViewStub) rootView.findViewById(R.id.top_stub);
        bottomLayout = (DynamicViewStub) rootView.findViewById(R.id.bottom_stub);
        topLayout.setInflatedView(createTopView(context)).inflate();
        bottomLayout.setInflatedView(createBottomView(context)).inflate();
        return rootView;
    }

    protected abstract View createBottomView(Context context);

    protected abstract View createTopView(Context context);

    @Override
    public boolean isEmpty() {
        return false;
    }

    public abstract void updataView(int i, ValidationMultiRuleMessage validationMultiRuleMessage);
}
