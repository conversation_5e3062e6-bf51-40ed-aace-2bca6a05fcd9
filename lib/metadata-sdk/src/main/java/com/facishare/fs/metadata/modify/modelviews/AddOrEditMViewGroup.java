/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.modify.modelviews;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.facishare.fs.common_utils.ISaveInstanceState;
import com.facishare.fs.common_utils.function.Supplier;
import com.facishare.fs.metadata.ILoadingView;
import com.facishare.fs.metadata.beans.Layout;
import com.facishare.fs.metadata.beans.ObjectData;
import com.facishare.fs.metadata.beans.ObjectDataKeys;
import com.facishare.fs.metadata.beans.ObjectDescribe;
import com.facishare.fs.metadata.beans.fields.Field;
import com.facishare.fs.metadata.beans.fields.FieldType;
import com.facishare.fs.metadata.beans.formfields.FormField;
import com.facishare.fs.metadata.config.MetaDataConfig;
import com.facishare.fs.metadata.modify.IFieldEditableContainer;
import com.facishare.fs.metadata.modify.MetaModifyUtil;
import com.facishare.fs.metadata.modify.Scene;
import com.facishare.fs.metadata.modify.backfill.BackFillInfo;
import com.facishare.fs.metadata.modify.backfill.BackFillInfoIndex;
import com.facishare.fs.metadata.modify.backfill.BackFillInfos;
import com.facishare.fs.metadata.modify.backfill.BackFillView;
import com.facishare.fs.metadata.modify.checker.IDataCheckerView;
import com.facishare.fs.metadata.modify.modelviews.componts.FormComponentMView;
import com.facishare.fs.metadata.modify.modelviews.componts.beans.ComponentViewArg;
import com.facishare.fs.metadata.modify.modelviews.componts.beans.ComponentViewResult;
import com.facishare.fs.metadata.modify.modelviews.field.AbsEditableItemMView;
import com.facishare.fs.metadata.modify.modelviews.field.AbsItemMView;
import com.facishare.fs.metadata.modify.modelviews.field.AttachMView;
import com.facishare.fs.metadata.modify.modelviews.field.IGetGeneralStatePathImageBean;
import com.facishare.fs.metadata.modify.remote_calculate.IGetRemoteCalculate;
import com.facishare.fs.metadata.modify.remote_calculate.IMetaRemoteCalculable;
import com.facishare.fs.metadata.modify.remote_calculate.RemoteExpressionExecutor;
import com.facishare.fs.metadata.modify.uievent.UiEventExecutor;
import com.facishare.fs.metadata.utils.MetaDataUtils;
import com.facishare.fs.metadata.utils.ModelViewUtils;
import com.facishare.fs.metadata.utils.OldCrmObjUtil;
import com.facishare.fs.modelviews.AutoModelViewGroup;
import com.facishare.fs.modelviews.ModelView;
import com.facishare.fs.modelviews.ModelViewGroup;
import com.facishare.fs.modelviews.MultiContext;
import com.facishare.fs.modelviews.controller.ModelViewController;
import com.facishare.fs.modelviews.relation.ParentObservable;
import com.facishare.fs.pluginapi.contact.beans.User;
import com.facishare.fs.pluginapi.pic.bean.GeneralStatePathImageBean;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.collection.SimpleArrayMap;
import android.text.TextUtils;
import android.view.ViewGroup;
import android.widget.LinearLayout;

/**
 * <AUTHOR>
 *         <b>DATE</b> 2016/11/8.
 */

public class AddOrEditMViewGroup extends AutoModelViewGroup<ComponentViewArg, ComponentViewResult> implements
        IFieldEditableContainer, ISaveInstanceState {
    public static final String FIRST_NOTIFY_FLAG = "first_notify_flag";
    private boolean mShowNotRequired = true;
    private ArrayMap<String, AbsItemMView> mFieldMViewMap = null;//二次获取字段model的场景很多, update后缓存, key为fieldName(国家省市区字段以国家fieldName为key)
    private List<AbsItemMView> mFieldMViewList = null;
    private final String KEY_SAVE_ORG_DATA = "KEY_SAVE_ORG_DATA";
    private ObjectData mOrgObjectData = null;//重要，计算时需要
    private ObjectDescribe mObjectDescribe;
    protected int mScene = Scene.INFO;
    private Set<IFieldEditableContainer> mOuterFieldEditableContainers = new LinkedHashSet<>();

    protected final String KEY_SHOW_NOT_REQUIRED = "AddOrEditMViewGroup_Key_Show_Not_Required";

    public AddOrEditMViewGroup(MultiContext context) {
        super(context);
    }

    public final ViewGroup onCreateView(Context context) {
        LinearLayout layout = new LinearLayout(context);
        layout.setOrientation(LinearLayout.VERTICAL);
        layout.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        //让根布局可获取焦点，避免editText焦点无法清除
        layout.setFocusable(true);
        layout.setFocusableInTouchMode(true);
        layout.setClickable(true);
        return layout;
    }

    @Override
    public ModelViewController<ComponentViewArg, ComponentViewResult> createModelViewController() {
        String apiName = mObjectDescribe != null ?  mObjectDescribe.getApiName() : "";
        return MetaDataConfig.getOptions().getMetaBizImplFactories().getModeViewControllerFactory(apiName)
                .getComponentController();
    }

    /**
     * 更新创建ModelView
     *
     * @param objectDescribe 对象描述（必须）
     * @param objectData     数据
     * @param layout         布局（必须）
     * @param scene          场景（必须）
     * @param backFillInfos  终端特殊回填逻辑
     * @see Scene
     *
     */
    public void updateModelViews(ObjectDescribe objectDescribe, ObjectData objectData, Layout layout, int scene,
                                 BackFillInfos backFillInfos) {
        updateModelViews(objectDescribe,objectData, layout, scene, backFillInfos, true);
    }

    /**
     * @param setupCalculate 是否初始化计算器 */
    public void updateModelViews(ObjectDescribe objectDescribe, ObjectData objectData, Layout layout, int scene,
                                 BackFillInfos backFillInfos, boolean setupCalculate) {
        mModelViewController = null;
        mOrgObjectData = objectData;
        if(scene==Scene.CREATE){
            initDefaultOwner(objectData);
        }
        mObjectDescribe = objectDescribe;
        mScene = scene;
        mFieldMViewMap = null;
        mFieldMViewList = null;
        MetaModifyUtil.interceptBeforeUpdate(getMultiContext(), true);
        //创建并渲染formComponent
        setArgs(MetaDataUtils.getComponentArgs(objectDescribe, objectData, layout, scene));
        //终端特殊回填逻辑
        updateBackFillInfos(objectDescribe, backFillInfos);

        MetaModifyUtil.interceptBeforeUpdate(getMultiContext(), false);
        //编辑态使用回填完成后的数据初始化布局规则
        if (Scene.INFO != scene) {
            //初始化计算需要的数据
            if (setupCalculate) {//渲染完后再初始化计算和 ui 事件，渲染过程中不触发
                RemoteExpressionExecutor.setUpData(getMultiContext(), objectDescribe !=null?objectDescribe
                        .getApiName():null, objectDescribe);
                UiEventExecutor.setUpData(getMultiContext(), objectDescribe !=null?objectDescribe
                        .getApiName():null, layout, objectDescribe);
                //初始化渲染完成需要触发的计算
                initCalculateAfterRenderEnd(backFillInfos);
            }

            List<FormComponentMView> formComViews = getFormComView();
            if (!formComViews.isEmpty()) {
                formComViews.get(0).initLayoutRule(getFieldMViewMap(), getObjectData(), objectDescribe, layout, scene);
                formComViews.get(0).setRealTimeObjectSupplier(new Supplier<ObjectData>() {
                    @Override
                    public ObjectData get() {
                        return getObjectData();
                    }
                });
            }
            //业务类型字段 支持作为父字段
            //特殊插入的业务类型字段需要隐藏掉
            //清除插入业务类型字段后留下的标志位，防止恢复重建时标志位不为空
            if (objectDescribe != null && objectDescribe.getFieldMaps() != null && objectDescribe.getFieldMaps().get
                    (ObjectDataKeys.RECORD_TYPE) != null) {
                objectDescribe.getFieldMaps().get(ObjectDataKeys.RECORD_TYPE).remove(MetaDataUtils
                        .TEMP_RECORD_TYPE_INSERTED_FLAG);
                AbsItemMView recordTypeMV = getFieldModelByFieldName(ObjectDataKeys.RECORD_TYPE);
                if (recordTypeMV != null) {
                    recordTypeMV.hide();
                }
            }
        }
    }

    private void initDefaultOwner(ObjectData objectData){
        if (objectData==null)
            return;
        if (TextUtils.isEmpty(objectData.getOwnerId())){
            User user = MetaDataConfig.getOptions().getAccountService().getCurrentUser();
            if (user != null) {
                List<String> curUserId =
                        new ArrayList<>(Arrays.asList(String.valueOf(user.getId())));
                objectData.put(ObjectDataKeys.OWNER, curUserId);
            }
        }
    }

    /**
     * 更新回填信息
     */
    private void updateBackFillInfos(ObjectDescribe objectDescribe, BackFillInfoIndex backFillInfoIndex) {
        //隐藏字段端无法通过backfill更新值，暂时为兼容已有逻辑，先从backfill中尝试解析数据到objectdata中，之后考再考虑完善的方案
        if (objectDescribe != null && backFillInfoIndex instanceof BackFillInfos) {
            Map<String, BackFillInfo> backFillInfoMap = ((BackFillInfos) backFillInfoIndex).getBackFillInfoMap();
            if (backFillInfoMap != null) {
                if (mOrgObjectData == null) {
                    mOrgObjectData = new ObjectData();
                }
                for (Map.Entry<String, BackFillInfo> entry : backFillInfoMap.entrySet()) {
                    Map<String, Object> fieldMap = objectDescribe.getFieldMaps().get(entry.getKey());
                    BackFillInfo backFillInfo = entry.getValue();
                    if (fieldMap == null || backFillInfo == null)
                        continue;
                    Field field = new Field(fieldMap);
                    FieldType fieldType = field.getFieldType();
                    if (fieldType == FieldType.OBJECT_REFERENCE || fieldType == FieldType.MASTER_DETAIL)
                    {//主要就是为了兼容目前常见的回填lookup字段逻辑
                        String labelKey = entry.getKey()+MetaDataUtils.EXT__R;
                        if (backFillInfo.value instanceof ObjectData) {
                            ObjectData objectData = (ObjectData) backFillInfo.value;
                            mOrgObjectData.put(entry.getKey(), objectData.getID());
                            mOrgObjectData.put(labelKey, objectData.getName());
                        } else if (backFillInfo.value instanceof String) {
                            mOrgObjectData.put(entry.getKey(), backFillInfo.value);
                            mOrgObjectData.put(labelKey, backFillInfo.content);
                        }
                    }else if (fieldType != FieldType.IMAGE && fieldType != FieldType.SIGNATURE){
                        mOrgObjectData.put(entry.getKey(), backFillInfo.value);
                    }
                }
            }
        }
        List<AbsItemMView> modelViewList = getAllFormFieldModelView();
        for (ModelView modelView : modelViewList) {
            if (modelView instanceof BackFillView) {
                ((BackFillView) modelView).updateBackFillInfos(backFillInfoIndex);
            }
        }
    }

    /**触发渲染完成需要进行的计算*/
    private void initCalculateAfterRenderEnd(BackFillInfoIndex backFillInfoIndex) {
        List<String> fields = new ArrayList<>();
        if (backFillInfoIndex instanceof BackFillInfos) {
            fields = MetaModifyUtil.
                    getTriggerCalFieldFromBackFill((BackFillInfos) backFillInfoIndex);
        }

        //新建时负责人字段
        if (mScene == Scene.CREATE) {
            fields.add(ObjectDataKeys.OWNER);
            fields.add(OldCrmObjUtil.OLD_OWNER_KEY1);
            fields.add(OldCrmObjUtil.OLD_OWNER_KEY2);
        }
        //防止如新建关联等场景，回填且只读的字段触发计算又导致自己本身也被计算
        RemoteExpressionExecutor.addCurrentFilterCalFields(getMultiContext(),fields);
        triggerCalculate(fields);
        RemoteExpressionExecutor.removeCurrentFilterCalFields(getMultiContext(),fields);
    }

    /**更新某个字段内容，并主动触发计算，暂时仅支持基本的文本数值类型*/
    public void updateFieldContentAndTriggerCalculate(String fieldName, Object value) {
        if (TextUtils.isEmpty(fieldName)){
            return;
        }
        ObjectData updateData = new ObjectData();
        updateData.put(fieldName, value);
        updateOrgObjectData(updateData);
        RemoteExpressionExecutor.interceptRemoteCalculate(getMultiContext(), true);
        ModelView fieldView = getFieldModelByFieldName(fieldName);
        if (fieldView != null) {
            ((IFormItemView) fieldView).updateContent(value);
        }
        RemoteExpressionExecutor.interceptRemoteCalculate(getMultiContext(), false);
        triggerCalculate(new ArrayList<>(Arrays.asList(fieldName)));
    }

    /**批量更新字段内容，并主动触发计算， 暂时仅支持基本的文本数值类型*/
    public void batchUpdateFieldContentAndTriggerCalculate(Map<String, Object> contentMap) {
        if (contentMap == null || contentMap.isEmpty())
            return;
        RemoteExpressionExecutor.interceptRemoteCalculate(getMultiContext(), true);
        List<IMetaRemoteCalculable> calculableList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : contentMap.entrySet()) {
            ModelView fieldView = getFieldModelByFieldName(entry.getKey());
            if (fieldView != null) {
                ((IFormItemView)fieldView).updateContent(entry.getValue());
            }
            if (fieldView instanceof IGetRemoteCalculate)  {
                calculableList.add(((IGetRemoteCalculate) fieldView).getRemoteCalculate());
            }
        }
        RemoteExpressionExecutor.interceptRemoteCalculate(getMultiContext(), false);
        if (RemoteExpressionExecutor.get(getMultiContext()) != null)  {
            RemoteExpressionExecutor.get(getMultiContext()).requestCalculate(calculableList);
        }
    }

    /**主动触发以这些字段为变量的公式计算*/
    public void triggerCalculate(List<String> parentFields) {
        List<IMetaRemoteCalculable> calculableList = new ArrayList<>();
        for (final String fieldName: parentFields) {
            ModelView fieldView = getFieldModelByFieldName(fieldName);
            if (fieldView == null) {//暂时兼容被隐藏的情况
                if(mObjectDescribe != null && mObjectDescribe.getFieldMaps() != null
                        && mObjectDescribe.getFieldMaps().get(fieldName) != null) {
                    IMetaRemoteCalculable fakeRemoteCalculable = new IMetaRemoteCalculable() {
                        @Override
                        public AbsEditableItemMView getFieldModelView() {
                            return null;
                        }

                        @NonNull
                        @Override
                        public ModelViewGroup getAddOrEditMViewGroup() {
                            return AddOrEditMViewGroup.this;
                        }

                        @NonNull
                        @Override
                        public FormField getFormField() {
                            return getField().to(FormField.class);
                        }

                        @NonNull
                        @Override
                        public Field getField() {
                            Field field1 = new Field();
                            field1.putAll(mObjectDescribe.getFieldMaps().get(fieldName));
                            return field1;
                        }
                    };
                    calculableList.add(fakeRemoteCalculable);
                }
            }else if (fieldView instanceof IGetRemoteCalculate)  {
                calculableList.add(((IGetRemoteCalculate) fieldView).getRemoteCalculate());
            }
        }
        if (RemoteExpressionExecutor.get(getMultiContext()) != null)  {
            RemoteExpressionExecutor.get(getMultiContext()).requestCalculate(calculableList);
        }
    }

    /**
     * 组装保存状态信息
     */
    @Nullable
    @Override
    public Bundle assembleInstanceState() {
        Bundle stateData = super.assembleInstanceState();
        //当前是否显示非必填
        stateData.putBoolean(KEY_SHOW_NOT_REQUIRED, mShowNotRequired);
        stateData.putSerializable(KEY_SAVE_ORG_DATA, mOrgObjectData);
        return stateData;
    }

    /**
     * 恢复销毁前状态
     */
    @Override
    public void restoreInstanceState(@Nullable Bundle savedInstanceState) {
        MetaModifyUtil.interceptBeforeUpdate(getMultiContext(), true);
        super.restoreInstanceState(savedInstanceState);
        if (savedInstanceState != null) {
            boolean showNotRequired = savedInstanceState.getBoolean(KEY_SHOW_NOT_REQUIRED, true);
            showNotRequiredViews(showNotRequired);
            //计算字段结果返回后可能会更新原始数据，因此需要恢复
            ObjectData data = (ObjectData) savedInstanceState.getSerializable(KEY_SAVE_ORG_DATA);
            if (data != null && mOrgObjectData != null) {
                mOrgObjectData.getMap().putAll(data.getMap());
                mOrgObjectData.getExtMap().putAll(data.getExtMap());
            }
        }
        MetaModifyUtil.interceptBeforeUpdate(getMultiContext(), false);
    }

    @Override
    protected void onModelViewsRenderEnd(Collection<ModelView> modelViews) {
        super.onModelViewsRenderEnd(modelViews);
        //只在编辑和新建时设置关联
        if (mScene != Scene.INFO) {
            ModelViewUtils.resetModelViewsRelation(getFieldMViewMap(), getAllFormFieldModelView());
            notifyFormFieldsChangeFirst(getAllFormFieldModelView());
        }
    }

    /**
     * 渲染完成后联动通知一次
     */
    private void notifyFormFieldsChangeFirst(Collection<?extends ModelView> formFieldMVs) {
        if (formFieldMVs != null) {
            for (ModelView formFieldMV : formFieldMVs) {
                if (formFieldMV instanceof ParentObservable) {
                    ((ParentObservable) formFieldMV).notifyChildrenChanged(FIRST_NOTIFY_FLAG);
                }
            }
        }
    }

    /**
     * 获取所有的字段model list
     */
    @NonNull
    public List<AbsItemMView> getAllFormFieldModelView() {
        if (mFieldMViewMap == null || mFieldMViewList == null) {
            getFieldMViewMap();
        }
        return new ArrayList<>(mFieldMViewList);
    }

    /**
     * 获取所有的字段model map
     */
    @NonNull
    public ArrayMap<String, AbsItemMView> getFieldMViewMap() {
        if (mFieldMViewMap == null) {
            mFieldMViewList = new ArrayList<>();
            mFieldMViewMap = new ArrayMap<>();
            LinkedHashMap<String, AbsItemMView> fieldMap = new LinkedHashMap<>();
            List<FormComponentMView> formCompMVs = getFormComView();
            for (FormComponentMView form : formCompMVs) {
                fieldMap.putAll(ModelViewUtils.getFieldMViewMapFromFormMV(form));
            }
            mFieldMViewMap.putAll(fieldMap);
            mFieldMViewList.addAll(new LinkedHashSet<>(fieldMap.values()));//去下重
        }
        ArrayMap<String, AbsItemMView> map = new ArrayMap<>();
        map.putAll((SimpleArrayMap<? extends String, ? extends AbsItemMView>) mFieldMViewMap);
        return map;
    }

    /**
     * 根据字段fieldName获取字段modelView
     */
    @Nullable
    public <T extends AbsItemMView> T getFieldModelByFieldName(String fieldName) {
        AbsItemMView itemMView = getFieldMViewMap().get(fieldName);
        return (T) itemMView;
    }

    @NonNull
    public List<FormComponentMView> getFormComView() {
        List<ModelView> modelViews = getModelViews();
        List<FormComponentMView> result = new ArrayList<>();
        if (modelViews == null) {
            return result;
        }
        for (ModelView modelView : modelViews) {
            if (modelView instanceof FormComponentMView) {
                result.add((FormComponentMView) modelView);
            }
        }
        return result;
    }

    @NonNull
    public ArrayList<GeneralStatePathImageBean> getPicDataList() {
        ArrayList<GeneralStatePathImageBean> dataList = new ArrayList<>();
        Map<String, List<GeneralStatePathImageBean>> picMVData = getPicMViewData();
        for (List<GeneralStatePathImageBean> list : picMVData.values()) {
            dataList.addAll(list);
        }
        return dataList;
    }

    @NonNull
    public Map<String, List<GeneralStatePathImageBean>> getPicMViewData() {
        Map<String, List<GeneralStatePathImageBean>> result = new HashMap<>();
        Map<String, AbsItemMView> formFieldMVs = getFieldMViewMap();
        for (Map.Entry<String, AbsItemMView> entry : formFieldMVs.entrySet()) {
            ModelView mview = entry.getValue();
            if (mview instanceof IGetGeneralStatePathImageBean) {  //图片字段
                IGetGeneralStatePathImageBean picMV = (IGetGeneralStatePathImageBean) mview;
                List<GeneralStatePathImageBean> gspibs = picMV.getGeneralStatePathImageBean();
                if (gspibs != null && !gspibs.isEmpty()) {
                    result.put(entry.getKey(), new ArrayList<>(gspibs));
                }
            }
        }
        return result;
    }

    public IDataCheckerView getFirstNotStandardFieldMView() {
        return getFirstNotStandardFieldMView(true);
    }

    /**
     * @param checkRequired 校验必填
     */
    public IDataCheckerView getFirstNotStandardFieldMView(boolean checkRequired) {
        List<AbsItemMView> formFieldMVs = getAllFormFieldModelView();
        for (ModelView mv : formFieldMVs) {
            if (mv instanceof AbsItemMView) {
                AbsItemMView itemMView = (AbsItemMView) mv;
                if (itemMView.getBackFillInfo() != null && itemMView.getBackFillInfo().notCommit) {
                    continue;
                }
            }
            if (mv instanceof IDataCheckerView && !((IDataCheckerView) mv).isDataStandard(checkRequired)) {
                return (IDataCheckerView) mv;
            }
        }
        return null;
    }

    /**输入校验，通过返 true, 失败会弹窗提醒*/
    public boolean checkInputData(boolean checkRequired){
        IDataCheckerView editableItemView = getFirstNotStandardFieldMView(checkRequired);
        if (editableItemView != null) {
            ModelViewUtils.alertFieldView(getMultiContext().getContext(),
                    (AbsItemMView) editableItemView, editableItemView.getNotStandardDescription());
            return false;
        }
        return true;
    }

    /**红字提醒校验，通过返 true, 失败会弹窗提醒*/
    public boolean checkUiRedRemind(){
        return UiEventExecutor.checkRedRemind(this);
    }

    @NonNull
    public ArrayList<String> getAttachUploadKeys() {
        ArrayList<String> result = new ArrayList<>();
        List<AbsItemMView> formFieldMVs = getAllFormFieldModelView();
        for (ModelView mv : formFieldMVs) {
            if (mv instanceof AttachMView) {
                result.add(((AttachMView) mv).getUploadKey());
            }
        }
        return result;
    }

    /**
     * 从ModelView中获取数据
     */
    @NonNull
    public ObjectData getObjectData() {
        //展示态不支持获取数据
        if (mScene==Scene.INFO){
            return new ObjectData();
        }
        ModelViewController<ComponentViewArg, ComponentViewResult> modelViewController = getModelViewController();
        List<ComponentViewResult> componentViewResults = new ArrayList<>();
        if (modelViewController != null) {
            componentViewResults = modelViewController.getResultList(getModelViews());
        }
        return getObjectData(componentViewResults);
    }

    @NonNull
    private ObjectData getObjectData(List<ComponentViewResult> componentViewResults) {
        Map<String, Object> objectMap = new HashMap<>();
        //覆盖原始数据
        if (mOrgObjectData != null && mOrgObjectData.getMap() != null) {
            objectMap.putAll(mOrgObjectData.getMap());
        }
        for (ComponentViewResult result : componentViewResults) {
            if (result.valuesMap != null) {
                objectMap.putAll(result.valuesMap);
            }
        }

        ObjectData result = new ObjectData(objectMap);
        if (mObjectDescribe != null) {
            result.setObjectDescribeApiName(mObjectDescribe.getApiName());
            result.setObjectDescribeId(mObjectDescribe.getId());
        }
        return result;
    }

    /**
     * 返回入参数据
     */
    public ObjectData getOrgObjectData() {
        return mOrgObjectData;
    }

    /**更新org数据（某些场景需要更新隐藏字段的值，得直接写入orgdata）*/
    public void updateOrgObjectData(ObjectData objectData) {
        if (objectData != null) {
            if (mOrgObjectData == null){
                mOrgObjectData = new ObjectData();
            }
            mOrgObjectData.putAll(objectData.getMap());
            triggerLayoutRule(objectData);//部分隐藏字段更新时需要触发布局规则
        }
    }

    private void triggerLayoutRule(ObjectData updateData){
        if (updateData==null||updateData.getMap()==null){
            return;
        }
        List<FormComponentMView> formComViews = getFormComView();
        if (!formComViews.isEmpty()) {

            formComViews.get(0).triggerLayoutRule(new ArrayList<>(updateData.getMap().keySet()));
        }
    }

    public boolean isShowNotRequired() {
        return mShowNotRequired;
    }

    public void setShowNotRequired(boolean showNotRequired) {
        mShowNotRequired = showNotRequired;
    }

    public void addOuterFieldEditableContainer(IFieldEditableContainer outerFieldEditableContainer) {
        mOuterFieldEditableContainers.add(outerFieldEditableContainer);
    }

    @Override
    public void showNotRequiredViews(boolean show) {
        if (getModelViews() != null) {
            for (ModelView modelView : getModelViews()) {
                if (modelView instanceof IFieldEditableContainer) {
                    ((IFieldEditableContainer) modelView).showNotRequiredViews(show);
                }
            }
        }
        for (IFieldEditableContainer listener : mOuterFieldEditableContainers) {
            listener.showNotRequiredViews(show);
        }
        mShowNotRequired = show;
    }

    protected void showLoading() {
        ILoadingView.ContextImplProxy.showLoading(getContext());
    }

    protected void dismissLoading() {
        ILoadingView.ContextImplProxy.dismissLoading(getContext());
    }
}
