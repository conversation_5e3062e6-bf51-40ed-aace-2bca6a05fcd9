/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.modify.master_detail;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import com.facishare.fs.bpm.data.source.RequestCallBack;
import com.facishare.fs.metadata.utils.MetaDataUtils;

/**
 * 批量触发从数据加载控制, 全部成功后才回调，失败会重试
 * Created by zhouz on 2019-08-13.
 */
public class BatchFetchDetailCtrl {
    List<IModifyDetailFrag> triggerFetchDetail;
    Runnable callBack;
     public BatchFetchDetailCtrl(
            List<IModifyDetailFrag> triggerFetchDetail, Runnable callBack) {
        this.triggerFetchDetail = triggerFetchDetail;
        this.callBack = callBack;
    }

    AtomicInteger counter = new AtomicInteger(0);
    public void trigger(){
        counter.set(triggerFetchDetail.size());
        for (IModifyDetailFrag frag : triggerFetchDetail) {
            triggerFetch(frag);
        }
    }

    private void triggerFetch(final IModifyDetailFrag frag){
        frag.triggerFetchData(new RequestCallBack.ActionCallBack() {
            @Override
            public void onSuccess() {
                if (counter.decrementAndGet() == 0) {
                    callBack.run();
                }
            }

            @Override
            public void onFailed(String error) {
                if (MetaDataUtils.noNet()){
                    return;
                }
                triggerFetch(frag);//失败重试
            }
        });
    }
}
