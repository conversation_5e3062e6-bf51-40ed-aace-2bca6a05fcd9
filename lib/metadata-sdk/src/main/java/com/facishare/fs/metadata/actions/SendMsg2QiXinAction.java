package com.facishare.fs.metadata.actions;

import com.facishare.fs.metadata.actions.basic.ActivityAction;
import com.facishare.fs.modelviews.MultiContext;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.common_beans.ReqType;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.SessionMessageTemp;

import android.app.Activity;
import android.content.Intent;

/**
 * Created by zhouz on 2017/8/2.
 */

public class SendMsg2QiXinAction extends ActivityAction<SessionMessageTemp> {
    private final int RQ_SELECT_SESSION = 0x010;
    private SessionMessageTemp mTarget;
    public SendMsg2QiXinAction(MultiContext context) {
        super(context);
    }

    @Override
    public void start(SessionMessageTemp target) {
        if (target == null)
            return;
        mTarget = target;
        tickBeforeStartActByInterface();
        HostInterfaceManager.getHostInterface().gotoSelectSessionActivityForResult(getActivity(), ReqType
                .TransmitNewMsg, RQ_SELECT_SESSION);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK && requestCode == RQ_SELECT_SESSION) {
            if (data != null) {
                SessionListRec slr = (SessionListRec) data.getSerializableExtra("sessioninfo");
                if (slr != null) {
                    HostInterfaceManager.getISessionMsg().go2SendMsg(getActivity(),slr,1,mTarget);
                }
            }
        }
    }
}
