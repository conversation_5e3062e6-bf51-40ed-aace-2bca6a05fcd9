/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.actions;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.contacts_fs.picker.DepartmentPicker;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.actions.basic.ActivityAction;
import com.facishare.fs.metadata.beans.GetOutPartnerByListResponse;
import com.facishare.fs.metadata.beans.PartnerList;
import com.facishare.fs.metadata.data.source.MetaDataRepository;
import com.facishare.fs.metadata.data.source.MetaDataSource;
import com.facishare.fs.modelviews.MultiContext;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.contact.beans.OutOwner;
import com.facishare.fs.pluginapi.contact.beans.OutTenant;
import com.facishare.fs.pluginapi.contact.beans.SelectSendRangeConfig;
import com.facishare.fs.workflow.utils.Shell;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

/**
 * Author:  wangrz
 * Date:    2019/10/24
 * Remarks: 选择外部负责人
 */

public class MetaDataSelectOutOwnerAction extends ActivityAction {
    /**
     * 名称分割符
     */
    public final static String DEFAULT_SEPARATOR = ",";
    private static final int SELECT_PARTNER_REQUEST_OWNER_CODE = 0x1011;
    private String mTitle = null;//选人标题
    private String mOutTenantId = null;//外部企业id
    private List<PartnerList> mParterList;//源数据列表
    private Map<String, String> mFilterIdNames = null;//要过滤的数据
    private LinkedHashMap<String, String> mSelectedIdNames = null;//已选的数据
    private boolean mIsSingle;//是否单选
    private OnSelectOutOwnerCallBack mCallback;
    private List<String> outPersonsId;//源外部人员,用于回填
    private int mSelectMaxCount;//允许选择的最大数量，小于等于0不生效

    public MetaDataSelectOutOwnerAction(MultiContext context) {
        super(context);
    }


    public MetaDataSelectOutOwnerAction setCallback(OnSelectOutOwnerCallBack callback) {
        mCallback = callback;
        return this;
    }

    public MetaDataSelectOutOwnerAction setTitle(String title) {
        mTitle = title;
        return this;
    }

    public MetaDataSelectOutOwnerAction setOutTenantId(String outTenantId) {
        this.mOutTenantId = outTenantId;
        return this;
    }

    public MetaDataSelectOutOwnerAction isSingle(boolean isSingle) {
        this.mIsSingle = isSingle;
        return this;
    }

    public MetaDataSelectOutOwnerAction selectMaxCount(int selectMaxCount) {
        this.mSelectMaxCount = selectMaxCount;
        return this;
    }

    /**
     * 不设置数据源，会调用接口获取
     * 兼容卡梅龙使用
     * @param partnerList
     * @return
     */
    public MetaDataSelectOutOwnerAction setPartnerList(List<PartnerList> partnerList){
        mParterList = partnerList;
        return this;
    }

    /**
     * 设置已选人员的源数据，用于分离出内部外部人员，以便于调取接口时回填外部人员
     */
    public MetaDataSelectOutOwnerAction setIdList(List<String> idList) {
        List<List<String>> allPersonIdList = Shell.isolatePersons(idList, getContext());
        if (!allPersonIdList.isEmpty()) {
            outPersonsId = allPersonIdList.get(0);
        }
        return this;
    }

    /**
     * 获取外部人员
     * */
    public List<String> getOutPersonsId() {
        return outPersonsId;
    }

    public MetaDataSelectOutOwnerAction setFilterIdNames(Map<String, String> idNames){
        mFilterIdNames = getCopyList(idNames);
        return this;
    }

    public MetaDataSelectOutOwnerAction setSelectedIdNames(Map<String, String> idNames){
        mSelectedIdNames = getCopyList(idNames);
        return this;
    }

    private static LinkedHashMap<String, String> getCopyList(Map<String, String> idNames){
        LinkedHashMap<String, String> copyMap = new LinkedHashMap<>();
        if (idNames != null && !idNames.isEmpty()) {

            for (Map.Entry<String, String> entry : idNames.entrySet()) {
                if(!TextUtils.isEmpty(entry.getKey())) {
                    copyMap.put(entry.getKey(), entry.getValue());
                }
            }
        }
        return copyMap;
    }

    public List<String> getSelectedIds() {
        return getSelectedIds(mSelectedIdNames);
    }

    private List<String> getSelectedIds(Map<String, String> idNames) {
        List<String> rst = new ArrayList<>();
        if(idNames != null && !idNames.isEmpty()) {
            rst.addAll(idNames.keySet());
        }
        return rst;
    }

    private List<String> getSelectedNames(Map<String, String> idNames) {
        List<String> rst = new ArrayList<>();
        if (idNames != null && !idNames.isEmpty()) {
            rst.addAll(idNames.values());
        }
        return rst;
    }

    public LinkedHashMap<String, String> getSelectedIdNames() {
        return getCopyList(mSelectedIdNames);
    }

    public String getNamesStr(Map<String, String> idNames) {
        if (idNames != null && !idNames.isEmpty()) {
            return TextUtils.join(DEFAULT_SEPARATOR, getSelectedNames(idNames));
        }
        return null;
    }

    @Override
    public void start(Object target) {
        if(mParterList == null || mParterList.isEmpty()) {
            showLoading();
            MetaDataRepository.getInstance(getActivity()).getOutPartnerByList(mOutTenantId, 0, 0, 0,
                    new MetaDataSource.GetOutPartnerCallback() {

                        @Override
                        public void onDataLoaded(GetOutPartnerByListResponse response) {
                            dismissLoading();
                            if (response != null && response.getData() != null) {
                                jumpToSelectPartnerOwner(response.getData());
                            }
                        }

                        @Override
                        public void onDataNotAvailable(String error) {
                            dismissLoading();
                            ToastUtils.show(error);
                        }
                    });
        }else {
            jumpToSelectPartnerOwner(mParterList);
        }
    }


    /**
     * 跳转选择合作伙伴负责人
     *
     * @param sourceList 数据源:外部负责人列表
     */
    public void jumpToSelectPartnerOwner(List<PartnerList> sourceList) {
        Map<OutTenant, List<OutOwner>> outTenantMap = transToOutTenantMap(sourceList);
        if (outTenantMap == null || outTenantMap.isEmpty()) {
            ToastUtils
                    .show(I18NHelper.getText("crm.service.MetaDataOperationService.selectOutOwnerListEmpty")/*暂无可用数据*/);
            return;
        }
        SelectSendRangeConfig selectSendRangeConfig = new SelectSendRangeConfig.Builder()
                .setTitle(TextUtils.isEmpty(mTitle) ? I18NHelper.getText("xt.reset_password_act.text.choose_member")/* 选择员工 */ : mTitle )
                .setLastTab(false)
                .setNoSelf(false)
                .setShowEmpTab(false)
                .setShowDepTab(false)
                .setOutTenantMap(outTenantMap)
                .setOnlyChooseOne(mIsSingle)
                .setEmployeeMaxCount(mSelectMaxCount)
                .setShowMeInPrivateMode(false)
                .build();
        tickBeforeStartActByInterface();
        HostInterfaceManager.getHostInterface().gotoSelectSendRangeActivity(mStartActForResult, selectSendRangeConfig,
                SELECT_PARTNER_REQUEST_OWNER_CODE);
    }

    /**
     * 转型选择外部负责人数据格式
     *
     * @param sourceList 数据源:外部负责人列表
     *
     * @return
     */
    private Map<OutTenant, List<OutOwner>> transToOutTenantMap(List<PartnerList> sourceList) {
        if (sourceList == null || sourceList.isEmpty()) {
            return null;
        }
        Map<OutTenant, List<OutOwner>> outTenantMap = new LinkedHashMap<>();
        for (PartnerList partener : sourceList) {
            OutOwner outOwner = null;
            //过滤负责人
            if (mFilterIdNames != null && !mFilterIdNames.isEmpty() && isContainTargetId(mFilterIdNames,
                    partener.getOutUserId())) {
                continue;
            }
            //回填负责人
            if (mSelectedIdNames != null && !mSelectedIdNames.isEmpty() && isContainTargetId(mSelectedIdNames,
                    partener.getOutUserId())) {
                outOwner = new OutOwner();
                outOwner.selected = true;
            }
            if (outOwner == null) {
                outOwner = new OutOwner();
            }
            OutTenant outTenant = new OutTenant();
            outTenant.outTenantId = String.valueOf(partener.getOutTenantId());
            outOwner.id = partener.getOutUserId();
            outOwner.name = partener.getName();
            outOwner.profile = partener.getProfileImage();
            List<OutOwner> outOwnerList = outTenantMap.get(outTenant);
            if (outOwnerList == null) {
                outOwnerList = new ArrayList<>();
                outOwnerList.add(outOwner);
                outTenantMap.put(outTenant, outOwnerList);
            } else {
                outOwnerList.add(outOwner);
            }
        }
        return outTenantMap;
    }

    private boolean isContainTargetId(Map<String, String> idNames, int targetId){
        return idNames != null ? idNames.keySet().contains(String.valueOf(targetId)) : false;
    }

    private LinkedHashMap<String, String> transformToIdNamesList(Map<OutTenant, List<OutOwner>> map) {
        LinkedHashMap<String, String > idNames = new LinkedHashMap<>();
        if (map != null) {
            for (List<OutOwner> outOwnerList : map.values()) {
                for (OutOwner outOwner : outOwnerList) {
                    idNames.put(String.valueOf(outOwner.id), outOwner.name);
                }
            }
        }
        return idNames;
    }

    public void notifyChanged() {
        //选人回调
        if(mCallback != null){
            //TODO wrz 此处带修改
            mCallback.onResult(mSelectedIdNames, null, null);
        }
    }
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == SELECT_PARTNER_REQUEST_OWNER_CODE && resultCode == Activity.RESULT_OK) {//选择合作伙伴负责人
            if (data != null) {
                LinkedHashMap<String, String> idNames = transformToIdNamesList(DepartmentPicker.getOutTenantMapPicked());
                setSelectedIdNames(idNames);
                if(mCallback != null){
                    mCallback.onResult(idNames, DepartmentPicker.getOutTenantMapPicked(), DepartmentPicker.getOutOwnerPicked());
                }
            }
        }
    }

    /**
     * 选人回调
     */
    public interface OnSelectOutOwnerCallBack {
        /**
         * 选人回调结果：三种格式
         *
         * @param idNames           id - name list
         * @param outOwnerGroupList 按企业分组
         * @param outOwnerList      不按企业分组，拍平
         */
        void onResult(LinkedHashMap<String, String> idNames,
                      LinkedHashMap<OutTenant, List<OutOwner>> outOwnerGroupList,
                      List<OutOwner> outOwnerList);
    }
}






