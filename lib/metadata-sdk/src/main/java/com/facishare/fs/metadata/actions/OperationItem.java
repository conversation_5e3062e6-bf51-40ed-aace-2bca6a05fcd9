/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.actions;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.facishare.fs.metadata.R;
import com.facishare.fs.metadata.beans.ButtonOption;

import androidx.annotation.NonNull;
import android.text.TextUtils;

/**
 * 一个操作Item的定义
 *
 * <AUTHOR>
 *         <b>DATE</b> 2016/11/4.
 */

public class OperationItem implements Serializable{
    /**
     * 详情预置按钮
     */
    public static Map<String,OperationItem> detailPreSetOpts = new HashMap<>();
    /**
     * 相关对象列表中 对象操作的预置列表
     */
    public static Map<String,OperationItem> relatedItemOpts = new HashMap<>();

    /**
     * 列表页操作
     */
    public static Map<String,OperationItem> listTopOpts = new HashMap<>();

    public static final String ACTION_TEL = "Dial";          // 打电话
    public static final String ACTION_List = "List";         // 列表
    public static final String ACTION_EMAIl = "SendMail";    // 邮件
    public static final String ACTION_DISCUSS = "Discuss";   // 讨论
    public static final String ACTION_SCHEDULE = "Schedule"; // 日程
    public static final String ACTION_REMIND = "Remind";     // 提醒
    public static final String ACTION_CREATE = "Add";        // 新建
    public static final String ACTION_TRANSFER_CREATE = "TransferAdd";// 转换新建
    public static final String ACTION_SCANCARD = "ScanCard"; // 扫名片
    public static final String ACTION_IMPORT_CONTACT = "ImportFromAddressBook"; // 通讯录导入
    public static final String ACTION_EDIT = "Edit";         // 编辑
    public static final String ACTION_DEL = "Delete";        // 删除
    public static final String ACTION_INVALID = "Abolish";   // 作废
    public static final String ACTION_RECOVER = "Recover";   // 恢复
    public static final String ACTION_SALE_RECORD = "SaleRecord";           //发销售记录
    public static final String ACTION_SERVICE_RECORD = "ServiceRecord";     //发服务记录
    public static final String ACTION_ADD_SALE_RECORD = "AddEvent";         // 相关卡片上的添加销售记录
    public static final String ACTION_CHANGE_OWNER = "ChangeOwner";         //更换负责人
    public static final String ACTION_RELATE = "Relate";                    //关联
    public static final String ACTION_UNRELATE = "DisRelate";               //接触关联
    public static final String ACTION_BULK_RELATE = "BulkRelate";           //批量关联
    public static final String ACTION_BULK_DISRELATE = "BulkDisRelate";//批量解关联
    public static final String ACTION_START_BPM = "StartBPM";//发起流程
    public static final String ACTION_ADD_RELATED_MEMBER = "AddTeamMember";//添加相关团队成员
    public static final String ACTION_EDIT_RELATED_MEMBER = "EditTeamMember";//更新相关团队成员
    public static final String ACTION_DELETE_RELATED_MEMBER = "DeleteTeamMember";//移除相关团队成员
    public static final String ACTION_ADD_SMART_FORM = "IntelligentForm";//新建智能表单
    public static final String ACTION_BATCH_EDITING="BatchEdit";//批量编辑
    public static final String ACTION_DUPLICATE_CHECK = "DuplicateCheckObj";//查重工具入口
    public static final String ACTION_LOCK = "Lock"; //锁定
    public static final String ACTION_UNLOCK = "Unlock"; //解锁
    public static final String ACTION_CUSTOM = "action_custom"; //自定义按钮，终端自己定义的
    public static final String ACTION_CLONE = "Clone";//复制
    public static final String ACTION_ViewLogistics = "ViewLogistics";//查看物流
    public static final String ACTION_ConfirmReceipt = "ConfirmReceipt";//确认收货
    public static final String ACTION_QixinChatGroup = "QixinChatGroup";//业务群
    public static final String ACTION_PRINT = "Print";//打印
    public static final String ACTION_CHANGE_PARTNER = "ChangePartner";//更换合作伙伴
    public static final String ACTION_CHANGE_PARTNER_OWNER = "ChangePartnerOwner";//更换合作伙伴负责人
    public static final String ACTION_DELETE_PARTNER = "DeletePartner";//移除合作伙伴
    public static final String ACTION_ESC_PRINT = "EscPrint";//热敏打印小票
    public static final String ACTION_START_FLOW_STAGE= "StartStagePropellor";//发起流程阶段推进器
    //
    public static final String ACTION_METACHECK_OTHER= "other";//其他
    public static final String ACTION_METACHECK_DETAIL= "detail";//查看详情
    public static final String ACTION_METACHECK_CHOOSE= "choose";//领取
    public static final String ACTION_METACHECK_OWNER= "owner";//联系负责人
    public static final String ACTION_METACHECK_ADMIN= "admin";//联系管理员
    public static final String ACTION_BIZ_SEARCH= "biz_search";//工商查询
    public static final String ACTION_ADD_TEAM_MEMBER = "add_team_member";//成为团队成员
    public static final String ACTION_CHANGE_PARTNER_OWNER2 = "change_partner_owner";//成为外部负责人





    static {
        detailPreSetOpts.put(ACTION_EDIT, new OperationItem(ACTION_EDIT, R.drawable.crm_edit));
        detailPreSetOpts.put(ACTION_SALE_RECORD,new OperationItem(ACTION_SALE_RECORD,R.drawable.bottom_btn_salerecord));
        detailPreSetOpts.put(ACTION_TEL,new OperationItem(ACTION_TEL,R.drawable.bottom_btn_call));
        detailPreSetOpts.put(ACTION_DISCUSS,new OperationItem(ACTION_DISCUSS,R.drawable.bottom_btn_forward));
        detailPreSetOpts.put(ACTION_EMAIl,new OperationItem(ACTION_EMAIl,R.drawable.bottom_btn_email));
        detailPreSetOpts.put(ACTION_CHANGE_OWNER,new OperationItem(ACTION_CHANGE_OWNER,R.drawable.bottom_btn_change_owner));
        detailPreSetOpts.put(ACTION_INVALID,new OperationItem(ACTION_INVALID,R.drawable.bottom_btn_invalid));
        detailPreSetOpts.put(ACTION_LOCK,new OperationItem(ACTION_LOCK,R.drawable.bottom_btn_lock));
        detailPreSetOpts.put(ACTION_UNLOCK,new OperationItem(ACTION_UNLOCK,R.drawable.bottom_btn_unlock));
        detailPreSetOpts.put(ACTION_PRINT,new OperationItem(ACTION_PRINT,R.drawable.bottom_btn_print));
        detailPreSetOpts.put(ACTION_REMIND,new OperationItem(ACTION_REMIND,R.drawable.bottom_btn_remind));
        detailPreSetOpts.put(ACTION_SCHEDULE,new OperationItem(ACTION_SCHEDULE,R.drawable.bottom_btn_schedule));
        detailPreSetOpts.put(ACTION_DEL,new OperationItem(ACTION_DEL,R.drawable.bottom_btn_delete));
        detailPreSetOpts.put(ACTION_RECOVER,new OperationItem(ACTION_RECOVER,R.drawable.bottom_btn_recover));
        detailPreSetOpts.put(ACTION_START_BPM,new OperationItem(ACTION_START_BPM,R.drawable.bottom_btn_start_bpm));
        detailPreSetOpts.put(ACTION_CLONE, new OperationItem(ACTION_CLONE, R.drawable.bottom_btn_clone));
        detailPreSetOpts.put(ACTION_CREATE, new OperationItem(ACTION_CREATE, R.drawable.bottom_btn_clone));
        detailPreSetOpts.put(ACTION_ADD_SALE_RECORD, new OperationItem(ACTION_ADD_SALE_RECORD, R.drawable.bottom_btn_salerecord));
        detailPreSetOpts.put(ACTION_START_FLOW_STAGE, new OperationItem(ACTION_START_FLOW_STAGE, R.drawable.bottom_btn_start_flowstage));
    }

    static {
        relatedItemOpts.put(ACTION_RELATE, new OperationItem(ACTION_RELATE, R.drawable.metadata_obj_relation));
        relatedItemOpts.put(ACTION_CREATE, new OperationItem(ACTION_CREATE, R.drawable.metadata_obj_add));
        relatedItemOpts.put(ACTION_ADD_SMART_FORM, new OperationItem(ACTION_ADD_SMART_FORM, R.drawable.smart_form_add_icon));
    }

    static {
        listTopOpts.put(ACTION_CREATE, new OperationItem(ACTION_CREATE, R.string.barbuttonicon_add));
        listTopOpts.put(ACTION_ADD_SMART_FORM, new OperationItem(ACTION_ADD_SMART_FORM, R.string.smart_form_add_white));
    }

    public String apiName;
    public String action;
    public int res;
    public String text;
    public ButtonOption buttonOption;

    public OperationItem(int res, String text) {
        this.res = res;
        this.text = text;
    }

    public OperationItem(String action, int res) {
        this(null, action, res, null);
    }

    public OperationItem(String action, int res, String text) {
        this(null, action, res, text);
    }

    public OperationItem(String apiName, String action, int res, String text) {
        this.apiName = apiName;
        this.action = action;
        this.text = text;
        this.res = res;
    }

    public OperationItem(ButtonOption buttonOption) {
        this.text = buttonOption.label;
        this.apiName = buttonOption.api_name;
        this.action = buttonOption.action;
        this.buttonOption = buttonOption;
    }

    public int getResId() {
        return res;
    }

    public String getText() {
        return text;
    }

    /**详情页操作*/
    @NonNull
    public static List<OperationItem> getOperationItems(List<ButtonOption> buttonOptions) {
        List<OperationItem> operationItems = new ArrayList<>();
        if (buttonOptions == null || buttonOptions.isEmpty()) {
            return operationItems;
        }
        for (ButtonOption buttonOption : buttonOptions) {
            OperationItem item = getOptionItem(buttonOption);
            if (item != null) {
                if (item.res<=0) {
                    item.res = R.drawable.bottom_btn_custom;
                }
                operationItems.add(item);
            }
            //映射按钮特殊图标
            if (TextUtils.equals(ButtonOption.BUTTON_TYPE_CONVERT, buttonOption.button_type)){

            }
        }
        return operationItems;
    }

    /**相关卡片右上角操作*/
    public static List<OperationItem> getRelatedItemOpts(List<ButtonOption> buttonOptions) {
        List<OperationItem> operationItems = new ArrayList<>();
        if (buttonOptions == null || buttonOptions.isEmpty()) {
            return operationItems;
        }
        for (ButtonOption buttonOption : buttonOptions) {
            OperationItem item = getRelatedListOptionItem(buttonOption);
            if (item != null) {
                operationItems.add(item);
            }
        }
        return operationItems;
    }

    /**列表页标题栏操作*/
    public static List<OperationItem> getListTopOpts(List<ButtonOption> buttonOptions) {
        List<OperationItem> operationItems = new ArrayList<>();
        if (buttonOptions == null || buttonOptions.isEmpty()) {
            return operationItems;
        }
        for (ButtonOption buttonOption : buttonOptions) {
            OperationItem item = getListTopOptionItem(buttonOption);
            if (item != null) {
                operationItems.add(item);
            }
        }
        return operationItems;
    }

    private static OperationItem getOptionItem(ButtonOption buttonOption) {
        return getOptionItemFromList(buttonOption, detailPreSetOpts);
    }

    public static OperationItem getRelatedListOptionItem(ButtonOption buttonOption) {
        return getOptionItemFromList(buttonOption, relatedItemOpts);
    }

    public static OperationItem getListTopOptionItem(ButtonOption buttonOption) {
        return getOptionItemFromList(buttonOption, listTopOpts);
    }

    private static OperationItem getOptionItemFromList(ButtonOption buttonOption,
                                                       Map<String, OperationItem> operationItems) {
        if (buttonOption == null) {
            return null;
        }
        OperationItem operationItem = operationItems.get(buttonOption.action);
        OperationItem copy = new OperationItem(buttonOption);
        if (operationItem != null) {
            copy.res = operationItem.res;
        }
        return copy;
    }
}
