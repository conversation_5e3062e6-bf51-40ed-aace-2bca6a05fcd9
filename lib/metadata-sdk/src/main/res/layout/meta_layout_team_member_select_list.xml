<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_default"
    android:orientation="vertical">

    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_height"/>
    <RelativeLayout
        android:id="@+id/layout_rule"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#f2f2f2"
        android:gravity="center_vertical"
        android:paddingLeft="12dp"
        android:paddingTop="9.5dp"
        android:paddingBottom="9.5dp"
        android:visibility="gone"
            android:paddingStart="12dp">
        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/customer_amount_tv"
            android:textColor="#999999"
            android:textSize="12dp"
            android:layout_marginLeft="4dp"
            i18n:fstext="meta.layout.meta_layout_select_teammember_type_and_permission.2907"
            android:singleLine="true"
            android:paddingRight="66dp"
                android:paddingEnd="66dp"
                android:layout_marginStart="4dp" />
        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/rule_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginRight="12dp"
            i18n:fstext="crm.layout.layout_select_sale_member_type_list.1823"
            android:textColor="#3487e2"
            android:textSize="12dp"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="12dp" />

    </RelativeLayout>

    <com.facishare.fs.sizectrlviews.SizeControlTextView
        android:id="@+id/div_text"
        style="@style/crm_layout_div_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#f2f2f2"
        android:paddingBottom="10dp"
        android:paddingLeft="12dp"
        android:paddingTop="10dp"
        i18n:fstext="crm.layout.layout_select_sale_member_type_list.1824"
        android:textColor="#99A1AD"
        android:textSize="13dp"
        android:visibility="gone"
            android:paddingStart="12dp" />

    <ListView
        android:id="@+id/lv_sales_member_type"
        style="@style/trans_backgroud_listview_style"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="#f2f2f2"
        android:divider="@color/transparence"
        android:dividerHeight="0px" />
    <include
        android:id="@+id/selected_text_layout"
        layout="@layout/item_select_object_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
</LinearLayout>
