<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:i18n="http://schemas.android.com/apk/res-auto"
        android:orientation="vertical"
        android:background="@drawable/metadata_filter_fragment_radius"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:textSize="12sp"
            android:layout_marginRight="15dp"
            android:layout_marginLeft="15dp"
            android:textColor="#616570"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            i18n:fstext="com.crm.metadata.list.top.web.filter.dialog"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp" />
    <ScrollView
            android:id="@+id/meta_data_bottom_filter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="179dp"
            android:maxHeight="396dp"
            android:layout_marginRight="12dp"
            android:layout_marginLeft="12dp"
            android:layout_marginEnd="12dp"
            android:layout_marginStart="12dp">
        <LinearLayout
                android:orientation="vertical"
                android:id="@+id/linear"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
    </ScrollView>
    <View
            android:layout_width="match_parent"
            android:layout_height="9dp"
            android:background="#EBEBEB"/>
    <com.facishare.fs.sizectrlviews.SizeControlButton
            android:id="@+id/meta_data_bottom_close"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            i18n:fstext="cml.crm.detail.close"
            android:gravity="center"
            android:textSize="16sp"
            android:textColor="#181C24"/>

</LinearLayout>