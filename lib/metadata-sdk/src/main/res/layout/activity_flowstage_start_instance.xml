<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:tools="http://schemas.android.com/tools"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                tools:context="com.facishare.fs.bpm.activity.StartInstanceActivity">
    <com.fxiaoke.fscommon_res.common_view.CommonTitleView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_height"/>
    <FrameLayout android:layout_width="match_parent"
                  android:layout_height="match_parent"
                 android:background="@color/bg_default"
                 android:layout_below="@id/title"
                 android:id="@+id/frag_container"/>

</RelativeLayout>