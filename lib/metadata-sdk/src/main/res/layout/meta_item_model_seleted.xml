<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:background="@color/bg_white_1"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
            android:paddingEnd="12dp"
            android:paddingStart="12dp">

        <ImageView
                android:id="@+id/cb_select"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="12dp"
                android:background="@drawable/button_checkbox_on"
                android:layout_marginEnd="12dp" />

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/content_textView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="15dp"
            android:layout_marginRight="12dp"
            android:layout_marginTop="15dp"
            android:layout_weight="1"
            android:singleLine="true"
            android:textColor="@color/font_black_1"
            android:textSize="@dimen/text_size_l"
                android:layout_marginEnd="12dp" />


        <ImageView
            android:id="@+id/icon_image"
            android:layout_width="7dp"
            android:layout_height="12dp"
            android:contentDescription="@string/img_des"
            android:src="@drawable/metadata_list_arrow" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginLeft="12dp"
        android:background="#eeeeee"
            android:layout_marginStart="12dp" />


</LinearLayout>