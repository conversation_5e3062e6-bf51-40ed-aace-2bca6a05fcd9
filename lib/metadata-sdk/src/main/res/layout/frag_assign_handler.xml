<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:paddingBottom="8dp"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.fxiaoke.cmviews.view.TopNoticeView
        android:id="@+id/top_notice"
        android:layout_below="@id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
    <LinearLayout
        android:id="@+id/view_entire_flow"
        android:orientation="horizontal"
        android:background="@drawable/selector_white_bg_gray_ripple"
        android:layout_below="@id/top_notice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <com.facishare.fs.sizectrlviews.SizeControlTextView
            i18n:fstext="crm.bpm.frag_assign_handler.work.flow"
            android:textColor="#333333"
            android:textSize="14dp"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"
            android:paddingTop="15dp"
            android:paddingBottom="15dp"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
                android:paddingStart="12dp"
                android:paddingEnd="12dp" />
        <ImageView
            android:src="@drawable/commonlist_arrow"
            android:layout_gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </LinearLayout>
    <LinearLayout
        android:orientation="vertical"
        android:layout_marginTop="12dp"
        android:layout_below="@id/view_entire_flow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal">

            <com.facishare.fs.sizectrlviews.SizeControlTextView
                    android:id="@+id/bpm_container_tip"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:padding="12dp"
                    i18n:fstext="crm.bpm.frag_assign_handler.choose.mode"
                    android:textColor="#3b4047"
                    android:textSize="15dp"/>

        </LinearLayout>
        <LinearLayout
                android:id="@+id/container"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
    </LinearLayout>
    <com.fxiaoke.cmviews.view.DrawableCenterTextView
        style="@style/bpm_task_button_yellow_fill_no_icon"
        android:id="@+id/select_people"
        android:layout_alignParentBottom="true"
        android:visibility="visible"
        i18n:fstext="meta.layout.frag_assign_handler.2941"/>
</RelativeLayout>