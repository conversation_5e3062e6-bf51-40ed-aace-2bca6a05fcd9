<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:orientation="horizontal"
              android:gravity="center_horizontal"
              android:padding="12dp">
    <LinearLayout
            style="@style/model_views_layout_style"
            android:layout_weight="1"
            android:layout_width="0dp">
        <com.facishare.fs.metadata.commonviews.NotSaveStateTextView
                android:id="@id/title"
                style="@style/model_views_title_style"
                i18n:fstext="meta.layout.meta_item_info_view.2911"/>
        <com.facishare.fs.metadata.commonviews.NotSaveStateTextView
                android:id="@id/help_text"
                style="@style/model_views_description_style"
                android:visibility="gone"
        />

        <com.facishare.fs.metadata.commonviews.NotSaveStateTextView
                android:id="@id/content"
                style="@style/model_views_content_style"/>
    </LinearLayout>
    <LinearLayout
            android:id="@id/action_container"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:layout_gravity="center_vertical"
            android:orientation="horizontal">
    </LinearLayout>
    <ImageView
        android:id="@id/arrow_img"
        android:visibility="gone"
        style="@style/model_view_arrow_style" />
</LinearLayout>