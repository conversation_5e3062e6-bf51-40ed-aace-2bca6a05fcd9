<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#ffffff">

    <LinearLayout
        android:id="@+id/layout_container"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@drawable/invitation_no_semicircle_selector"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="12dp"
            android:paddingStart="12dp">

        <ImageView
            android:id="@+id/checkbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="6dp"
            android:src="@drawable/image_check_box"
                android:layout_marginEnd="6dp" />

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/text_type_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            i18n:fstext="meta.layout.meta_layout_select_teammember_type_and_permission.2909" />
    </LinearLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="12dp"
        android:background="#eeeeee"
            android:layout_marginStart="12dp" />
</LinearLayout>
