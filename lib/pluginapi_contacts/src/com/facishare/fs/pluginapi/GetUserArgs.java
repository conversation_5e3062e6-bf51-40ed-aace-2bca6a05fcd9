/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.pluginapi;

/**
 * Created by anjx on 2016/4/5.
 */
public class GetUserArgs {

    private int mId;

    public int getId() {
        return mId;
    }

    public static class Builder {
        private int mId;

        public Builder setId(int id) {
            mId = id;
            return this;
        }

        public GetUserArgs build() {
            GetUserArgs args = new GetUserArgs();
            args.mId = this.mId;
            return args;
        }
    }

}
