package com.facishare.fs.pluginapi.contact;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.pluginapi.ContactsHostManager;
import com.facishare.fs.pluginapi.contact.beans.Organization;
import com.facishare.fs.pluginapi.contact.beans.User;

import java.util.List;

/**
 * Created by anjx on 2015/12/22.
 */
public abstract class ContactWebViewActivity extends Activity {

    protected WebView mWebView;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

    }

    protected void initWebView() {
        mWebView = (WebView) findViewById(getWebViewId());
        mWebView.setWebChromeClient(new WebChromeClient(){

        });

        mWebView.setWebViewClient(new WebViewClient(){
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }

        });

        WebSettings settings = mWebView.getSettings();
        settings.setJavaScriptEnabled(true);
        settings.setCacheMode(WebSettings.LOAD_NO_CACHE);
        mWebView.addJavascriptInterface(new EmptyJavaInterface(), "isAndroid");
        mWebView.addJavascriptInterface(new ContactJavascriptInterface(this, mWebView), "contact");
    }

    private class EmptyJavaInterface {

        @JavascriptInterface
        public void isAndroid() {

        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == ContactJavascriptInterface.REQUEST_CODE_SELECT_EMP) {
            if (resultCode == RESULT_OK) {

                JSONObject jo = new JSONObject();
                List<User> selectedUser = ContactsHostManager.getContacts().getSelectedUser();
                if (selectedUser != null && selectedUser.size() > 0) {
                    JSONArray joPeopleArray = new JSONArray();
                    for (User user : selectedUser) {
                        JSONObject joPeople = new JSONObject();
                        joPeople.put("peopleName", user.getName());
                        joPeople.put("peopleID", user.getId());
                        joPeople.put("headImageUrl", user.getImageUrl());
                        joPeopleArray.add(joPeople);
                    }
                    jo.put("selectPeoples", joPeopleArray);
                }
                List<Organization> selectedOrg = ContactsHostManager.getContacts().getSelectedOrg();
                if (selectedOrg != null && selectedOrg.size() > 0) {
                    JSONArray joOrgArray = new JSONArray();
                    for (Organization org : selectedOrg) {
                        JSONObject joOrg = new JSONObject();
                        joOrg.put("cricleName", org.getI18NName());
                        joOrg.put("cricleID", org.getId());
                        joOrgArray.add(joOrg);
                    }
                    jo.put("selectCircles", joOrgArray);
                }

                mWebView.loadUrl("javascript:contact.selectEmpSucceed( eval(" + jo.toJSONString() +
                        "))");

            } else {
                mWebView.loadUrl("javascript:contact.selectEmpCancel()");
            }
        }
    }

    protected abstract int getWebViewId();
}