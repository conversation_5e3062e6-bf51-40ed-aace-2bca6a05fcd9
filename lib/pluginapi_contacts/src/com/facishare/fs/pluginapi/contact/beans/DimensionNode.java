package com.facishare.fs.pluginapi.contact.beans;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 维度树节点
 * Created by zhouz on 2020/10/22.
 */
public class DimensionNode implements Serializable {
    private static final long serialVersionUID = -18684867235872903L;
    private String id;//多维度ID
    private String name;//多维度名称
    @JSONField(serialize = false, deserialize = false)
    private DimensionNode parentNode;//父节点
    @JSONField(serialize = false, deserialize = false)
    private List<DimensionNode> children=new ArrayList<>();//子节点

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @JSONField(serialize = false, deserialize = false)
    public DimensionNode getParentNode() {
        return parentNode;
    }

    @JSONField(serialize = false, deserialize = false)
    public void setParentNode(DimensionNode parentNode) {
        this.parentNode = parentNode;
    }

    @JSONField(serialize = false, deserialize = false)
    public List<DimensionNode> getChildren() {
        return children;
    }

    @JSONField(serialize = false, deserialize = false)
    public void setChildren(List<DimensionNode> children) {
        this.children = children;
    }

    public DimensionNode cloneNoRelation(){
        DimensionNode node = new DimensionNode();
        node.id=this.id;
        node.name=this.name;
        return node;
    }
}
