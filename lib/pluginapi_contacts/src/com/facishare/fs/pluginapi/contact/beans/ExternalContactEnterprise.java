/*
 * Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.pluginapi.contact.beans;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Created by pangc on 2018/7/30.
 */

public class ExternalContactEnterprise implements Serializable {

    public ExternalContactEnterprise() {

    }

    /**
     * 公司账号
     */
    private String extEnterpriseAccount;

    /**
     * 公司简称
     */
    private String extCompanyShortName;

    /**
     * 公司全称
     */
    private String extCompanyName;

    /**
     * 公司头像
     */
    private String extCompanyProfile;

    /**
     * 公司全称拼音
     */
    private String extCompanyNameSpell;

    /**
     * 公司简称拼音
     */
    private String extCompanyShortNameSpell;

    @JSONField(name = "M1")
    public String getExtEnterpriseAccount() {
        return extEnterpriseAccount;
    }

    @JSONField(name = "M1")
    public void setExtEnterpriseAccount(String extEnterpriseAccount) {
        this.extEnterpriseAccount = extEnterpriseAccount;
    }

    @JSONField(name = "M2")
    public String getExtCompanyShortName() {
        return extCompanyShortName;
    }

    @JSONField(name = "M2")
    public void setExtCompanyShortName(String extCompanyShortName) {
        this.extCompanyShortName = extCompanyShortName;
    }

    @JSONField(name = "M3")
    public String getExtCompanyName() {
        return extCompanyName;
    }

    @JSONField(name = "M3")
    public void setExtCompanyName(String extCompanyName) {
        this.extCompanyName = extCompanyName;
    }

    @JSONField(name = "M4")
    public String getExtCompanyProfile() {
        return extCompanyProfile;
    }

    @JSONField(name = "M4")
    public void setExtCompanyProfile(String extCompanyProfile) {
        this.extCompanyProfile = extCompanyProfile;
    }

    @JSONField(name = "M5")
    public String getExtCompanyNameSpell() {
        return extCompanyNameSpell;
    }

    @JSONField(name = "M5")
    public void setExtCompanyNameSpell(String extCompanyNameSpell) {
        this.extCompanyNameSpell = extCompanyNameSpell;
    }

    @JSONField(name = "M6")
    public String getExtCompanyShortNameSpell() {
        return extCompanyShortNameSpell;
    }

    @JSONField(name = "M6")
    public void setExtCompanyShortNameSpell(String extCompanyShortNameSpell) {
        this.extCompanyShortNameSpell = extCompanyShortNameSpell;
    }
}
