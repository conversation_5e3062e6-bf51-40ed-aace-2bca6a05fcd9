/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.pluginapi.contact.beans.coustomer;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * Created by 钟光燕 on 2016/3/30.
 * ===================================================
 * <p/>
 * code is m h l
 * <p/>
 * ===================================================
 */
public class CustomerStatue {

    //部分类型

    private String moduleKey;
    //状态 1,有更新，2.无更新

    private int status;

    @JSONField(name = "M1")
    public String getModuleKey() {
        return moduleKey;
    }

    @JSONField(name = "M1")
    public void setModuleKey(String moduleKey) {
        this.moduleKey = moduleKey;
    }

    @JSONField(name = "M2")
    public int getStatus() {
        return status;
    }

    @JSONField(name = "M2")
    public void setStatus(int status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "UpdateStatue{" +
                "moduleKey='" + moduleKey + '\'' +
                ", status=" + status +
                '}';

    }
}
