package com.facishare.fs.pluginapi.contact.beans;

import com.lidroid.xutils.db.annotation.Id;
import com.lidroid.xutils.db.annotation.NoAutoIncrement;
import com.lidroid.xutils.db.annotation.Table;

import java.io.Serializable;

/**
 * 仅用做数据库实体，不作为服务器接口数据结构
 */
@Table(name = "StopEmployeeEntity")
public class StopEmpEntity implements Cloneable, Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 3915278101288179136L;
    /**
     * 员工ID
     */
    @Id
    @NoAutoIncrement
    public int employeeID;

    /**
     * 昵称
     */
    public String name;
    /**
     * 头像文件路径
     */
    public String profileImage;
    /**
     * 岗位
     */
    public String post;

    /**
     * 昵称拼音首字母
     */
    public String nameSpell;

    /**
     * 昵称排序
     */
    public String nameOrder;

    /**
     * 停用时间
     */
   public int stopTime;

    /**
     * 状态
     */
   public int status;

    /**
     * 范围
     */
   public int range;

   /*
    *用户主部门ID
    * */
   public int mainDepartment;

   //是否选中
   public boolean isSelect;

    /**
     * 部门，id以字符串分割
     */
    public String department;


    public AEmpSimpleEntity toEmpEntiry(){
        AEmpSimpleEntity entity = new AEmpSimpleEntity();
        entity.employeeID= employeeID;
        entity.name= name;
        entity.profileImage= profileImage;
        entity.post= post;
        entity.setNameSpell(nameSpell);
        entity.setNameOrder(nameOrder);
        entity.setMainDepartment(mainDepartment);
        entity.setDepartment(department);
        entity.setStatus(status);
        entity.setRange(range);
        return entity;
    }

}

