/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.pluginapi.contact.beans;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * SelectSendRangeActivity界面配置
 * Created by wangyp on 2017/3/22.
 */

public class SelectSendRangeConfig<T> implements Serializable {

    public String title = null;//title显示
    public boolean noSelf = false;// 默认为false,包含自己
    public boolean isLastTab = false;// 是否显示最近
    public boolean isLoadLastData = true;//是否显示最近常用数据 回执范围需要一个最近的页签,但是不加载常用数据
    public boolean isShowDepTab = true;//是否显示部门tab
    public boolean isShowEmpTab = true;//是否显示员工tab
    public boolean isGrouptab = false;//是否显示群组tab
    public boolean isShowStopEmpTob = false;//是否显示已停用员工Tab
    public boolean isShowRoleTab = false;//是否显示角色Tab
    public boolean isShowUserGroupTab = false;//是否显示用户组Tab
    public boolean isSelectOutTeam = false;//是否是选择外部团队
    public boolean isShowOutPerson = false;//是否显示外部人员
    public boolean isShowOutEnterprise = false;//是否显示外部企业
    public boolean isShowOutOppositePerson =  false;//是否显示外部对接人

    public int employeeMaxCount = 0;//员工最大数
    public String employeeCountExceedTip;//超出人数的提示 目前还未传到下一层 支持不充分

    public boolean showMeInPrivateMode = true;//只选一个人时，是否将当前用户显示为私密
    public boolean showTitleResetButton = false;//是否显示标题栏右侧的重置按钮

    public SendRangeData sendRangeData = null; //快捷：发送范围（自定义了里面的数据）
    public SendRangeData atRangeData = null;//快捷：at范围 例如：选2个at人，在进入回执选人页
    public Map<Integer, String> empsMap = null; //回填员工
    public Map<Integer, String> depsMap = null; //回填部门
    public Map<String, Boolean> groupMap = null;//回填群组
    public Map<Integer, String> stopEmps = null;//回填已停用员工
    public List<OutOppositePersonDataBean> selectOutOppositePersions=null;//已选的下游对接人

    public List<AddTabData> addTabDatas = null;//新添加tab类及其数据
    public SendRangeData noSelectData = null;//锁定的员工和部门、组织，不能选择或反选
    public SendRangeData hideData = null;//隔离的员工和部门

    public Map<OutTenant, List<OutOwner>> outTenantMap = null;//外部租户
    public List<OutTenant> OutTenantList = null;//外部企业
    public String outOwnerTabTitle = null;//外部人员 tab title显示
    public String outTenantTabTitle = null;//外部企业 tab title显示
    public String innerTabTitle = null;//内部人员tab title显示
    public String roleTabTitle = null;//角色Tab  title显示
    public String userGroupTabTitle = null;//用户组Tab  title显示
    public String outPersonTabTitle = null;//外部人员tab title显示
    public String outEnterpriseTabTitle = null;//外部企业tab title显示
    public String outOppositePersonTabTitle = null;//外部对接人title

    public CSDataConfig csDataConfig = CSDataConfig.builder().build();//控制最近tab下内容显示的配置
    public int showHasEmail = -1;//显示有email的员工列表   add by wubb
    public int showHasPhone = -1;//显示有手机号的员工列表   add by wubb

    /**
     * 员工和部门显示选择控制
     */
    public boolean isInCustomMode = false;//是否是自定义模式，自定义模式可以支持传入自定义的用户列表和自定义的部门列表
    public boolean isHideDepLevel = false;//是否隐藏组织架构
    public ArrayList<Integer> customEmpIds = null;//传入的emptIds
    public ArrayList<Integer> customDepIds = null;//传入的deptIds
    public ArrayList<Integer> filterEmpIds = null;//需要过滤掉的员工
    public ArrayList<Integer> filterDepIds = null;//需要过滤掉的部门
    public ArrayList<Integer> whiteStopEmpIds = null;//已停用员工白名单
    public ArrayList<Integer> blackStopEmpIds = null;//已停用员工黑名单
    public ArrayList<String> filterOutEmpIds = null;//需要过滤的外部人员

    public ConfirmChecker mConfirmChecker;//点确定时的校验逻辑
    /**
     * 是否单选
     */
    public boolean mOnlyChooseOne = false;

    public List<T> mapToList(Map<T, String> maps) {
        List<T> temps = new ArrayList<>();
        if (maps != null && maps.size() > 0) {
            temps.addAll(maps.keySet());
        }
        return temps;
    }

    public boolean isNoSelectEmp(int id) {
        if (noSelectData != null
                && noSelectData.getSelectEmp() != null
                && noSelectData.getSelectEmp().size() > 0) {

            for (int emp : noSelectData.getSelectEmp().keySet()) {
                if (emp == id) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isNoSelectDep(int id) {
        if (noSelectData != null
                && noSelectData.getSelectDep() != null
                && noSelectData.getSelectDep().size() > 0) {

            for (int dep : noSelectData.getSelectDep().keySet()) {
                if (dep == id) {
                    return true;
                }
            }
        }
        return false;
    }

    public static Builder builder() {
        return new Builder();
    }

    private SelectSendRangeConfig(Builder builder) {
        this.title = builder.title;
        this.noSelf = builder.noSelf;
        this.isLastTab = builder.isLastTab;
        this.isLoadLastData = builder.isLoadLastData;
        this.isGrouptab = builder.isGrouptab;
        this.isShowDepTab = builder.isShowDepTab;
        this.isShowEmpTab = builder.isShowEmpTab;
        this.isShowStopEmpTob = builder.isShowStopEmpTob;
        this.isShowRoleTab = builder.isShowRoleTab;
        this.isShowUserGroupTab = builder.isShowUserGroupTab;
        this.isSelectOutTeam = builder.isSelectOutTeam;
        this.filterOutEmpIds = builder.filterOutEmpIds;

        this.isShowOutPerson = builder.isShowOutPerson;
        this.isShowOutEnterprise = builder.isShowOutEnterprise;
        this.isShowOutOppositePerson = builder.isShowOutOppositePerson;
        this.outOppositePersonTabTitle = builder.outOppositePersonTabTitle;
        this.outPersonTabTitle = builder.outPersonTabTitle;
        this.outEnterpriseTabTitle = builder.outEnterpriseTabTitle;

        this.employeeMaxCount = builder.employeeMaxCount;
        this.employeeCountExceedTip = builder.employeeCountExceedTip;

        this.showMeInPrivateMode = builder.showMeInPrivateMode;
        this.showTitleResetButton = builder.showTitleResetButton;

        this.sendRangeData = builder.sendRangeData;
        this.atRangeData = builder.atRangeData;
        this.empsMap = builder.empsMap;
        this.depsMap = builder.depsMap;
        this.groupMap = builder.groupMap;
        this.stopEmps = builder.stopEmpsMap;
        this.addTabDatas = builder.addTabDatas;
        this.noSelectData = builder.noSelectData;
        this.hideData = builder.hideData;
        this.selectOutOppositePersions = builder.selectOutOppositePersions;

        this.outTenantMap = builder.outTenantMap;
        this.OutTenantList = builder.OutTenantList;
        this.outOwnerTabTitle = builder.outOwnerTabTitle;
        this.outTenantTabTitle = builder.outTenantTabTitle;
        this.innerTabTitle = builder.innerTabTitle;
        this.roleTabTitle = builder.roleTabTitle;
        this.userGroupTabTitle = builder.userGroupTabTitle;

        this.csDataConfig = builder.csDataConfig;
        this.showHasEmail = builder.showHasEmail;
        this.showHasPhone = builder.showHasPhone;
        this.isInCustomMode = builder.isInCustomMode;
        this.customEmpIds = builder.customEmpIds;
        this.customDepIds = builder.customDepIds;
        this.filterEmpIds = builder.filterEmpIds;
        this.filterDepIds = builder.filterDepIds;
        this.isHideDepLevel = builder.isHideDepLevel;
        this.whiteStopEmpIds = builder.whiteStopEmpIds;
        this.blackStopEmpIds = builder.blackStopEmpIds;

        this.mConfirmChecker = builder.mConfirmChecker;
        this.mOnlyChooseOne = builder.mOnlyChooseOne;
    }

    public static class Builder {
        private String title = null;
        private boolean noSelf = false;
        private boolean isLastTab = false;
        private boolean isLoadLastData = true;
        private boolean isShowDepTab = true;
        private boolean isShowEmpTab = true;
        private boolean isGrouptab = false;
        private boolean isShowStopEmpTob = false;//是否显示已停用员工Tab
        private boolean isShowRoleTab = false;//是否显示角色TAB
        private boolean isShowUserGroupTab = false;//是否显示用户组TAB
        private boolean isSelectOutTeam = false;//是否是选择外部团队

        private String outPersonTabTitle = null;//外部人员tab title显示
        private String outEnterpriseTabTitle = null;//外部企业tab title显示
        private String outOppositePersonTabTitle = null;//外部对接人title
        private boolean isShowOutPerson = false;//是否显示外部人员
        private boolean isShowOutEnterprise = false;//是否显示外部企业
        private boolean isShowOutOppositePerson = false;//是否显示外部对接人
        public ArrayList<String> filterOutEmpIds = null;//需要过滤的外部人员

        private boolean showMeInPrivateMode = true;//只选一个人时，是否将当前用户显示为私密
        private boolean showTitleResetButton = false;//是否显示标题栏右侧的重置按钮

        private int employeeMaxCount = 0;//最大员工数
        private String employeeCountExceedTip;//员工最大数

        private SendRangeData sendRangeData = null;
        private SendRangeData atRangeData = null;
        private Map<Integer, String> empsMap = null;
        private Map<Integer, String> depsMap = null;
        private Map<Integer, String> stopEmpsMap = null;
        private Map<String, Boolean> groupMap = null;
        private List<AddTabData> addTabDatas = null;
        private List<OutOppositePersonDataBean> selectOutOppositePersions=null;
        private SendRangeData noSelectData = null;
        private SendRangeData hideData = null;

        public Map<OutTenant, List<OutOwner>> outTenantMap = null;//外部人员
        public List<OutTenant> OutTenantList = null;//外部企业
        public String outOwnerTabTitle = null;//外部人员 tab title显示
        public String outTenantTabTitle = null;//外部企业 tab title显示
        public String innerTabTitle = null;//内部人员tab title显示
        public String roleTabTitle = null;//角色Tab  title显示
        public String userGroupTabTitle = null;//用户组Tab  title显示

        private CSDataConfig csDataConfig = CSDataConfig.builder().build();
        private int showHasEmail = -1;
        private int showHasPhone = -1;
        private boolean isInCustomMode = false;
        private boolean isHideDepLevel = false;
        private ArrayList<Integer> customEmpIds = null;
        private ArrayList<Integer> customDepIds = null;
        private ArrayList<Integer> filterEmpIds = null;
        private ArrayList<Integer> filterDepIds = null;
        public ArrayList<Integer> whiteStopEmpIds = null;//已停用员工白名单
        public ArrayList<Integer> blackStopEmpIds = null;//已停用员工黑名单

        private ConfirmChecker mConfirmChecker;//点确定时的校验逻辑

        public boolean mOnlyChooseOne = false;

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setNoSelf(boolean noSelf) {
            this.noSelf = noSelf;
            return this;
        }

        public Builder setLastTab(boolean lastTab) {
            this.isLastTab = lastTab;
            return this;
        }

        public Builder setFilterOutEmpIds(ArrayList<String> filterOutEmpIds) {
            this.filterOutEmpIds = filterOutEmpIds;
            return this;
        }

        public Builder setOutPersonTabTitle(String outPersonTabTitle) {
            this.outPersonTabTitle = outPersonTabTitle;
            return this;
        }

        public Builder setOutEnterpriseTabTitle(String outEnterpriseTabTitle) {
            this.outEnterpriseTabTitle = outEnterpriseTabTitle;
            return this;
        }

        public Builder setSelectOutOppositePersions(
                List<OutOppositePersonDataBean> selectOutOppositePersions) {
            this.selectOutOppositePersions = selectOutOppositePersions;
            return this;
        }

        public Builder setOutOppositePersonTabTitle(String outOppositePersonTabTitle) {
            this.outOppositePersonTabTitle = outOppositePersonTabTitle;
            return this;
        }

        public Builder setShowOutPerson(boolean showOutPerson) {
            isShowOutPerson = showOutPerson;
            return this;
        }

        public Builder setShowOutEnterprise(boolean showOutEnterprise) {
            isShowOutEnterprise = showOutEnterprise;
            return this;
        }

        public Builder setShowOutOppositePerson(boolean showOutOppositePerson){
            isShowOutOppositePerson = showOutOppositePerson;
            return this;
        }

        public Builder setLoadLastData(boolean loadLastData) {
            this.isLoadLastData = loadLastData;
            return this;
        }

        public Builder setGrouptab(boolean grouptab) {
            this.isGrouptab = grouptab;
            return this;
        }

        public Builder setSelectOutTeam(boolean selectOutTeam) {
            isSelectOutTeam = selectOutTeam;
            return this;
        }

        public Builder setShowDepTab(boolean showDepTab) {
            isShowDepTab = showDepTab;
            return this;
        }

        public Builder setShowEmpTab(boolean showEmpTab) {
            isShowEmpTab = showEmpTab;
            return this;
        }

        public Builder setShowStopEmpTob(boolean showStopEmpTob) {
            isShowStopEmpTob = showStopEmpTob;
            return this;
        }

        public Builder setShowRoleTab(boolean showRoleTab) {
            isShowRoleTab = showRoleTab;
            return this;
        }

        public Builder setRoleTabTitle(String roleTabTitle) {
            this.roleTabTitle = roleTabTitle;
            return this;
        }

        public Builder setShowUserGroupTab(boolean isShowUserGroupTab) {
            this.isShowUserGroupTab = isShowUserGroupTab;
            return this;
        }

        public Builder setUserGroupTabTitle(String userGroupTabTitle) {
            this.userGroupTabTitle = userGroupTabTitle;
            return this;
        }

        public Builder setSendRangeData(SendRangeData sendRangeData) {
            this.sendRangeData = sendRangeData;
            return this;
        }

        public Builder setAtRangeData(SendRangeData atRangeData) {
            this.atRangeData = atRangeData;
            return this;
        }

        public Builder setEmpsMap(Map<Integer, String> empsMap) {
            this.empsMap = empsMap;
            return this;
        }

        public Builder setStopEmpMap(Map<Integer, String> stopEmps) {
            this.stopEmpsMap = stopEmps;
            return this;
        }

        public Builder setDepsMap(Map<Integer, String> depsMap) {
            this.depsMap = depsMap;
            return this;
        }

        public Builder setGroupMap(Map<String, Boolean> groupMap) {
            this.groupMap = groupMap;
            return this;
        }

        public Builder setOutTenantMap(Map<OutTenant, List<OutOwner>> outTenants) {
            this.outTenantMap = outTenants;
            return this;
        }

        public Builder setOutTenantList(List<OutTenant> outTenantList) {
            this.OutTenantList = outTenantList;
            return this;
        }

        public Builder setOutOwnerTabTitle(String outOwnerTabTitle) {
            this.outOwnerTabTitle = outOwnerTabTitle;
            return this;
        }

        public Builder setInnerTabTitle(String innerTabTitle) {
            this.innerTabTitle = innerTabTitle;
            return this;
        }

        public Builder setOutTenantTabTitle(String outTenantTabTitle) {
            this.outTenantTabTitle = outTenantTabTitle;
            return this;
        }

        public Builder setCsDataConfig(CSDataConfig csDataConfig) {
            this.csDataConfig = csDataConfig;
            return this;
        }

        public Builder setShowHasEmail(int showHasEmail) {
            this.showHasEmail = showHasEmail;
            return this;
        }

        public Builder setShowHasPhone(int showHasPhone) {
            this.showHasPhone = showHasPhone;
            return this;
        }

        public Builder setInCustomMode(boolean inCustomMode) {
            this.isInCustomMode = inCustomMode;
            return this;
        }

        public Builder setHideDepLevel(boolean hideDepLevel) {
            isHideDepLevel = hideDepLevel;
            return this;
        }

        public Builder setCustomEmpIds(ArrayList<Integer> customEmpIds) {
            this.customEmpIds = customEmpIds;
            return this;
        }

        public Builder setCustomDepIds(ArrayList<Integer> customDepIds) {
            this.customDepIds = customDepIds;
            return this;
        }

        public Builder setAddTabDatas(List<AddTabData> addTabDatas) {
            this.addTabDatas = addTabDatas;
            return this;
        }

        public Builder setNoSelectData(SendRangeData noSelectData) {
            this.noSelectData = noSelectData;
            return this;
        }

        public Builder setHideData(SendRangeData hideData) {
            this.hideData = hideData;
            return this;
        }

        public Builder setFilterEmpIds(ArrayList<Integer> filterEmpIds) {
            this.filterEmpIds = filterEmpIds;
            return this;
        }

        public Builder setFilterDepIds(ArrayList<Integer> filterDepIds) {
            this.filterDepIds = filterDepIds;
            return this;
        }

        public Builder setShowMeInPrivateMode(boolean showMeInPrivateMode) {
            this.showMeInPrivateMode = showMeInPrivateMode;
            return this;
        }

        public Builder setShowTitleResetButton(boolean showTitleResetButton) {
            this.showTitleResetButton = showTitleResetButton;
            return this;
        }

        public Builder setConfirmChecker(ConfirmChecker confirmChecker) {
            mConfirmChecker = confirmChecker;
            return this;
        }

        public Builder setWhiteStopEmpIds(ArrayList<Integer> whiteStopEmpIds) {
            this.whiteStopEmpIds = whiteStopEmpIds;
            return this;
        }

        public Builder setBlackStopEmpIds(ArrayList<Integer> blackStopEmpIds) {
            this.blackStopEmpIds = blackStopEmpIds;
            return this;
        }

        public Builder setEmployeeMaxCount(int maxCount) {
            this.employeeMaxCount = maxCount;
            return this;
        }

        public Builder setEmployeeCountExceedTip(String employeeCountExceedTip) {
            this.employeeCountExceedTip = employeeCountExceedTip;
            return this;
        }

        public Builder setOnlyChooseOne(boolean onlyChooseOne) {
            mOnlyChooseOne = onlyChooseOne;
            return this;
        }

        public SelectSendRangeConfig build() { // 构建，返回一个新对象
            return new SelectSendRangeConfig(this);
        }

    }
}
