/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fs.trainhelper.docpreviewlib.docpreview.interfaces;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Date;

import com.alibaba.fastjson.TypeReference;
import com.fs.trainhelper.docpreviewlib.docpreview.beans.CommonResult;
import com.fs.trainhelper.docpreviewlib.docpreview.beans.TrainhelperResult;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.lib.pay.constants.ErrorCode;

import android.text.TextUtils;

/**
 * Created by zhaozp on 2016/3/23.
 */
public abstract class TrainhelperHttpCallback<T extends CommonResult> extends
        WebApiExecutionCallback<TrainhelperResult> {
    private static final String TAG = "TrainhelperHttpCallback";

    public abstract void onSuccess(T result);

    @Override
    public Class<TrainhelperResult> getTypeReferenceFHE() {
        return TrainhelperResult.class;
    }

    @Override
    public TypeReference<WebApiResponse<TrainhelperResult>> getTypeReference() {
        return new TypeReference<WebApiResponse<TrainhelperResult>>() {
        };
    }

    @Override
    public void completed(Date date, TrainhelperResult response) {
        if(response == null) {
            failed(new CommonResult(ErrorCode.FAIL, "response is null", CommonResult.ERROR_TYPE_BIZ));
            return;
        }
        if(response.getErrorCode() != ErrorCode.OK) {
            response.setErrorType(CommonResult.ERROR_TYPE_BIZ);
            failed(response);
            return;
        }
        Class<T> clazz = null;
        Type t = getClass().getGenericSuperclass();
        if(t instanceof ParameterizedType){
            Type[] p = ((ParameterizedType)t).getActualTypeArguments();
            clazz = (Class<T>)p[0];
        }else{
            failed(new CommonResult(ErrorCode.FAIL, "parse type failed", CommonResult.ERROR_TYPE_BIZ));
            return;
        }
        T result = response.toBean(clazz);
        if(result == null) {
            if(TextUtils.isEmpty(response.data)){
                onSuccess(null);
            }else{
                failed(new CommonResult(ErrorCode.FAIL, "parse json failed", CommonResult.ERROR_TYPE_BIZ));
            }

        } else {
            onSuccess(result);
        }
    }

    @Override
    public void failed(WebApiFailureType failureType, int httpStatusCode, String error, int errorCode,
                       int enterpriseID) {
        failed(new CommonResult(httpStatusCode, error, CommonResult.ERROR_TYPE_NET));
    }

    public abstract void failed(CommonResult result);
}
