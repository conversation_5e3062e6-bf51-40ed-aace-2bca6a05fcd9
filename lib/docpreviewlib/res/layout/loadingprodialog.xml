<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical" 
    >
<!-- android:background="@null"    android:background="@drawable/pop_bg" -->
    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/loading_progress_dialog_shape"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginStart="20dp">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginRight="15dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
            android:paddingStart="10dp"
            android:layout_marginEnd="15dp"
            android:paddingEnd="10dp">

        <ProgressBar
            android:id="@+id/loadingImageView"
            style="?android:attr/progressBarStyleSmall"
            android:layout_width="26dp"
            android:layout_height="26dp"
            android:layout_centerVertical="true"

            android:layout_marginTop="13dp"
            android:indeterminateDrawable="@anim/progress_round" />
        <com.facishare.fs.i18n.I18NTextView
            android:id="@+id/textView_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:paddingRight="10dp"
            android:layout_toRightOf="@+id/loadingImageView"
            android:textColor="#000000"
            android:textSize="14dp"
            i18n:fstext="common.doc_preview.guide.loading"
                android:layout_toEndOf="@+id/loadingImageView"
                android:layout_marginStart="10dp"
                android:paddingEnd="10dp" />


    </RelativeLayout>

    </RelativeLayout>

</LinearLayout>