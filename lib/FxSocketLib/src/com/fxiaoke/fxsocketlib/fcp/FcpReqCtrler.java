/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.fxsocketlib.fcp;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

import com.facishare.fs.common_utils.JsonHelper;
import com.facishare.fs.common_utils.ProtostuffUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.fxiaoke.fxsocketlib.businessctrl.FcpTaskFailureCode;
import com.fxiaoke.fxsocketlib.fcp.api.FcpErrorData;
import com.fxiaoke.fxsocketlib.fcp.api.FcpReqParam;
import com.fxiaoke.fxsocketlib.fcp.api.IFcpReqCtrler;
import com.fxiaoke.fxsocketlib.fcp.api.IFcpReqFeedListener;
import com.fxiaoke.fxsocketlib.fcp.api.StatusErrorException;
import com.fxiaoke.fxlog.DebugEvent;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.fxsocketlib.businessctrl.FcpRequestTask;
import com.fxiaoke.fxsocketlib.businessctrl.FcpTaskBase;
import com.fxiaoke.fxsocketlib.businessctrl.FcpTaskListener;
import com.fxiaoke.fxsocketlib.envctrl.FcpConnectEnvCtrl;
import com.fxiaoke.fxsocketlib.socketctrl.FcpHeaderType;
import com.fxiaoke.fxsocketlib.socketctrl.FcpRequest;
import com.fxiaoke.fxsocketlib.socketctrl.FcpRequestMethod;
import com.fxiaoke.fxsocketlib.socketctrl.FcpResponse;
import com.fxiaoke.fxsocketlib.socketctrl.FcpResponseCode;
import com.fxiaoke.fxsocketlib.utils.FcpUtils;
import com.google.protobuf.InvalidProtocolBufferException;

import android.text.TextUtils;
import android.util.Log;

/**
 * Created by lid on 2015/11/16.
 */
public class FcpReqCtrler implements IFcpReqCtrler,FcpTaskListener {
    public final static DebugEvent debug_queryByJson = new DebugEvent("queryByJson");
    FcpTaskBase mtask;
    @Override
    public void query(FcpReqParam req)  throws StatusErrorException {
        if (mtask!=null){
            throw new StatusErrorException("repeate use the same ctrler");
        }
        FcpRequestTask reqTask = FcpConnectEnvCtrl.getInstance().createRequestTask(
                FcpTaskBase.TaskStatus.notinit);
        if(req.contentType==1){//protobuf
            FCLog.d(FCLog.envctrl,"tid:"+reqTask.getMytaskid()+" protobuf");
        }else{
            FCLog.d(FCLog.envctrl,"tid:"+reqTask.getMytaskid()+" params:"+new String(req.reqBody));
        }

        mtask=reqTask;
        reqTask.setListener(this);
        if (req.timeout==-1) {
        }else {
            reqTask.setMaxExeTime(req.timeout);
        }
        reqTask.setMethod(FcpRequestMethod.Query);
        reqTask.setReqBody(req.reqBody);
        reqTask.setReqParamBody(req);
        Map<String, Object> paramsMap=new HashMap<String, Object>();
        paramsMap.put("task", "query");
        reqTask.setParams(paramsMap);

        if(!TextUtils.isEmpty(req.postId)) {
            reqTask.addHeader(FcpHeaderType.V3PostId, req.postId);
        }
        reqTask.addHeader(FcpHeaderType.ContentType, req.contentType);
        reqTask.addHeader(FcpHeaderType.ContentCrypto, 1L);
        reqTask.addHeader(FcpHeaderType.V3QueryName, req.queryName);

        exeTask(reqTask);
    }

    /**
     * the result is is returned the same as method query
     * @param req
     * @throws StatusErrorException
     */
    @Override
    public void querySync(FcpReqParam req)  throws StatusErrorException{
        FcpRequestTask reqTask = FcpConnectEnvCtrl.getInstance().createRequestTask(
                FcpTaskBase.TaskStatus.notinit);
        if (req.timeout==-1) {
        }else {
            reqTask.setMaxExeTime(req.timeout);
        }
        reqTask.setListener(this);
        reqTask.setMethod(FcpRequestMethod.Query);
        reqTask.setReqBody(req.reqBody);
        reqTask.setReqParamBody(req);
        Map<String, Object> paramsMap=new HashMap<String, Object>();
        paramsMap.put("task", "querySync");
        reqTask.setParams(paramsMap);

        reqTask.addHeader(FcpHeaderType.ContentType, req.contentType);
        reqTask.addHeader(FcpHeaderType.ContentCrypto, 1L);
        reqTask.addHeader(FcpHeaderType.V3QueryName, req.queryName);
        reqTask.addHeader(FcpHeaderType.FsLocale, I18NHelper.getInstance().getCurrentLang());
        reqTask.setStatus(FcpTaskBase.TaskStatus.idle);
        reqTask.execute_Sync();
        FcpResponse rsp=reqTask.getSyncResult();
        if (rsp!=null&&rsp.getMessageCode()== FcpResponseCode.OK) {
            onComplete(reqTask,rsp);
        }else{
            onError(reqTask, FcpUtils.getRspErrString(rsp));

        }
        reqTask.close();
    }

    @Override
    public void cancel() {
        if (mtask!=null){
            mtask.cancel();
        }
    }

    void exeTask(FcpTaskBase task){
        FCLog.d(FCLog.socketctrl, "exeTask");
        task.setStatus(FcpTaskBase.TaskStatus.idle);
        task.execute();
    }

    @Override
    public void onProgress(FcpTaskBase task, int progress, int total) {
        if (task.getReqParamBody()!=null) {
            try {
                FcpReqParam rrb=(FcpReqParam)task.getReqParamBody();
                if (rrb!=null&&rrb.callback!=null) {
                    rrb.callback.onProgress(rrb,progress, total);
                }
            } catch (ClassCastException e) {
                // TODO: handle exception
            }

        }
    }

    @Override
    public void onComplete(FcpTaskBase task, FcpResponse rsp) {
        FcpReqParam rp=(FcpReqParam)task.getReqParamBody();
        if (rp.contentType==2L || rp.contentType==3L){
            processJsonResult(task, rsp);
        }else if (rp.contentType==1L){
            processProtoResult(task, rsp, rp.useProtostuff);
        }
        task.close();
        mtask=null;
    }
    void processJsonResult(FcpTaskBase task, FcpResponse rsp){
        FcpReqParam rp=(FcpReqParam)task.getReqParamBody();
        if (rp!=null&&rp.callback!=null) {
            byte[] key=getAESKey();
            String rst=new String();
            try {
                FCLog.d(FCLog.envctrl,"tid"+task.getMytaskid()+" rsp:"+
                        new String(rsp.getContent(key)));
                if(rp.callback instanceof IFcpReqFeedListener&&rp.queryName.equals("EMXAXTPROXY.Proxy.Forward")){
                    IFcpReqFeedListener feedListener = (IFcpReqFeedListener) rp.callback;
                    feedListener.onSuccessFeed(rp,rsp.getContent(key));
                }else{
                    rp.callback.onSuccess(rp,JsonHelper.fromJsonBytes(
                            rsp.getContent(key),rp.callback.getResultType()
                    ));
                }

            } catch (IOException e) {
                FcpErrorData errorData=new FcpErrorData((short) 0, FcpTaskFailureCode.DefaultClientError,new String(rsp.getContent
                        (key)));
                FCLog.e(FcpReqCtrler.debug_queryByJson,
                        Log.getStackTraceString(e));
                rp.callback.onFailed(rp,errorData);
                e.printStackTrace();
            } catch (Exception e) {
                FcpErrorData errorData=new FcpErrorData((short) 0,FcpTaskFailureCode.DefaultClientError,new String(rsp.getContent(key)));
                rp.callback.onFailed(rp,errorData);
                FCLog.e(FcpReqCtrler.debug_queryByJson,
                        Log.getStackTraceString(e));
                FCLog.i(FcpReqCtrler.debug_queryByJson,
                        new String(rsp.getContent(key)));
            }
        }
    }
    void processProtoResult(FcpTaskBase task, FcpResponse rsp, boolean useProtostuff){
        FcpReqParam rp=(FcpReqParam)task.getReqParamBody();
        byte[] key=getAESKey();
        byte[] content = rsp.getContent(key);
        if (rp!=null&&rp.callback!=null) {
            try {
                Class typeref=rp.callback.getResultType();
                if (!useProtostuff) {
                    Field f= null;
                    f = typeref.getDeclaredField("PARSER");
                    com.google.protobuf.Parser p=null;
                    if (f!=null) {
                        p=(com.google.protobuf.Parser)f.get(null);
                    }
                    rp.callback.onSuccess(rp,p.parseFrom(content));
                } else {
                    rp.callback.onSuccess(rp, ProtostuffUtils.deserialize(content, typeref));
                }


            } catch (InvalidProtocolBufferException e) {
                processPtotoException(rp,e);
            }catch (NullPointerException e) {
                processPtotoException(rp,e);
            }catch (NoSuchFieldException e) {
                processPtotoException(rp,e);
            } catch (IllegalAccessException e) {
                processPtotoException(rp,e);
            }
        }
    }

    private void processPtotoException(FcpReqParam rp,Exception e){
        FcpErrorData errorData=new FcpErrorData((short) 0,FcpTaskFailureCode.DefaultClientError,"protobuf");
        rp.callback.onFailed(rp,errorData);
        FCLog.e(FcpReqCtrler.debug_queryByJson,
                Log.getStackTraceString(e));
    }
    @Override
    public void onError(final FcpTaskBase task, String reason) {
        Map<String,Object> params= task.getParams();
        FcpReqParam rp=(FcpReqParam)task.getReqParamBody();
        if (params!=null&&rp!=null&&rp.callback!=null) {
            String taskName=(String)params.get("task");
            //if (taskName!=null&&taskName.equals("query")) {
            FcpErrorData errorData = FcpUtils.getFailedInfoByTask(task);
            rp.callback.onFailed(rp,errorData);
//            long failureCode=task.getFailureCode();
//            FCLog.i(FCLog.socketctrl, "FReqCtr.onError task.FailureCode: "+failureCode + " tid:"+task.getMytaskid());
//            FcpResponse rsp=task.getSyncResult();
//            short msgCode = rsp!=null?rsp.getMessageCode():0;
//
//            FcpErrorData errorData = new FcpErrorData(msgCode,failureCode,reason);
//            errorData.businessFailMsg=task.getBusinessFailMsg();
//            if (failureCode<0){
//                rp.callback.onFailed(rp,errorData);
//            }else {
//                errorData.errorMsg=FcpUtils.getFailedReason(Long.valueOf(failureCode));
//                rp.callback.onFailed(rp, errorData);
//            }
        }
        FcpUtils.closeTaskAsync(task);
        mtask=null;
    }

    @Override
    public void onNewNotify(FcpTaskBase task, FcpRequest req) {

    }

    @Override
    public void onDisConnnect(FcpTaskBase task) {

    }

    @Override
    public void onDebug(FcpTaskBase task, String msg) {

    }
    byte[] getAESKey(){
        byte[] key=FcpConnectEnvCtrl.getInstance().getAESKey();
        return key;
    }
}
