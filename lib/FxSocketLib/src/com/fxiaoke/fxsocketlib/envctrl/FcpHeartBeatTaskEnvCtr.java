package com.fxiaoke.fxsocketlib.envctrl;

import java.util.concurrent.TimeUnit;

import com.facishare.fs.utils_fs.AppStateHelper;
import com.facishare.fs.utils_fs.AppStateListener;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.fxsocketlib.businessctrl.FcpHeartBeatTask;
import com.fxiaoke.fxsocketlib.socketctrl.FcpClient;
import com.fxiaoke.fxsocketlib.socketctrl.FcpClientManager;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;

/**
 * 当前类职责两个：1 检查是否具备执行心跳请求条件，具备即唤起心跳任务；2 对外提供管理心跳任务的能力（启动定时或取消定时）
 */
public class FcpHeartBeatTaskEnvCtr {
    final static String ACTION_FCP_HEART_BEAT = "com.fxiaoke.fxsocketlib.envctrl.awaken";
    static long lastest_time_backapp = 0;
    static final String TAG = "FcpHeartBeatTaskEnvCtr";// heart beat receiver
    //App前后台状态监听
    private AppStateListener mAppStateListener = null;
    private static FcpHeartBeatTaskEnvCtr instance = null;

    private int initIntervalMinuteTime = 3;//默认是3分钟
    public void setInitIntervalMinuteTime(int initIntervalMinuteTime) {
        this.initIntervalMinuteTime = initIntervalMinuteTime;
    }
    public int getInitIntervalMinuteTime() {
        return initIntervalMinuteTime;
    }
    public static final String CLOUD_CTR_KEY = "fcp_beat_interval_minute_time";

    private FcpHeartBeatTaskEnvCtr() {
        registerRunTopLis();//监听前后台切换
    }

    private static final int Interval_Time_For_Send_Beat_Task = 10000;//在非心跳任务触发心跳请求的请求间隔时间，用以避免发送重复的心跳请求；
    private static long lastRetryBeatTaskWhenTimeOut = 0;//上一次在业务任务超时时，响应了发送请求心跳请求的时间；

    public static void sendHeatBeatTask() {
        long cur = System.currentTimeMillis();
        if ((cur - lastRetryBeatTaskWhenTimeOut) > Interval_Time_For_Send_Beat_Task) {
            exeHeartBeatTask();
        } else {
            FCLog.i(FCLog.heartbeat, TAG, "sendHeatBeatTask failed exe by not reach Interval_Time");
        }
        lastRetryBeatTaskWhenTimeOut = cur;
    }
    public static FcpHeartBeatTaskEnvCtr getInstance() {
        if (instance == null) {
            instance = new FcpHeartBeatTaskEnvCtr();
        }
        return instance;
    }

    public boolean checkToExeFcpHeartBeatTask() {
        //        FCLog.d(FCLog.heartbeat, TAG, "checkToExeFcpHeartBeatTask runing...");

        boolean needHeartBeat = true;
        if (AppStateHelper.isAppRunTop()) {
            FCLog.d(FCLog.heartbeat, TAG,  "checkToExeFcpHeartBeatTask will set lastest_time_backapp 0  "
                    + ""+lastest_time_backapp);
            lastest_time_backapp = 0;
        } else {
            if (lastest_time_backapp == 0) {
                lastest_time_backapp = System.currentTimeMillis();
                FCLog.w(FCLog.heartbeat, TAG,"checkToExeFcpHeartBeatTask lastest_time_backapp updated to "+lastest_time_backapp);
            } else {
            }
        }
        if (lastest_time_backapp == 0) {

        } else {
            int backtime = (int) ((System.currentTimeMillis() - lastest_time_backapp) / 60000);
            if (lastest_time_backapp != 0 && (backtime > FcpConnectEnvCtrl.MAX_BACKUP_APP_MINUTES)) {
                if (FcpClientManager.getInstance().haveBusyClient()) {
                    lastest_time_backapp = 0;
                    FCLog.i(FCLog.heartbeat, TAG, "can't offline : busy client set lastest_time_backapp 0 ");
                } else {
                    if(FcpConnectEnvCtrl.getInstance().backout()){
                        FCLog.d(FCLog.heartbeat, TAG, "offline finished ... ");
                        needHeartBeat = false;
                    }else{
                        FCLog.d(FCLog.heartbeat, TAG, "offline canceled ... ");
                    }
                }
            } else {

            }
        }
        if (needHeartBeat) {
            exeHeartBeatTask();
        }
        FCLog.d(FCLog.heartbeat, TAG, "checkToExeFcpHeartBeatTask called and exeHeartBeatTask: " + needHeartBeat);
        return needHeartBeat;
    }

    private void registerRunTopLis() {
        if (mAppStateListener == null) {
            mAppStateListener = new AppStateListener() {
                @Override
                public void onRunTopChanged(boolean b) {
                    //应用打开
                    if (b) {//第一次注册成功时
                        FCLog.d(FCLog.heartbeat, TAG,  "onRunTopChanged will set 0 lastest_time_backapp: "
                                + ""+lastest_time_backapp);
                        lastest_time_backapp = 0;
                    }else {
                        if (lastest_time_backapp == 0) {
                            lastest_time_backapp = System.currentTimeMillis();
                            FCLog.w(FCLog.heartbeat, TAG,"onRunTopChanged lastest_time_backapp updated to "+FcpHeartBeatTaskEnvCtr.lastest_time_backapp);
                        }
                    }
                }

                @Override
                public void onScreenOn(boolean b) {
                }
            };
            AppStateHelper.registerAppStateListener(mAppStateListener);
            FCLog.d(FCLog.heartbeat, TAG, "register app switch lis...");
        }
    }

    public static void exeHeartBeatTask() {
        if (FcpConnectEnvCtrl.getInstance().isInit()) {
            // exe heart beat
            FcpClient c = FcpConnectEnvCtrl.getInstance().getPrimarySocketClient();
            if (c == null) {
                FCLog.d(FCLog.heartbeat, TAG, "need resume");
                FcpConnectEnvCtrl.getInstance().resume();
            } else {
                if (c.getBussinessStatus() == FcpClient.BussinessStatus.Authorized) {
                    FCLog.d(FCLog.heartbeat, TAG, "exe");
                    FcpHeartBeatTask task = FcpConnectEnvCtrl.getInstance().createHeartBeatTask();
                    if (task != null) {
                        task.execute();
                    }
                } else {
                    FCLog.d(FCLog.heartbeat, TAG, "unAuthorized");
                }
            }
        } else {
            FCLog.d(FCLog.heartbeat, TAG, "Not init");
        }
    }

    /**
     * 开始定时执行心跳任务
     */
    public void scheduleJob(Context mctx) {
        FcpHeartBeatSyncJob.schedulePeriodicJob(mctx);
    }

    /**
     *  重设定时心跳任务的周期
     */
    public void reScheduleJob(Context mctx,int intervalMinutesTime) {
        setInitIntervalMinuteTime(intervalMinutesTime);
        FcpHeartBeatSyncJob.schedulePeriodicJob(mctx,intervalMinutesTime);
    }

    /**
     *  取消心跳定时任务
     */
    public void cancelJob() {
        FcpHeartBeatSyncJob.cancelJob();
    }
}
