<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.tencent.tinker.lib">

    <application>
        <service android:exported="false"
            android:name=".service.TinkerPatchService$IntentServiceRunner"

            android:process=":patch" />
        <service android:exported="false"
            android:name=".service.TinkerPatchService$IntentServiceRunner$InnerService"

            android:process=":patch" />
        <service android:exported="false"
            android:name=".service.TinkerPatchService$JobServiceRunner"

            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":patch" />
        <service android:exported="false"
            android:name=".service.DefaultTinkerResultService"
              />
    </application>

</manifest>
