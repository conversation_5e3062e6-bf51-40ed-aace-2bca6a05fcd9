package com.tencent.tinkutils.lib.tinker;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.res.AssetManager;

import com.tencent.tinkutils.lib.util.TinkerLog;
import com.tencent.tinkutils.loader.shareutil.SharePatchFileUtil;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

import dalvik.system.DexClassLoader;

public class AntiCheatingHelper {

    private DexClassLoader classLoader = null;
    private static AntiCheatingHelper instance = null;
    public synchronized static AntiCheatingHelper getInstance(){
        if(instance == null){
            instance = new AntiCheatingHelper();
        }
        return instance;
    }
    private AntiCheatingHelper(){}


    public void copyApktoPrivatePath(Context ctx, File src){
        //先拷贝apk到vaccine/apk目录
        String ApkDirName = ctx.getFilesDir().getParent() + "/vaccine/apk/" + src.getName().replace("dat", "apk");
        try{
            SharePatchFileUtil.copyFileUsingStream(src ,new File(ApkDirName));
            saveVersion(ctx, src.getName().replace("dat", "apk"));
            TinkerLog.e("anticheat", "copyApktoPrivatePath src: " + src.getAbsolutePath() + " dst: " + ApkDirName);
            TinkerLog.e("anticheat", "saveVersion: " + src.getName().replace("dat", "apk"));
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void load(Context ctx, boolean isupgrade){
        File dexoutputDir = null;
        String apkName = getVersion(ctx);
        TinkerLog.e("anticheat", "load: getVersion: " + apkName  + " isupgrade: " + isupgrade);
        ApplicationInfo applicationInfo = ctx.getApplicationInfo();
        if (applicationInfo == null) {
            return;
        }

        String apkPath =  applicationInfo.dataDir + "/kwq_anti/" + "anticheat_asset.apk";
        TinkerLog.e("anticheat", "applicationInfo.dataDir: " + applicationInfo.dataDir);
        apkPath = ctx.getFilesDir().getParent() + "/kwq_anti/" + "anticheat_asset.apk";
        TinkerLog.e("anticheat", "ctx.getFilesDir().getParent(): " + ctx.getFilesDir().getParent());
        if(apkName != null &&  !apkName.equals("")){
            TinkerLog.e("anticheat", "load: apkName: " + apkName + " is not null ");
            apkPath =  ctx.getFilesDir().getParent() + "/vaccine/apk/" + apkName;
        }else{
            TinkerLog.e("anticheat", "load: apkName: " + apkName + " is null load asset ");
        }


        File apkFile = new File(apkPath);
        if(!apkFile.exists()){
            TinkerLog.e("anticheat", "load: apkFile " + apkFile.getAbsolutePath() + " is not exists ");
            return;
        }
        String outputDirName = ctx.getFilesDir().getParent() + "/vaccine/opt";
        String libDirName =  ctx.getFilesDir().getParent() + "/vaccine/lib";
        File optDir  = new File(outputDirName);
        File libDir = new File(libDirName);
        if(isupgrade){
            SharePatchFileUtil.deleteDir(libDir);
            SharePatchFileUtil.deleteDir(optDir);
            TinkerLog.e("anticheat", "load: deleteDir libDir： " + libDir  + " optDir: " + optDir);
        }
        if(!optDir.exists()){
            optDir.mkdirs();
            TinkerLog.e("anticheat", "load:  optDir.mkdirs ");
        }

        if(!libDir.exists()){
            libDir.mkdirs();
            TinkerLog.e("anticheat", "load:  libDir.mkdirs ");
            try {
                extractLibFile(new ZipFile(apkPath), libDir);
            } catch (IOException e) {
                TinkerLog.e("anticheat", "load: extractLibFile exception: " + e.getMessage());
                e.printStackTrace();
            }
        }
        try{
            TinkerLog.e("anticheat", "load: new DexClassLoader  "  +
                    " apkPath" + apkPath +
                    " outputDirName" + outputDirName +
                    " libDirName" + libDirName);
            classLoader = new DexClassLoader(apkPath, outputDirName, libDirName, getClass().getClassLoader());
        }catch (Exception e){
            TinkerLog.e("anticheat", "load: exception  "  + e.getMessage());
            e.printStackTrace();
        }
    }


    public String isCheat(){
        try{
            if(classLoader != null){
                TinkerLog.e("anticheat", "isCheat:   classLoader != null");
                Class mMainClass = classLoader.loadClass("com.facishare.fs.anticheat.checkCheatingHelper");
                Constructor constructor = mMainClass.getConstructor();
                Object Main = constructor.newInstance();
                TinkerLog.e("anticheat", "isCheat:  class new instance");
                Method isCheat = mMainClass.getMethod("isCheating", null);
                isCheat.setAccessible(true);
                TinkerLog.e("anticheat", "isCheat:  getMethod isCheating");
                String cheatMsg = String.valueOf(isCheat.invoke(Main,null));
                TinkerLog.e("anticheat", "isCheat:  invoke method isCheat cheatMsg" + cheatMsg);
                return cheatMsg;
            }

        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    public DexClassLoader getClassLoader(){
        return classLoader;
    }


    private  void saveVersion(Context ctx, String value) {
        SharedPreferences sp = ctx.getSharedPreferences("anti_cheat", 0);
        sp.edit().putString("anti_cheat_version", value).apply();
    }

    private  String getVersion(Context ctx) {
        SharedPreferences sp = ctx.getSharedPreferences("anti_cheat", 0);
        return sp.getString("anti_cheat_version", "");
    }



    private  boolean extractLibFile(ZipFile zip, File tardir)
            throws IOException {

        if(!tardir.exists()){
            tardir.mkdirs();
        }

        String defaultArch = "armeabi";
        Map<String,List<ZipEntry>> archLibEntries = new HashMap<String, List<ZipEntry>>();
        for (Enumeration<? extends ZipEntry> e = zip.entries(); e.hasMoreElements();) {
            ZipEntry entry = e.nextElement();
            String name = entry.getName();
            if (name.startsWith("/")) {
                name = name.substring(1);
            }
            if (name.startsWith("lib/")) {
                if(entry.isDirectory()){
                    continue;
                }
                List<ZipEntry> ents = archLibEntries.get(defaultArch);
                if (ents == null) {
                    ents = new LinkedList<ZipEntry>();
                    archLibEntries.put(defaultArch, ents);
                }
                ents.add(entry);
            }
        }
        List<ZipEntry> libEntries = libEntries = archLibEntries.get(defaultArch);
        boolean hasLib = false;
        if (libEntries != null) {
            hasLib = true;
            if (!tardir.exists()) {
                tardir.mkdirs();
            }
            for (ZipEntry libEntry : libEntries) {
                String ename = libEntry.getName();
                String pureName = ename.substring(ename.lastIndexOf('/') + 1);
                File target = new File(tardir, pureName);
                writeToFile(zip.getInputStream(libEntry), target);
            }
        }
        return hasLib;
    }

    private void writeToFile(InputStream dataIns, File target) throws IOException {
        final int BUFFER = 1024;
        BufferedOutputStream bos = new BufferedOutputStream(
                new FileOutputStream(target));
        int count;
        byte data[] = new byte[BUFFER];
        while ((count = dataIns.read(data, 0, BUFFER)) != -1) {
            bos.write(data, 0, count);
        }
        bos.close();
    }


    public static void copyApk(Context ctx) {

        ApplicationInfo applicationInfo = ctx.getApplicationInfo();
        if (applicationInfo == null) {
           return;
        }
        String dest_parent = applicationInfo.dataDir + "/kwq_anti/";

        AssetManager asset = ctx.getAssets();
        String ASSETS_NAME = "kwq_anti.apk";
        String filename = dest_parent  + "anticheat_asset.apk";

        File dest = new File(filename);
        // 如果目录不中存在，创建这个目录
        if (!dest.getParentFile().exists())
            dest.getParentFile().mkdirs();

        InputStream is = null;
        FileOutputStream fos = null;
        try {
            if (dest.exists()){
                dest.delete();
            }

            is = asset.open(ASSETS_NAME);
            fos = new FileOutputStream(filename);
            byte[] buffer = new byte[1024];
            int count = 0;
            while ((count = is.read(buffer)) > 0) {
                fos.write(buffer, 0, count);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            try{
                if(fos != null){
                    fos.close();
                }
            }catch (Exception e){
                e.printStackTrace();
            }
            try{
                if(is != null){
                    is.close();
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }


}
