package com.facishare.performance.monitor;

import android.annotation.TargetApi;
import android.content.Context;
import android.os.Build;
import android.view.Choreographer;

import java.util.ArrayList;

public class FPSMonitor extends AbsMonitor {
    private static final String TAG = "ChoreographerMonitor";

    public ArrayList<Long> getFrames() {
        return frames;
    }

    ArrayList<Long>  frames = new ArrayList<>();

    @Override
    public void start() {
        start(null);
    }

    @Override
    public void start(Context context) {
        if (started) {
            return;
        }
       // stop();
        Choreographer.getInstance().postFrameCallback(frameCallback);
        started = true;
    }

    @Override
    public void stop() {
        if (!started) {
            return;
        }
        Choreographer.getInstance().removeFrameCallback(frameCallback);
        started = false;
    }


    ///////////////////////////////////////////////////////////////////
    //                      doFrame监控相关：                         //
    ///////////////////////////////////////////////////////////////////
    private Choreographer.FrameCallback frameCallback = new Choreographer.FrameCallback() {//系统绘帧回调
        @TargetApi(Build.VERSION_CODES.JELLY_BEAN)
        public void doFrame(long frameTimeNanos) {
            frames.add(System.currentTimeMillis());

            // 开启下一个doFrame监控
            Choreographer.getInstance().postFrameCallback(frameCallback);
        }
    };
}
