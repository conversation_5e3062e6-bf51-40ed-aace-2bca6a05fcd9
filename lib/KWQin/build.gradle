disableLintTask()
apply plugin: 'com.android.library'

android {
    compileSdkVersion gradle.compileSdkVersionForLib
    buildToolsVersion gradle.buildToolsVersion
    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
//            jniLibs.srcDir 'libs'
            aidl.srcDirs=['src']
            java.srcDirs=['src']
            res.srcDirs=['res']
            assets.srcDirs = ['assets'] //多了一个assets目录
        }
    }

    compileOptions {
        sourceCompatibility gradle.javaVersion
        targetCompatibility gradle.javaVersion
    }

    defaultConfig {
        minSdkVersion 14
        targetSdkVersion gradle.targetSdkVersion
        versionCode 1
        versionName gradle.versionmap.get(project.name)
    }

    packagingOptions {
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    compileOnly fileTree(dir: project.getRootDir().getAbsolutePath() + "/libs", include: ['*.jar'])
    compileOnly fileTree(dir: project.getRootDir().getAbsolutePath() + "/libsDex", include: ['*.jar'])
    compileOnly "androidx.legacy:legacy-support-v4:${gradle.andoidxVersion}"
    compileOnly depLibProjectWithMavenOrSource("FxLog")
    compileOnly depLibProjectWithMavenOrSource("FxDbLib")
    compileOnly depLibProjectWithMavenOrSource("pluginapi")
    compileOnly depLibProjectWithMavenOrSource("pluginapi_contacts")
    compileOnly depLibProjectWithMavenOrSource("pluginapi_account")
    compileOnly depLibProjectWithMavenOrSource("xUtils")
    compileOnly depLibProjectWithMavenOrSource("fshttp")
    compileOnly depLibProjectWithMavenOrSource("user_context")
    compileOnly depLibProjectWithMavenOrSource("FXStatEngine")
    compileOnly depLibProjectWithMavenOrSource('locationLib')
    compileOnly 'com.tencent.tbs:tbssdk:44136'
}
