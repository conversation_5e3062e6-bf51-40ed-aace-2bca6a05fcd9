package com.facishare.fs.biz_function.subbiz_attendance_new.bean;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

public class CheckEffectInfoArgs implements Serializable{
	//规则id
	@JSONField(name="M10")
	public String ruleId;
	//参与考勤部门id列表  用于控件展示
	@JSONField(name="M11")
	public List<Integer> deptIds;
	//参与考勤员工id列表  用于控件显示
	@JSONField(name="M12")
	public List<Integer> userIds;

	/**
	 * 外部角色id
	 */
	@JSONField(name = "M13")
	public List<String> outerRoleIds;
	/**
	 *0不是外部 1 是外部
	 */
	@JSONField(name = "M14")
	public int isOuter;

	public CheckEffectInfoArgs(){}
	@JSONCreator
	public CheckEffectInfoArgs(
			@JSONField(name="M10")
			String ruleId,
			@JSONField(name="M11")
			List<Integer> deptIds,
			@JSONField(name="M12")
			List<Integer> userIds,
			@JSONField(name="M14")
			int isOuter,
			@JSONField(name="M13")
			List<String> outerRoleIds) {
		this.ruleId = ruleId;
		this.deptIds = deptIds;
		this.userIds = userIds;
		this.outerRoleIds = outerRoleIds;
		this.isOuter = isOuter;
	}
}
