package com.facishare.fs.biz_function.subbiz_attendance_new.bean;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

public class RestTimeGroup implements Serializable {

    /** 开始时间 HH:mm */
    @JSONField(name = "M1")
    public String startTime;

    /** 结束时间 HH:mm */
    @JSONField(name = "M2")
    public String endTime;

    @JSONCreator
    public RestTimeGroup(@JSONField(name = "M1")
                         String startTime,
                         @JSONField(name = "M2")
                         String endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
    }
}