/*******************************************************************************
 * Copyright 2011-2014 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *******************************************************************************/
package com.nostra13.universalimageloader.core.display;

import com.nostra13.universalimageloader.core.assist.LoadedFrom;
import com.nostra13.universalimageloader.core.imageaware.ImageAware;
import com.nostra13.universalimageloader.core.imageaware.ImageViewAware;

import android.graphics.*;
import android.graphics.drawable.Drawable;
import android.util.TypedValue;

/**
 * Can display bitmap with rounded corners. This implementation works only with ImageViews wrapped
 * in ImageViewAware.
 * <br />
 * This implementation is inspired by
 * <a href="http://www.curious-creature.org/2012/12/11/android-recipe-1-image-with-rounded-corners/">
 * Romain Guy's article</a>. It rounds images using custom drawable drawing. Original bitmap isn't changed.
 * <br />
 * <br />
 * If this implementation doesn't meet your needs then consider
 * <a href="https://github.com/vinc3m1/RoundedImageView">RoundedImageView</a> or
 * <a href="https://github.com/Pkmmte/CircularImageView">CircularImageView</a> projects for usage.
 *
 * <AUTHOR> Tarasevich (nostra13[at]gmail[dot]com)
 * @since 1.5.6
 */
public class RoundedBitmapWithBorderDisplayer extends RoundedBitmapDisplayer {

	private final int borderColor;
	private final int borderWidth;

	public RoundedBitmapWithBorderDisplayer() {
		this(-1, 0, 0, 0);
	}

	public RoundedBitmapWithBorderDisplayer(int cornerRadiusPixels) {
		this(cornerRadiusPixels, 0, 0, 0);
	}

	public RoundedBitmapWithBorderDisplayer(int cornerRadiusPixels, int marginPixels, int borderColor, int borderWidth) {
		super(cornerRadiusPixels, marginPixels);
		this.borderColor = borderColor;
		this.borderWidth = borderWidth;
	}


	@Override
	public void display(Bitmap bitmap, ImageAware imageAware, LoadedFrom loadedFrom) {
		if (!(imageAware instanceof ImageViewAware)) {
			throw new IllegalArgumentException("ImageAware should wrap ImageView. ImageViewAware is expected.");
		}

		if (cornerRadius == -1) {
			int pix =  (int)Math.min((imageAware.getHeight()) / 2.0f, (imageAware.getWidth()) / 2.0f);

			imageAware.setImageDrawable(new RoundedWithBorderDrawable(bitmap, pix, margin, borderColor, borderWidth));
		}else {
			imageAware.setImageDrawable(new RoundedWithBorderDrawable(bitmap, cornerRadius, margin, borderColor, borderWidth));
		}

	}

	@Override
	public void display(String imageUrl, ImageAware imageAware) {
		// 目前这个方法只在看长图用，这里不做处理
	}

	public static class RoundedWithBorderDrawable extends RoundedDrawable {

		private Paint mArcPaint;
		private float borderWidth;

		public RoundedWithBorderDrawable(Bitmap bitmap, int cornerRadius, int margin, int borderColor, float borderWidth) {
			super(bitmap, cornerRadius, margin);
			this.borderWidth = borderWidth;

			mArcPaint = new Paint();
			mArcPaint.setAntiAlias(true);
			mArcPaint.setStrokeWidth(borderWidth);
			mArcPaint.setStrokeCap(Paint.Cap.ROUND);
			mArcPaint.setColor(borderColor);
			mArcPaint.setStyle(Paint.Style.STROKE);

		}

		@Override
		protected void onBoundsChange(Rect bounds) {
			super.onBoundsChange(bounds);
			mRect.set(margin, margin, bounds.width() - margin, bounds.height() - margin);
			
			// Resize the original bitmap to fit the new bound
			Matrix shaderMatrix = new Matrix();
			shaderMatrix.setRectToRect(mBitmapRect, mRect, Matrix.ScaleToFit.FILL);
			bitmapShader.setLocalMatrix(shaderMatrix);
			
		}

		@Override
		public void draw(Canvas canvas) {
			canvas.drawRoundRect(mRect, cornerRadius, cornerRadius, paint);
			RectF tr = new RectF(mRect.left + borderWidth / 2, mRect.top + borderWidth / 2, mRect.right - borderWidth / 2, mRect.bottom - borderWidth / 2);
			canvas.drawArc(tr, 0, 360, false, mArcPaint);
		}

		@Override
		public int getOpacity() {
			return PixelFormat.TRANSLUCENT;
		}

		@Override
		public void setAlpha(int alpha) {
			paint.setAlpha(alpha);
		}

		@Override
		public void setColorFilter(ColorFilter cf) {
			paint.setColorFilter(cf);
		}
	}
}
