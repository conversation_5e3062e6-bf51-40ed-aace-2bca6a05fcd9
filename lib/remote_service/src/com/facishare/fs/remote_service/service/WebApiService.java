package com.facishare.fs.remote_service.service;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.core.http.FsHttp;
import com.core.http.callback.StringCallback;
import com.core.http.request.GetRequest;
import com.facishare.fs.remote_service.aidl.IWebApiCallback;
import com.facishare.fs.remote_service.aidl.IWebApiInterface;
import com.facishare.fs.remote_service.aidl.ParamItem;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiParameterList;
import com.fxiaoke.fshttp.web.http.WebApiUtils;

import java.io.IOException;
import java.util.Date;
import java.util.List;

public class WebApiService extends Service {

    public WebApiService() {

    }

    @Override
    public IBinder onBind(Intent intent) {
        return new WebApiBinder();
    }


    class WebApiBinder extends IWebApiInterface.Stub{

        @Override
        public void basicTypes(int anInt, long aLong, boolean aBoolean, float aFloat, double aDouble, String aString)
                throws RemoteException {
        }

        @Override
        public  void postAsync(String controller, String action, List<ParamItem> Params,  final IWebApiCallback Callback) throws RemoteException {

            final WebApiParameterList params = WebApiParameterList.create();
            for (ParamItem item : Params) {
                params.with(item.key,item.value);
            }

            WebApiUtils.buildFHEFullJsonDataType(params);
            final WebApiExecutionCallback<String> callback = new WebApiExecutionCallback<String>() {

                @Override
                public TypeReference getTypeReference() {
                    return new TypeReference() {
                    };
                }

                @Override
                public Class getTypeReferenceFHE() {
                    return Object.class;
                }

                @Override
                public void completed(Date date, String t) {
                    try {
                        Log.e("remote_zds", "webapiservice  postasync: " + t);
                        Callback.onResult(t);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                    try {
                        Callback.onError(JSON.toJSONString(failureType), httpStatusCode);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
            };
            WebApiUtils.postAsync(controller, action, params, callback,true);

        }

        @Override
        public void getAsync(String controller, String action, List<ParamItem> Params, final IWebApiCallback Callback) throws RemoteException {

            final WebApiParameterList params = WebApiParameterList.create();
            for (ParamItem item : Params) {
                params.with(item.key,item.value);
            }

            WebApiUtils.buildFHEFullJsonDataType(params);
            final WebApiExecutionCallback<String> callback = new WebApiExecutionCallback<String>() {

                @Override
                public TypeReference getTypeReference() {
                    return new TypeReference() {
                    };
                }

                @Override
                public Class getTypeReferenceFHE() {
                    return Object.class;
                }

                @Override
                public void completed(Date date, String t) {
                    try {
                        Log.e("remote_zds", "webapiservice  getasync: " + t);
                        Callback.onResult(t);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                    try {
                        String failRes = JSON.toJSONString(failureType);
                        Callback.onError(error, httpStatusCode);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
            };
            WebApiUtils.getAsync(controller, action, params, callback);
        }

        @Override
        public String getFSCHost(String requestUrl){
            return WebApiUtils.getFSCHost(requestUrl);
        }

        @Override
        public String getRequestUrl(){
            return WebApiUtils.requestUrl;
        }

        @Override
        public   void getFsHttp(String url, List<ParamItem> Params, final IWebApiCallback Callback){

            GetRequest req = FsHttp.get(url);
            for(ParamItem it: Params){
                req.addParam(it.key, it.value);
            }

            req.request(new StringCallback<String>() {
                @Override
                public void onSuccess(String s, int i) {

                    try {
                        Callback.onResult(s);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onFail(int httpStatusCode, String error, IOException e) {

                    try {
                        Callback.onError(error, httpStatusCode);
                    } catch (RemoteException re) {
                        re.printStackTrace();
                    }
                }
            });
        }
    }

}
