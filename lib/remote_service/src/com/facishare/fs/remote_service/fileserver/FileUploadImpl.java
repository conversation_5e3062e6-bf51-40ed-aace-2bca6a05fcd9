package com.facishare.fs.remote_service.fileserver;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Queue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;

import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.remote_service.fileupload.FileUploadProgressCallback;
import com.facishare.fs.remote_service.fileupload.FileUploadStateCallback;
import com.facishare.fs.remote_service.fileupload.FileUploadStates;
import com.facishare.fs.remote_service.fileupload.FileUploadTaskInfo;
import com.facishare.fs.remote_service.fileupload.FileUploadVo;
import com.facishare.fs.remote_service.fileupload.IFileUploader;
import com.facishare.fs.remote_service.fileupload.IFileUploaderBusinessCallback;
import com.fs.fsprobuf.ServerProtobuf;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.fxsocketlib.fcp.FcpTempFileUploader;
import com.fxiaoke.fxsocketlib.fcp.api.FcpUploadParam;
import com.fxiaoke.fxsocketlib.fcp.api.IFcpTempFileUploadListener;
import com.fxiaoke.fxsocketlib.fcp.api.IMiniSandboxContext;

import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;
import android.util.SparseArray;

public class FileUploadImpl extends IFileUploader.Stub {

    private String mLoaderId;

    private int mCurMaxId = 0;

    private int mFileLength;

    private Queue<Integer> mFileUploadTaskQueue = new LinkedBlockingQueue<Integer>();

    /**
     * key:任务ID，value:任务信息
     */
    private SparseArray<FileUploadTaskInfo> mTaskInfoMap = new SparseArray<FileUploadTaskInfo>();

    /**
     * 任务回调
     */
    private IFileUploaderBusinessCallback mFileUploaderBusinessCallback;

    /**
     * 进度回调
     */
    private List<FileUploadProgressCallback> mProgressCallbackList = new ArrayList<FileUploadProgressCallback>();

    /**
     * 状态回调的list
     */
    private List<FileUploadStateCallback> mStateCallbackList = new ArrayList<FileUploadStateCallback>();

    private ExecutorService mExeService;
    public FileUploadImpl() {
        mExeService = Executors.newSingleThreadExecutor();
    }

    private synchronized int genNewId() {
        // TODO 超范围时的处理
        mCurMaxId++;
        return mCurMaxId;
    }

    @Override
    public void setLoaderId(String loaderId) {
        mLoaderId = loaderId;
    }

    @Override
    public String getLoaderId() {
        return mLoaderId;
    }

    @Override
    public int addTask(String name, String path) throws RemoteException {
        return addTaskEx4(name, path, null, 0, 0);
    }

    @Override
    public int addTaskEx2(String name, String path, int enterpriseEnv) throws RemoteException {
        return addTaskEx4(name, path, null, 0, enterpriseEnv);
    }

    @Override
    public int addTaskEx3(String name, String path, FileUploadVo vo, long timeOut) throws RemoteException {
        return addTaskEx4(name, path, vo, timeOut, 0);
    }

    @Override
    public int addTaskEx4(String name, String path, FileUploadVo vo, long timeOut, int enterpriseEnv)
            throws RemoteException {
        //		if (mContext == null) {
        //			mContext = context.getApplicationContext();
        //		}
        FileUploadTaskInfo task = new FileUploadTaskInfo();
        int id = genNewId();
        task.id = id;
        task.name = name;
        task.path = path;
        task.state = FileUploadStates.WAITING;
        task.timeOut = timeOut;
        task.vo = vo;
        task.enterpriseEnv = enterpriseEnv;
        mTaskInfoMap.put(task.id, task);

        retryTask(id);
        return id;
    }

    @Override
    public int addTaskEx5(String name, String path, FileUploadVo vo) throws RemoteException {
        return addTaskEx4(name, path, vo, 0, 0);
    }

    @Override
    public int addTaskEx6(String name, String path, FileUploadVo vo, int enterpriseEnv) throws RemoteException {
        return addTaskEx4(name, path, vo, 0, enterpriseEnv);
    }

    @Override
    public int addTaskEx7(String name, String path, FileUploadVo vo, long timeOut, int enterpriseEnv, String upEa, String appId) throws RemoteException {
        FileUploadTaskInfo task = new FileUploadTaskInfo();
        int id = genNewId();
        task.id = id;
        task.name = name;
        task.path = path;
        task.state = FileUploadStates.WAITING;
        task.timeOut = timeOut;
        task.vo = vo;
        task.enterpriseEnv = enterpriseEnv;
        task.upEa = upEa;
        task.appId = appId;
        mTaskInfoMap.put(task.id, task);

        retryTask(id);
        return id;
    }


    @Override
    public void cancelTask(int taskId) {
        FCLog.d(FileServerContants.UPLOAD_TAG, "cancelTask:" + taskId);
        FileUploadTaskInfo task = mTaskInfoMap.get(taskId);
        if (task != null) {
            task.state = FileUploadStates.CANCELED;
            callStateCallback(task);
            mTaskInfoMap.remove(taskId);
        }
    }

    @Override
    public void retryTask(int taskId) throws RemoteException {
        FCLog.i(FileServerContants.UPLOAD_TAG, "retryTask,TaskId:" + taskId);

        FileUploadTaskInfo task = mTaskInfoMap.get(taskId);
        FCLog.i(FileServerContants.UPLOAD_TAG, "retryTask,mTaskInfoMap.get(taskId):" + task != null ? task.path : "null");
        if (task.state == FileUploadStates.FAILED) {
            task.state = FileUploadStates.WAITING;
            callStateCallback(task);
        }

        mFileUploadTaskQueue.offer(task.id);
        if (mFileUploadTaskQueue.size() == 1) {
            int nextTaskId = mFileUploadTaskQueue.element();
            if (nextTaskId != 0) {
                FCLog.i(FileServerContants.UPLOAD_TAG, "retryTask call processLineTask()  nextTaskId= "+nextTaskId );
                processLineTask(nextTaskId);
            }
        }else{
            FCLog.i(FileServerContants.UPLOAD_TAG, "retryTask not call processLineTask() " );
        }
    }

    private void executerNextTask() {
        if (mFileUploadTaskQueue.size() > 0) {
            mFileUploadTaskQueue.poll();

            if (mFileUploadTaskQueue.size() > 0) {
                int nextTaskId = mFileUploadTaskQueue.element();
                if (nextTaskId != 0) {
                    try {
                        processLineTask(nextTaskId);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    private void processLineTask(int taskId) throws RemoteException {
        FileUploadTaskInfo taskInfo = mTaskInfoMap.get(taskId);
        if (taskInfo == null) {
            executerNextTask();
        } else {
            String path = taskInfo.path;
            long timeout = taskInfo.timeOut;
            uploadTempFileASyncEx4(taskId, path, this, timeout, taskInfo.enterpriseEnv); //加参数
        }
    }

    //	@Override
    //	public List<FileUploadTaskInfo> getUploadTaskList() {
    //		List<FileUploadTaskInfo> list = new ArrayList<FileUploadTaskInfo>();
    //		for (int index = 0; index < mTaskInfoMap.size(); index++) {
    //			FileUploadTaskInfo task = mTaskInfoMap.valueAt(index);
    //			list.add(task);
    //		}
    //		return list;
    //	}

    @Override
    public FileUploadTaskInfo getUploadTaskById(int taskId) {
        for (int index = 0; index < mTaskInfoMap.size(); index++) {
            FileUploadTaskInfo task = mTaskInfoMap.valueAt(index);
            if (task != null && task.id == taskId) {
                return task;
            }
        }
        return null;
    }

    //MARK- IBusinessCallback
    @Override
    public void setBusinessCallback(IFileUploaderBusinessCallback callBack) {
        mFileUploaderBusinessCallback = callBack;
    }

    @Override
    public IFileUploaderBusinessCallback getBusinessCallback() {
        return mFileUploaderBusinessCallback;
    }

    // MARK- FileSaveProgressCallback
    @Override
    public void addProgressCallback(FileUploadProgressCallback callBack) {
        mProgressCallbackList.add(callBack);
    }

    @Override
    public void removeProgressCallback(FileUploadProgressCallback callBack) {
        mProgressCallbackList.remove(callBack);
    }

    //	@Override
    //	public List<FileUploadProgressCallback> getFileSaveProgressCallbackList() {
    //		List<FileUploadProgressCallback> callBackList = new ArrayList<FileUploadProgressCallback>();
    //		for (FileUploadProgressCallback callBack : mProgressCallbackList) {
    //			if (callBack != null) {
    //				callBackList.add(callBack);
    //			}
    //		}
    //
    //		return callBackList;
    //	}

    // FileSaveStateCallback
    @Override
    public synchronized void addStateCallback(FileUploadStateCallback callBack) {
        mStateCallbackList.add(callBack);
    }

    @Override
    public synchronized void removeStateCallback(FileUploadStateCallback callback) {
        mStateCallbackList.remove(callback);
    }

    //	@Override
    //	public List<FileUploadStateCallback> getFileSaveStateCallbackList() {
    //		List<FileUploadStateCallback> callBackList = new ArrayList<FileUploadStateCallback>();
    //		for (FileUploadStateCallback callBack : mStateCallbackList) {
    //			if (callBack != null) {
    //				callBackList.add(callBack);
    //			}
    //		}
    //
    //		return callBackList;
    //	}

    /**
     * 调用状态回调接口
     *
     * @param taskInfo TODO
     * @param taskInfo
     */
    private void callStateCallback(FileUploadTaskInfo taskInfo) {
        if (mStateCallbackList.size() == 0) {
            FCLog.i(FileServerContants.UPLOAD_TAG, "callStateCallback return ,TaskId:" + taskInfo.id);
            return;
        }
        FCLog.i(FileServerContants.UPLOAD_TAG, "callStateCallback: TaskId:" + taskInfo.id + " state:" + taskInfo.state);
        final FileUploadTaskInfo localTaskInfo = taskInfo;
        mExeService.submit(new Runnable() {
            @Override
            public void run() {
                for (FileUploadStateCallback callBack : mStateCallbackList) {
                    if (callBack != null) {
                        try {
                            callBack.onStateChanged(localTaskInfo, localTaskInfo.state);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }
                }
                FCLog.i(FileServerContants.UPLOAD_TAG, "callStateCallback foreach end : TaskId:" + taskInfo.id + " state:" + taskInfo.state);
            }
        });
    }

    @Override
    public void onTempFileUploadSuccess(int taskId, final String storagePath) {
        FCLog.d(FileServerContants.UPLOAD_TAG, "upload success,TaskId:" + taskId);

        final FileUploadTaskInfo taskInfo = mTaskInfoMap.get(taskId);
        if (taskInfo == null) {
            //产生异常taskId, 规避
            FCLog.e(FileServerContants.UPLOAD_TAG, "onTempFileUploadSuccess, abnormal taskId, ignore");
            cancelTask(taskId);
            executerNextTask();
            return;
        }

        if (storagePath != null) {
            FCLog.d(FileServerContants.UPLOAD_TAG, "临时文件名：" + storagePath);

            if (mFileUploaderBusinessCallback != null) {
                mExeService.submit(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            mFileUploaderBusinessCallback.onTempFileUploader(taskInfo, storagePath, mFileLength);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }
                });

            }
        } else {
            taskInfo.state = FileUploadStates.FAILED;
            callStateCallback(taskInfo);
            executerNextTask();
        }
    }

    @Override
    public void onTempFileUploadFailed(int taskId, String data) throws RemoteException {
        FCLog.d(FileServerContants.UPLOAD_TAG, "upload failed,TaskId:" + taskId);

        FileUploadTaskInfo taskInfo = mTaskInfoMap.get(taskId);
        if (taskInfo != null) {
            taskInfo.state = FileUploadStates.FAILED;
            callStateCallback(taskInfo);
        }
        executerNextTask();
    }

    @Override
    public void onTempFileUploadProgress(int taskId, int cur, int total) {
        FCLog.d(FileServerContants.UPLOAD_TAG,
                String.format("upload progress,TaskId:%d,cur:%d,total:%d", taskId, cur, total));

        FileUploadTaskInfo taskInfo = null;
        if (cur == 0 && total == 0) {
            taskInfo = mTaskInfoMap.get(taskId);
            if (taskInfo != null && taskInfo.state == FileUploadStates.WAITING) {
                FCLog.d(FileServerContants.UPLOAD_TAG, "start progress,taskInfo state:" + taskInfo.state);

                taskInfo.state = FileUploadStates.UPLOADING;
                callStateCallback(taskInfo);
            }
            return;
        }

        taskInfo = mTaskInfoMap.get(taskId);
        if (taskInfo == null) {
            return;
        }

        FCLog.d(FileServerContants.UPLOAD_TAG, "upload progress,taskInfo state:" + taskInfo.state);

        mFileLength = total;
        // 在这里处理下，不显示到100%
        if (cur == total) {
            cur = total - 1;
        }

        final int localTaskId = taskId;
        final int localCur = cur;
        mExeService.submit(new Runnable() {
            @Override
            public void run() {
                for (FileUploadProgressCallback callBack : mProgressCallbackList) {
                    if (callBack != null) {
                        try {
                            callBack.onProgressChanged(localTaskId, localCur, mFileLength);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        });
    }




    @Override
    public void uploadTempFileASync(int taskId, String path, IFileUploader callback) throws RemoteException {
        uploadTempFileASync(taskId,path,"",false,false,callback,0,0);
    }

    @Override
    public void uploadTempFileASyncEx2(int taskId, String path, IFileUploader callback, long timeout)
            throws RemoteException {
        uploadTempFileASync(taskId,path,"",false,false,callback,0,0);
    }

    @Override
    public void uploadTempFileASyncEx3(int taskId, String path, String bussnessTAG, boolean isNeedThumbnail,
                                       boolean isOrignalPic, IFileUploader callback, long timeout)
            throws RemoteException {
        uploadTempFileASync(taskId,path,bussnessTAG,isNeedThumbnail,isOrignalPic,callback,timeout,0);
    }

    @Override
    public void uploadTempFileASyncEx4(int taskId, String path, IFileUploader callback, long timeout, int
            enterpriseEnv)
            throws RemoteException {
        uploadTempFileASync(taskId, path,"",false,false, callback, timeout, enterpriseEnv);
    }

    @Override
    public void setTaskResult(int taskId, boolean success) {
        FileUploadTaskInfo task = mTaskInfoMap.get(taskId);
        if (success) {
            task.state = FileUploadStates.UPLOADED;
            mTaskInfoMap.remove(taskId);
        } else {
            task.state = FileUploadStates.FAILED;
        }

        callStateCallback(task);
        executerNextTask();
    }

    public void uploadTempFileASync(int taskId,
                                    String path,
                                    final String bussnessTAG,final boolean isNeedThumbnail,final boolean isOrignalPic,
                                    IFileUploader callBack, long timeOut,final int enterpriseEnv) {
        final int localTaskId = taskId;
        final String localPath = path;
        final long timeout = timeOut;
        final String localExtension = suffix(path).toLowerCase(
                Locale.getDefault());
        final IFileUploader localCallBack = callBack;
        FCLog.i(FileServerContants.UPLOAD_TAG, "uploadTempFileASync call mExeService.submit()  localTaskId= "+localTaskId );
        mExeService.submit(new Runnable() {
            @Override
            public void run() {
                FCLog.i(FileServerContants.UPLOAD_TAG, "uploadTempFileASync,TaskId:" + localTaskId);
                try {
                    localCallBack.onTempFileUploadProgress(localTaskId, 0, 0);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
                try {
                    ServerProtobuf.EnterpriseEnv env = ServerProtobuf.EnterpriseEnv.INNER;
                    if(enterpriseEnv == ServerProtobuf.EnterpriseEnv.CROSS.getNumber()){
                        env = ServerProtobuf.EnterpriseEnv.CROSS;
                    }

                    FileUploadTaskInfo taskInfo = mTaskInfoMap.get(taskId);
                    IMiniSandboxContext miniSandboxContext = null;
                    if(taskInfo != null&&!TextUtils.isEmpty(taskInfo.upEa)&&!TextUtils.isEmpty(taskInfo.appId)){
                        miniSandboxContext = HostInterfaceManager.getHostInterface().getMiniSandboxContext(taskInfo.upEa,taskInfo.appId);
                    }
                    IFcpTempFileUploadListener fcpTempFileUploadListener = new FileUploadTempStateInternalCallBack(localTaskId,
                            localCallBack,miniSandboxContext);

                    new FcpTempFileUploader(fcpTempFileUploadListener).uploadTempFile_sync(localPath,
                            localExtension, env,bussnessTAG,isNeedThumbnail,isOrignalPic,timeout);
                } catch (Exception e) {
                    FCLog.e(FileServerContants.UPLOAD_TAG, "uploadTempFileASync error:" + localTaskId+ Log.getStackTraceString(e));
                }
            }
        });
    }

    public String suffix(String fileName) {
        String[] ssNames = fileName.split("\\.");
        if (ssNames.length == 0) {
            return "";
        }
        return ssNames[ssNames.length - 1];
    }

    private static class FileUploadTempStateInternalCallBack implements
            IFcpTempFileUploadListener {

        private int mTaskId;
        private IFileUploader mCallBack;
        private IMiniSandboxContext sandboxContext;

        private FileUploadTempStateInternalCallBack(int taskId,
                                                    IFileUploader callBack) {
            mTaskId = taskId;
            mCallBack = callBack;
        }
        private FileUploadTempStateInternalCallBack(int taskId,
                                                    IFileUploader callBack,IMiniSandboxContext sandboxContext) {
            mTaskId = taskId;
            mCallBack = callBack;
            this.sandboxContext = sandboxContext;
        }

        @Override
        public void onSuccess(String clientPath) {
            FCLog.d(FileServerContants.UPLOAD_TAG, "FileUploadTempState,onSuccess:" + clientPath);
            if (mCallBack != null) {
                try {
                    mCallBack.onTempFileUploadSuccess(mTaskId, clientPath);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }

        @Override
        public void onFailed(Object data) {
            FCLog.d(FileServerContants.UPLOAD_TAG, "FileUploadTempState,onFailed:" + data);
            if (mCallBack != null) {
                try {
                    mCallBack.onTempFileUploadFailed(mTaskId, data.toString());
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }

        @Override
        public void onProgress(FcpUploadParam param, int cur, int total) {
            FCLog.d(FileServerContants.UPLOAD_TAG, "FileUploadTempState,onProgress cur:" + cur+" ,total:"+total);
            if (mCallBack != null) {
                try {
                    mCallBack.onTempFileUploadProgress(mTaskId, cur, total);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }

        @Override
        public IMiniSandboxContext getSandboxContext() {
            //暂时不处理prm上游问题
            return sandboxContext;
        }
    }
}
