
package com.facishare.fs.remote_service.fileupload;

import com.facishare.fs.i18n.I18NHelper;
import android.os.Parcelable;

public abstract class FileUploadStates implements Parcelable {

    public static final int WAITING = 0;

    public static final int UPLOADING = 1;

    public static final int FAILED = 2;

    public static final int UPLOADED = 4;

    /**
     * 仅作数据通知，实际没有这种类型数据
     */
    public static final int CANCELED = 5;

    public static final String getStateDesc(int state) {
        if (state == WAITING) {
            return I18NHelper.getText("meta.adapter.NewAttachAdapter.3038")/* 等待上传 */;
        } else if (state == UPLOADING) {
            return I18NHelper.getText("th.material.base.material_uploading")/* 正在上传 */;
        }
        if (state == FAILED) {
            return I18NHelper.getText("crm.layout.function_fsnetdisk_uploading_list_item2.7785")/* 上传失败 */;
        }
        if (state == UPLOADED) {
            return I18NHelper.getText("meta.layout.item_model_attach_divider.2934")/* 上传成功 */;
        }
        return "";
    }
}
