<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
                xmlns:app="http://schemas.android.com/apk/res-auto"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginBottom="12dp"
                android:layout_marginRight="12dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="3dp"
        android:src="@drawable/ow_icn_camera_owner"/>

    <com.fxiaoke.cmviews.view.AutoScaleTextView
        android:id="@+id/tv_photo_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12.5dp"
        android:layout_toRightOf="@+id/iv_icon"
        android:textColor="@color/white"
        android:maxLines="2"
        android:textSize="15sp"
        app:minTextSize="12sp"
            android:layout_toEndOf="@+id/iv_icon"
            android:layout_marginStart="12.5dp" />


</RelativeLayout>