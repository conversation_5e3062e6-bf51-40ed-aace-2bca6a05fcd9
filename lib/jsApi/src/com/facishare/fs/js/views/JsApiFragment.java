package com.facishare.fs.js.views;


import java.util.Map;

import android.Manifest;
import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.annotation.TargetApi;
import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.graphics.Bitmap;
import android.net.Uri;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.os.RemoteException;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.view.GestureDetector;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewStub;
import android.view.animation.AccelerateInterpolator;
import android.webkit.ClientCertRequest;
import android.webkit.GeolocationPermissions;
import android.webkit.HttpAuthHandler;
import android.webkit.JavascriptInterface;
import android.webkit.JsPromptResult;
import android.webkit.JsResult;
import android.webkit.SslErrorHandler;
import android.webkit.ValueCallback;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.common_utils.ToastUtils;
import com.facishare.fs.common_utils.UrlHelper;
import com.facishare.fs.common_utils.permission.GrantedExecuter;
import com.facishare.fs.dialogs.CommonDialog;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.js.BaseActionHandler;
import com.facishare.fs.js.JSFileUploadStateCallback;
import com.facishare.fs.js.JSServerHandler;
import com.facishare.fs.js.JsApiPermissionManager;
import com.facishare.fs.js.R;
import com.facishare.fs.js.WebViewJavascriptBridge;
import com.facishare.fs.js.ava_h5.AvaSyncJsapiManager;
import com.facishare.fs.js.fsminiapp.beans.FSMiniAppBean;
import com.facishare.fs.js.fsminiapp.business.FSMiniAppBusiness;
import com.facishare.fs.js.fsminiapp.interfaces.FSMiniAppInitListener;
import com.facishare.fs.js.utils.JsApiHelper;
import com.facishare.fs.js.utils.JsapiLog;
import com.facishare.fs.js.utils.WebViewChooseFileHelper;
import com.facishare.fs.js.utils.WebViewHelper;
import com.facishare.fs.remote_service.fileserver.FileServerContants;
import com.facishare.fs.remote_service.fileupload.IFileServer;
import com.facishare.fs.remote_service.fileupload.IFileUploader;
import com.fxiaoke.cmviews.FlexiableWebView;
import com.fxiaoke.cmviews.FlexiableX5WebView;
import com.fxiaoke.cmviews.WebViewEx;
import com.fxiaoke.cmviews.X5WebViewEx;
import com.fxiaoke.cmviews.dialog.LoadingDialog;
import com.fxiaoke.cmviews.dialog.SSLDialogUtils;
import com.fxiaoke.fscommon_res.common_view.CommonTitleView;
import com.fxiaoke.fscommon_res.permission.PermissionExecuter;
import com.fxiaoke.fshttp.web.http.LocalCookie;
import com.fxiaoke.fxlog.FCLog;
import com.tencent.smtt.export.external.interfaces.ConsoleMessage;
import com.tencent.smtt.export.external.interfaces.GeolocationPermissionsCallback;
import com.tencent.smtt.export.external.interfaces.PermissionRequest;
import com.weidian.lib.hera.utils.AvaAdapterUtils;
import com.weidian.lib.hera.utils.PermissionListener;

/**
 * A simple {@link Fragment} subclass.
 */
public abstract class JsApiFragment extends Fragment {
    public JsApiFragment() {

    }
    private static final String TAG="JsApiFragment";
    private static final String WEB_CACHE_DIR = "/webcache"; // web缓存目录
    private static final String FILEUPLOADER_ID = JsApiFragment.class.getName();

    private static final String WEB_VIEW_INTERFACE="WebViewInterface";
    private static final String ACTION_GET_HTML_CONTENT="action_get_html_content";
    private static final String ACTION_GET_HTML_BODY_CONTENT="action_get_html_body_content";
    private static final String ACTION_GET_HTML_CONTENT_HEIGHT="action_get_html_content_height";
    private IAction<String> getHtmlContentAction,getHtmlBodyContentAction;
    private IAction<Integer> getHtmlContentHeightAction;

    protected WebViewChooseFileHelper webViewChooseFileHelper;

    protected WebViewJavascriptBridge mJsBridge;
    protected JSServerHandler mJsHandler;

    protected IFileServer mFileServer;
    protected IFileUploader mFileUploader;
    protected JSFileUploadStateCallback mJsFileUploadStateCallback;

    protected FragmentActivity mActivity;
    protected TextView tv_from;
    protected FrameLayout fl_webView;
    protected FlexiableWebView mWebView;
    protected JsApiWebChromeClient mJsApiWebChromeClient;
    protected JsApiWebViewClient mJsApiWebViewClient;
    protected FlexiableX5WebView mX5WebView;
    protected X5JsApiWebChromeClient mX5JsApiWebChromeClient;
    protected X5JsApiWebViewClient mX5JsApiWebViewClient;
    protected CommonTitleView mCommonTitleView;
    protected ViewStub vs_error_view;
    protected LinearLayout ll_webview_error;
    protected ProgressBar mWebProgressBar = null;
    protected boolean mShowProgressBar=true;
    protected boolean mEnableLocalCache=false;
    protected int mCurProgress = 0;
    protected final static Handler mHandler=new Handler();
    protected LoadingDialog mMiniAppLoadingDialog=null;

    GestureDetector.SimpleOnGestureListener mSimpleOnGestureListener;
    public void setSimpleOnGestureListener(GestureDetector.SimpleOnGestureListener simpleOnGestureListener){
        mSimpleOnGestureListener=simpleOnGestureListener;
    }

    protected boolean mH5TitleCanChangeTitleBar=false;//H5里面的tile是否可以改变CommonTitleView中间文本
    public void setH5TitleCanChangeTitleBar(boolean h5TitleCanChangeTitleBar){
        mH5TitleCanChangeTitleBar=h5TitleCanChangeTitleBar;
    }

    private Runnable mOnPageFinishedListener;
    public void setOnPageFinishedListener(Runnable onPageFinishedListener) {
        mOnPageFinishedListener = onPageFinishedListener;
    }

    private Runnable mFragmentInitFinishedListener;
    public void setFragmentInitFinishedListener(Runnable listener){
        mFragmentInitFinishedListener=listener;
    }

    public void scrollTo(int x,int y){
        if(mWebView!=null){
            mWebView.scrollTo(x,y);
        }
        else if(mX5WebView!=null){
            mX5WebView.getView().scrollTo(x,y);
        }
    }

    public void scrollBy(int x,int y){
        if(mWebView!=null){
            mWebView.scrollBy(x,y);
        }
        else{
            mX5WebView.getView().scrollBy(x,y);
        }
    }

    public void showLoadingProgressBar(){
        if (mWebProgressBar != null) {
            mWebProgressBar.setVisibility(View.VISIBLE);
        }
        mShowProgressBar=true;
    }

    public void hideLoadingProgressBar(){
        if (mWebProgressBar != null) {
            mWebProgressBar.setVisibility(View.GONE);
        }
        mShowProgressBar=false;
    }

    public void registerActionHandler(String action, BaseActionHandler actionHandler){
        if(TextUtils.isEmpty(action)) return;
        if(actionHandler==null) return;
        mJsHandler.registerActionHandler(action,actionHandler);
        JsApiPermissionManager.getInstance().addItem2NoPermissionJsApiList(action);
    }

    public void addCustomJavaScripInterface(CustomJavaScriptInterface customJavaScriptInterface,String interfaceName){
        if(customJavaScriptInterface==null) return;
        if(TextUtils.isEmpty(interfaceName)) return;
        if(mWebView!=null){
            mWebView.addJavascriptInterface(customJavaScriptInterface,interfaceName);
        }
        else if(mX5WebView!=null){
            mX5WebView.addJavascriptInterface(customJavaScriptInterface,interfaceName);
        }
    }

    public void addJavascriptInterface(Object obj,String interfaceName){
        if(obj==null) return;
        if(TextUtils.isEmpty(interfaceName)) return;
        if(mWebView!=null){
            mWebView.addJavascriptInterface(obj, interfaceName);
        }
        else if(mX5WebView!=null){
            mX5WebView.addJavascriptInterface(obj, interfaceName);
        }
    }

    public void callHandler(String handlerName) {
        mJsBridge.callHandler(handlerName);
    }

    public void callHandler(String handlerName, String data) {
        mJsBridge.callHandler(handlerName, data);
    }

    public void callHandler(String handlerName, String data, WebViewJavascriptBridge.WVJBResponseCallback responseCallback) {
        mJsBridge.callHandler(handlerName, data, responseCallback);
    }

    /**
     * 是否启用WebView下拉操作，默认是不启用的
     * @param enablePullDown
     */
    public void setEnablePullDown(boolean enablePullDown){
        if(mWebView!=null){
            mWebView.setEnablePullDown(enablePullDown);
        }
        else if(mX5WebView!=null){
            mX5WebView.setEnablePullDown(enablePullDown);
        }
    }

    public String getUrl(){
        if(mWebView!=null)
        {
            return mWebView.getUrl();
        }
        else if(mX5WebView!=null){
            return mX5WebView.getUrl();
        }
        return "";
    }

    public void reload(){
        if(mWebView!=null)
        {
            if(mJsApiWebViewClient!=null){
                mJsApiWebViewClient.onReloadUrl(getUrl());
            }
            mWebView.reload();
        }
        else if(mX5WebView!=null){
            if(mX5JsApiWebViewClient!=null){
                mX5JsApiWebViewClient.onReloadUrl(getUrl());
            }
            mX5WebView.reload();
        }
    }

    public void loadUrl(String url){
        if(TextUtils.isEmpty(url)) {
            JsapiLog.e(getContext(),TAG,"loadUrl null");
            return;
        }
        checkAndLoadUrl(url);
    }

    private void loadUrlNow(String url) {
        if(TextUtils.isEmpty(url)) return;
        try{
            if(mWebView!=null)
            {
                if(FSMiniAppBusiness.isFSMiniAPPUrl(url)) {
                    mWebView.getSettings().setCacheMode(WebSettings.LOAD_DEFAULT);
                } else if(WebViewHelper.isEnableCache(url)) {
                    mEnableLocalCache=true;
                    WebSettings webSettings = mWebView.getSettings();
                    //设置缓存模式
                    webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
                    // 开启DOM storage API 功能
                    webSettings.setDomStorageEnabled(true);
                    // 开启database storage API功能
                    webSettings.setDatabaseEnabled(true);
                    Log.i(TAG, "dataBasePath="+webSettings.getDatabasePath());
                    String appCachePath = mActivity.getFilesDir().getAbsolutePath() + WEB_CACHE_DIR;
                    Log.i(TAG, "appCachePath="+appCachePath);
                    // 设置数据库缓存路径
                    webSettings.setAppCachePath(appCachePath);
                    webSettings.setAppCacheEnabled(true);
                }
                mWebView.loadUrl(url);
            }
            else if(mX5WebView!=null){
                if(FSMiniAppBusiness.isFSMiniAPPUrl(url)) {
                    mX5WebView.getSettings().setCacheMode(WebSettings.LOAD_DEFAULT);
                } else if(WebViewHelper.isEnableCache(url)) {
                    mEnableLocalCache=true;
                    com.tencent.smtt.sdk.WebSettings webSettings = mX5WebView.getSettings();
                    //设置缓存模式
                    webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
                    // 开启DOM storage API 功能
                    webSettings.setDomStorageEnabled(true);
                    // 开启database storage API功能
                    webSettings.setDatabaseEnabled(true);
                    Log.i(TAG, "dataBasePath="+webSettings.getDatabasePath());
                    String appCachePath = mActivity.getFilesDir().getAbsolutePath() + WEB_CACHE_DIR;
                    Log.i(TAG, "appCachePath="+appCachePath);
                    // 设置数据库缓存路径
                    webSettings.setAppCachePath(appCachePath);
                    webSettings.setAppCacheEnabled(true);
                }
                mX5WebView.loadUrl(url);
            }
        }
        catch (Exception e){
            e.printStackTrace();
        }
    }

    private void initMiniAppLoadingDialog(){
        mMiniAppLoadingDialog = new LoadingDialog(getContext(), "", com.fxiaoke.cmviews.R.raw.mini_app_loading, LoadingDialog.Type_GIF);
        mMiniAppLoadingDialog.setCancelable(true);
        mMiniAppLoadingDialog.getWindow().setGravity(Gravity.TOP);
    }

    private void checkAndLoadUrl(String url){
        FCLog.i(TAG,"begin checkAndLoadUrl()");
        if(FSMiniAppBusiness.isFSMiniAPPUrl(url)){
            if (!PermissionExecuter.hasPermission(getContext(), Manifest.permission
                    .WRITE_EXTERNAL_STORAGE)) {
                new PermissionExecuter().requestPermissions(getContext(), Manifest.permission.WRITE_EXTERNAL_STORAGE,
                        new GrantedExecuter() {

                            @Override
                            public void exe() {
                                checkAndLoadUrl(url);
                            }
                        });
                return;
            }

            JsapiLog.i(getContext(),TAG,"FSMiniAPP Url");
            initMiniAppLoadingDialog();
            mMiniAppLoadingDialog.show();
            FSMiniAppBusiness.registerInitListener(new FSMiniAppInitListener() {
                @Override
                public void onFailed() {
                    mActivity.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            ToastUtils.show(I18NHelper.getText("xt.biz_function.CheckWebActivity.1")/* 纷享小程序初始化失败 */);
                            mMiniAppLoadingDialog.dismiss();
                            onWebViewError();
                        }
                    });
                }

                @Override
                public void onSuccess(final String baseUrl,final String html, final String js, final FSMiniAppBean appBean) {
                    mActivity.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mMiniAppLoadingDialog.dismiss();
                            setWinodw(appBean.window);
                            loadDataWithBaseURL(baseUrl,html,null,"utf-8",null);
                            //loadUrlNow(filePath);
                            loadUrlNow(js);
                        }
                    });
                }

                /**
                 * 对window进行设置
                 * @param window
                 */
                private void setWinodw(com.alibaba.fastjson.JSONObject window) {
                    if(window == null) return;
                    String title = window.getString("navigationBarTitleText");
                    String navigationBarBackgroundColor = window.getString("navigationBarBackgroundColor");
                    String navigationBarTextStyle = window.getString("navigationBarTextStyle");
                    boolean navigationBarFSMenu = window.getBoolean("navigationBarFSMenu");
                    String backgroundColor = window.getString("backgroundColor");

                    if(!TextUtils.isEmpty(title)){
                        if(mCommonTitleView!=null){
                            mCommonTitleView.setMiddleText(title);
                        }
                    }

//                    if(navigationBarFSMenu){
//                        showMoreIcon();
//                    }else{
//                        hideMoreIcon();
//                    }
//
//                    if(!TextUtils.isEmpty(navigationBarBackgroundColor)){
//                        JSTitleProperty titleProperty = new JSTitleProperty();
//                        titleProperty.setBgColor(navigationBarBackgroundColor);
//                        setTitleProperty(titleProperty);
//                    }

                    // TODO: 2017/7/20 navigationBarTextStyle backgroundColor未实现

                }
            });
            FSMiniAppBusiness.initAsync(getContext(),url);
        }
        else{
            JsapiLog.i(getContext(),TAG,"Normal Url");
            loadUrlNow(url);
        }
        FCLog.i(TAG,"end checkAndLoadUrl()");
    }

    public void loadData(String data, String mimeType, String encoding){
        if(TextUtils.isEmpty(data)) return;
        try{
            if(mWebView!=null)
            {
                if(android.os.Build.VERSION.SDK_INT>=Build.VERSION_CODES.Q){
                    mWebView.loadDataWithBaseURL("file:///android_asset", data, mimeType, encoding, null);
                }else{
                    mWebView.loadData(data,mimeType,encoding);
                }
            }
            else if(mX5WebView!=null){
                if(android.os.Build.VERSION.SDK_INT>=Build.VERSION_CODES.Q) {
                    mX5WebView.loadDataWithBaseURL("file:///android_asset", data, mimeType, encoding, null);
                }else{
                    mX5WebView.loadData(data,mimeType,encoding);
                }

            }
        }
        catch (Exception e){
            e.printStackTrace();
        }
    }

    public void loadDataWithBaseURL(String baseUrl, String data, String mimeType, String encoding, String historyUrl){
        if(TextUtils.isEmpty(data)) return;
        try{
            if(mWebView!=null)
            {
                mWebView.loadDataWithBaseURL(baseUrl, data, mimeType, encoding, historyUrl);
            }
            else if(mX5WebView!=null){
                mX5WebView.loadDataWithBaseURL(baseUrl, data, mimeType, encoding, historyUrl);
            }
        }
        catch (Exception e){
            e.printStackTrace();
        }
    }

    public void setOnTouchListener(View.OnTouchListener listener){
        if(mWebView!=null){
            mWebView.setOnTouchListener(listener);
        }
        else if(mX5WebView!=null){
            mX5WebView.setOnTouchListener(listener);
        }
    }

    public boolean canGoBack(){
        if(mWebView!=null){
            return mWebView.canGoBack();
        }
        else if(mX5WebView!=null){
            return mX5WebView.canGoBack();
        }
        return false;
    }

    public void goBack(){
        if(mWebView!=null){
            mWebView.goBack();
        }
        else if(mX5WebView!=null){
            mX5WebView.goBack();
        }
    }

    /**
     * 必须调用这个函数来初始化JSAPI，否则无法使用JSAPI
     * @param commonTitleView 可以传null，如果为Null,则表示不需要使用标题栏对应的JSAPI
     */
    public void initJsApi(CommonTitleView commonTitleView){
        JsapiLog.i(getContext(),TAG,"initJsApi");
        mCommonTitleView=commonTitleView;
        mJsHandler = new JSServerHandler(mActivity);
        mJsBridge = new WebViewJavascriptBridge(mActivity, mWebView!=null?mWebView:mX5WebView, mJsHandler);
        mJsHandler.initJavascriptBridge(mJsBridge);
        mJsBridge.setSyncJavascriptInterface(new AvaSyncJsapiManager(this.getContext(),"-1"));
        mJsFileUploadStateCallback = new JSFileUploadStateCallback(mJsBridge);
        bindService();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(mJsHandler!=null) {
            mJsHandler.processActivityResult(requestCode, resultCode, data);
        }
        if(webViewChooseFileHelper!=null) {
            webViewChooseFileHelper.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mActivity=getActivity();
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        JsapiLog.i(getContext(),TAG,"onViewCreated");
        initWebViewInterface();

        if(mFragmentInitFinishedListener!=null)
            mFragmentInitFinishedListener.run();
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }

    public void updateTitleBarTitle(String title){
        if(TextUtils.isEmpty(title)) return;
        if(mCommonTitleView==null) return;
        if(!mH5TitleCanChangeTitleBar) return;
        mCommonTitleView.setTitle(title);
    }

    public String getWebViewTitle() {
        if(mCommonTitleView==null) return null;
        return mCommonTitleView.getCenterTxtView().getText().toString();
    }

    private void onWebViewError() {
        if(ll_webview_error==null&&vs_error_view!=null){
            ll_webview_error=(LinearLayout) vs_error_view.inflate();
            if (ll_webview_error!=null){
                ll_webview_error.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        reload();
                        ll_webview_error.setVisibility(View.GONE);
                        fl_webView.setVisibility(View.VISIBLE);
                    }
                });
            }

        }
        if (ll_webview_error!=null&&fl_webView!=null){
            ll_webview_error.setVisibility(View.VISIBLE);
            fl_webView.setVisibility(View.GONE);
        }

    }

    private void onWebViewReceivedError(String curUrl,String reqUrl){
        if(TextUtils.isEmpty(curUrl)) return;
        if(TextUtils.isEmpty(reqUrl)) return;
        if(!curUrl.equalsIgnoreCase(reqUrl)) return;

        Uri uriReq=null;
        try
        {
            uriReq=Uri.parse(reqUrl);
        }
        catch (Exception e){
            e.printStackTrace();
        }
        if(uriReq==null) return;

        String host=uriReq.getScheme();
        if(host.equalsIgnoreCase("http") || host.equalsIgnoreCase("https"))
        {
            onWebViewError();
        }
    }

    protected class JsApiWebChromeClientEx extends WebViewEx.WebChromeClientEx{
        public JsApiWebChromeClientEx(){
            super(mWebView);
        }

        private void animationProgress(int from, int to, final Runnable completedAction) {
            ObjectAnimator animator = ObjectAnimator.ofInt(mWebProgressBar, "progress", from, to);
            animator.setDuration(300);
            animator.setInterpolator(new AccelerateInterpolator());
            animator.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {

                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    if (completedAction != null)
                        completedAction.run();
                }

                @Override
                public void onAnimationCancel(Animator animation) {

                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });
            animator.start();
        }
        @Override
        public boolean onConsoleMessage(android.webkit.ConsoleMessage consoleMessage) {
            Context context=getContext();
            if(context!=null){
                JsapiLogAdapter.getInstance(context).log(consoleMessage,"","jsapi_sys");
            }
//            return super.onConsoleMessage(consoleMessage);
            return true;
        }
        @Override
        public void onProgressChanged(WebView view, int newProgress) {
            if (mWebProgressBar==null) return;

            if (mCurProgress > newProgress)
                return;
            if (newProgress == 100 && mCurProgress != 100) {
                animationProgress(mCurProgress, newProgress, new Runnable() {
                    @Override
                    public void run() {
                        if (mWebProgressBar!=null){
                            mWebProgressBar.setVisibility(View.GONE);
                            mCurProgress = 0;
                            mWebProgressBar.setProgress(0);
                        }
                    }
                });
                mCurProgress = newProgress;
            } else {
                if(mShowProgressBar){
                    mWebProgressBar.setVisibility(View.VISIBLE);
                }
                mCurProgress = newProgress;
                mWebProgressBar.setProgress(newProgress);
            }
            if(mJsApiWebChromeClient!=null)
                mJsApiWebChromeClient.onProgressChanged(newProgress);
        }

        @Override
        public void onReceivedTitle(WebView view, String title) {
            super.onReceivedTitle(view, title);
            FCLog.i("onReceivedTitle" + title+" url="+view.getUrl());
            //部分手机url为空的时候会返回url地址或者url地址去掉sechema后的地址当作title,下面进行过滤处理
            String url=view.getUrl();
            if(title.equalsIgnoreCase(url)) return;
            String url2="http://"+title;
            if(url2.equalsIgnoreCase(url)) return;
            url2="https://"+title;
            if(url2.equalsIgnoreCase(url)) return;
            updateTitleBarTitle(title);
            if(mJsApiWebChromeClient!=null)
                mJsApiWebChromeClient.onReceivedTitle(title);
        }

        @Override
        public void onReceivedIcon(WebView webView, Bitmap bitmap) {
            super.onReceivedIcon(webView, bitmap);
            if(mJsApiWebChromeClient!=null)
                mJsApiWebChromeClient.onReceivedIcon(bitmap);
        }

        @Override
        public void onReceivedTouchIconUrl(WebView webView, String url, boolean precomposed) {
            super.onReceivedTouchIconUrl(webView, url, precomposed);
            if(mJsApiWebChromeClient!=null)
                mJsApiWebChromeClient.onReceivedTouchIconUrl(url, precomposed);
        }

        @Override
        public boolean onCreateWindow(WebView webView, boolean isDialog, boolean isUserGesture, Message resultMsg) {
            if(mJsApiWebChromeClient!=null)
                return mJsApiWebChromeClient.onCreateWindow(isDialog,isUserGesture,resultMsg);
            return super.onCreateWindow(webView, isDialog,isUserGesture,resultMsg);
        }

        @Override
        public void onRequestFocus(WebView webView) {
            super.onRequestFocus(webView);
            if(mJsApiWebChromeClient!=null)
                mJsApiWebChromeClient.onRequestFocus();
        }

        @Override
        public void onCloseWindow(WebView webView) {
            super.onCloseWindow(webView);
            if(mJsApiWebChromeClient!=null)
                mJsApiWebChromeClient.onCloseWindow();
        }

        @Override
        public boolean onJsAlert(WebView webView, String url, String message, JsResult result) {
            if(mJsApiWebChromeClient!=null)
                return mJsApiWebChromeClient.onJsAlert(url, message, result);
            return super.onJsAlert(webView, url,message,result);
        }

        @Override
        public boolean onJsConfirm(WebView webView, String url, String message, JsResult result) {
            if(mJsApiWebChromeClient!=null)
                mJsApiWebChromeClient.onJsConfirm(url, message, result);
            return super.onJsConfirm(webView, url, message, result);
        }

        @Override
        public boolean onJsPrompt(WebView view, String url, String message, String defaultValue, JsPromptResult result) {
            if(mJsApiWebChromeClient!=null)
                return mJsApiWebChromeClient.onJsPrompt(url, message, defaultValue, result);
            return super.onJsPrompt(view, url, message, defaultValue, result);
        }

        @Override
        public boolean onJsBeforeUnload(WebView webView, String url, String message, JsResult result) {
            if(mJsApiWebChromeClient!=null)
                mJsApiWebChromeClient.onJsBeforeUnload(url, message, result);
            return super.onJsBeforeUnload(webView,url,message,result);
        }

        // TODO: 1、加混淆，2、参数解析
        //For Android >= 4.1
        public void openFileChooser(android.webkit.ValueCallback<Uri> valueCallback, String acceptType, String capture) {
            webViewChooseFileHelper.openFileChooserDialog(valueCallback, acceptType, capture);
        }

        // For Android >= 5.0
        @Override
        public boolean onShowFileChooser(WebView webView, android.webkit.ValueCallback<Uri[]> filePathCallback,
                                         FileChooserParams fileChooserParams) {
            return webViewChooseFileHelper.showFileChooserDialog(filePathCallback, fileChooserParams);
        }

        @Override
        public void onGeolocationPermissionsShowPrompt(String origin, GeolocationPermissions.Callback callback) {
            JsApiHelper.onGeolocationPermissionsShowPrompt(getActivity(),origin, new Runnable() {
                @Override
                public void run() {
                    callback.invoke(origin,false,true);//拒绝
                }
            }, new Runnable() {
                @Override
                public void run() {
                    callback.invoke(origin,true,true);//允许
                }
            });
        }

        @Override
        public void onPermissionRequest(android.webkit.PermissionRequest request) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                AvaAdapterUtils.getAvaPermissionAdapter().requestWebViewPermissions(getActivity(), request.getResources(), new PermissionListener() {
                    @Override
                    public void granted() {
                        request.grant(request.getResources());
                    }

                    @Override
                    public void failed() {
                        request.deny();
                    }

                    @Override
                    public boolean isClosePage() {
                        return false;
                    }
                });
            }
        }
    }

    protected class JsApiWebViewClientEx extends WebViewEx.WebViewClientEx{
        public JsApiWebViewClientEx(){
            super(mWebView);
        }

        @Override
        public boolean shouldOverrideUrlLoading(WebView webView, String url) {
            if(WebViewHelper.shouldOverrideUrlLoading(webView.getContext(),url,webView.getSettings().getUserAgentString())) return true;
            if(mJsApiWebViewClient!=null)
                return mJsApiWebViewClient.shouldOverrideUrlLoading(url);
            return super.shouldOverrideUrlLoading(webView, url);
        }

        @Override
        public void onPageStarted(WebView webView, String url, Bitmap favicon) {
            super.onPageStarted(webView, url, favicon);
            if(mJsApiWebViewClient!=null)
                mJsApiWebViewClient.onPageStarted(url, favicon);
        }

        @Override
        public void onPageFinished(WebView webView, String url) {
            super.onPageFinished(webView, url);
            String hostName= UrlHelper.getHostName(url);
            if(!TextUtils.isEmpty(hostName)&&tv_from!=null){
                tv_from.setText(I18NHelper.getFormatText("jsapi.fragment.common.webpage_provided_by",hostName)/* 网页由{0}提供 */);
            }
            WebViewHelper.loadJsBridge(webView);
            if (mOnPageFinishedListener != null) {
                mOnPageFinishedListener.run();
            }
            if(mJsApiWebViewClient!=null)
                mJsApiWebViewClient.onPageFinished(url);
        }

        @Override
        public void onLoadResource(WebView view, String url) {
            super.onLoadResource(view, url);
            if(mJsApiWebViewClient!=null)
                mJsApiWebViewClient.onLoadResource(url);
        }

        @TargetApi(Build.VERSION_CODES.LOLLIPOP)
        @Override
        public WebResourceResponse shouldInterceptRequest(WebView webView, WebResourceRequest request) {
            if(mJsApiWebViewClient!=null)
                mJsApiWebViewClient.shouldInterceptRequest();
            return WebViewHelper.interceptRequest(webView.getContext(),request.getUrl().toString(),mEnableLocalCache);
        }

        @Override
        public WebResourceResponse shouldInterceptRequest(WebView webView, String url) {
            if(mJsApiWebViewClient!=null)
                mJsApiWebViewClient.shouldInterceptRequest();
            return WebViewHelper.interceptRequest(webView.getContext(),url,mEnableLocalCache);
        }

        @Override
        public void onTooManyRedirects(WebView webView, Message cancelMsg, Message continueMsg) {
            super.onTooManyRedirects(webView, cancelMsg, continueMsg);
            if(mJsApiWebViewClient!=null)
                mJsApiWebViewClient.onTooManyRedirects(cancelMsg, continueMsg);
        }

        @Override
        public void onReceivedError(WebView webView, int errorCode, String description, String failingUrl) {
            super.onReceivedError(webView, errorCode, description, failingUrl);
            FCLog.e(TAG,"onReceivedError,errCode="+errorCode+",errMsg="+description+",req url="+failingUrl);
            onWebViewReceivedError(webView.getUrl(),failingUrl);
            if(mJsApiWebViewClient!=null)
                mJsApiWebViewClient.onReceivedError(errorCode, description, failingUrl);
        }

        @TargetApi(23)
        @Override
        public void onReceivedError(WebView webView, WebResourceRequest request, WebResourceError error) {
            super.onReceivedError(webView, request, error);
            FCLog.e(TAG,"onReceivedError,errCode="+error.getErrorCode()+",errMsg="+error.getDescription()+",req url="+request.getUrl().toString());
            onWebViewReceivedError(webView.getUrl(),request.getUrl().toString());
            if(mJsApiWebViewClient!=null)
                mJsApiWebViewClient.onReceivedError();
        }

        @TargetApi(Build.VERSION_CODES.LOLLIPOP)
        @Override
        public void onReceivedHttpError(WebView webView, WebResourceRequest webResourceRequest, WebResourceResponse webResourceResponse) {
            super.onReceivedHttpError(webView, webResourceRequest, webResourceResponse);
            if(mJsApiWebViewClient!=null)
                mJsApiWebViewClient.onReceivedHttpError(webResourceResponse.getStatusCode(),webResourceRequest.getUrl().toString());
        }

        @Override
        public void onFormResubmission(WebView webView, Message dontResend, Message resend) {
            super.onFormResubmission(webView, dontResend, resend);
            if(mJsApiWebViewClient!=null)
                mJsApiWebViewClient.onFormResubmission(dontResend, resend);
        }

        @Override
        public void doUpdateVisitedHistory(WebView view, String url, boolean isReload) {
            super.doUpdateVisitedHistory(view, url, isReload);
            if(mJsApiWebViewClient!=null)
                mJsApiWebViewClient.doUpdateVisitedHistory(url, isReload);
        }

        @Override
        public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
            super.onReceivedSslError(view, handler, error);
            SSLDialogUtils.showDialog(view.getContext(), error.getPrimaryError(),handler);
            if(mJsApiWebViewClient!=null)
                mJsApiWebViewClient.onReceivedSslError();
        }

        @TargetApi(21)
        @Override
        public void onReceivedClientCertRequest(WebView webView, ClientCertRequest clientCertRequest) {
            super.onReceivedClientCertRequest(webView, clientCertRequest);
            showIgnoreCertRequestDialog(webView.getContext(), webView.getUrl(), new Runnable() {
                @Override
                public void run() {
                    ignoreClientCertRequest(clientCertRequest);
//                    clientCertRequest.ignore();
                    if (mJsApiWebViewClient != null) {
                        mJsApiWebViewClient.onReceivedClientCertRequest();
                    }
                }
            });

        }

        @Override
        public void onReceivedHttpAuthRequest(WebView webView, HttpAuthHandler httpAuthHandler, String host, String realm) {
            super.onReceivedHttpAuthRequest(webView, httpAuthHandler, host, realm);
            if(mJsApiWebViewClient!=null)
                mJsApiWebViewClient.onReceivedHttpAuthRequest(host, realm);
        }

        @Override
        public boolean shouldOverrideKeyEvent(WebView webView, KeyEvent keyEvent) {
            if(mJsApiWebViewClient!=null)
                mJsApiWebViewClient.shouldOverrideKeyEvent(keyEvent);
            return super.shouldOverrideKeyEvent(webView, keyEvent);
        }

        @Override
        public void onUnhandledKeyEvent(WebView webView, KeyEvent keyEvent) {
            super.onUnhandledKeyEvent(webView, keyEvent);
            if(mJsApiWebViewClient!=null)
                mJsApiWebViewClient.onUnhandledKeyEvent(keyEvent);
        }

        @Override
        public void onReceivedLoginRequest(WebView webView, String realm, String account, String args) {
            super.onReceivedLoginRequest(webView, realm, account, args);
            if(mJsApiWebViewClient!=null)
                mJsApiWebViewClient.onReceivedLoginRequest(realm, account, args);
        }
    }

    protected class X5JsApiWebChromeClientEx extends X5WebViewEx.WebChromeClientEx{
        public X5JsApiWebChromeClientEx(){
            super(mX5WebView);
        }

        private void animationProgress(int from, int to, final Runnable completedAction) {
            ObjectAnimator animator = ObjectAnimator.ofInt(mWebProgressBar, "progress", from, to);
            animator.setDuration(300);
            animator.setInterpolator(new AccelerateInterpolator());
            animator.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {

                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    if (completedAction != null)
                        completedAction.run();
                }

                @Override
                public void onAnimationCancel(Animator animation) {

                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });
            animator.start();
        }

        @Override
        public void onProgressChanged(com.tencent.smtt.sdk.WebView view, int newProgress) {
            if (mWebProgressBar==null) return;
            if (mCurProgress > newProgress)
                return;
            if (newProgress == 100 && mCurProgress != 100) {
                animationProgress(mCurProgress, newProgress, new Runnable() {
                    @Override
                    public void run() {
                        if (mWebProgressBar!=null){
                            mWebProgressBar.setVisibility(View.GONE);
                            mCurProgress = 0;
                            mWebProgressBar.setProgress(0);
                        }

                    }
                });
                mCurProgress = newProgress;
            } else {
                if(mShowProgressBar){
                    mWebProgressBar.setVisibility(View.VISIBLE);
                }
                mCurProgress = newProgress;
                mWebProgressBar.setProgress(newProgress);
            }
            if(mX5JsApiWebChromeClient!=null)
                mX5JsApiWebChromeClient.onProgressChanged(newProgress);
        }

        @Override
        public void onReceivedTitle(com.tencent.smtt.sdk.WebView view, String title) {
            super.onReceivedTitle(view, title);
            FCLog.i("onReceivedTitle" + title);
            //部分手机url为空的时候会返回url地址或者url地址去掉sechema后的地址当作title,下面进行过滤处理
            String url=view.getUrl();
            if(title.equalsIgnoreCase(url)) return;
            String url2="http://"+title;
            if(url2.equalsIgnoreCase(url)) return;
            url2="https://"+title;
            if(url2.equalsIgnoreCase(url)) return;
            updateTitleBarTitle(title);
            if(mX5JsApiWebChromeClient!=null)
                mX5JsApiWebChromeClient.onReceivedTitle(title);
        }

        @Override
        public void onReceivedIcon(com.tencent.smtt.sdk.WebView webView, Bitmap bitmap) {
            super.onReceivedIcon(webView, bitmap);
            if(mX5JsApiWebChromeClient!=null)
                mX5JsApiWebChromeClient.onReceivedIcon(bitmap);
        }

        @Override
        public void onReceivedTouchIconUrl(com.tencent.smtt.sdk.WebView webView, String url, boolean precomposed) {
            super.onReceivedTouchIconUrl(webView, url, precomposed);
            if(mX5JsApiWebChromeClient!=null)
                mX5JsApiWebChromeClient.onReceivedTouchIconUrl(url, precomposed);
        }

        @Override
        public boolean onCreateWindow(com.tencent.smtt.sdk.WebView webView, boolean isDialog, boolean isUserGesture, Message resultMsg) {
            if(mX5JsApiWebChromeClient!=null)
                return mX5JsApiWebChromeClient.onCreateWindow(isDialog,isUserGesture,resultMsg);
            return super.onCreateWindow(webView, isDialog,isUserGesture,resultMsg);
        }

        @Override
        public void onRequestFocus(com.tencent.smtt.sdk.WebView webView) {
            super.onRequestFocus(webView);
            if(mX5JsApiWebChromeClient!=null)
                mX5JsApiWebChromeClient.onRequestFocus();
        }

        @Override
        public void onCloseWindow(com.tencent.smtt.sdk.WebView webView) {
            super.onCloseWindow(webView);
            if(mX5JsApiWebChromeClient!=null)
                mX5JsApiWebChromeClient.onCloseWindow();
        }

        @Override
        public boolean onJsAlert(com.tencent.smtt.sdk.WebView webView, String url, String message, com.tencent.smtt.export.external.interfaces.JsResult result) {
            if(mX5JsApiWebChromeClient!=null)
                return mX5JsApiWebChromeClient.onJsAlert(url, message, result);
            return super.onJsAlert(webView, url,message,result);
        }

        @Override
        public boolean onJsConfirm(com.tencent.smtt.sdk.WebView webView, String url, String message, com.tencent.smtt.export.external.interfaces.JsResult result) {
            if(mX5JsApiWebChromeClient!=null)
                mX5JsApiWebChromeClient.onJsConfirm(url, message, result);
            return super.onJsConfirm(webView, url, message, result);
        }

        @Override
        public boolean onJsPrompt(com.tencent.smtt.sdk.WebView view, String url, String message, String defaultValue, com.tencent.smtt.export.external.interfaces.JsPromptResult result) {
            if(mX5JsApiWebChromeClient!=null)
                return mX5JsApiWebChromeClient.onJsPrompt(url, message, defaultValue, result);
            return super.onJsPrompt(view, url, message, defaultValue, result);
        }

        @Override
        public boolean onJsBeforeUnload(com.tencent.smtt.sdk.WebView webView, String url, String message, com.tencent.smtt.export.external.interfaces.JsResult result) {
            if(mX5JsApiWebChromeClient!=null)
                mX5JsApiWebChromeClient.onJsBeforeUnload(url, message, result);
            return super.onJsBeforeUnload(webView,url,message,result);
        }

        //For Android >= 4.1
        @Override
        public void openFileChooser(com.tencent.smtt.sdk.ValueCallback<Uri> valueCallback, String acceptType, String capture) {
            webViewChooseFileHelper.openFileChooserDialog(valueCallback, acceptType, capture);
        }

        // For Android >= 5.0
        @Override
        public boolean onShowFileChooser(com.tencent.smtt.sdk.WebView webView, com.tencent.smtt.sdk.ValueCallback<Uri[]> valueCallback,
                                         FileChooserParams fileChooserParams) {
            return webViewChooseFileHelper.showFileChooserDialog(valueCallback, fileChooserParams);
        }

        @Override
        public void onGeolocationPermissionsShowPrompt(String origin, GeolocationPermissionsCallback callback) {
            JsApiHelper.onGeolocationPermissionsShowPrompt(getActivity(),origin, new Runnable() {
                @Override
                public void run() {
                    callback.invoke(origin,false,true);//拒绝
                }
            }, new Runnable() {
                @Override
                public void run() {
                    callback.invoke(origin,true,true);//允许
                }
            });
        }

        @Override
        public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
            Context context=getContext();
            if(context!=null){
                JsapiLogAdapter.getInstance(context).log(consoleMessage,"","jsapi_x5");
            }
//            return super.onConsoleMessage(consoleMessage);
            return true;
        }

        @Override
        public void onPermissionRequest(PermissionRequest permissionRequest) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                AvaAdapterUtils.getAvaPermissionAdapter().requestWebViewPermissions(getActivity(), permissionRequest.getResources(), new PermissionListener() {
                    @Override
                    public void granted() {
                        permissionRequest.grant(permissionRequest.getResources());
                    }

                    @Override
                    public void failed() {
                        permissionRequest.deny();
                    }

                    @Override
                    public boolean isClosePage() {
                        return false;
                    }
                });
            }
        }

    }

    protected class X5JsApiWebViewClientEx extends X5WebViewEx.WebViewClientEx{
        public X5JsApiWebViewClientEx(){
            super(mX5WebView);
        }

        @Override
        public boolean shouldOverrideUrlLoading(com.tencent.smtt.sdk.WebView webView, String url) {
            if(WebViewHelper.shouldOverrideUrlLoading(webView.getContext(),url,webView.getSettings().getUserAgentString())) return true;
            if(mX5JsApiWebViewClient!=null)
                return mX5JsApiWebViewClient.shouldOverrideUrlLoading(url);
            return super.shouldOverrideUrlLoading(webView, url);
        }

        @Override
        public void onPageStarted(com.tencent.smtt.sdk.WebView view, String url, Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.onPageStarted(url, favicon);
        }

        @Override
        public void onPageFinished(com.tencent.smtt.sdk.WebView webView, String url) {
            super.onPageFinished(webView, url);
            String hostName=UrlHelper.getHostName(url);
            if(!TextUtils.isEmpty(hostName)&&tv_from!=null){
                tv_from.setText(I18NHelper.getFormatText("jsapi.fragment.common.this_webpage_provided_by",hostName)/* 此网页由{0}提供 */);
            }
            WebViewHelper.loadJsBridge(webView);
            if (mOnPageFinishedListener != null) {
                mOnPageFinishedListener.run();
            }
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.onPageFinished(url);
        }

        @Override
        public void onLoadResource(com.tencent.smtt.sdk.WebView view, String url) {
            super.onLoadResource(view, url);
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.onLoadResource(url);
        }

        @TargetApi(Build.VERSION_CODES.LOLLIPOP)
        @Override
        public com.tencent.smtt.export.external.interfaces.WebResourceResponse shouldInterceptRequest(com.tencent.smtt.sdk.WebView webView, com.tencent.smtt.export.external.interfaces.WebResourceRequest request) {
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.shouldInterceptRequest();
            return WebViewHelper.interceptX5Request(webView.getContext(),request.getUrl().toString(),mEnableLocalCache);
        }

        @Override
        public com.tencent.smtt.export.external.interfaces.WebResourceResponse shouldInterceptRequest(com.tencent.smtt.sdk.WebView webView, String url) {
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.shouldInterceptRequest();
            return WebViewHelper.interceptX5Request(webView.getContext(),url,mEnableLocalCache);
        }

        @Override
        public com.tencent.smtt.export.external.interfaces.WebResourceResponse shouldInterceptRequest(com.tencent.smtt.sdk.WebView webView, com.tencent.smtt.export.external.interfaces.WebResourceRequest request, Bundle bundle) {
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.shouldInterceptRequest();
            return WebViewHelper.interceptX5Request(webView.getContext(),request.getUrl().toString(),mEnableLocalCache);
        }

        @Override
        public void onTooManyRedirects(com.tencent.smtt.sdk.WebView webView, Message cancelMsg, Message continueMsg) {
            super.onTooManyRedirects(webView, cancelMsg, continueMsg);
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.onTooManyRedirects(cancelMsg, continueMsg);
        }

        @Override
        public void onReceivedError(com.tencent.smtt.sdk.WebView webView, int errorCode, String description, String failingUrl) {
            super.onReceivedError(webView, errorCode, description, failingUrl);
            FCLog.e(TAG,"onReceivedError,errCode="+errorCode+",errMsg="+description+",req url="+failingUrl);
            onWebViewReceivedError(webView.getUrl(),failingUrl);
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.onReceivedError(errorCode, description, failingUrl);
        }

        @Override
        public void onReceivedError(com.tencent.smtt.sdk.WebView webView, com.tencent.smtt.export.external.interfaces.WebResourceRequest request, com.tencent.smtt.export.external.interfaces.WebResourceError error) {
            super.onReceivedError(webView, request, error);
            FCLog.e(TAG,"onReceivedError,errCode="+error.getErrorCode()+",errMsg="+error.getDescription()+",req url="+request.getUrl().toString());
            onWebViewReceivedError(webView.getUrl(),request.getUrl().toString());
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.onReceivedError();
        }

        @Override
        public void onReceivedHttpError(com.tencent.smtt.sdk.WebView webView, com.tencent.smtt.export.external.interfaces.WebResourceRequest webResourceRequest, com.tencent.smtt.export.external.interfaces.WebResourceResponse webResourceResponse) {
            super.onReceivedHttpError(webView, webResourceRequest, webResourceResponse);
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.onReceivedHttpError(webResourceResponse.getStatusCode(),webResourceRequest.getUrl().toString());
        }

        @Override
        public void onFormResubmission(com.tencent.smtt.sdk.WebView webView, Message dontResend, Message resend) {
            super.onFormResubmission(webView, dontResend, resend);
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.onFormResubmission(dontResend, resend);
        }

        @Override
        public void doUpdateVisitedHistory(com.tencent.smtt.sdk.WebView view, String url, boolean isReload) {
            super.doUpdateVisitedHistory(view, url, isReload);
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.doUpdateVisitedHistory(url, isReload);
        }

        @Override
        public void onReceivedSslError(com.tencent.smtt.sdk.WebView view, com.tencent.smtt.export.external.interfaces.SslErrorHandler handler, com.tencent.smtt.export.external.interfaces.SslError error) {
            super.onReceivedSslError(view, handler, error);
            SSLDialogUtils.showDialog(view.getContext(), error.getPrimaryError(),handler);
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.onReceivedSslError();
        }

        @Override
        public void onReceivedClientCertRequest(com.tencent.smtt.sdk.WebView webView, com.tencent.smtt.export.external.interfaces.ClientCertRequest clientCertRequest) {
            super.onReceivedClientCertRequest(webView, clientCertRequest);
            showIgnoreCertRequestDialog(webView.getContext(), webView.getUrl(), new Runnable() {
                @Override
                public void run() {
                    ignoreClientCertRequest(clientCertRequest);
                    if (mX5JsApiWebViewClient != null) {
                        mX5JsApiWebViewClient.onReceivedClientCertRequest();
                    }
                }
            });
        }

        @Override
        public void onReceivedHttpAuthRequest(com.tencent.smtt.sdk.WebView webView, com.tencent.smtt.export.external.interfaces.HttpAuthHandler httpAuthHandler, String host, String realm) {
            super.onReceivedHttpAuthRequest(webView, httpAuthHandler, host, realm);
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.onReceivedHttpAuthRequest(host, realm);
        }

        @Override
        public boolean shouldOverrideKeyEvent(com.tencent.smtt.sdk.WebView webView, KeyEvent keyEvent) {
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.shouldOverrideKeyEvent(keyEvent);
            return super.shouldOverrideKeyEvent(webView, keyEvent);
        }

        @Override
        public void onUnhandledKeyEvent(com.tencent.smtt.sdk.WebView webView, KeyEvent keyEvent) {
            super.onUnhandledKeyEvent(webView, keyEvent);
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.onUnhandledKeyEvent(keyEvent);
        }

        @Override
        public void onReceivedLoginRequest(com.tencent.smtt.sdk.WebView webView, String realm, String account, String args) {
            super.onReceivedLoginRequest(webView, realm, account, args);
            if(mX5JsApiWebViewClient!=null)
                mX5JsApiWebViewClient.onReceivedLoginRequest(realm, account, args);
        }
    }

    /**
     * 获取网页的整个html元素的内容，包括html元素
     * @param action
     */
    public void getHtmlContent(IAction<String> action){
        getHtmlContentAction=action;
        String js="JSON.stringify(document.documentElement.outerHTML)";
        try {
            if(Build.VERSION.SDK_INT>=Build.VERSION_CODES.KITKAT)
            {
                if(mWebView!=null){
                    mWebView.evaluateJavascript("javascript:"+js, new ValueCallback<String>() {
                        @Override
                        public void onReceiveValue(String value) {
                            String data="";
                            try {
                                data= JSONObject.parseObject(value,String.class);
                            }
                            catch (Exception e){
                                e.printStackTrace();
                            }
                            if(getHtmlContentAction!=null){
                                getHtmlContentAction.action(data);
                            }
                            getHtmlContentAction=null;
                        }
                    });
                }
                else if(mX5WebView!=null){
                    mX5WebView.evaluateJavascript("javascript:"+js, new com.tencent.smtt.sdk.ValueCallback<String>() {
                        @Override
                        public void onReceiveValue(String value) {
                            String data="";
                            try {
                                data= JSONObject.parseObject(value,String.class);
                            }
                            catch (Exception e){
                                e.printStackTrace();
                            }
                            if(getHtmlContentAction!=null){
                                getHtmlContentAction.action(data);
                            }
                            getHtmlContentAction=null;
                        }
                    });
                }
            }
            else{
                String js2="javascript:WebViewInterface.handle('"+ACTION_GET_HTML_CONTENT+"',"+js+")";
                loadUrlNow(js2);
            }
        }
        catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 获取网页的body元素的内容，不包括body元素
     * @param action
     */
    public void getHtmlBodyContent(IAction<String> action){
        getHtmlBodyContentAction=action;
        String js="JSON.stringify(document.body.innerHTML)";
        try {
            if(Build.VERSION.SDK_INT>=Build.VERSION_CODES.KITKAT)
            {
                if(mWebView!=null){
                    mWebView.evaluateJavascript("javascript:"+js, new ValueCallback<String>() {
                        @Override
                        public void onReceiveValue(String value) {
                            String data="";
                            try {
                                data= JSONObject.parseObject(value,String.class);
                            }
                            catch (Exception e){
                                e.printStackTrace();
                            }
                            if(getHtmlBodyContentAction!=null){
                                getHtmlBodyContentAction.action(data);
                            }
                            getHtmlBodyContentAction=null;
                        }
                    });
                }
                else if(mX5WebView!=null){
                    mX5WebView.evaluateJavascript("javascript:"+js, new com.tencent.smtt.sdk.ValueCallback<String>() {
                        @Override
                        public void onReceiveValue(String value) {
                            String data="";
                            try {
                                data= JSONObject.parseObject(value,String.class);
                            }
                            catch (Exception e){
                                e.printStackTrace();
                            }
                            if(getHtmlBodyContentAction!=null){
                                getHtmlBodyContentAction.action(data);
                            }
                            getHtmlBodyContentAction=null;
                        }
                    });
                }
            }
            else{
                String js2="javascript:WebViewInterface.handle('"+ACTION_GET_HTML_BODY_CONTENT+"',"+js+")";
                loadUrlNow(js2);
            }
        }
        catch (Exception e){
            e.printStackTrace();
        }
    }

    public void getHtmlContentHeight(IAction<Integer> action){
        if(mWebView==null) return;
        getHtmlContentHeightAction=action;
        String js="document.body.scrollHeight";
        try {
            if(Build.VERSION.SDK_INT>=Build.VERSION_CODES.KITKAT)
            {
                if(mWebView!=null){
                    mWebView.evaluateJavascript("javascript:"+js, new ValueCallback<String>() {
                        @Override
                        public void onReceiveValue(String value) {
                            int data=0;
                            try {
                                data= Integer.parseInt(value);
                            }
                            catch (Exception e){
                                e.printStackTrace();
                            }
                            if(getHtmlContentHeightAction!=null){
                                getHtmlContentHeightAction.action(data);
                            }
                            getHtmlContentHeightAction=null;
                        }
                    });
                }
                else if(mX5WebView!=null){
                    mX5WebView.evaluateJavascript("javascript:"+js, new com.tencent.smtt.sdk.ValueCallback<String>() {
                        @Override
                        public void onReceiveValue(String value) {
                            int data=0;
                            try {
                                data= Integer.parseInt(value);
                            }
                            catch (Exception e){
                                e.printStackTrace();
                            }
                            if(getHtmlContentHeightAction!=null){
                                getHtmlContentHeightAction.action(data);
                            }
                            getHtmlContentHeightAction=null;
                        }
                    });
                }
            }
            else{
                String js2="javascript:WebViewInterface.handle('"+ACTION_GET_HTML_CONTENT_HEIGHT+"',"+js+")";
                loadUrlNow(js2);
            }
        }
        catch (Exception e){
            e.printStackTrace();
        }
    }

    public void initWebViewInterface(){
        Object jsInterface=new Object(){
            @JavascriptInterface
            public void handle(String action, String data){
                String data2="";
                try {
                    data2=JSONObject.parseObject(data,String.class);
                }
                catch (Exception e){
                    e.printStackTrace();
                }
                if(action.equalsIgnoreCase(ACTION_GET_HTML_CONTENT)){
                    if(getHtmlContentAction!=null){
                        getHtmlContentAction.action(data2);
                    }
                    getHtmlContentAction=null;
                }
                else if(action.equalsIgnoreCase(ACTION_GET_HTML_BODY_CONTENT)){
                    if(getHtmlBodyContentAction!=null){
                        getHtmlBodyContentAction.action(data2);
                    }
                    getHtmlBodyContentAction=null;
                }
            }
        };
        if(mWebView!=null){
            mWebView.addJavascriptInterface(jsInterface,WEB_VIEW_INTERFACE);
        }
        else if(mX5WebView!=null){
            mX5WebView.addJavascriptInterface(jsInterface,WEB_VIEW_INTERFACE);
        }
    }

    protected void initWebView(){
        WebSettings webSettings= mWebView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setBlockNetworkImage(false);
        webSettings.setBlockNetworkLoads(false);
        webSettings.setLoadsImagesAutomatically(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setDisplayZoomControls(false);
        webSettings.setBuiltInZoomControls(true);// 2
        webSettings.setSupportZoom(true);
        webSettings.setUseWideViewPort(true);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setCacheMode(WebSettings.LOAD_NO_CACHE);// webview页面不使用缓存
        // anjx 0825 add支持https环境下访问http资源。
        if (Build.VERSION.SDK_INT >= 21) {
            webSettings.setMixedContentMode(android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.JELLY_BEAN) //cdn  iconfont问题
        {
            webSettings.setAllowUniversalAccessFromFileURLs(true);
        }
        String agent = webSettings.getUserAgentString();
        if (!TextUtils.isEmpty(agent)) {
            webSettings.setUserAgentString(agent + " FSBrowser/" + JsApiHelper.getAppVersion());
        }

        mWebView.setHorizontalScrollBarEnabled(false);
        mWebView.setInitialScale(15);
        mWebView.setScrollBarStyle(View.SCROLLBARS_INSIDE_OVERLAY);

        mWebView.setWebChromeClient(new JsApiWebChromeClientEx());
        mWebView.setWebViewClient(new JsApiWebViewClientEx());

        if(mSimpleOnGestureListener!=null){
            final GestureDetector mGestureDetector=new GestureDetector(mWebView.getContext(),mSimpleOnGestureListener);
            mWebView.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    return mGestureDetector.onTouchEvent(event);
                }
            });
        }
        WebViewHelper.setCookies4FS(getUpCookie());
    }

    protected void initX5WebView(){
        com.tencent.smtt.sdk.WebSettings webSettings= mX5WebView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setBlockNetworkImage(false);
        webSettings.setBlockNetworkLoads(false);
        webSettings.setLoadsImagesAutomatically(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setDisplayZoomControls(false);
        webSettings.setBuiltInZoomControls(true);// 2
        webSettings.setSupportZoom(true);
        webSettings.setUseWideViewPort(true);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setCacheMode(com.tencent.smtt.sdk.WebSettings.LOAD_NO_CACHE);// webview页面不使用缓存
        // anjx 0825 add支持https环境下访问http资源。
        if (Build.VERSION.SDK_INT >= 21) {
            webSettings.setMixedContentMode(android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }
        String agent = webSettings.getUserAgentString();
        if (!TextUtils.isEmpty(agent)) {
            webSettings.setUserAgentString(agent + " FSBrowser/" + JsApiHelper.getAppVersion());
        }

        mX5WebView.setHorizontalScrollBarEnabled(false);
        mX5WebView.setInitialScale(15);
        mX5WebView.setScrollBarStyle(View.SCROLLBARS_INSIDE_OVERLAY);

        mX5WebView.setWebChromeClient(new X5JsApiWebChromeClientEx());
        mX5WebView.setWebViewClient(new X5JsApiWebViewClientEx());

        if(mSimpleOnGestureListener!=null){
            final GestureDetector mGestureDetector=new GestureDetector(mX5WebView.getContext(),mSimpleOnGestureListener);
            mX5WebView.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    return mGestureDetector.onTouchEvent(event);
                }
            });
        }

        WebViewHelper.setX5Cookies4FS(getUpCookie());
    }

    private Map getUpCookie() {
        Activity activity = getActivity();
        Map<String, String> upCookies = WebViewHelper.getUpCookies(activity);
        if (upCookies != null) {
            if (mX5WebView != null) {
                LocalCookie.clearX5CookieManager(activity);
                WebViewHelper.setX5Cookies4FS();
            } else if (mWebView != null) {
                LocalCookie.clearCookieManager(activity);
                WebViewHelper.setCookies4FS();
            }
        }
        return upCookies;
    }

    private ServiceConnection mConn = new ServiceConnection() {

        @Override
        public void onServiceDisconnected(ComponentName name) {
            handleOnFileServiceDisconnected();
        }

        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            mFileServer = IFileServer.Stub.asInterface(service);
            try {
                mFileUploader = mFileServer.getFileUploader(FILEUPLOADER_ID,true);
                mFileUploader.addStateCallback(mJsFileUploadStateCallback);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
            mJsHandler.initFileUploader(mFileUploader);
        }
    };

    public void bindService() {

        Intent service = new Intent(FileServerContants.SERVER_ATION);
        service.setComponent(FileServerContants.g_FileServiceComponentName);
        if (mActivity != null && !mActivity.isFinishing()) {
            try {
                mActivity.bindService(service, mConn, Context.BIND_AUTO_CREATE);
            }catch (Exception e){
                FCLog.e(TAG,Log.getStackTraceString(e));
            }
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if(mActivity!=null)
        {
            try {
                mActivity.unbindService(mConn);
            }
            catch (Exception e){
                e.printStackTrace();
                FCLog.d(TAG,e.getMessage());
            }
        }
        handleOnFileServiceDisconnected();
        webViewDestroy();
        if(mJsHandler!=null){
            mJsHandler.reset();
        }
    }

    public void webViewDestroy(){
        if (mWebView != null) {
            //清除WebView内存缓存，不要清除硬盘缓存文件，否则会出现问题，这个问题是由于WebView的一个BUG
            mWebView.clearCache(false);
            mWebView.destroy();
            mWebView = null;
        }
        else if(mX5WebView!=null){
            //清除WebView内存缓存，不要清除硬盘缓存文件，否则会出现问题，这个问题是由于WebView的一个BUG
            mX5WebView.clearCache(false);
            mX5WebView.destroy();
            mX5WebView=null;
        }
    }
    private void handleOnFileServiceDisconnected(){
        mFileServer = null;
        if (mFileUploader != null) {
            try {
                mFileUploader.removeStateCallback(mJsFileUploadStateCallback);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
            ;
            //mFileUploader.removeProgressCallback(QuickSellActivity.this);
            // mFileServer.unRegisterFileUpLoder(mFileUploader.getLoaderId());
            mFileUploader = null;
        }
    }

    protected void ignoreClientCertRequest(com.tencent.smtt.export.external.interfaces.ClientCertRequest clientCertRequest) {
        try {
            clientCertRequest.ignore();
        } catch (Exception e) {
            FCLog.e(TAG, "ignoreClientCertRequest failed by " + Log.getStackTraceString(e));
        }
    }

    protected void ignoreClientCertRequest(ClientCertRequest clientCertRequest) {
        //在sso登录过程中eu-mobile.events.data.microsoft.com 这个网站本身的证书有异常（不可信），触发了onReceivedClientCertRequest回调，
        //而 ClientCertRequest的ignore()方法本身在执行过程中有异常，会导致崩溃，所以要捕获异常
        //2023-07-07 10:19:51.896 18302-18302/com.facishare.fs E/chromium: [ERROR:aw_contents_client_bridge.cc(257)] No client certificate selected
        //2023-07-07 10:19:51.897 18302-18302/com.facishare.fs I/JsApiFragment: onReceivedClientCertRequest is call by  https://login.microsoftonline
        // .com/aec0543a-cc73-4fa4-9e2a-cf52adf9b788/login
        //2023-07-07 10:19:51.898 18302-18302/com.facishare.fs W/System.err: java.lang.IllegalStateException: The callback was already called.
        //2023-07-07 10:19:51.898 18302-18302/com.facishare.fs W/System.err:     at l8.a(chromium-TrichromeWebViewGoogle.apk-stable-443021033:3)
        //2023-07-07 10:19:51.899 18302-18302/com.facishare.fs W/System.err:     at j8.run(chromium-TrichromeWebViewGoogle.apk-stable-443021033:1)
        //2023-07-07 10:19:51.899 18302-18302/com.facishare.fs W/System.err:     at org.chromium.base.task.PostTask.c(chromium-TrichromeWebViewGoogle.apk-stable-443021033:3)
        //2023-07-07 10:19:51.899 18302-18302/com.facishare.fs W/System.err:     at ps0.ignore(chromium-TrichromeWebViewGoogle.apk-stable-443021033:2)
        //2023-07-07 10:19:51.899 18302-18302/com.facishare.fs W/System.err:     at com.facishare.fs.js.views.JsApiFragment$JsApiWebViewClientEx.onReceivedClientCertRequest
        // (JsApiFragment.java:883)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            try {
                clientCertRequest.ignore();
            } catch (Exception e) {
                FCLog.e(TAG, "ignoreClientCertRequest failed by " + Log.getStackTraceString(e));
            }
        }
    }

    protected void showIgnoreCertRequestDialog(Context context, String url, Runnable successCallBack) {
        //在H5页面中，一般情况下不会验证客户端证书。客户端证书验证通常在服务器与客户端之间建立安全的HTTPS连接时进行。在这种情况下，服务器会要求客户端提供有效的客户端证书来验证其身份。
        // 然而，在特定的应用场景下，可能会在H5页面中实现客户端证书验证(H5页面本身并不直接进行客户端证书验证，而是通过WebView来加载和显示页面)
        FCLog.i(TAG, "showIgnoreCertRequestDialog is call by  " + url);
        if (successCallBack != null) {//和IOS先对齐，先不弹框提示异常的证书校验
            successCallBack.run();
        }
        //        String des = I18NHelper.getText("jsapi.webview.ignore_cert_tip","当前网页证书未知，是否信任并继续？");
        //        final CommonDialog dialog = CommonDialog.createTwoButtonDialog(context, des);
        //        dialog.initTwoButtonDialogListenerTShow(new CommonDialog.myDiaLogListener() {
        //            @Override
        //            public void onClick(View view) {
        //                dialog.dismiss();
        //                int i = view.getId();
        //                if (i == R.id.button_mydialog_enter) {
        //                    if (successCallBack != null) {
        //                        successCallBack.run();
        //                    }
        //                } else if (i == R.id.button_mydialog_cancel) {
        //                }
        //            }
        //        });
        //        dialog.show();
    }
}
