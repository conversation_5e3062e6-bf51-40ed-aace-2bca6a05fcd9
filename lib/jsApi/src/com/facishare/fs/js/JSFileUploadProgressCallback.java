package com.facishare.fs.js;

import android.app.Activity;
import android.os.RemoteException;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.remote_service.fileupload.FileUploadVo;
import com.facishare.fs.remote_service.fileupload.IFileUploader;
import com.facishare.fs.remote_service.fileupload.FileUploadProgressCallback;
import com.facishare.fs.remote_service.fileupload.FileUploadTaskInfo;
import com.fxiaoke.fxlog.FCLog;

import java.lang.ref.WeakReference;

/**
 * Created by fup on 2017/3/6.
 */
public class JSFileUploadProgressCallback extends FileUploadProgressCallback.Stub {
    private static final String TAG = JSFileUploadProgressCallback.class.getSimpleName();
    private WeakReference<BaseJavascriptBridge> mJsBridge;
    private WeakReference<Activity> mActivity;
    private IFileUploader mFileUploader;

    public JSFileUploadProgressCallback(Activity context, BaseJavascriptBridge jsBridge) {
        mJsBridge = new WeakReference<>(jsBridge);
        mActivity = new WeakReference<Activity>(context);
    }

    public void setFileUploader(IFileUploader fileUploader) {
        mFileUploader = fileUploader;
    }

    @Override
    public void onProgressChanged(int taskId, int cur, int total) {
        Activity activity = mActivity.get();
        if (activity == null || activity.isFinishing() || mFileUploader == null) {
            return;
        }
        FileUploadTaskInfo info = null;
        try {
            info = mFileUploader.getUploadTaskById(taskId);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        if(info == null) {
            return;
        }
        FileUploadVo vo = (FileUploadVo) info.vo;
        if(vo == null) {
            return;
        }
        JSONObject jo = new JSONObject();
        jo.put("id", vo.id);
        int progress = (cur*100)/total;
        jo.put("progress", progress);

        JSONObject progressJo = new JSONObject();
        progressJo.put("file", jo);
        FCLog.i(TAG, "onProgressChanged, name:" + vo.name + ",id:" + vo.id + ",progress:" + progress);
        if(mJsBridge.get()!=null){
            mJsBridge.get().callHandler("uploadFileHandler", JSON.toJSONString(progressJo));
        }
    }
}
