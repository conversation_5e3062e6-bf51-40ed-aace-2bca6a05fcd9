package com.facishare.fs.js.ava;

import com.facishare.fs.js.JSServerHandler;

import java.util.HashMap;
import java.util.Map;

public class AvaJSServerHandlerDiedCache {
    private static AvaJSServerHandlerDiedCache mSingleton = null;
    private Map<String, JSServerHandler> mHandlerMap = new HashMap<>();

    private AvaJSServerHandlerDiedCache() {
    }

    public static AvaJSServerHandlerDiedCache getInstance() {
        if (mSingleton == null) {
            synchronized (AvaJSServerHandlerDiedCache.class) {
                if (mSingleton == null) {
                    mSingleton = new AvaJSServerHandlerDiedCache();
                }
            }
        }
        return mSingleton;

    }


    public void putJSServerHandler(String dataCode, JSServerHandler jsServerHandler){
        mHandlerMap.put(dataCode,jsServerHandler);
    }

    public JSServerHandler getAndRemoveJSServerHandler(String dataCode){
        JSServerHandler handler = mHandlerMap.get(dataCode);

        if(handler != null){
            mHandlerMap.remove(dataCode);
        }
        return handler;
    }

    public void removeJSServerHandler(String dataCode){
        if(dataCode != null && mHandlerMap.containsKey(dataCode)){
            mHandlerMap.remove(dataCode);
        }
    }

    public JSServerHandler getJSServerHandler(String dataCode){
        return mHandlerMap.get(dataCode);
    }


}
