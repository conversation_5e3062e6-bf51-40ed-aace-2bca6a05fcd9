package com.facishare.fs.js.utils;

import android.os.Build;

import com.alibaba.fastjson.JSON;
import com.facishare.fs.pluginapi.cloudctrl.ICloudCtrl;

import java.util.List;

/**
 * webview云控工具类
 * <AUTHOR>
 * @date 2020/11/20
 */
public class WebViewCloudCtrlHelper {
    private static final String NOT_USE_X5_CORE_DEVICES = "not_use_x5_core_devices";
    private static final String NOT_USE_X5_CORE_MODELS = "not_use_x5_core_models";
    private static final String IS_FORCE_USE_X5_CORE = "is_force_use_x5_core";
    private static final String IS_USE_X5_CORE = "is_use_x5_core";
    private static final String USE_X5_BUSINESS_LIST = "use_x5_core_business_list";
    private static final String NOT_USE_X5_BUSINESS_LIST = "not_use_x5_core_business_list";
    private static final String IS_APP_CENTER_H5_USE_X5_CORE = "is_app_center_h5_use_x5_core";

    private static final String IS_ALL_JSAPI_FRAGMENT_USE_X5_CORE = "is_all_jsapi_fragment_use_x5_core";
    private static final String JSAPI_FRAGMENT_USE_X5_CORE_BUSINESS_LIST = "jsapi_fragment_use_x5_core_business_list";

    public static final String SCREEN_ORIENTATION_FULL_USER_BUSINESS_LIST = "check_web_activity_screen_orientation_full_user_business_list";

    /**
     * 判断当前系统的X5功能是否被禁用
     *
     * @param cloudCtrl
     * @return
     */
    public static boolean isX5DisabledOnCurrentSystem(ICloudCtrl cloudCtrl) {
        if (cloudCtrl.contains(WebViewCloudCtrlHelper.NOT_USE_X5_CORE_DEVICES)) {
            String not_use_x5_core_devices = cloudCtrl.getStringConfig(WebViewCloudCtrlHelper.NOT_USE_X5_CORE_DEVICES, "");
            List<Integer> deviceList = null;
            try {
                deviceList = JSON.parseArray(not_use_x5_core_devices, Integer.class);
            } catch (Exception e) {
                e.printStackTrace();
            }
            int api = Build.VERSION.SDK_INT;
            if (deviceList != null && deviceList.size() > 0) {
                if (deviceList.contains(api)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断当前型号的手机的X5是否被禁用
     *
     * @param cloudCtrl
     * @return
     */
    public static boolean isX5DisabledOnCurrentPhone(ICloudCtrl cloudCtrl) {
        if (cloudCtrl.contains(WebViewCloudCtrlHelper.NOT_USE_X5_CORE_MODELS)) {
            String not_use_x5_core_models = cloudCtrl.getStringConfig(WebViewCloudCtrlHelper.NOT_USE_X5_CORE_MODELS, "");
            List<String> modelList = null;
            try {
                modelList = JSON.parseArray(not_use_x5_core_models, String.class);
            } catch (Exception e) {
                e.printStackTrace();
            }
            String model = Build.MODEL;
            if (modelList != null && modelList.size() > 0) {
                if (modelList.contains(model)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 判断当前url的X5是否被禁用
     *
     * @param cloudCtrl
     * @param url
     * @return
     */
    public static boolean isX5DisabledOnCurrentUrl(ICloudCtrl cloudCtrl, String url) {
        if (cloudCtrl.contains(WebViewCloudCtrlHelper.NOT_USE_X5_BUSINESS_LIST)) {
            String not_use_x5_core_business_list = cloudCtrl.getStringConfig(WebViewCloudCtrlHelper.NOT_USE_X5_BUSINESS_LIST, "[]");
            List<String> notUseX5CoreBusinessList = null;
            try {
                notUseX5CoreBusinessList = JSON.parseArray(not_use_x5_core_business_list, String.class);
                if (notUseX5CoreBusinessList != null && notUseX5CoreBusinessList.size() > 0) {
                    for (String businessUrl : notUseX5CoreBusinessList) {
                        if (url.toLowerCase().startsWith(businessUrl.toLowerCase())) {
                            return true;
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return false;
    }

    /**
     * 判断X5是否被强制启用
     *
     * @param cloudCtrl
     * @return
     */
    public static boolean isX5ForceEnabled(ICloudCtrl cloudCtrl) {
        boolean forceEnableX5 = false;
        if (cloudCtrl.contains(WebViewCloudCtrlHelper.IS_FORCE_USE_X5_CORE)) {
            forceEnableX5 = cloudCtrl.getBooleanConfig(WebViewCloudCtrlHelper.IS_FORCE_USE_X5_CORE, false);
        }
        return forceEnableX5;
    }

    public static boolean isX5EnabledOnAllJsApiFragment(ICloudCtrl cloudCtrl) {
        return cloudCtrl.getBooleanConfig(IS_ALL_JSAPI_FRAGMENT_USE_X5_CORE, false);
    }

    /**
     * 判断当前url是否需要使用X5
     *
     * @param cloudCtrl
     * @param url
     * @return
     */
    public static boolean isX5EnabledOnCurrentUrl(ICloudCtrl cloudCtrl, String url) {
        if (cloudCtrl.contains(WebViewCloudCtrlHelper.USE_X5_BUSINESS_LIST)) {
            String use_x5_core_business_list = cloudCtrl.getStringConfig(WebViewCloudCtrlHelper.USE_X5_BUSINESS_LIST, "[]");
            List<String> useX5CoreBusinessList = null;
            try {
                useX5CoreBusinessList = JSON.parseArray(use_x5_core_business_list, String.class);
                if (useX5CoreBusinessList != null && useX5CoreBusinessList.size() > 0) {
                    for (String businessUrl : useX5CoreBusinessList) {
                        if (url.toLowerCase().startsWith(businessUrl.toLowerCase())) {
                            return true;
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return false;
    }

    /**
     * 判断指定类型的jsapi fragment是否启用X5
     * @param cloudCtrl
     * @param businessType
     * @return
     */
    public static boolean isX5EnabledOnBusinessType(ICloudCtrl cloudCtrl, String businessType) {
        String json = cloudCtrl.getStringConfig(JSAPI_FRAGMENT_USE_X5_CORE_BUSINESS_LIST, "[]");
        List<String> useX5CoreBusinessList = null;
        try {
            useX5CoreBusinessList = JSON.parseArray(json, String.class);
            if (useX5CoreBusinessList != null && useX5CoreBusinessList.size() > 0) {
                for (String item : useX5CoreBusinessList) {
                    if (businessType.equalsIgnoreCase(item)) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }
}
