package com.facishare.fs.js.utils;

import android.media.MediaPlayer;

/**
 * Created by wubb on 2016/7/4.
 * 音频播放工具类
 */
public class MediaPlayerHelper {
    public static interface MediaPlayerListener
    {
        void onStop(int curPosition,int duration);
        void onError();
    }
    static MediaPlayer mediaPlayer;
    static MediaPlayerListener mMediaPlayerListener;
    public static void playAudio(String filePath, final MediaPlayerListener listener)
    {
        mMediaPlayerListener=listener;
        try
        {
            mediaPlayer=new MediaPlayer();
            mediaPlayer.setDataSource(filePath);
            mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                @Override
                public void onCompletion(MediaPlayer mp) {
                    if(mMediaPlayerListener!=null)
                        mMediaPlayerListener.onStop(mediaPlayer.getDuration(),mediaPlayer.getDuration());
                }
            });
            mediaPlayer.prepare();
            mediaPlayer.start();
        }
        catch(Exception e)
        {
            e.printStackTrace();
        }
    }

    public static void stopAudio(){
        if(mediaPlayer==null) return;
        try{
            mediaPlayer.stop();
            if(mMediaPlayerListener!=null)
                mMediaPlayerListener.onStop(mediaPlayer.getCurrentPosition(),mediaPlayer.getDuration());
        }
        catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void pauseAudio(){
        if(mediaPlayer==null) return;
        try{
            mediaPlayer.pause();
        }
        catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void resumeAudio(){
        if(mediaPlayer==null) return;
        try{
            mediaPlayer.start();
        }
        catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void release(){
        if(mediaPlayer==null) return;
        try
        {
            mediaPlayer.release();
            mediaPlayer=null;
        }
        catch (Exception e){
            e.printStackTrace();
        }
    }
}
