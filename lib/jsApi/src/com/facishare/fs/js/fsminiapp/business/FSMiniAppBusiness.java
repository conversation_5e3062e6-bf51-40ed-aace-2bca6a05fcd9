package com.facishare.fs.js.fsminiapp.business;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.common_utils.CloseUtils;
import com.facishare.fs.common_utils.UnzipUtils;
import com.facishare.fs.context.FSContextManager;
import com.facishare.fs.js.fsminiapp.beans.FSMiniAppBean;
import com.facishare.fs.js.fsminiapp.interfaces.FSMiniAppInitListener;
import com.fxiaoke.fscommon.files.DirectoryHelper;
import com.fxiaoke.fscommon.files.FileHelper;
import com.fxiaoke.fshttp.web.http.WebApiDownloadFileCallback;
import com.fxiaoke.fshttp.web.http.WebApiParameterList;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.fxiaoke.fxlog.FCLog;
import com.lidroid.xutils.util.MD5;

import java.io.File;
import java.io.FileInputStream;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * Created by wubb on 2017/6/26.
 * 纷享小程序核心业务逻辑代码
 */

public class FSMiniAppBusiness {
    private static final String TAG = "FSMiniAppBusiness";
    //https://www.fxiaoke.com/__gamma__/$appname?$queryString#$hash 纷享小程序示例url
    public static final String FS_MINI_APP_ROOT_PATH = FSContextManager.getCurUserContext().getSDOperator().getBaseDir() + "/fsminiapp";
    private static final String FS_MINI_APP_MAIN_URL = WebApiUtils.getWebViewRequestUrl()+"/open/__gamma__/map.json";
    public static final String FS_MINI_APP_PREFIX = "/__gamma__/";
    private static final String FS_MINI_APP_MAP_FILE_PATH = FS_MINI_APP_ROOT_PATH + "/map.json";

    //纷享小程序map文件缓存数据
    private static JSONObject mMapData = null;

    static ExecutorService executorService = Executors.newCachedThreadPool();

    /**
     * 初始化小程序配置
     */
    public static void initConfig() {
        File file =new File(FS_MINI_APP_ROOT_PATH);
        if(!file.exists())
            file.mkdirs();

        String mapData = FileHelper.readStringFromFile(FS_MINI_APP_MAP_FILE_PATH, "utf-8");
        if (!TextUtils.isEmpty(mapData)) {
            try {
                mMapData = JSONObject.parseObject(mapData);
                FCLog.i(TAG,"initConfig mapData = "+mapData);
            } catch (Exception e){
                FCLog.e(TAG,e.getMessage());
                FileHelper.deleteFile(FS_MINI_APP_MAP_FILE_PATH);
            }
        }else {
            FCLog.i(TAG,"initConfig mapData = null");
        }
    }

    /**
     * 更新小程序配置
     *
     * @param context
     */
    public static void updateConfig(Context context) {
        downloadConfigData(((errCode, errMsg) -> executorService.submit(() -> unZipPublicAssetFile(context))));
    }

    private static FSMiniAppInitListener mInitListener;

    public static void registerInitListener(FSMiniAppInitListener listener) {
        mInitListener = listener;
    }

    public static void unRegisterInitListener() {
        mInitListener = null;
    }

    /**
     * 初始化纷享小程序
     *
     * @param url 纷享小程序url
     */
    public static void initAsync(final Context context, final String url) {
        executorService.submit(() -> {
            try {
                init(context, url);
            } catch (Exception e) {
                FCLog.i(TAG,"init mini app failed with exception,exception="+e.getMessage());
                e.printStackTrace();
            }
        });
    }

    /**
     * 清除缓存
     */
    public static void clearCache(Context context) {
        DirectoryHelper.deleteDir(new File(FS_MINI_APP_ROOT_PATH),true);
        updateConfig(context);
    }

    /**
     * 解压纷享小程序公共资源包
     *
     * @param context
     * @return
     */
    private static void unZipPublicAssetFile(final Context context) {
        if (FileHelper.fileExist(FS_MINI_APP_ROOT_PATH)) return;
        try {
            createRootDir();
            UnzipUtils.unAssetZip(context, "fsminiapp/fsminiapp.zip", FS_MINI_APP_ROOT_PATH, true);
        } catch (Exception e) {
            FCLog.d(TAG, e.getMessage());
            FCLog.d(TAG, "unZipPublicAssetFile failed, exception = "+e.getMessage());
        }
    }

    /**
     * 判断url是否纷享小程序url
     *
     * @param url 正常的web url
     * @return
     */
    public static boolean isFSMiniAPPUrl(String url) {
        return !TextUtils.isEmpty(url) && url.indexOf(FS_MINI_APP_PREFIX) >= 0;
    }

    /**
     * 创建纷享小程序根目录
     */
    private static void createRootDir() {
        DirectoryHelper.mkDirs(FS_MINI_APP_ROOT_PATH);
    }

    /**
     * 获取纷享小程序的名字
     * 取url里面/__gamma__/后面第一个“/”中间的字符串作为小程序的名字
     * 比如：https://www.fxiaoke.com/open/__gamma__/example/abc/def?name=test&age=20
     * 则小程序的名字为example
     *
     * @param url 纷享小程序url
     * @return
     */
    public static String getName(String url) {
        int pos=url.indexOf(FS_MINI_APP_PREFIX);
        int pos2= url.indexOf("/",pos+FS_MINI_APP_PREFIX.length());
        if(pos2<0){
            pos2=url.indexOf("?");
            if(pos2<0){
                pos2=url.length();
            }
        }
        String fsMiniAppName = url.substring(pos+FS_MINI_APP_PREFIX.length(),pos2);
        return fsMiniAppName;
    }

    public static String getFsMiniAppRootPath(String url) {
        return FS_MINI_APP_ROOT_PATH+"/" + getName(url);
    }

    /**
     * 获取url问号之后的内容，包括查询参数和锚点
     *
     * @param url 纷享小程序url
     * @return
     */
    private static String getQueryParam(String url) {
        String query = null;
        int pos = url.indexOf("?");
        if (pos > 0) {
            query = url.substring(pos + 1);
        }
        return query;
    }

    /**
     * 保存小程序全局配置数据
     *
     * @param json
     */
    private static void saveMapFileData(String json) {
        FileHelper.saveStringToFile(json, "utf-8", FS_MINI_APP_MAP_FILE_PATH);
    }

    private static String getFSMiniAppJson(String fsMiniAppName){
        FCLog.i(TAG,"getFSMiniAppJson fsMiniAppName = "+fsMiniAppName);
        Future<String> future = executorService.submit(()->{
            String fsMiniAppJson = null;
            if (mMapData != null) {
                fsMiniAppJson = mMapData.getString(fsMiniAppName);
            } else {
                initConfig();
                if (mMapData!=null&&mMapData.size()>0){
                    fsMiniAppJson = mMapData.getString(fsMiniAppName);
                }
                if (TextUtils.isEmpty(fsMiniAppJson)){
                    CountDownLatch latch = new CountDownLatch(1);
                    executorService.submit(()->downloadConfigData(((errCode, errMsg) -> latch.countDown())));
                    try {
                        latch.await();
                    } catch (Exception e){
                        FCLog.e(TAG,e.getMessage());
                    }
                    if(mMapData==null){
                        FCLog.i(TAG,"getFSMiniAppJson mMapData=null");
                        onInitFailed();
                        return null;
                    }
                    fsMiniAppJson = mMapData.getString(fsMiniAppName);
                }

            }
            return fsMiniAppJson;
        });
        String result = null;
        try {
            result= future.get();
        } catch (Exception e){
            FCLog.e(TAG,e.getMessage());
            FCLog.i(TAG,"getFSMiniAppJson exception="+e.getMessage());
            onInitFailed();
        }
        return result;
    }

    /**
     * 初始化纷享小程序
     *
     * @param url 纷享小程序url
     */
    private static void init(Context context, String url) {
        FCLog.i(TAG,"begin init mini app");
        String fsMiniAppName = getName(url);
        String fsMiniAppJson = getFSMiniAppJson(fsMiniAppName);
        if(TextUtils.isEmpty(fsMiniAppJson)) {
            onInitFailed();
            FCLog.i(TAG,"init mini app failed,fsMiniAppJson="+fsMiniAppJson);
            return;
        }

        FSMiniAppBean bean = mMapData.getObject(fsMiniAppName, FSMiniAppBean.class);
        if(TextUtils.isEmpty(bean.name)) {
            bean.name=fsMiniAppName;
        }

        String zipFilePath = FS_MINI_APP_ROOT_PATH + "/" + bean.name + ".zip";

        String zipFileMd5 = getFileMd5(zipFilePath);
        if (!FileHelper.fileExist(zipFilePath) || !TextUtils.equals(zipFileMd5,bean.md5)) {
            FCLog.i(TAG,"zip file not exist or mini app config file md5 != local file zip file md5");
            FCLog.i(TAG,"mini app config file md5="+bean.md5);
            FCLog.i(TAG,"zip file md5="+zipFileMd5);
            downloadZip(bean.url, zipFilePath,(success)->{
                if(!success) {
                    FCLog.i(TAG,"init mini app failed,download zip file failed,success="+success);
                    onInitFailed();
                    return;
                }
                try {
                    unZip(context, zipFilePath, bean.name);
                    open(bean, url, fsMiniAppJson);
                    FCLog.i(TAG,"end init mini app,success");
                } catch (Exception e){
                    FCLog.i(TAG,"init mini app failed with exception="+e.getMessage());
                    FCLog.d(TAG, e.getMessage());
                    onInitFailed();
                }
            });
        } else {
            try {
                FCLog.i(TAG,"zip file exist");
                unZip(context, zipFilePath, bean.name);
                open(bean, url, fsMiniAppJson);
                FCLog.i(TAG,"end init mini app,success");
            } catch (Exception e){
                FCLog.i(TAG,"init mini app failed with exception="+e.getMessage());
                FCLog.d(TAG, e.getMessage());
                onInitFailed();
            }
        }
    }

    /**
     * 获取文件MD5
     *
     * @param filePath
     * @return
     * @throws Exception
     */
    private static String getFileMd5(String filePath) {
        FileInputStream fis = null;
        String md5 = "";
        try {
            fis = new FileInputStream(filePath);
            md5 = MD5.getFileMD5(fis);
        } catch (Exception e) {
            FCLog.d("getFileMd5 fail", e.getMessage());
        } finally {
            CloseUtils.close(fis);
        }
        return md5;
    }

    private static void onInitFailed() {
        if (mInitListener != null)
            mInitListener.onFailed();
    }

    /**
     * 下载纷享小程序资源包
     *
     * @param url           纷享小程序url
     * @param zipFilePath 纷享小程序保存路径
     * @return
     */
    private static void downloadZip(String url, String zipFilePath,DownloadListener downloadListener) {
        FCLog.i(TAG,"begin download zip file,url="+url);
        WebApiUtils.downloadAsync(url, WebApiParameterList.create(), new WebApiDownloadFileCallback() {
            @Override
            public void completed(byte[] content, String type) {
                FCLog.i(TAG,"end download zip file");
                if(content==null || content.length==0) {
                    FCLog.i(TAG,"end download zip file,download failure");
                    if(downloadListener!=null) {
                        downloadListener.completed(false);
                    }
                    return;
                }
                FCLog.i(TAG,"end download zip file,download success");
                boolean result = FileHelper.saveToFile(content, zipFilePath);
                FCLog.i(TAG,"save download zip file result="+result);
                if(downloadListener!=null) {
                    downloadListener.completed(result);
                }
            }
        });
    }

    /**
     * 解压纷享小程序压缩包，如果文件已经存在，则无需再次解压
     *
     * @param zipFilePath
     * @return
     */
    private static void unZip(Context context, String zipFilePath, String fsMiniAppName) throws Exception {
        FCLog.i(TAG,"begin unzip file,zipFilePath="+zipFilePath+",fsMiniAppName="+fsMiniAppName);
        if (TextUtils.isEmpty(zipFilePath) || TextUtils.isEmpty(fsMiniAppName)) return;
        String filePath = FS_MINI_APP_ROOT_PATH + "/" + fsMiniAppName;
        UnzipUtils.unZipSdcardResSync(context, zipFilePath, filePath, true);
        FCLog.i(TAG,"end unzip file,success");
    }

    /**
     * 打开纷享小程序
     *
     * @param appBean 纷享小程序的名字
     * @param url   原始url查询参数和锚点
     */
    private static void open(FSMiniAppBean appBean, String url, String fsMiniAppJson) {
        //String fsMiniAppQuery = getQueryParam(url);
        FCLog.i(TAG,"begin open mini app");
        FCLog.i(TAG,"url="+url);
        FCLog.i(TAG,"fsMiniAppJson="+fsMiniAppJson);
        FCLog.i(TAG,"appBean="+JSONObject.toJSONString(appBean));
        if (TextUtils.isEmpty(appBean.name)){
            FCLog.i(TAG,"open appBean.name=null");
            onInitFailed();
            return;
        }
        if (TextUtils.isEmpty(fsMiniAppJson)) {
            FCLog.i(TAG,"open fsMiniAppJson=null");
            onInitFailed();
            return;
        }
        String indexFilePath = FS_MINI_APP_ROOT_PATH + "/" + appBean.name + "/index.html";
        FCLog.i(TAG,"indexFilePath="+indexFilePath);
        if (!FileHelper.fileExist(indexFilePath)) {
            FCLog.i(TAG,indexFilePath+" not exist");
            onInitFailed();
            return;
        }
        if (mInitListener != null) {
//            String filePath = "file://" + indexFilePath;
//            if (!TextUtils.isEmpty(fsMiniAppQuery)) {
//                filePath += "?" + fsMiniAppQuery;
//            }
            String js = "javascript:window.__appconfig=" + fsMiniAppJson;
            String html = FileHelper.readStringFromFile(indexFilePath, "utf-8");
            mInitListener.onSuccess(url ,html, js, appBean);
            FCLog.i(TAG,"open mini app success");
        } else {
            FCLog.i(TAG,"open mini app failed, mInitListener=null");
        }
    }

    private static void downloadConfigData(UpdateListener updateListener) {
        WebApiUtils.downloadAsync(FS_MINI_APP_MAIN_URL, WebApiParameterList.create(), new WebApiDownloadFileCallback() {
            @Override
            public void completed(byte[] content, String type) {
                if(content==null || content.length==0) {
                    FCLog.i(TAG,"downloadConfigData failed, config response is empty");
                    if(updateListener!=null) {
                        updateListener.onUpdate(1,"config response is empty");
                    }
                    return;
                }
                String json = new String(content);
                try {
                    mMapData = JSON.parseObject(json);
                    saveMapFileData(mMapData.toJSONString());
                    if(updateListener!=null) {
                        updateListener.onUpdate(0,"");
                    }
                } catch (Exception e){
                    FCLog.e(TAG,e.getMessage());
                    FCLog.i(TAG,"downloadConfigData failed, exception = "+e.getMessage());
                    if(updateListener!=null) {
                        updateListener.onUpdate(1,e.getMessage());
                    }
                }
            }
        });
    }

    /**
     * 在线检查小程序的配置文件，如果有更新，则更新小程序的缓存数据
     * @param activity
     * @param url 小程序的url
     * @param updateListener errCode=0，代表成功，其它代表失败，可以通过errMsg查看错误信息
     */
    public static void checkAndUpdate(Activity activity, String url, UpdateListener updateListener) {
        if(!isFSMiniAPPUrl(url)) {
            FCLog.i(TAG,"checkAndUpdate failed, url error, not mini app url");
            if(updateListener!=null) {
                updateListener.onUpdate(1,"url error, not mini app url");
                return;
            }
        }
        downloadConfigData((errCode, errMsg) -> {
            if(errCode==1) {
                FCLog.i(TAG,"checkAndUpdate,downloadConfigData failed, mini app config file failed");
                updateListener.onUpdate(1,"mini app config file failed");
                return;
            }
            String fsMiniAppName=getName(url);
            FSMiniAppBean bean = mMapData.getObject(fsMiniAppName, FSMiniAppBean.class);
            FCLog.i(TAG,"checkAndUpdate,bean="+JSONObject.toJSONString(bean));

            String zipFilePath = FS_MINI_APP_ROOT_PATH + "/" + bean.name + ".zip";
            FCLog.i(TAG,"checkAndUpdate,zipFilePath="+zipFilePath);

            boolean fileExist = FileHelper.fileExist(zipFilePath);
            FCLog.i(TAG,"checkAndUpdate,fileExist="+fileExist);

            String fileMd5 = getFileMd5(zipFilePath);
            FCLog.i(TAG,"md5File="+fileMd5+",bean.md5="+bean.md5);

            if (!fileExist || !fileMd5.equalsIgnoreCase(bean.md5)) {
                downloadZip(bean.url, zipFilePath,(success)->{
                    if(!success) {
                        FCLog.i(TAG,"checkAndUpdate,downloadZip failed, file download failed");
                        updateListener.onUpdate(1,"file download failed");
                        return;
                    }
                    try {
                        unZip(activity, zipFilePath, bean.name);
                        if(updateListener!=null) {
                            updateListener.onUpdate(0,"");
                        }
                    } catch (Exception e) {
                        FCLog.e(TAG, e.getMessage());
                        FCLog.i(TAG,"checkAndUpdate,unzip failed, exception="+e.getMessage());
                        if(updateListener!=null)
                            updateListener.onUpdate(1,e.getMessage());
                    }
                });
            } else {
                try {
                    unZip(activity, zipFilePath, bean.name);
                    if(updateListener!=null) {
                        updateListener.onUpdate(0,"");
                    }
                } catch (Exception e) {
                    FCLog.e(TAG, e.getMessage());
                    FCLog.i(TAG,"checkAndUpdate,unzip failed, exception="+e.getMessage());
                    if(updateListener!=null)
                        updateListener.onUpdate(1,e.getMessage());
                }
            }
        });
    }

    public interface UpdateListener{
        void onUpdate(int errCode, String errMsg);
    }
    public interface DownloadListener{
        void completed(boolean success);
    }
}
