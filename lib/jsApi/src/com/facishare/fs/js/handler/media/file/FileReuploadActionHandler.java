package com.facishare.fs.js.handler.media.file;

import android.app.Activity;
import android.os.AsyncTask;
import android.os.RemoteException;
import android.text.TextUtils;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.fs.NoProguard;
import com.facishare.fs.js.BaseActionHandler;
import com.facishare.fs.js.JSApiOpenError;
import com.facishare.fs.js.JSApiUtil;
import com.facishare.fs.js.JSApiWebUtils;
import com.facishare.fs.js.WebViewJavascriptBridge;
import com.facishare.fs.js.beans.TempFile2ForeverResult;
import com.facishare.fs.remote_service.fileupload.FileUploadVo;
import com.facishare.fs.pluginapi.AccountManager;
import com.facishare.fs.remote_service.fileupload.IFileUploader;
import com.facishare.fs.remote_service.fileupload.FileUploadTaskInfo;
import com.facishare.fs.remote_service.fileupload.IFileUploaderBusinessCallback;
import com.fs.fsprobuf.ServerProtobuf;
import com.fxiaoke.fscommon.files.FileUtil;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fxlog.FCLog;
import com.lidroid.xutils.util.FsIOUtils;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

/**
 * Created by wubb on 2017/1/5.
 * 同时支持image、file reupload
 */

public class FileReuploadActionHandler extends BaseActionHandler {
    public static class ImageReUploadModel{
        @NoProguard
        public String imageLocalPath;
        @NoProguard
        public String id;
        @NoProguard
        public int type;//0代表N_PATH，1代表A_PATH
    }
    public static class FileReUploadModel{
        @NoProguard
        public String fileLocalPath;
        @NoProguard
        public String id;
        @NoProguard
        public int type;//0代表N_PATH，1代表A_PATH
    }
    IFileUploader mFileUploader;
    WebViewJavascriptBridge.WVJBResponseCallback mJsCallback=null;
    ImageReUploadModel mImageReUploadModel;
    FileReUploadModel mFileReUploadModel;
    ServerProtobuf.EnterpriseEnv mEnterpriseEnv= ServerProtobuf.EnterpriseEnv.INNER;

    TYPE mTYpe;
    boolean mIsPublic=false;
    private static final String TAG = FileReuploadActionHandler.class.getSimpleName();
    public enum TYPE {
        FILE, IMAGE, UNKNOWN
    }

    public FileReuploadActionHandler(IFileUploader fileUploader) {
        mFileUploader = fileUploader;
    }
    public static class JSONKeyNameUtils {
        public static final String RESULT = "result";
        public static final String ID = "id";
        public static final String NAME = "name";
        public static final String SIZE = "size";
        public static final String NPATH = "npath";
        public static final String APATH="apath";
        static HashMap<TYPE, HashMap <String, String>> map = new HashMap<>();
        static {
            map.put(TYPE.FILE, generateFileReUploadJSONName());
            map.put(TYPE.IMAGE, generateImageReUploadJSONName());
        }
        static HashMap <String, String> generateFileReUploadJSONName() {
            HashMap<String, String> res = new HashMap<>();
            res.put(RESULT, "result");
            res.put(ID, "id");
            res.put(NAME, "fileName");
            res.put(SIZE, "fileSize");
            res.put(NPATH, "fileNPath");
            res.put(APATH, "fileAPath");
            return res;
        }
        static HashMap <String, String> generateImageReUploadJSONName() {
            HashMap<String, String> res = new HashMap<>();
            res.put(RESULT, "result");
            res.put(ID, "id");
            res.put(NAME, "imageName");
            res.put(SIZE, "imageSize");
            res.put(NPATH, "imageNPath");
            res.put(APATH, "imageAPath");
            return res;
        }
        public static String getJSONKeyName(TYPE type, String key) {
            HashMap <String, String> innerMap = map.get(type);
            if(innerMap == null) {
                return null;
            }
            return innerMap.get(key);
        }
    }

    @Override
    protected boolean needAutoConsumeCallbackAfterHandle() {
        return false;
    }

    @Override
    protected boolean needOverrideOnActivityResultMethod() {
        return false;
    }

    @Override
    public void handle(Activity activity, String action, JSONObject data, int requestCode, WebViewJavascriptBridge.WVJBResponseCallback jsCallback) {
        try{
            mImageReUploadModel= JSONObject.toJavaObject(data,ImageReUploadModel.class);
        }
        catch (Exception e){
            e.printStackTrace();
        }
        try {
            mFileReUploadModel = JSONObject.toJavaObject(data,FileReUploadModel.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if((mImageReUploadModel==null || TextUtils.isEmpty(mImageReUploadModel.imageLocalPath)) &&
                (mFileReUploadModel == null || TextUtils.isEmpty(mFileReUploadModel.fileLocalPath))){
            sendCallbackOfInvalidParameter();
            return;
        }
        String filePath;
        if(mImageReUploadModel!=null && !TextUtils.isEmpty(mImageReUploadModel.imageLocalPath)) {
            filePath = getLocalPath(mImageReUploadModel.imageLocalPath);
            mTYpe = TYPE.IMAGE;
            mIsPublic=mImageReUploadModel.type==1?true:false;
        } else if(mFileReUploadModel != null && !TextUtils.isEmpty(mFileReUploadModel.fileLocalPath)) {
            filePath = getLocalPath(mFileReUploadModel.fileLocalPath);
            mTYpe = TYPE.FILE;
            mIsPublic=mFileReUploadModel.type==1?true:false;
        } else {
            sendCallbackOfInvalidParameter();
            return;
        }
        mJsCallback=jsCallback;

        FileUploadCallback bCallback = new FileUploadCallback(activity);
        try {
            mFileUploader.setBusinessCallback(bCallback);
        } catch (RemoteException e) {
            e.printStackTrace();
        }

        int id = ID_NOT_USED;
        if(mTYpe == TYPE.IMAGE) {
            id = getId(mImageReUploadModel.id);
            mEnterpriseEnv=mImageReUploadModel.type==1?ServerProtobuf.EnterpriseEnv.CROSS:ServerProtobuf.EnterpriseEnv.INNER;
        } else if(mTYpe == TYPE.FILE) {
            id = getId(mFileReUploadModel.id);
            mEnterpriseEnv=mFileReUploadModel.type==1?ServerProtobuf.EnterpriseEnv.CROSS:ServerProtobuf.EnterpriseEnv.INNER;
        }
        FileUploadTaskInfo task = null;
        try {
            task = mFileUploader.getUploadTaskById(id);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        if(task != null && task.vo != null) {
            FileUploadVo v = (FileUploadVo) task.vo;
//            v.object = jsCallback;// TODO: 2018/7/16
        }
        if(task == null) {
            sendCallback(JSApiOpenError.ErrorCode.INVALID_PARAMETERS,
                    "please use upload interface first!!!");
        } else {
            try {
                mFileUploader.retryTask( id);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }

    }
    public static final int ID_NOT_USED = -100;
    public static int getId(String voId) {
        if(TextUtils.isEmpty(voId) || !voId.contains("-")) {
            return ID_NOT_USED;
        }
        try {
            String resStr = voId.substring(voId.lastIndexOf('-')+1, voId.length());
            return Integer.parseInt(resStr);
        } catch (Exception e) {
            return ID_NOT_USED;
        }
    }
    private String getLocalPath(String localPath) {
        if(localPath == null) {
            return "";
        }
        if(localPath.startsWith(JSApiUtil.THUMB_PATH_URL)) {
            return localPath.replace(JSApiUtil.THUMB_PATH_URL,"");
        }
        return localPath;
    }

    private class FileUploadCallback extends IFileUploaderBusinessCallback.Stub {

        private WeakReference<Activity> mActivity;

        public FileUploadCallback(Activity activity) {
            mActivity = new WeakReference<>(activity);
        }

        @Override
        public void onTempFileUploader(FileUploadTaskInfo taskInfo, String tempFilePath, int fileLength) {
            boolean success = mActivity.get() != null && !mActivity.get().isFinishing();
            FileUploadVo vo = (FileUploadVo) taskInfo.vo;

            JSONObject image = new JSONObject();
            image.put(JSONKeyNameUtils.getJSONKeyName(mTYpe, JSONKeyNameUtils.RESULT), success);
            image.put(JSONKeyNameUtils.getJSONKeyName(mTYpe, JSONKeyNameUtils.ID), vo.id);
            image.put(JSONKeyNameUtils.getJSONKeyName(mTYpe, JSONKeyNameUtils.NAME), vo.name);
            long fileSize= FileUtil.getFileInfo(new File(taskInfo.path)).Size;
            image.put(JSONKeyNameUtils.getJSONKeyName(mTYpe, JSONKeyNameUtils.SIZE), fileSize);
            if(mIsPublic){
                image.put(JSONKeyNameUtils.getJSONKeyName(mTYpe, JSONKeyNameUtils.APATH), tempFilePath);
            }
            else
            {
                image.put(JSONKeyNameUtils.getJSONKeyName(mTYpe, JSONKeyNameUtils.NPATH), tempFilePath);
            }

            transformTNPath2NPath(image);

            try {
                mFileUploader.setTaskResult(taskInfo.id, success);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    private static final String FILE_SUFFIX = "file://";

    private static List<FileUploadVo> trans2FileUploadVo(String filePath) {
        List<FileUploadVo> fileInfoList = new ArrayList<>();
        FileUploadVo file = new FileUploadVo();
        file.id = UUID.randomUUID().toString();
        file.name = FsIOUtils.getFileName(filePath);
        file.path = filePath;
        if (file.path.startsWith(FILE_SUFFIX)){
            file.path = file.path.substring(FILE_SUFFIX.length());
        }
        fileInfoList.add(file);
        return fileInfoList;
    }

    private void transformTNPath2NPath(JSONObject jsonObject) {
        new AsyncTNPath2NPathTask().execute(jsonObject);
    }

    public class AsyncTNPath2NPathTask extends AsyncTask<JSONObject, Void, JSONObject> {

        @Override
        protected JSONObject doInBackground(JSONObject... params) {
            //网络请求
            JSONObject jobject = params[0];
            String nPathJsonKey = JSONKeyNameUtils.getJSONKeyName(mTYpe, JSONKeyNameUtils.NPATH);
            String nameJsonkey = JSONKeyNameUtils.getJSONKeyName(mTYpe, JSONKeyNameUtils.NAME);
            String tempPath = jobject.getString(nPathJsonKey);
            String name = jobject.getString(nameJsonkey);
            String userId = "E." + AccountManager.getAccount().getEnterpriseAccount() + "." + AccountManager.getAccount().getEmployeeId();
            String npath = TempFile2ForeverSync(tempPath, name, userId);
            jobject.remove(nPathJsonKey); //update npath
            jobject.put(nPathJsonKey, npath);

            jobject.put("errorCode", 0);
            jobject.put("errorMessage", "success");
            return jobject;
        }

        @Override
        protected void onPostExecute(JSONObject obj) {
            super.onPostExecute(obj);
            String json=obj.toJSONString();
            FCLog.i(TAG, "file/image reupload success, " + json);
            sendCallback(obj);
        }
    }

    public String TempFile2ForeverSync(String tempPath, String fileName, String fsUserId) {
        final StringWrap res = new StringWrap("get npath or apath error");
        JSApiWebUtils.TempFile2ForeverSync(tempPath, fileName, fsUserId, new WebApiExecutionCallback<TempFile2ForeverResult>() {
            @Override
            public void completed(Date time, TempFile2ForeverResult response) {
                if(response != null && !TextUtils.isEmpty(response.nPath)) {
                    res.str = response.nPath;
                } else {
                    res.str = "get npath or apth success but content is empty";
                }
            }

            @Override
            public Class<TempFile2ForeverResult> getTypeReferenceFHE() {
                return TempFile2ForeverResult.class;
            }

            @Override
            public TypeReference<WebApiResponse<TempFile2ForeverResult>> getTypeReference() {
                return new TypeReference<WebApiResponse<TempFile2ForeverResult>>() {

                };
            }

            @Override
            public void failed(WebApiFailureType failureType, int httpStatusCode, String error) {
                super.failed(failureType, httpStatusCode, error);
                res.str = failureType.description();
            }
        });
        return res.str;
    }

    public class StringWrap {
        public String str;
        public StringWrap(String str) {
            this.str = str;
        }
    }
}
