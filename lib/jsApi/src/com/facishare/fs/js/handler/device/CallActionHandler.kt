package com.facishare.fs.js.handler.util

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import com.alibaba.fastjson.JSONObject
import com.facishare.fs.js.BaseActionHandler
import com.facishare.fs.js.BaseJavascriptBridge

class CallActionHandler : BaseActionHandler() {
    override fun handle(activity: Activity, action: String, data: JSONObject, requestCode: Int, jsCallback: BaseJavascriptBridge.WVJBResponseCallback) {
        val tel:String?=data["tel"] as String?
        if(!TextUtils.isEmpty(tel)){
            activity.startActivity(Intent(Intent.ACTION_VIEW).apply {
                setData(Uri.parse("tel:"+tel))
            })
            sendCallbackOfSuccess()
        } else {
            sendCallbackOfInvalidParameter()
        }
    }
}