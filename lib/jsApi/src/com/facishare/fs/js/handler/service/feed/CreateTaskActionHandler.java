package com.facishare.fs.js.handler.service.feed;

import android.app.Activity;

import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.js.BaseActionHandler;
import com.facishare.fs.js.WebViewJavascriptBridge;
import com.facishare.fs.pluginapi.HostInterfaceManager;

/**
 * Created by wubb on 2016/6/28.
 * 创建任务型工作动态
 */
public class CreateTaskActionHandler extends BaseActionHandler {
    @Override
    protected boolean needOverrideOnActivityResultMethod() {
        return false;
    }

    @Override
    public void handle(Activity activity, String action, JSONObject data, int requestCode, WebViewJavascriptBridge.WVJBResponseCallback jsCallback) {
        showTaskActivity(activity);
        sendCallbackOfSuccess();
    }

    private void showTaskActivity(Activity activity){
        HostInterfaceManager.getHostInterface().gotoXSendTaskActivity(activity,null);
    }
}
