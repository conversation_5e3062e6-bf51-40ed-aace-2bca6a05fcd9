package com.facishare.fs.js.handler.service.mail;

import com.facishare.fs.i18n.I18NHelper;
import android.app.Activity;
import android.content.Intent;

import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.NoProguard;
import com.facishare.fs.js.BaseActionHandler;
import com.facishare.fs.js.WebViewJavascriptBridge;
import com.facishare.fs.js.webview.IWebView;
import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.fxiaoke.fxdblib.beans.MsgTypeKey;
import com.fxiaoke.fxdblib.beans.OpenMessageEnterpriseMailboxData;
import com.fxiaoke.fxdblib.beans.OpenMessageEpMailboxContentData;
import com.fxiaoke.fxdblib.beans.SessionMessage;
import com.fxiaoke.fxdblib.beans.SessionMessageTemp;
import com.fxiaoke.fxdblib.utils.SessionMsgDataUtil;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * CreatedBy   wanzp on 2016/6/13
 * FSOpen.mail.shareToChat JS接口对应的Native Handler
 * 邮件转发企信（分享到企信消息里去）
 */

public class MailShareToChatActionHandler extends BaseActionHandler {
    public static class MailShareToChatModel{
        @NoProguard
        public String url;//转发的链接地址
        @NoProguard
        public String postTime;//原企信发布时间，时间格式：yyyy-MM-dd hh:mm:ss
        @NoProguard
        public String summary;//原企信摘要
        @NoProguard
        public String title;//原企信抬头
        @NoProguard
        public String sender;//企业邮箱使用，指定发件人名称
    }

    private IWebView iWebView;
    @Override
    public void handle(Activity activity, String action, JSONObject data, int requestCode, WebViewJavascriptBridge.WVJBResponseCallback jsCallback) {
        if(activity instanceof IWebView){
            iWebView=(IWebView)activity;
        }
        if(iWebView==null){
            sendCallbackOfUnknown();
            return;
        }
        MailShareToChatModel model= null;
        try {
            model = JSONObject.toJavaObject(data,MailShareToChatModel.class);
        } catch (Exception e) {
            e.printStackTrace();
            sendCallbackOfInvalidParameter();
            return;
        }
        if(model == null || model.title == null){
            sendCallbackOfInvalidParameter();
            return;
        }
        checkShareModel(model);
        mailShareToChat(activity,model,requestCode);
    }

    private void checkShareModel(MailShareToChatModel model) {
        model.summary = (model.summary == null ? iWebView.getWebViewTitle() : model.summary) ;
        model.url = (model.url == null ? iWebView.getCurrentUrl() : model.url) ;
        model.sender = (model.sender == null ? I18NHelper.getText("jsapi.service.sharetoconvertion.fsmail")/* 纷享销客企业邮箱 */ : model.sender) ;
        SimpleDateFormat formater = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String postTime = formater.format(new Date(System.currentTimeMillis()));
        model.postTime = (model.postTime == null ? postTime: model.postTime) ;
    }

    private void mailShareToChat(Activity activity, MailShareToChatModel model, int requestCode) {

        String title = model.title;
        String defaultsummary = model.summary;
        String sender = model.sender;

        Date date;
        String srcTime = model.postTime;
        SimpleDateFormat sdf  =  new  SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        df.setLenient(false);//这个的功能是不把1996-13-3 转换为1997-1-3
        try
        {
            date  =  sdf.parse(srcTime);
        }
        catch(Exception  e)
        {
            sendCallbackOfInvalidParameter();
            return;
        }
        String time = sdf.format(date);
        String summary = model.summary;
        String url = model.url;

        OpenMessageEpMailboxContentData content = new OpenMessageEpMailboxContentData() ;
//        content.setMessageId(mSessionMessage.getMessageId()+"");
//            try {
//                url = AES.Encrypt(url,AES.KEY_STRING_MAILBOX);
//            } catch (Exception e) {
//                FCLog.e(TAG,"MAILBOX URL ---AES.Encrypt EXCEPTION");
//                e.printStackTrace();
//            }
        content.setButtonUrl(url);
        content.setCreateTime(time);
//        content.setOwnerId(myID+"");
        content.setPostTime(time);
        content.setSummary(summary);

        OpenMessageEnterpriseMailboxData openMessageEnterpriseMailboxData = new OpenMessageEnterpriseMailboxData();
        openMessageEnterpriseMailboxData.Title = title;
        openMessageEnterpriseMailboxData.Type = MsgTypeKey.MSG_OPEN_MESSAGE_ENTERPRISE_MAILBOX_KEY;
        openMessageEnterpriseMailboxData.DefaultSummary = defaultsummary;
//            openMessageEnterpriseMailboxData.MessageContent = JSON.toJSONString(content);
        openMessageEnterpriseMailboxData.MessageContent = content;

        SessionMessageTemp smt = new SessionMessageTemp();
        smt.setMessageType(MsgTypeKey.MSG_OPEN_MESSAGE_KEY);
        smt.setOpenMessageEnterpriseMailboxData(openMessageEnterpriseMailboxData);
        HostInterfaceManager.getHostInterface().gotoSelectSessionActivity2(activity, smt, true, requestCode);
    }

    @Override
    protected boolean needAutoConsumeCallbackAfterHandle() {
        return false;
    }

    @Override
    protected boolean needOverrideOnActivityResultMethod() {
        return true;
    }

    @Override
    protected void onActivityResult(Activity activity, int requestCode, int resultCode, Intent intent, String action,
                                    WebViewJavascriptBridge.WVJBResponseCallback jsCallback) {
        super.onActivityResult(activity, requestCode, resultCode, intent, action, jsCallback);
        switch (resultCode) {
            case Activity.RESULT_CANCELED:
                sendCallbackOfCanceledByUser();
                return;
            case Activity.RESULT_OK:
                sendCallbackOfSuccess();
                return;
        }
        sendCallbackOfSuccess();
    }
}
