package com.facishare.fs.js.handler.webview.page;

import android.app.Activity;

import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.js.BaseActionHandler;
import com.facishare.fs.js.JSApiOpenError;
import com.facishare.fs.js.WebViewJavascriptBridge;
import com.facishare.fs.js.webview.IWebView;

/**
 * Created by wubb on 2016/7/7.
 */
public class GenerateQRActionHandler extends BaseActionHandler {
    @Override
    protected boolean needOverrideOnActivityResultMethod() {
        return false;
    }

    @Override
    public void handle(Activity activity, String action, JSONObject data, int requestCode, WebViewJavascriptBridge.WVJBResponseCallback jsCallback) {
        IWebView webView=(IWebView)activity;
        if(webView==null){
            sendCallbackOfUnknown();
            return;
        }
        webView.createQRCode();
        sendCallbackOfSuccess();
    }
}
