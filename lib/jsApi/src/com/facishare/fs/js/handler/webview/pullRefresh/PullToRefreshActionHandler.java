package com.facishare.fs.js.handler.webview.pullRefresh;

import android.app.Activity;

import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.js.BaseActionHandler;
import com.facishare.fs.js.WebViewJavascriptBridge;
import com.facishare.fs.js.webview.IWebView;

/**
 * Created by wubb on 2016/6/3.
 */
public class PullToRefreshActionHandler extends BaseActionHandler {
    public static final String WEBVIEW_PULL_REFRESH_ENABLE="webview.pullRefresh.enable";
    public static final String WEBVIEW_PULL_REFRESH_DISALBE="webview.pullRefresh.disable";
    public static final String WEBVIEW_PULL_REFRESH_STOP="webview.pullRefresh.stop";

    @Override
    protected boolean needOverrideOnActivityResultMethod() {
        return false;
    }

    @Override
    public void handle(Activity activity, String action, JSONObject data, int requestCode, WebViewJavascriptBridge.WVJBResponseCallback jsCallback) {
        IWebView iWebView = null;
        if(activity instanceof IWebView){
            iWebView = (IWebView)activity;
        }
        if(iWebView==null){
            sendCallbackOfUnknown();
            return;
        }
        if(action.equalsIgnoreCase(WEBVIEW_PULL_REFRESH_ENABLE)){
            iWebView.enablePullToRefresh();
        }
        else if(action.equalsIgnoreCase(WEBVIEW_PULL_REFRESH_DISALBE)){
            iWebView.disablePullToRefresh();
        }
        else if(action.equalsIgnoreCase(WEBVIEW_PULL_REFRESH_STOP)){
            iWebView.stopPullToRefresh();
        }else {
            sendCallbackOfApiNotExist();
            return;
        }
        sendCallbackOfSuccess();
    }
}
