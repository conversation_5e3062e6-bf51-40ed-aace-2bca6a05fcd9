<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:orientation="horizontal">

    <ImageView
        android:id="@+id/tag"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_marginRight="6dp"
        android:layout_marginTop="2.5dp"
        android:src="@drawable/approve_flow_node_opinion_changed_person"
        android:visibility="gone"
            android:layout_marginEnd="6dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/tv_transferLog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#3b4047"
            android:textSize="12dp"/>

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/approval_flow_node_time_color"
            android:textSize="@dimen/approval_flow_node_time_Size"/>

    </LinearLayout>

</LinearLayout>