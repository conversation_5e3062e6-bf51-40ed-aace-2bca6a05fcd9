<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
              xmlns:tools="http://schemas.android.com/tools"
              android:id="@+id/root"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:orientation="vertical">

    <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="37dp"
    	    android:background="@color/white">

        <!--左侧的Bar-->
        <com.fxiaoke.plugin.crm.common_view.RelationObjLeftBar
                android:id="@+id/title_left_bar"
                android:layout_width="4dp"
                android:layout_height="16dp"
                android:layout_marginTop="15dp"
                android:background="#FFAC38"/>

        <!--标题-->
        <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@+id/title_left_bar"
                android:ellipsize="end"
                i18n:fstext="ad.res.layout_approve_component.2"
                android:textColor="#525866"
                android:textSize="15dp"
                android:textStyle="bold"
                android:layout_marginLeft="8dp"
                android:layout_marginTop="12dp"
                android:layout_marginStart="8dp"
                android:layout_toEndOf="@+id/title_left_bar" />

    </RelativeLayout>

    <!--中间容器-->
    <LinearLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@color/white"/>

    <!--底部总数布局-->
    <RelativeLayout
            android:id="@+id/total_count_layout"
            android:layout_width="match_parent"
            android:layout_height="38dp"
            android:background="@color/white">

        <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/des_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                i18n:fstext="ad.res.layout_approve_component.1"
                android:textColor="#aaadb3"
                android:textSize="@dimen/text_size_s"
                android:layout_marginLeft="12dp"
                android:layout_centerVertical="true"
                android:layout_marginStart="12dp" />

        <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/div_view"
                android:layout_width="1dp"
                android:layout_height="10dp"
                android:layout_marginLeft="4dp"
                android:layout_marginRight="4dp"
                android:background="#d8d8d8"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@+id/des_text"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:layout_toEndOf="@+id/des_text" />

        <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/count_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                tools:text="0"
                android:textColor="#aaadb3"
                android:textSize="12dp"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@+id/div_view"
                android:layout_toEndOf="@+id/div_view" />


        <ImageView
                android:id="@+id/img_right_arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:scaleType="fitCenter"
                android:src="@drawable/icon_nav_right_arrow"
                android:layout_centerVertical="true"
                android:layout_alignParentRight="true"
                android:layout_marginRight="16dp"
                android:layout_marginEnd="16dp"
                android:layout_alignParentEnd="true" />
    </RelativeLayout>
</LinearLayout>