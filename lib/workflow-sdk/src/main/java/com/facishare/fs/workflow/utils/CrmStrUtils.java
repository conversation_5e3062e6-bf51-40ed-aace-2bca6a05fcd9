/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.workflow.utils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.facishare.fs.common_utils.DateTimeUtils;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.metadata.beans.MetaDataParser;
import com.facishare.fs.pluginapi.crm.type.CoreObjType;
import com.facishare.fs.workflow.http.instance.beans.MChangeDetail;

import android.text.TextUtils;

/**
 * Created by wangy on 2017/4/24.
 */

public class CrmStrUtils {

    /**
     * 从Object中获取图片地址，预置的老对象与自定义对象在更改前、更改后返回的数据格式不一致，这里判断下
     *
     * @param isOldValue 是否是更改前的数据
     * @param object     存放图片地址的数据对象
     * @param objType    对象类型
     *
     * @return
     */
    public static String getImagePath(boolean isOldValue, Object object, CoreObjType objType) {
        String imagePath = "";
        if (object instanceof JSONObject) {
            JSONObject jsonObject = (JSONObject) object;
            imagePath = jsonObject.getString("path");
        }
        return imagePath;
    }

    /**
     * 获取编辑触发的审批附件字段数据，预置的老对象与自定义对象在更改前、更改后返回的数据格式不一致，这里判断下
     *
     * @param isOldValue   是否是更改前的数据
     * @param fieldApiName 字段名
     * @param detail       数据
     * @param objType      对象类型
     *
     * @return map
     */
    public static Map<String, Object> getAttachDataMap(boolean isOldValue, String fieldApiName,
                                                       MChangeDetail.MFieldChangeDetail detail,
                                                       CoreObjType objType) {
        Map<String, Object> dataMap = new HashMap<>();
        Object data = isOldValue ? detail.getOldValue() : detail.getNewValue();
        dataMap.put(fieldApiName, data);
        return dataMap;
    }

    public static List<Integer> stringToList(List<String> strings) {
        if (strings == null || strings.isEmpty()) {
            return null;
        }
        List<Integer> list = new ArrayList<>();
        for (String string : strings) {
            if (TextUtils.isEmpty(string)) {
                continue;
            }
            list.add(MetaDataParser.parseInt(string));
        }
        return list;
    }

    public static void removeValue(int value, List<Integer> values) {
        if (values == null || values.isEmpty()) {
            return;
        }
        Iterator<Integer> it = values.iterator();
        while (it.hasNext()) {
            Integer integer = it.next();
            if (integer == value) {
                it.remove();
            }
        }
    }

    /**
     * 获取时间显示字符串
     */
    public static String formatTime1(long time) {
        return isNullTime(time) ? "--"
                : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA).format(new Date(time));
    }

    /**
     * 获取时间显示字符串
     */
    public static String formatTime2(long time) {
        if (isNullTime(time)) {
            return "--";
        } else {
            Date date = new Date(time);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            String pattern;
            if (Calendar.getInstance().get(Calendar.YEAR) == calendar.get(Calendar.YEAR)) {
                pattern = I18NHelper
                        .getText("common.date_time_format.des.mm_dd_hh_mm")/* MM月dd日 HH:mm */;
            } else {
                pattern = I18NHelper.getText(
                        "wq.wq.fs_net_disk_file_util.text.yyyy_mm_dd")/* yyyy年MM月dd日 HH:mm */;
            }
            return new SimpleDateFormat(pattern, Locale.CHINA).format(date);
        }
    }

    /**
     * 判断是否是和服务端约定的空时间
     */
    public static boolean isNullTime(long time) {
        return time == DateTimeUtils.DEFAULT_TIME || time == 0;
    }
}
