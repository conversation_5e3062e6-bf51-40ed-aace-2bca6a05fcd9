package com.facishare.fs.workflow.approvedetail.adapters.nodes;

import java.util.List;

import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.pluginapi.contact.beans.User;
import com.facishare.fs.workflow.R;
import com.facishare.fs.workflow.approvedetail.adapters.ApproveNodeBean;
import com.facishare.fs.workflow.approvedetail.adapters.view.ApproveNodeTitleView;
import com.facishare.fs.workflow.http.instance.beans.GetDetailByInstanceIdResult;
import com.facishare.fs.workflow.utils.Shell;

import android.view.LayoutInflater;
import android.view.ViewGroup;

/**
 * 单人节点
 * <p>
 * Created by xiangd on 2017/8/4.
 */
public class SingleViewHolder extends BaseViewHolder {

    private ApproveNodeTitleView title;

    public SingleViewHolder(LayoutInflater inflater, ViewGroup parentView) {
        super(inflater, R.layout.item_approve_flow_node_type_single, parentView);
        title = convertView.findViewById(R.id.title);
    }

    @Override
    public void updateView(ApproveNodeBean data, int position, int allCount) {
        super.updateView(data, position, allCount);
        GetDetailByInstanceIdResult.MApprovalTask task = data.getApprovalTask();
        String taskName;
        String userId = "";
        if (task.hasCandidateIds()) {
            userId = task.getCandidateIds().get(0);
        } else {
            List<GetDetailByInstanceIdResult.MApprovalTask.MApprovalOpinion> opinions = task.getOpinions();
            if (opinions != null && !opinions.isEmpty()) {
                userId = opinions.get(0).getUserId();
            }
        }
        User user = Shell.getUserById(userId, data.getEmployeeInfo());
        if (user == null) {//该逻辑需要确定是否正确
            taskName = I18NHelper.getText("crm.workflow.ApproveFlowDataConverter.6")/* 人员异常 */;
        } else {
            taskName = task.name;
        }
        title.updateTitle(taskName, data.getLinkAppName());
    }
}