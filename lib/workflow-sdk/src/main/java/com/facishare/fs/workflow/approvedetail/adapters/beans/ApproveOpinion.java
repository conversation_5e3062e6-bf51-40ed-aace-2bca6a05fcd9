/*
 * Copyright (C) 2020 Facishare Technology Co., Ltd. All Rights Reserved.
 */

/**
 * 文件名 : ApproveOpinion.java
 * 包含类名列表 : ApproveOpinion
 * 版本信息 : Ver 1.0
 * 创建日期 : 2016年12月01日 12:30
 */
package com.facishare.fs.workflow.approvedetail.adapters.beans;

import java.util.List;
import java.util.Map;

import com.facishare.fs.workflow.beans.ApproveNodeStatus;
import com.facishare.fs.workflow.enums.OpinionNodeType;
import com.facishare.fs.workflow.http.instance.beans.GetDetailByInstanceIdResult;

/**
 * 类名 : ApproveOpinion
 * 作者 : wangying
 * 实现的主要功能 :
 * 创建日期 : 2016年12月01日 12:30
 */
public class ApproveOpinion extends BaseAOpinion {

    private GetDetailByInstanceIdResult.MApprovalTask mApprovalTask;
    private GetDetailByInstanceIdResult.MApprovalTask.MApprovalOpinion mApprovalOpinion;

    public ApproveOpinion(Map<String, Object> employeeInfo,
                          GetDetailByInstanceIdResult.MApprovalTask task,
                          GetDetailByInstanceIdResult.MApprovalTask.MApprovalOpinion opinion) {
        super(OpinionNodeType.AOpinion, employeeInfo);
        this.mApprovalTask = task;
        this.mApprovalOpinion = opinion;
    }

    @Override
    protected int getSortValue() {
        return (int) getReplyTime();
    }

    public String getUserId() {
        return mApprovalOpinion == null ? null : mApprovalOpinion.getUserId();
    }

    public ApproveNodeStatus getActionType() {
        return mApprovalOpinion == null ? null
                : ApproveNodeStatus.getApproveNodeStatus(mApprovalOpinion.getActionType());
    }

    public String getOpinion() {
        return mApprovalOpinion == null ? null : mApprovalOpinion.getOpinion();
    }

    public boolean isSequence() {
        return mApprovalOpinion != null && mApprovalOpinion.isSequence();
    }

    public String getToTaskName() {
        return mApprovalOpinion == null ? null : mApprovalOpinion.getToTaskName();
    }

    public List<String> getExtraNodeAssignee() {
        return mApprovalOpinion == null ? null : mApprovalOpinion.getExtraNodeAssignee();
    }

    public boolean isGradeOrLevel() {
        return mApprovalTask != null && mApprovalTask.isGradeOrLevel();
    }

    public boolean isAutoAgreed() {
        return mApprovalOpinion != null && mApprovalOpinion.isAutoAgree();
    }

    public long getReplyTime() {
        return mApprovalOpinion == null ? 0 : mApprovalOpinion.getReplyTime();
    }
}