package com.facishare.fs.workflow.views;

import java.util.ArrayList;
import java.util.List;

import com.facishare.fs.common_utils.function.BiConsumer;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.pluginapi.contact.beans.OutOwner;
import com.facishare.fs.pluginapi.contact.beans.User;
import com.facishare.fs.workflow.R;
import com.facishare.fs.workflow.selectuser.SelectEmpConfig;
import com.facishare.fs.workflow.selectuser.SelectEmpManager;

import android.content.Context;
import android.graphics.Color;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

/**
 * 选人View
 */
public class SelectEmpView extends FrameLayout {

    private List<Integer> mSelectedIds;//已选的id
    private List<Integer> mFilterIds;//过滤的id
    private boolean mOnlyChooseOne = false;
    private int mSelectMaxCount = -1;
    private String mLinkAppId;
    private String mLinkAppType;

    private TextView mContentText;

    public SelectEmpView(Context context) {
        this(context, null);
    }

    public SelectEmpView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SelectEmpView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        View view = LayoutInflater.from(context).inflate(R.layout.layout_select_emp, this, true);
        view.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                SelectEmpConfig config = new SelectEmpConfig()
                        .setSelectedIds(mSelectedIds)
                        .setFilterIds(mFilterIds)
                        .setOnlyChooseOne(mOnlyChooseOne)
                        .setSelectMaxCount(mSelectMaxCount)
                        .setLinkAppId(mLinkAppId)
                        .setLinkAppType(mLinkAppType);
                SelectEmpManager.go2SelectEmpAct(context, config,
                        new BiConsumer<List<User>, List<OutOwner>>() {
                            @Override
                            public void accept(List<User> users, List<OutOwner> outOwners) {
                                if (mSelectedIds == null) {
                                    mSelectedIds = new ArrayList<>();
                                }
                                mSelectedIds.clear();
                                List<String> userNames = new ArrayList<>();
                                if (users != null && !users.isEmpty()) {
                                    for (User user : users) {
                                        if (user == null) {
                                            continue;
                                        }
                                        userNames.add(user.getName());
                                        mSelectedIds.add(user.getId());
                                    }
                                }
                                if (outOwners != null && !outOwners.isEmpty()) {
                                    for (OutOwner outOwner : outOwners) {
                                        if (outOwner == null) {
                                            continue;
                                        }
                                        userNames.add(outOwner.name);
                                        mSelectedIds.add((int) outOwner.id);
                                    }
                                }
                                mContentText.setText(TextUtils.join(",", userNames));
                            }
                        });
            }
        });
        TextView titleView = view.findViewById(R.id.tv_title);
        final String STR_REQUIRED_FIELD_PREFIX = "* ";
        StringBuilder stringBuilder = new StringBuilder(STR_REQUIRED_FIELD_PREFIX);
        stringBuilder.append(I18NHelper.getText("crm.workflow.IGetResultFrag.1")/* 加签人 */);
        SpannableString spannableString = new SpannableString(stringBuilder);
        ForegroundColorSpan colorSpan = new ForegroundColorSpan(Color.parseColor("#f27474"));
        spannableString.setSpan(colorSpan, 0, STR_REQUIRED_FIELD_PREFIX.length(),
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        titleView.setText(spannableString);

        mContentText = view.findViewById(R.id.tv_content);
        mContentText.setHint(I18NHelper.getText("crm.layout.select_task_priority.7280")/* 请选择 */);
    }

    public SelectEmpView setSelectedIds(List<Integer> selectedIds) {
        this.mSelectedIds = selectedIds;
        return this;
    }

    public SelectEmpView setFilterIds(List<Integer> filterIds) {
        this.mFilterIds = filterIds;
        return this;
    }

    public SelectEmpView setOnlyChooseOne(boolean onlyChooseOne) {
        this.mOnlyChooseOne = onlyChooseOne;
        return this;
    }

    public SelectEmpView setSelectMaxCount(int selectMaxCount) {
        this.mSelectMaxCount = selectMaxCount;
        return this;
    }

    public SelectEmpView setLinkAppId(String mLinkAppId) {
        this.mLinkAppId = mLinkAppId;
        return this;
    }

    public SelectEmpView setLinkAppType(String mLinkAppType) {
        this.mLinkAppType = mLinkAppType;
        return this;
    }

    public List<Integer> getSelectedIds() {
        return mSelectedIds;
    }

    public List<String> getSelectedIds2String() {
        if (mSelectedIds == null || mSelectedIds.isEmpty()) {
            return null;
        }
        List<String> ids = new ArrayList<>();
        for (Integer id : mSelectedIds) {
            ids.add(String.valueOf(id));
        }
        return ids;
    }
}
