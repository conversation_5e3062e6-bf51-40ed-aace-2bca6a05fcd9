/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.workflow.http.instance.beans;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 类名
 * 作者 zhangss
 * 实现的主要功能。
 * 创建日期 2019/3/14
 */
public class GetCanRefuseTasksResult implements Serializable {

    private List<BeforeTask> taskList;
    private boolean definitionModified;

    @JSONField(name = "M1")
    public List<BeforeTask> getTaskList() {
        return taskList;
    }

    @JSONField(name = "M1")
    public void setTaskList(List<BeforeTask> taskList) {
        this.taskList = taskList;
    }

    @JSONField(name = "M2")
    public boolean isDefinitionModified() {
        return definitionModified;
    }

    @JSONField(name = "M2")
    public void setDefinitionModified(boolean definitionModified) {
        this.definitionModified = definitionModified;
    }

    public static class BeforeTask implements Serializable {

        private String taskId;
        private String taskFullName;

        @JSONField(name = "M1")
        public String getTaskId() {
            return taskId;
        }

        @JSONField(name = "M1")
        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        @JSONField(name = "M2")
        public String getTaskFullName() {
            return taskFullName;
        }

        @JSONField(name = "M2")
        public void setTaskFullName(String taskFullName) {
            this.taskFullName = taskFullName;
        }
    }
}
