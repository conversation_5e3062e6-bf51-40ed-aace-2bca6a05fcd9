/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.workflow.views;

import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.workflow.R;
import com.facishare.fs.workflow.utils.Shell;
import com.fxiaoke.cmviews.CircleImageView;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.nostra13.universalimageloader.core.DisplayImageOptions;
import com.nostra13.universalimageloader.core.ImageLoader;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.FrameLayout;

/**
 * 作者 : wangying
 * 实现的主要功能 :
 */
public class ApproveHeadView extends FrameLayout {
    private CircleImageView mHeadImg;
    private CircleImageView mStatusImg;
    private float sizeDp = 36;//头像大小
    private float paddingLeftRight = 6; //左右边距
    private DisplayImageOptions mImageOptions;

    public ApproveHeadView(Context context) {
        super(context);
        init(context, null);
    }

    public ApproveHeadView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        mHeadImg = new CircleImageView(context,attrs);
        mStatusImg = new CircleImageView(context,attrs);
        mImageOptions = Shell.getImageOptions(context,R.drawable.approve_head_single,true);
        mHeadImg.setImageResource(mImageOptions.getImageResForEmptyUri());
        addView(mHeadImg);
        addView(mStatusImg);
        setPadding(FSScreen.dp2px(paddingLeftRight),0,FSScreen.dp2px(paddingLeftRight),0);
    }

    /**
     * 修改头像尺寸
     *
     * @param dp
     */
    public void setSizeDp(float dp) {
        sizeDp = dp;
        requestLayout();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int itemWidthSpec = MeasureSpec.makeMeasureSpec(FSScreen.dp2px(sizeDp),MeasureSpec.EXACTLY);
        int itemHeightSpec = MeasureSpec.makeMeasureSpec(FSScreen.dp2px(sizeDp),MeasureSpec.EXACTLY);
        measureChildren(itemWidthSpec,itemHeightSpec);
        int totalWidthSpec = MeasureSpec.makeMeasureSpec(FSScreen.dp2px(sizeDp + paddingLeftRight * 2),
                MeasureSpec.EXACTLY);
        int totalHeightSpec = MeasureSpec.makeMeasureSpec(FSScreen.dp2px(sizeDp),MeasureSpec.EXACTLY);
        setMeasuredDimension(totalWidthSpec,totalHeightSpec);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        int padding = FSScreen.dp2px(paddingLeftRight);
        int width = right - left;
        int height = bottom - top;
        mHeadImg.layout(padding,0,width - padding,height);
        mStatusImg.layout(padding,0,width - padding,height);
    }

    public void setBorderColor(int color){
        mHeadImg.setBorderColor(color);
        mStatusImg.setBorderColor(color);
    }

    public void setBorderWidth(int width){
        mHeadImg.setBorderWidth(width);
        mStatusImg.setBorderWidth(width);
    }

    public void setHeadImage(int res){
        mHeadImg.setImageResource(res);
    }

    public void setHeadImage(String imgUrl) {
        ImageLoader.getInstance().displayImage(WebApiUtils.getDownloadUrlForImg(imgUrl, WebApiUtils.ImageType
                .IMG_100x100),mHeadImg,mImageOptions);
    }

    public void setStatusImg(int resId) {
        mStatusImg.setImageResource(resId);
    }

    public void setCoverImage(int res){
        mStatusImg.setImageResource(res);
    }

}