package com.fxiaoke.host.ava;

import com.fxiaoke.avatar.main.MPActivity;
import com.fxiaoke.avatar.page.GlobalPageManager;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.stat_engine.events.custevents.AvaCustomPerformanceEvent;
import com.fxiaoke.stat_engine.events.custevents.AvaPerformanceEvent;
import com.fxiaoke.stat_engine.model.CustEventType;
import com.weidian.lib.hera.trace.HeraTrace;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class CustomTickManager {

    private static final CustomTickManager instance = new CustomTickManager();
    private CustomTickManager() {}
    public static CustomTickManager getInstance() {
        return instance;
    }

    private final Map<String, Tick> ticks = new HashMap<>();

    public void tick_custom(String pageId, JSONObject  params) {
        MPActivity  act = GlobalPageManager.getInstance().getMPActivityByPageId(pageId);
        if(act != null){
            act.customTick(params);
        }else{
            FCLog.e("customTick", "customTick no page "  + pageId + " params:" + params.toString());
        }
    }

    public static class Tick {
        private String pageId;
        private String key;
        private long startTime;
        private long endTime;
        private long cost;

        public String getPageId() {
            return pageId;
        }

        public void setPageId(String pageId) {
            this.pageId = pageId;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public long getStartTime() {
            return startTime;
        }

        public void setStartTime(long startTime) {
            this.startTime = startTime;
        }

        public long getEndTime() {
            return endTime;
        }

        public void setEndTime(long endTime) {
            this.endTime = endTime;
        }

        public long getCost() {
            return cost;
        }

        public void setCost(long cost) {
            this.cost = cost;
        }
    }
}