package com.fxiaoke.host.ava;

import java.util.HashMap;
import java.util.Map;

import org.json.JSONException;
import org.json.JSONObject;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.avatar.api.BaseApi;
import com.fxiaoke.avatar.main.MP;
import com.fxiaoke.avatar.main.MPManager;
import com.fxiaoke.fscommon.avatar.config.ConfigManagerService;
import com.fxiaoke.fscommon.avatar.stat.IAvatarStat;
import com.fxiaoke.fscommon.weex.bundle.BundleInfo;
import com.fxiaoke.stat_engine.StatEngine;
import com.fxiaoke.stat_engine.events.StatEvent;
import com.fxiaoke.stat_engine.events.custevents.ExEvent;
import com.fxiaoke.stat_engine.events.session.UeEventSession;
import com.fxiaoke.stat_engine.http.HttpLogTickManager;
import com.fxiaoke.stat_engine.model.PageType;
import com.fxiaoke.stat_engine.statuscode.ErrorType;
import com.weidian.lib.hera.interfaces.IApiSync;
import com.weidian.lib.hera.interfaces.ICallback;
import com.fxiaoke.avatar.page.ViewBridge;
import android.content.Context;

/**
 * 获取fs环境的基本信息
 */
public class AvatarStatApi extends BaseApi implements IApiSync {
    private final String TAG = AvatarStatApi.class.getName();
    //性能埋点
    private final String uiStart_PS = "uiStart_PS";
    private final String uiEnd_PS = "uiEnd_PS";
    private final String netStart_PS = "netStart_PS";
    private final String netEnd_PS = "netEnd_PS";
    private final String dataRenderStart_PS = "dataRenderStart_PS";
    private final String dataRenderEnd_PS = "dataRenderEnd_PS";
    private final String eventData_PS = "eventData_PS";
    private final String tick_PS = "tick_PS";

    //可用率埋点
    private final String start_SS = "start_SS";
    private final String end_SS = "end_SS";
    private final String error_SS = "error_SS";
    private final String cancel_SS = "cancel_SS";

    //业务埋点
    private final String tick_Stat = "tick_Stat";
    private final String tick_http = "tick_http";
    Map<String,UeEventSession> mUeEventSessionCache=new HashMap<>();

    public UeEventSession getUeEventSession(String eventID,String key){
        UeEventSession us=mUeEventSessionCache.get(key);
        if(us==null){
            us=new UeEventSession(StatEvent.ueEvent(eventID),key, new PageType(PageType.PageType_avatar));
            mUeEventSessionCache.put(key,us);
        }
        return us;
    }

    public AvatarStatApi(Context context) {
        super(context);
    }

    @Override
    public void onCreate() {
        super.onCreate();
    }


    @Override
    public String[] apis() {
        return new String[]{
                uiStart_PS,
                uiEnd_PS,
                netStart_PS,
                netEnd_PS,
                dataRenderStart_PS,
                dataRenderEnd_PS,
                eventData_PS,
                tick_PS,
                start_SS,
                end_SS,
                error_SS,
                cancel_SS,
                tick_Stat,
                tick_http,
                "tick_custom",
                "tick_exclude"
        };
    }

    @Override
    public void invoke(String event, JSONObject param, ICallback callback) {
        try {
            doEvent(event, param);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public String invokeSync(String event, JSONObject param, ICallback iCallback) {
        try {
            return doEvent(event, param);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return "";
    }

    private String doEvent(String event, JSONObject param) throws JSONException {
        Context ctx=getContext();
        IAvatarStat ias=null;
        if (ctx!=null&&ctx instanceof IAvatarStat){
            ias=(IAvatarStat)ctx;
        }else if (tick_Stat.equals(event)){
        }else{
            return "";
        }
        String pageId=param.has("pageId")?param.getString("pageId"):null;

        if (uiStart_PS.equals(event)) {
            if (getEngineVersion()==1){
                ias.getPerformanceSession(pageId).uiStart();
            }else{
            }
        } else if (uiEnd_PS.equals(event)) {
            if (getEngineVersion()==1){
                ias.getPerformanceSession(pageId).uiEnd();
            }else{
            }

        } else if (netStart_PS.equals(event)) {
            if (getEngineVersion()==1){
                ias.getPerformanceSession(pageId).netStart();
            }else{
            }
        } else if (netEnd_PS.equals(event)) {
            if (getEngineVersion()==1){
                ias.getPerformanceSession(pageId).netEnd();
            }else{
            }
        } else if (dataRenderStart_PS.equals(event)) {
            if (getEngineVersion()==1){
                ias.getPerformanceSession(pageId).renderStart();
            }else{
            }
        } else if (dataRenderEnd_PS.equals(event)) {
            if (getEngineVersion()==1){
                ias.getPerformanceSession(pageId).renderEnd();
            }else{
            }
        } else if (eventData_PS.equals(event)) {
            if (getEngineVersion()==1){
                ias.getPerformanceSession(pageId).eventData(param.toString());
            }else{
            }
        } else if (tick_PS.equals(event)) {
            if (getEngineVersion()==1){
                ias.getPerformanceSession(pageId).biz(param.getString("biz"))
                        .module(param.getString("module"))
                        .subModule(param.getString("submodule")).tick(param, true);

            }else{
                if (pageId==null){
                    return "";
                }
                ViewBridge vb = null;
                MP mp = MPManager.getInstance().getMP(mAppConfig.getAppId());
                if(mp!=null){
                    vb = mp.getViewBridge(Integer.valueOf(pageId.substring(7)));
                }
//                ViewBridge vb=PageWebViewCache.getInstance().getPageWebView(mAppConfig.getAppId(),Integer.valueOf(pageId.substring(7)));
                if(vb!=null){
                    vb.setPerformanceStatData(param);
                }
            }
        } else if (start_SS.equals(event)) {
            getUeEventSession((String)param.get("eventID"),(String)param.get("uniqueKey")).startTick();
        } else if (end_SS.equals(event)) {
            getUeEventSession("",(String)param.get("uniqueKey")).endTick();
        } else if (error_SS.equals(event)) {
            ErrorType errorType =  JSON.parseObject(param.toString(), ErrorType.class);
            getUeEventSession("",(String)param.get("uniqueKey")).
                    errorTick(errorType);
        } else if (cancel_SS.equals(event)) {
            getUeEventSession("",(String)param.get("uniqueKey")).cancelTick();
        } else if (tick_Stat.equals(event)) {
            Map<String, Object> params=new HashMap<>();
            JSONObject eventData = param.getJSONObject("eventData");
            if (eventData!=null){
                //转为 fastjson，防止埋点引擎不识别
                params.putAll(JSON.parseObject(eventData.toString()).getInnerMap());

            }
            params.put(PageType.PageType, PageType.PageType_avatar);
            StatEngine.tick(param.getString("eventID"),params);
        } else if (tick_http.equals(event)) {
            HttpLogTickManager.getInstance().tickByAva(param);
        }else if ("tick_custom".equals(event)) {
            CustomTickManager.getInstance().tick_custom(pageId, param.has("params") ? param.getJSONObject("params"): new JSONObject());
        }else if ("tick_exclude".equals(event)) {
            if (pageId==null){
                return "";
            }

            long time = param.has("exclude_time") ? param.getLong("exclude_time"):0;
            if(time!= 0){
                ias.getPerformanceSession(pageId).tick_exclude(time);
            }
        }
        return "";
    }
}
