<?xml version="1.0" encoding="utf-8"?>
<!--<selector xmlns:android="http://schemas.android.com/apk/res/android">-->
<!--    &lt;!&ndash; 按下状态 &ndash;&gt;-->
<!--    <item-->
<!--        android:drawable="@color/list_item_bg_pressed"-->
<!--        android:state_focused="false"-->
<!--        android:state_pressed="true">-->
<!--        <shape android:shape="rectangle">-->
<!--            <corners-->
<!--                android:bottomLeftRadius="8dp"-->
<!--                android:bottomRightRadius="8dp"/>-->
<!--        </shape>-->
<!--    </item>-->

<!--    &lt;!&ndash; 正常状态 &ndash;&gt;-->
<!--    <item-->
<!--        android:drawable="@color/list_item_bg_normal">-->
<!--        <shape android:shape="rectangle">-->
<!--            <corners-->
<!--            android:bottomLeftRadius="8dp"-->
<!--            android:bottomRightRadius="8dp"/>-->
<!--        </shape>-->
<!--    </item>-->
<!--</selector>-->


<selector xmlns:android="http://schemas.android.com/apk/res/android">
<!-- 按下状态 -->
<item
    android:state_pressed="true">
    <shape android:shape="rectangle">
        <solid android:color="@color/list_item_bg_pressed" />
        <corners
            android:bottomLeftRadius="8dp"
            android:bottomRightRadius="8dp"/>
    </shape>
</item>

<!-- 正常状态 -->
<item>
    <shape android:shape="rectangle">
        <solid android:color="@color/list_item_bg_normal" />
        <corners
            android:bottomLeftRadius="8dp"
            android:bottomRightRadius="8dp"/>
    </shape>
</item>
</selector>