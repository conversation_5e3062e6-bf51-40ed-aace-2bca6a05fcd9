<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent" android:layout_height="wrap_content"
              android:background="#ffffff"
              android:orientation="vertical">
    <com.facishare.fs.i18n.I18NTextView
            android:id="@+id/ccm_title"
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:maxLines="1"
            android:ellipsize="end"
            i18n:fstext="common.host.des.checking_remind"
            android:textColor="#222222"
            android:textSize="20sp"
            android:gravity="center_vertical"
            android:layout_marginLeft="24dp"
            android:layout_marginRight="24dp"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp" />
    <TextView android:id="@+id/check_now" android:layout_width="match_parent"
              android:layout_height="48dp"
              android:background="@drawable/list_item_select"
              android:ellipsize="end"
              android:gravity="center_vertical"
              android:paddingLeft="24dp"
              android:paddingRight="24dp"
              android:maxLines="1"
              android:textColor="#333333"
              android:textSize="16sp"
              android:clickable="true"
            android:paddingEnd="24dp"
            android:paddingStart="24dp" />
    <TextView android:id="@+id/alarm_delay_1"
              android:layout_width="match_parent"
              android:layout_height="48dp"
              android:background="@drawable/list_item_select"
              android:ellipsize="end"
              android:gravity="center_vertical"
              android:paddingLeft="24dp"
              android:paddingRight="24dp"
              android:maxLines="1"
              android:textColor="#333333"
              android:textSize="16sp"
              android:clickable="true"
            android:paddingStart="24dp"
            android:paddingEnd="24dp" />
    <TextView android:id="@+id/alarm_delay_2"
              android:layout_width="match_parent"
              android:layout_height="48dp"
              android:background="@drawable/list_item_select"
              android:ellipsize="end"
              android:gravity="center_vertical"
              android:paddingLeft="24dp"
              android:paddingRight="24dp"
              android:maxLines="1"
              android:textColor="#333333"
              android:textSize="16sp"
              android:clickable="true"
            android:paddingEnd="24dp"
            android:paddingStart="24dp" />
    <View
            android:id="@+id/bottom_line"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:background="#ffffff"
    />
</LinearLayout>
