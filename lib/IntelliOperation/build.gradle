disableLintTask()
apply plugin: 'com.android.library'

android {
    compileSdkVersion gradle.compileSdkVersionForLib
    buildToolsVersion gradle.buildToolsVersion
    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            aidl.srcDirs = ['src']
            java.srcDirs = ['src']
            res.srcDirs = ['res']
            assets.srcDirs = ['assets'] //多了一个assets目录
        }
    }

    compileOptions {
        sourceCompatibility gradle.javaVersion
        targetCompatibility gradle.javaVersion
    }

    defaultConfig {
        // applicationId "com.fxiaoke.intelliOperation"
        minSdkVersion 14
        targetSdkVersion gradle.targetSdkVersion
        versionCode 1
        versionName gradle.versionmap.get(project.name)
    }

    packagingOptions {
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

}

dependencies {

    compileOnly "androidx.legacy:legacy-support-v4:${gradle.andoidxVersion}"
    compileOnly files(rootProject.getRootDir().getAbsolutePath() + "/libs/fastjson_${gradle.fastjsonVersion}.jar")
    compileOnly depLibProjectWithMavenOrSource("FXStatEngine")
    compileOnly depLibProjectWithMavenOrSource("fshttp")
    compileOnly depLibProjectWithMavenOrSource("pluginapi")
    compileOnly depLibProjectWithMavenOrSource("data_impl")
    compileOnly depLibProjectWithMavenOrSource("pluginapi_account")
    compileOnly depLibProjectWithMavenOrSource("xUtils")
    compileOnly depLibProjectWithMavenOrSource("FxLog")
    if(releaseType=='RELEASE'){
		compileOnly  depLibProjectWithAarMaven("weex_release")

	}else{
		compileOnly depLibProjectWithAarMaven("weex_debug")

	}
    compileOnly depLibProjectWithMavenOrSource("support_v4")

}
