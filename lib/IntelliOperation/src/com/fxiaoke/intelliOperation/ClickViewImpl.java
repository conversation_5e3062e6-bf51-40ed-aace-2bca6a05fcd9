/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.intelliOperation;

import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.intelliOperation.base.abs.IClickView;
import com.fxiaoke.intelliOperation.beans.H5Config;
import com.fxiaoke.intelliOperation.beans.OpConfig;
import com.fxiaoke.intelliOperation.callback.OnPluginLoadFinishCallback;
import com.fxiaoke.intelliOperation.type.OpShowType;
import com.fxiaoke.intelliOperation.utils.OperLog;
import com.fxiaoke.intelliOperation.utils.ViewUtils;
import com.lidroid.xutils.util.FSNetUtils;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.ViewParent;

/**
 * Created by xiangd on 2017/3/29.
 */
class ClickViewImpl implements IClickView, OnClickListener {

    private static final String TAG = ClickViewImpl.class.getSimpleName();
    /**
     * 响应Click点击事件的View
     */
    private View mSrcOnClickView;
    private OnClickListener mSrcListener;
    private StrategyNode mStrategyNode;

    public ClickViewImpl(StrategyNode strategyNode) {
        this.mStrategyNode = strategyNode;
    }

    @Override
    public void initBaseView(View baseView) {
        this.mSrcOnClickView = baseView;
        setOnClickView(baseView);
    }

    @Override
    public void setOnClickView(View clickView) {
        if (clickView == null) {
            return;
        }
        View findView = findOnClickView(clickView);
        if (findView == null) {
            // OperLog.w(TAG, "Not find clickView id= " + mStrategyNode.getStrategyId());
            return;
        }

        if (mSrcOnClickView != clickView && mSrcOnClickView != null) {
            mSrcOnClickView.setOnClickListener(null);
        }
        mSrcOnClickView = findView;
        OnClickListener findListener = ViewUtils.getViewInnerClickListener(findView);

        if (findListener != this) {
            // OperLog.d(TAG, "success find id= " + mStrategyNode.getNodeButtonId() + ",view= " + clickView);
            mSrcListener = findListener;
            findView.setOnClickListener(this);
        }
        if (findListener == null) {
            // OperLog.w(TAG, "Not find mListener id= " + mStrategyNode.getStrategyId());
        }
    }

    @Override
    public void updateOnClickListener() {
        setOnClickView(mSrcOnClickView);
    }

    @Override
    public void destroy() {
        mSrcOnClickView = null;
        mSrcListener = null;
        mStrategyNode = null;
    }

    @Override
    public boolean onItemClick(final View view, int position, boolean bizDoShowH5) {
        String buttonID = mStrategyNode.getNodeButtonId();
        OpShowType opShowType = OperationManager.getInstance().getOpShowType(buttonID);
        OperationManager.getInstance().click(mStrategyNode);
        if (OpShowType.RedH5 == opShowType) {
            if (bizDoShowH5) {
                OperLog.i(TAG, "onItemClick, biz handle the showRedH5, id = " + buttonID);
            } else {
                final OpConfig opConfig = OperationManager.getInstance().getOpConfig(buttonID);
                OperationManager.getInstance().setPluginLoadListener(new OnPluginLoadFinishCallback() {
                    @Override
                    public void onFinish() {
                        doRedH5Click(view.getContext(), new H5Config(opConfig.h5Url));
                    }
                });
            }
            return true;
        } else {
            return false;
        }
    }

    @Override
    public void onClick(final View view) {
        String buttonID = mStrategyNode.getNodeButtonId();
        OpShowType opShowType = OperationManager.getInstance().getOpShowType(buttonID);
        OperationManager.getInstance().setHistoryOpShowType(buttonID, opShowType);
        if (OpShowType.RedH5 == opShowType) {
            OpConfig opConfig = OperationManager.getInstance().getOpConfig(buttonID);

            final H5Config h5Config = new H5Config(opConfig.h5Url);
            if (mStrategyNode.onShowH5ClickCallback(h5Config)) {
                OperLog.i(TAG, "onClick, biz handle the showRedH5, id = " + buttonID);
            } else {
                OperationManager.getInstance().setPluginLoadListener(new OnPluginLoadFinishCallback() {
                    @Override
                    public void onFinish() {
                        doRedH5Click(view.getContext(), h5Config);
                    }
                });
            }
        }

        if (mSrcListener != null) {
            mSrcListener.onClick(view);
        }
        OperationManager.getInstance().click(mStrategyNode);
    }

    private void doRedH5Click(Context context, H5Config h5Config) {
        OperLog.i(TAG, "doRedH5Click, id= " + mStrategyNode.getNodeButtonId() + ",h5Config= " + h5Config);
        int netType = FSNetUtils.getInstance().getNetType();
        if (netType > 0) {
            OperationManager.showH5ShadeAct(context, h5Config.h5Url);
        } else {
            OperLog.i(TAG, "Not load h5Url, netType = " + netType);
        }
    }

    private View findOnClickView(View view) {
        return findOnClickViewFromParent(view);
    }

    private View findOnClickViewFromParent(View view) {
        if (view == null) {
            return null;
        }

        if (view.isClickable()) {
            return view;
        }
        ViewParent viewParent = view.getParent();
        if (viewParent instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) viewParent;
            if (viewGroup.isClickable()) {
                return viewGroup;
            }
            return findOnClickViewFromParent(viewGroup);
        }
        return null;
    }
}
