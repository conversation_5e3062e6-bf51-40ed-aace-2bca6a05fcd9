/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.intelliOperation.render;

import com.fxiaoke.intelliOperation.type.OpNodeType;
import com.fxiaoke.intelliOperation.utils.OperLog;
import com.fxiaoke.intelliOperation.utils.ViewUtils;
import com.fxiaoke.intelliOperation.view.HookFrameLayout;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

/**
 * Created by xiangd on 2017/3/30.
 */
class HookViewGroupMgr {

    private static final String TAG = HookViewGroupMgr.class.getSimpleName();

    private HookFrameLayout mHookFrameLayout;

    public HookViewGroupMgr(View clickView, View anchorView, RedViewMgr redViewMgr, OpNodeType opNodeType) {
        initHookViewGroup(clickView, anchorView, redViewMgr, opNodeType);
    }

    private void initHookViewGroup(View clickView, View baseView, RedViewMgr redViewMgr, OpNodeType opNodeType) {
        View contentView = clickView;
        if (contentView == null) {
            contentView = ViewUtils.findWrapViewGroup(baseView);
        }
        if (contentView instanceof ViewGroup) {
            final Context context = contentView.getContext();
            mHookFrameLayout = HookFrameLayout.wrapHookFrameLayout(context, (ViewGroup) contentView, opNodeType);
            if (mHookFrameLayout != null) {
                mHookFrameLayout.measure(0, 0);
                redViewMgr.initHookViewAndUpdateHierarchyView(mHookFrameLayout);
            } else {
                OperLog.w(TAG, "Fail to init HookViewGroup!");
            }
        } else {
            OperLog.w(TAG, "Fail to find content View!");
        }
    }

    public void destroy() {
        mHookFrameLayout = null;
    }

}
