/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.intelliOperation;

import java.util.ArrayList;
import java.util.List;

import com.fxiaoke.intelliOperation.base.abs.IRenderViewListener;
import com.fxiaoke.intelliOperation.base.abs.IShowH5CallBack;
import com.fxiaoke.intelliOperation.beans.H5Config;
import com.fxiaoke.intelliOperation.beans.OpConfig;
import com.fxiaoke.intelliOperation.callback.OnShowH5ClickCallback;
import com.fxiaoke.intelliOperation.type.OpNodeType;
import com.fxiaoke.intelliOperation.type.OpShowType;
import com.fxiaoke.intelliOperation.utils.OperLog;
import com.fxiaoke.intelliOperation.utils.ViewUtils;

import android.view.View;
import android.view.View.OnAttachStateChangeListener;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;

/**
 * Created by xiangd on 2017/4/1.
 */
public class DynamicListBizOpNode implements OnItemClickListener, IShowH5CallBack, OnAttachStateChangeListener {

    private static final String TAG = DynamicListBizOpNode.class.getSimpleName();

    private OnShowH5ClickCallback mOnShowH5ClickCallback;
    private OnItemClickListener mOnItemClickListener;
    private List<DynamicBizOpNode> mNodeList = new ArrayList<>();

    public DynamicListBizOpNode(AbsListView absListView) {
        absListView.addOnAttachStateChangeListener(this);
    }

    private void hookOnItemClickListener(AbsListView absListView) {
        OnItemClickListener onItemClickListener = absListView.getOnItemClickListener();
        if (onItemClickListener != null) {
            if (mOnItemClickListener != onItemClickListener) {
                mOnItemClickListener = onItemClickListener;
                absListView.setOnItemClickListener(this);
            } else {
                OperLog.w(TAG, "Exception mOnItemClickListener = findOnItemClickListener");
            }
        } else {
            OperLog.e(TAG, "Not find OnItemClickListener, absListView= " + absListView);
        }
    }

    public DynamicBizOpNode newBizOpNode(int position, String buttonID, View anchorView) {
        return newBizOpNode(position, buttonID, null, anchorView);
    }

    public DynamicBizOpNode newBizOpNode(int position, String buttonID, View convertView, View anchorView, IRenderViewListener listener) {
        DynamicBizOpNode bizOpNode = newBizOpNode(position, buttonID,convertView,anchorView);
        bizOpNode.setRenderViewCallBack(listener);
        return bizOpNode;
    }

    /**
     * getView中的每个子View需通过此方法获取到DynamicBizOpNode
     *
     * @param position 位置索引，对应getView中的参数position
     * @param buttonID
     * @param clickedView clickedView是getView中响应点击事件的ViewGroup，且包含anchorView子View（可以为null）
     * @param anchorView 红点显示的基准View，默认显示在该View的右上角
     * @return DynamicBizOpNode
     */
    public DynamicBizOpNode newBizOpNode(int position, String buttonID, View clickedView, View anchorView) {
        DynamicBizOpNode bizOpNode = new DynamicBizOpNode(position, buttonID, OpNodeType.AbsListViewItem);
        bizOpNode.initDynamicViewRender(clickedView, anchorView);
        bizOpNode.register();
        mNodeList.add(bizOpNode);
        return bizOpNode;
    }

    public void destroy() {
        for (DynamicBizOpNode opNode : mNodeList) {
            opNode.destroy();
        }
        mNodeList.clear();
        mOnItemClickListener = null;
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        OperLog.d(TAG, "onItemClick position = " + position);
        DynamicBizOpNode matchNode = null;
        boolean bizDoShowH5 = false; // 业务是否自己处理了H5遮罩逻辑
        for (DynamicBizOpNode opNode : mNodeList) {
            if (opNode.getPosition() == position) {
                matchNode = opNode;
                String buttonID = opNode.getNodeButtonId();
                OpShowType opShowType = OperationManager.getInstance().getOpShowType(buttonID);
                if (OpShowType.RedH5 == opShowType && mOnShowH5ClickCallback != null) {
                    OpConfig opConfig = OperationManager.getInstance().getOpConfig(buttonID);
                    bizDoShowH5 = mOnShowH5ClickCallback.onShowH5(new H5Config(opConfig.h5Url));
                }
                break;
            }
        }

        if (mOnItemClickListener != null&&mOnItemClickListener!=this) {//防止循环
            // 执行原有点击事件
            mOnItemClickListener.onItemClick(parent, view, position, id);
        }

        if (matchNode != null) {
            // 执行内部点击逻辑并更新内部状态
            matchNode.onItemClick(view, position, bizDoShowH5);
        } else {
            OperLog.w(TAG, "Exception, not find BizOpNode position = " + position);
        }

    }

    @Override
    public void onViewAttachedToWindow(View view) {
        // OperLog.d(TAG, "onViewAttached");
        hookOnItemClickListener((AbsListView) view);
    }

    @Override
    public void onViewDetachedFromWindow(View view) {
        // OperLog.d(TAG, "onViewDetached");
    }

    @Override
    public void setShowH5CallBack(OnShowH5ClickCallback callBack) {
        mOnShowH5ClickCallback = callBack;
    }
}
