<?xml version="1.0" encoding="utf-8"?>
<com.intsig.camera.ui.ControlPanelLayout xmlns:android="http://schemas.android.com/apk/res/android"
     xmlns:switcher="http://schemas.android.com/apk/res-auto"
        android:id="@+id/control_panel"
        android:layout_width="96dip"
        android:layout_height="fill_parent"
        android:minWidth="76dp"
        android:background="@color/camera_color_blue">
   <!--  <include layout="@layout/review_thumbnail" />
    <include layout="@layout/review_control" /> -->
     <com.intsig.camera.ui.RotateTextView android:id="@+id/videoLengthText"
            style="@style/VideoSecondText"
            android:visibility="gone" 
            android:layout_height="42dp"
            android:layout_width="42dp"
            android:layout_alignParentTop="true"
            android:layout_marginTop="20dp" />
    <CheckBox android:id="@+id/record_button"
            android:layout_width="74dp"
            android:layout_height="74dp"
            android:layout_centerInParent="true"
            android:scaleType="center"
            android:clickable="true"
            android:focusable="true"
            android:contentDescription="@string/accessibility_shutter_button"
            android:button="@drawable/btn_video_record"/>
    <!-- <include layout="@layout/mode_picker" /> -->
</com.intsig.camera.ui.ControlPanelLayout>