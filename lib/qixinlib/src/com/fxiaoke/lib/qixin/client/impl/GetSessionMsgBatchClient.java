/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.lib.qixin.client.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fs.fsprobuf.ServerProtobuf;
import com.fxiaoke.dataimpl.msg.IMsgDataListener;
import com.fxiaoke.dataimpl.msg.MsgDataController;
import com.fxiaoke.dataimpl.msg.utils.PBReqArgCreateUtils;
import com.fxiaoke.fxdblib.ChatDBHelper;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxdblib.beans.SessionMessage;
import com.fxiaoke.fxdblib.beans.SessionTypeKey;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.fxsocketlib.businessctrl.FcpTaskBase;
import com.fxiaoke.fxsocketlib.socketctrl.FcpResponse;
import com.fxiaoke.lib.qixin.BizListenerManager;
import com.fxiaoke.lib.qixin.client.QiXinApiClient;
import com.fxiaoke.lib.qixin.consts.QXLog;

import android.content.Context;

/**
 * Created by ywg on 2016/12/21.
 */

public class GetSessionMsgBatchClient extends QiXinApiClient<Boolean, ServerProtobuf.BatchGetMessagesResult> {
    final String TAG = "BatchGetMsgs";
    private static final String V3_QUERY_BatchGetMessages = "A.Messenger.BatchGetMessages";
    private Map<String, Long> mUtilMessageIds = new HashMap<>();
    List<SessionListRec> mSlList = null;
    public GetSessionMsgBatchClient(Context ctx, List<SessionListRec> slList) {
        super(ctx, PBReqArgCreateUtils.getEnvBySession(slList.get(0)));
        mSlList = slList;
    }

    @Override
    public void execute() {
        super.execute();
    }

    @Override
    public String getAction() {
        return V3_QUERY_BatchGetMessages;
    }

    @Override
    public Map<String, Object> getParamMap() {
        Map<String, Object> params = new HashMap<String, Object>();
//        params.put("task", "getSessionMsgContinueNext");
//        params.put("sessionid", mSessionId);
        return params;
    }

    @Override
    public byte[] getParamContent() {
        //make param
        ChatDBHelper dbHelper = getChatDbHelper();
        ServerProtobuf.BatchGetMessagesArg.Builder bgma = ServerProtobuf.BatchGetMessagesArg.newBuilder();
        for (SessionListRec serverSession : mSlList) {
            if (!isNeedGetMsg(serverSession)) {
                FCLog.i(QXLog.MESSAGE_NET, "BatchGetMessages cancel 1"
                        + " sid:" + serverSession.getSessionId()
                        + " C:" + serverSession.getSessionCategory()
                        + " SC:" + serverSession.getSessionSubCategory()
                        + " status:" + serverSession.getStatus());
                continue;
            }
            // TODO: 2016/12/21 本地检查失败不发请求时返回false还是true?
            Boolean b = new GetSessionMsgTDClient(mContext, serverSession).executeSync();
            if (b != null && b.booleanValue()) {
                FCLog.i(QXLog.MESSAGE_NET, "BatchGetMessages cancel TD");
                continue;
            }
            long maxMsgIdInDb = dbHelper.getLastMsgid(serverSession.getSessionId());
            if (maxMsgIdInDb >= serverSession.getLastMessageId()) {
                FCLog.i(QXLog.MESSAGE_NET, "BatchGetMessages cancel 2"
                        + " sid:" + serverSession.getSessionId()
                        + " C:" + serverSession.getSessionCategory()
                        + " SC:" + serverSession.getSessionSubCategory()
                        + " maxMsgIdInDb:" + maxMsgIdInDb
                        + " sessionLastId:" + serverSession.getLastMessageId());
                continue;
            }
            long untilMsgId = (maxMsgIdInDb > 0) ? maxMsgIdInDb : serverSession.getEpochMessageId();
            mUtilMessageIds.put(serverSession.getSessionId(), untilMsgId);

            ServerProtobuf.BatchSessionItem.Builder bsi = ServerProtobuf.BatchSessionItem.newBuilder();
            bsi.setUntilMessageId(untilMsgId);
            bsi.setSessionId(serverSession.getSessionId());
            ServerProtobuf.BatchSessionItem item = bsi.build();
            bgma.addSessions(item);
        }
        if (bgma.getSessionsCount() == 0) {
            return null;
        }
        //当前要拉取消息的一批session必须是同类型，要么都是互联企业，或都非互联企业，所以取第一个
//        bgma.setEnv(getEnvBySession(slList.get(0)));
        bgma.setEnv(mEnv);
        byte[] content = bgma.build().toByteArray();
        return content;
    }

    @Override
    protected boolean configTask(FcpTaskBase task) {
        super.configTask(task);
        task.setMaxExeTime(TASK_MAX_TIME_NONE);//为了让后台自动拉取，所以不设置超时
        return true;
    }

    @Override
    public Boolean processData(FcpTaskBase task, FcpResponse rsp, ServerProtobuf.BatchGetMessagesResult rst) {
        boolean ret = false;
        do {
            if (isFailed(task,rsp)){
                break;
            }
            ChatDBHelper dbHelper = getChatDbHelper();
            byte[] key = getAESKey();
            List<SessionMessage> needProcessList = new ArrayList<SessionMessage>();
            try {
                dbHelper.beginTransaction();
                try {
                    for (ServerProtobuf.BatchSessionMessages bsm : rst.getSessionMessagesList()) {
                        FCLog.i(QXLog.MESSAGE_NET, "BatchGetMessages: " + bsm.getSessionId() + "@util-" + mUtilMessageIds.get(bsm.getSessionId()));

                        List<SessionMessage> smList = new ArrayList<SessionMessage>();
                        persistentServerMsgs(getSession(bsm.getSessionId()), bsm.getMessagesList(), smList,true);
                        needProcessList.addAll(smList);

                        //notify msg listener
                        IMsgDataListener lis = BizListenerManager.getMsgDataListener();
                        if (lis != null) {
                            lis.onNewMsg(bsm.getSessionId(), smList);
                        }
                    }

                    dbHelper.setTransactionSuccessful();
                } finally {
                    dbHelper.endTransaction();
                }
                for (SessionMessage sessionMessage : needProcessList) {
                    MsgDataController.getInstace(mContext).processMsgDown(sessionMessage);
                    // TODO: 2016/12/21 如何触发 handleAVEventMessage
                    //                            if (sessionMessage.getAVEventMsgData() != null) {
                    //                                FCLog.d(TAG, "bs_s handleAVEvent");
                    //                                MsgDataController.getInstace(mContext).handleAVEventMessage(sessionMessage);
                    //                            }
                }

            } catch (NullPointerException e) {
                FCLog.w(TAG, e.getMessage());
            }
        } while (false);
        ret = true;
        return ret;
    }

    @Override
    protected Class getProtoResultType() {
        return ServerProtobuf.BatchGetMessagesResult.class;
    }

    SessionListRec getSession(String sid){
        SessionListRec ret=null;
        if (mSlList!=null){
            for (SessionListRec slr:mSlList
                 ) {
                if (slr.getSessionId().equals(sid)){
                    ret=slr;
                    break;
                }
            }
        }

        return ret;
    }
    boolean isNeedGetMsg(SessionListRec sessionListRec) {
        boolean ret = true;
        if (sessionListRec.getSessionCategory().equals(SessionTypeKey.Session_Remind_key)
                || sessionListRec.getSessionCategory()
                .equals(SessionTypeKey.Session_Remind_New_key)
                || (sessionListRec.getSessionCategory()
                            .equals(SessionTypeKey.Session_OpenApplication_key))
                || (sessionListRec.getSessionCategory()
                            .equals(SessionTypeKey.Session_WorkNotice_key))
                || (sessionListRec.getSessionCategory()
                            .equals(SessionTypeKey.Session_Plan_Reminder_key))
                || (sessionListRec.getSessionCategory()
                            .equals(SessionTypeKey.Session_Approve_Reminder_key))
                || (sessionListRec.getSessionCategory()
                            .equals(SessionTypeKey.Session_Order_Reminder_key))
                || (sessionListRec.getSessionCategory()
                            .equals(SessionTypeKey.Session_Followed_Reminder_key))
                || (sessionListRec.getSessionCategory()
                            .equals(SessionTypeKey.Session_Device_Authorization))
                /* <FEATURE20151216 AVCALL xiangd start */
                // || sessionListRec.getOrderingTime() < 0\
                /* FEATURE20151216 AVCALL xiangd end> */
                || sessionListRec.getStatus() >= 100) {
            ret = false;
        }
        return ret;
    }
}
