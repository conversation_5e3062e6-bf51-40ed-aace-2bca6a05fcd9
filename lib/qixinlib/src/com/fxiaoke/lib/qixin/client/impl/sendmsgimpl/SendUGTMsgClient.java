/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.lib.qixin.client.impl.sendmsgimpl;

import com.fs.fsprobuf.ServerProtobuf;
import com.fxiaoke.fxdblib.beans.SessionMessageTemp;
import com.fxiaoke.fxsocketlib.businessctrl.FcpTaskBase;
import com.fxiaoke.fxsocketlib.socketctrl.FcpResponse;

import android.content.Context;

/**
 * Created by anjx on 2017/2/16.
 */

public class SendUGTMsgClient extends SendMsgBaseClient<Boolean, ServerProtobuf.SendUGTMessageResult> {
    private static final String V3_QUERY_SENDUGTMESSAGE = "A.Messenger.SendUGTMessage";// 新的统一消息模板消息
    private int mContentId;

    public SendUGTMsgClient(Context ctx, ServerProtobuf.EnterpriseEnv env, SessionMessageTemp smt) {
        super(ctx, env, smt);
    }

    @Override
    public String getAction() {
        return V3_QUERY_SENDUGTMESSAGE;
    }

    @Override
    public byte[] getParamContent() {
        // TODO mContentId
        ServerProtobuf.SendUGTMessageArg.Builder ss = ServerProtobuf.SendUGTMessageArg.newBuilder();
        ss.setSessionId(msg.getSessionid());
        ss.setPreviousMessageId(msg.getPreviousMessageId());
        if (mContentId > 0) {
            ss.setContentId(mContentId);
        } else {
            ss.setContentId(0);//获取内容时需要的ContentId   注意：ContentId 和content是互斥的，只有当ContentId <= 0时，content才生效
            ss.setContent(msg.getContent());
        }
        ss.setEnv(getEnvByTempMsg(msg));//设置跨企业标识
        ServerProtobuf.SendUGTMessageArg arg = ss.build();
        byte[] content = arg.toByteArray();
        return content;
    }

    @Override
    public Boolean processData(FcpTaskBase task, FcpResponse rsp,
                            ServerProtobuf.SendUGTMessageResult result) {
        return newMsg(task,rsp, result.getMessage());
    }

    @Override
    protected Class getProtoResultType() {
        return ServerProtobuf.SendUGTMessageResult.class;
    }
}
