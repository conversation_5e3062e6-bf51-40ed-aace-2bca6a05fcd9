/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.lib.qixin.client.impl.sendmsgimpl;

import android.content.Context;

import com.fs.fsprobuf.ServerProtobuf;
import com.fxiaoke.fxdblib.beans.SessionMessageTemp;
import com.fxiaoke.fxsocketlib.businessctrl.FcpTaskBase;
import com.fxiaoke.fxsocketlib.socketctrl.FcpResponse;

/**
 * Created by anjx on 2017/2/16.
 */

public class SendOpenMsgClient extends SendMsgBaseClient<Boolean, ServerProtobuf.SendMessageResult> {
    private static final String V3_QUERY_SendOpenMessage = "A.Messenger.SendOpenMessage";

    public SendOpenMsgClient(Context ctx, ServerProtobuf.EnterpriseEnv env, SessionMessageTemp smt) {
        super(ctx, env, smt);
    }

    @Override
    public String getAction() {
        return V3_QUERY_SendOpenMessage;
    }

    @Override
    public byte[] getParamContent() {
        ServerProtobuf.SendTextMessageArg.Builder ss = ServerProtobuf.SendTextMessageArg
                .newBuilder();
        ss.setContent(msg.getContent());
        ss.setSessionId(msg.getSessionid());
        ss.setPreviousMessageId(msg.getPreviousMessageId());
        ss.setEnv(getEnvByTempMsg(msg));//设置企业标识
        ServerProtobuf.SendTextMessageArg arg = ss.build();
        byte[] content = arg.toByteArray();
        return content;
    }

    @Override
    public Boolean processData(FcpTaskBase task, FcpResponse rsp,
                            ServerProtobuf.SendMessageResult result) {
        return newMsg(task,rsp,result.getMessage());
    }

    @Override
    protected Class getProtoResultType() {
        return ServerProtobuf.SendMessageResult.class;
    }

}
