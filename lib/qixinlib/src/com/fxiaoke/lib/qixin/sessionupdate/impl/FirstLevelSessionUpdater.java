package com.fxiaoke.lib.qixin.sessionupdate.impl;

import java.util.ArrayList;
import java.util.List;

import com.fxiaoke.dataimpl.msg.ISessionListener;
import com.fxiaoke.fxdblib.beans.SessionListRec;
import com.fxiaoke.fxlog.DebugEvent;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.lib.qixin.BizListenerManager;

import android.content.Context;
import android.text.TextUtils;

/**
 * 处理一级session，忽略二级session
 * Created by anjx on 2016/12/19.
 */

public class FirstLevelSessionUpdater extends NormalSessionUpdater {
    private static final DebugEvent TAG = new DebugEvent("FirstLevelSession");
    public FirstLevelSessionUpdater(Context context) {
        super(context);
    }

    @Override
    protected boolean match() {
        return mParam.needRefreshRoot == false && TextUtils.isEmpty(mParam.sessionUpdatedItem.getParentSessionId());
    }

    @Override
    protected void sendNotify() {
        sendNotify2Msg();

        //TODO: 2017/11/7 need delete log at release time
        String sessionLog = "";
        if(mParam.sessionListRec!=null){
            sessionLog = " slr: " + mParam.sessionListRec.toString();
        }
        String smLog = "";
        if(mParam.sessionMessage!=null){
            smLog = " sm: " + mParam.sessionMessage.toString();
        }
        FCLog.d(TAG, "slr updateSessionListRecInfoBySessionMsg " + sessionLog+ smLog);


        List<SessionListRec> sList = new ArrayList<>();
        sList.add(mParam.sessionListRec);
        BizListenerManager.triggerAllSessionListeners(sList);

        List<ISessionListener> sessionListenerList = BizListenerManager.getFirstLevelSessionListeners();
        if (sessionListenerList != null) {
			List<SessionListRec> list = new ArrayList<>();
            list.add(mParam.sessionListRec);
            for (ISessionListener lis : sessionListenerList) {
                lis.onSessionChanged(list);
            }
        }
    }

}
