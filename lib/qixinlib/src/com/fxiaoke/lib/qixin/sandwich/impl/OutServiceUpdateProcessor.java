package com.fxiaoke.lib.qixin.sandwich.impl;

import android.content.Context;

import com.facishare.fs.pluginapi.session_command.IServiceChangeListener;
import com.fxiaoke.dataimpl.msg.SingletonObjectHolder;
import com.fxiaoke.fxdblib.beans.SessionSandwich;
import com.fxiaoke.fxdblib.beans.SessionSumary;
import com.fxiaoke.lib.qixin.sandwich.ISandwitchProcessor;

import java.util.List;

/**
 * Created by anjx on 2016/12/14.
 */

public class OutServiceUpdateProcessor implements ISandwitchProcessor {

    @Override
    public void process(Context ctx, SessionSandwich ssCommond, SessionSumary sessionSumary) {
        long lastUpdateTime = sessionSumary.getLasttime() ;
        if (lastUpdateTime == 0 || lastUpdateTime < ssCommond.getUpdateTimeStamp()){

            List<Object> lisObjects = SingletonObjectHolder.getInstance()
                    .findObjects(
                            IServiceChangeListener.class);
            for (Object ob : lisObjects) {
                IServiceChangeListener lis =
                        (IServiceChangeListener) ob;
                lis.onOutServiceChange();
            }
        }
        sessionSumary.setLasttime(ssCommond.getUpdateTimeStamp());
    }
}
