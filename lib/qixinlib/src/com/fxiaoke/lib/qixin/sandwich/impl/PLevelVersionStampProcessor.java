package com.fxiaoke.lib.qixin.sandwich.impl;

import com.facishare.fs.contacts_fs.datactrl.IContactSynchronizer;
import com.facishare.fs.context.FSContextManager;
import com.fxiaoke.fxdblib.beans.SessionSandwich;
import com.fxiaoke.fxdblib.beans.SessionSumary;
import com.fxiaoke.fxlog.DebugEvent;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.lib.qixin.sandwich.ISandwitchProcessor;

import android.content.Context;

/**
 * Created by anjx on 2016/12/14.
 */

public class PLevelVersionStampProcessor implements ISandwitchProcessor {
    private static final DebugEvent TAG = new DebugEvent(PLevelVersionStampProcessor.class.getSimpleName());

    @Override
    public void process(Context ctx, SessionSandwich ssCommond, SessionSumary sessionSumary) {
        if(FSContextManager.getCurUserContext().getAccount().isVisitorLogin()){//无租户下不能调用
            return;
        }
        long localTimeStamp = FSContextManager.getCurUserContext().getContactSynchronizer()
                .getTimestamp(IContactSynchronizer.KEY_PLEVEL_STAMP);
        // anjx 180117 不再使用content，而使用统一的updatetimestamp
        long serverTimeStamp = ssCommond.getUpdateTimeStamp();
        FCLog.i(TAG, "ss_P VStamp LocalTS = " + localTimeStamp + " ServerTS = "
                + serverTimeStamp);
        if (localTimeStamp < serverTimeStamp) {
            FCLog.i(TAG, "ss_P VStamp Has Changed");
            FSContextManager.getCurUserContext().getContactSynchronizer().requestPLevelData(ctx, null, false);
        }
    }
}
