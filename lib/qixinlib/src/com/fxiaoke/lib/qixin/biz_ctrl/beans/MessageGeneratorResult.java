package com.fxiaoke.lib.qixin.biz_ctrl.beans;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.fs.NoProguard;

/**
 * Created by yangwg on 2018/5/3.
 */
@NoProguard
public class MessageGeneratorResult {
    @J<PERSON><PERSON>ield(name = "M1")
    public String messageContent;
    @JSO<PERSON>ield(name = "M2")
    public String messageType;
    @JSONField(name = "M3")
    public String objectId;
    @JSONCreator
    public MessageGeneratorResult(@J<PERSON><PERSON>ield(name = "M1") String messageContent,
                                  @JSONField(name = "M2") String messageType,
                                  @JSO<PERSON>ield(name = "M3") String objectId) {
        this.messageContent = messageContent;
        this.messageType = messageType;
        this.objectId = objectId;
    }
}
