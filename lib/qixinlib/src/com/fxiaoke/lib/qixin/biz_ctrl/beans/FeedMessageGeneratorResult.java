package com.fxiaoke.lib.qixin.biz_ctrl.beans;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.fs.NoProguard;

/**
 * Created by yangwg on 2019/11/6.
 */
@NoProguard
public class FeedMessageGeneratorResult {
    @J<PERSON><PERSON>ield(name = "M1")
    public String messageContent;
    @JSONField(name = "M2")
    public String messageType;
    @JSONField(name = "M3")
    public String objectId;
    @JSONCreator
    public FeedMessageGeneratorResult(@JSONField(name = "M1") String messageContent,
                                      @<PERSON>SO<PERSON>ield(name = "M2") String messageType,
                                      @JSO<PERSON><PERSON>(name = "M3") String objectId) {
        this.messageContent = messageContent;
        this.messageType = messageType;
        this.objectId = objectId;
    }
}
