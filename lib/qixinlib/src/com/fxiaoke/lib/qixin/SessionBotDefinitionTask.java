package com.fxiaoke.lib.qixin;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.TypeReference;
import com.fs.fsprobuf.ServerProtobuf;
import com.fxiaoke.dataimpl.msg.ITaskListener;
import com.fxiaoke.fscommon.queue.FSTaskPriorityQueue;
import com.fxiaoke.fscommon.queue.IFSTask;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiParameterList;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fshttp.web.http.WebApiUtils;
import com.fxiaoke.fxdblib.beans.sessiondefine.SessionBotDefinition;
import com.fxiaoke.fxlog.DebugEvent;
import com.fxiaoke.fxlog.FCLog;
import com.fxiaoke.lib.qixin.biz_ctrl.beans.GetBotDefinitionsResult;

import android.content.Context;

/**
 * Created by yangwg on 2017/6/16.
 */

public class SessionBotDefinitionTask extends IFSTask {
    protected DebugEvent TAG = FSTaskPriorityQueue.TAG;
    protected Context mContext;
    ServerProtobuf.EnterpriseEnv mEnv;
    String SUB_TAG = "BotDef";
    ITaskListener mInitTaskListener = null;
    List<ITaskListener> mTaskLisList = new ArrayList<ITaskListener>();
    boolean isForceUpdateAllBotDef = false;//是否强制更新所有的botdef数据

    public void setForceUpdateAllBotDef(boolean isForceUpdate) {
        this.isForceUpdateAllBotDef = isForceUpdate;
    }
    public SessionBotDefinitionTask(Context context, ServerProtobuf.EnterpriseEnv env,
                                    FSTaskPriority taskPriority, ITaskListener taskLis) {
        mContext = context;
        mEnv = env;
        setTaskPriority(taskPriority);
        //都是调用同一个请求,请求同样的数据，所以key报错一样，可以减少一定程度的重复请求
        setTaskIdentify(SessionBotDefinition.Key_Session_bot_definition_time);
        if (taskLis != null) {
            mInitTaskListener = taskLis;
            mTaskLisList.add(taskLis);
            FCLog.d(TAG, SUB_TAG, "SessionBotDefinitionTask mTaskLisList.size: " + mTaskLisList.size()
                    + " ,lis: " + taskLis.hashCode());
        }
    }

    public ITaskListener getInitTaskListener() {
        return mInitTaskListener;
    }

    @Override
    public void updateTaskPriority(IFSTask inputTask) {
        super.updateTaskPriority(inputTask);
        if (inputTask instanceof SessionBotDefinitionTask) {
            ITaskListener lis = ((SessionBotDefinitionTask) inputTask).getInitTaskListener();
            if (lis != null) {
                mTaskLisList.add(lis);
                FCLog.d(TAG, SUB_TAG, "updateTaskPriority mTaskLisList.size: " + mTaskLisList.size());
            }
        }
    }

    @Override
    public void run() {
        long localBotDefinitionsTimeStamp = isForceUpdateAllBotDef ? 0 : SessionBotDefinitionQueueCtr.getLocalBotDefinitionsTimeStamp(mContext);
        FCLog.i(TAG, SUB_TAG, "SessionBotDefinitionTask begin to exe... isForceUpdateAllBotDef：" + isForceUpdateAllBotDef + ", localBotDefinitionsTimeStamp: " + localBotDefinitionsTimeStamp);
        getBotDefinitions(localBotDefinitionsTimeStamp, new WebApiExecutionCallback<GetBotDefinitionsResult>() {
            @Override
            public TypeReference<WebApiResponse<GetBotDefinitionsResult>> getTypeReference() {
                return new TypeReference<WebApiResponse<GetBotDefinitionsResult>>(){};
            }

            @Override
            public Class<GetBotDefinitionsResult> getTypeReferenceFHE() {
                return GetBotDefinitionsResult.class;
            }

            @Override
            public void completed(Date date, final GetBotDefinitionsResult result) {
                SessionBotDefinitionQueueCtr.getInstance().scheduleTask(new Runnable() {
                    @Override
                    public void run() {
                        if (result != null && result.botDefinitionsResult != null
                                && result.botDefinitionsResult.size() > 0) {
                            boolean ret = SessionBotDefinitionQueueCtr.processCompletedData(mContext,
                                    result.botDefinitionsResult, result.botDefinitionsTimeStamp);
                            if (ret) {//通知成功
                                FCLog.d(TAG, SUB_TAG,"SessionBotDefinitionTask complete success");
                                for (ITaskListener lis : mTaskLisList) {
                                    lis.onSuccess(result);
                                }
                            } else {//通知失败
                                FCLog.i(TAG, SUB_TAG,
                                        "SessionBotDefinitionTask complete but process local Bot Data failed");
                                for (ITaskListener lis : mTaskLisList) {
                                    lis.onFailed("process local Bot Data failed");
                                }
                            }
                        } else {
                            FCLog.i(TAG, SUB_TAG, "SessionBotDefinitionTask complete server return null");
                            for (ITaskListener lis : mTaskLisList) {
                                lis.onFailed("server return null ");
                            }
                        }
                        runComplete();
                    }
                });
            }

                @Override
                public void failed (WebApiFailureType failureType, int httpStatusCode, final String error){
                    super.failed(failureType, httpStatusCode, error);
                    SessionBotDefinitionQueueCtr.getInstance().scheduleTask(new Runnable() {
                        @Override
                        public void run() {
                            for (ITaskListener lis : mTaskLisList) {
                                lis.onFailed(error);
                            }
                            runComplete();
                        }
                    });
                }
            });
    }



    private void getBotDefinitions(final long localBotDefinitionsTimeStamp ,
                                   final WebApiExecutionCallback<GetBotDefinitionsResult>
            callback) {
        WebApiParameterList param = WebApiParameterList.createWith("M1", localBotDefinitionsTimeStamp );
        WebApiUtils.postAsync("FHE/EM1AQIXINBOTMANAGE/Bots", "GetBotDefinitions", param, callback);
    }
}