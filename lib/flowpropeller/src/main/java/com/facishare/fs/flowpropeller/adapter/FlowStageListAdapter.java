/*
 * Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.flowpropeller.adapter;

import java.util.HashMap;
import java.util.List;

import com.facishare.fs.common_utils.FSScreen;
import com.facishare.fs.flowpropeller.R;
import com.facishare.fs.flowpropeller.beans.CommonButtonBean;
import com.facishare.fs.flowpropeller.beans.FlowStageFlag;
import com.facishare.fs.flowpropeller.beans.FlowStageInstanceInfo;
import com.facishare.fs.flowpropeller.beans.SimpleTasksBean;
import com.facishare.fs.flowpropeller.beans.StageTaskListType;
import com.facishare.fs.flowpropeller.views.StageTaskListView;
import com.facishare.fs.i18n.I18NHelper;
import com.facishare.fs.sizectrlviews.SizeControlTextView;
import com.fxiaoke.cmviews.BaseListAdapter;
import com.fxiaoke.plugin.crm.CrmModelViewUtils;
import com.lidroid.xutils.util.FSViewUtils;

import android.content.Context;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

/**
 * Author:  wangrz
 * Date:    2018/8/15 11:41
 * Remarks: 任务列表
 */
public class FlowStageListAdapter
        extends BaseListAdapter<FlowStageInstanceInfo.StagesBean, FlowStageListAdapter.ViewHolder> {
    private int mSelectedItem = -100;
    private OnFlowStageClickListener mOnFlowStageClickListener;
    private HashMap<Integer, Boolean> selectedMap = new HashMap<>();
    //是否展示赢率
    private boolean isShowWinRate;
    //刷新处理人 button
    private CommonButtonBean mRefreshHandlerButton;

    public FlowStageListAdapter(Context context) {
        super(context);
    }

    @Override
    public void setDataList(List<FlowStageInstanceInfo.StagesBean> stagesBeans) {
        super.setDataList(stagesBeans);
        initSelectedMap(stagesBeans);
    }

    @Override
    public void updateDataList(List<FlowStageInstanceInfo.StagesBean> stagesBeans) {
        super.updateDataList(stagesBeans);
        initSelectedMap(stagesBeans);
    }

    private void initSelectedMap(List<FlowStageInstanceInfo.StagesBean> stagesBeans) {
        selectedMap.clear();
        if (stagesBeans == null || stagesBeans.size() == 0) {
            return;
        }
        for (int i = 0; i < stagesBeans.size(); i++) {
            selectedMap.put(i, false);
        }
    }

    public void setRefreshHandlerButton(CommonButtonBean button){
        mRefreshHandlerButton = button;
    }

    /**
     * 是否展示赢率
     * @param isShowWinRate
     */
    public void isShowWinRate(boolean isShowWinRate){
        this.isShowWinRate = isShowWinRate;
    }




    /**
     * 设置选中 item 的位置
     *
     * @param pos
     */
    public void setSelectedItem(int pos) {
        this.mSelectedItem = pos;
        for (int i = 0; i < selectedMap.size(); i++) {
            if (pos == i) {
                selectedMap.put(pos, !selectedMap.get(pos));
            }else {
                selectedMap.put(i, false);
            }
        }
        notifyDataSetInvalidated();
    }

    @Override
    protected View createConvertView(Context context, int position, FlowStageInstanceInfo.StagesBean info) {
        LayoutInflater inflater = LayoutInflater.from(context);
        return inflater.inflate(R.layout.layout_flow_stage_item, null);
    }

    @Override
    protected void updateView(ViewHolder holder, int position, FlowStageInstanceInfo.StagesBean info) {

        //设置第一个 item 的 指示线
        if (position == 0) {
            holder.line1.setVisibility(View.INVISIBLE);
        } else {
            holder.line1.setVisibility(View.VISIBLE);
        }
        //设置最后一个 item 的 指示线、距离底部间距
        if (position == mDataList.size() - 1) {
            holder.line2.setVisibility(View.INVISIBLE);
            setItemBottom(holder.stageCard, 20f);
        } else {
            holder.line2.setVisibility(View.VISIBLE);
            setItemBottom(holder.stageCard, 0f);
        }
        //阶段名字
        holder.mTvStageName.setText(info.getName());
        //是否展示刷新处理人按钮: server 下发有刷新按钮，且当前阶段，且非终结态
        if (mRefreshHandlerButton != null && info.isIsCurrent() && !info.isIsTerminal()) {
            holder.mTvRefreshTaskHandler.setText(mRefreshHandlerButton.getLabel());
            handleRefreshHandlerView(holder.mTvRefreshTaskHandler, info);
            holder.mTvRefreshTaskHandler.setVisibility(View.VISIBLE);
        } else {
            holder.mTvRefreshTaskHandler.setVisibility(View.GONE);
        }
        holder.mTvRefreshTaskHandler.post(new Runnable() {
            @Override
            public void run() {
                boolean isShow = holder.mTvRefreshTaskHandler.getVisibility() == View.VISIBLE;
                FSViewUtils.addViewTouchRangeDp( holder.mTvRefreshTaskHandler, isShow ? 5 : 0,
                        isShow ? 5: 0, isShow ? 10: 0, isShow ? 10 : 0);
            }
        });
        //获取扩展信息中的数据
        FlowStageFlag stageFlag;
        if(info.getExtension() != null) {
            stageFlag = info.getExtension().getStageFlag();
            if (isShowWinRate ) {
                String probability = info.getExtension().getProbability();
                if (TextUtils.isEmpty(probability)) {
                    probability = "0";
                }
                String winRate = TextUtils.isEmpty(info.getExtension().getProbabilityLabel()) ?
                        I18NHelper.getFormatText("crm.views.SaleActionRender.v1.1379"/* 赢率  */, probability)
                        : I18NHelper.getFormatText("crm.flow.stage.probability"/* {0} {1}%  */,
                                info.getExtension().getProbabilityLabel(), probability);
                holder.mTvWinRate.setVisibility(View.VISIBLE);
                holder.mTvWinRate.setText(winRate);//当前销售阶段赢率
            } else {
                holder.mTvWinRate.setVisibility(View.GONE);
            }
        }else {
            stageFlag = FlowStageFlag.UNKNOW;
        }

        holder.mTvStageStatus.setVisibility(View.GONE);
        //暂时不需要了：是否是终结态类型阶段
//        if (info.isIsTerminal()) {
//            holder.mTvStageStatus.setVisibility(View.INVISIBLE);
//        } else {
//            holder.mTvStageStatus.setVisibility(View.VISIBLE);
//        }

        //是否展示阶段展开指示箭头
        if (info.getTasks() == null || info.getTasks().size() == 0) {
            holder.mIvArrow.setVisibility(View.INVISIBLE);
        }else {
            holder.mIvArrow.setVisibility(View.VISIBLE);
        }

        //设置当前任务列表
        addTaskView(holder, info);
        handleSelectedItem(holder, position, info);

        //目前暂时用 isIsCurrent、isIsTerminal 来判断是否终结态，如果终结状态（赢单、无效、输单）, 传入的数据源会去掉其它两个终态阶段
        if (info.isIsCurrent() && info.isIsTerminal()) {
            switch (stageFlag) {
                case WIN://赢单
                    holder.stageCard.setBackgroundResource(R.drawable.shape_flow_stage_item_win_bg);
                    holder.mIvStatus.setImageResource(R.drawable.icon_flow_stage_status_complete);
                    break;
                case FAILED://输单
                    holder.stageCard.setBackgroundResource(R.drawable.shape_flow_stage_item_faild_bg);
                    holder.mIvStatus.setImageResource(R.drawable.icon_flow_stage_status_faild);
                    break;
                case VOID://无效
                    holder.stageCard.setBackgroundResource(R.drawable.shape_flow_stage_item_void_bg);
                    holder.mIvStatus.setImageResource(R.drawable.icon_flow_stage_status_void);
                    break;
                default://默认
                    holder.stageCard.setBackgroundResource(R.drawable.shape_flow_stage_item_void_bg);
                    holder.mIvStatus.setImageResource(R.drawable.icon_flow_stage_status_complete);
                    break;
            }
        }else {
            //设置阶段进行状态
            switch (info.getStageState()) {
                case UNSTART://未开始
                    holder.mIvStatus.setImageResource(R.drawable.icon_flow_stage_status_unstart);
                    break;
                case INPROGRESS://进行中
                    holder.mIvStatus.setImageResource(R.drawable.icon_flow_stage_status_progress);
                    break;
                case PASS://完成
                    holder.mIvStatus.setImageResource(R.drawable.icon_flow_stage_status_complete);
                    break;
                case UNCOMPLETED://未完成
                    holder.mIvStatus.setImageResource(R.drawable.icon_flow_stage_status_uncomplete);
                    break;
                case SKIPPED://跳过
                case CANCEL://取消
                case ERROR://异常
                default://未知
                    holder.mIvStatus.setImageResource(R.drawable.icon_flow_stage_status_unstart);
                    break;
            }
            //阶段状态
            holder.mTvStageStatus.setText(info.getStageState().des);
            //普通阶段 card 颜色
            holder.stageCard.setBackgroundResource(R.drawable.shape_flow_stage_item_normal_bg);
        }
    }

    @Override
    protected ViewHolder createHolder(View view, int i, FlowStageInstanceInfo.StagesBean info) {
        ViewHolder holder = new ViewHolder();
        holder.mIvStatus = (ImageView) view.findViewById(R.id.iv_status);
        holder.mTvStageName = (SizeControlTextView) view.findViewById(R.id.tv_stage_name);
        holder.mTvRefreshTaskHandler = (SizeControlTextView) view.findViewById(R.id.tv_refresh_task_handler);
        holder.mTvWinRate = (SizeControlTextView) view.findViewById(R.id.tv_win_rate);
        holder.mTvStageStatus = (SizeControlTextView) view.findViewById(R.id.tv_stage_status);
        holder.mIvArrow = (ImageView) view.findViewById(R.id.iv_arrow);
        holder.mCardLine = (View) view.findViewById(R.id.cardLine);
        holder.mStageTaskListView = (StageTaskListView) view.findViewById(R.id.stage_task_listView);
        holder.line1 = view.findViewById(R.id.line1);
        holder.line2 = view.findViewById(R.id.line2);
        holder.stageCard = view.findViewById(R.id.stageCard);
        holder.mStageTaskListView.setTaskListType(StageTaskListType.ALL_TASK_LIST);
        if (isShowWinRate) {
            holder.mTvWinRate.setVisibility(View.VISIBLE);
        } else {
            holder.mTvWinRate.setVisibility(View.GONE);
        }
        View ll_bg = view.findViewById(R.id.ll_bg);
        //内容展示区（标题和副标题）的高度
        ll_bg.post(new Runnable() {
            @Override
            public void run() {
                int contentHeight = ll_bg.getHeight();
                //状态 icon 高度
                int ivStatusHeight = FSScreen.dip2px(16);
                ViewGroup.LayoutParams lineLP = holder.line1.getLayoutParams();
                //card 距离顶部间隙 marginTop:20，card paddingTop: 10
                lineLP.height = FSScreen.dip2px(30) + (contentHeight / 2 - ivStatusHeight / 2);
                holder.line1.setLayoutParams(lineLP);
            }
        });

        return holder;
    }

    private void handleRefreshHandlerView(TextView mTvStageRefresh,
                                          FlowStageInstanceInfo.StagesBean info) {
        mTvStageRefresh.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnFlowStageClickListener == null) {
                    return;
                }
                mOnFlowStageClickListener.onRefreshTaskHandler(info);
            }
        });
    }


    private void addTaskView(ViewHolder holder, final FlowStageInstanceInfo.StagesBean info) {
        holder.mStageTaskListView.updateList(mContext, info);
        holder.mStageTaskListView.setOnItemClickListener(new StageTaskListView.OnTaskItemClickListener() {
            @Override
            public void onItemClick(SimpleTasksBean obj) {
                if (mOnFlowStageClickListener == null) {
                    return;
                }
                mOnFlowStageClickListener.onTaskItemClick(obj, info);
            }
        });
    }

    /**
     * 处理选中的 item
     *
     * @param holder
     * @param pos
     * @param info
     */
    protected void handleSelectedItem(ViewHolder holder, int pos,
                                      FlowStageInstanceInfo.StagesBean info) {
        if (info.getTasks() == null || info.getTasks().size() == 0) {
            holder.mStageTaskListView.setVisibility(View.GONE);
            holder.mCardLine.setVisibility(View.GONE);
            return;
        }

        if (pos == mSelectedItem) {
            if(selectedMap.get(pos)) {
                holder.mIvArrow.animate().setDuration(150).rotation(180).start();
            }else {
                //旋转0度是复位
                holder.mIvArrow.animate().setDuration(150).rotation(0).start();
            }
            holder.mStageTaskListView.setVisibility(selectedMap.get(pos) ? View.VISIBLE : View.GONE);
            holder.mCardLine.setVisibility(selectedMap.get(pos) ? View.VISIBLE : View.GONE);
        } else {
            //旋转0度是复位
            holder.mIvArrow.animate().setDuration(0).rotation(0).start();
            holder.mStageTaskListView.setVisibility(View.GONE);
            holder.mCardLine.setVisibility(View.GONE);
        }
    }

    /**
     * 设置item 距离底部距离
     *
     * @param view
     * @param value
     */
    private void setItemBottom(View view, float value) {
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) view.getLayoutParams();
        layoutParams.bottomMargin = (int) TypedValue
                .applyDimension(TypedValue.COMPLEX_UNIT_DIP, value, mContext.getResources().getDisplayMetrics());
        view.setLayoutParams(layoutParams);
    }


    static class ViewHolder {
        View view;
        ImageView mIvStatus;
        TextView mTvNumberCenter;
        RelativeLayout mLayoutStatus;
        TextView mTvStageName;
        TextView mTvRefreshTaskHandler;
        TextView mTvWinRate;
        TextView mTvStageStatus;
        ImageView mIvArrow;
        LinearLayout stageCard;
        StageTaskListView mStageTaskListView;
        View mCardLine;
        View line1;
        View line2;
    }

    public void setOnFlowStageClickListener(OnFlowStageClickListener listener) {
        mOnFlowStageClickListener = listener;
    }

    public interface OnFlowStageClickListener {
        /**
         * 刷新任务处理人
         * @param stagesBean
         */
        void onRefreshTaskHandler(FlowStageInstanceInfo.StagesBean stagesBean);

        /**
         * 任务点击
         * @param tasksBean
         * @param stagesBean
         */
        void onTaskItemClick(SimpleTasksBean tasksBean, FlowStageInstanceInfo.StagesBean stagesBean);
    }
}
