/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.pluginapi.msg.beans;

/**
 * Created by yangwg on 2016/10/19.
 */
public class SessionRemindEvent {
    String sessionId = "";
    String sessionSubCate = "";
    Object extendObject = null;
    public SessionRemindEvent(String sessionId,String sessionSubCate){
        this.sessionId = sessionId;
        this.sessionSubCate = sessionSubCate;
    }
    public SessionRemindEvent(String sessionId,String sessionSubCate,Object obj){
        this.sessionId = sessionId;
        this.sessionSubCate = sessionSubCate;
        this.extendObject = obj;
    }
    public String getSessionId() {
        return sessionId;
    }

    public String getSessionSubCate() {
        return sessionSubCate;
    }

    public Object getExtendObject() {
        return extendObject;
    }
}
