/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.pluginapi.crm.translate.translator;

import com.facishare.fs.pluginapi.crm.beans.EmployeeSolidInfo;
import com.facishare.fs.pluginapi.crm.beans.MarketingEventInfo;
import com.facishare.fs.pluginapi.crm.beans.SelectObjFieldItem;
import com.facishare.fs.pluginapi.crm.beans.SelectObjectBean;
import com.facishare.fs.pluginapi.crm.translate.ITranslate;
import com.facishare.fs.pluginapi.crm.type.CoreObjType;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by liuyu on 2016/12/20.
 */
public class MarketingEventTranslator implements ITranslate<MarketingEventInfo>{

    public static final String FIELD_NAME = "Name";
    public static final String FIELD_EVENT_TYPE = "EventType";
    public static final String FIELD_BEGIN_DATE = "BeginDate";
    public static final String FIELD_END_DATE = "EndDate";
    public static final String FIELD_OWNER_NAME = "OwnerName";

    @Override
    public boolean accept(CoreObjType objectType) {
        return CoreObjType.MarketingEvent == objectType;
    }

    @Override
    public MarketingEventInfo generalObj2BizObj(SelectObjectBean selectObjectBean) {
        MarketingEventInfo marketingEventInfo = null;
        if (selectObjectBean != null && selectObjectBean.mType == CoreObjType.MarketingEvent) {
            marketingEventInfo = new MarketingEventInfo();
            LinkedHashMap<String, SelectObjFieldItem> fields = selectObjectBean.gainExpandedField();
            marketingEventInfo.setMarketingEventID(selectObjectBean.mId);
            SelectObjFieldItem name = fields.get(FIELD_NAME);
            SelectObjFieldItem eventType = fields.get(FIELD_EVENT_TYPE);
            SelectObjFieldItem beginDate = fields.get(FIELD_BEGIN_DATE);
            SelectObjFieldItem endDate = fields.get(FIELD_END_DATE);
            SelectObjFieldItem owner = fields.get(FIELD_OWNER_NAME);
            if (name != null) {
                marketingEventInfo.setName(name.fieldValue);
                marketingEventInfo.setShowName(name.fieldDisplayValue);
            }
            if (eventType != null) {
                marketingEventInfo.setEventType(eventType.fieldValue);
                marketingEventInfo.setEventTypeName(eventType.fieldDisplayValue);
            }
            if (beginDate != null) {
                try {
                    marketingEventInfo.setBeginDate(Long.decode(beginDate.fieldValue));
                } catch (NumberFormatException e) {
                    marketingEventInfo.setBeginDate(-1);
                }
            }
            if (endDate != null) {
                try {
                    marketingEventInfo.setEndDate(Long.decode(endDate.fieldValue));
                } catch (NumberFormatException e) {
                    marketingEventInfo.setEndDate(-1);
                }
            }
            if (owner != null) {
                EmployeeSolidInfo ownerInfo = new EmployeeSolidInfo();
                try {
                    ownerInfo.employeeID = Integer.decode(owner.fieldValue);
                    marketingEventInfo.setOwnerID(Integer.decode(owner.fieldValue));
                } catch (NumberFormatException e) {
                    ownerInfo.employeeID = -1;
                }
                ownerInfo.name = owner.fieldDisplayValue;
                marketingEventInfo.setOwner(ownerInfo);
            }
        }
        return marketingEventInfo;
    }

    @Override
    public SelectObjectBean bizObj2GeneralObj(MarketingEventInfo marketingEventInfo) {
        if (marketingEventInfo == null) {
            return null;
        }
        List<SelectObjFieldItem> fields = new ArrayList<>();
        fields.add(new SelectObjFieldItem(CoreObjType.MarketingEvent.value, FIELD_NAME, marketingEventInfo.getShowName(), marketingEventInfo.getName()));
        fields.add(new SelectObjFieldItem(CoreObjType.MarketingEvent.value, FIELD_EVENT_TYPE, marketingEventInfo.getShowEventTypeName(), marketingEventInfo.getEventType()));
        fields.add(new SelectObjFieldItem(CoreObjType.MarketingEvent.value, FIELD_BEGIN_DATE, String.valueOf(marketingEventInfo.getBeginDate()), String.valueOf(marketingEventInfo.getEndDate())));
        String ownerName = marketingEventInfo.getOwner() != null ? marketingEventInfo.getOwner().name : "";
        fields.add(new SelectObjFieldItem(CoreObjType.MarketingEvent.value, FIELD_OWNER_NAME, ownerName, ownerName));
        return SelectObjectBean.createExpandedBeanByFieldList(marketingEventInfo.getMarketingEventID(), CoreObjType.MarketingEvent.value, fields, 0);
    }
}
