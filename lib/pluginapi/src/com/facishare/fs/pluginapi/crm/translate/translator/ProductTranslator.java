/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.pluginapi.crm.translate.translator;

import com.facishare.fs.pluginapi.crm.beans.SelectObjFieldItem;
import com.facishare.fs.pluginapi.crm.beans.SelectObjectBean;
import com.facishare.fs.pluginapi.crm.fieldauthority.FieldAuthUtils;
import com.facishare.fs.pluginapi.crm.type.CoreObjType;
import com.facishare.fs.pluginapi.crm.type.CoreObjType;
import com.facishare.fs.pluginapi.crm.beans.ProductInfo;
import com.facishare.fs.pluginapi.crm.translate.ITranslate;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by liuyu on 2016/12/20.
 */
public class ProductTranslator implements ITranslate<ProductInfo>{

    public static final String FIELD_NAME = "Name";
    public static final String FIELD_PRICE = "Price";
    public static final String FIELD_UNIT = "Unit";//产品价格的单位
    public static final String FIELD_CATEGORY = "Category";

    @Override
    public boolean accept(CoreObjType objectType) {
        return CoreObjType.Product == objectType;
    }

    @Override
    public ProductInfo generalObj2BizObj(SelectObjectBean selectObjectBean) {
        ProductInfo productInfo = null;
        if (selectObjectBean != null && selectObjectBean.mType == CoreObjType.Product) {
            LinkedHashMap<String, SelectObjFieldItem> fields = selectObjectBean.gainExpandedField();
            productInfo = new ProductInfo();
            productInfo.productID = selectObjectBean.mId;
            SelectObjFieldItem name = fields.get(FIELD_NAME);
            if (name != null) {
                productInfo.name = name.fieldValue;
                productInfo.mShowName = name.fieldDisplayValue;
            }
            SelectObjFieldItem price = fields.get(FIELD_PRICE);
            if(price != null){
                productInfo.price = price.fieldValue;
                productInfo.mShowPrice = price.fieldDisplayValue;
            }
            SelectObjFieldItem unit = fields.get(FIELD_UNIT);
            if(price != null){
                productInfo.unit = unit.fieldValue;
                productInfo.mShowUnit = unit.fieldDisplayValue;
            }
            SelectObjFieldItem category = fields.get(FIELD_CATEGORY);
            if (category != null) {
                productInfo.category = category.fieldValue;
                productInfo.mShowCategory = category.fieldDisplayValue;
            }
        }
        return productInfo;
    }

    @Override
    public SelectObjectBean bizObj2GeneralObj(ProductInfo productInfo) {
        if (productInfo == null) {
            return null;
        }
        List<SelectObjFieldItem> fields = new ArrayList<>();
        fields.add(new SelectObjFieldItem(CoreObjType.Product.value, FIELD_NAME, productInfo.mShowName, productInfo.name));
        fields.add(new SelectObjFieldItem(CoreObjType.Product.value, FIELD_PRICE, productInfo.mShowPrice, productInfo.price));
        fields.add(new SelectObjFieldItem(CoreObjType.Product.value, FIELD_UNIT, productInfo.mShowUnit, productInfo.unit));
        fields.add(new SelectObjFieldItem(CoreObjType.Product.value, FIELD_CATEGORY, productInfo.mShowCategory, productInfo.category));
        return SelectObjectBean.createExpandedBeanByFieldList(productInfo.productID, CoreObjType.Product.value, fields, 0);
    }
}
