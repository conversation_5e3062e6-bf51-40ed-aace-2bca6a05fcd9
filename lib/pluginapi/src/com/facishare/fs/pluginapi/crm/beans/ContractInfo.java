package com.facishare.fs.pluginapi.crm.beans;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.fs.pluginapi.crm.customfieldannotations.ObjField;
import com.facishare.fs.pluginapi.crm.customfieldannotations.ObjFieldType;
import com.facishare.fs.pluginapi.crm.customfieldannotations.ObjsField;
import com.facishare.fs.pluginapi.crm.fieldauthority.FieldAuthUtils;
import com.facishare.fs.pluginapi.crm.type.CoreObjType;

public class ContractInfo implements ICrmBizDesc{
    private static final long serialVersionUID = 8602677949163323902L;
    /**	* 客户名称	*/
    @JSONField(name="M7")
    @ObjsField(objFields = {
            @ObjField(targetFieldName = "account_id", fieldType = ObjFieldType.CONTENT, editable = false)})
    public String customerName;
    //权限字段
    public String mShowCustomerName;
    /**	* 成交日期	*/
    @JSONField(name="M8")
    public long tradeTime;
    /**	* 成交金额	*/
    @JSONField(name="M9")
    public String tradeMoney;
    /**	* 创建时间	*/
    @JSONField(name="M10")
    public long createTime;
    /**	*  提交时间	*/
    @JSONField(name="M11")
    public long submitTime;
    /**	*  创建人	*/
    @JSONField(name="M12")
    public String creatorName;
    /**	* 合同金额	*/
    @JSONField(name="M13")
    public String contractMoney;
    //权限字段
    public String mShowContractMoney;
    /**	* 负责人	*/
    @JSONField(name="M14")
    public int ownerID;
    /**	* 状态	*/
    @JSONField(name="M15")
    public int status;
    /**	* 是否已删除	*/
    @JSONField(name="M16")
    public boolean isDeleted;
    /**	* 	*/
    @JSONField(name="M17")
    @ObjsField(objFields = {
            @ObjField(targetFieldName = "account_id", fieldType = ObjFieldType.ID, editable = false)})
    public String customerID;
    /**	* 销售订单编号	*/
    @JSONField(name="M18")
    public String tradeCode;
    @JSONField(name="M19")
    public String ownerName;
    /**	* 合同ID	*/
    @JSONField(name="M1")
    @ObjsField(objFields = {
            @ObjField(targetFieldName = "ContractID", fieldType = ObjFieldType.ID, editable = false),
            @ObjField(targetFieldName = "contract_id", fieldType = ObjFieldType.ID, editable = false)})
    public String contractID;
    /**	* 客户成交ID	*/
    @JSONField(name="M2")
    public String customerTradeID;
    /**	* 合同编号	*/
    @JSONField(name="M3")
    public String contractNo;
    //权限字段
    public String mShowContractNo;
    /**	* 合同标题	*/
    @JSONField(name="M4")
    @ObjsField(objFields = {
            @ObjField(targetFieldName = "ContractID", fieldType = ObjFieldType.CONTENT, editable = false),
            @ObjField(targetFieldName = "contract_id", fieldType = ObjFieldType.CONTENT, editable = false)})
    public String title;
    //权限字段
    public String mShowTitle;
    /**	* 开始日期	*/
    @JSONField(name="M5")
    public long startTime;
    /**	* 截止日期	*/
    @JSONField(name="M6")
    public long endTime;

    /**	* 创建人ID	*/
    @JSONField(name="M21")
    public int creatorID;
    /**
     * 	[6.0.0Added]是否锁定
     * 	0-未锁定 1-锁定
     */
    @JSONField(name="M22")
    public int mIsLocked;

    public ContractInfo() {
        mShowContractMoney = "";
        mShowCustomerName = "";
        mShowTitle = "";
        mShowContractNo = "";
    }

    @JSONCreator
    public ContractInfo(@JSONField(name="M7") String customerName,
                        @JSONField(name="M8") long tradeTime,
                        @JSONField(name="M9") String tradeMoney,
                        @JSONField(name="M10") long createTime,
                        @JSONField(name="M11") long submitTime,
                        @JSONField(name="M12") String creatorName,
                        @JSONField(name="M13") String contractMoney,
                        @JSONField(name="M14") int ownerID,
                        @JSONField(name="M15") int status,
                        @JSONField(name="M16") boolean isDeleted,
                        @JSONField(name="M17") String customerID,
                        @JSONField(name="M18") String tradeCode,
                        @JSONField(name="M19") String ownerName,
                        @JSONField(name="M1") String contractID,
                        @JSONField(name="M2") String customerTradeID,
                        @JSONField(name="M3") String contractNo,
                        @JSONField(name="M4") String title,
                        @JSONField(name="M5") long startTime,
                        @JSONField(name="M6") long endTime,
                        @JSONField(name="M21") int creatorID,
                        @JSONField(name="M22") int isLocked) {
        this.customerName = customerName;
        this.mShowCustomerName = FieldAuthUtils.getShowStr(customerName);
        this.tradeTime = tradeTime;
        this.tradeMoney = tradeMoney;
        this.createTime = createTime;
        this.submitTime = submitTime;
        this.creatorName = creatorName;
        this.contractMoney = contractMoney;
        this.mShowContractMoney = FieldAuthUtils.getShowStr(contractMoney);
        this.ownerID = ownerID;
        this.status = status;
        this.isDeleted = isDeleted;
        this.tradeCode = tradeCode;
        this.ownerName = ownerName;
        this.customerID = customerID;
        this.contractID = contractID;
        this.customerTradeID = customerTradeID;
        this.contractNo = contractNo;
        this.mShowContractNo = FieldAuthUtils.getShowStr(contractNo);
        this.title = title;
        this.mShowTitle = FieldAuthUtils.getShowStr(title);
        this.startTime = startTime;
        this.endTime = endTime;
        this.creatorID = creatorID;
        this.mIsLocked = isLocked;
    }

    @Override
    public CoreObjType objType() {
        return CoreObjType.Contract;
    }

    @Override
    public String mainField() {
        return contractNo;
    }

    @Override
    public String showMainField() {
        return mShowContractNo;
    }

    @Override
    public String id() {
        return contractID;
    }

    @Override
    public String customerId() {
        return customerID;
    }

    @Override
    public String customerName() {
        return customerName;
    }

    @Override
    public int ownerId() {
        return ownerID;
    }

    @Override
    public boolean checkLocked() {
        return 1 == mIsLocked;
    }
}
