/**
 * 文件名 : CommonQueryInfo.java
 * 包含类名列表 : CommonQueryInfo
 * 版本信息 : Ver 1.0
 * 创建日期 : 2016年06月28日 10:17
 */
package com.facishare.fs.pluginapi.crm.beans;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.fs.pluginapi.crm.beans.FilterConditionInfo;

import java.io.Serializable;
import java.util.List;

/**
 * 类名 : CommonQueryInfo
 * 作者 : wangying
 * 实现的主要功能 :
 * 创建日期 : 2016年06月28日 10:17
 */
public class CommonQueryInfo implements Serializable {
    /**	* 自定义检索主表ID	*/
    @JSONField(name="M1")
    public String filterMainID;
    /**	* 表格过滤条件集合	*/
    @JSONField(name="M2")
    public List<FilterConditionInfo> conditions;
    /**	* 排序字段	*/
    @JSONField(name="M3")
    public String sortField;
    /**	* 排序类型 1、升序 2、降序	*/
    @JSONField(name="M4")
    public int sortType;

    public CommonQueryInfo() {}

    public CommonQueryInfo(@JSONField(name="M1") String filterMainID,
                           @JSONField(name="M2") List<FilterConditionInfo> conditions){
        this.filterMainID = filterMainID;
        this.conditions = conditions;
    }

    @JSONCreator
    public CommonQueryInfo(@JSONField(name="M1") String filterMainID,
                           @JSONField(name="M2") List<FilterConditionInfo> conditions,
                           @JSONField(name="M3") String sortField,
                           @JSONField(name="M4") int sortType) {
        this.filterMainID = filterMainID;
        this.conditions = conditions;
        this.sortField = sortField;
        this.sortType = sortType;
    }
}