package com.facishare.fs.pluginapi.crm.beans;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.fs.pluginapi.contact.beans.AEmpShortEntity;
import com.facishare.fs.pluginapi.crm.customfieldannotations.ObjField;
import com.facishare.fs.pluginapi.crm.customfieldannotations.ObjFieldType;
import com.facishare.fs.pluginapi.crm.customfieldannotations.ObjsField;
import com.facishare.fs.pluginapi.crm.fieldauthority.FieldAuthUtils;
import com.facishare.fs.pluginapi.crm.type.CoreObjType;

public class CustomerOverviewInfo implements ICrmBizDesc{
	private static final long serialVersionUID = 987106117054583259L;
	//客户ID
	@JSONField(name="M1")
	@ObjsField(objFields = {
			@ObjField(targetFieldName = "CustomerID", fieldType = ObjFieldType.ID, editable = false),
			@ObjField(targetFieldName = "account_id", fieldType = ObjFieldType.ID, editable = false)})
	public String customerID;
	//名称
	@JSONField(name="M2")
	@ObjsField(objFields = {
			@ObjField(targetFieldName = "CustomerID", fieldType = ObjFieldType.CONTENT, editable = false),
			@ObjField(targetFieldName = "account_id", fieldType = ObjFieldType.CONTENT, editable = false)})
	public String name;
	//权限字段 名称的展示str
	public final String mShowName;
	//客户编号
	@JSONField(name="M3")
	public String customerNo;
	//成交状态1:未成交，2：已成交，3：多次成交
	@JSONField(name="M4")
	public int dealStatus;
	//客户级别
	@JSONField(name="M5")
	public String level;
	//权限字段 客户级别的展示str
	public final String mShowLevel;
	//所属公海
	@JSONField(name="M6")
	public String highSeasName;
	//领取时间
	@JSONField(name="M7")
	public long claimTime;
	//来源
	@JSONField(name="M8")
	public String source;
	//行业
	@JSONField(name="M9")
	public String industry;
	//电话
	@JSONField(name="M10")
	public String tel;
	//最后跟进时间
	@JSONField(name="M11")
	public long lastFollowTime;
	//剩余保有时间
	@JSONField(name="M12")
	public String remainingTime;
	//客户状态 1、报备中 2、未分配 3、已分配 4、已作废
	@JSONField(name="M13")
	public int status;
	//地址
	@JSONField(name="M14")
	public String address;
	//创建人
	@JSONField(name="M15")
	public AEmpShortEntity creator;
	//审核人
	@JSONField(name="M16")
	public AEmpShortEntity filingChecker;
	//上级ID
	@JSONField(name="M17")
	public int leaderID;
	//创建时间
	@JSONField(name="M18")
	public long createTime;
	//公海ID
	@JSONField(name="M19")
	public String highSeasID;
	//到期时间
	@JSONField(name="M20")
	public long expireTime;
	//
	@JSONField(name="M21")
	public String areaFullName;
	//负责人
	@JSONField(name="M22")
	public AEmpShortEntity owner;
	//转手次数
	@JSONField(name="M23")
	public int resaleCount;
	//最后更新时间
	@JSONField(name="M24")
	public long updateTime;
	//完成度
	@JSONField(name="M25")
	public String completionRate;
	//备注
	@JSONField(name="M26")
	public String remark;
	//行业名称
	@JSONField(name="M27")
	public String industryName;
	//详细地址
	@JSONField(name="M28")
	public String houseNo;
	//国家，省，市，区
	@JSONField(name="M29")
	public String area;
	//网址
	@JSONField(name="M30")
	public String uRL;
	//邮件
	@JSONField(name="M31")
	public String email;
	//商机金额
	@JSONField(name="M32")
	public String opportunityMoney;
	//订单金额
	@JSONField(name="M33")
	public String customerOrderMoney;
	//退货金额
	@JSONField(name="M34")
	public String returnOrderMoney;
	//回款金额
	@JSONField(name="M35")
	public String paymentMoney;
	//退款金额
	@JSONField(name="M36")
	public String refundMoney;
	//待回款金额
	@JSONField(name="M37")
	public String waitPaymentMoney;
	@JSONField(name="M38")
	public String recyclingReason;
	//传真
	@JSONField(name="M39")
	public String fax;
	//负责人所在的部门
	@JSONField(name="M40")
	public String circles;
	//最后修改人
	@JSONField(name="M41")
	public String updatorName;
	//最后修改人id
	@JSONField(name="M42")
	public int updatorID;
	//负责人姓名
	@JSONField(name="M43")
	public String ownerName;
	//是否回收提醒
	@JSONField(name = "M44")
	public boolean isRemindRecycling;
	//是否锁定 0-未锁定 1-锁定
	@JSONField(name = "M45")
	public int mIsLocked;
 
	public CustomerOverviewInfo(){
		fakeEmpNotNull();
		this.mShowName = "";
		this.mShowLevel = "";
	}
	@JSONCreator
	public CustomerOverviewInfo(
			@JSONField(name="M1")
			String customerID,
			@JSONField(name="M2")
			String name,
			@JSONField(name="M3")
			String customerNo,
			@JSONField(name="M4")
			int dealStatus,
			@JSONField(name="M5")
			String level,
			@JSONField(name="M6")
			String highSeasName,
			@JSONField(name="M7")
			long claimTime,
			@JSONField(name="M8")
			String source,
			@JSONField(name="M9")
			String industry,
			@JSONField(name="M10")
			String tel,
			@JSONField(name="M11")
			long lastFollowTime,
			@JSONField(name="M12")
			String remainingTime,
			@JSONField(name="M13")
			int status,
			@JSONField(name="M14")
			String address,
			@JSONField(name="M15")
			AEmpShortEntity creator,
			@JSONField(name="M16")
			AEmpShortEntity filingChecker,
			@JSONField(name="M17")
			int leaderID,
			@JSONField(name="M18")
			long createTime,
			@JSONField(name="M19")
			String highSeasID,
			@JSONField(name="M20")
			long expireTime,
			@JSONField(name="M21")
			String areaFullName,
			@JSONField(name="M22")
			AEmpShortEntity owner,
			@JSONField(name="M23")
			int resaleCount,
			@JSONField(name="M24")
			long updateTime,
			@JSONField(name="M25")
			String completionRate,
			@JSONField(name="M26")
			String remark,
			@JSONField(name="M27")
			String industryName,
			@JSONField(name="M28")
			String houseNo,
			@JSONField(name="M29")
			String area,
			@JSONField(name="M30")
			String uRL,
			@JSONField(name="M31")
			String email,
			@JSONField(name="M32")
			String opportunityMoney,
			@JSONField(name="M33")
			String customerOrderMoney,
			@JSONField(name="M34")
			String returnOrderMoney,
			@JSONField(name="M35")
			String paymentMoney,
			@JSONField(name="M36")
			String refundMoney,
			@JSONField(name="M37")
			String waitPaymentMoney,
			@JSONField(name="M38")
			String recyclingReason,
			@JSONField(name="M39")
			String fax,
			@JSONField(name="M40")
			String circles,
			@JSONField(name="M41")
			String updatorName,
			@JSONField(name="M42")
			int updatorID,
			@JSONField(name="M43")
			String ownerName,
			@JSONField(name = "M44")
			boolean isRemindRecycling,
			@JSONField(name = "M45")
			int isLocked) {
		this.customerID = customerID;
		this.name = name;
		this.mShowName = FieldAuthUtils.getShowStr(name);
		this.customerNo = customerNo;
		this.dealStatus = dealStatus;
		this.level = level;
		this.mShowLevel = FieldAuthUtils.getShowStr(level);
		this.highSeasName = highSeasName;
		this.claimTime = claimTime;
		this.source = source;
		this.industry = industry;
		this.tel = tel;
		this.lastFollowTime = lastFollowTime;
		this.remainingTime = remainingTime;
		this.status = status;
		this.address = address;
		this.creator = creator;
		this.filingChecker = filingChecker;
		this.leaderID = leaderID;
		this.createTime = createTime;
		this.highSeasID = highSeasID;
		this.expireTime = expireTime;
		this.areaFullName = areaFullName;
		this.owner = owner;
		this.resaleCount = resaleCount;
        this.updateTime = updateTime;
		this.completionRate = completionRate;
		this.remark = remark;
		this.industryName = industryName;
		this.houseNo = houseNo;
		this.area = area;
		this.uRL = uRL;
		this.email = email;
		this.opportunityMoney = opportunityMoney;
		this.customerOrderMoney = customerOrderMoney;
		this.returnOrderMoney = returnOrderMoney;
		this.paymentMoney = paymentMoney;
		this.refundMoney = refundMoney;
		this.waitPaymentMoney = waitPaymentMoney;
		this.recyclingReason = recyclingReason;
		this.fax = fax;
		this.circles = circles;
		this.updatorName = updatorName;
		this.updatorID = updatorID;
		this.ownerName = ownerName;
		this.isRemindRecycling = isRemindRecycling;
		this.mIsLocked = isLocked;
		fakeEmpNotNull();
	}

	/**
	 * 伪造非空的人员实体
	 */
	private void fakeEmpNotNull() {
		if (creator == null) {
			this.creator = new AEmpShortEntity();
		}
		if (filingChecker == null) {
			this.filingChecker = new AEmpShortEntity();
		}
		if (owner == null) {
			this.owner = new AEmpShortEntity();
		}
	}

	@Override
	public CoreObjType objType() {
		return CoreObjType.Customer;
	}

	@Override
	public String mainField() {
		return name;
	}

	@Override
	public String showMainField() {
		return mShowName;
	}

	@Override
	public String id() {
		return customerID;
	}

	@Override
	public String customerId() {
		return customerID;
	}

	@Override
	public String customerName() {
		return name;
	}

	@Override
	public int ownerId() {
		return owner == null ? -1 : owner.employeeID;
	}

	@Override
	public boolean checkLocked() {
		return 1 == mIsLocked;
	}


	//是否展示销售记录
	@JSONField(serialize = false, deserialize = false)
	public boolean allowMemberViewFeed = false;

	//未领取客户前，允许公海成员发送销售记录
	@JSONField(serialize = false, deserialize = false)
	public boolean allowMemberSendFeed = false;

	//是否客户相关团队成员
	@JSONField(serialize = false, deserialize = false)
	public boolean isTeamMember = false;
}
