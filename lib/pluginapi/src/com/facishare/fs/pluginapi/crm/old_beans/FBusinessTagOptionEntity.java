package com.facishare.fs.pluginapi.crm.old_beans;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;

public class FBusinessTagOptionEntity implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -4614124122139238979L;
	/**
	 * 标签ID
	 */
	@JSONField(name="a")
	public  String fBusinessTagID;
	/**
	 * 标签选项ID
	 */
	@JSONField(name="b")
	public  int fBusinessTagOptionID;
	/**
	 * 标签选项名称
	 */
	@JSONField(name="c")
	public String name;
	/**
	 * 是否默认
	 */
	@JSONField(name="d")
	public  boolean isDefault;

	@JSONCreator
	public FBusinessTagOptionEntity(@JSONField(name="a") String fBusinessTagID,
			@JSONField(name="b") int fBusinessTagOptionID,
			@JSONField(name="c") String name,
			@JSONField(name="d") boolean isDefault) {
		this.fBusinessTagID = fBusinessTagID;
		this.fBusinessTagOptionID = fBusinessTagOptionID;
		this.name = name;
		this.isDefault = isDefault;
	}
	
	//标签选择，修改
	public boolean isSelect;
	//数据ID标签修改时回传
	public String dataID;
	
	//客户搜索选择
	public boolean isCustomerSelect;
	
	public FBusinessTagOptionEntity()
	{
		super();
	}
}
