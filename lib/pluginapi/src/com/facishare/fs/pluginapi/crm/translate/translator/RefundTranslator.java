package com.facishare.fs.pluginapi.crm.translate.translator;

import com.facishare.fs.pluginapi.crm.beans.SelectObjFieldItem;
import com.facishare.fs.pluginapi.crm.beans.SelectObjectBean;
import com.facishare.fs.pluginapi.crm.beans.TradeRefundInfo;
import com.facishare.fs.pluginapi.crm.translate.ITranslate;
import com.facishare.fs.pluginapi.crm.type.CoreObjType;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by lixn on 2017/5/11.
 */

public class RefundTranslator implements ITranslate<TradeRefundInfo> {

    public static final String FIELD_TRADE_REFUND_CODE = "TradeRefundCode";
    public static final String FIELD_TRADE_REFUND_MONEY= "RefundMoney";
    public static final String FIELD_TRADE_REFUND_DATE = "RefundTime";


    @Override
    public boolean accept(CoreObjType objectType) {
        return CoreObjType.Refund == objectType;
    }

    @Override
    public TradeRefundInfo generalObj2BizObj(SelectObjectBean selectObjectBean) {
        TradeRefundInfo refundInfo = null;
        if (selectObjectBean != null && selectObjectBean.mType == CoreObjType.Refund) {
            LinkedHashMap<String, SelectObjFieldItem> fields = selectObjectBean.gainExpandedField();
            refundInfo = new TradeRefundInfo();
            refundInfo.tradeRefundID = selectObjectBean.mId;
            SelectObjFieldItem refundCode = fields.get(FIELD_TRADE_REFUND_CODE);
            SelectObjFieldItem refundMoney = fields.get(FIELD_TRADE_REFUND_MONEY);
            SelectObjFieldItem refundDate = fields.get(FIELD_TRADE_REFUND_DATE);
            if (refundCode != null) {
                refundInfo.tradeRefundCode = refundCode.fieldValue;
                refundInfo.mShowTradeRefundCode = refundCode.fieldDisplayValue;
            }
            if (refundMoney != null) {
                refundInfo.refundMoney = refundMoney.fieldValue;
                refundInfo.mShowRefundMoney = refundMoney.fieldDisplayValue;
            }
            if (refundDate != null) {
                try {
                    refundInfo.refundTime = Long.decode(refundDate.fieldValue);
                } catch (Exception e) {
                    refundInfo.refundTime = -1;
                }
            }
        }
        return refundInfo;
    }

    @Override
    public SelectObjectBean bizObj2GeneralObj(TradeRefundInfo tradeRefundInfo) {
        if (tradeRefundInfo == null) {
            return null;
        }
        List<SelectObjFieldItem> fields = new ArrayList<>();
        fields.add(new SelectObjFieldItem(CoreObjType.Refund.value, FIELD_TRADE_REFUND_CODE, tradeRefundInfo.mShowTradeRefundCode, tradeRefundInfo.tradeRefundCode));
        fields.add(new SelectObjFieldItem(CoreObjType.Refund.value, FIELD_TRADE_REFUND_MONEY, tradeRefundInfo.mShowRefundMoney, tradeRefundInfo.refundMoney));
        fields.add(new SelectObjFieldItem(CoreObjType.Refund.value, FIELD_TRADE_REFUND_DATE, String.valueOf(tradeRefundInfo.refundTime), String.valueOf(tradeRefundInfo.refundTime)));
        return SelectObjectBean.createExpandedBeanByFieldList(tradeRefundInfo.tradeRefundID, CoreObjType.Refund.value, fields, 0);
    }
}
