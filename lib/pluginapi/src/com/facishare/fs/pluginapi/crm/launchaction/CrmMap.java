/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.pluginapi.crm.launchaction;

/**
 * Created by liuyu on 2016/5/17.
 * 地图相关
 */
public class CrmMap {
    /** 附近客户页*/
    public static final String nearCustomer = CrmLaunchActionSet.BASE + ".nearcustomer";
    /** 选择地址页*/
    public static final String selectAddress = CrmLaunchActionSet.BASE + ".selectaddress";
    /** 显示地址页*/
    public static final String showAddress = CrmLaunchActionSet.BASE + ".showaddress";
}
