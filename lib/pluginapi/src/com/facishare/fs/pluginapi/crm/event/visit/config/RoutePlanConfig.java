package com.facishare.fs.pluginapi.crm.event.visit.config;

import com.facishare.fs.pluginapi.crm.beans.VisitInfo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 跳转路线规划配置项
 * <p>
 * Created by xiangd on 2017/10/23.
 */
public class RoutePlanConfig implements Serializable {

    /**
     * 拜访数据列表，必选项
     */
    private List<VisitInfo> mVisitList;
    /**
     * 标题时间，可选
     */
    private Date mTitleDate;

    private RoutePlanConfig() {
    }

    public List<VisitInfo> getVisitList() {
        return mVisitList;
    }

    public Date getTitleDate() {
        return mTitleDate;
    }

    public static class Builder {
        RoutePlanConfig mRoutePlanConfig;


        public Builder() {
            mRoutePlanConfig = new RoutePlanConfig();
        }

        public Builder setVisitList(List<VisitInfo> mVisitList) {
            mRoutePlanConfig.mVisitList = mVisitList;
            return this;
        }

        public Builder setTitleDate(Date mTitleDate) {
            mRoutePlanConfig.mTitleDate = mTitleDate;
            return this;
        }

        public RoutePlanConfig build() {
            return mRoutePlanConfig;
        }
    }

}
