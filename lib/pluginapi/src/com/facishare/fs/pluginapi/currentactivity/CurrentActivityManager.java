package com.facishare.fs.pluginapi.currentactivity;

import android.app.Activity;

/**
 * Description:用来保存除音视频 Activity 之外的其他插件 Activity 的 Context 引用，
 * 用来作为音视频弹出权限 Dialog 的页面
 *
 * <AUTHOR>
 * @since 2016-05-18
 */
public class CurrentActivityManager {
    private static final String TAG = "CurrentActivityManager";

    private Activity activity;

    private static volatile CurrentActivityManager instance;

    public static CurrentActivityManager getInstance() {
        if (instance == null) {
            synchronized (CurrentActivityManager.class) {
                if (instance == null) {
                    instance = new CurrentActivityManager();
                }
            }
        }
        return instance;
    }

    public void setCurrentActivity(Activity activity) {
        this.activity = activity;
    }

    public void clearCurrentActivity() {
        activity = null;
    }

    public Activity getCurrentActivity() {
        return activity;
    }
}
