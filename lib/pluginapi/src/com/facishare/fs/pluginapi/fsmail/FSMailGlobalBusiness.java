package com.facishare.fs.pluginapi.fsmail;

import com.alibaba.fastjson.TypeReference;
import com.facishare.fs.pluginapi.fsmail.callbacks.OnGetFSMailBindingCallback;
import com.facishare.fs.pluginapi.fsmail.results.EmailGetResult;
import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;
import com.fxiaoke.fshttp.web.http.WebApiFailureType;
import com.fxiaoke.fshttp.web.http.WebApiResponse;
import com.fxiaoke.fshttp.web.http.WebApiUtils;

import java.util.Date;

/**
 * Created by wubb on 2017/7/3.
 */

public class FSMailGlobalBusiness {
    private static final String FSMAIL_CONTROLER="FHE/EM1AEMAILPROXY/emailproxy";
    /**
     * 获取邮箱绑定状态
     * @param callback
     */
    public static void getFSMailBindingStatus(final OnGetFSMailBindingCallback callback){
        WebApiUtils.postAsync(FSMAIL_CONTROLER, "EmailGet"
                , null
                , new WebApiExecutionCallback<EmailGetResult>() {

                    @Override
                    public TypeReference<WebApiResponse<EmailGetResult>> getTypeReference() {
                        return new TypeReference<WebApiResponse<EmailGetResult>>() {
                        };
                    }

                    public Class<EmailGetResult> getTypeReferenceFHE() {
                        return EmailGetResult.class;
                    }

                    @Override
                    public void completed(Date time, EmailGetResult response) {
                        if (callback != null) {
                            callback.onSuccess(response.data.status==3?false:true);
                        }
                    }

                    @Override
                    public void failed(WebApiFailureType failureType, int httpStatusCode, String error, int errorCode,
                                       int enterpriseID) {
                        if (callback != null) {
                            callback.onFailed(failureType,httpStatusCode,error,errorCode,enterpriseID);
                        }
                    }
                });
    }
}
