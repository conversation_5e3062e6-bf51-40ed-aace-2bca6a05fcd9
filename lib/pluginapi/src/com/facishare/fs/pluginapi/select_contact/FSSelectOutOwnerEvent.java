/*
 * Copyright (C) 2019 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.pluginapi.select_contact;

import java.util.LinkedHashMap;
import java.util.List;

import com.facishare.fs.pluginapi.contact.beans.OutOwner;

/**
 * Author:  wangrz
 * Date:    2019/9/22 17:51
 * Remarks: 选择外部联系人
 */
public class FSSelectOutOwnerEvent {
    public List<OutOwner> outOwnerList;
    public LinkedHashMap<Integer, String> employeesMapPicked;

    public FSSelectOutOwnerEvent(LinkedHashMap<Integer, String> employeesMapPicked,
                                 List<OutOwner> outOwnerList) {
        this.employeesMapPicked = employeesMapPicked;
        this.outOwnerList = outOwnerList;
    }
}
