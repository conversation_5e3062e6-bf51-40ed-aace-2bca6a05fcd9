/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.pluginapi.gremind;

/**
 * FloatRemindDialog滑入划出动画方向
 * 默认顶部滑入滑出，TOP in TOP out
 * <p>
 * Created by xiangd on 2017/6/9.
 */

public enum AnimOrientation {

    LEFT,
    TOP,
    RIGHT,
    BOTTOM;

    public static boolean isHorizontalOrient(AnimOrientation animOrientation) {
        return LEFT == animOrientation || RIGHT == animOrientation;
    }

    public static boolean isVerticalOrient(AnimOrientation animOrientation) {
        return TOP == animOrientation || BOTTOM == animOrientation;
    }

}
