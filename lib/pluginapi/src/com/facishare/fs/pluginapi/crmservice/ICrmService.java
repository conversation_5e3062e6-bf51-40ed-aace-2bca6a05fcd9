/*
 * Copyright (C) 2021 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.pluginapi.crmservice;

import java.util.List;
import java.util.Map;

import com.fxiaoke.fshttp.web.http.WebApiExecutionCallback;

import android.content.Context;

/**
 * author: wangrz
 * date: 2021/11/3 13:19
 * description: crm 接口请求
 */
public interface ICrmService {
     /**
      * 对象 分版&权限 校验接口
      * 接口逻辑：1、首先判断分版是否支持 2、然后判断权限
      * 6.3.0 增加
      *
      * @param apiNames 要检验的对象
      * @param actionCodes 要校验的权限标识
      * @param callback
      */
    void checkFunctionRight(List<String> apiNames, List<String> actionCodes,
                            WebApiExecutionCallback<GetRightResult> callback);

    void checkObjectRight(Context context, final String apiName, final String actionCode,
                          GetObjectRightCallback callback);
    /**
     * 从map中获得对象和相应action的权限
     *
     * @param apiName
     * @return
     */
    boolean getActionRightFromMap(String apiName, String action, Map<String, Object> map);
}
