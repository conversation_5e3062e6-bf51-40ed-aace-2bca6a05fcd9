/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.lib.pay.bean.result;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Created by wangtao on 2015/11/18.
 */
public class CommonResult implements Serializable {

    private static final long serialVersionUID = 7649839151672455138L;


    private String code;

    @JSONField (serialize = false)
    int errorCode;

    @JSONField(serialize = false)
    String errorMessage;

    @JSONField(serialize = false)
    String errorUrl;

    boolean fromCache;


    public CommonResult() {
    }

    public CommonResult(int errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    @JSONField(name = "M1")
    public String getCode() {
        return code;
    }
    @JSONField(name = "M1")
    public void setCode(String code) {
        this.code = code;
    }

    public String getErrorUrl() {
        return errorUrl;
    }

    public void setErrorUrl(String errorUrl) {
        this.errorUrl = errorUrl;
    }


    public boolean isFromCache() {
        return fromCache;
    }

    public void setFromCache(boolean fromCache) {
        this.fromCache = fromCache;
    }

    @Override
    public String toString() {
        return "CommonResult{" +
                "errorCode=" + errorCode +
                ", errorMessage='" + errorMessage +
                ", errorUrl='" + errorUrl + '\'' +
                "} " + super.toString();
    }
}
