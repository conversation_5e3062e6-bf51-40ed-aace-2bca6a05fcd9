/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.lib.pay.bean;

import java.io.Serializable;

/**
 * Created by wangkw on 2016/7/1.
 */
public class EPayInfo implements Serializable {
    public static String KEY = "EPay_Info";

    public long amount; //金额，单位是分
    public String orderNo;//订单号
    public String merchantCode;//商户号
    public String remark;//交易备注
    public String subject;//商品名称
    public String body;//商品描述
    public String toEA;//收款的企业号，为空默认为纷享
    public String toEAName;//收款的企业名称
    public String toEAWalletId;//收款企业账号ID
    public String signature;//后台返回的签名参数
    public String productId; //商品ID
    public String toUserId;//收款人ID
    public String receiverSubEA;

    public long getAmount() {
        return amount;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public String getMerchantCode() {
        return merchantCode;
    }

    public String getRemark() {
        return remark;
    }

    public String getSubject() {
        return subject;
    }

    public String getBody() {
        return body;
    }

    public String getToEA() {
        return toEA;
    }

    public String getToEAName() {
        return toEAName;
    }

    public String getToEAWalletId() {
        return toEAWalletId;
    }


    public String getSignature() {
        return signature;
    }

    public String getProductId() {
        return productId;
    }

    public String getToUserId() {
        return toUserId;
    }

    public String getReceiverSubEA() {
        return receiverSubEA;
    }
}
