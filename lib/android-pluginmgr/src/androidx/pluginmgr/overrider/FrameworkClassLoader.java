/*
 * Copyright (C) 2015 HouKx <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package androidx.pluginmgr.overrider;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.util.Map;

import com.fxiaoke.fxlog.FCLog;

import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;

import androidx.pluginmgr.IPlugChangedListener;
import androidx.pluginmgr.PluginManager;
import androidx.pluginmgr.ProxyActivityManager;
import androidx.pluginmgr.environment.PlugInfo;

/**
 * 框架类加载器（Application 的 classLoder被替换成此类的实例）
 *
 * <AUTHOR>
 */
@SuppressLint("NewApi")
public class FrameworkClassLoader extends ClassLoader {
    private static final String TAG = FrameworkClassLoader.class.getSimpleName();
    private static final String EXCEPTION_FORMAT = "[pluginID:%s,loadActivity:%s,loadClass:%s]";

    private String plugId;
    private String actName;
    IPlugChangedListener mlis;

    public FrameworkClassLoader(ClassLoader parent) {
        super(parent);
    }

    public void setIPlugChangedListener(IPlugChangedListener lis) {
        mlis = lis;
    }

    public void confirmEnv(String plugId, String actName) {
        FCLog.i(PluginManager.DE_PluginManager, "confirmEnv:pluginid " + plugId + " actname " + actName);
        if (plugId != null && !plugId.equals(this.plugId)) {
            try {
                Field sConstructorMap = LayoutInflater.class.getDeclaredField("sConstructorMap");
                sConstructorMap.setAccessible(true);
                Map<String, Constructor<? extends View>> map =
                        (Map<String, Constructor<? extends View>>) sConstructorMap.get(null);
                map.clear();
            } catch (NoSuchFieldException e) {
                FCLog.w(PluginManager.DE_PluginManager, TAG, Log.getStackTraceString(e));
            } catch (IllegalAccessException e) {
                FCLog.w(PluginManager.DE_PluginManager, TAG, Log.getStackTraceString(e));
            } catch (IllegalArgumentException e) {
                FCLog.w(PluginManager.DE_PluginManager, TAG, Log.getStackTraceString(e));
            }

            if (mlis != null) {
                mlis.onInNewPlug(plugId);
            }
        }
        this.plugId = plugId;
        this.actName = actName;
    }

    public String confirmEnv(String plugId, String actName, Intent intent) {
        return newActivityClassName(plugId, actName, intent);
    }

    private String newActivityClassName(String plugId, String actName, Intent it) {
        confirmEnv(plugId, actName);
        PlugInfo plugin = PluginManager.getInstance().getPluginById(plugId);
        ActivityInfo activityInfo = plugin.findActivityByClassName(actName);
        if (activityInfo == null) {
            FCLog.e(TAG, "is activity " + actName + " decleared in AndroidManifest.xml?");
        }

        String targetActivityName =
                ProxyActivityManager.getInstance().findTargetActivityNameWithParent(activityInfo, actName);
        if (it != null) {
            it.setComponent(
                    new ComponentName(PluginManager.getInstance().getContext(), targetActivityName));
        }
        return targetActivityName;
    }

    public PlugInfo getCurPlug() {
        return PluginManager.getInstance().getPluginById(plugId);
    }

    protected Class<?> loadClass(String className, boolean resolve) throws ClassNotFoundException {
//    FCLog.d("cl", "loadClass: " + className);
        PlugInfo plugin = PluginManager.getInstance().getPluginById(plugId);
//    FCLog.d("cl", "plugin = " + plugin);
//        if (className.equals("com.tencent.qalsdk.service.QalService")) {
//            Log.e("zhaodsh", "zhaodsh");
//        }

        Class<?> ret;
        try {
            if (plugin != null) {
                PluginClassLoader pluginClassLoader = plugin.getClassLoader();
                if (pluginClassLoader != null) {
                    if (ProxyActivityManager.getInstance().isContainsTargetActivity(className)) {
                        ret = pluginClassLoader.loadActivityClass(actName, className);
                    } else {
                        ret = pluginClassLoader.loadClass(className);
                    }
                    return ret;
                }
            }
        } catch (ClassNotFoundException e) {
            if (ProxyActivityManager.getInstance().isContainsTargetActivity(className)
                    || className.endsWith("Activity")) {
                //ignore
                FCLog.e(PluginManager.DE_PluginManager, TAG, Log.getStackTraceString(e));
                throw new ClassNotFoundException(String.format(EXCEPTION_FORMAT, plugId, actName, className), e.getException());
            } else {
                for (PlugInfo pluginItem : PluginManager.getInstance().getPlugins()) {
                    if (pluginItem.equals(plugin)) {
                        continue;
                    }

                    try {
                        PluginClassLoader pluginClassLoader = pluginItem.getClassLoader();
                        if (pluginClassLoader != null) {
                            ret = pluginClassLoader.loadClass(className);
                            if (ret != null) {
                                return ret;
                            }
                        }
                    } catch (ClassNotFoundException e1) {
                        FCLog.w(PluginManager.DE_PluginManager, TAG, Log.getStackTraceString(e1));
                    }
                }
            }
        }
        ret = super.loadClass(className, resolve);
        return ret;
    }
}