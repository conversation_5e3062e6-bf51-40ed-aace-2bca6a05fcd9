package androidx.pluginmgr.delegate;

import android.app.Application;
import android.app.Instrumentation;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.support.v4_fs.util.annotation.NonNull;
import android.util.Log;

import androidx.pluginmgr.PluginManager;
import androidx.pluginmgr.reflect.Reflect;
import androidx.pluginmgr.reflect.ReflectException;
import com.fxiaoke.fxlog.FCLog;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @version 1.0
 */
public final class DelegateActivityThread {
  private static final String TAG = "DelegateActivityThread";
  private static DelegateActivityThread SINGLETON = new DelegateActivityThread();

  private Reflect activityThreadReflect;

  private DelegateActivityThread() {
    try {
      activityThreadReflect = Reflect.on("android.app.ActivityThread").call("currentActivityThread");
    } catch (ReflectException e) {
      FCLog.w(PluginManager.DE_PluginManager, TAG, Log.getStackTraceString(e));
    }
  }

  public static DelegateActivityThread getSingleton() {
    return SINGLETON;
  }

  public Object getActivityThread() {
    return activityThreadReflect.get();
  }

  public Instrumentation getInstrumentation() {
    try {
      return activityThreadReflect.get("mInstrumentation");
    } catch (ReflectException e) {
      FCLog.w(PluginManager.DE_PluginManager, TAG, Log.getStackTraceString(e));
    }
    return null;
  }

  public void setInstrumentation(Instrumentation newInstrumentation) {
    try {
      activityThreadReflect.set("mInstrumentation", newInstrumentation);
    } catch (ReflectException e) {
      FCLog.w(PluginManager.DE_PluginManager, TAG, Log.getStackTraceString(e));
    }
  }

  public Object getLoadedApk(@NonNull ApplicationInfo applicationInfo) {
    try {
      Class[] loadedApkParameterTypes = new Class[] {
        ApplicationInfo.class, Class.forName("android.content.res.CompatibilityInfo"), int.class
      };
      Method getPackageInfo =
        activityThreadReflect.exactMethod("getPackageInfo", loadedApkParameterTypes);

      Object[] loadApkArgs = new Object[] {
        applicationInfo, null, Context.CONTEXT_IGNORE_SECURITY
      };

      Reflect loadedApkReflect =
        activityThreadReflect.on(getPackageInfo, activityThreadReflect.get(), loadApkArgs);
      return loadedApkReflect.get();
    } catch (ClassNotFoundException e) {
      FCLog.w(PluginManager.DE_PluginManager, TAG, Log.getStackTraceString(e));
    } catch (NoSuchMethodException e) {
      FCLog.w(PluginManager.DE_PluginManager, TAG, Log.getStackTraceString(e));
    } catch (ReflectException e) {
      FCLog.w(PluginManager.DE_PluginManager, TAG, Log.getStackTraceString(e));
    }

    return null;
  }

  public Application makeApplication(@NonNull Object loadedApk) {
    try {
      Object[] applicationArgs = new Object[] {
        false, getInstrumentation()
      };
      return Reflect.on(loadedApk).call("makeApplication", applicationArgs).get();
    }catch (ReflectException e){
      FCLog.w(PluginManager.DE_PluginManager, TAG, Log.getStackTraceString(e));
    }

    return null;
  }
}
