package androidx.pluginmgr.delegate;

import android.content.Context;
import android.content.res.AssetManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.content.res.XmlResourceParser;
import androidx.annotation.AnyRes;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.PluralsRes;
import androidx.annotation.RawRes;
import androidx.annotation.StringRes;
import androidx.annotation.StyleRes;
import androidx.annotation.StyleableRes;
import android.util.DisplayMetrics;
import android.util.TypedValue;

import java.io.InputStream;

/**
 * Created by lid on 2017/6/6.
 */

public class ResourcesDelegate extends Resources{
    Context mHost;
    @Deprecated
    public ResourcesDelegate(Context host, AssetManager assets, DisplayMetrics metrics, Configuration config) {
        super(assets,metrics,config);
        mHost=host;
    }

    public XmlResourceParser getLayout(@LayoutRes int id) throws NotFoundException {
        try {
            return super.getLayout(id);
        }catch (NotFoundException e){
            return mHost.getResources().getLayout(id);
        }
    }
    @NonNull
    public String getString(@StringRes int id) throws NotFoundException {
        try {
            return super.getString(id);
        }catch (NotFoundException e){
            return mHost.getResources().getString(id);
        }
    }
    @NonNull
    public String getString(@StringRes int id, Object... formatArgs) throws NotFoundException {
        try {
            return super.getString(id,formatArgs);
        }catch (NotFoundException e){
            return mHost.getResources().getString(id,formatArgs);
        }
    }
    public String getResourceName(@AnyRes int resid) throws NotFoundException {
        try {
            return super.getResourceName(resid);
        }catch (NotFoundException e){
            return mHost.getResources().getResourceName(resid);
        }
    }
    public void getValue(String name, TypedValue outValue, boolean resolveRefs)
            throws NotFoundException {
        try {
            super.getValue(name,outValue,resolveRefs);
        }catch (NotFoundException e){
            mHost.getResources().getValue(name,outValue,resolveRefs);
        }
    }
    public void getValue(@AnyRes int id, TypedValue outValue, boolean resolveRefs)
            throws NotFoundException {
        try {
            super.getValue(id,outValue,resolveRefs);
        }catch (NotFoundException e){
            mHost.getResources().getValue(id,outValue,resolveRefs);
        }
    }
    public String getResourcePackageName(@AnyRes int resid) throws NotFoundException {
        try {
            return super.getResourcePackageName(resid);
        }catch (NotFoundException e){
            return mHost.getResources().getResourcePackageName(resid);
        }
    }
    public String getResourceTypeName(@AnyRes int resid) throws NotFoundException {
        try {
            return super.getResourceTypeName(resid);
        }catch (NotFoundException e){
            return mHost.getResources().getResourceTypeName(resid);
        }
    }
    @NonNull
    public String getQuantityString(@PluralsRes int id, int quantity, Object... formatArgs)
            throws NotFoundException {
        try {
            return super.getQuantityString(id,quantity,formatArgs);
        }catch (NotFoundException e){
            return mHost.getResources().getQuantityString(id,quantity,formatArgs);
        }
    }
    @NonNull
    public String getQuantityString(@PluralsRes int id, int quantity) throws NotFoundException {
        try {
            return super.getQuantityString(id,quantity);
        }catch (NotFoundException e){
            return mHost.getResources().getQuantityString(id,quantity);
        }
    }
    @NonNull
    public CharSequence getQuantityText(@PluralsRes int id, int quantity)
            throws NotFoundException {
        try {
            return super.getQuantityText(id,quantity);
        }catch (NotFoundException e){
            return mHost.getResources().getQuantityText(id,quantity);
        }
    }
    @NonNull public CharSequence getText(@StringRes int id) throws NotFoundException {
        try {
            return super.getText(id);
        }catch (NotFoundException e){
            return mHost.getResources().getText(id);
        }
    }
    public CharSequence getText(@StringRes int id, CharSequence def) {
        try {
            return super.getText(id,def);
        }catch (NotFoundException e){
            return mHost.getResources().getText(id,def);
        }
    }
    public InputStream openRawResource(@RawRes int id) throws NotFoundException {
        try {
            return super.openRawResource(id);
        }catch (NotFoundException e){
            return mHost.getResources().openRawResource(id);
        }
    }
    public InputStream openRawResource(@RawRes int id, TypedValue value)
            throws NotFoundException {
        try {
            return super.openRawResource(id,value);
        }catch (NotFoundException e){
            return mHost.getResources().openRawResource(id,value);
        }
    }
    public int getIdentifier(String name, String defType, String defPackage) {
        try {
            return super.getIdentifier(name,defType,defPackage);
        }catch (NotFoundException e){
            return mHost.getResources().getIdentifier(name,defType,defPackage);
        }
    }
}
