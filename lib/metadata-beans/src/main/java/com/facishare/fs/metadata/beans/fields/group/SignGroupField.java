package com.facishare.fs.metadata.beans.fields.group;

import java.util.Map;

/**
 * 签到组件
 * Created by weilh on 2017/7/10.
 */

public class SignGroupField extends GroupField implements GroupFieldKeys.Sign{

    public SignGroupField(Map<String, Object> map) {
        super(map);
    }

    public String getSignInTimeField() {
        return getFieldApiName(SIGN_IN_TIME_FIELD);
    }

    public String getSignOutTimeField() {
        return getFieldApiName(SIGN_OUT_TIME_FIELD);
    }

    public String getSignInLocationField() {
        return getFieldApiName(SIGN_IN_LOCATION_FIELD);
    }

    public String getSignOutLocationField() {
        return getFieldApiName(SIGN_OUT_LOCATION_FIELD);
    }

    public String getSignInStatusField() {
        return getFieldApiName(SIGN_IN_STATUS_FIELD);
    }

    public String getSignOutStatusField() {
        return getFieldApiName(SIGN_OUT_STATUS_FIELD);
    }

    public String getVisitStatusField() {
        return getFieldApiName(VISIT_STATUS_FIELD);
    }

    public String getIntervalField() {
        return getFieldApiName(INTERVAL_FIELD);
    }

    public String getSignInInfoList() {
        return getFieldApiName(SIGN_IN_INFO_LIST);
    }

}
