package com.facishare.fs.metadata.beans.components;

import java.util.List;
import java.util.Map;

import com.facishare.fs.metadata.beans.formfields.FormField;

/**
 * Created by zhouz on 2017/7/13.
 */

public class TableComponent extends Component implements ComponentKeys.Table {
    public TableComponent(Map<String, Object> map) {
        super(map);
    }

    //业务类型名称
    public String getHeader() {
        return getString(HEADER);
    }

    //列表字段
    public List<FormField> getIncludeFields() {
        return getMetaDataList(INCLUDE_FIELDS, FormField.class);
    }

    //业务类型apiName
    public String getRefObjectApiName() {
        return getString(REF_OBJECT_API_NAME);
    }

    public boolean isNotMatch() {
        return getBoolean(NOT_MATCH);
    }
}
