/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.beans;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.fs.common_utils.ICannotSentByIntent;
import com.facishare.fs.metadata.beans.fields.CalculateRelation;
import com.facishare.fs.metadata.beans.fields.Field;
import com.facishare.fs.metadata.beans.fields.FieldKeys;
import com.facishare.fs.metadata.beans.fields.FieldType;
import com.facishare.fs.metadata.beans.fields.group.GroupField;

import androidx.annotation.NonNull;
import android.text.TextUtils;

/**
 * 对象描述
 * <AUTHOR>
 *         <b>DATE</b> 2016/9/20.
 */
public class ObjectDescribe extends JSONObject implements Serializable, ICannotSentByIntent {

    public ObjectDescribe(){
        super();
    }

    public String getId() {
        return getString("_id");
    }

    public String getApiName() {
        return getString("api_name");
    }

    public void setApiName(String apiName) {
        put("api_name", apiName);
    }

    public String getCreateTime() {
        return getString("create_time");
    }

    public String getDisplayName() {
        return getString("display_name");
    }

    public void setDisplayName(String displayName) {
        put("display_name", displayName);
    }

    public Map<String, Map<String, Object>> getFieldMaps() {
        return (Map<String, Map<String, Object>>)get("fields");
    }

    @JSONField(serialize = false,deserialize = false)
    @NonNull
    public Map<String, Field> getFields(){
        try {
            return MetaDataParser.toMetaDataMap(getFieldMaps(), Field.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new HashMap<>();
    }

    public void setFields(Map<String, Map<String, Object>> fields) {
        put("fields", fields);
    }

    public int getVersion() {
        return getIntValue("version");
    }

    public void setVersion(int version) {
        put( "version", version);
    }

    @JSONField(serialize = false,deserialize = false)
    public GroupField getGroupFieldByGroupType(String groupType) {
        Map<String, Field> fieldMap = getFields();
        for (Field field : fieldMap.values()) {
            if (FieldType.GROUP.key.equals(field.getType())) {
                GroupField groupField = field.to(GroupField.class);
                if (TextUtils.equals(groupType, groupField.getGroupType())) {
                    return groupField;
                }
            }
        }
        return null;
    }
    @JSONField(name = "calculate_relation")
    public Map<String, Object> getCalculateRelationMap() {
        return (Map<String, Object>)get( "calculate_relation");
    }

    @JSONField(serialize = false,deserialize = false)
    public CalculateRelation getCalculateRelation() {
        try {
            return MetaDataParser.toMetaData(getCalculateRelationMap(), CalculateRelation.class);
        } catch (Exception ignore) {
        }
        return null;
    }
    /**describe级别的CalculateFields*/
    @JSONField(serialize = false,deserialize = false)
    public Map<String, List<Map<String, Object>>> getCalculateFields(){
        CalculateRelation calculateRelation = getCalculateRelation();
        if (calculateRelation != null) {
            return calculateRelation.getCalculateFields();
        }
        return null;
    }

    @JSONField(serialize = false,deserialize = false)
    private Map<String, List<Map<String, Object>>> allCalculateFields;
    /**组合describe级别和字段级别的CalculateFields，并缓存下*/
    @JSONField(serialize = false,deserialize = false)
    public Map<String, List<Map<String, Object>>> getAllCalculateFields() {
        if (allCalculateFields == null) {
            Map<String, Field> fieldMap = getFields();
            Field field = new Field();
            field.put(FieldKeys.Common.CALCULATE_RELATION, getCalculateRelationMap());
            fieldMap.put("tempAddDescribeCalRelation", field);
            allCalculateFields = new HashMap<>();
            allCalculateFields.putAll(MetaDataParser.getCalculateFields(fieldMap.values()));
        }
        return JSON.parseObject(JSON.toJSONString(allCalculateFields), new TypeReference<Map<String, List<Map<String, Object>>>>(){});
    }

    @Override
    public ObjectDescribe clone() {
        try {
            return JSON.parseObject(JSON.toJSONString(this), ObjectDescribe.class);
        } catch (Exception ignore) {
            return null;
        }
    }
}
