/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.beans.fields;

import java.util.Map;

/**
 * 邮箱
 */
public class EmailField extends Field implements FieldKeys.EMAIL {
    public EmailField(Map<String, Object> map) {
        super(map);
    }

    public String getDefaultValue() {
        return getString(DEFAULT_VALUE);
    }

    public String getPattern(){
        return getString(PATTERN);
    }


}
