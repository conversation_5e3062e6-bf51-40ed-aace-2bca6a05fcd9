/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.beans.formfields;

import java.util.List;
import java.util.Map;

import com.facishare.fs.metadata.beans.fields.Option;

public class BooleanForm<PERSON>ield extends Form<PERSON>ield implements FormFieldKeys.BOOLEAN {
    public BooleanFormField(Map<String, Object> map) {
        super(map);
    }

    public Boolean getDefaultValue() {
        return getBoolean(DEFAULT_VALUE);
    }

    public List<Option> getOptions() {
        return getMetaDataList(OPTIONS, Option.class);
    }
}
