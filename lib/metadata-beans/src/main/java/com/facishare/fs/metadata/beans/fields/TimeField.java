/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.facishare.fs.metadata.beans.fields;

import java.util.Map;

/**
 * 时间
 */
public class TimeField extends Field implements FieldKeys.TIME {
    public TimeField(Map<String, Object> map) {
        super(map);
    }

    public String getDateFormat() {
        return getString(DATE_FORMAT);
    }

    public String getTimeZone() {
        return getString(TIME_ZONE);
    }
}
