apply plugin: 'com.android.library'
//apply plugin: 'groovyx.android'

android {
    compileSdkVersion gradle.compileSdkVersionForLib
    buildToolsVersion gradle.buildToolsVersion
    compileOptions {
        sourceCompatibility gradle.javaVersion
        targetCompatibility gradle.javaVersion
    }

    defaultConfig {
        minSdkVersion gradle.minSdkVersion
        targetSdkVersion gradle.targetSdkVersion
        versionCode 3
        versionName gradle.versionmap.get(project.name)
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    }
    buildTypes {
        release {
            minifyEnabled false
        }
    }
    testOptions.unitTests {
        all {
            testLogging {
                //events 'passed', 'skipped', 'failed', 'standardOut', 'standardError'
                events 'skipped', 'failed', 'standardOut'
            }
//            jacoco {
//                includeNoLocationClasses = true
//            }
        }
        includeAndroidResources = true
        returnDefaultValues = true
    }
    lintOptions {
        abortOnError false
    }
    packagingOptions {
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
    }
}

task makeJar(type: Jar, dependsOn: build) {
    from file('build/intermediates/classes/release')
    archiveName = "$project.name-release.jar"
    destinationDir = file('build/outputs/jar')
    //过滤不需要的class
    exclude "**/**/BuildConfig.class"
    exclude "**/**/BuildConfig\$*.class"
    exclude "**/R.class"
    exclude "**/R\$*.class"

    //指定打包的class
    include "com/facishare/fs/metadata/beans/**/*.class"
}


/**
 * maven配置
 */
task generateSourcesJar(type: Jar) {
    from android.sourceSets.main.java.srcDirs
    classifier 'sources'
}
//uploadArchives {
//    dependsOn makeJar
//    repositories {
//        mavenDeployer {
//            repository(url: gradle.mavenUrl) {
//                authentication(userName: gradle.mavenUserName, password: gradle.mavenPwd)
//            }
//            snapshotRepository(url: gradle.mavenDevUrl) {
//                authentication(userName: gradle.mavenUserName, password: gradle.mavenPwd)
//            }
//        }
//    }
//    doFirst {
//        repositories.mavenDeployer.addFilter("jar-src") { artifact, file ->
//            artifact.name == 'jar-src' || artifact.name == 'jar'
//        }
//        repositories.mavenDeployer.addFilter("jar") { artifact, file ->
//            artifact.name == 'jar'
//        }
//
//        def versionName = "${android.defaultConfig.versionName}"
//        def version = versionName;
//        def jarSrcVersion = "$versionName-sources";
//        if (project.isSnapshot == 'true') {
//            version = "$versionName-SNAPSHOT"
//            jarSrcVersion += "-SNAPSHOT"
//        }
//        println version
//        repositories.mavenDeployer.pom('jar').version = version
//        repositories.mavenDeployer.pom('jar').artifactId = "$project.name"
//        repositories.mavenDeployer.pom('jar').groupId = "com.facishare.fs"
//
//        repositories.mavenDeployer.pom('jar-src').version = jarSrcVersion
//        repositories.mavenDeployer.pom('jar-src').artifactId = "$project.name"
//        repositories.mavenDeployer.pom('jar-src').groupId = "com.facishare.fs"
//    }
//}

//artifacts {
//    archives(file("$buildDir/outputs/jar/$project.name-release.jar")) {
//        name 'jar'
//    }
//    archives(generateSourcesJar) {
//        name 'jar-src'
//    }
//}

//sonar配置
//sonarqube {
//    properties {
//        property "sonar.host.url", gradle.sonarHost
//        property "sonar.projectName", "fs-metadata-beans-android-sdk"
//        property "sonar.projectKey", "fs-pass-metadata-android:metadata-beans"
//        property "sonar.projectVersion", "$android.defaultConfig.versionName"
//        property "sonar.inclusions", "src/main/java/com/facishare/fs/metadata/beans/**"
//        property "sonar.sources", "src"
//        property "sonar.sourceEncoding", "UTF-8"
//        property "sonar.jacoco.reportPaths", "build/jacoco/testDebugUnitTest.exec"
//        property "sonar.junit.reportsPath", "build/test-results/debug"
//        property "sonar.scm.provider", "git"
//    }
//}

//task jacocoTestReport(type: JacocoReport, dependsOn: "testDebugUnitTest") {
//    group = "Reporting"
//    description = "Generate Jacoco coverage reports after running tests."
//    reports {
//        xml.enabled = true
//        html.enabled = true
//    }
//    classDirectories = fileTree(
//            dir: 'build/intermediates/classes/debug/com/facishare/fs/metadata/beans',
//            excludes: ['**/R.class',
//                       '**/R$*.class',
//                       '**/BuildConfig.*',
//                       '**/Manifest*.*'])
//    sourceDirectories = files('src/main/java/com/facishare/fs/metadata/beans')
//    executionData = files('build/jacoco/testDebugUnitTest.exec')
//    doFirst {
//        files('build/intermediates/classes/debug').getFiles().each { file ->
//            if (file.name.contains('$$')) {
//                file.renameTo(file.path.replace('$$', '$'))
//            }
//        }
//    }
//}

dependencies {
    // Lib's dependencies (provided)
    compileOnly fileTree(dir: rootProject.getRootDir().getAbsolutePath() + "/libs", include: ['*.jar'])
    compileOnly fileTree(dir: rootProject.getRootDir().getAbsolutePath() + "/libsDex", include: ['*.jar'])
    compileOnly depLibProjectWithMavenOrSource("xUtils")
    compileOnly "androidx.legacy:legacy-support-v4:${gradle.andoidxVersion}"

    //Dependencies for local unit tests
    testImplementation "junit:junit:$gradle.junitVersion"
    testImplementation "org.mockito:mockito-core:$gradle.mockitoVersion"
    testImplementation "org.robolectric:robolectric:$gradle.robolectricVersion"
    testImplementation "org.robolectric:shadows-supportv4:$gradle.robolectricVersion"

}
