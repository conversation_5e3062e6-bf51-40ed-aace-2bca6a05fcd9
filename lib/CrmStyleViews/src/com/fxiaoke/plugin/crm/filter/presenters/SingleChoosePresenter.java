package com.fxiaoke.plugin.crm.filter.presenters;

import android.content.Context;
import android.util.Log;

import com.fxiaoke.plugin.crm.common_view.model_views.abstract_views.CrmModelView;
import com.fxiaoke.plugin.crm.filter.modelviews.SingleChoiceModel2;
import com.fxiaoke.plugin.crm.filter.FilterUtils;
import com.facishare.fs.pluginapi.crm.beans.FilterConditionInfo;
import com.fxiaoke.plugin.crm.filter.beans.FilterItemInfo;
import com.fxiaoke.plugin.crm.filter.beans.FilterType;
import com.fxiaoke.plugin.crm.filter.presenters.base.BaseFilterPresenter;
import com.fxiaoke.plugin.crm.filter.presenters.base.FilterModelResultArg;
import com.fxiaoke.plugin.crm.filter.presenters.base.FilterModelViewArg;

import java.util.List;

/**
 * Created by xudd on 2016/4/20.
 */
public class SingleChoosePresenter extends BaseFilterPresenter {
    public static final String TAG = SingleChoosePresenter.class.getSimpleName();
    @Override
    public boolean accept(FilterModelViewArg filterModelViewArg) {
        return filterModelViewArg != null && filterModelViewArg.filterItemInfo != null
                && (filterModelViewArg.filterItemInfo.fieldType == FilterType.SINGLE_CHOICE
                || filterModelViewArg.filterItemInfo.fieldType == FilterType.RECORD_TYPE);
    }

    @Override
    protected void onUpdateView(CrmModelView modelView, FilterModelViewArg filterModelViewArg) {

    }

    @Override
    protected CrmModelView onCreateView(Context context, FilterModelViewArg filterModelViewArg) {
        SingleChoiceModel2 singleChoiceModel = new SingleChoiceModel2(context);
        singleChoiceModel.setData(FilterUtils.getChoiceStrList(filterModelViewArg.filterItemInfo.enumDetails));
        updateModelViewSelectedPosition(singleChoiceModel, filterModelViewArg);
        return singleChoiceModel;
    }

    /**
     * 更新选中item
     * @param singleChoiceModel
     * @param filterModelViewArg
     */
    public void updateModelViewSelectedPosition(SingleChoiceModel2 singleChoiceModel, FilterModelViewArg
            filterModelViewArg){
        FilterConditionInfo filterConditionInfo = getRecoverInfo(filterModelViewArg.filterItemInfo,
                filterModelViewArg.recoverList);
        int selectedPos = -1;
        if(filterConditionInfo != null){
            String recoverStr = filterConditionInfo.filterValue;
            if(recoverStr == null){
                if(filterConditionInfo.comparison == 9){
                    selectedPos = SingleChoiceModel2.Status.EMPTY.key;
                }else if(filterConditionInfo.comparison == 10){
                    selectedPos = SingleChoiceModel2.Status.NOT_EMPTY.key;
                }else{
                    selectedPos = -1;
                }
            }else{
                selectedPos = singleChoiceModel.getNullItemCount() + FilterUtils.getSelectedPosition(filterModelViewArg
                        .filterItemInfo.enumDetails, recoverStr);
            }
        }
        singleChoiceModel.setSelectedPosition(selectedPos);
    }

    @Override
    protected String getRecoverStr(FilterItemInfo filterItemInfo, List<FilterConditionInfo> recoverList) {
        return super.getRecoverStr(filterItemInfo, recoverList);
    }

    @Override
    protected FilterModelResultArg onDealResult(CrmModelView modelView, FilterModelViewArg filterModelViewArg) {
        return super.getDealResult(getFilterConditionInfo(modelView));
    }

    @Override
    protected FilterConditionInfo[] getFilterConditionInfo(CrmModelView modelView) {
        FilterConditionInfo[] conditionInfos = {new FilterConditionInfo(), new FilterConditionInfo()};
        FilterItemInfo filterInfo = (FilterItemInfo) modelView.getTagNoCheckType();
        conditionInfos[0].fieldName = filterInfo.fieldName;
        conditionInfos[0].filterValue = null;
        conditionInfos[1].fieldName = null;
        SingleChoiceModel2 singleChoiceModel = (SingleChoiceModel2) modelView;
        if(!singleChoiceModel.isEmpty() && filterInfo.enumDetails != null){
            if(singleChoiceModel.getCurrStatus() == SingleChoiceModel2.Status.TEXT){
                int selectedIndex = singleChoiceModel.getSelectedPosition() - singleChoiceModel.getNullItemCount();
                Log.i(TAG, "singleChoiceModel.getSelectedPosition():"+singleChoiceModel.getSelectedPosition());
                if(selectedIndex < filterInfo.enumDetails.size()) {
                    conditionInfos[0].filterValue = filterInfo.enumDetails.get(selectedIndex).mItemcode;
                    conditionInfos[0].comparison = 1;
                }
            }else if(singleChoiceModel.getCurrStatus() == SingleChoiceModel2.Status.EMPTY){
                conditionInfos[0].filterValue = "";
                conditionInfos[0].comparison = 9;
            }else if(singleChoiceModel.getCurrStatus() == SingleChoiceModel2.Status.NOT_EMPTY){
                conditionInfos[0].filterValue = "";
                conditionInfos[0].comparison = 10;
            }
        }
        return conditionInfos;
    }
}
