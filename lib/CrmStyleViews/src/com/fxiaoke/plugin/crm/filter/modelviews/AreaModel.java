/*
 * Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.filter.modelviews;

import java.util.List;

import com.facishare.fs.pluginapi.HostInterfaceManager;
import com.facishare.fs.pluginapi.crm.biz_api.ICrmCascade;
import com.facishare.fs.sizectrlviews.SizeControlTextView;
import com.fxiaoke.crmstyleviews.R;
import com.fxiaoke.plugin.crm.common_view.model_views.abstract_views.CrmModelView;
import com.fxiaoke.plugin.crm.filter.AreaNode;
import com.fxiaoke.plugin.crm.filter.filterviews.FilterSelectItemGroup;
import com.fxiaoke.plugin.crm.filter.filterviews.FilterSelectItemView;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

/**
 *
 */
public class AreaModel extends CrmModelView {

    public static final int REQUEST_SELECT_CITY = 0x12;

    private FilterSelectItemGroup mItemGroup;
    private FilterSelectItemView mNotLimitItem, mEmptyItem, mNotEmptyItem;
    private ImageView mSelectImg;
    private SizeControlTextView mContent;
    //选择当前区域类型
    private int mFilterType;
    //当前选中状态
    private Status mCurrStatus;
    //选择的id集合
    private List<String> mSelectedIdList;
    //是否是当前点击的model
    private boolean mClicked = false;

    public enum Status {
        NOT_LIMIT, EMPTY, NOT_EMPTY, SELECT
    }

    public AreaModel(Context context) {
        super(context);
        mCurrStatus = Status.NOT_LIMIT;
    }

    @Override
    protected View onCreateView(Context context) {
        View convertView = LayoutInflater.from(context).inflate(R.layout.item_crm_filter_view_area, null);
        mItemGroup = (FilterSelectItemGroup) convertView.findViewById(R.id.group);
        mNotLimitItem = (FilterSelectItemView) convertView.findViewById(R.id.item_not_limit);
        mEmptyItem = (FilterSelectItemView) convertView.findViewById(R.id.item_empty);
        mNotEmptyItem = (FilterSelectItemView) convertView.findViewById(R.id.item_not_empty);
        mSelectImg = (ImageView) convertView.findViewById(R.id.iv_area_checked);
        mContent = (SizeControlTextView) convertView.findViewById(R.id.tv_area_name);
        mItemGroup.post(new Runnable() {
            @Override
            public void run() {
                mItemGroup.select(mNotLimitItem, true);
            }
        });
        setSelectItemCallback();
        convertView.findViewById(R.id.ll_filter).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mClicked = true;
                toSelectCityAct();
            }
        });
        return convertView;
    }

    /**
     * @param state
     * @param selectedIdList
     * @param nameStr
     */
    public void update(Status state, List<String> selectedIdList, String nameStr) {
        this.mSelectedIdList = selectedIdList;
        mCurrStatus = state;
        if (state == Status.SELECT) {
            mContent.setText(nameStr);
            mSelectImg.setVisibility(View.VISIBLE);
            mItemGroup.unselectAll();
        } else {
            mContent.setText(null);
            mSelectImg.setVisibility(View.INVISIBLE);
        }
    }

    /**
     * 跳转省市区选择界面
     */
    private void toSelectCityAct() {
        /**
         * 跳转到城市级联选择页面
         *
         * @param context           上下文
         * @param isCityCascade     是否是城市级联数据
         * @param isSelectedByLevel 是否放开了层级单选
         * @param selectedIds       选中的节点id
         * @param requestCode       请求码
         */
        HostInterfaceManager.getICrm()
                .go2CityCascadeActivity((Activity) getContext(),
                        true,
                        true,
                        mSelectedIdList,
                        AreaNode.findByFilterType(mFilterType),
                        REQUEST_SELECT_CITY);
    }

    public List<String> getSelectedIdList() {
        return mSelectedIdList;
    }

    /**
     * 当过滤条件为：不限、空（未填写）、不为空时，清空选择的数据
     */
    private void clearSelectedData() {
        if (mSelectedIdList != null) {
            mSelectedIdList.clear();
            mSelectedIdList = null;
        }
        update(mCurrStatus, mSelectedIdList, null);
    }

    @Override
    public boolean isEmpty() {
        return getCurrStatus() == Status.NOT_LIMIT;
    }

    @Override
    public void setTitle(String title) {

    }

    @Override
    public void setHint(String hint) {
        mContent.setHint(hint);
    }

    @Override
    public void reset() {
        mContent.setText(null);
    }

    public void setFilterType(int filterType) {
        this.mFilterType = filterType;
    }

    public void setCurrStatus(Status mCurrStatus) {
        this.mCurrStatus = mCurrStatus;
    }

    public Status getCurrStatus() {
        return mCurrStatus;
    }

    public String getResult() {
        if (mSelectedIdList != null && mSelectedIdList.size() > 0) {
            return mSelectedIdList.get(mSelectedIdList.size() - 1);
        } else {
            return "";
        }
    }

    private void setSelectItemCallback() {
        mNotLimitItem.setSelectStateChangeCallback(new FilterSelectItemView.Callback() {
            @Override
            public void onSelectStageChanged(boolean isSelect) {
                if (isSelect) {
                    mCurrStatus = Status.NOT_LIMIT;
                    mSelectImg.setVisibility(View.INVISIBLE);
                    clearSelectedData();
                }
            }
        });

        mEmptyItem.setSelectStateChangeCallback(new FilterSelectItemView.Callback() {
            @Override
            public void onSelectStageChanged(boolean isSelect) {
                if (isSelect) {
                    mCurrStatus = Status.EMPTY;
                    mSelectImg.setVisibility(View.INVISIBLE);
                    clearSelectedData();
                }
            }
        });

        mNotEmptyItem.setSelectStateChangeCallback(new FilterSelectItemView.Callback() {
            @Override
            public void onSelectStageChanged(boolean isSelect) {
                if (isSelect) {
                    mCurrStatus = Status.NOT_EMPTY;
                    mSelectImg.setVisibility(View.INVISIBLE);
                    clearSelectedData();
                }
            }
        });
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_SELECT_CITY && resultCode == Activity.RESULT_OK) {
            if (mClicked) {
                mClicked = false;
                if (data != null) {
                    //选中的城市名称
                    String nameStr = data.getStringExtra(ICrmCascade.SELECTED_CITY_NAMES);
                    if (nameStr != null) {
                        String[] names = nameStr.split("/");
                        if (names.length > 1) {
                            nameStr = names[names.length - 1];
                        }
                    }
                    //选中的城市id集合
                    List<String> idList = (List<String>) data.getSerializableExtra(ICrmCascade.SELECTED_CITY_LIST);
                    if (idList != null && idList.size() > 0) {
                        update(Status.SELECT, idList, nameStr);
                    } else {
                        update(Status.NOT_LIMIT, idList, nameStr);
                    }

                }
            }
        }
    }
}
