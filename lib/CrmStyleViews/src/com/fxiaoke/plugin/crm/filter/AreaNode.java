/*
 * Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.filter;

import com.fxiaoke.plugin.crm.filter.beans.FilterType;

public enum AreaNode {
    /**
     * 空节点
     */
    NULL(0),
    /**
     * 国家
     */
    COUNTRY(1),

    /**
     * 省
     */
    PROVINCE(2),

    /**
     * 市
     */
    CITY(3),

    /**
     * 区
     */
    DISTRICT(4);
    public int value;

    AreaNode(int value) {
        this.value = value;
    }


    public static int findByFilterType(int fieldType) {
        if (fieldType== FilterType.NATION) {
            return COUNTRY.value;
        }else if (fieldType == FilterType.PROVINCE) {
            return PROVINCE.value;
        }else if (fieldType == FilterType.CITY) {
            return CITY.value;
        }else if (fieldType == FilterType.DISTRICT) {
            return DISTRICT.value;
        }else {
            return NULL.value;
        }
    }
}
