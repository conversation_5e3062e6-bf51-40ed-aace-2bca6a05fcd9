/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.filter.modelviews;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.facishare.fs.i18n.I18NHelper;
import com.fxiaoke.crmstyleviews.R;
import com.fxiaoke.plugin.crm.common_view.model_views.abstract_views.CrmModelView;
import com.fxiaoke.plugin.crm.filter.adapter.FilterSelectAdapter;
import com.fxiaoke.plugin.crm.filter.adapter.FilterSelectItem;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ListView;

/**
 * 单选类型，筛选V5.4用
 *
 * <AUTHOR>
 */
public class SingleChoiceModel2 extends CrmModelView {

    /**
     * 是否添加"不限"选项
     */
    private boolean mAddNoLimit = true;

    /**
     * 是否添加"空（未填写）"选项
     */
    private boolean mAddEmpty = true;

    /**
     * 是否添加"不为空"选项
     */
    private boolean mAddNoEmpty = true;

    /**
     * 列表顶部本地添加的item数
     */
    private int mNullItemCount = 3;

    private ListView listView;
    private FilterSelectAdapter adapter;
    private int defaultPosition = 0;
    private int selectedPosition = defaultPosition;
    private List<String> choiceItems;

    public enum Status{
        NOT_LIMIT(0), EMPTY(1), NOT_EMPTY(2), TEXT(3);

        public int key;
        Status(int key){
            this.key = key;
        }

        public static Status getStatusByKey(int key){
            for(Status status : Status.values()){
                if(status.key == key){
                    return status;
                }
            }
            return null;
        }
    }

    public SingleChoiceModel2(Context context) {
        super(context);
        setAddNullItem(true);
    }

    public SingleChoiceModel2(Context context, boolean isBoolModel) {
        super(context);
        setAddNullItem(true);
        if(isBoolModel){
            setData(Arrays.asList(new String[]{I18NHelper.getText("common.fake_data.des.yes")/* 是 */, I18NHelper.getText("xt.create_vote.text.no")/* 否 */}));
        }
    }

    /**
     * 是否在列表顶部添加"不限"、"空（未填写）"、"不为空"选项
     */
    public void setAddNullItem(boolean add) {
        setAddNoLimit(add);
        setAddEmpty(add);
        setAddNoEmpty(add);
    }

    public void setAddNoLimit(boolean add) {
        if (add == mAddNoLimit) {
            return;
        }
        mAddNoLimit = add;
        if (add) {
            mNullItemCount ++;
        } else {
            mNullItemCount --;
        }
    }

    public void setAddEmpty(boolean add) {
        if (add == mAddEmpty) {
            return;
        }
        mAddEmpty = add;
        if (add) {
            mNullItemCount ++;
        } else {
            mNullItemCount --;
        }
    }

    public void setAddNoEmpty(boolean add) {
        if (add == mAddNoEmpty) {
            return;
        }
        mAddNoEmpty = add;
        if (add) {
            mNullItemCount ++;
        } else {
            mNullItemCount --;
        }
    }

    public synchronized void setData(List<String> choiceItems) {
        this.choiceItems = new ArrayList<>(choiceItems);
        if (mAddNoLimit) {
            this.choiceItems.add(Status.NOT_LIMIT.key, I18NHelper.getText("crm.layout.item_crm_filter_view_click.1974")/* 不限 */);
        }
        if (mAddEmpty) {
            this.choiceItems.add(Status.EMPTY.key, I18NHelper.getText("meta.layout.item_filter_empty.2936")/* 空(未填写) */);
        }
        if (mAddNoEmpty) {
            this.choiceItems.add(Status.NOT_EMPTY.key, I18NHelper.getText("meta.layout.item_filter_empty.2935")/* 不为空 */);
        }
        List<FilterSelectItem> list = trans2FilterSelectItems(this.choiceItems);
        if(adapter != null){
            adapter.updateDataList(list);
        }
    }

    public int getNullItemCount(){
        if(mNullItemCount < 0){
            mNullItemCount = 0;
        }
        return mNullItemCount;
    }

    public int getSelectedPosition() {
        return selectedPosition;
    }

    public void setSelectedPosition(int position) {
        if (position >= defaultPosition && position < choiceItems.size()) {
            selectedPosition = position;
            if(adapter != null){
                adapter.setSelectedPos(position);
            }
        }
    }

    public Status getCurrStatus(){
        String choice = "";
        Status status = Status.TEXT;
        if (selectedPosition >=0 && selectedPosition < choiceItems.size()) {
            choice = choiceItems.get(selectedPosition);
        }
        if (TextUtils.equals(choice, I18NHelper.getText("crm.layout.item_crm_filter_view_click.1974")/* 不限 */)) {
            status = Status.NOT_LIMIT;
        } else if (TextUtils.equals(choice, I18NHelper.getText("meta.layout.item_filter_empty.2936")/* 空(未填写) */)) {
            status = Status.EMPTY;
        } else if (TextUtils.equals(choice, I18NHelper.getText("meta.layout.item_filter_empty.2935")/* 不为空 */)) {
            status = Status.NOT_EMPTY;
        }
        return status;
    }

    public void clearSelected() {
        selectedPosition = defaultPosition;
        if(adapter != null) {
            adapter.setSelectedPos(0);
        }
    }

    @Override
    public View onCreateView(Context context) {
        View convertView = LayoutInflater.from(getContext()).inflate(R.layout.item_crm_filter_view_select, null);
        listView = (ListView) convertView.findViewById(R.id.crm_filter_views_listview);

        adapter = new FilterSelectAdapter(getContext(), true, trans2FilterSelectItems(this.choiceItems));
        listView.setAdapter(adapter);

        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                setSelectedPosition(position);
            }
        });
        return convertView;
    }

    @Override
    public boolean isEmpty() {
        return getCurrStatus() == Status.NOT_LIMIT;
    }

    @Override
    public void setTitle(String title) {
    }

    @Override
    public void setHint(String hint) {
    }

    @Override
    public void reset() {
        clearSelected();
    }

    private List<FilterSelectItem> trans2FilterSelectItems(List<String> oldList){
        List<FilterSelectItem> list = new ArrayList<>();
        if(oldList != null){
            for(String string : oldList){
                list.add(new FilterSelectItem(string));
            }
        }
        return list;
    }
}
