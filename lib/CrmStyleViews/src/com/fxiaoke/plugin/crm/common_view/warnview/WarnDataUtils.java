package com.fxiaoke.plugin.crm.common_view.warnview;

import static com.facishare.fs.dialogs.CommonDialog.FLAG_scrollView_And_SingleButton;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.facishare.fs.i18n.I18NHelper;

import android.content.Context;

/**
 * 展示条件提示工具类
 */
public class WarnDataUtils {
    private Context mContext;
    private WarnResult result;
    private String topText;
    private FilterDialog filterDialog;

    public WarnDataUtils(Context context) {
        this.mContext = context;
    }

    public WarnDataUtils setResult(WarnResult result) {
        this.result = result;
        return this;
    }

    public WarnDataUtils setResultData(WarnBean warnBean) {
        this.result = new WarnResult(warnBean);
        return this;
    }

    public WarnDataUtils setTopText(String topText) {
        this.topText = topText;
        return this;
    }

    /**
     * 展示Dialog
     */
    public void showFilterDialog() {
        if (result == null || result.ruleMessage == null || result.ruleMessage.conditions == null) {
            return;
        }
        if (filterDialog != null && filterDialog.isShowing()) {
            filterDialog.dismiss();
        }
        filterDialog = new FilterDialog(mContext);
        filterDialog.setTip(result.ruleMessage.tip);
        filterDialog.setItems(result.ruleMessage.conditions)
                .setShowType(FLAG_scrollView_And_SingleButton);
        filterDialog.setTitle(topText);
        filterDialog.setPositiveButton(I18NHelper.getText("qx.session.guide.dialog_btn_known")/* 知道了 */);
        filterDialog.show();
    }

    public FilterDialog getFilterDialog() {
        return filterDialog;
    }

    private List<WarnData> getBpmWarnListData() {
        //
        //        String conditionPattern = result.mBpmWarnBean.conditionPattern;
        //        if (TextUtils.isEmpty(conditionPattern)) {
        //            return new ArrayList<>();
        //        }
        //        conditionPattern = conditionPattern.trim().replaceAll(" ", "");
        //        String[] patterns = conditionPattern.split(WarnType.OR.value);
        //        List<BpmWarn> conditions = result.mBpmWarnBean.conditions;
        //        List<BpmWarn> bpmWarnList = new ArrayList<>();
        //        for (int i = 0; i < patterns.length; i++) {
        //            String p = patterns[i];
        //            p = p.replaceAll("\\(", "").replaceAll("\\)", "");
        //            String[] strings = p.split(WarnType.AND.value);
        //            for (int j = 0; j < strings.length; j++) {
        //                String s = strings[j];
        //                if (isNumeric(s)) {
        //                    BpmWarn bpmWarn = getWarnForRowNo(conditions, Integer.parseInt(s));
        //                    if (j != strings.length - 1) {
        //                        //and line
        //                        bpmWarn.showAnd = true;
        //                    }
        //                    bpmWarnList.add(bpmWarn);
        //                    if (j == strings.length - 1 && i != patterns.length - 1) {
        //                        //or line
        //                        bpmWarnList.add(new BpmWarn(WarnType.OR.value));
        //                    }
        //                }
        //            }
        //        }
        return new ArrayList<>();//bpmWarnList;
    }

    /**
     * 判断是否是数字
     */
    private boolean isNumeric(String str) {
        Pattern pattern = Pattern.compile("[0-9]*");
        Matcher isNum = pattern.matcher(str);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }

    /**
     * 根据rowNo获取BpmWarn
     */
    private WarnData getWarnForRowNo(List<WarnData> conditions, int rowNo) {
        for (WarnData bpmWarnList : conditions) {
            if (rowNo == bpmWarnList.getRowNo()) {
                return bpmWarnList;
            }
        }
        return null;
    }
}
