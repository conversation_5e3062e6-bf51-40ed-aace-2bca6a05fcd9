/*
 * Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.fxiaoke.plugin.crm.common_view;

import java.util.ArrayList;
import java.util.List;

import com.fxiaoke.plugin.crm.common_view.scrollable_view.motion_listener.CanScrollVerticallyDelegate;
import com.fxiaoke.plugin.crm.common_view.scrollable_view.motion_listener.OnFlingOverListener;

import androidx.fragment.app.Fragment;

/**
 * Created by l<PERSON>yu on 2017/7/10.
 * 通用详情页滚动碎片接口
 * 用于新老对象融合
 */

public interface IDetailScrollFragment extends CanScrollVerticallyDelegate, OnFlingOverListener {
    /**
     * 提供一些通用方法
     */
    class helper {
        public static List<Fragment> toFrag(List<IDetailScrollFragment> iDetailScrollFragments) {
            List<Fragment> fragments = new ArrayList<>();
            if (iDetailScrollFragments != null && iDetailScrollFragments.size() > 0) {
                for (IDetailScrollFragment iDetailScrollFragment : iDetailScrollFragments) {
                    if (iDetailScrollFragment instanceof Fragment) {
                        fragments.add((Fragment) iDetailScrollFragment);
                    }
                }
            }
            return fragments;
        }
    }
}
