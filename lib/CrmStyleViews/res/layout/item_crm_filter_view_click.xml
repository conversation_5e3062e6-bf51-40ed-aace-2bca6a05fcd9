<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/ll_filter_default"
        style="@style/crm_filter_views_layout_style">

        <ImageView
            android:id="@+id/iv_filter_checked_default"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/crm_selected_filter_scene"
            android:layout_marginRight="13dp"
                android:layout_marginEnd="13dp">
        </ImageView>

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/tv_filter_default"
            style="@style/crm_filter_views_title_style"
            i18n:fstext="crm.layout.item_crm_filter_view_click.1974"/>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#eeeeee"
        android:layout_marginLeft="48dp"
            android:layout_marginStart="48dp" />

    <LinearLayout
        android:id="@+id/ll_filter"
        style="@style/crm_filter_views_layout_style">

        <ImageView
            android:id="@+id/iv_filter_checked"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/crm_selected_filter_scene"
            android:layout_marginRight="13dp"
            android:visibility="invisible"
                android:layout_marginEnd="13dp">
        </ImageView>

        <com.facishare.fs.sizectrlviews.SizeControlTextView
            android:id="@+id/tv_filter"
            android:layout_width="0dp"
            android:layout_weight="1"
            style="@style/crm_filter_views_title_style"
            i18n:fshint="crm.layout.select_task_priority.7280"
            android:textColorHint="#cccccc"/>

        <ImageView
            android:id="@+id/arrow_img"
            style="@style/crm_model_view_arrow_style" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#eeeeee"
        android:layout_marginLeft="48dp"
            android:layout_marginStart="48dp" />
</LinearLayout>
