<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2018 Facishare Technology Co., Ltd. All Rights Reserved.
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:i18n="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical">

    <LinearLayout
            android:id="@+id/comparison_select_layout"
            style="@style/crm_filter_views_layout_style">
        <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/crm_filter_view_click3_title1"
                i18n:fstext="crm.layout.item_crm_filter_view_click3.3540"
                android:layout_width="0dp"
                android:layout_weight="4"
                style="@style/crm_filter_views_title_style"/>

        <ImageView
                android:id="@+id/crm_filter_view_click3_arrow1"
                android:layout_weight="0.5"
                style="@style/crm_model_view_arrow_style"/>

    </LinearLayout>

    <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#eeeeee"
            android:layout_marginLeft="14dp"
            android:layout_marginStart="14dp" />

    <LinearLayout
            android:id="@+id/emp_select_layout"
            style="@style/crm_filter_views_layout_style">

        <com.facishare.fs.sizectrlviews.SizeControlTextView
                android:id="@+id/crm_filter_view_click3_title2"
                i18n:fshint="crm.layout.select_task_priority.7280"
                android:layout_width="0dp"
                android:layout_weight="4"
                style="@style/crm_filter_views_title_style"/>


        <ImageView
                android:id="@+id/crm_filter_views_time_arrow2"
                android:layout_weight="0.5"
                style="@style/crm_model_view_arrow_style"/>
    </LinearLayout>

    <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#eeeeee"
            android:layout_marginLeft="12dp"
            android:layout_marginStart="12dp" />

</LinearLayout>